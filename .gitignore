######################
# Project Specific
######################
/target/www/**
/src/test/javascript/coverage/
/src/test/javascript/PhantomJS*/

######################
# Node
######################
/node/
node_tmp/
node_modules/
npm-debug.log.*
/.awcache/*

######################
# SASS
######################
.sass-cache/

######################
# Eclipse
######################
*.pydevproject
.project
.metadata
tmp/
tmp/**/*
*.tmp
*.bak
*.swp
*~.nib
local.properties
.classpath
.settings/
.loadpath
.factorypath
/src/main/resources/rebel.xml

# External tool builders
.externalToolBuilders/**

# Locally stored "Eclipse launch configurations"
*.launch

# CDT-specific
.cproject

# PDT-specific
.buildpath

######################
# Intellij
######################
.idea/
*.iml
*.iws
*.ipr
*.ids
*.orig
classes/

######################
# Visual Studio Code
######################
.vscode/

######################
# Maven
######################
/log/
/target/

######################
# Gradle
######################
.gradle/
/build/

######################
# Package Files
######################
*.jar
*.war
*.ear
*.db

######################
# Windows
######################
# Windows image file caches
Thumbs.db

# Folder config file
Desktop.ini

######################
# Mac OSX
######################
.DS_Store
.svn

# Thumbnails
._*

# Files that might appear on external disk
.Spotlight-V100
.Trashes

######################
# Directories
######################
/bin/
/deploy/

######################
# Logs
######################
*.log*

######################
# Others
######################
*.class
*.*~
*~
.merge_file*

######################
# Gradle Wrapper
######################
!gradle/wrapper/gradle-wrapper.jar

######################
# Maven Wrapper
######################
!.mvn/wrapper/maven-wrapper.jar

######################
# ESLint
######################
.eslintcache


src/main/java/com/cowell/pricecenter/config/MyBatisConfig.java
src/main/java/com/cowell/pricecenter/config/MyBatisScannerConfig.java
src/main/java/com/cowell/pricecenter/config/TransactionConfig.java
src/main/java/com/cowell/pricecenter/config/sharding/DataSourceConfig.java
src/main/java/com/cowell/pricecenter/config/sharding/algorithm/ShardingDatabaseAlgorithm.java
src/main/java/com/cowell/pricecenter/config/sharding/algorithm/ShardingDatabaseHashAlgorithm.java
src/main/java/com/cowell/pricecenter/config/sharding/algorithm/ShardingTableAlgorithm.java
src/main/java/com/cowell/pricecenter/config/sharding/algorithm/ShardingTableHashAlgorithm.java
src/main/java/com/cowell/pricecenter/entity/AdjustPriceOrder.java
src/main/java/com/cowell/pricecenter/entity/AdjustPriceOrderDetail.java
src/main/java/com/cowell/pricecenter/entity/AdjustPriceOrderDetailExample.java
src/main/java/com/cowell/pricecenter/entity/AdjustPriceOrderExample.java
src/main/java/com/cowell/pricecenter/entity/AuditLog.java
src/main/java/com/cowell/pricecenter/entity/AuditLogExample.java
src/main/java/com/cowell/pricecenter/entity/PriceGroup.java
src/main/java/com/cowell/pricecenter/entity/PriceGroupDetail.java
src/main/java/com/cowell/pricecenter/entity/PriceGroupDetailExample.java
src/main/java/com/cowell/pricecenter/entity/PriceGroupExample.java
src/main/java/com/cowell/pricecenter/entity/PriceOrgGoods.java
src/main/java/com/cowell/pricecenter/entity/PriceOrgGoodsExample.java
src/main/java/com/cowell/pricecenter/entity/PriceOrgGoodsIdGenerator.java
src/main/java/com/cowell/pricecenter/entity/PriceStoreDetail.java
src/main/java/com/cowell/pricecenter/entity/PriceStoreDetailExample.java
src/main/java/com/cowell/pricecenter/entity/PriceType.java
src/main/java/com/cowell/pricecenter/entity/PriceTypeExample.java
src/main/java/com/cowell/pricecenter/enums/AuditStatusEnum.java
src/main/java/com/cowell/pricecenter/enums/AuditTypeEnum.java
src/main/java/com/cowell/pricecenter/enums/CompetitiveEnum.java
src/main/java/com/cowell/pricecenter/enums/DeleteStatusEnum.java
src/main/java/com/cowell/pricecenter/enums/EffectStatusEnum.java
src/main/java/com/cowell/pricecenter/enums/ExecEnum.java
src/main/java/com/cowell/pricecenter/enums/ManageSignEnum.java
src/main/java/com/cowell/pricecenter/enums/OperateTypeEnum.java
src/main/java/com/cowell/pricecenter/enums/OrgLevelEnum.java
src/main/java/com/cowell/pricecenter/enums/PriceTypeEnum.java
src/main/java/com/cowell/pricecenter/enums/ReturnCodeEnum.java
src/main/java/com/cowell/pricecenter/enums/StatusEnum.java
src/main/java/com/cowell/pricecenter/enums/TargetTypeEnum.java
src/main/java/com/cowell/pricecenter/enums/VersionEnum.java
src/main/java/com/cowell/pricecenter/mapper/AuditLogMapper.java
src/main/java/com/cowell/pricecenter/mapper/PriceGroupDetailMapper.java
src/main/java/com/cowell/pricecenter/mapper/PriceOrgGoodsIdGeneratorMapper.java
src/main/java/com/cowell/pricecenter/mapper/extension/AdjustPriceOrderExMapper.java
src/main/java/com/cowell/pricecenter/mapper/extension/PriceGroupExtMapper.java
src/main/java/com/cowell/pricecenter/mapper/extension/PriceOrgGoodsExtMapper.java
src/main/java/com/cowell/pricecenter/mapper/extension/PriceStoreDetailExMapper.java
src/main/java/com/cowell/pricecenter/schedule/AdjustPriceOrderJobHandler.java
src/main/java/com/cowell/pricecenter/service/IAdjustPriceOrderDetailService.java
src/main/java/com/cowell/pricecenter/service/IAdjustPriceOrderService.java
src/main/java/com/cowell/pricecenter/service/IAuditLogService.java
src/main/java/com/cowell/pricecenter/service/IPriceGroupService.java
src/main/java/com/cowell/pricecenter/service/IPriceOrgGoodsService.java
src/main/java/com/cowell/pricecenter/service/dto/request/AdjustOrderListParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/AdjustPriceOrderDetailParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/AuditAdjustPriceOrderParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/AuthOrgPARAM.java
src/main/java/com/cowell/pricecenter/service/dto/request/AuthPriceOrgGoodsParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/ForestGoodsParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/ItemSkuParamVo.java
src/main/java/com/cowell/pricecenter/service/dto/request/NewPriceAdjustOrderParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/OrderQueryParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/PageParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/PageResponse.java
src/main/java/com/cowell/pricecenter/service/dto/request/PermissionsParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/PriceGroupParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/PriceOrgGoodsParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/PriceStoreDetailParam.java
src/main/java/com/cowell/pricecenter/service/dto/request/PriceTypeParam.java
src/main/java/com/cowell/pricecenter/service/dto/response/AdjustOrderListDTO.java
src/main/java/com/cowell/pricecenter/service/dto/response/AdjustPriceOrderDTO.java
src/main/java/com/cowell/pricecenter/service/dto/response/AdjustPriceOrderDetailDTO.java
src/main/java/com/cowell/pricecenter/service/dto/response/ForestGoodsDTO.java
src/main/java/com/cowell/pricecenter/service/dto/response/ItemSkuVo.java
src/main/java/com/cowell/pricecenter/service/dto/response/PriceListDTO.java
src/main/java/com/cowell/pricecenter/service/dto/response/PriceOrgGoodsDTO.java
src/main/java/com/cowell/pricecenter/service/dto/response/PriceStoreDetailDTO.java
src/main/java/com/cowell/pricecenter/service/dto/response/PriceTypeDTO.java
src/main/java/com/cowell/pricecenter/service/feign/PermissionService.java
src/main/java/com/cowell/pricecenter/service/feign/SearchService.java
src/main/java/com/cowell/pricecenter/service/impl/AdjustPriceOrderDetailServiceImpl.java
src/main/java/com/cowell/pricecenter/service/impl/AdjustPriceOrderServiceImpl.java
src/main/java/com/cowell/pricecenter/service/impl/AuditLogServiceImpl.java
src/main/java/com/cowell/pricecenter/service/impl/PriceGroupServiceImpl.java
src/main/java/com/cowell/pricecenter/service/impl/PriceOrgGoodsServiceImpl.java
src/main/java/com/cowell/pricecenter/service/impl/RedissonCacheServiceImpl.java
src/main/java/com/cowell/pricecenter/service/vo/LevelVo.java
src/main/java/com/cowell/pricecenter/service/vo/OrgVo.java
src/main/java/com/cowell/pricecenter/service/vo/PermissionVo.java
src/main/java/com/cowell/pricecenter/web/rest/AdjustPriceOrderResource.java
src/main/java/com/cowell/pricecenter/web/rest/PermissionResource.java
src/main/java/com/cowell/pricecenter/web/rest/PriceGroupResource.java
src/main/java/com/cowell/pricecenter/web/rest/PriceOrgGoodsResource.java
src/main/java/com/cowell/pricecenter/web/rest/PriceStoreDetailResource.java
src/main/java/com/cowell/pricecenter/web/rest/errors/BusinessErrorException.java
src/main/java/com/cowell/pricecenter/web/rest/errors/BusinessException.java
src/main/java/com/cowell/pricecenter/web/rest/util/CheckAdjustPriceOrderUtil.java
src/main/java/com/cowell/pricecenter/web/rest/util/CodeUtil.java
src/main/java/com/cowell/pricecenter/web/rest/util/Pagination.java
src/main/java/com/cowell/pricecenter/web/rest/vo/CommonResponse.java
src/main/java/com/cowell/pricecenter/web/rest/vo/ForestGoodsVO.java
src/main/java/com/cowell/pricecenter/web/rest/vo/PageVO.java
src/main/resources/mapper/AdjustPriceOrderDetailMapper.xml
src/main/resources/mapper/AdjustPriceOrderMapper.xml
src/main/resources/mapper/AuditLogMapper.xml
src/main/resources/mapper/PriceGroupDetailMapper.xml
src/main/resources/mapper/PriceGroupMapper.xml
src/main/resources/mapper/PriceOrgGoodsMapper.xml
src/main/resources/mapper/PriceStoreDetailMapper.xml
src/main/resources/mapper/PriceTypeMapper.xml
src/main/resources/mapper/extension/AdjustPriceOrderExMapper.xml
src/main/resources/mapper/extension/PriceGroupExtMapper.xml
src/main/resources/mapper/extension/PriceOrgGoodsExtMapper.xml
src/main/resources/mapper/extension/PriceOrgGoodsIdGeneratorMapper.xml
src/main/resources/mapper/extension/PriceStoreDetailExMapper.xml
src/test/com/cowell/pricecenter/PriceTest.java
