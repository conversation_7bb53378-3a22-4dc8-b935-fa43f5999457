{"fluentMethods": true, "relationships": [], "fields": [{"fieldName": "itemId", "javadoc": "主键，商品id，生成策略：自增0+businessId后五位", "fieldType": "<PERSON>"}, {"fieldName": "businessId", "javadoc": "连锁id", "fieldType": "<PERSON>"}, {"fieldName": "storeId", "javadoc": "门店id", "fieldType": "<PERSON>"}, {"fieldName": "spuId", "javadoc": "spu id", "fieldType": "<PERSON>"}, {"fieldName": "bgCateId", "javadoc": "后台类目id", "fieldType": "<PERSON>"}, {"fieldName": "itemName", "javadoc": "商品标题", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 500}, {"fieldName": "price", "javadoc": "商品价格(备注：sku价格)", "fieldType": "<PERSON>"}, {"fieldName": "imgHead", "javadoc": "商品展示主图", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 200}, {"fieldName": "status", "javadoc": "商品状态：1正常，2下架，3删除", "fieldType": "Integer"}, {"fieldName": "stock", "javadoc": "商品库存", "fieldType": "<PERSON>"}, {"fieldName": "buyStock", "javadoc": "记录当前可购买的商品库存数", "fieldType": "<PERSON>"}, {"fieldName": "sold", "javadoc": "商品销量", "fieldType": "<PERSON>"}, {"fieldName": "isSoldout", "javadoc": "商品售罄状态：0,未售罄 1售罄", "fieldType": "Integer"}, {"fieldName": "offShelfTime", "javadoc": "下架", "fieldType": "ZonedDateTime"}, {"fieldName": "flag", "javadoc": "0:不推荐;1:推荐", "fieldType": "Integer"}, {"fieldName": "extend1", "javadoc": "扩展字段1", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "extend2", "javadoc": "扩展字段2", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 8192}, {"fieldName": "extend3", "javadoc": "扩展字段3", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "flagBin", "javadoc": "扩展用，二制位标记(商品状态记录0-1-1-1-1-0)", "fieldType": "Integer"}, {"fieldName": "merchantCode", "javadoc": "商家编号", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 64}, {"fieldName": "turnover", "javadoc": "营业额", "fieldType": "<PERSON>"}, {"fieldName": "version", "javadoc": "版本号", "fieldType": "Integer"}], "changelogDate": "20180430131551", "javadoc": "分库分表：businessId", "entityTableName": "item_base", "dto": "no", "pagination": "no", "service": "no", "jpaMetamodelFiltering": false, "microserviceName": "stockcenter"}