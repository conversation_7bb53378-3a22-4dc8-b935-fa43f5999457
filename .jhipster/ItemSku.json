{"fluentMethods": true, "relationships": [], "fields": [{"fieldName": "itemskuId", "javadoc": "商品sku id，生成策略：自增+businessId后五位", "fieldType": "<PERSON>"}, {"fieldName": "businessId", "javadoc": "连锁id", "fieldType": "<PERSON>"}, {"fieldName": "storeId", "javadoc": "门店id", "fieldType": "<PERSON>"}, {"fieldName": "itemId", "javadoc": "商品id，生成策略：自增+businessId后五位", "fieldType": "<PERSON>"}, {"fieldName": "cspuId", "javadoc": "cspu id", "fieldType": "<PERSON>"}, {"fieldName": "title", "javadoc": "型号描述", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 64}, {"fieldName": "attrIds", "javadoc": "商品属性", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": 200}, {"fieldName": "img", "javadoc": "图片地址", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": 200}, {"fieldName": "stock", "javadoc": "商品库存", "fieldType": "<PERSON>"}, {"fieldName": "buyStock", "javadoc": "记录当前可购买的商品库存数", "fieldType": "<PERSON>"}, {"fieldName": "price", "javadoc": "商品价格", "fieldType": "<PERSON>"}, {"fieldName": "status", "javadoc": "1 show; 2 hide", "fieldType": "Integer"}, {"fieldName": "sold", "javadoc": "商品销量", "fieldType": "<PERSON>"}, {"fieldName": "skuMerchantCode", "javadoc": "外部商家商品编码", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 64}, {"fieldName": "extend", "javadoc": "扩展字段", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "version", "javadoc": "版本号", "fieldType": "Integer"}], "changelogDate": "20180430131553", "javadoc": "商品SKU表", "entityTableName": "item_sku", "dto": "no", "pagination": "no", "service": "no", "jpaMetamodelFiltering": false, "microserviceName": "stockcenter"}