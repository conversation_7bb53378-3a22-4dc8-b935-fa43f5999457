{"fluentMethods": true, "relationships": [], "fields": [{"fieldName": "name", "javadoc": "标签名称", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": 100}, {"fieldName": "level", "javadoc": "标签当前层级.0为顶层，最多两层", "fieldType": "Integer"}, {"fieldName": "parentId", "javadoc": "父标签ID", "fieldType": "<PERSON>"}, {"fieldName": "childrenMutiSelect", "javadoc": "如作为父标签，子标签是否多选.1为支持多选，0为非多选", "fieldType": "Integer"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "javadoc": "1:叶子;0:非叶子", "fieldType": "Integer"}, {"fieldName": "type", "javadoc": "标签类型:1普通业务标签 ;2资质标签;3用户输入标签", "fieldType": "Integer"}, {"fieldName": "target", "javadoc": "作用域:1商品;2卖家;3买家", "fieldType": "Integer"}, {"fieldName": "vertical", "javadoc": "垂直行业，对应后台1级类目", "fieldType": "<PERSON>"}, {"fieldName": "scene", "javadoc": "标签使用场景", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": 100}, {"fieldName": "ownerId", "javadoc": "标签归属", "fieldType": "<PERSON>"}, {"fieldName": "status", "javadoc": "标签状态.1:有效;0无效", "fieldType": "Integer"}, {"fieldName": "description", "javadoc": "描述", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 100}, {"fieldName": "starttime", "javadoc": "标签有效的起始时间", "fieldType": "ZonedDateTime"}, {"fieldName": "endtime", "javadoc": "标签结束时间", "fieldType": "ZonedDateTime"}, {"fieldName": "version", "javadoc": "版本号", "fieldType": "Integer"}], "changelogDate": "20180430131555", "javadoc": "标签表", "entityTableName": "tag_details", "dto": "no", "pagination": "no", "service": "no", "jpaMetamodelFiltering": false, "microserviceName": "stockcenter"}