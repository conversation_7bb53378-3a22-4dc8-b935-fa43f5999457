{"fluentMethods": true, "relationships": [], "fields": [{"fieldName": "keyId", "javadoc": "业务类型为0，表示item_skuId； 业务类型为1，待扩展", "fieldType": "<PERSON>"}, {"fieldName": "linkId", "javadoc": "业务类型为0，表示itemId； 业务类型为1，待扩展", "fieldType": "<PERSON>"}, {"fieldName": "seq", "javadoc": "单次分布式事务请求唯一seq号", "fieldType": "String", "fieldValidateRules": ["maxlength", "required"], "fieldValidateRulesMaxlength": 128}, {"fieldName": "type", "javadoc": "业务类型：0表示订单减库存；1 待扩展", "fieldType": "Integer"}, {"fieldName": "value", "javadoc": "业务类型为0：表示加减库存量；业务类型为1：待扩展", "fieldType": "<PERSON>"}, {"fieldName": "flag", "javadoc": "记录状态：0表示正常执行; 1表示已回滚", "fieldType": "Integer"}, {"fieldName": "version", "javadoc": "版本号", "fieldType": "Integer"}], "changelogDate": "20180430131554", "javadoc": "tcc log表", "entityTableName": "seq_log", "dto": "no", "pagination": "no", "service": "no", "jpaMetamodelFiltering": false, "microserviceName": "stockcenter"}