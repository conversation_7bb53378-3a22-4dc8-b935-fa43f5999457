{"fluentMethods": true, "relationships": [], "fields": [{"fieldName": "itemId", "javadoc": "商品id，生成策略：自增1+businessId后五位", "fieldType": "<PERSON>"}, {"fieldName": "businessId", "javadoc": "连锁id", "fieldType": "<PERSON>"}, {"fieldName": "storeId", "javadoc": "门店id", "fieldType": "<PERSON>"}, {"fieldName": "imgs", "javadoc": "商品图片列表地址集合", "fieldType": "String"}, {"fieldName": "titles", "javadoc": "图片描述(数组结构)", "fieldType": "String"}, {"fieldName": "itemComment", "javadoc": "商品描述", "fieldType": "String"}, {"fieldName": "extend1", "javadoc": "扩展字段1", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "extend2", "javadoc": "扩展字段2", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "extend3", "javadoc": "扩展字段3", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "extend4", "javadoc": "扩展字段4", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "extend5", "javadoc": "扩展字段5", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": 2048}, {"fieldName": "itemDetail", "javadoc": "对商品详情的描述， 格式为[{type:1,data}]", "fieldType": "String"}, {"fieldName": "version", "javadoc": "版本号", "fieldType": "Integer"}], "changelogDate": "20180430131552", "javadoc": "商品拓展信息表(商品单记录扩展) ;", "entityTableName": "item_info", "dto": "no", "pagination": "no", "service": "no", "jpaMetamodelFiltering": false, "microserviceName": "stockcenter"}