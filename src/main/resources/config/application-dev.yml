# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: http://www.jhipster.tech/profiles/
# More information on configuration properties: http://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
    level:
        ROOT: error
        com.cowell.pricecenter: debug
        io.github.jhipster: error
        com.cowell.pricecenter.mapper: debug

eureka:
    instance:
        prefer-ip-address: true
    client:
        service-url:
            #defaultZone: http://admin:${jhipster.registry.password}@*************:8761/eureka/
#            defaultZone: http://admin:${jhipster.registry.password}@registry.cowellhealth.net:8761/eureka/
            defaultZone: http://admin:${jhipster.registry.password}@************:8761/eureka/
#        fetch-registry: false


xxl.job:
    admin.addresses: http://xxl-test.gaojihealth.cn/xxl-job
    executor:
        appname: pricecenter-job
        ip:
        port: 18099
        logpath: /data/applogs/xxl-job/jobhandler
        logretentiondays: -1
    accessToken:

spring:
    cloud:
        stream:
            kafka:
                binder:
                    brokers: ************:9092,************:9092,************:9092
                    zkNodes: ************:2181,************:2181,************:2181/kafka-elk
            bindings:
                sleuth: #这里用stream给我们提供的默认output，后面会讲到自定义output
                    destination: sleuth
                    content-type: application/json
                    producer:
                        headerMode: raw
    profiles:
        active: dev
        include: swagger,no-liquibase
    devtools:
        restart:
            enabled: true
        livereload:
            enabled: false # we use gulp + BrowserSync for livereload
    jackson:
        serialization.indent_output: true
#    datasource:
#        type: com.zaxxer.hikari.HikariDataSource
#        driver-class-name: com.mysql.jdbc.Driver
##        url: jdbc:mysql://*************:3306/cowell_express?useUnicode=true&characterEncoding=utf8&useSSL=false&autoReconnect=true
#        url: *********************************************************************************************************************
#        username: scrmrw
#        password: 123qaz!@#
#        hikari:
#            data-source-properties:
#                cachePrepStmts: true
#                prepStmtCacheSize: 250
#                prepStmtCacheSqlLimit: 2048
#                useServerPrepStmts: true
    ds0:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: jdbc:mysql://**********:3306/cowell_pricecenter_0?useUnicode=true&characterEncoding=utf8&useSSL=false&statementInterceptors=com.zipkinMysql.MySQLStatementInterceptor
        username: root
        password: Gaoji_001#
        initial-size: 50
        min-idle: 10
        max-active: 50
        max-wait: 60000
        validation-query: SELECT 1
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000
    ds1:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: jdbc:mysql://**********:3306/cowell_pricecenter_1?useUnicode=true&characterEncoding=utf8&useSSL=false&statementInterceptors=com.zipkinMysql.MySQLStatementInterceptor
        username: root
        password: Gaoji_001#
        initial-size: 50
        min-idle: 10
        max-active: 50
        max-wait: 60000
        validation-query: SELECT 1
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000
    ds2:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: jdbc:mysql://**********:3306/cowell_pricecenter_2?useUnicode=true&characterEncoding=utf8&useSSL=false&statementInterceptors=com.zipkinMysql.MySQLStatementInterceptor
        username: root
        password: Gaoji_001#
        initial-size: 50
        min-idle: 10
        max-active: 50
        max-wait: 60000
        validation-query: SELECT 1
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000
    ds3:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.jdbc.Driver
        url: *********************************************************************************************************************************************************************
        username: root
        password: Gaoji_001#
        initial-size: 50
        min-idle: 10
        max-active: 50
        max-wait: 60000
        validation-query: SELECT 1
        test-while-idle: true
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 30000
        max-evictable-idle-time-millis: 60000

    mail:
        host: localhost
        port: 25
        username:
        password:
    messages:
        cache-seconds: 1
    thymeleaf:
        cache: false
    zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
        base-url: http://localhost:9411
        enabled: false
        locator:
            discovery:
                enabled: true
    rocketmq:
        # NameServer地址*************:9876
        name-server-address: ***********:9876;***********:9876
        name-server-address2: ***********:9876;***********:9876
        name-server-address3: ***********:9876;***********:9876
        name-server-address4: ***********:9876;***********:9876
        name-server-address-cube: ***********:9876;***********:9876
        # 可选, 如果无需发送消息则忽略该配置
        producer-group: priceCenter_local
        consumer-group: priceCenter_local
        pricecopy:
            producer-topic: PRICECOPY_TOPIC_DEV
            consumer-topic: PRICECOPY_TOPIC_DEV
        pricepushtask:
            producer-topic: PRICEPUSHTASK_TOPIC_DEV
            consumer-topic: PRICEPUSHTASK_TOPIC_DEV
        pricepushtask-exe:
            producer-topic: PRICEPUSHTASK_EXE_TOPIC_DEV
            consumer-topic: PRICEPUSHTASK_EXE_TOPIC_DEV
        pricepush:
            producer-topic: PRICEPUSH_TOPIC_DEV
            consumer-topic: PRICEPUSH_TOPIC_DEV
        erp_callback_price:
            consumer-topic: ERP_CALLBACK_%s_SERVICE_TOPIC_DEV
            group: PRICE_GROUP_DEV
            adjust-org-store-consumer-group: PRICE_ORG_STORE_GROUP_DEV
            tag: ERP_CALLBACK_%s_SERVICE_TAG_DEV
        price-detail-history:
            producer-topic: PRICE_DETAIL_HISTORY_TOPIC_DEV
            consumer-topic: PRICE_DETAIL_HISTORY_TOPIC_DEV
        zdt-mq-message-push-group : digitalstore_message_group
        zdt-mq-message-push-topic : digitalstore_DEV_message_topic
        zdt-mq-message-push-tag : digitalstore_DEV_message_tag
        price-getesdata:
            consumer-topic: AUCTION_SPU_EXTEND_BINLOG
        price-store-detail-group : PRICE_STORE_DETAIL_GROUP_LOCAL
        price-store-detail-topic : PRICE_STORE_DETAIL_LOCAL
        price-store-detail-tag : PRICE_STORE_DETAIL_TAG_LOCAL
        price_increment_sync_hd:
            consumer-topic: ERP_CALLBACK_STOCK_SERVICE_TOPIC_DEV
            group: ERP_CALLBACK_STOCK_SERVICE_GROUP_DEV
            tag: ERP_CALLBACK_PRICE_SERVICE_TAG_DEV
        price_hd_compensate:
            consumer-topic: ERP_CALLBACK_DATACORRECT_SERVICE_TOPIC_DEV
            group: ERP_CALLBACK_DATACORRECT_SERVICE_GROUP_DEV
        price_all_sync_hd:
            consumer-topic: PRICE_SYNC_ALL_HD_TOPIC_DEV
            group: PRICE_SYNC_ALL_HD_GROUP_DEV
        price_all_sync_self:
            producer-topic: PRICE_SYNC_ALL_SELF_TOPIC_DEV
            consumer-topic: PRICE_SYNC_ALL_SELF_TOPIC_DEV
            consumer-group: PRICE_SYNC_ALL_SELF_GROUP_DEV
            producer-group: PRICE_SYNC_ALL_SELF_GROUP_DEV
        price_sync_center_store:
            producer-topic: PRICE_SYNC_CENTER_STORE_TOPIC_DEV
            consumer-topic: PRICE_SYNC_CENTER_STORE_TOPIC_DEV
            producer-group: PRICE_SYNC_CENTER_STORE_GROUP_DEV
            consumer-group: PRICE_SYNC_CENTER_STORE_GROUP_DEV
        price_sync_self:
            producer-topic: PRICE_SYNC_SELF_TOPIC_DEV
            consumer-topic: PRICE_SYNC_SELF_TOPIC_DEV
            consumer-group: PRICE_SYNC_SELF_GROUP_DEV
            producer-group: PRICE_SYNC_SELF_GROUP_DEV
        price_sync_compensate_self:
            producer-topic: PRICE_SYNC_COMPENSATE_SELF_TOPIC_DEV
            consumer-topic: PRICE_SYNC_COMPENSATE_SELF_TOPIC_DEV
            producer-group: PRICE_SYNC_COMPENSATE_SELF_GROUP_DEV
            consumer-group: PRICE_SYNC_COMPENSATE_SELF_GROUP_DEV
        pricepush-yjs-topic: PRICEPUSH_YJS_TOPIC_LOCAL
        pricewarning:
            producer-topic: PRICEWARNING-TOPIC-TEST
            consumer-topic: PRICEWARNING-TOPIC-TEST
            consumer-group: PRICEWARNING-GROUP-TEST
            producer-group: PRICEWARNING-GROUP-TEST
            tag: PRICEWARNING-TAG-TEST
        price_sync_task:
            producer-topic: PRICE_SYNC_TASK_TOPIC_DEV
            consumer-topic: PRICE_SYNC_TASK_TOPIC_DEV
            producer-group: PRICE_SYNC_TASK_GROUP_DEV
            consumer-group: PRICE_SYNC_TASK_GROUP_DEV
        auctionspu_to_adjustprice:
            consumer-topic: ADJUST_PRICE_CATEGORY_TOPIC_DEV
            group: ADJUST_PRICE_CATEGORY_GROUP_DEV
        auctionspu_to_adjustprice_self:
            producer-topic: AUCTIONSPU_TO_ADJUSTPRICE_SELF_TOPIC_DEV
            consumer-topic: AUCTIONSPU_TO_ADJUSTPRICE_SELF_TOPIC_DEV
            consumer-group: AUCTIONSPU_TO_ADJUSTPRICE_SELF_GROUP_DEV
            producer-group: AUCTIONSPU_TO_ADJUSTPRICE_SELF_GROUP_DEV
        initialize_price:
            producer-topic: INITIALIZE_PRICE_TOPIC_DEV
            consumer-topic: INITIALIZE_PRICE_TOPIC_DEV
            producer-group: INITIALIZE_PRICE_GROUP_DEV
            consumer-group: INITIALIZE_PRICE_GROUP_DEV
        goj:
            consumer-topic: GOODS_OF_JOINT_TOPIC_DEV
            group: GOODS_OF_JOINT_GROUP_DEV
        compare-price-self:
            consumer-topic: COMPARE_PRODUCER_ONESELF_TOPIC_DEV
            tag: COMPARE_PRODUCER_ONESELF_TAG_DEV
            group: COMPARE_PRODUCER_ONESELF_GROUP_DEV

        sync_item_third_topic: MB_SYNC_ITEM_THIRD_TOPIC_dev
        sync_item_third_group: MB_SYNC_ITEM_THIRD_GROUP_dev
        push_price_store_spu_topic: PUSH_PRICE_STORE_SPU_TOPIC_DEV
        push_price_store_spu_group: PUSH_PRICE_STORE_SPU_GROUP_DEV
        push_price_store_spu_tag: PUSH_PRICE_STORE_SPU_TAG_DEV
        his_price_self:
            producer-topic: HIS_PRICE_SELF_TOPIC_DEV
            consumer-topic: HIS_PRICE_SELF_TOPIC_DEV
            consumer-group: HIS_PRICE_SELF_GROUP_DEV
            producer-group: HIS_PRICE_SELF_GROUP_DEV
        compare_producer_oneself_topic2: compare_producer_oneself_topic2_DEV
        compare_producer_oneself_groupName2: compare_producer_oneself_groupName2_DEV
        #erp推送cmall订单状态\积分订单\退货销售单号
        erp-callback-service:
            consumer-topic: ERP_CALLBACK_ORDER_SERVICE_TOPIC_DEV
        #成本
        erp-callback-store-cost-tag: ERP_CALLBACK_STORE_COST_SERVICE_TAG_DEV
        erp-callback-store-cost-group: ERP_CALLBACK_STORE_COST_PC_GROUP_DEV
        store_cost_data_transfer:
            consumer-topic: FIX_COST_DATA_TRANSFER_TOPIC_DEV
            group: FIX_COST_DATA_TRANSFER_GROUP_DEV
        store_cost_data_transfer_group: FIX_COST_DATA_TRANSFER_GROUP_DEV
        store_cost_data_transfer_topic: FIX_COST_DATA_TRANSFER_TOPIC_DEV

        adjust_price_order_execute:
            producer-topic: ADJUST_PRICE_ORDER_EXECUTE_TOPIC_DEV
            producer-group: ADJUST_PRICE_ORDER_EXECUTE_GROUP_DEV
            consumer-topic: ADJUST_PRICE_ORDER_EXECUTE_TOPIC_DEV
            consumer-group: ADJUST_PRICE_ORDER_EXECUTE_GROUP_DEV
            consumer-medium-group: ADJUST_PRICE_ORDER_EXECUTE_MEDIUM_GROUP_DEV
            consumer-high-group: ADJUST_PRICE_ORDER_EXECUTE_HIGH_GROUP_DEV

        adjust_price_order_detail_supplement_data:
            producer-topic: ADJUST_PRICE_ORDER_DETAIL_SUPPlEMENT_DATA_TOPIC_DEV
            producer-group: ADJUST_PRICE_ORDER_DETAIL_SUPPlEMENT_DATA_GROUP_DEV
            consumer-topic: ADJUST_PRICE_ORDER_DETAIL_SUPPlEMENT_DATA_TOPIC_DEV
            consumer-group: ADJUST_PRICE_ORDER_DETAIL_SUPPlEMENT_DATA_GROUP_DEV

        #管控单审核通过之后计算管控单对应的门店商品明细
        calculate_control_manage_order_store_goods_detail:
            producer-topic: controlOrderDetailCal_TOPIC_DEV
            producer-group: controlOrderDetailCal_GROUP_DEV
            #自产自销用的
            consumer-topic: controlOrderDetailCal_TOPIC_DEV
            consumer-group: CONSUMER_CONTROL_STORE_GOODS_DETAIL_CAL_GROUP_DEV

        #生效管控单门店商品明细(计算门店商品价格不在管控范围之内的商品)
        effect_control_manage_order_store_detail_topic:
            producer-topic: CONTROL_ORDER_STORE_DETAIL_EFFECT_TOPIC_DEV
            producer-group: CONTROL_ORDER_STORE_DETAIL_EFFECT_GROUP_DEV
            #自产自销用的
            consumer-topic: CONTROL_ORDER_STORE_DETAIL_EFFECT_TOPIC_DEV
            consumer-group: CONSUMER_EFFECT_CONTROL_STORE_DETAIL_GROUP_DEV

        # 提交OA审核
        price-oa-target-topic: PRICE_OA_TARGET_TOPIC_DEV
        price-oa-target-group: PRICE_OA_TARGET_GROUP_DEV
        price-oa-target:
            producer-topic: PRICE_OA_TARGET_TOPIC_DEV
            producer-group: PRICE_OA_TARGET_GROUP_DEV
            consumer-topic: PRICE_OA_TARGET_TOPIC_DEV
            consumer-group: PRICE_OA_TARGET_GROUP_DEV

        # 接收OA结果
        price-oa-source-topic: PRICE_OA_SOURCE_TOPIC_DEV
        price-oa-source-group: PRICE_OA_SOURCE_GROUP_DEV
        price-oa-source:
            producer-topic: PRICE_OA_SOURCE_TOPIC_DEV
            producer-group: PRICE_OA_SOURCE_GROUP_DEV
            consumer-topic: PRICE_OA_SOURCE_TOPIC_DEV
            consumer-group: PRICE_OA_SOURCE_GROUP_DEV

        # 价格复制
        price-copy-topic: PRICE_COPY_TOPIC_DEV
        price-copy-group: PRICE_COPY_GROUP_DEV
        price-copy:
            producer-topic: PRICE_COPY_TOPIC_DEV
            producer-group: PRICE_COPY_GROUP_DEV
            consumer-topic: PRICE_COPY_TOPIC_DEV
            consumer-group: PRICE_COPY_GROUP_DEV
        compare_producer_oneself:
            groupName2: groupName2

        # 调价单审核状态变更通知
        adjustOrderChangeStatus:
            producer-topic: ADJUST_PRICE_ORDER_STATUS_CHANGE_TOPIC_DEV
            producer-group: ADJUST_PRICE_ORDER_STATUS_CHANGE_GROUP_DEV
            consumer-topic: ADJUST_PRICE_ORDER_STATUS_CHANGE_TOPIC_DEV
            consumer-group: ADJUST_PRICE_ORDER_STATUS_CHANGE_GROUP_DEV


        control_order_detail_supplement_data:
            producer-topic: CONTROL_ORDER_DETAIL_SUPPlEMENT_DATA_TOPIC_DEV
            producer-group: CONTROL_ORDER_DETAIL_SUPPlEMENT_DATA_GROUP_DEV
            consumer-topic: CONTROL_ORDER_DETAIL_SUPPlEMENT_DATA_TOPIC_DEV
            consumer-group: CONTROL_ORDER_DETAIL_SUPPlEMENT_DATA_GROUP_DEV

        price_change:
            producer-topic: PRICE_CHANGE_TOPIC_DEV
            producer-group: PRICE_CHANGE_GROUP_DEV
            consumer-topic: PRICE_CHANGE_TOPIC_DEV
            consumer-group: PRICE_CHANGE_GROUP_DEV

        price_update:
            consumer-topic: PRICE_UPDATE_TOPIC_DEV
            consumer-group: PRICE_UPDATE_GROUP_DEV
        adjust_price_order_org_store_detail_extend:
            producer-topic: ADJUST_PRICE_ORDER_ORG_STORE_DETAIL_EXTEND_TOPIC_DEV
            producer-group: ADJUST_PRICE_ORDER_ORG_STORE_DETAIL_EXTEND_GROUP_DEV

        org_store_detail_extend_history:
            topic: ADJUST_PRICE_ORDER_ORG_STORE_DETAIL_EXTEND_HISTORY_TOPIC_DEV
            group: ADJUST_PRICE_ORDER_ORG_STORE_DETAIL_EXTEND_HISTORY_GROUP_DEV

        #补充orgStore数据
        adjust_price_order_org_store_detail_actual_extend:
            topic: PRICE_ORDER_ORG_STORE_DETAIL_ACTUAL_EXTEND_TOPIC_DEV
            group: PRICE_ORDER_ORG_STORE_DETAIL_ACTUAL_EXTEND_TOPIC_DEV

        synch_waretype_actiname:
            topic: SYNCH_WARETYPE_ACTINAME_TOPIC_DEV
            group: SYNCH_WARETYPE_ACTINAME_GROUP_DEV

        price_order_detail_limitstatus:
            topic: PRICE_ORDER_DETAIL_LIMITSTATUS_TOPIC_DEV
            group: PRICE_ORDER_DETAIL_LIMITSTATUS_TOPIC_DEV

        price_data_fix:
            consumer-topic: COMPARE_DATA_FIX_PRICECENTER_TOPIC_DEV
            consumer-group: COMPARE_DATA_FIX_PRICECENTER_GROUP_DEV
            consumer-tag: COMPARE_DATA_FIX_PRICECENTER_TAG_DEV
        erp_store_group_change:
            producer-topic: ERP_STORE_GROUP_CHANGE_TOPIC_DEV
            producer-group: ERP_STORE_GROUP_CHANGE_GROUP_DEV

        push_price_to_channel:
            producer-topic: PUSH_PRICE_TO_CHANNEL_TOPIC_DEV
            producer-group: PUSH_PRICE_TO_CHANNEL_GROUP_DEV

spring.sleuth.stream.enabled: false

liquibase:
    contexts: dev

# ===================================================================
# To enable SSL, generate a certificate using:
# keytool -genkey -alias pay -storetype PKCS12 -keyalg RSA -keysize 2048 -keystore keystore.p12 -validity 3650
#
# You can also use Let's Encrypt:
# https://maximilian-boehm.com/hp2121/Create-a-Java-Keystore-JKS-from-Let-s-Encrypt-Certificates.htm
#
# Then, modify the server.ssl properties so your "server" configuration looks like:
#
# server:
#    port: 8443
#    ssl:
#        key-store: keystore.p12
#        key-store-password: <your-password>
#        key-store-type: PKCS12
#        key-alias: pay
# ===================================================================
server:
    port: 9070

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: http://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
    http:
        version: V_1_1 # To use HTTP/2 you will need SSL support (see above the "server.ssl" configuration)
    # CORS is disabled by default on microservices, as you should access them through a gateway.
    # If you want to enable it, please uncomment the configuration below.
    # cors:
        # allowed-origins: "*"
        # allowed-methods: "*"
        # allowed-headers: "*"
        # exposed-headers: "Authorization,Link,X-Total-Count"
        # allow-credentials: true
        # max-age: 1800
    security:
        client-authorization:
            access-token-uri: http://uaa/oauth/token
            token-service-id: uaa
            client-id: internal
            client-secret: internal
    mail: # specific JHipster mail property, for standard properties see MailProperties
        from: paycenter@localhost
        base-url: http://127.0.0.1:9060
    metrics: # DropWizard Metrics configuration, used by MetricsConfiguration
        jmx.enabled: true
        graphite: # Use the "graphite" Maven profile to have the Graphite dependencies
            enabled: false
            host: localhost
            port: 2003
            prefix: pricecenter
        prometheus: # Use the "prometheus" Maven profile to have the Prometheus dependencies
            enabled: false
            endpoint: /prometheusMetrics
        logs: # Reports Dropwizard metrics in the logs
            enabled: false
            report-frequency: 60 # in seconds
    logging:
        logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
            enabled: false
            host: localhost
            port: 5000
            queue-size: 512
        spectator-metrics: # Reports Spectator Circuit Breaker metrics in the logs
            enabled: false
            # edit spring.metrics.export.delay-millis to set report frequency

oauth2:
    signature-verification:
        public-key-endpoint-uri: http://uaa/oauth/token_key
        #ttl for public keys to verify JWT tokens (in ms)
        ttl: 3600000
        #max. rate at which public keys will be fetched (in ms)
        public-key-refresh-rate-limit: 10000
    web-client-configuration:
        #keep in sync with UAA configuration
        client-id: web_app
        secret: changeit

#Redis
spring.redisson:
    address[0]: redis://common_microservice_redis-01_test.cowelltech.com:6379
    read-mode: MASTER

#     address[0]: redis://***********:7000
#     address[1]: redis://***********:7001
#     address[2]: redis://***********:7000
#     address[3]: redis://***********:7001
#     address[4]: redis://***********:7000
#     address[5]: redis://***********:7001

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# http://www.jhipster.tech/common-application-properties/
# ===================================================================
mybatis:
    mapper-locations: classpath:mapper/*.xml
    type-aliases-package: com.example.demo.model
application:

tencent:
    cos:
        region: ap-beijing
        appId: 1256038144
        secretId: AKID53pPq6r2nRzMYFTETWN6d3IMXcWFOYWK
        secretKey: qW4ThmfaXRBkYxCKGE0e7Uow0zoR3Yt2
        baseUrl: https://BUCKET.cos.REGION.myqcloud.com/
        defaultBucketName: gjscrm

#本地开发联调标识
local:
    dev:
     flag: true


push:
    batch:
        size: 1

business:
    mapping:
        hd: 22
        yk: 22
    showUnprice:
        url: 222
excel:
    import:
        detail:
            size: 1
        goods:
            size: 1
        pricelable:
            size: 1
        adjustPriceOrder:
            size: 1
    store:
        master:
            rolecode: YDZ

apollo:
    bootstrap:
        enabled: true
syncPrice:
    enable: 1


store:
    master:
        rolecode: 1
    showprice:
        url: http://mobile-test.gaojihealth.cn/app/modifyprice/modifypriceMessage

name-server-address1: ***********:9876;***********:9876

price:
    refresh:
        business: 99999

noticeHDSendData: 1
price.warn.rolecode: YJSLSYY
price.warn.url: 123

env.active: dev

store.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
store.ribbon.listOfServers: http://***********:10160

permission.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
permission.ribbon.listOfServers: http://***********:10129

searchapi.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
searchapi.ribbon.listOfServers: http://***********:10143

itemcenter.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
itemcenter.ribbon.listOfServers: http://**********:10082

erpsaas.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
erpsaas.ribbon.listOfServers: http://**********:10049

toc.ribbon.NIWSServerListClassName: com.netflix.loadbalancer.ConfigurationBasedServerList
toc.ribbon.listOfServers: http://***********:10162
ribbon:
    ReadTimeount: 5000
    ConnectTimeout: 5000
#    eureka










