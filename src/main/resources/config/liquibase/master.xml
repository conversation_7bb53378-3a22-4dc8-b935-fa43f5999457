<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <include file="config/liquibase/changelog/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>

    <!-- jhipster-needle-liquibase-add-changelog - JHip<PERSON> will add liquibase changelogs here -->
    <!-- jhipster-needle-liquibase-add-constraints-changelog - J<PERSON>ip<PERSON> will add liquibase constraints changelogs here -->
</databaseChangeLog>
