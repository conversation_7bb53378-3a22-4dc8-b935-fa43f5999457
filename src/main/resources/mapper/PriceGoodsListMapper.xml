<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceGoodsListMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceGoodsList">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_no" jdbcType="VARCHAR" property="storeNo" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="goods_code" jdbcType="VARCHAR" property="goodsCode" />
    <result column="list_price" jdbcType="DECIMAL" property="listPrice" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="created_by_name" jdbcType="VARCHAR" property="createdByName" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, store_no, store_id, goods_code, list_price, `status`, extend, gmt_create,
    created_by, created_by_name, gmt_update, updated_by, updated_by_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceGoodsListExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_goods_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_goods_list
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_goods_list
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceGoodsListExample">
    delete from price_goods_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceGoodsList">
    insert into price_goods_list (id, business_id, store_no,
      store_id, goods_code, list_price,
      `status`, extend, gmt_create,
      created_by, created_by_name, gmt_update,
      updated_by, updated_by_name)
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{storeNo,jdbcType=VARCHAR},
      #{storeId,jdbcType=BIGINT}, #{goodsCode,jdbcType=VARCHAR}, #{listPrice,jdbcType=DECIMAL},
      #{status,jdbcType=TINYINT}, #{extend,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{createdBy,jdbcType=VARCHAR}, #{createdByName,jdbcType=VARCHAR}, #{gmtUpdate,jdbcType=TIMESTAMP},
      #{updatedBy,jdbcType=VARCHAR}, #{updatedByName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceGoodsList">
    insert into price_goods_list
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="goodsCode != null">
        goods_code,
      </if>
      <if test="listPrice != null">
        list_price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdByName != null">
        created_by_name,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsCode != null">
        #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="listPrice != null">
        #{listPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdByName != null">
        #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceGoodsListExample" resultType="java.lang.Long">
    select count(*) from price_goods_list
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_goods_list
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeNo != null">
        store_no = #{record.storeNo,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsCode != null">
        goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="record.listPrice != null">
        list_price = #{record.listPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createdByName != null">
        created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_goods_list
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_no = #{record.storeNo,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      goods_code = #{record.goodsCode,jdbcType=VARCHAR},
      list_price = #{record.listPrice,jdbcType=DECIMAL},
      `status` = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceGoodsList">
    update price_goods_list
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsCode != null">
        goods_code = #{goodsCode,jdbcType=VARCHAR},
      </if>
      <if test="listPrice != null">
        list_price = #{listPrice,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdByName != null">
        created_by_name = #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceGoodsList">
    update price_goods_list
    set business_id = #{businessId,jdbcType=BIGINT},
      store_no = #{storeNo,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      goods_code = #{goodsCode,jdbcType=VARCHAR},
      list_price = #{listPrice,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      created_by_name = #{createdByName,jdbcType=VARCHAR},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into price_goods_list (business_id, store_no, store_id, goods_code, list_price, status, extend, gmt_create, created_by, created_by_name, gmt_update, updated_by, updated_by_name)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.businessId,jdbcType=BIGINT},
            #{item.storeNo,jdbcType=VARCHAR},
            #{item.storeId,jdbcType=BIGINT},
            #{item.goodsCode,jdbcType=VARCHAR},
            #{item.listPrice,jdbcType=DECIMAL},
            #{item.status,jdbcType=TINYINT},
            #{item.extend,jdbcType=VARCHAR},
            #{item.gmtCreate,jdbcType=TIMESTAMP},
            #{item.createdBy,jdbcType=VARCHAR},
            #{item.createdByName,jdbcType=VARCHAR},
            #{item.gmtUpdate,jdbcType=TIMESTAMP},
            #{item.updatedBy,jdbcType=VARCHAR},
            #{item.updatedByName,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update price_goods_list
            set
            list_price = #{item.listPrice,jdbcType=DECIMAL},
            gmt_update = #{item.gmtUpdate,jdbcType=TIMESTAMP},
            updated_by = #{item.updatedBy,jdbcType=VARCHAR},
            updated_by_name = #{item.updatedByName,jdbcType=VARCHAR}
            where store_no = #{item.storeNo,jdbcType=VARCHAR} and goods_code = #{item.goodsCode,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectByStoreAndGoodsPairs" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from price_goods_list
        where (store_no, goods_code) in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            (#{item.storeNo,jdbcType=VARCHAR}, #{item.goodsCode,jdbcType=VARCHAR})
        </foreach>
    </select>
</mapper>
