<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceLimitControlConfigMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceLimitControlConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="limit_type" jdbcType="INTEGER" property="limitType" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="price_types" jdbcType="VARCHAR" property="priceTypes" />
    <result column="org_ids" jdbcType="VARCHAR" property="orgIds" />
    <result column="goods_count" jdbcType="BIGINT" property="goodsCount" />
    <result column="markup_ratio_limit" jdbcType="DECIMAL" property="markupRatioLimit" />
    <result column="markup_amount_limit" jdbcType="DECIMAL" property="markupAmountLimit" />
    <result column="warning_period" jdbcType="VARCHAR" property="warningPeriod" />
    <result column="warning_period_value" jdbcType="VARCHAR" property="warningPeriodValue" />
    <result column="intervention_measures" jdbcType="VARCHAR" property="interventionMeasures" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_by_name" jdbcType="VARCHAR" property="createdByName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, limit_type, rule_name, price_types, org_ids, goods_count, markup_ratio_limit,
    markup_amount_limit, warning_period, warning_period_value, intervention_measures,
    `status`, gmt_create, gmt_update, extend, version, created_by, created_by_name, updated_by,
    updated_by_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_limit_control_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_limit_control_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_limit_control_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfigExample">
    delete from price_limit_control_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfig">
    insert into price_limit_control_config (id, limit_type, rule_name,
      price_types, org_ids, goods_count,
      markup_ratio_limit, markup_amount_limit, warning_period,
      warning_period_value, intervention_measures,
      `status`, gmt_create, gmt_update,
      extend, version, created_by,
      created_by_name, updated_by, updated_by_name
      )
    values (#{id,jdbcType=BIGINT}, #{limitType,jdbcType=INTEGER}, #{ruleName,jdbcType=VARCHAR},
      #{priceTypes,jdbcType=VARCHAR}, #{orgIds,jdbcType=VARCHAR}, #{goodsCount,jdbcType=BIGINT},
      #{markupRatioLimit,jdbcType=DECIMAL}, #{markupAmountLimit,jdbcType=DECIMAL}, #{warningPeriod,jdbcType=VARCHAR},
      #{warningPeriodValue,jdbcType=VARCHAR}, #{interventionMeasures,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP},
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT},
      #{createdByName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfig" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    insert into price_limit_control_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="limitType != null">
        limit_type,
      </if>
      <if test="ruleName != null">
        rule_name,
      </if>
      <if test="priceTypes != null">
        price_types,
      </if>
      <if test="orgIds != null">
        org_ids,
      </if>
      <if test="goodsCount != null">
        goods_count,
      </if>
      <if test="markupRatioLimit != null">
        markup_ratio_limit,
      </if>
      <if test="markupAmountLimit != null">
        markup_amount_limit,
      </if>
      <if test="warningPeriod != null">
        warning_period,
      </if>
      <if test="warningPeriodValue != null">
        warning_period_value,
      </if>
      <if test="interventionMeasures != null">
        intervention_measures,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdByName != null">
        created_by_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="limitType != null">
        #{limitType,jdbcType=INTEGER},
      </if>
      <if test="ruleName != null">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypes != null">
        #{priceTypes,jdbcType=VARCHAR},
      </if>
      <if test="orgIds != null">
        #{orgIds,jdbcType=VARCHAR},
      </if>
      <if test="goodsCount != null">
        #{goodsCount,jdbcType=BIGINT},
      </if>
      <if test="markupRatioLimit != null">
        #{markupRatioLimit,jdbcType=DECIMAL},
      </if>
      <if test="markupAmountLimit != null">
        #{markupAmountLimit,jdbcType=DECIMAL},
      </if>
      <if test="warningPeriod != null">
        #{warningPeriod,jdbcType=VARCHAR},
      </if>
      <if test="warningPeriodValue != null">
        #{warningPeriodValue,jdbcType=VARCHAR},
      </if>
      <if test="interventionMeasures != null">
        #{interventionMeasures,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null">
        #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfigExample" resultType="java.lang.Long">
    select count(*) from price_limit_control_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_limit_control_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.limitType != null">
        limit_type = #{record.limitType,jdbcType=INTEGER},
      </if>
      <if test="record.ruleName != null">
        rule_name = #{record.ruleName,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypes != null">
        price_types = #{record.priceTypes,jdbcType=VARCHAR},
      </if>
      <if test="record.orgIds != null">
        org_ids = #{record.orgIds,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsCount != null">
        goods_count = #{record.goodsCount,jdbcType=BIGINT},
      </if>
      <if test="record.markupRatioLimit != null">
        markup_ratio_limit = #{record.markupRatioLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.markupAmountLimit != null">
        markup_amount_limit = #{record.markupAmountLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.warningPeriod != null">
        warning_period = #{record.warningPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.warningPeriodValue != null">
        warning_period_value = #{record.warningPeriodValue,jdbcType=VARCHAR},
      </if>
      <if test="record.interventionMeasures != null">
        intervention_measures = #{record.interventionMeasures,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdByName != null">
        created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_limit_control_config
    set id = #{record.id,jdbcType=BIGINT},
      limit_type = #{record.limitType,jdbcType=INTEGER},
      rule_name = #{record.ruleName,jdbcType=VARCHAR},
      price_types = #{record.priceTypes,jdbcType=VARCHAR},
      org_ids = #{record.orgIds,jdbcType=VARCHAR},
      goods_count = #{record.goodsCount,jdbcType=BIGINT},
      markup_ratio_limit = #{record.markupRatioLimit,jdbcType=DECIMAL},
      markup_amount_limit = #{record.markupAmountLimit,jdbcType=DECIMAL},
      warning_period = #{record.warningPeriod,jdbcType=VARCHAR},
      warning_period_value = #{record.warningPeriodValue,jdbcType=VARCHAR},
      intervention_measures = #{record.interventionMeasures,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfig">
    update price_limit_control_config
    <set>
      <if test="limitType != null">
        limit_type = #{limitType,jdbcType=INTEGER},
      </if>
      <if test="ruleName != null">
        rule_name = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypes != null">
        price_types = #{priceTypes,jdbcType=VARCHAR},
      </if>
      <if test="orgIds != null">
        org_ids = #{orgIds,jdbcType=VARCHAR},
      </if>
      <if test="goodsCount != null">
        goods_count = #{goodsCount,jdbcType=BIGINT},
      </if>
      <if test="markupRatioLimit != null">
        markup_ratio_limit = #{markupRatioLimit,jdbcType=DECIMAL},
      </if>
      <if test="markupAmountLimit != null">
        markup_amount_limit = #{markupAmountLimit,jdbcType=DECIMAL},
      </if>
      <if test="warningPeriod != null">
        warning_period = #{warningPeriod,jdbcType=VARCHAR},
      </if>
      <if test="warningPeriodValue != null">
        warning_period_value = #{warningPeriodValue,jdbcType=VARCHAR},
      </if>
      <if test="interventionMeasures != null">
        intervention_measures = #{interventionMeasures,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null">
        created_by_name = #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfig">
    update price_limit_control_config
    set limit_type = #{limitType,jdbcType=INTEGER},
      rule_name = #{ruleName,jdbcType=VARCHAR},
      price_types = #{priceTypes,jdbcType=VARCHAR},
      org_ids = #{orgIds,jdbcType=VARCHAR},
      goods_count = #{goodsCount,jdbcType=BIGINT},
      markup_ratio_limit = #{markupRatioLimit,jdbcType=DECIMAL},
      markup_amount_limit = #{markupAmountLimit,jdbcType=DECIMAL},
      warning_period = #{warningPeriod,jdbcType=VARCHAR},
      warning_period_value = #{warningPeriodValue,jdbcType=VARCHAR},
      intervention_measures = #{interventionMeasures,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_by_name = #{createdByName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
