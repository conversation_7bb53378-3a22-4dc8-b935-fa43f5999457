<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.AdjustPriceOrderOrgDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail">
    <!--@mbg.generated-->
    <!--@Table adjust_price_order_org_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_level" jdbcType="INTEGER" property="orgLevel" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, adjust_code, org_id, org_level, org_name, gmt_create, gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetailExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from adjust_price_order_org_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from adjust_price_order_org_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from adjust_price_order_org_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetailExample">
    <!--@mbg.generated-->
    delete from adjust_price_order_org_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail">
    <!--@mbg.generated-->
    insert into adjust_price_order_org_detail (id, adjust_code, org_id,
      org_level, org_name, gmt_create,
      gmt_update)
    values (#{id,jdbcType=BIGINT}, #{adjustCode,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT},
      #{orgLevel,jdbcType=INTEGER}, #{orgName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail">
    <!--@mbg.generated-->
    insert into adjust_price_order_org_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adjustCode != null and adjustCode != ''">
        adjust_code,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgLevel != null">
        org_level,
      </if>
      <if test="orgName != null and orgName != ''">
        org_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="adjustCode != null and adjustCode != ''">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgLevel != null">
        #{orgLevel,jdbcType=INTEGER},
      </if>
      <if test="orgName != null and orgName != ''">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetailExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from adjust_price_order_org_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update adjust_price_order_org_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgLevel != null">
        org_level = #{record.orgLevel,jdbcType=INTEGER},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update adjust_price_order_org_detail
    set id = #{record.id,jdbcType=BIGINT},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_level = #{record.orgLevel,jdbcType=INTEGER},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail">
    <!--@mbg.generated-->
    update adjust_price_order_org_detail
    <set>
      <if test="adjustCode != null and adjustCode != ''">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgLevel != null">
        org_level = #{orgLevel,jdbcType=INTEGER},
      </if>
      <if test="orgName != null and orgName != ''">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail">
    <!--@mbg.generated-->
    update adjust_price_order_org_detail
    set adjust_code = #{adjustCode,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_level = #{orgLevel,jdbcType=INTEGER},
      org_name = #{orgName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
