<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceWarningRecordMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceWarningRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="price_type" jdbcType="INTEGER" property="priceType" />
    <result column="src_price" jdbcType="BIGINT" property="srcPrice" />
    <result column="new_price" jdbcType="BIGINT" property="newPrice" />
    <result column="price_source" jdbcType="TINYINT" property="priceSource" />
    <result column="price_warn_type" jdbcType="TINYINT" property="priceWarnType" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="warn_type" jdbcType="TINYINT" property="warnType" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price_warn_version" jdbcType="VARCHAR" property="priceWarnVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, store_id, store_name, goods_no, sku_name, price_type, src_price,
    new_price, price_source, price_warn_type, last_update_time, created_by, gmt_create,
    updated_by, gmt_update, status, version, extend, warn_type, channel_id, price_type_code, price_warn_version
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceWarningRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_warning_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_warning_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_warning_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceWarningRecordExample">
    delete from price_warning_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceWarningRecord">
    insert into price_warning_record (id, business_id, store_id,
      store_name, goods_no, sku_name,
      price_type, src_price, new_price,
      price_source, price_warn_type, last_update_time,
      created_by, gmt_create, updated_by,
      gmt_update, status, version,
      extend, warn_type, channel_id, price_type_code, price_warn_version)
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
      #{storeName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR},
      #{priceType,jdbcType=INTEGER}, #{srcPrice,jdbcType=BIGINT}, #{newPrice,jdbcType=BIGINT},
      #{priceSource,jdbcType=TINYINT}, #{priceWarnType,jdbcType=TINYINT}, #{lastUpdateTime,jdbcType=TIMESTAMP},
      #{createdBy,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR},
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{version,jdbcType=INTEGER},
      #{extend,jdbcType=VARCHAR}, #{warnType,jdbcType=TINYINT}, #{channelId,jdbcType=INTEGER},
      #{priceTypeCode,jdbcType=VARCHAR}, #{priceWarnVersion,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceWarningRecord">
    insert into price_warning_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="priceType != null">
        price_type,
      </if>
      <if test="srcPrice != null">
        src_price,
      </if>
      <if test="newPrice != null">
        new_price,
      </if>
      <if test="priceSource != null">
        price_source,
      </if>
      <if test="priceWarnType != null">
        price_warn_type,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="warnType != null">
        warn_type,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="priceTypeCode != null">
        price_type_code,
      </if>
      <if test="priceWarnVersion != null">
        price_warn_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="priceType != null">
        #{priceType,jdbcType=INTEGER},
      </if>
      <if test="srcPrice != null">
        #{srcPrice,jdbcType=BIGINT},
      </if>
      <if test="newPrice != null">
        #{newPrice,jdbcType=BIGINT},
      </if>
      <if test="priceSource != null">
        #{priceSource,jdbcType=TINYINT},
      </if>
      <if test="priceWarnType != null">
        #{priceWarnType,jdbcType=TINYINT},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="warnType != null">
        #{warnType,jdbcType=TINYINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="priceTypeCode != null">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceWarnVersion != null">
        #{priceWarnVersion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceWarningRecordExample" resultType="java.lang.Long">
    select count(*) from price_warning_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_warning_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null">
        sku_name = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.priceType != null">
        price_type = #{record.priceType,jdbcType=INTEGER},
      </if>
      <if test="record.srcPrice != null">
        src_price = #{record.srcPrice,jdbcType=BIGINT},
      </if>
      <if test="record.newPrice != null">
        new_price = #{record.newPrice,jdbcType=BIGINT},
      </if>
      <if test="record.priceSource != null">
        price_source = #{record.priceSource,jdbcType=TINYINT},
      </if>
      <if test="record.priceWarnType != null">
        price_warn_type = #{record.priceWarnType,jdbcType=TINYINT},
      </if>
      <if test="record.lastUpdateTime != null">
        last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.warnType != null">
        warn_type = #{record.warnType,jdbcType=TINYINT},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.priceTypeCode != null">
        price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceWarnVersion != null">
        price_warn_version = #{record.priceWarnVersion,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_warning_record
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      price_type = #{record.priceType,jdbcType=INTEGER},
      src_price = #{record.srcPrice,jdbcType=BIGINT},
      new_price = #{record.newPrice,jdbcType=BIGINT},
      price_source = #{record.priceSource,jdbcType=TINYINT},
      price_warn_type = #{record.priceWarnType,jdbcType=TINYINT},
      last_update_time = #{record.lastUpdateTime,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      version = #{record.version,jdbcType=INTEGER},
      extend = #{record.extend,jdbcType=VARCHAR},
      warn_type = #{record.warnType,jdbcType=TINYINT},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price_warn_version = #{record.priceWarnVersion,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceWarningRecord">
    update price_warning_record
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="priceType != null">
        price_type = #{priceType,jdbcType=INTEGER},
      </if>
      <if test="srcPrice != null">
        src_price = #{srcPrice,jdbcType=BIGINT},
      </if>
      <if test="newPrice != null">
        new_price = #{newPrice,jdbcType=BIGINT},
      </if>
      <if test="priceSource != null">
        price_source = #{priceSource,jdbcType=TINYINT},
      </if>
      <if test="priceWarnType != null">
        price_warn_type = #{priceWarnType,jdbcType=TINYINT},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="warnType != null">
        warn_type = #{warnType,jdbcType=TINYINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="priceTypeCode != null">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceWarnVersion != null">
        price_warn_version = #{priceWarnVersion,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceWarningRecord">
    update price_warning_record
    set business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      price_type = #{priceType,jdbcType=INTEGER},
      src_price = #{srcPrice,jdbcType=BIGINT},
      new_price = #{newPrice,jdbcType=BIGINT},
      price_source = #{priceSource,jdbcType=TINYINT},
      price_warn_type = #{priceWarnType,jdbcType=TINYINT},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      version = #{version,jdbcType=INTEGER},
      extend = #{extend,jdbcType=VARCHAR},
      warn_type = #{warnType,jdbcType=TINYINT},
      channel_id = #{channelId,jdbcType=INTEGER},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_warn_version = #{priceWarnVersion,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
