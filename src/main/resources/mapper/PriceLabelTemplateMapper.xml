<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceLabelTemplateMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceLabelTemplate">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="template_info" jdbcType="VARCHAR" property="templateInfo" />
    <result column="template_width" jdbcType="VARCHAR" property="templateWidth" />
    <result column="template_height" jdbcType="VARCHAR" property="templateHeight" />
    <result column="template_type" jdbcType="INTEGER" property="templateType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, store_id, org_id, template_name, template_info, template_width, 
    template_height, template_type, remark, status, gmt_create, gmt_update, extend, version, 
    created_by, updated_by, updated_by_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceLabelTemplateExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_label_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_label_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_label_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceLabelTemplateExample">
    delete from price_label_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceLabelTemplate">
    insert into price_label_template (id, business_id, store_id, 
      org_id, template_name, template_info, 
      template_width, template_height, template_type, 
      remark, status, gmt_create, 
      gmt_update, extend, version, 
      created_by, updated_by, updated_by_name
      )
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{orgId,jdbcType=BIGINT}, #{templateName,jdbcType=VARCHAR}, #{templateInfo,jdbcType=VARCHAR}, 
      #{templateWidth,jdbcType=VARCHAR}, #{templateHeight,jdbcType=VARCHAR}, #{templateType,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceLabelTemplate">
    insert into price_label_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="templateName != null">
        template_name,
      </if>
      <if test="templateInfo != null">
        template_info,
      </if>
      <if test="templateWidth != null">
        template_width,
      </if>
      <if test="templateHeight != null">
        template_height,
      </if>
      <if test="templateType != null">
        template_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateInfo != null">
        #{templateInfo,jdbcType=VARCHAR},
      </if>
      <if test="templateWidth != null">
        #{templateWidth,jdbcType=VARCHAR},
      </if>
      <if test="templateHeight != null">
        #{templateHeight,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        #{templateType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceLabelTemplateExample" resultType="java.lang.Long">
    select count(*) from price_label_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_label_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.templateName != null">
        template_name = #{record.templateName,jdbcType=VARCHAR},
      </if>
      <if test="record.templateInfo != null">
        template_info = #{record.templateInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.templateWidth != null">
        template_width = #{record.templateWidth,jdbcType=VARCHAR},
      </if>
      <if test="record.templateHeight != null">
        template_height = #{record.templateHeight,jdbcType=VARCHAR},
      </if>
      <if test="record.templateType != null">
        template_type = #{record.templateType,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_label_template
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      template_name = #{record.templateName,jdbcType=VARCHAR},
      template_info = #{record.templateInfo,jdbcType=VARCHAR},
      template_width = #{record.templateWidth,jdbcType=VARCHAR},
      template_height = #{record.templateHeight,jdbcType=VARCHAR},
      template_type = #{record.templateType,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceLabelTemplate">
    update price_label_template
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="templateName != null">
        template_name = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="templateInfo != null">
        template_info = #{templateInfo,jdbcType=VARCHAR},
      </if>
      <if test="templateWidth != null">
        template_width = #{templateWidth,jdbcType=VARCHAR},
      </if>
      <if test="templateHeight != null">
        template_height = #{templateHeight,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        template_type = #{templateType,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceLabelTemplate">
    update price_label_template
    set business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      org_id = #{orgId,jdbcType=BIGINT},
      template_name = #{templateName,jdbcType=VARCHAR},
      template_info = #{templateInfo,jdbcType=VARCHAR},
      template_width = #{templateWidth,jdbcType=VARCHAR},
      template_height = #{templateHeight,jdbcType=VARCHAR},
      template_type = #{templateType,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>