<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.AdjustPriceOrderDetailExMapper">
    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="adjust_code" jdbcType="BIGINT" property="adjustCode" />
        <result column="spu_id" jdbcType="BIGINT" property="spuId" />
        <result column="sku_id" jdbcType="BIGINT" property="skuId" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="auth_org_id" jdbcType="BIGINT" property="authOrgId" />
        <result column="auth_org_name" jdbcType="VARCHAR" property="authOrgName" />
        <result column="auth_level" jdbcType="TINYINT" property="authLevel" />
        <result column="price_group_names" jdbcType="VARCHAR" property="priceGroupNames" />
        <result column="org_names" jdbcType="VARCHAR" property="orgNames" />
        <result column="org_ids" jdbcType="VARCHAR" property="orgIds" />
        <result column="org_levels" jdbcType="VARCHAR" property="orgLevels" />
        <result column="price_type_id" jdbcType="BIGINT" property="priceTypeId" />
        <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
        <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="adjust_type" jdbcType="TINYINT" property="adjustType" />
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
        <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit" />
        <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit" />
        <result column="cur_name" jdbcType="VARCHAR" property="curName" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="jhi_specification" jdbcType="VARCHAR" property="jhiSpecification" />
        <result column="dosage" jdbcType="VARCHAR" property="dosage" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
        <result column="price_flag" jdbcType="INTEGER" property="priceFlag" />
        <result column="is_maintain" jdbcType="TINYINT" property="isMaintain" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="label" jdbcType="VARCHAR" property="label" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="prodarea" jdbcType="VARCHAR" property="prodarea" />
        <result column="guide_price" jdbcType="DECIMAL" property="guidePrice" />
        <result column="channel_id" jdbcType="INTEGER" property="channelId" />
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="channel_out_code" jdbcType="VARCHAR" property="channelOutCode" />
        <result column="channel_en_code" jdbcType="VARCHAR" property="channelEnCode" />
        <result column="extend1" jdbcType="VARCHAR" property="extend1" />
        <result column="adjust_detail_id" jdbcType="VARCHAR" property="adjustDetailId" />
        <result column="adjust_price_version" jdbcType="VARCHAR" property="adjustPriceVersion" />
        <result column="medical_price_interception" jdbcType="INTEGER" property="medicalPriceInterception" />
    </resultMap>

    <resultMap id="GoodBaseColumnMap" type="com.cowell.pricecenter.service.dto.response.AdjustBaseDetailDTO">
        <result column="spu_id" jdbcType="BIGINT" property="spuId" />
        <result column="sku_id" jdbcType="BIGINT" property="skuId" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="org_ids" jdbcType="VARCHAR" property="orgIds" />
        <result column="cur_name" jdbcType="VARCHAR" property="curName" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="jhi_specification" jdbcType="VARCHAR" property="jhiSpecification" />
        <result column="dosage" jdbcType="VARCHAR" property="dosage" />
        <result column="prodarea" jdbcType="VARCHAR" property="prodarea" />
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="price_flag" jdbcType="INTEGER" property="priceFlag" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="label" jdbcType="VARCHAR" property="label" />
        <result column="reason" jdbcType="VARCHAR" property="reason" />
        <result column="extend1" jdbcType="VARCHAR" property="extend1" />
    </resultMap>

    <resultMap id="GoodExtend1Map" type="com.cowell.pricecenter.service.dto.response.AdjustOrderDetailGoodsExtend1">
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="extend1" jdbcType="VARCHAR" property="extend1" />
        <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
        <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
    </resultMap>


    <sql id="Base_Column_List">
        id, adjust_code, auth_org_id, auth_org_name, auth_level, org_goods_id, spu_id, sku_id, goods_no,
        price_group_names, org_names, org_ids, org_levels, price_type_id, price_type_code, price_type_name,
        price, adjust_type, original_price, upper_limit, lower_limit, cur_name, manufacturer,
        jhi_specification, dosage, status, gmt_create, gmt_update, extend, version, created_by,
        updated_by, updated_by_name, price_flag, is_maintain,comment, `label`, reason, prodarea, guide_price,
    channel_id, goods_unit, goods_name, channel_out_code, channel_en_code, extend1, adjust_detail_id, adjust_price_version
     </sql>
    <sql id="Good_Price_Type_Base_Column_List">
        id,adjust_code, auth_org_id, auth_org_name, auth_level, spu_id, sku_id, goods_no, org_names, org_ids,org_levels, price_type_id,
        price_type_code, price_type_name,price, adjust_type, original_price, upper_limit, lower_limit, cur_name, manufacturer,
        price_flag, jhi_specification, dosage, status, extend, is_maintain,comment, `label`, reason, prodarea, guide_price,
        goods_unit, goods_name, extend1, gmt_create,medical_price_interception
    </sql>

    <sql id="Good_Price_Type_Base_GroupBy">
        id,adjust_code, auth_org_id, auth_org_name, auth_level, spu_id, sku_id, goods_no, org_ids,price_type_id,
        price_type_code, price_type_name,price, adjust_type, original_price, upper_limit, lower_limit, cur_name, manufacturer,
        price_flag, jhi_specification, dosage, status, extend, is_maintain, reason, prodarea, guide_price,
        goods_unit, goods_name
    </sql>

    <sql id="Good_Base_Column_List">
        spu_id, sku_id, goods_no, org_ids, cur_name, manufacturer, goods_unit, goods_name, jhi_specification, dosage, prodarea, price_flag,comment, `label`, reason, extend1
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <delete id="deleteByAdjustCode">
        delete
        from adjust_price_order_detail
        where adjust_code = #{adjustCode}
        <if test="goodsNoList != null and goodsNoList.size > 0">
          and goods_no in
          <foreach collection="goodsNoList" item="goodsNo" index="index" open="(" close=")" separator=",">
              #{goodsNo}
          </foreach>
        </if>
        <if test="channelIdList != null and channelIdList.size > 0">
            and channel_id in
            <foreach collection="channelIdList" item="channelId" index="index" open="(" close=")" separator=",">
                #{channelId}
            </foreach>
        </if>
        <if test="priceTypeCodeList != null and priceTypeCodeList.size > 0">
            and price_type_code in
            <foreach collection="priceTypeCodeList" item="priceTypeCode" index="index" open="(" close=")" separator=",">
                #{priceTypeCode}
            </foreach>
        </if>
        <if test="detaildList != null and detaildList.size > 0">
            and id in
            <foreach collection="detaildList" item="detailId" index="index" open="(" close=")" separator=",">
                #{detailId}
            </foreach>
        </if>
        <if test="skuIdList != null and skuIdList.size > 0">
            and sku_id in
            <foreach collection="skuIdList" item="skuId" index="index" open="(" close=")" separator=",">
                #{skuId}
            </foreach>
        </if>
    </delete>
    <delete id="deleteByAdjustCodeAndGoodsNoAndPriceType">
        delete
        from  adjust_price_order_detail
        where adjust_code = #{adjustCode} and price_type_code = #{priceTypeCode} and goods_no = #{goodsNo}
    </delete>

    <select id="selectAdjustPriceOrderDetailListPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from adjust_price_order_detail
        where status = 0 and adjust_code = #{adjustCode}

        LIMIT #{page},#{size}
    </select>

    <select id="selectAdjustPriceOrderDetailListNoticePage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from adjust_price_order_detail
        <include refid="selectAdjustOrderDetailV2ListNoticeWhere"></include>
        LIMIT #{page},#{size}
    </select>

    <select id="selectAdjustPriceOrderDetailCountNoticePage" resultType="java.lang.Long">
        select
        count(*)
        from adjust_price_order_detail
        <include refid="selectAdjustOrderDetailV2ListNoticeWhere"></include>
    </select>

    <sql id="selectAdjustOrderDetailV2ListNoticeWhere">
        where status = 0 and JSON_EXTRACT(extend1, '$.detailAuditStatus') = 1
        and price_type_code not in('GJYJS')
        <if test="adjustCodeList != null and adjustCodeList.size > 0">
            and adjust_code in
            <foreach collection="adjustCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="adjustDetailIdList != null and adjustDetailIdList.size > 0">
            and id in
            <foreach collection="adjustDetailIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="selectSingleGoodPriceTypeOrderDetailV2ListWhere">
        where status = 0 and adjust_code = #{adjustCode}
        <if test="goodsNo != null and goodsNo !=''">
            and goods_no = #{goodsNo}
        </if>
        <if test="channelIdList != null and channelIdList.size > 0">
            and channel_id in
            <foreach collection="channelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="priceTypeCodeList != null and priceTypeCodeList.size > 0">
            and price_type_code in
            <foreach collection="priceTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="detaildList != null and detaildList.size > 0">
            and id in
            <foreach collection="detaildList" item="detailId" index="index" open="(" close=")" separator=",">
                #{detailId}
            </foreach>
        </if>
    </sql>
    <select id="selectOrderDetailV2ListGroupByGoodsAndPriceType" resultMap="BaseResultMap">
        select
        <include refid="Good_Price_Type_Base_Column_List"></include>
        from adjust_price_order_detail
        where
        <foreach collection="goodsNoList" item="item" open="( " separator=") or (" close=" )">
            adjust_code = #{adjustCode}
            and goods_no = #{item.goodsNo}
            <if test="item.skuId != null">
            	and sku_id = #{item.skuId}
            </if>
            <if test="item.orgIds!=null and item.orgIds!=''">
                and org_ids = #{item.orgIds}
            </if>
        </foreach>
        group by <include refid="Good_Price_Type_Base_GroupBy"></include>
        order by medical_price_interception desc,gmt_create desc
    </select>

    <select id="selectOrderDetailV2ListPageGroupByGoodAndPriceType" resultMap="BaseResultMap">
        select
        <include refid="Good_Price_Type_Base_Column_List"></include>
        from adjust_price_order_detail
        <include refid="selectSingleGoodPriceTypeOrderDetailV2ListWhere"></include>
        group by <include refid="Good_Price_Type_Base_GroupBy"></include>
        order by gmt_create desc
        <if test="toPage">
            LIMIT #{offset}, #{size}
        </if>
    </select>

    <select id="selectOrderDetailV2ListPageGroupByGoodAndPriceTypeCount" resultType="java.lang.Long">
        select
            count(distinct goods_no,sku_id,ifnull(org_ids,''))
        from adjust_price_order_detail
        <include refid="selectSingleGoodPriceTypeOrderDetailV2ListWhere"></include>
    </select>

    <select id="selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType"  resultType="com.cowell.pricecenter.service.dto.AdjustPriceOrderDetailSku">
        select
            goods_no as goodsNo,sku_id as skuId,ifnull(org_ids,'') as orgIds
        from adjust_price_order_detail
        <include refid="selectSingleGoodPriceTypeOrderDetailV2ListWhere"></include>
        group by goods_no,org_ids,sku_id
        order by medical_price_interception desc,gmt_create desc
        <if test="toPage">
            LIMIT #{offset}, #{size}
        </if>
    </select>

    <select id="getGoodsNoListbyAdjustCode" resultType="java.lang.String">
        select
            goods_no
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0
        group by goods_no
    </select>

    <select id="countAdjustPriceOrderDetailSupplementNotCompleted" resultType="java.lang.Integer">
        select
            count(*)
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataCompletion") = 0
    </select>
    <select id="countAdjustPriceOrderDetailSupplementNotFull" resultType="java.lang.Integer">
        select
            count(*)
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataFull") = 0
    </select>
    <select id="getGoodsNoListForDetailDataNotFull" resultType="java.lang.String">
        select
            goods_no
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataFull") = 0
        group by goods_no
    </select>

    <select id="countOfNeedSpecialAuditOrderDetail" resultType="java.lang.Integer">
        select
            count(*)
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.needSpecialAudit") = 1
    </select>
    <select id="getToCheckOrderDetailListForDetailDataNotFull" resultMap="BaseResultMap">
        select adjust_code, goods_no, price_type_code, price_type_name
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataFull") = 0
        group by adjust_code, goods_no, price_type_code, price_type_name
    </select>

    <select id="countOfGoodsByAdjustCode" resultType="java.lang.Integer">
        select
            count(distinct goods_no,org_ids,sku_id)
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0
    </select>

    <select id="listAdjustPriceOrderBaseDetailsByCode" resultMap="GoodBaseColumnMap">
        select
        <include refid="Good_Base_Column_List"></include>
        from adjust_price_order_detail
        where adjust_code = #{adjustCode}
        <if test="goodsNo != null">
            and goods_no = #{goodsNo}
        </if>
        <if test="skuId != null">
            and sku_id = #{skuId}
        </if>
        and status = 0 and goods_name is not null
        group by <include refid="Good_Base_Column_List"></include>
    </select>

    <update id="updateByAdjustCode">
        update
            adjust_price_order_detail
        <set>
            <if test="price != null">
                price = #{price},
            </if>
            <if test="extend1Str != null">
                extend1 = #{extend1Str},
            </if>
            <if test="spuId != null">
                spu_id = #{spuId},
            </if>
            <if test="skuId != null">
                sku_id = #{skuId},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice},
            </if>
            <if test="guidePrice != null and guidePrice gte 0 ">
                guide_price = #{guidePrice},
            </if>
            <if test="guidePrice != null and guidePrice lt 0 ">
                guide_price = null,
            </if>
            <if test="upperLimit != null and upperLimit gte 0 ">
                upper_limit = #{upperLimit},
            </if>
            <if test="upperLimit != null and upperLimit lt 0 ">
                upper_limit = null,
            </if>
            <if test="lowerLimit != null and lowerLimit gte 0 ">
                lower_limit = #{lowerLimit},
            </if>
            <if test="lowerLimit != null and lowerLimit lt 0 ">
                lower_limit = null,
            </if>
            <if test="reason != null">
                reason = #{reason},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedByName != null">
                updated_by_name = #{updatedByName},
            </if>
            <if test="activityType != null">
                label = #{activityType},
            </if>
            <if test="goodsType != null">
                comment = #{goodsType},
            </if>
            <if test="manufacturer != null">
                manufacturer = #{manufacturer},
            </if>
            <if test="jhiSpecification != null">
                jhi_specification = #{jhiSpecification},
            </if>
            <if test="dosage != null">
                dosage = #{dosage},
            </if>
            <if test="prodarea != null">
                prodarea = #{prodarea},
            </if>
            <if test="goodsUnit != null">
                goods_unit = #{goodsUnit},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName},
            </if>
            <if test="curName != null">
                cur_name = #{curName},
            </if>
            <if test="comment != null">
                comment = #{comment},
            </if>
            <if test="label != null">
                label = #{label},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="extend1Str != null">
                extend1 = #{extend1Str},
            </if>
            <if test="priceFlag != null">
                price_flag = #{priceFlag},
            </if>
            <if test="medicalPriceInterception !=null ">
                medical_price_interception = #{medicalPriceInterception},
            </if>
            version = version + 1
        </set>
        where
            adjust_code = #{adjustCode}
            and id in
            <foreach collection="adjustDetailMergeIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            <if test="goodsNo != null">
                and goods_no = #{goodsNo}
            </if>
            <if test="priceTypeCode != null">
                and price_type_code = #{priceTypeCode}
            </if>
            <if test="channelId != null">
                and channel_id = #{channelId}
            </if>
    </update>



    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderDetail">
        insert into adjust_price_order_detail ( adjust_code, auth_org_id,
            auth_org_name, auth_level, org_goods_id,
            spu_id, sku_id, goods_no, price_group_names,
            org_names, org_ids, org_levels, price_type_id,
            price_type_code, price_type_name, price,
            adjust_type, original_price, upper_limit,
            lower_limit, cur_name, manufacturer,
            jhi_specification, dosage, status,
            extend,version, created_by, updated_by,
            updated_by_name,is_maintain,comment,
            `label`,reason, prodarea, guide_price,
            channel_id, goods_unit, goods_name,
            channel_out_code, channel_en_code, extend1, adjust_detail_id, adjust_price_version,
            price_flag
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.adjustCode,jdbcType=VARCHAR}, #{item.authOrgId,jdbcType=BIGINT},
                #{item.authOrgName,jdbcType=VARCHAR}, #{item.authLevel,jdbcType=TINYINT}, #{item.orgGoodsId,jdbcType=BIGINT},
                #{item.spuId,jdbcType=BIGINT}, #{item.skuId,jdbcType=BIGINT}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.priceGroupNames,jdbcType=VARCHAR},
                #{item.orgNames,jdbcType=VARCHAR}, #{item.orgIds,jdbcType=VARCHAR}, #{item.orgLevels,jdbcType=VARCHAR},#{item.priceTypeId,jdbcType=BIGINT},
                #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL},
                #{item.adjustType,jdbcType=TINYINT}, #{item.originalPrice,jdbcType=DECIMAL}, #{item.upperLimit,jdbcType=DECIMAL},
                #{item.lowerLimit,jdbcType=DECIMAL}, #{item.curName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
                #{item.jhiSpecification,jdbcType=VARCHAR}, #{item.dosage,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
                #{item.extend,jdbcType=VARCHAR},
                #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT}, #{item.updatedBy,jdbcType=BIGINT},
                #{item.updatedByName,jdbcType=VARCHAR}, #{item.isMaintain,jdbcType=TINYINT},#{item.comment,jdbcType=VARCHAR},
                #{item.label,jdbcType=VARCHAR},#{item.reason,jdbcType=VARCHAR},
                #{item.prodarea,jdbcType=VARCHAR},#{item.guidePrice,jdbcType=DECIMAL},
                #{item.channelId,jdbcType=INTEGER},#{item.goodsUnit,jdbcType=VARCHAR},
                #{item.goodsName,jdbcType=VARCHAR},#{item.channelOutCode,jdbcType=VARCHAR},
                #{item.channelEnCode,jdbcType=VARCHAR},#{item.extend1,jdbcType=VARCHAR},
                #{item.adjustDetailId,jdbcType=VARCHAR},#{item.adjustPriceVersion,jdbcType=VARCHAR},
                #{item.priceFlag,jdbcType=INTEGER}
            </trim>
        </foreach>
    </insert>

    <select id="listOrderDetailByGoodsNoOrgIds" resultMap="BaseResultMap">
        select id,adjust_code, goods_no, sku_id, org_ids, org_levels, org_names, extend1, channel_id
        from adjust_price_order_detail
        where adjust_code = #{adjustCode}
        <if test="goodsNoList != null and goodsNoList.size>0">
            and goods_no in
            <foreach collection="goodsNoList" index="index" item="goodsNo" open="(" separator="," close=")">
                #{goodsNo}
            </foreach>
        </if>
        <if test="orgIdList !=null and orgIdList.size>0">
            and org_ids in
            <foreach collection="orgIdList" index="index" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="excludeDetailIdList != null and excludeDetailIdList.size>0">
            and id not in
            <foreach collection="excludeDetailIdList" index="index" item="detailId" open="(" separator="," close=")">
                #{detailId}
            </foreach>
        </if>
        group by adjust_code, goods_no, org_ids, channel_id
    </select>

    <select id="listAdjustPriceOrderDetailPageByCode" resultMap="BaseResultMap">
        select id, adjust_code, goods_no,sku_id, org_ids, org_names, org_levels, price, price_flag, comment, label, extend1
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.detailAuditStatus") = 1
        group by adjust_code, goods_no, sku_id, org_ids order by goods_no, org_ids  limit #{page},#{size}
    </select>

    <select id="selectOrderDetailByGoodsNoOrgList" resultMap="BaseResultMap">
        select id,adjust_code, goods_no, channel_id, price, price_type_code, sku_id, extend1
        from adjust_price_order_detail
        where adjust_code = #{adjustCode}
        <if test="goodsNo != null">
            and goods_no = #{goodsNo}
        </if>
        <if test="skuId != null">
            and sku_id = #{skuId}
        </if>
        <if test="orgIdList !=null and orgIdList.size>0">
            and org_ids in
            <foreach collection="orgIdList" index="index" item="orgId" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
    </select>
    <select id="countAdjustPriceOrderDetailReasonIsNull" resultType="java.lang.Integer">
        select
            count(*)
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and (reason is null or reason='')
    </select>

    <update id="updateAdjustPriceOrderDetailAuditStatus">
        update adjust_price_order_detail
        set extend1=JSON_SET(extend1, "$.detailAuditStatus",#{auditStatusNew}) ,
        extend1=JSON_SET(extend1, "$.operatorUserName",#{operatorUserName})
        <if test="init">
            ,extend1=JSON_SET(extend1, "$.detailAuditMessage",'')
        </if>

        where adjust_code=#{adjustCode}
        <if test="auditStatusOld != null">
            and JSON_EXTRACT(extend1, "$.detailAuditStatus") = #{auditStatusOld}
        </if>
    </update>

    <select id="selectAdjustPriceOrderDetailSupplementNotCompleted" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataCompletion") = 0
    </select>

    <select id="listGoodsExtend1ByAdjustCode" resultMap="GoodExtend1Map">
        select goods_no, price_type_code, price_type_name, extend1 from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0
    </select>

    <update id="updateByExampleSelective" parameterType="map">
        update adjust_price_order_detail
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.adjustCode != null">
                adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
            </if>
            <if test="record.authOrgId != null">
                auth_org_id = #{record.authOrgId,jdbcType=BIGINT},
            </if>
            <if test="record.authOrgName != null">
                auth_org_name = #{record.authOrgName,jdbcType=VARCHAR},
            </if>
            <if test="record.authLevel != null">
                auth_level = #{record.authLevel,jdbcType=TINYINT},
            </if>
            <if test="record.orgGoodsId != null">
                org_goods_id = #{record.orgGoodsId,jdbcType=BIGINT},
            </if>
            <if test="record.spuId != null">
                spu_id = #{record.spuId,jdbcType=BIGINT},
            </if>
            <if test="record.skuId != null">
                sku_id = #{record.skuId,jdbcType=BIGINT},
            </if>
            <if test="record.goodsNo != null">
                goods_no = #{record.goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="record.priceGroupNames != null">
                price_group_names = #{record.priceGroupNames,jdbcType=VARCHAR},
            </if>
            <if test="record.orgNames != null">
                org_names = #{record.orgNames,jdbcType=VARCHAR},
            </if>
            <if test="record.orgIds != null">
                org_ids = #{record.orgIds,jdbcType=VARCHAR},
            </if>
            <if test="record.orgLevels != null">
                org_levels = #{record.orgLevels,jdbcType=VARCHAR},
            </if>
            <if test="record.priceTypeId != null">
                price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
            </if>
            <if test="record.priceTypeCode != null">
                price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="record.priceTypeName != null">
                price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
            </if>
            <if test="record.price != null">
                price = #{record.price,jdbcType=DECIMAL},
            </if>
            <if test="record.adjustType != null">
                adjust_type = #{record.adjustType,jdbcType=TINYINT},
            </if>
            <if test="record.originalPrice != null">
                original_price = #{record.originalPrice,jdbcType=DECIMAL},
            </if>
            <if test="record.upperLimit != null and record.upperLimit gte 0 ">
                upper_limit = #{record.upperLimit,jdbcType=DECIMAL},
            </if>
            <if test="record.upperLimit != null and record.upperLimit lt 0 ">
                upper_limit = null,
            </if>

            <if test="record.lowerLimit != null and record.lowerLimit gte 0 ">
                lower_limit = #{record.lowerLimit,jdbcType=DECIMAL},
            </if>
            <if test="record.lowerLimit != null and record.lowerLimit lt 0 ">
                lower_limit = null,
            </if>

            <if test="record.curName != null">
                cur_name = #{record.curName,jdbcType=VARCHAR},
            </if>
            <if test="record.manufacturer != null">
                manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
            </if>
            <if test="record.jhiSpecification != null">
                jhi_specification = #{record.jhiSpecification,jdbcType=VARCHAR},
            </if>
            <if test="record.dosage != null">
                dosage = #{record.dosage,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=TINYINT},
            </if>
            <if test="record.gmtCreate != null">
                gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.gmtUpdate != null">
                gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
            </if>
            <if test="record.extend != null">
                extend = #{record.extend,jdbcType=VARCHAR},
            </if>
            <if test="record.version != null">
                version = #{record.version,jdbcType=INTEGER},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy,jdbcType=BIGINT},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy,jdbcType=BIGINT},
            </if>
            <if test="record.updatedByName != null">
                updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
            </if>
            <if test="record.priceFlag != null">
                price_flag = #{record.priceFlag,jdbcType=INTEGER},
            </if>
            <if test="record.isMaintain != null">
                is_maintain = #{record.isMaintain,jdbcType=TINYINT},
            </if>
            <if test="record.comment != null">
                comment = #{record.comment,jdbcType=VARCHAR},
            </if>
            <if test="record.label != null">
                `label` = #{record.label,jdbcType=VARCHAR},
            </if>
            <if test="record.reason != null">
                reason = #{record.reason,jdbcType=VARCHAR},
            </if>
            <if test="record.prodarea != null">
                prodarea = #{record.prodarea,jdbcType=VARCHAR},
            </if>
            <if test="record.guidePrice != null and record.guidePrice gte 0 ">
                guide_price = #{record.guidePrice,jdbcType=DECIMAL},
            </if>
            <if test="record.guidePrice != null and record.guidePrice lt 0 ">
                guide_price = null,
            </if>
            <if test="record.channelId != null">
                channel_id = #{record.channelId,jdbcType=INTEGER},
            </if>
            <if test="record.goodsUnit != null">
                goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
            </if>
            <if test="record.goodsName != null">
                goods_name = #{record.goodsName,jdbcType=VARCHAR},
            </if>
            <if test="record.channelOutCode != null">
                channel_out_code = #{record.channelOutCode,jdbcType=VARCHAR},
            </if>
            <if test="record.channelEnCode != null">
                channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
            </if>
            <if test="record.extend1 != null">
                extend1 = #{record.extend1,jdbcType=VARCHAR},
            </if>
            <if test="record.adjustDetailId != null">
                adjust_detail_id = #{record.adjustDetailId,jdbcType=VARCHAR},
            </if>
            <if test="record.adjustPriceVersion != null">
                adjust_price_version = #{record.adjustPriceVersion,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>

    <update id="updateAdjustPriceOrderDetailLimitStatus">
        update adjust_price_order_detail
        set extend1=JSON_SET(extend1, "$.limitStatus",#{limitStatusNew}),
        extend1=JSON_SET(extend1, "$.dataFull",#{dataFullNew})
        where adjust_code=#{adjustCode} and
        (JSON_EXTRACT(extend1, "$.limitStatus") != #{limitStatusNew} or JSON_EXTRACT(extend1, "$.dataFull") != #{dataFullNew})
    </update>

    <select id="selectAdjustPriceOrderDetailIdById" resultType="com.cowell.pricecenter.service.dto.response.AdjustPriceOrderDetailIdDTO">
        select id,adjust_code as adjustCode,org_ids as orgIds,org_levels as orgLevels,extend1,reason,goods_name as goodsName,manufacturer,jhi_specification as specifications,goods_unit as unit from adjust_price_order_detail
        where adjust_code=#{adjustCode}
        and id in
        <foreach collection="idList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <delete id="deleteBatch">
        delete from adjust_price_order_detail
        where adjust_code = #{adjustCode}
        limit #{limit}
    </delete>

    <select id="getAdjustPriceOrderDetailPrice" resultType="com.cowell.pricecenter.service.dto.AdjustPriceOrderDetailPrice">
    	select id,adjust_code as adjustCode,org_ids as orgIds,org_levels as orgLevels, goods_no as goodsNo,price,extend1 from adjust_price_order_detail
    	where adjust_code=#{adjustCode} and price_type_code=#{priceTypeCode} and status = 0
    </select>

    <update id="batchUpdateExtend1">
        update adjust_price_order_detail
        set extend1 =
        <foreach collection="detailList" item="item" open="case " close=" end">
            when  adjust_code = #{item.adjustCode} and id = #{item.id}  then #{item.extend1}
        </foreach>
        <where>
            adjust_code = #{code} and id in
            <foreach collection="detailList" index="index" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="selectDetailByB2cGoods" resultMap="BaseResultMap">
        select
        <include refid="Good_Price_Type_Base_Column_List"></include>
        from adjust_price_order_detail
        where
        <foreach collection="itemList" item="item" open="( " separator=") or (" close=" )">
            adjust_code = #{adjustCode}
            and goods_no = #{item.goodsNo}
            and sku_id = #{item.itemSkuId}
        </foreach>
    </select>

	<delete id="deleteDetailByB2cGoods">
        delete from adjust_price_order_detail
        where
        <foreach collection="detailList" item="item" open="( " separator=") or (" close=" )">
            adjust_code = #{adjustCode}
            and goods_no = #{item.goodsNo}
            and sku_id = #{item.skuId}
        </foreach>
    </delete>

    <select id="getSkuIdListbyAdjustCode" resultType="java.lang.String">
        select
            sku_id
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0
        group by sku_id
    </select>

    <select id="grossPureMarginLessThanZero" resultType="java.lang.String">
    	<![CDATA[
            select goods_no from adjust_price_order_detail where adjust_code = #{adjustCode}
			and JSON_EXTRACT(extend1, "$.grossPureMargin") < 0 group by goods_no
        ]]>
    </select>

    <select id="getLSJLessThanHYJbyAdjustCode" resultType="java.lang.String">
    	<![CDATA[
            select goods_no
            from adjust_price_order_detail where adjust_code = #{adjustCode}
            and price_type_code = 'HYJ' and status=0
            and JSON_EXTRACT(extend1, '$.rebate') >1 group by goods_no
        ]]>
    </select>

    <select id="countAdjustPriceOrderDetailPriceIsNull" resultType="java.lang.Integer">
        select
        count(*)
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and price is null
    </select>

    <select id="selectAdjustOrderDetailExtend1" resultType="com.cowell.pricecenter.service.dto.AdjustOrgIdLevelDTO">
        select adjust_code as adjustCode,extend1 from adjust_price_order_detail where adjust_code=#{adjustCode}
    </select>

    <select id="selectAdjustOrderDetailOrgIdLevel" resultType="com.cowell.pricecenter.service.dto.AdjustOrgIdLevelDTO">
		select adjust_code as adjustCode,org_ids as orgIds,org_levels as orgLevels from adjust_price_order_detail
        where adjust_code=#{adjustCode} group by org_ids,org_levels
    </select>
    <select id="selectPriceFloorRemindGoodsNo" resultType="java.lang.String">
    	<![CDATA[
            select goods_no
            from adjust_price_order_detail where adjust_code = #{adjustCode}
            and price_type_code = 'LSJ' and status=0
            and JSON_EXTRACT(extend1, '$.priceFloorRemind') = 1 group by goods_no
        ]]>
    </select>

    <select id="goodsOfNeedSpecialAuditOrderDetail" resultType="java.lang.String">
        select
            goods_no
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and JSON_EXTRACT(extend1, "$.needSpecialAudit") = 1
        group by goods_no
    </select>
    <select id="selectAdjustPriceOrderDetailIdByAdjustCode" resultType="java.lang.Long">
        select
        id
        from adjust_price_order_detail
        where adjust_code = #{adjustCode}
        <if test="channelIdList != null and channelIdList.size>0">
            and channel_id in
            <foreach collection="channelIdList" index="index" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
        </if>
        <if test="priceTypeCodes != null and priceTypeCodes.size>0">
            and price_type_code in
            <foreach collection="priceTypeCodes" index="index" item="priceTypeCode" open="(" separator="," close=")">
                #{priceTypeCode}
            </foreach>
        </if>
    </select>

    <update id="batchUpdateExtend1InsurancePriceControl">
        update adjust_price_order_detail
        set medical_price_interception =
        <foreach collection="medPriceLimitMap" item="value" index="key" open="case " close=" end">
            when  adjust_code = #{adjustCode} and id = #{key,jdbcType=BIGINT}  then #{value,jdbcType=INTEGER}
        </foreach>
        WHERE adjust_code = #{adjustCode}
        AND id IN
        <foreach collection="medPriceLimitMap" item="value" index="key" open="(" separator="," close=")">
            #{key,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="countMedicalPriceInterception" resultType="java.lang.Integer">
        select
        count(*)
        from adjust_price_order_detail
        where adjust_code = #{adjustCode} and status = 0 and medical_price_interception=1
    </select>

    <select id="selectPricesForHd" resultType="java.lang.String">
        <![CDATA[
            select goods_no
            from (
            select goods_no, count(1) as cnt
            from adjust_price_order_detail
            where adjust_code = #{adjustCode} and price < 0 and status = 0
            group by adjust_code, goods_no, org_ids
            ) t
            where t.cnt = 3
        ]]>
    </select>

</mapper>
