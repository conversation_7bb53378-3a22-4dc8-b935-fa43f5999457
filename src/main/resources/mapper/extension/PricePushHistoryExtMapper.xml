<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PricePushHistoryExtMapper">
    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PricePushHistory">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="business_id" jdbcType="BIGINT" property="businessId" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
        <result column="push_code" jdbcType="VARCHAR" property="pushCode" />
        <result column="batch" jdbcType="INTEGER" property="batch" />
        <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
        <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
        <result column="channel_id" jdbcType="INTEGER" property="channelId" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="price_flag" jdbcType="INTEGER" property="priceFlag" />
        <result column="label" jdbcType="VARCHAR" property="label" />
        <result column="comment" jdbcType="VARCHAR" property="comment" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
        <result column="send_msg" jdbcType="VARCHAR" property="sendMsg" />
        <result column="receive_status" jdbcType="INTEGER" property="receiveStatus" />
        <result column="receive_msg" jdbcType="VARCHAR" property="receiveMsg" />
        <result column="effect_status" jdbcType="INTEGER" property="effectStatus" />
        <result column="effect_msg" jdbcType="VARCHAR" property="effectMsg" />
        <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    </resultMap>

    <sql id="Base_Column_List">
	    <!--@mbg.generated-->
	    id, business_id, store_id, goods_no, adjust_code, push_code, batch, price_type_code,
	    price_type_name, channel_id, price, price_flag, `label`, `comment`, `status`, gmt_create,
	    gmt_update, extend, version, created_by, updated_by, send_status, send_msg, receive_status,
	    receive_msg, effect_status, effect_msg, sku_id
	</sql>

    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.PricePushHistory">
        insert into price_push_history (business_id, store_id, goods_no,
        adjust_code, push_code, batch,
        price_type_code, price_type_name, channel_id,
        price, price_flag, `label`,
        `comment`, `status`, gmt_create,
        gmt_update, extend, version,
        created_by, updated_by, sku_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.businessId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.goodsNo,jdbcType=VARCHAR},
                #{item.adjustCode,jdbcType=VARCHAR}, #{item.pushCode,jdbcType=VARCHAR}, #{item.batch,jdbcType=INTEGER},
                #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER},
                #{item.price,jdbcType=DECIMAL}, #{item.priceFlag,jdbcType=INTEGER}, #{item.label,jdbcType=VARCHAR},
                #{item.comment,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=TIMESTAMP},
                #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},
                #{item.createdBy,jdbcType=BIGINT}, #{item.updatedBy,jdbcType=BIGINT}, #{item.skuId,jdbcType=BIGINT}
            </trim>
        </foreach>
    </insert>

    <update id="updateBatchByPushCodeAndBatchLimit">
        update price_push_history
        set batch = #{batch,jdbcType=INTEGER},
            version = version+1
        where push_code = #{pushCode} and batch = #{selectBatch} limit #{limit}
    </update>
    <select id="selectPricePushHistoryPage" parameterType="com.cowell.pricecenter.param.PricePushHistoryParam" resultMap="BaseResultMap">
        select
	    <include refid="Base_Column_List" />
	    from price_push_history
	    where adjust_code = #{adjustCode} and effect_status = #{effectStatus} and status = 0
	    <if test="storeId != null">
	      and store_id = #{storeId}
	    </if>
	    <if test="goodsNo != null and goodsNo!=''">
	       and goods_no = #{goodsNo}
	    </if>
	    <if test="channelId !=null">
	       and channel_id = #{channelId}
	    </if>
        <if test="businessId !=null">
            and business_id = #{businessId}
        </if>
	    <if test="priceTypeCode !=null and priceTypeCode!=''">
	       and price_type_code = #{priceTypeCode}
	    </if>
	    <if test="priceTypeCodeList != null and priceTypeCodeList.size > 0">
            and price_type_code in
            <foreach collection="priceTypeCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
	    order by goods_no
    </select>

    <select id="selectPricePushHistoryStatistics" parameterType="com.cowell.pricecenter.param.PricePushHistoryParam" resultType="com.cowell.pricecenter.service.dto.PricePushHistoryStatisticsDTO">
        select
		count(1) as total,
		sum(case pph.effect_status when -1 then 1 else 0 end) as progressCount,
		sum(case pph.effect_status when 1 then 1 else 0 end) as successCount,
		sum(case pph.effect_status when 2 then 1 else 0 end) as failCount
		from price_push_history pph where pph.adjust_code = #{adjustCode} and status = 0
		<if test="businessId != null">
          and pph.business_id = #{businessId}
        </if>
		<if test="storeId != null">
          and pph.store_id = #{storeId}
        </if>
        <if test="goodsNo != null and goodsNo!=''">
           and pph.goods_no = #{goodsNo}
        </if>
        <if test="channelId !=null">
           and pph.channel_id = #{channelId}
        </if>
        <if test="priceTypeCode !=null and priceTypeCode!=''">
           and pph.price_type_code = #{priceTypeCode}
        </if>
        <if test="priceTypeCodeList != null and priceTypeCodeList.size > 0">
            and pph.price_type_code in
            <foreach collection="priceTypeCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectPricePushHistoryEffectError" resultType="integer">
        select count(1) from price_push_history
	    where adjust_code = #{adjustCode} and effect_status = 2 and effect_msg = '商品编码不存在' and status = 0
    </select>

    <select id="selectPricePushHistoryEffectErrorData" resultMap="BaseResultMap">
    	select push_code,business_id from price_push_history
		where adjust_code = #{adjustCode} and effect_status = 2 and effect_msg = '商品编码不存在' and status = 0
		group by push_code,business_id
    </select>

</mapper>
