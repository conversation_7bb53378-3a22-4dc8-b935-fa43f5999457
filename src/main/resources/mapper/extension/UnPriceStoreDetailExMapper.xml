<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.UnPriceStoreDetailExMapper">
    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.UnPriceStoreDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="store_id" jdbcType="BIGINT" property="storeId"/>
        <result column="store_name" jdbcType="VARCHAR" property="storeName"/>
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo"/>
        <result column="business_id" jdbcType="BIGINT" property="businessId"/>
        <result column="business_name" jdbcType="VARCHAR" property="businessName"/>
        <result column="platform_id" jdbcType="BIGINT" property="platformId"/>
        <result column="platform_name" jdbcType="VARCHAR" property="platformName"/>
        <result column="cur_name" jdbcType="VARCHAR" property="curName"/>
        <result column="bar_code" jdbcType="VARCHAR" property="barCode"/>
        <result column="op_code" jdbcType="VARCHAR" property="opCode"/>
        <result column="jhi_specification" jdbcType="VARCHAR" property="jhiSpecification"/>
        <result column="dosage" jdbcType="VARCHAR" property="dosage"/>
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate"/>
        <result column="extend" jdbcType="VARCHAR" property="extend"/>
        <result column="version" jdbcType="INTEGER" property="version"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName"/>
    </resultMap>
    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.UnPriceStoreDetail">
        insert into un_price_store_detail ( store_id, store_name, goods_no,
        business_id, business_name, cur_name,
        bar_code, op_code, jhi_specification,
        dosage, manufacturer, extend)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.storeId,jdbcType=BIGINT}, #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR},
                #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR},#{item.curName,jdbcType=VARCHAR},
                #{item.barCode,jdbcType=VARCHAR}, #{item.opCode,jdbcType=VARCHAR}, #{item.jhiSpecification,jdbcType=VARCHAR},
                #{item.dosage,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.extend,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

    <select id="queryDistinctBusinessId"  parameterType="com.cowell.pricecenter.entity.UnPriceStoreDetail"
            resultMap="BaseResultMap">
        select business_id from un_price_store_detail group by business_id
    </select>
</mapper>

