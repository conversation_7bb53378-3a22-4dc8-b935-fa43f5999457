<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceWarningRecordExtMapper">
    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceWarningRecord" >
        <id column="id" property="id" jdbcType="BIGINT" />
        <result column="business_id" property="businessId" jdbcType="BIGINT" />
        <result column="store_id" property="storeId" jdbcType="BIGINT" />
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR" />
        <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
        <result column="price_type" property="priceType" jdbcType="INTEGER" />
        <result column="src_price" property="srcPrice" jdbcType="BIGINT" />
        <result column="new_price" property="newPrice" jdbcType="BIGINT" />
        <result column="price_source" property="priceSource" jdbcType="TINYINT" />
        <result column="price_warn_type" property="priceWarnType" jdbcType="TINYINT" />
        <result column="last_update_time" property="lastUpdateTime" jdbcType="TIMESTAMP" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="gmt_update" property="gmtUpdate" jdbcType="TIMESTAMP" />
        <result column="status" property="status" jdbcType="TINYINT" />
        <result column="version" property="version" jdbcType="INTEGER" />
        <result column="extend" property="extend" jdbcType="VARCHAR" />
        <result column="channel_id" jdbcType="INTEGER" property="channelId" />
        <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
        <result column="price_warn_version" jdbcType="VARCHAR" property="priceWarnVersion" />
        
    </resultMap>

    <resultMap id="PushMsgDTOResultMap" type="com.cowell.pricecenter.service.dto.PushMsgDTO" >
        <result column="business_id" property="businessId" jdbcType="BIGINT" />
        <result column="store_id" property="storeId" jdbcType="BIGINT" />
        <result column="store_name" property="storeName" jdbcType="VARCHAR" />
        <result column="price_warn_type" property="priceWarnType" jdbcType="TINYINT" />
        <result column="business_name" property="businessName" jdbcType="VARCHAR" />
        <result column="warn_type" jdbcType="TINYINT" property="warnType" />
    </resultMap>

    <sql id="Base_Column_List">
        id, business_id, store_id, store_name, goods_no, sku_name, price_type, src_price,
    new_price, price_source, price_warn_type, last_update_time, created_by, gmt_create,
    updated_by, gmt_update, status, version, extend, channel_id, price_type_code, price_warn_version
     </sql>

    <!--批量保存-->
    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.PriceWarningRecord">
        insert into price_warning_record (business_id, store_id,store_name, goods_no, sku_name,
        price_type, src_price, new_price,price_source, price_warn_type, last_update_time,
        created_by, gmt_create, updated_by,gmt_update, status, version,extend, warn_type,
        channel_id, price_type_code, price_warn_version)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.businessId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
                #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR},
                #{item.priceType,jdbcType=INTEGER}, #{item.srcPrice,jdbcType=BIGINT}, #{item.newPrice,jdbcType=BIGINT},
                #{item.priceSource,jdbcType=TINYINT}, #{item.priceWarnType,jdbcType=TINYINT}, #{item.lastUpdateTime,jdbcType=TIMESTAMP},
                #{item.createdBy,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=VARCHAR},
                #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.status,jdbcType=TINYINT}, #{item.version,jdbcType=INTEGER},
                #{item.extend,jdbcType=VARCHAR}, #{item.warnType,jdbcType=TINYINT},#{item.channelId,jdbcType=INTEGER},
                #{item.priceTypeCode,jdbcType=VARCHAR},#{item.priceWarnVersion,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

    <update id="updateStatus" parameterType="map">
        UPDATE price_warning_record
        SET status = #{newStatus},updated_by = #{userId}
        WHERE business_id = #{record.businessId}
        AND store_id = #{record.storeId}
        AND status = #{record.status}
        AND price_type = #{record.priceType}
        AND goods_no IN
        <foreach collection="goodsNoList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectByPage" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List" />
        FROM price_warning_record
        WHERE 1 = 1
        <if test="null != storeId and storeId > 0">
            AND store_id = #{storeId}
        </if>
        AND business_id = #{businessId}
        AND status = 0
        <if test="null != keyWord and '' != keyWord">
            AND (goods_no = #{keyWord} or sku_name like concat('',#{keyWord},'%'))
        </if>
        <if test="null != startTime and null != endTime">
            AND gmt_create BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="null != priceWarnType and priceWarnType != -1">
            AND price_warn_type = #{priceWarnType}
        </if>
        ORDER BY store_id,gmt_create desc
        limit #{current},#{pageSize}
    </select>

    <select id="count" resultType="java.lang.Integer" parameterType="java.util.Map">
        SELECT  COUNT(1)
        FROM price_warning_record
        WHERE 1 = 1
        <if test="null != storeId and storeId > 0">
            AND store_id = #{storeId}
        </if>
        AND business_id = #{businessId}
        AND status = 0
        <if test="null != keyWord and '' != keyWord">
            AND (goods_no = #{keyWord} or sku_name like concat('',#{keyWord},'%'))
        </if>
        <if test="null != startTime and null != endTime">
            AND gmt_create BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="null != priceWarnType and priceWarnType != -1">
            AND price_warn_type = #{priceWarnType}
        </if>
    </select>

    <select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List" />
        FROM price_warning_record
        WHERE 1 = 1
        <if test="null != storeId and storeId > 0">
            AND store_id = #{storeId}
        </if>
        AND business_id = #{businessId}
        AND status = 0
        <if test="null != keyWord and '' != keyWord">
            AND (goods_no = #{keyWord} or sku_name like concat('',#{keyWord},'%'))
        </if>
        <if test="null != startTime and null != endTime">
            AND gmt_create BETWEEN #{startTime} and #{endTime}
        </if>
        <if test="null != priceWarnType and priceWarnType != -1">
            AND price_warn_type = #{priceWarnType}
        </if>
        ORDER BY store_id,goods_no,gmt_create desc
    </select>

    <select id="queryPushMsg" resultMap="PushMsgDTOResultMap" parameterType="java.lang.Long">
        SELECT
            business_id,
            store_id,
            store_name,
            price_warn_type,
            json_extract (extend, '$.businessName') AS business_name, count(1) AS num
        FROM
            price_warning_record
        WHERE
            business_id = #{businessId}
        AND `status` = 0
        GROUP BY
            price_warn_type
    </select>
    
    <select id="selectPriceWarnPushData" resultMap="PushMsgDTOResultMap">
        select business_id,store_id,warn_type from price_warning_record 
        where business_id = #{businessId} and gmt_create between #{startTime} and #{endTime}
        and `status` = 0 group by business_id,store_id,warn_type
    </select>
</mapper>
