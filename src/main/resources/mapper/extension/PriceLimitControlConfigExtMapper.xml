<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceLimitControlConfigExtMapper">

    <!-- 第一步：根据主表条件查询规则ID列表 -->
    <select id="selectRuleIdsByMainCondition" parameterType="com.cowell.pricecenter.service.dto.request.PriceLimitControlQueryParam"
            resultType="long">
        SELECT c.id
        FROM price_limit_control_config c
        WHERE 1=1
        <if test="id != null and id != ''">
            AND c.id = #{id}
        </if>
        <if test="status != null">
            AND c.status = #{status}
        </if>
        <if test="status == null">
            AND c.status >= -1
        </if>
        <if test="limitType != null">
            AND c.limit_type = #{limitType}
        </if>
        <if test="gmtCreateBegin != null and gmtCreateBegin != ''">
            AND c.gmt_create >= #{gmtCreateBegin}
        </if>
        <if test="gmtCreateEnd != null and gmtCreateEnd != ''">
            AND c.gmt_create &lt;= #{gmtCreateEnd}
        </if>
        <if test="createdByName != null and createdByName != ''">
            AND c.created_by_name LIKE CONCAT(#{createdByName}, '%')
        </if>
        <if test="ruleName != null and ruleName != ''">
            AND c.rule_name LIKE CONCAT('%', #{ruleName}, '%')
        </if>
        ORDER BY c.gmt_create DESC
    </select>

    <!-- 第二步：根据门店条件过滤规则ID -->
    <select id="selectRuleIdsByStoreCondition" resultType="long">
        SELECT  price_limit_control_id
        FROM price_limit_control_org_expand
        WHERE status = 0
        <if test="ruleIds != null and ruleIds.size() > 0">
            AND price_limit_control_id IN
            <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
                #{ruleId}
            </foreach>
        </if>
        <if test="storeOrgIds != null and storeOrgIds.size() > 0">
            AND store_org_id IN
            <foreach collection="storeOrgIds" item="storeOrgId" open="(" separator="," close=")">
                #{storeOrgId}
            </foreach>
        </if>
        <if test="businessOrgIds != null and businessOrgIds.size() > 0">
            AND business_org_id IN
            <foreach collection="businessOrgIds" item="businessOrgId" open="(" separator="," close=")">
                #{businessOrgId}
            </foreach>
        </if>
        group by  price_limit_control_id
    </select>

    <!-- 第三步：根据商品条件过滤规则ID -->
    <select id="selectRuleIdsByGoodsCondition" resultType="long">
        SELECT price_limit_control_id
        FROM price_limit_control_goods_config
        WHERE 1=1
        <if test="ruleIds != null and ruleIds.size() > 0">
            AND price_limit_control_id IN
            <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
                #{ruleId}
            </foreach>
        </if>
        <if test="goodsNo != null and goodsNo != ''">
            AND goods_no = #{goodsNo}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        group by  price_limit_control_id
    </select>


    <!-- 批量插入商品配置 -->
    <insert id="batchInsertGoodsConfig" parameterType="java.util.List">
        INSERT INTO price_limit_control_goods_config (
            price_limit_control_id, goods_no, goods_name, jhi_specification,
            manufacturer, unit, online_price, national_talk_price, status,
            gmt_create, gmt_update, version, created_by, created_by_name,
            updated_by, updated_by_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.priceLimitControlId}, #{item.goodsNo}, #{item.goodsName}, #{item.jhiSpecification},
                #{item.manufacturer}, #{item.unit}, #{item.onlinePrice}, #{item.nationalTalkPrice}, #{item.status},
                #{item.gmtCreate}, #{item.gmtUpdate}, #{item.version}, #{item.createdBy}, #{item.createdByName},
                #{item.updatedBy}, #{item.updatedByName}
            )
        </foreach>
    </insert>

    <!-- 批量插入机构配置 -->
    <insert id="batchInsertOrgExpand" parameterType="java.util.List">
        INSERT INTO price_limit_control_org_expand (
            price_limit_control_id, store_org_id, store_id, store_name,
            business_org_id, business_id, business_name, platform_id, platform_name,
            status, gmt_create, gmt_update, version, created_by, created_by_name,
            updated_by, updated_by_name
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.priceLimitControlId}, #{item.storeOrgId}, #{item.storeId}, #{item.storeName},
                #{item.businessOrgId}, #{item.businessId}, #{item.businessName}, #{item.platformId}, #{item.platformName},
                #{item.status}, #{item.gmtCreate}, #{item.gmtUpdate}, #{item.version}, #{item.createdBy}, #{item.createdByName},
                #{item.updatedBy}, #{item.updatedByName}
            )
        </foreach>
    </insert>


    <update id="updateConfigLimitValue" parameterType="com.cowell.pricecenter.entity.PriceLimitControlConfig">
        update price_limit_control_config
        <set>
            <if test="markupRatioLimit != null">
                markup_ratio_limit = #{markupRatioLimit,jdbcType=DECIMAL},
            </if>
            <if test="markupRatioLimit == null">
                markup_ratio_limit = NULL,
            </if>
            <if test="markupAmountLimit != null">
                markup_amount_limit = #{markupAmountLimit,jdbcType=DECIMAL},
            </if>
            <if test="markupAmountLimit == null">
                markup_amount_limit = NULL,
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 根据规则ID删除商品配置 -->
    <delete id="deleteGoodsConfigByRuleId" parameterType="long">
        delete from    price_limit_control_goods_config
        WHERE price_limit_control_id = #{ruleId}
    </delete>

    <!-- 根据规则ID删除机构配置 -->
    <delete id="deleteOrgExpandByRuleId" parameterType="long">
        delete from  price_limit_control_org_expand
        WHERE price_limit_control_id = #{ruleId}
    </delete>

    <!-- 校验规则名称唯一性 -->
    <select id="checkRuleNameUnique" resultType="int">
        SELECT COUNT(1)
        FROM price_limit_control_config
        WHERE rule_name = #{ruleName}
        AND status != -1
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>


    <!-- 根据规则ID查询商品配置 -->
    <select id="selectGoodsConfigByRuleId" resultType="com.cowell.pricecenter.entity.PriceLimitControlGoodsConfig">
        SELECT
        id AS id,
        price_limit_control_id AS priceLimitControlId,
        goods_no AS goodsNo,
        goods_name AS goodsName,
        jhi_specification AS jhiSpecification,
        manufacturer AS manufacturer,
        unit AS unit,
        online_price AS onlinePrice,
        national_talk_price AS nationalTalkPrice,
        reason AS reason,
        status AS status
        FROM price_limit_control_goods_config
        WHERE price_limit_control_id = #{ruleId}
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="goodsKeyword != null and goodsKeyword != ''">
            AND (goods_no LIKE CONCAT('%', #{goodsKeyword}, '%')
            OR goods_name LIKE CONCAT('%', #{goodsKeyword}, '%'))
        </if>
        ORDER BY
            reason DESC,
            gmt_update DESC
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 统计规则商品数量（带关键词过滤） -->
    <select id="countGoodsConfigByRuleIdAndKeyword" resultType="long">
        SELECT COUNT(1)
        FROM price_limit_control_goods_config
        WHERE price_limit_control_id = #{ruleId}
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="goodsKeyword != null and goodsKeyword != ''">
            AND (goods_no LIKE CONCAT('%', #{goodsKeyword}, '%')
            OR goods_name LIKE CONCAT('%', #{goodsKeyword}, '%'))
        </if>
    </select>


</mapper>
