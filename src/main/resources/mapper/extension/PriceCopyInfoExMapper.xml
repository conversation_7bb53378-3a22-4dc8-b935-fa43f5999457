<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceCopyInfoExMapper">

  <update id="updateBatchedNumAddByCopyNo" parameterType="String">
    update price_copy_info
     set batched_num = batched_num +1
      where copy_no = #{copyNo,jdbcType=VARCHAR}
  </update>

</mapper>
