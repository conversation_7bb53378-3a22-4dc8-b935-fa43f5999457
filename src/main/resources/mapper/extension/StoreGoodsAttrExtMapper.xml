<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.StoreGoodsAttrExtMapper">
    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.StoreGoodsAttr">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="store_attr_code" jdbcType="VARCHAR" property="storeAttrCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>

    <sql id="Base_Column_List">
    id, business_id, business_code, business_name, store_id, store_code, store_name, 
    goods_no, goods_name, `comment`, `label`, store_attr_code, `type`, `status`, gmt_create, 
    gmt_update, create_by, create_by_id, update_by, update_by_id, version, extend
  </sql>

    <!--批量保存-->
    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttr">
        insert into store_goods_attr (business_id, business_code,
	      business_name, store_id, store_code,
	      store_name, goods_no, goods_name,
	      `comment`, `label`, store_attr_code, `type`,
	      `status`, gmt_create, gmt_update,
	      create_by, create_by_id, update_by,
	      update_by_id, version, extend)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.businessId,jdbcType=BIGINT}, #{item.businessCode,jdbcType=VARCHAR},
      			#{item.businessName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT}, #{item.storeCode,jdbcType=VARCHAR},
      			#{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
      			#{item.comment,jdbcType=VARCHAR}, #{item.label,jdbcType=VARCHAR}, #{item.storeAttrCode,jdbcType=VARCHAR}, 
      			#{item.type,jdbcType=INTEGER},#{item.status,jdbcType=INTEGER}, #{item.gmtCreate,jdbcType=TIMESTAMP},
      			#{item.gmtUpdate,jdbcType=TIMESTAMP},#{item.createBy,jdbcType=VARCHAR}, #{item.createById,jdbcType=BIGINT}, 
      			#{item.updateBy,jdbcType=VARCHAR},#{item.updateById,jdbcType=BIGINT}, #{item.version,jdbcType=INTEGER},
      			#{item.extend,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

    <delete id="deleteBatch">
        delete from store_goods_attr
        where 1=1
        <if test="null != businessId">
            and business_id =  #{businessId}
        </if>
        <if test="storeIdList != null and storeIdList.size > 0">
            and store_id in
            <foreach collection="storeIdList" item="storeId" index="index" separator="," open="(" close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="null != deleteBeforeNow">
            and gmt_create &lt;  #{deleteBeforeNow}
        </if>
        limit #{limit}
    </delete>

</mapper>
