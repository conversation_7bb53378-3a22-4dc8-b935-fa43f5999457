<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceBusinessDetailHistoryExtMapper">
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail" useGeneratedKeys="true">
        insert into price_business_detail_history (
        id, goods_no, channel_id, business_id, price, price_type_code, price_type_id, price_type_name, 
        order_code, order_type, `status`, extend, version, gmt_create, created_by, created_by_name, 
        gmt_update, updated_by, updated_by_name, `comment`)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id,jdbcType=VARCHAR},#{item.goodsNo,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER}, 
                #{item.businessId,jdbcType=BIGINT}, #{item.price,jdbcType=DECIMAL}, #{item.priceTypeCode,jdbcType=VARCHAR},
                #{item.priceTypeId,jdbcType=BIGINT}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.orderCode,jdbcType=VARCHAR}, 
                #{item.orderType,jdbcType=INTEGER}, #{item.status,jdbcType=TINYINT}, #{item.extend,jdbcType=VARCHAR}, 
                #{item.version,jdbcType=INTEGER}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=BIGINT}, 
                #{item.createdByName,jdbcType=VARCHAR}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.updatedBy,jdbcType=BIGINT}, 
                #{item.updatedByName,jdbcType=VARCHAR}, #{item.comment,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>
    
    <delete id="deleteHistoryRefPrice">
        delete from price_business_detail_history
        <where>
            order_code = #{adjustCode}  
            and channel_id=#{channelId} 
            and goods_no=#{goodsNo}  
            and business_id = #{businessId}
            and price_type_code in
            <foreach collection="priceTypeCodes" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
    </delete> 
</mapper>