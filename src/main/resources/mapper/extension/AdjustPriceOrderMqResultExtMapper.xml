<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.AdjustPriceOrderMqResultExtMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceOrderMqResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="adjust_execute_time" jdbcType="TIMESTAMP" property="adjustExecuteTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, adjust_code, store_id, business_id, adjust_execute_time, `status`, gmt_create,
    gmt_update
  </sql>
    <select id="selectDistinctBusinessIdByAdjustCode" parameterType="java.lang.String" resultType="Long">
        select business_id from adjust_price_order_mq_result where adjust_code = #{adjustCode} group by business_id
    </select>

    <select id="selectDistinctAdjustCodeByGmtCreate" resultType="java.lang.String">
        select adjust_code
        from adjust_price_order_mq_result
        where
            <![CDATA[
                gmt_create < #{gmtCreate}
            ]]>
        and `status` in (0, 1)
        group by adjust_code
    </select>

</mapper>
