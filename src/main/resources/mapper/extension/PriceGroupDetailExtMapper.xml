<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceGroupDetailExtMapper">
    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceGroupDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="group_id" jdbcType="BIGINT" property="groupId" />
        <result column="parent_org_level" jdbcType="TINYINT" property="parentOrgLevel" />
        <result column="parent_org_id" jdbcType="BIGINT" property="parentOrgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="org_level" jdbcType="TINYINT" property="orgLevel" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    </resultMap>

    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.PriceGroupDetail">
        insert into price_group_detail (group_id, parent_org_level,
        parent_org_id, org_name, org_level,
        org_id, gmt_create,
        gmt_update, extend,
        created_by, updated_by, updated_by_name
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.groupId,jdbcType=BIGINT}, #{item.parentOrgLevel,jdbcType=TINYINT},
                #{item.parentOrgId,jdbcType=BIGINT}, #{item.orgName,jdbcType=VARCHAR}, #{item.orgLevel,jdbcType=TINYINT},
                #{item.orgId,jdbcType=BIGINT}, #{item.gmtCreate,jdbcType=TIMESTAMP},
                #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR},
                #{item.createdBy,jdbcType=BIGINT}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedByName,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>


</mapper>
