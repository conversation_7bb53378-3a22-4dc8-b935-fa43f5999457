<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgDetailExtMapper">

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail" useGeneratedKeys="true">
        insert into adjust_price_order_org_detail (adjust_code, org_id, org_level,org_name, gmt_create, gmt_update)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.adjustCode,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT}, #{item.orgLevel,jdbcType=INTEGER},
                #{item.orgName,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}
            </trim>
        </foreach>

    </insert>

</mapper>
