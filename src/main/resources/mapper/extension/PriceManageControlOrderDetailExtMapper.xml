<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceManageControlOrderDetailExtMapper">

    <resultMap id="AdjustPriceOrderManagePriceMap" type="com.cowell.pricecenter.service.dto.AdjustPriceOrderManagePriceDTO">
        <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit"/>
        <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit"/>
        <result column="guide_price" jdbcType="DECIMAL" property="guidePrice"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceManageControlOrderDetail">
        <!--@mbg.generated-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="control_order_code" jdbcType="VARCHAR" property="controlOrderCode" />
        <result column="auth_org_id" jdbcType="BIGINT" property="authOrgId" />
        <result column="auth_org_name" jdbcType="VARCHAR" property="authOrgName" />
        <result column="auth_level" jdbcType="TINYINT" property="authLevel" />
        <result column="org_goods_id" jdbcType="BIGINT" property="orgGoodsId" />
        <result column="spu_id" jdbcType="BIGINT" property="spuId" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="price_group_names" jdbcType="VARCHAR" property="priceGroupNames" />
        <result column="price_type_id" jdbcType="BIGINT" property="priceTypeId" />
        <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
        <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
        <result column="price_type_out_code" jdbcType="VARCHAR" property="priceTypeOutCode" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
        <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit" />
        <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit" />
        <result column="cur_name" jdbcType="VARCHAR" property="curName" />
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
        <result column="jhi_specification" jdbcType="VARCHAR" property="jhiSpecification" />
        <result column="dosage" jdbcType="VARCHAR" property="dosage" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="extend1" jdbcType="VARCHAR" property="extend1" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
        <result column="channel_id" jdbcType="INTEGER" property="channelId" />
        <result column="prodarea" jdbcType="VARCHAR" property="prodarea" />
        <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
        <result column="channel_out_code" jdbcType="VARCHAR" property="channelOutCode" />
        <result column="channel_en_code" jdbcType="VARCHAR" property="channelEnCode" />
        <result column="guide_price" jdbcType="DECIMAL" property="guidePrice" />
        <result column="dxs_date" jdbcType="TIMESTAMP" property="dxsDate" />
        <result column="effect_status" jdbcType="INTEGER" property="effectStatus" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, control_order_detail_id, control_order_code, auth_org_id, auth_org_name, auth_level, org_goods_id, spu_id,
        goods_no, price_group_names, price_type_id, price_type_code, price_type_name, price_type_out_code,
        price, original_price, upper_limit, lower_limit, cur_name, manufacturer, jhi_specification,
        dosage, `status`, gmt_create, gmt_update, extend, extend1, version, created_by, updated_by,
        updated_by_name, channel_id, prodarea, goods_unit, goods_name, channel_out_code,
        channel_en_code, guide_price,dxs_date,effect_status
    </sql>

    <sql id="Good_Price_Type_Base_Column_List">
        control_order_code, goods_no, price_type_id, price_type_code, price_type_name, gmt_create
    </sql>

    <select id="getNewestAdjustPriceOrderManagePriceDTO" resultMap="AdjustPriceOrderManagePriceMap">
        select a.upper_limit,
               a.lower_limit,
               a.guide_price
        from price_manage_control_order_detail a
                 join price_manage_control_org_detail b on a.control_order_code = b.control_order_code
                 join price_manage_control_order c on a.control_order_code = c.control_order_id
        where a.goods_no = #{goodsNo}
          and a.price_type_code = #{priceTypeCode}
          and a.channel_id = #{channelId}
          and a.status = 0
          and c.audit_status = 5
          <if test="effectTime != null and effectTime != ''">
             <![CDATA[
                and c.effect_time <=  #{effectTime}
             ]]>
          </if>
          <if test="orgIdList != null and orgIdList.size>0">
            and b.org_id  in
            <foreach collection="orgIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
          </if>
        order by  c.order_level desc, c.effect_time desc, a.gmt_update desc limit 1
    </select>

    <select id="controlOrderDetailListCount" resultType="java.lang.Integer">
        select
        count(*)
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode,jdbcType=VARCHAR}
        <if test="goodsNo != null and goodsNo != ''">
            and goods_no = #{goodsNo,jdbcType=VARCHAR}
        </if>
        group by goods_no;
    </select>

    <select id="controlOrderIdGoodsCount" resultType="java.util.HashMap">
        SELECT control_order_code as 'code', COUNT(*) as 'num' FROM `price_manage_control_order_detail`
        where 1=1
        <if test="controlOrderIdList != null and controlOrderIdList.size>0">
            and control_order_code in
            <foreach collection="controlOrderIdList" item="item" index="index" separator="," open="("
                     close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY control_order_code
    </select>
    <select id="controlOrderDetailList" resultType="com.cowell.pricecenter.service.dto.response.controlOrder.ControlOrderDetailVO">
        select
            control_order_code controlOrderCode,
            goods_no goodsNo,
            goods_name goodsName,
            cur_name curName,
            jhi_specification jhiSpecification,
            manufacturer manufacturer,
            goods_unit goodsUnit,
            prodarea prodarea
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode,jdbcType=VARCHAR}
        <if test="goodsNo != null and goodsNo != ''">
            and goods_no = #{goodsNo,jdbcType=VARCHAR}
        </if>
        group by goods_no
        limit ${offset}, ${limit}
    </select>



    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetail">
        insert into price_manage_control_order_detail (control_order_detail_id, control_order_code, auth_org_id, auth_org_name,
        auth_level, org_goods_id, spu_id,
        goods_no, price_group_names, price_type_id,
        price_type_code, price_type_name, price_type_out_code,
        price, original_price, upper_limit,
        lower_limit, cur_name, manufacturer,
        jhi_specification, dosage, `status`,
        gmt_create, gmt_update, extend,
        extend1, version, created_by,
        updated_by, updated_by_name, channel_id,
        prodarea, goods_unit, goods_name,
        channel_out_code, channel_en_code, guide_price,
        dxs_date,effect_status,order_type)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.controlOrderDetailId,jdbcType=VARCHAR}, #{item.controlOrderCode,jdbcType=VARCHAR}, #{item.authOrgId,jdbcType=BIGINT}, #{item.authOrgName,jdbcType=VARCHAR},
                #{item.authLevel,jdbcType=TINYINT}, #{item.orgGoodsId,jdbcType=BIGINT}, #{item.spuId,jdbcType=BIGINT},
                #{item.goodsNo,jdbcType=VARCHAR}, #{item.priceGroupNames,jdbcType=VARCHAR}, #{item.priceTypeId,jdbcType=BIGINT},
                #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.priceTypeOutCode,jdbcType=VARCHAR},
                #{item.price,jdbcType=DECIMAL}, #{item.originalPrice,jdbcType=DECIMAL}, #{item.upperLimit,jdbcType=DECIMAL},
                #{item.lowerLimit,jdbcType=DECIMAL}, #{item.curName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR},
                #{item.jhiSpecification,jdbcType=VARCHAR}, #{item.dosage,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
                #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR},
                #{item.extend1,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT},
                #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedByName,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER},
                #{item.prodarea,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
                #{item.channelOutCode,jdbcType=VARCHAR}, #{item.channelEnCode,jdbcType=VARCHAR}, #{item.guidePrice,jdbcType=DECIMAL},
                #{item.dxsDate,jdbcType=TIMESTAMP},#{item.effectStatus,jdbcType=INTEGER},#{item.orderType,jdbcType=TINYINT}
            </trim>
        </foreach>

    </insert>

    <sql id="selectSingleGoodPriceTypeOrderDetailV2ListWhere">
        where status = 0 and control_order_code = #{controlOrderCode} and
        JSON_EXTRACT(extend1, "$.dataCompletion") = #{dataCompletion}
        <if test="goodsKeyword != null and goodsKeyword !=''">
            and (goods_no like #{goodsKeyword} OR cur_name like #{goodsKeyword})
        </if>
        <if test="channelIdList != null and channelIdList.size > 0">
            and channel_id in
            <foreach collection="channelIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="priceTypeCodeList != null and priceTypeCodeList.size > 0">
            and price_type_code in
            <foreach collection="priceTypeCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="countControlOrderDetailSupplementNotCompleted" resultType="java.lang.Integer">
        select
            count(*)
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataCompletion") = 0
    </select>

    <select id="countControlOrderDetailCompleted" resultType="java.lang.Integer">
        select
            count(*)
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataCompletion") = 1 and JSON_EXTRACT(extend1, "$.dataFull") = 1
    </select>

    <select id="selectRemoveChannelAttOrderDetailV2ListCount" resultType="java.lang.Long">
        select
        count(distinct goods_no)
        from price_manage_control_order_detail
        <include refid="selectSingleGoodPriceTypeOrderDetailV2ListWhere"></include>
    </select>

    <select id="selectRemoveChannelAttOrderDetailV2ListPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from price_manage_control_order_detail
        <include refid="selectSingleGoodPriceTypeOrderDetailV2ListWhere"></include>
        group by control_order_code, goods_no, price_type_code
        <if test="toPage">
            LIMIT #{offset}, #{size}
        </if>
    </select>

    <select id="selectOrderDetailListGroupByGoodsAndPriceType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode} and goods_no in
        <foreach collection="goodsNoList" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by control_order_code, goods_no, price_type_code
        order by gmt_create desc
    </select>

    <select id="selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType"  resultType="java.lang.String">
        select
        goods_no
        from price_manage_control_order_detail
        <include refid="selectSingleGoodPriceTypeOrderDetailV2ListWhere"></include>
        group by goods_no
        order by gmt_create desc
        <if test="toPage">
            LIMIT #{offset}, #{size}
        </if>
    </select>

    <select id="selectOrderDetailV2ListPageGroupByGoodAndPriceType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from price_manage_control_order_detail
        <include refid="selectSingleGoodPriceTypeOrderDetailV2ListWhere"></include>
        order by gmt_create desc
        <if test="toPage">
            LIMIT #{offset}, #{size}
        </if>
    </select>

    <select id="getToCheckOrderDetailListForDetailDataNotFull" resultMap="BaseResultMap">
        select control_order_code, goods_no, price_type_code, price_type_name
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode} and status = 0 and JSON_EXTRACT(extend1, "$.dataFull") = 0
        group by control_order_code, goods_no, price_type_code, price_type_name
    </select>

    <select id="countOfGoodsByControlOrderCode" resultType="java.lang.Integer">
        select
            count(distinct goods_no)
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode} and status = 0
    </select>

    <update id="updateByAdjustCode">
        update
        price_manage_control_order_detail
        <set>

            <if test="price != null">
                price = #{price},
            </if>
            <if test="extend1Str != null">
                extend1 = #{extend1Str},
            </if>
            <if test="spuId != null">
                spu_id = #{spuId},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice},
            </if>
            <if test="guidePrice != null">
                guide_price = #{guidePrice},
            </if>
            <if test="upperLimit != null">
                upper_limit = #{upperLimit},
            </if>
            <if test="lowerLimit != null">
                lower_limit = #{lowerLimit},
            </if>
            <if test="gmtUpdate != null">
                gmt_update = #{gmtUpdate},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedByName != null">
                updated_by_name = #{updatedByName},
            </if>
            <if test="manufacturer != null">
                manufacturer = #{manufacturer},
            </if>
            <if test="jhiSpecification != null">
                jhi_specification = #{jhiSpecification},
            </if>
            <if test="dosage != null">
                dosage = #{dosage},
            </if>
            <if test="prodarea != null">
                prodarea = #{prodarea},
            </if>
            <if test="goodsUnit != null">
                goods_unit = #{goodsUnit},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName},
            </if>
            <if test="curName != null">
                cur_name = #{curName},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            version = version + 1
        </set>
        where
        control_order_code = #{controlOrderCode}
        <if test="goodsNo != null">
            and goods_no = #{goodsNo}
        </if>
        <if test="priceTypeCode != null">
            and price_type_code = #{priceTypeCode}
        </if>
        <if test="channelId != null">
            and channel_id = #{channelId}
        </if>
    </update>

    <delete id="deleteByControlCodeAndGoodsNoAndPriceType">
        delete
        from  price_manage_control_order_detail
        where control_order_code = #{controlOrderCode} and price_type_code = #{priceTypeCode} and goods_no = #{goodsNo}
    </delete>

    <select id="getGoodsNoListbyControlCode" resultType="java.lang.String">
        select
            goods_no
        from price_manage_control_order_detail
        where control_order_code = #{controlOrderCode} and status = 0
        group by goods_no
    </select>
</mapper>
