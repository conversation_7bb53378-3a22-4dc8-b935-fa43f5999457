<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgStoreDetailExtMapper">

    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="org_store_detail_id" jdbcType="VARCHAR" property="orgStoreDetailId" />
        <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="adjust_price_level" jdbcType="INTEGER" property="adjustPriceLevel" />
        <result column="store_id" jdbcType="BIGINT" property="storeId" />
        <result column="store_name" jdbcType="VARCHAR" property="storeName" />
        <result column="business_id" jdbcType="BIGINT" property="businessId" />
        <result column="business_name" jdbcType="VARCHAR" property="businessName" />
        <result column="platform_id" jdbcType="BIGINT" property="platformId" />
        <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
        <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
        <result column="sku_id" jdbcType="BIGINT" property="skuId" />
        <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
        <result column="price" jdbcType="DECIMAL" property="price" />
        <result column="channel_id" jdbcType="INTEGER" property="channelId" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="adjust_execute_time" jdbcType="TIMESTAMP" property="adjustExecuteTime" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <sql id="Base_Column_List">
        id, org_store_detail_id, adjust_code, org_id, org_name, adjust_price_level, store_id,
        store_name, business_id, business_name, platform_id, platform_name, goods_no, sku_id, price_type_code,price,
        channel_id, `status`, adjust_execute_time,gmt_create, gmt_update,extend
    </sql>

    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail">
        insert into adjust_price_order_org_store_detail (org_store_detail_id, adjust_code, org_id,
        org_name, adjust_price_level, store_id,
        store_name, business_id, business_name,
        platform_id, platform_name, goods_no, sku_id,
        price_type_code, price, channel_id, `status`,
        adjust_execute_time, gmt_create, gmt_update, extend
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.orgStoreDetailId,jdbcType=VARCHAR}, #{item.adjustCode,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT},
                #{item.orgName,jdbcType=VARCHAR}, #{item.adjustPriceLevel,jdbcType=INTEGER}, #{item.storeId,jdbcType=BIGINT},
                #{item.storeName,jdbcType=VARCHAR}, #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR},
                #{item.platformId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR},  #{item.goodsNo,jdbcType=VARCHAR},#{item.skuId,jdbcType=BIGINT},
                #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, #{item.channelId,jdbcType=INTEGER}, #{item.status,jdbcType=TINYINT},
                #{item.adjustExecuteTime,jdbcType=TIMESTAMP}, #{item.gmtCreate,jdbcType=TIMESTAMP},
                #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

    <select id="distinctStorePage" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetailExample" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        adjust_code,org_id,adjust_price_level,store_id,business_id
        from adjust_price_order_org_store_detail
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        group by store_id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>

    <update id="batchUpdateExtend">
        update adjust_price_order_org_store_detail
        set extend =
        <foreach collection="orgStoreDetailList" item="item" open="case " close=" end">
            when  adjust_code = #{adjustCode} and id = #{item.id}  then #{item.extend}
        </foreach>
        <where>
            adjust_code = #{adjustCode} and id in
            <foreach collection="orgStoreDetailList" index="index" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
        </where>
    </update>

    <select id="selectAdjustOrderDetailIdByStore" resultType="java.lang.String">
        select
        json_extract (extend, '$.adjustDetailId')
        from adjust_price_order_org_store_detail
        where 1=1
        AND extend LIKE '%adjustDetailId%'
        <if test="storeId != null">
            and store_id = #{storeId}
        </if>
        <if test="adjustCodeList != null and adjustCodeList.size > 0">
            and adjust_code in
            <foreach collection="adjustCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectOrgStoreDetailSplit" resultType="com.cowell.pricecenter.param.OrgStoreDetailSplitDTO">
        select adjust_code as adjustCode,store_id as storeId,price_type_code as priceTypeCode,channel_id as channelId
        from adjust_price_order_org_store_detail
        where adjust_code = #{adjustCode}
        group by adjust_code,store_id,price_type_code,channel_id
    </select>

    <select id="selectOrgStoreDetailUnSupplement" resultType="java.lang.Integer">
        select count(1)
        from adjust_price_order_org_store_detail
        where adjust_code = #{adjustCode} and status=0
    </select>

    <delete id="updateStatusBatch">
        update adjust_price_order_org_store_detail set status=0
        where adjust_code = #{adjustCode} and status=5 limit #{limit}
   </delete>

   <select id="listIdByAdjustCodeAndStoreOrgIdsAndGoodnoes" resultType="java.lang.Long">
   		SELECT id FROM
		adjust_price_order_org_store_detail
		WHERE
			adjust_code = #{adjustCode} and JSON_EXTRACT(extend, "$.effectStatus") = 1
			<if test="storeOrgIds != null and storeOrgIds.size > 0">
	            and org_id IN
				<foreach collection="storeOrgIds" item="item" index="index" open="(" close=")" separator=",">
	                #{item}
	            </foreach>
	        </if>
			<if test="goodsnoList != null and goodsnoList.size > 0">
	            and goods_no IN
				<foreach collection="goodsnoList" item="item" index="index" open="(" close=")" separator=",">
	                #{item}
	            </foreach>
	        </if>
	        <if test="priceTypeList != null and priceTypeList.size > 0">
	            and price_type_code not in
				<foreach collection="priceTypeList" item="item" index="index" open="(" close=")" separator=",">
	                #{item}
	            </foreach>
	        </if>
	        <if test="summary != null and summary">
	        	group by business_id,goods_no,adjust_code,price_type_code,price
	        </if>
		LIMIT ${offset}, ${limit}
   </select>

   <select id="priceHistorySummaryData" resultType="com.cowell.pricecenter.service.dto.request.AdjustPriceHistoryRecordDTO">
        select business_id as businessId,business_name as businessName,goods_no as goodsNo,
        adjust_code as adjustCode,price_type_code as priceType,price as adjustAfterPrice,
		json_extract(extend,'$.adjustBeforePrice') as adjustBeforePrice,
		json_extract(extend,'$.adjustDetailId') as adjustDetailId from adjust_price_order_org_store_detail
		where adjust_code = #{adjustCode}
		and id in
		<foreach collection="idList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
		group by business_id,goods_no,adjust_code,price_type_code,price

    </select>

    <select id="selectPricePushHistoryStatistics" parameterType="com.cowell.pricecenter.param.PricePushHistoryParam" resultType="com.cowell.pricecenter.service.dto.PricePushHistoryStatisticsDTO">
        select
		count(1) as total,
		sum(case JSON_EXTRACT(pph.extend, "$.effectStatus") when -1 then 1 else 0 end) as progressCount,
		sum(case JSON_EXTRACT(pph.extend, "$.effectStatus") when 1 then 1 else 0 end) as successCount,
		sum(case JSON_EXTRACT(pph.extend, "$.effectStatus") when 0 then 1 else 0 end) as failCount
		from adjust_price_order_org_store_detail pph where pph.adjust_code = #{adjustCode} and pph.price_type_code!='GJYJS'
		<if test="businessId != null">
          and pph.business_id = #{businessId}
        </if>
		<if test="storeId != null">
          and pph.store_id = #{storeId}
        </if>
        <if test="goodsNo != null and goodsNo!=''">
           and pph.goods_no = #{goodsNo}
        </if>
        <if test="channelId !=null">
           and pph.channel_id = #{channelId}
        </if>
        <if test="priceTypeCode !=null and priceTypeCode!=''">
           and pph.price_type_code = #{priceTypeCode}
        </if>
        <if test="priceTypeCodeList != null and priceTypeCodeList.size > 0">
            and pph.price_type_code in
            <foreach collection="priceTypeCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectPricePushHistoryEffectError" resultType="integer">
        select count(1) from adjust_price_order_org_store_detail
	    where adjust_code = #{adjustCode} and JSON_EXTRACT(extend, "$.effectStatus") = 0 and JSON_EXTRACT(extend, "$.effectMsg") = '商品编码不存在' and price_type_code!='GJYJS'
    </select>

    <select id="selectPricePushHistoryPage" parameterType="com.cowell.pricecenter.param.PricePushHistoryParam" resultType="com.cowell.pricecenter.entity.PricePushHistory">
        select
	    id, business_id as businessId, store_id as storeId, store_name as storeName, sku_id as skuId, goods_no as goodsNo, adjust_code as adjustCode, price_type_code as priceTypeCode,
	    channel_id as channelId, price, gmt_create as gmtCreate,gmt_update as gmtUpdate,extend
	    from adjust_price_order_org_store_detail
	    where adjust_code = #{adjustCode} and JSON_EXTRACT(extend, "$.effectStatus") = #{effectStatus} and price_type_code!='GJYJS'
	    <if test="storeId != null">
	      and store_id = #{storeId}
	    </if>
	    <if test="goodsNo != null and goodsNo!=''">
	       and goods_no = #{goodsNo}
	    </if>
	    <if test="channelId !=null">
	       and channel_id = #{channelId}
	    </if>
        <if test="businessId !=null">
            and business_id = #{businessId}
        </if>
	    <if test="priceTypeCode !=null and priceTypeCode!=''">
	       and price_type_code = #{priceTypeCode}
	    </if>
	    <if test="priceTypeCodeList != null and priceTypeCodeList.size > 0">
            and price_type_code in
            <foreach collection="priceTypeCodeList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
	    order by goods_no
    </select>

    <select id="distinctBusinessPage" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetailExample" resultMap="BaseResultMap">
        select adjust_code,business_id,goods_no,price_type_code,price,channel_id
        from adjust_price_order_org_store_detail
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        group by business_id,goods_no,price_type_code,price
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
</mapper>
