<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceDictionaryExtendMapper">
    <insert id="batchInsert" parameterType="list">
        insert into price_dictionary (
            dict_code, dict_name, dict_type, parent_id, `status`, extend,
            created_by, updated_by, updated_by_name, gmt_create, gmt_update
        )
        values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.dictCode,jdbcType=VARCHAR},
            #{item.dictName,jdbcType=VARCHAR},
            #{item.dictType,jdbcType=INTEGER},
            #{item.parentId,jdbcType=BIGINT},
            #{item.status,jdbcType=TINYINT},
            #{item.extend,jdbcType=VARCHAR},
            #{item.createdBy,jdbcType=BIGINT},
            #{item.updatedBy,jdbcType=BIGINT},
            #{item.updatedByName,jdbcType=VARCHAR},
            #{item.gmtCreate,jdbcType=TIMESTAMP},
            #{item.gmtUpdate,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <select id="getMaxCode" resultType="java.lang.String" parameterType="java.lang.Long">
        select max(dict_code) maxCode from price_dictionary where parent_id = #{parentId}
    </select>
</mapper>
