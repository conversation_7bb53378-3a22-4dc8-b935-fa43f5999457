<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PricePushTaskExtMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PricePushTask">
      <id column="id" jdbcType="BIGINT" property="id" />
      <result column="business_id" jdbcType="BIGINT" property="businessId" />
      <result column="item_count" jdbcType="INTEGER" property="itemCount" />
      <result column="push_code" jdbcType="VARCHAR" property="pushCode" />
      <result column="batch_count" jdbcType="INTEGER" property="batchCount" />
      <result column="batch" jdbcType="INTEGER" property="batch" />
      <result column="batch_total_count" jdbcType="INTEGER" property="batchTotalCount" />
      <result column="adjust_time" jdbcType="TIMESTAMP" property="adjustTime" />
      <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
      <result column="type" jdbcType="INTEGER" property="type" />
      <result column="result" jdbcType="TINYINT" property="result" />
      <result column="receive_msg" jdbcType="VARCHAR" property="receiveMsg" />
      <result column="price_update_result" jdbcType="INTEGER" property="priceUpdateResult" />
      <result column="price_update_msg" jdbcType="VARCHAR" property="priceUpdateMsg" />
      <result column="original_batch" jdbcType="INTEGER" property="originalBatch" />
      <result column="push_again" jdbcType="TINYINT" property="pushAgain" />
      <result column="result_time" jdbcType="TIMESTAMP" property="resultTime" />
      <result column="status" jdbcType="TINYINT" property="status" />
      <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
      <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
      <result column="extend" jdbcType="VARCHAR" property="extend" />
      <result column="version" jdbcType="INTEGER" property="version" />
      <result column="created_by" jdbcType="BIGINT" property="createdBy" />
      <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
    <sql id="Base_Column_List">
        id, business_id, item_count, push_code, batch_count, batch, batch_total_count, adjust_time,
        adjust_code, `type`, `result`, receive_msg, price_update_result, price_update_msg, original_batch,
        push_again, result_time, `status`, gmt_create, gmt_update, extend, version, created_by,
        updated_by
    </sql>

  <select id="selectByPushCodeAndBusinessId" parameterType="com.cowell.pricecenter.entity.PricePushTaskExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_push_task
      <if test="_parameter != null">
          <include refid="Example_Where_Clause" />
      </if>
  </select>

    <insert id="insertOrUpdate" parameterType="com.cowell.pricecenter.entity.PricePushTask">
        insert into price_push_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="businessId != null">
                business_id,
            </if>
            <if test="itemCount != null">
                item_count,
            </if>
            <if test="pushCode != null">
                push_code,
            </if>
            <if test="batchCount != null">
                batch_count,
            </if>
            <if test="batch != null">
                batch,
            </if>
            <if test="batchTotalCount != null">
                batch_total_count,
            </if>
            <if test="adjustTime != null">
                adjust_time,
            </if>
            <if test="adjustCode != null">
                adjust_code,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="result != null">
                `result`,
            </if>
            <if test="receiveMsg != null">
                receive_msg,
            </if>
            <if test="priceUpdateResult != null">
                price_update_result,
            </if>
            <if test="priceUpdateMsg != null">
                price_update_msg,
            </if>
            <if test="originalBatch != null">
                original_batch,
            </if>
            <if test="pushAgain != null">
                push_again,
            </if>
            <if test="resultTime != null">
                result_time,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtUpdate != null">
                gmt_update,
            </if>
            <if test="extend != null">
                extend,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="businessId != null">
                #{businessId,jdbcType=BIGINT},
            </if>
            <if test="itemCount != null">
                #{itemCount,jdbcType=INTEGER},
            </if>
            <if test="pushCode != null">
                #{pushCode,jdbcType=VARCHAR},
            </if>
            <if test="batchCount != null">
                #{batchCount,jdbcType=INTEGER},
            </if>
            <if test="batch != null">
                #{batch,jdbcType=INTEGER},
            </if>
            <if test="batchTotalCount != null">
                #{batchTotalCount,jdbcType=INTEGER},
            </if>
            <if test="adjustTime != null">
                #{adjustTime,jdbcType=TIMESTAMP},
            </if>
            <if test="adjustCode != null">
                #{adjustCode,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="result != null">
                #{result,jdbcType=TINYINT},
            </if>
            <if test="receiveMsg != null">
                #{receiveMsg,jdbcType=VARCHAR},
            </if>
            <if test="priceUpdateResult != null">
                #{priceUpdateResult,jdbcType=INTEGER},
            </if>
            <if test="priceUpdateMsg != null">
                #{priceUpdateMsg,jdbcType=VARCHAR},
            </if>
            <if test="originalBatch != null">
                #{originalBatch,jdbcType=INTEGER},
            </if>
            <if test="pushAgain != null">
                #{pushAgain,jdbcType=TINYINT},
            </if>
            <if test="resultTime != null">
                #{resultTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtUpdate != null">
                #{gmtUpdate,jdbcType=TIMESTAMP},
            </if>
            <if test="extend != null">
                #{extend,jdbcType=VARCHAR},
            </if>
            <if test="version != null">
                #{version,jdbcType=INTEGER},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=BIGINT},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=BIGINT},
            </if>
        </trim>
            ON DUPLICATE KEY UPDATE
                item_count = #{itemCount,jdbcType=INTEGER},
                batch_count = #{batchCount,jdbcType=INTEGER},
                batch = #{batch,jdbcType=INTEGER},
                batch_total_count = #{batchTotalCount,jdbcType=INTEGER}
    </insert>

    <select id="countOfResult" resultType="com.cowell.pricecenter.entity.PricePushTaskResultCount">
        select result as resultCode, count(result) as itemCount
        from price_push_task
        where adjust_code = #{adjustCode} and status = 0
        group by result
    </select>

    <select id="countOfUpdateResult" resultType="com.cowell.pricecenter.entity.PricePushTaskResultCount">
        select price_update_result as resultCode, count(price_update_result) as itemCount
        from price_push_task
        where adjust_code = #{adjustCode} and status = 0
        group by price_update_result
    </select>

    <select id="countOfEffectResult" resultType="com.cowell.pricecenter.entity.PricePushTaskResultCount">
        select price_effect_result as resultCode, count(price_effect_result) as itemCount
        from price_push_task
        where adjust_code = #{adjustCode} and status = 0
        group by price_effect_result
    </select>

    <update id="updatePricePushTaskVersion">
    	update price_push_task set version = version+1 where id=#{id} and version=#{version}
    </update>

    <select id="getTaskStatistics" resultType="com.cowell.pricecenter.service.dto.PricePushHistoryStatisticsDTO">
        SELECT
        SUM(item_count) as total,
        SUM(CASE WHEN result_time IS NOT NULL THEN batch_count ELSE 0 END) as progressCount
        FROM price_push_task
        WHERE adjust_code = #{adjustCode}
        AND status = 0
        AND push_again = 0
        AND type = 1
    </select>
</mapper>
