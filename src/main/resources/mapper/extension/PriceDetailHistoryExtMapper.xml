<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceDetailHistoryExtMapper">
    <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.PriceDetailHistory">
        insert into price_detail_history (business_id, business_name, store_id,
            store_name, goods_no, sku_id, adjust_code,
            price_type_code, price_type_name, price,
            updated_by, updated_by_name, `status`,
            gmt_create, gmt_update, extend,
            version, channel_id, dsx_price,
            next_price_dxs_adjust_code, next_price_dxs_date,extend1
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT},
                #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.skuId,jdbcType=BIGINT},#{item.adjustCode,jdbcType=VARCHAR},
                #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL},
                #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedByName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
                #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR},
                #{item.version,jdbcType=INTEGER}, #{item.channelId,jdbcType=INTEGER}, #{item.dsxPrice,jdbcType=DECIMAL},
                #{item.nextPriceDxsAdjustCode,jdbcType=VARCHAR}, #{item.nextPriceDxsDate,jdbcType=TIMESTAMP}, #{item.extend1,jdbcType=VARCHAR}
            </trim>
        </foreach>
    </insert>

    <select id="findPreviousPriceByStoreIdAndGoodsNoAndPriceType" resultType="com.cowell.pricecenter.entity.PriceDetailHistory">
        select store_id as storeId,goods_no as goodsNo,adjust_code as adjustCode,price_type_code as priceTypeCode,price,extend1 from price_detail_history 
        where store_id=#{storeId} and goods_no=#{goodsNo} and 
        price_type_code=#{priceTypeCode} and channel_id=#{channelId} and gmt_create &lt; #{lastTime} order by gmt_create desc limit 1
    </select>
</mapper>
