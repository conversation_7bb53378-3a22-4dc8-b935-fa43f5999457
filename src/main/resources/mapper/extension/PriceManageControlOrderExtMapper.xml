<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceManageControlOrderExtMapper">

    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceManageControlOrder">
        <!--@mbg.generated-->
        <!--@Table price_manage_control_order-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="control_order_id" jdbcType="VARCHAR" property="controlOrderId" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="order_level" jdbcType="TINYINT" property="orderLevel" />
        <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
        <result column="control_reason" jdbcType="VARCHAR" property="controlReason" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
        <result column="extend" jdbcType="VARCHAR" property="extend" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="created_by" jdbcType="BIGINT" property="createdBy" />
        <result column="created_by_name" jdbcType="VARCHAR" property="createdByName" />
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
        <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
        <result column="audit_by" jdbcType="BIGINT" property="auditBy" />
        <result column="audit_by_name" jdbcType="VARCHAR" property="auditByName" />
        <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
        <result column="audit_reason" jdbcType="VARCHAR" property="auditReason" />
        <result column="channel" jdbcType="VARCHAR" property="channel" />
        <result column="price_type" jdbcType="VARCHAR" property="priceType" />
        <result column="control_type" jdbcType="INTEGER" property="controlType" />
        <result column="effect_time" jdbcType="TIMESTAMP" property="effectTime" />
        <result column="scheduled_time" jdbcType="TIMESTAMP" property="scheduledTime" />
        <result column="exec_status" jdbcType="INTEGER" property="execStatus" />
        <result column="effect_status" jdbcType="VARCHAR" property="effectStatus" />
        <result column="control_order_type" jdbcType="INTEGER" property="controlOrderType" />
        <result column="control_no_exec_reason" jdbcType="VARCHAR" property="controlNoExecReason" />
        <result column="control_order_name" jdbcType="VARCHAR" property="controlOrderName" />
    </resultMap>

    <resultMap id="UnControlResultMap" type="com.cowell.pricecenter.service.dto.response.controlOrder.UnControlOrderVO" >
        <result column="id" property="id" jdbcType="BIGINT" />
        <result column="un_control_order_id" property="unControlOrderId" jdbcType="VARCHAR" />
        <result column="control_order_id" property="controlOrderId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, control_order_id, org_id, org_name, order_level, audit_status, control_reason,
        `status`, gmt_create, gmt_update, extend, version, created_by, created_by_name, updated_by,
        updated_by_name, audit_by, audit_by_name, audit_date, audit_reason, channel, price_type,
        control_type, effect_time, scheduled_time, exec_status, effect_status, control_order_type,
        control_no_exec_reason, control_order_name
    </sql>

    <sql id="Base_Column_Alias_List">
        a.id, a.control_order_id, a.org_id, a.org_name, a.order_level, a.audit_status, a.control_reason,
        a.`status`, a.gmt_create, a.gmt_update, a.extend, a.version, a.created_by, a.created_by_name, a.updated_by,
        a.updated_by_name, a.audit_by, a.audit_by_name, a.audit_date, a.audit_reason, a.channel, a.price_type,
        a.control_type, a.effect_time, a.scheduled_time, a.exec_status, a.effect_status, a.control_order_type,
        a.control_no_exec_reason, a.control_order_name
    </sql>

    <sql id="searchControlOrderFromWhere">
        from price_manage_control_order
        where status = 0
        and order_type = 1
        <if test="controlOrderId != null and controlOrderId != ''">
            and control_order_id = #{controlOrderId}
        </if>
        <if test="controlOrderName != null and controlOrderName != ''">
            and control_order_name like concat('%', #{controlOrderName},'%')
        </if>
        <if test="controlOrderType != null">
            and control_order_type = #{controlOrderType}
        </if>
        <if test="auditStatus != null">
            and audit_status = #{auditStatus}
        </if>
        <if test="createdByName != null and createdByName != ''">
            and created_by_name like concat('%', #{createdByName}, '%')
        </if>
        <if test="channelId != null">
            and FIND_IN_SET(#{channelId}, channel)
        </if>
        <if test="effectStartTime != null and effectEndTime != null">
            and effect_time between #{effectStartTime} and #{effectEndTime}
        </if>
        <if test="startTime != null and endTime != null">
            and gmt_create between #{startTime} and #{endTime}
        </if>
        <if test="controlOrderIdList != null and controlOrderIdList.size > 0">
            and control_order_id in
            <foreach collection="controlOrderIdList" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="selectUnControlOrderCodeListWhere">
        where status = 0 and control_order_type = 2
        <if test="controlOrderCodeList != null and controlOrderCodeList.size > 0">
            and
            <foreach collection="controlOrderCodeList" item="item" index="index" separator="or" open="(" close=")">
                JSON_EXTRACT(extend, "$.controlOrderCode") like concat('%', #{item},'%')
            </foreach>
        </if>
    </sql>

    <select id="selectUnControlOrderListByCode" resultMap="UnControlResultMap">
        select
         id, control_order_id as un_control_order_id, json_extract (extend, '$.controlOrderCode') AS control_order_id
        from price_manage_control_order
        <include refid="selectUnControlOrderCodeListWhere"></include>
    </select>

    <select id="selectControlOrderNoListRelationUnControl" resultType="java.lang.String">
        select
         json_extract (extend, '$.controlOrderCode')
        from price_manage_control_order
        <include refid="selectUnControlOrderCodeListWhere"></include>
    </select>

    <select id="selectUmControlOrderListByControlOrderCodeKey" resultMap="UnControlResultMap">
        select
            control_order_id as un_control_order_id, json_extract (extend, '$.controlOrderCode') as control_order_id
        from price_manage_control_order
        where status = 0 and control_order_type = 2 and extend like "%controlOrderCode%"
        and gmt_create between #{startTime} and #{endTime}
    </select>

    <select id="selectNotControlOrderCodeList" resultType="java.lang.String">
        select
            control_order_id
        from price_manage_control_order
        where 1=1
        <if test="controlOrderCodeList != null and controlOrderCodeList.size > 0">
            and
            <foreach collection="controlOrderCodeList" item="item" index="index" separator="or" open="(" close=")">
                JSON_EXTRACT(extend, "$.controlOrderCode") = #{item}
            </foreach>
        </if>
    </select>

    <select id="searchControlOrderCount" resultType="java.lang.Long">
        select count(*)
        <include refid="searchControlOrderFromWhere"></include>
    </select>

    <select id="searchControlOrderList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        <include refid="searchControlOrderFromWhere"></include>
        order by gmt_create desc
        LIMIT #{offset},#{pageSize}
    </select>

    <select id="selectByControlOrderCode" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
        from price_manage_control_order
        where control_order_id = #{controlOrderCode,jdbcType=VARCHAR} limit 1
    </select>

    <select id="selectAuditStatusByControlOrderCode" resultType="java.lang.Byte">
        select
            audit_status
        from price_manage_control_order
        where id = #{id,jdbcType=BIGINT} limit 1
    </select>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrder" useGeneratedKeys="true">
        insert into price_manage_control_order ( control_order_id, org_id, org_name, order_level, audit_status, control_reason,
        `status`, extend, version, created_by, created_by_name, updated_by,
        updated_by_name, audit_by, audit_by_name, audit_date, audit_reason, channel, price_type,
        control_type, effect_time, scheduled_time, exec_status, effect_status, control_order_type,
        control_no_exec_reason, control_order_name
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.controlOrderId,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT}, #{item.orgName,jdbcType=VARCHAR},
                #{item.orderLevel,jdbcType=TINYINT}, #{item.auditStatus,jdbcType=TINYINT}, #{item.controlReason,jdbcType=VARCHAR},
                #{item.status,jdbcType=TINYINT},
                #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT},
                #{item.createdByName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedByName,jdbcType=VARCHAR},
                #{item.auditBy,jdbcType=BIGINT}, #{item.auditByName,jdbcType=VARCHAR}, #{item.auditDate,jdbcType=TIMESTAMP},
                #{item.auditReason,jdbcType=VARCHAR}, #{item.channel,jdbcType=VARCHAR}, #{item.priceType,jdbcType=VARCHAR},
                #{item.controlType,jdbcType=INTEGER}, #{item.effectTime,jdbcType=TIMESTAMP}, #{item.scheduledTime,jdbcType=TIMESTAMP},
                #{item.execStatus,jdbcType=INTEGER}, #{item.effectStatus,jdbcType=VARCHAR}, #{item.controlOrderType,jdbcType=INTEGER},
                #{item.controlNoExecReason,jdbcType=VARCHAR}, #{item.controlOrderName,jdbcType=VARCHAR}
            </trim>
        </foreach>

    </insert>

    <select id="getMaxOrderCode" resultType="java.lang.String" parameterType="java.lang.Byte">
        select max(control_order_id) maxCode from price_manage_control_order where order_type = #{orderType}
    </select>

    <select id="searchWarnRuleList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_Alias_List"></include>
        <include refid="WarnRulesListFromWhere"></include>
        <if test="orgIds != null and orgIds.size > 0">
            group by  <include refid="Base_Column_Alias_List"></include>
        </if>
        order by a.gmt_create desc
        limit #{offset}, #{size}
    </select>

    <select id="countWarnRules" resultType="java.lang.Long">
        select count(distinct a.id)
        <include refid="WarnRulesListFromWhere"></include>
    </select>

    <sql id="WarnRulesListFromWhere">
        from price_manage_control_order a
        <if test="orgIds != null and orgIds.size > 0">
            join price_manage_control_org_detail b on a.control_order_id = b.control_order_code
        </if>
        where a.status = 0 and a.order_type = #{orderType}
        <if test="ruleCode != null and ruleCode != ''">
            and a.control_order_id = #{ruleCode}
        </if>
        <if test="ruleName != null and ruleName != ''">
            and a.control_order_name like concat('%', #{ruleName}, '%')
        </if>
        <if test="goodsNo != null and goodsNo != ''">
            and a.extend like concat('%', #{goodsNo}, '%')
        </if>
        <if test="goodsChooseWay != null">
            and a.control_type = #{goodsChooseWay}
        </if>
        <if test="warnType != null">
            and a.control_order_type = #{warnType}
        </if>
        <if test="createdByName != null and createdByName != ''">
            and a.created_by_name = #{createdByName}
        </if>
        <if test="orgIds != null and orgIds.size > 0">
            and b.org_id in
            <foreach collection="orgIds" item="orgId" index="index" open="(" close=")" separator=",">
                #{orgId}
            </foreach>
        </if>
        <if test="channelId != null">
            and a.channel = #{channelId}
        </if>
    </sql>
    <select id="selectWarnRulesOrgIds"  resultType="java.lang.Long">
        select pmcod.org_id from price_manage_control_order pmco join price_manage_control_org_detail pmcod on pmco.control_order_id = pmcod.control_order_code
        where pmco.control_order_type=2 group by pmcod.org_id
    </select>
</mapper>
