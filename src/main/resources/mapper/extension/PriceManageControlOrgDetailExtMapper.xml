<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceManageControlOrgDetailExtMapper">

    <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceManageControlOrgDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="control_order_code" jdbcType="VARCHAR" property="controlOrderCode" />
        <result column="org_id" jdbcType="BIGINT" property="orgId" />
        <result column="org_level" jdbcType="INTEGER" property="orgLevel" />
        <result column="org_name" jdbcType="VARCHAR" property="orgName" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    </resultMap>

    <sql id="Base_Column_List">
        id, control_order_code, org_id, org_level, org_name, gmt_create, gmt_update
    </sql>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrgDetail" useGeneratedKeys="true">
        insert into price_manage_control_org_detail (control_order_code, org_id, org_level,
        org_name, gmt_create, gmt_update, order_type
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.controlOrderCode,jdbcType=VARCHAR}, #{item.orgId,jdbcType=BIGINT}, #{item.orgLevel,jdbcType=INTEGER},
                #{item.orgName,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP},
                #{item.orderType,jdbcType=TINYINT}
            </trim>
        </foreach>

    </insert>

    <select id="selectOrgByControlOrderCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from price_manage_control_org_detail
        where control_order_code = #{controlOrderCode,jdbcType=VARCHAR}
    </select>

    <select id="selectOrgByControlOrderCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from price_manage_control_org_detail
        where 1=1
        <if test="controlOrderCodeList != null and controlOrderCodeList.size > 0">
            and control_order_code in
            <foreach collection="controlOrderCodeList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
