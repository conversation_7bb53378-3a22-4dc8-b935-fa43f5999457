<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceBusinessDetailExtMapper">
   
   <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail" useGeneratedKeys="true">
      insert into price_business_detail (id, goods_no, channel_id,
      business_id, price, price_type_code,price_type_id, price_type_name, `status`,
      extend, version, gmt_create,created_by, created_by_name, gmt_update,
      updated_by, updated_by_name, `comment`)
      values
      <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
               #{item.id,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER},
		       #{item.businessId,jdbcType=BIGINT}, #{item.price,jdbcType=DECIMAL}, #{item.priceTypeCode,jdbcType=VARCHAR},
		       #{item.priceTypeId,jdbcType=BIGINT}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
		       #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER}, #{item.gmtCreate,jdbcType=TIMESTAMP},
		       #{item.createdBy,jdbcType=BIGINT}, #{item.createdByName,jdbcType=VARCHAR}, #{item.gmtUpdate,jdbcType=TIMESTAMP},
		       #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedByName,jdbcType=VARCHAR}, #{item.comment,jdbcType=VARCHAR}
            </trim>
      </foreach>
    </insert>
    
  <update id="updateByBusinessGoodsSelective" parameterType="com.cowell.pricecenter.entity.PriceBusinessDetail">
    update price_business_detail
    <set>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    <where>
           goods_no = #{goodsNo,jdbcType=VARCHAR} and
           business_id = #{businessId,jdbcType=BIGINT} and
           price_type_code = #{priceTypeCode,jdbcType=VARCHAR}
    </where>
  </update>
  
  <delete id="batchDelete">
    delete from price_business_detail
        <where>
            <foreach collection="list" item="item" open="( " separator=") or (" close=" )">
                goods_no = #{item.goodsNo} and
                business_id = #{item.businessId}
            </foreach>
        </where>
  </delete>
  
  <delete id="deletePriceBusinessDetail">
    delete from price_business_detail
        <where>
            business_id = #{businessId} and goods_no = #{goodsNo}
        </where>
  </delete>
</mapper>
