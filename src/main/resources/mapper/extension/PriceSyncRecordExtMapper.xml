<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cowell.pricecenter.mapper.extension.PriceSyncRecordExtMapper" >
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceSyncRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="business_id" property="businessId" jdbcType="BIGINT" />
    <result column="store_id" property="storeId" jdbcType="BIGINT" />
    <result column="store_name" property="storeName" jdbcType="VARCHAR" />
    <result column="goods_no" property="goodsNo" jdbcType="VARCHAR" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="BIGINT" />
    <result column="price_type" property="priceType" jdbcType="INTEGER" />
    <result column="sync_result" property="syncResult" jdbcType="TINYINT" />
    <result column="sync_warn_desc" property="syncWarnDesc" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
    <result column="gmt_update" property="gmtUpdate" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="extend" property="extend" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="channel" property="channel" jdbcType="INTEGER" />
  </resultMap>
    <sql id="Example_Where_Clause" >
        <where >
            <foreach collection="oredCriteria" item="criteria" separator="or" >
                <if test="criteria.valid" >
                    <trim prefix="(" suffix=")" prefixOverrides="and" >
                        <foreach collection="criteria.criteria" item="criterion" >
                            <choose >
                                <when test="criterion.noValue" >
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue" >
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue" >
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue" >
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause" >
        <where >
            <foreach collection="example.oredCriteria" item="criteria" separator="or" >
                <if test="criteria.valid" >
                    <trim prefix="(" suffix=")" prefixOverrides="and" >
                        <foreach collection="criteria.criteria" item="criterion" >
                            <choose >
                                <when test="criterion.noValue" >
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue" >
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue" >
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue" >
                                    and ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List" >
        id, business_id, store_id, store_name, goods_no, sku_name, price, price_type, sync_result,
        sync_warn_desc, created_by, gmt_create, updated_by, gmt_update, status, extend, version,
        channel
    </sql>

  <insert id="batchInsert" parameterType="com.cowell.pricecenter.entity.PriceSyncRecord" >
    insert into price_sync_record (id, business_id, store_id,
      store_name, goods_no, sku_name,
      price, price_type, sync_result,
      sync_warn_desc, created_by, gmt_create,
      updated_by, gmt_update, status,
      extend, version,channel)
    values
      <foreach collection="list" item="item" index="index" separator=",">
        <trim prefix="(" suffix=")" suffixOverrides=",">
              #{item.id,jdbcType=BIGINT}, #{item.businessId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
              #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.skuName,jdbcType=VARCHAR},
              #{item.price,jdbcType=BIGINT}, #{item.priceType,jdbcType=TINYINT}, #{item.syncResult,jdbcType=TINYINT},
              #{item.syncWarnDesc,jdbcType=VARCHAR}, #{item.createdBy,jdbcType=VARCHAR}, #{item.gmtCreate,jdbcType=TIMESTAMP},
              #{item.updatedBy,jdbcType=VARCHAR}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.status,jdbcType=TINYINT},
              #{item.extend,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},#{item.channel,jdbcType=INTEGER}
        </trim>
      </foreach>
  </insert>

</mapper>
