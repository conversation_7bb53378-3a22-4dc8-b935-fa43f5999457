<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PricePushTaskMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PricePushTask">
    <!--@mbg.generated-->
    <!--@Table price_push_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="item_count" jdbcType="INTEGER" property="itemCount" />
    <result column="push_code" jdbcType="VARCHAR" property="pushCode" />
    <result column="batch_count" jdbcType="INTEGER" property="batchCount" />
    <result column="batch" jdbcType="INTEGER" property="batch" />
    <result column="batch_total_count" jdbcType="INTEGER" property="batchTotalCount" />
    <result column="adjust_time" jdbcType="TIMESTAMP" property="adjustTime" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="result" jdbcType="TINYINT" property="result" />
    <result column="receive_msg" jdbcType="VARCHAR" property="receiveMsg" />
    <result column="price_update_result" jdbcType="INTEGER" property="priceUpdateResult" />
    <result column="price_update_msg" jdbcType="VARCHAR" property="priceUpdateMsg" />
    <result column="price_effect_result" jdbcType="INTEGER" property="priceEffectResult" />
    <result column="price_effect_msg" jdbcType="VARCHAR" property="priceEffectMsg" />
    <result column="original_batch" jdbcType="INTEGER" property="originalBatch" />
    <result column="push_again" jdbcType="TINYINT" property="pushAgain" />
    <result column="result_time" jdbcType="TIMESTAMP" property="resultTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, item_count, push_code, batch_count, batch, batch_total_count, adjust_time,
    adjust_code, `type`, `result`, receive_msg, price_update_result, price_update_msg,
    price_effect_result, price_effect_msg, original_batch, push_again, result_time, `status`,
    gmt_create, gmt_update, extend, version, created_by, updated_by
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PricePushTaskExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_push_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
          limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
          limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_push_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from price_push_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PricePushTaskExample">
    <!--@mbg.generated-->
    delete from price_push_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushTask" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_task (business_id, item_count, push_code,
      batch_count, batch, batch_total_count,
      adjust_time, adjust_code, `type`,
      `result`, receive_msg, price_update_result,
      price_update_msg, price_effect_result, price_effect_msg,
      original_batch, push_again, result_time,
      `status`, gmt_create, gmt_update,
      extend, version, created_by,
      updated_by)
    values (#{businessId,jdbcType=BIGINT}, #{itemCount,jdbcType=INTEGER}, #{pushCode,jdbcType=VARCHAR},
      #{batchCount,jdbcType=INTEGER}, #{batch,jdbcType=INTEGER}, #{batchTotalCount,jdbcType=INTEGER},
      #{adjustTime,jdbcType=TIMESTAMP}, #{adjustCode,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER},
      #{result,jdbcType=TINYINT}, #{receiveMsg,jdbcType=VARCHAR}, #{priceUpdateResult,jdbcType=INTEGER},
      #{priceUpdateMsg,jdbcType=VARCHAR}, #{priceEffectResult,jdbcType=INTEGER}, #{priceEffectMsg,jdbcType=VARCHAR},
      #{originalBatch,jdbcType=INTEGER}, #{pushAgain,jdbcType=TINYINT}, #{resultTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP},
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT},
      #{updatedBy,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushTask" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="itemCount != null">
        item_count,
      </if>
      <if test="pushCode != null">
        push_code,
      </if>
      <if test="batchCount != null">
        batch_count,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="batchTotalCount != null">
        batch_total_count,
      </if>
      <if test="adjustTime != null">
        adjust_time,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="result != null">
        `result`,
      </if>
      <if test="receiveMsg != null">
        receive_msg,
      </if>
      <if test="priceUpdateResult != null">
        price_update_result,
      </if>
      <if test="priceUpdateMsg != null">
        price_update_msg,
      </if>
      <if test="priceEffectResult != null">
        price_effect_result,
      </if>
      <if test="priceEffectMsg != null">
        price_effect_msg,
      </if>
      <if test="originalBatch != null">
        original_batch,
      </if>
      <if test="pushAgain != null">
        push_again,
      </if>
      <if test="resultTime != null">
        result_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="pushCode != null">
        #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batchCount != null">
        #{batchCount,jdbcType=INTEGER},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=INTEGER},
      </if>
      <if test="batchTotalCount != null">
        #{batchTotalCount,jdbcType=INTEGER},
      </if>
      <if test="adjustTime != null">
        #{adjustTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        #{result,jdbcType=TINYINT},
      </if>
      <if test="receiveMsg != null">
        #{receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="priceUpdateResult != null">
        #{priceUpdateResult,jdbcType=INTEGER},
      </if>
      <if test="priceUpdateMsg != null">
        #{priceUpdateMsg,jdbcType=VARCHAR},
      </if>
      <if test="priceEffectResult != null">
        #{priceEffectResult,jdbcType=INTEGER},
      </if>
      <if test="priceEffectMsg != null">
        #{priceEffectMsg,jdbcType=VARCHAR},
      </if>
      <if test="originalBatch != null">
        #{originalBatch,jdbcType=INTEGER},
      </if>
      <if test="pushAgain != null">
        #{pushAgain,jdbcType=TINYINT},
      </if>
      <if test="resultTime != null">
        #{resultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PricePushTaskExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_push_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_push_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.itemCount != null">
        item_count = #{record.itemCount,jdbcType=INTEGER},
      </if>
      <if test="record.pushCode != null">
        push_code = #{record.pushCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batchCount != null">
        batch_count = #{record.batchCount,jdbcType=INTEGER},
      </if>
      <if test="record.batch != null">
        batch = #{record.batch,jdbcType=INTEGER},
      </if>
      <if test="record.batchTotalCount != null">
        batch_total_count = #{record.batchTotalCount,jdbcType=INTEGER},
      </if>
      <if test="record.adjustTime != null">
        adjust_time = #{record.adjustTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.result != null">
        `result` = #{record.result,jdbcType=TINYINT},
      </if>
      <if test="record.receiveMsg != null">
        receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.priceUpdateResult != null">
        price_update_result = #{record.priceUpdateResult,jdbcType=INTEGER},
      </if>
      <if test="record.priceUpdateMsg != null">
        price_update_msg = #{record.priceUpdateMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.priceEffectResult != null">
        price_effect_result = #{record.priceEffectResult,jdbcType=INTEGER},
      </if>
      <if test="record.priceEffectMsg != null">
        price_effect_msg = #{record.priceEffectMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.originalBatch != null">
        original_batch = #{record.originalBatch,jdbcType=INTEGER},
      </if>
      <if test="record.pushAgain != null">
        push_again = #{record.pushAgain,jdbcType=TINYINT},
      </if>
      <if test="record.resultTime != null">
        result_time = #{record.resultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_push_task
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      item_count = #{record.itemCount,jdbcType=INTEGER},
      push_code = #{record.pushCode,jdbcType=VARCHAR},
      batch_count = #{record.batchCount,jdbcType=INTEGER},
      batch = #{record.batch,jdbcType=INTEGER},
      batch_total_count = #{record.batchTotalCount,jdbcType=INTEGER},
      adjust_time = #{record.adjustTime,jdbcType=TIMESTAMP},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=INTEGER},
      `result` = #{record.result,jdbcType=TINYINT},
      receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
      price_update_result = #{record.priceUpdateResult,jdbcType=INTEGER},
      price_update_msg = #{record.priceUpdateMsg,jdbcType=VARCHAR},
      price_effect_result = #{record.priceEffectResult,jdbcType=INTEGER},
      price_effect_msg = #{record.priceEffectMsg,jdbcType=VARCHAR},
      original_batch = #{record.originalBatch,jdbcType=INTEGER},
      push_again = #{record.pushAgain,jdbcType=TINYINT},
      result_time = #{record.resultTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PricePushTask">
    <!--@mbg.generated-->
    update price_push_task
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        item_count = #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="pushCode != null">
        push_code = #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batchCount != null">
        batch_count = #{batchCount,jdbcType=INTEGER},
      </if>
      <if test="batch != null">
        batch = #{batch,jdbcType=INTEGER},
      </if>
      <if test="batchTotalCount != null">
        batch_total_count = #{batchTotalCount,jdbcType=INTEGER},
      </if>
      <if test="adjustTime != null">
        adjust_time = #{adjustTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        `result` = #{result,jdbcType=TINYINT},
      </if>
      <if test="receiveMsg != null">
        receive_msg = #{receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="priceUpdateResult != null">
        price_update_result = #{priceUpdateResult,jdbcType=INTEGER},
      </if>
      <if test="priceUpdateMsg != null">
        price_update_msg = #{priceUpdateMsg,jdbcType=VARCHAR},
      </if>
      <if test="priceEffectResult != null">
        price_effect_result = #{priceEffectResult,jdbcType=INTEGER},
      </if>
      <if test="priceEffectMsg != null">
        price_effect_msg = #{priceEffectMsg,jdbcType=VARCHAR},
      </if>
      <if test="originalBatch != null">
        original_batch = #{originalBatch,jdbcType=INTEGER},
      </if>
      <if test="pushAgain != null">
        push_again = #{pushAgain,jdbcType=TINYINT},
      </if>
      <if test="resultTime != null">
        result_time = #{resultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PricePushTask">
    <!--@mbg.generated-->
    update price_push_task
    set business_id = #{businessId,jdbcType=BIGINT},
      item_count = #{itemCount,jdbcType=INTEGER},
      push_code = #{pushCode,jdbcType=VARCHAR},
      batch_count = #{batchCount,jdbcType=INTEGER},
      batch = #{batch,jdbcType=INTEGER},
      batch_total_count = #{batchTotalCount,jdbcType=INTEGER},
      adjust_time = #{adjustTime,jdbcType=TIMESTAMP},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      `result` = #{result,jdbcType=TINYINT},
      receive_msg = #{receiveMsg,jdbcType=VARCHAR},
      price_update_result = #{priceUpdateResult,jdbcType=INTEGER},
      price_update_msg = #{priceUpdateMsg,jdbcType=VARCHAR},
      price_effect_result = #{priceEffectResult,jdbcType=INTEGER},
      price_effect_msg = #{priceEffectMsg,jdbcType=VARCHAR},
      original_batch = #{originalBatch,jdbcType=INTEGER},
      push_again = #{pushAgain,jdbcType=TINYINT},
      result_time = #{resultTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_push_task
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="item_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.itemCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="push_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.pushCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="batch_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.batchCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="batch = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.batch,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="batch_total_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.batchTotalCount,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="adjust_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.adjustTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="adjust_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.adjustCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.type,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`result` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.result,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="receive_msg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.receiveMsg,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_update_result = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceUpdateResult,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="price_update_msg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceUpdateMsg,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="original_batch = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.originalBatch,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="push_again = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.pushAgain,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="result_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.resultTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="extend = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.extend,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_push_task
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="item_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.itemCount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.itemCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="push_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.pushCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="batch_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.batchCount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.batchCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="batch = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.batch != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.batch,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="batch_total_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.batchTotalCount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.batchTotalCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="adjust_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.adjustTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.adjustTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="adjust_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.adjustCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.adjustCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.type,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`result` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.result != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.result,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="receive_msg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveMsg != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.receiveMsg,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_update_result = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceUpdateResult != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceUpdateResult,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_update_msg = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceUpdateMsg != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceUpdateMsg,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="original_batch = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.originalBatch != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.originalBatch,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="push_again = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushAgain != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.pushAgain,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="result_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.resultTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.resultTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtCreate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtUpdate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="extend = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extend != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.extend,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_task
    (business_id, item_count, push_code, batch_count, batch, batch_total_count, adjust_time,
      adjust_code, `type`, `result`, receive_msg, price_update_result, price_update_msg,
      original_batch, push_again, result_time, `status`, gmt_create, gmt_update, extend,
      version, created_by, updated_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessId,jdbcType=BIGINT}, #{item.itemCount,jdbcType=INTEGER}, #{item.pushCode,jdbcType=VARCHAR},
        #{item.batchCount,jdbcType=INTEGER}, #{item.batch,jdbcType=INTEGER}, #{item.batchTotalCount,jdbcType=INTEGER},
        #{item.adjustTime,jdbcType=TIMESTAMP}, #{item.adjustCode,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER},
        #{item.result,jdbcType=TINYINT}, #{item.receiveMsg,jdbcType=VARCHAR}, #{item.priceUpdateResult,jdbcType=INTEGER},
        #{item.priceUpdateMsg,jdbcType=VARCHAR}, #{item.originalBatch,jdbcType=INTEGER},
        #{item.pushAgain,jdbcType=TINYINT}, #{item.resultTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=TINYINT},
        #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT}, #{item.updatedBy,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushTask" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      business_id,
      item_count,
      push_code,
      batch_count,
      batch,
      batch_total_count,
      adjust_time,
      adjust_code,
      `type`,
      `result`,
      receive_msg,
      price_update_result,
      price_update_msg,
      original_batch,
      push_again,
      result_time,
      `status`,
      gmt_create,
      gmt_update,
      extend,
      version,
      created_by,
      updated_by,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{businessId,jdbcType=BIGINT},
      #{itemCount,jdbcType=INTEGER},
      #{pushCode,jdbcType=VARCHAR},
      #{batchCount,jdbcType=INTEGER},
      #{batch,jdbcType=INTEGER},
      #{batchTotalCount,jdbcType=INTEGER},
      #{adjustTime,jdbcType=TIMESTAMP},
      #{adjustCode,jdbcType=VARCHAR},
      #{type,jdbcType=INTEGER},
      #{result,jdbcType=TINYINT},
      #{receiveMsg,jdbcType=VARCHAR},
      #{priceUpdateResult,jdbcType=INTEGER},
      #{priceUpdateMsg,jdbcType=VARCHAR},
      #{originalBatch,jdbcType=INTEGER},
      #{pushAgain,jdbcType=TINYINT},
      #{resultTime,jdbcType=TIMESTAMP},
      #{status,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP},
      #{extend,jdbcType=VARCHAR},
      #{version,jdbcType=INTEGER},
      #{createdBy,jdbcType=BIGINT},
      #{updatedBy,jdbcType=BIGINT},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      business_id = #{businessId,jdbcType=BIGINT},
      item_count = #{itemCount,jdbcType=INTEGER},
      push_code = #{pushCode,jdbcType=VARCHAR},
      batch_count = #{batchCount,jdbcType=INTEGER},
      batch = #{batch,jdbcType=INTEGER},
      batch_total_count = #{batchTotalCount,jdbcType=INTEGER},
      adjust_time = #{adjustTime,jdbcType=TIMESTAMP},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      `result` = #{result,jdbcType=TINYINT},
      receive_msg = #{receiveMsg,jdbcType=VARCHAR},
      price_update_result = #{priceUpdateResult,jdbcType=INTEGER},
      price_update_msg = #{priceUpdateMsg,jdbcType=VARCHAR},
      original_batch = #{originalBatch,jdbcType=INTEGER},
      push_again = #{pushAgain,jdbcType=TINYINT},
      result_time = #{resultTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushTask" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="itemCount != null">
        item_count,
      </if>
      <if test="pushCode != null">
        push_code,
      </if>
      <if test="batchCount != null">
        batch_count,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="batchTotalCount != null">
        batch_total_count,
      </if>
      <if test="adjustTime != null">
        adjust_time,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="result != null">
        `result`,
      </if>
      <if test="receiveMsg != null">
        receive_msg,
      </if>
      <if test="priceUpdateResult != null">
        price_update_result,
      </if>
      <if test="priceUpdateMsg != null">
        price_update_msg,
      </if>
      <if test="originalBatch != null">
        original_batch,
      </if>
      <if test="pushAgain != null">
        push_again,
      </if>
      <if test="resultTime != null">
        result_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="pushCode != null">
        #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batchCount != null">
        #{batchCount,jdbcType=INTEGER},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=INTEGER},
      </if>
      <if test="batchTotalCount != null">
        #{batchTotalCount,jdbcType=INTEGER},
      </if>
      <if test="adjustTime != null">
        #{adjustTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        #{result,jdbcType=TINYINT},
      </if>
      <if test="receiveMsg != null">
        #{receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="priceUpdateResult != null">
        #{priceUpdateResult,jdbcType=INTEGER},
      </if>
      <if test="priceUpdateMsg != null">
        #{priceUpdateMsg,jdbcType=VARCHAR},
      </if>
      <if test="originalBatch != null">
        #{originalBatch,jdbcType=INTEGER},
      </if>
      <if test="pushAgain != null">
        #{pushAgain,jdbcType=TINYINT},
      </if>
      <if test="resultTime != null">
        #{resultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="itemCount != null">
        item_count = #{itemCount,jdbcType=INTEGER},
      </if>
      <if test="pushCode != null">
        push_code = #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batchCount != null">
        batch_count = #{batchCount,jdbcType=INTEGER},
      </if>
      <if test="batch != null">
        batch = #{batch,jdbcType=INTEGER},
      </if>
      <if test="batchTotalCount != null">
        batch_total_count = #{batchTotalCount,jdbcType=INTEGER},
      </if>
      <if test="adjustTime != null">
        adjust_time = #{adjustTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="result != null">
        `result` = #{result,jdbcType=TINYINT},
      </if>
      <if test="receiveMsg != null">
        receive_msg = #{receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="priceUpdateResult != null">
        price_update_result = #{priceUpdateResult,jdbcType=INTEGER},
      </if>
      <if test="priceUpdateMsg != null">
        price_update_msg = #{priceUpdateMsg,jdbcType=VARCHAR},
      </if>
      <if test="originalBatch != null">
        original_batch = #{originalBatch,jdbcType=INTEGER},
      </if>
      <if test="pushAgain != null">
        push_again = #{pushAgain,jdbcType=TINYINT},
      </if>
      <if test="resultTime != null">
        result_time = #{resultTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>
