<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PricePushHistoryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PricePushHistory">
    <!--@mbg.generated-->
    <!--@Table price_push_history-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="push_code" jdbcType="VARCHAR" property="pushCode" />
    <result column="batch" jdbcType="INTEGER" property="batch" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="price_flag" jdbcType="INTEGER" property="priceFlag" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="send_status" jdbcType="INTEGER" property="sendStatus" />
    <result column="send_msg" jdbcType="VARCHAR" property="sendMsg" />
    <result column="receive_status" jdbcType="INTEGER" property="receiveStatus" />
    <result column="receive_msg" jdbcType="VARCHAR" property="receiveMsg" />
    <result column="effect_status" jdbcType="INTEGER" property="effectStatus" />
    <result column="effect_msg" jdbcType="VARCHAR" property="effectMsg" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, business_id, store_id, goods_no, adjust_code, push_code, batch, price_type_code,
    price_type_name, channel_id, price, price_flag, `label`, `comment`, `status`, gmt_create,
    gmt_update, extend, version, created_by, updated_by, send_status, send_msg, receive_status,
    receive_msg, effect_status, effect_msg, sku_id
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PricePushHistoryExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_push_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_push_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from price_push_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PricePushHistoryExample">
    <!--@mbg.generated-->
    delete from price_push_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_history (business_id, store_id, goods_no,
      adjust_code, push_code, batch,
      price_type_code, price_type_name, channel_id,
      price, price_flag, `label`,
      `comment`, `status`, gmt_create,
      gmt_update, extend, version,
      created_by, updated_by, send_status,
      send_msg, receive_status, receive_msg,
      effect_status, effect_msg, sku_id)
    values (#{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, #{goodsNo,jdbcType=VARCHAR},
      #{adjustCode,jdbcType=VARCHAR}, #{pushCode,jdbcType=VARCHAR}, #{batch,jdbcType=INTEGER},
      #{priceTypeCode,jdbcType=VARCHAR}, #{priceTypeName,jdbcType=VARCHAR}, #{channelId,jdbcType=INTEGER},
      #{price,jdbcType=DECIMAL}, #{priceFlag,jdbcType=INTEGER}, #{label,jdbcType=VARCHAR},
      #{comment,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER},
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{sendStatus,jdbcType=INTEGER},
      #{sendMsg,jdbcType=VARCHAR}, #{receiveStatus,jdbcType=INTEGER}, #{receiveMsg,jdbcType=VARCHAR},
      #{effectStatus,jdbcType=INTEGER}, #{effectMsg,jdbcType=VARCHAR}, #{skuId,jdbcType=BIGINT}})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="pushCode != null">
        push_code,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="priceTypeCode != null">
        price_type_code,
      </if>
      <if test="priceTypeName != null">
        price_type_name,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="priceFlag != null">
        price_flag,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="sendMsg != null">
        send_msg,
      </if>
      <if test="receiveStatus != null">
        receive_status,
      </if>
      <if test="receiveMsg != null">
        receive_msg,
      </if>
      <if test="effectStatus != null">
        effect_status,
      </if>
      <if test="effectMsg != null">
        effect_msg,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="pushCode != null">
        #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=INTEGER},
      </if>
      <if test="priceTypeCode != null">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceFlag != null">
        #{priceFlag,jdbcType=INTEGER},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=INTEGER},
      </if>
      <if test="sendMsg != null">
        #{sendMsg,jdbcType=VARCHAR},
      </if>
      <if test="receiveStatus != null">
        #{receiveStatus,jdbcType=INTEGER},
      </if>
      <if test="receiveMsg != null">
        #{receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="effectStatus != null">
        #{effectStatus,jdbcType=INTEGER},
      </if>
      <if test="effectMsg != null">
        #{effectMsg,jdbcType=VARCHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PricePushHistoryExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_push_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_push_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.pushCode != null">
        push_code = #{record.pushCode,jdbcType=VARCHAR},
      </if>
      <if test="record.batch != null">
        batch = #{record.batch,jdbcType=INTEGER},
      </if>
      <if test="record.priceTypeCode != null">
        price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeName != null">
        price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.priceFlag != null">
        price_flag = #{record.priceFlag,jdbcType=INTEGER},
      </if>
      <if test="record.label != null">
        `label` = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.sendStatus != null">
        send_status = #{record.sendStatus,jdbcType=INTEGER},
      </if>
      <if test="record.sendMsg != null">
        send_msg = #{record.sendMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.receiveStatus != null">
        receive_status = #{record.receiveStatus,jdbcType=INTEGER},
      </if>
      <if test="record.receiveMsg != null">
        receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.effectStatus != null">
        effect_status = #{record.effectStatus,jdbcType=INTEGER},
      </if>
      <if test="record.effectMsg != null">
        effect_msg = #{record.effectMsg,jdbcType=VARCHAR},
      </if>
      <if test="record.skuId != null">
        effect_msg = #{record.skuId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_push_history
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      push_code = #{record.pushCode,jdbcType=VARCHAR},
      batch = #{record.batch,jdbcType=INTEGER},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      price = #{record.price,jdbcType=DECIMAL},
      price_flag = #{record.priceFlag,jdbcType=INTEGER},
      `label` = #{record.label,jdbcType=VARCHAR},
      `comment` = #{record.comment,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      send_status = #{record.sendStatus,jdbcType=INTEGER},
      send_msg = #{record.sendMsg,jdbcType=VARCHAR},
      receive_status = #{record.receiveStatus,jdbcType=INTEGER},
      receive_msg = #{record.receiveMsg,jdbcType=VARCHAR},
      effect_status = #{record.effectStatus,jdbcType=INTEGER},
      effect_msg = #{record.effectMsg,jdbcType=VARCHAR},
      sku_id = #{record.skuId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PricePushHistory">
    <!--@mbg.generated-->
    update price_push_history
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="pushCode != null">
        push_code = #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        batch = #{batch,jdbcType=INTEGER},
      </if>
      <if test="priceTypeCode != null">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceFlag != null">
        price_flag = #{priceFlag,jdbcType=INTEGER},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PricePushHistory">
    <!--@mbg.generated-->
    update price_push_history
    set business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      push_code = #{pushCode,jdbcType=VARCHAR},
      batch = #{batch,jdbcType=INTEGER},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      price_flag = #{priceFlag,jdbcType=INTEGER},
      `label` = #{label,jdbcType=VARCHAR},
      `comment` = #{comment,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_push_history
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="store_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="goods_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.goodsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="adjust_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.adjustCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="push_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.pushCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="batch = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.batch,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="price_type_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.price,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="price_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceFlag,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="`label` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.label,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`comment` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.comment,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="extend = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.extend,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_push_history
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="goods_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.goodsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="adjust_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.adjustCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.adjustCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="push_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pushCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.pushCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="batch = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.batch != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.batch,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_flag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceFlag != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceFlag,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="`label` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.label != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.label,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`comment` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.comment != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.comment,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtCreate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtUpdate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="extend = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extend != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.extend,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_history
    (business_id, store_id, goods_no, adjust_code, push_code, batch, price_type_code,
      price_type_name, channel_id, price, price_flag, `label`, `comment`, `status`, gmt_create,
      gmt_update, extend, version, created_by, updated_by)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT}, #{item.goodsNo,jdbcType=VARCHAR},
        #{item.adjustCode,jdbcType=VARCHAR}, #{item.pushCode,jdbcType=VARCHAR}, #{item.batch,jdbcType=INTEGER},
        #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.priceTypeName,jdbcType=VARCHAR},
        #{item.channelId,jdbcType=INTEGER}, #{item.price,jdbcType=DECIMAL}, #{item.priceFlag,jdbcType=INTEGER},
        #{item.label,jdbcType=VARCHAR}, #{item.comment,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
        #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT}, #{item.updatedBy,jdbcType=BIGINT}
        )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      business_id,
      store_id,
      goods_no,
      adjust_code,
      push_code,
      batch,
      price_type_code,
      price_type_name,
      channel_id,
      price,
      price_flag,
      `label`,
      `comment`,
      `status`,
      gmt_create,
      gmt_update,
      extend,
      version,
      created_by,
      updated_by,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{businessId,jdbcType=BIGINT},
      #{storeId,jdbcType=BIGINT},
      #{goodsNo,jdbcType=VARCHAR},
      #{adjustCode,jdbcType=VARCHAR},
      #{pushCode,jdbcType=VARCHAR},
      #{batch,jdbcType=INTEGER},
      #{priceTypeCode,jdbcType=VARCHAR},
      #{priceTypeName,jdbcType=VARCHAR},
      #{channelId,jdbcType=INTEGER},
      #{price,jdbcType=DECIMAL},
      #{priceFlag,jdbcType=INTEGER},
      #{label,jdbcType=VARCHAR},
      #{comment,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP},
      #{extend,jdbcType=VARCHAR},
      #{version,jdbcType=INTEGER},
      #{createdBy,jdbcType=BIGINT},
      #{updatedBy,jdbcType=BIGINT},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      push_code = #{pushCode,jdbcType=VARCHAR},
      batch = #{batch,jdbcType=INTEGER},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=INTEGER},
      price = #{price,jdbcType=DECIMAL},
      price_flag = #{priceFlag,jdbcType=INTEGER},
      `label` = #{label,jdbcType=VARCHAR},
      `comment` = #{comment,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PricePushHistory" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_push_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="pushCode != null">
        push_code,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="priceTypeCode != null">
        price_type_code,
      </if>
      <if test="priceTypeName != null">
        price_type_name,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="priceFlag != null">
        price_flag,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="pushCode != null">
        #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=INTEGER},
      </if>
      <if test="priceTypeCode != null">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceFlag != null">
        #{priceFlag,jdbcType=INTEGER},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="pushCode != null">
        push_code = #{pushCode,jdbcType=VARCHAR},
      </if>
      <if test="batch != null">
        batch = #{batch,jdbcType=INTEGER},
      </if>
      <if test="priceTypeCode != null">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="priceFlag != null">
        price_flag = #{priceFlag,jdbcType=INTEGER},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
</mapper>
