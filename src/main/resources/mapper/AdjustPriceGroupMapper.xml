<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.AdjustPriceGroupMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceGroup">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_names" jdbcType="VARCHAR" property="orgNames" />
    <result column="org_ids" jdbcType="VARCHAR" property="orgIds" />
    <result column="org_info" jdbcType="VARCHAR" property="orgInfo" />
    <result column="group_level" jdbcType="TINYINT" property="groupLevel" />
    <result column="adjust_type" jdbcType="TINYINT" property="adjustType" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="old_group_name" jdbcType="VARCHAR" property="oldGroupName" />
    <result column="old_group_id" jdbcType="BIGINT" property="oldGroupId" />
    <result column="new_group_name" jdbcType="VARCHAR" property="newGroupName" />
    <result column="new_group_id" jdbcType="BIGINT" property="newGroupId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="audit_by" jdbcType="BIGINT" property="auditBy" />
    <result column="audit_by_name" jdbcType="VARCHAR" property="auditByName" />
    <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_id, org_names, org_ids, org_info, group_level, adjust_type, audit_status,
    old_group_name, old_group_id, new_group_name, new_group_id, reason, status, gmt_create,
    gmt_update, extend, version, created_by, updated_by, updated_by_name, audit_by, audit_by_name,
    audit_date
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceGroupExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from adjust_price_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from adjust_price_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from adjust_price_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceGroupExample">
    delete from adjust_price_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.AdjustPriceGroup">
    insert into adjust_price_group (id, org_id, org_names,
      org_ids, org_info, group_level,
      adjust_type, audit_status, old_group_name,
      old_group_id, new_group_name, new_group_id,
      reason, status, gmt_create,
      gmt_update, extend, version,
      created_by, updated_by, updated_by_name,
      audit_by, audit_by_name, audit_date
      )
    values (#{id,jdbcType=BIGINT}, #{orgId,jdbcType=BIGINT}, #{orgNames,jdbcType=VARCHAR},
      #{orgIds,jdbcType=VARCHAR}, #{orgInfo,jdbcType=VARCHAR}, #{groupLevel,jdbcType=TINYINT},
      #{adjustType,jdbcType=TINYINT}, #{auditStatus,jdbcType=TINYINT}, #{oldGroupName,jdbcType=VARCHAR},
      #{oldGroupId,jdbcType=BIGINT}, #{newGroupName,jdbcType=VARCHAR}, #{newGroupId,jdbcType=BIGINT},
      #{reason,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER},
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR},
      #{auditBy,jdbcType=BIGINT}, #{auditByName,jdbcType=VARCHAR}, #{auditDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.AdjustPriceGroup">
    insert into adjust_price_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgNames != null">
        org_names,
      </if>
      <if test="orgIds != null">
        org_ids,
      </if>
      <if test="orgInfo != null">
        org_info,
      </if>
      <if test="groupLevel != null">
        group_level,
      </if>
      <if test="adjustType != null">
        adjust_type,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="oldGroupName != null">
        old_group_name,
      </if>
      <if test="oldGroupId != null">
        old_group_id,
      </if>
      <if test="newGroupName != null">
        new_group_name,
      </if>
      <if test="newGroupId != null">
        new_group_id,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>

    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgNames != null">
        #{orgNames,jdbcType=VARCHAR},
      </if>
      <if test="orgIds != null">
        #{orgIds,jdbcType=VARCHAR},
      </if>
      <if test="orgInfo != null">
        #{orgInfo,jdbcType=VARCHAR},
      </if>
      <if test="groupLevel != null">
        #{groupLevel,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="oldGroupName != null">
        #{oldGroupName,jdbcType=VARCHAR},
      </if>
      <if test="oldGroupId != null">
        #{oldGroupId,jdbcType=BIGINT},
      </if>
      <if test="newGroupName != null">
        #{newGroupName,jdbcType=VARCHAR},
      </if>
      <if test="newGroupId != null">
        #{newGroupId,jdbcType=BIGINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>

    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceGroupExample" resultType="java.lang.Long">
    select count(*) from adjust_price_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update adjust_price_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgNames != null">
        org_names = #{record.orgNames,jdbcType=VARCHAR},
      </if>
      <if test="record.orgIds != null">
        org_ids = #{record.orgIds,jdbcType=VARCHAR},
      </if>
      <if test="record.orgInfo != null">
        org_info = #{record.orgInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.groupLevel != null">
        group_level = #{record.groupLevel,jdbcType=TINYINT},
      </if>
      <if test="record.adjustType != null">
        adjust_type = #{record.adjustType,jdbcType=TINYINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.oldGroupName != null">
        old_group_name = #{record.oldGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.oldGroupId != null">
        old_group_id = #{record.oldGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.newGroupName != null">
        new_group_name = #{record.newGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.newGroupId != null">
        new_group_id = #{record.newGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditBy != null">
        audit_by = #{record.auditBy,jdbcType=BIGINT},
      </if>
      <if test="record.auditByName != null">
        audit_by_name = #{record.auditByName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditDate != null">
        audit_date = #{record.auditDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update adjust_price_group
    set id = #{record.id,jdbcType=BIGINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_names = #{record.orgNames,jdbcType=VARCHAR},
      org_ids = #{record.orgIds,jdbcType=VARCHAR},
      org_info = #{record.orgInfo,jdbcType=VARCHAR},
      group_level = #{record.groupLevel,jdbcType=TINYINT},
      adjust_type = #{record.adjustType,jdbcType=TINYINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      old_group_name = #{record.oldGroupName,jdbcType=VARCHAR},
      old_group_id = #{record.oldGroupId,jdbcType=BIGINT},
      new_group_name = #{record.newGroupName,jdbcType=VARCHAR},
      new_group_id = #{record.newGroupId,jdbcType=BIGINT},
      reason = #{record.reason,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      audit_by = #{record.auditBy,jdbcType=BIGINT},
      audit_by_name = #{record.auditByName,jdbcType=VARCHAR},
      audit_date = #{record.auditDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.AdjustPriceGroup">
    update adjust_price_group
    <set>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgNames != null">
        org_names = #{orgNames,jdbcType=VARCHAR},
      </if>
      <if test="orgIds != null">
        org_ids = #{orgIds,jdbcType=VARCHAR},
      </if>
      <if test="orgInfo != null">
        org_info = #{orgInfo,jdbcType=VARCHAR},
      </if>
      <if test="groupLevel != null">
        group_level = #{groupLevel,jdbcType=TINYINT},
      </if>
      <if test="adjustType != null">
        adjust_type = #{adjustType,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="oldGroupName != null">
        old_group_name = #{oldGroupName,jdbcType=VARCHAR},
      </if>
      <if test="oldGroupId != null">
        old_group_id = #{oldGroupId,jdbcType=BIGINT},
      </if>
      <if test="newGroupName != null">
        new_group_name = #{newGroupName,jdbcType=VARCHAR},
      </if>
      <if test="newGroupId != null">
        new_group_id = #{newGroupId,jdbcType=BIGINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="auditBy != null">
        audit_by = #{auditBy,jdbcType=BIGINT},
      </if>
      <if test="auditByName != null">
        audit_by_name = #{auditByName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null">
        audit_date = #{auditDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.AdjustPriceGroup">
    update adjust_price_group
    set org_id = #{orgId,jdbcType=BIGINT},
      org_names = #{orgNames,jdbcType=VARCHAR},
      org_ids = #{orgIds,jdbcType=VARCHAR},
      org_info = #{orgInfo,jdbcType=VARCHAR},
      group_level = #{groupLevel,jdbcType=TINYINT},
      adjust_type = #{adjustType,jdbcType=TINYINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      old_group_name = #{oldGroupName,jdbcType=VARCHAR},
      old_group_id = #{oldGroupId,jdbcType=BIGINT},
      new_group_name = #{newGroupName,jdbcType=VARCHAR},
      new_group_id = #{newGroupId,jdbcType=BIGINT},
      reason = #{reason,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      audit_by = #{auditBy,jdbcType=BIGINT},
      audit_by_name = #{auditByName,jdbcType=VARCHAR},
      audit_date = #{auditDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
