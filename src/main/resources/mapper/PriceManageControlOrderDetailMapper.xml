<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceManageControlOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceManageControlOrderDetail">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="control_order_code" jdbcType="VARCHAR" property="controlOrderCode" />
    <result column="auth_org_id" jdbcType="BIGINT" property="authOrgId" />
    <result column="auth_org_name" jdbcType="VARCHAR" property="authOrgName" />
    <result column="auth_level" jdbcType="TINYINT" property="authLevel" />
    <result column="org_goods_id" jdbcType="BIGINT" property="orgGoodsId" />
    <result column="spu_id" jdbcType="BIGINT" property="spuId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="price_group_names" jdbcType="VARCHAR" property="priceGroupNames" />
    <result column="price_type_id" jdbcType="BIGINT" property="priceTypeId" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
    <result column="price_type_out_code" jdbcType="VARCHAR" property="priceTypeOutCode" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit" />
    <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="jhi_specification" jdbcType="VARCHAR" property="jhiSpecification" />
    <result column="dosage" jdbcType="VARCHAR" property="dosage" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="extend1" jdbcType="VARCHAR" property="extend1" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="prodarea" jdbcType="VARCHAR" property="prodarea" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="channel_out_code" jdbcType="VARCHAR" property="channelOutCode" />
    <result column="channel_en_code" jdbcType="VARCHAR" property="channelEnCode" />
    <result column="guide_price" jdbcType="DECIMAL" property="guidePrice" />
    <result column="dxs_date" jdbcType="TIMESTAMP" property="dxsDate" />
    <result column="effect_status" jdbcType="INTEGER" property="effectStatus" />
    <result column="control_order_audit_time" jdbcType="TIMESTAMP" property="controlOrderAuditTime" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
  </resultMap>

  <resultMap id="AssembleResultMap" type="com.cowell.pricecenter.service.dto.ControlOrderLimitDTO">
      <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
      <result column="org_id" jdbcType="BIGINT" property="orgId" />
      <result column="org_level" jdbcType="INTEGER" property="orgLevel" />
      <result column="channel_id" jdbcType="INTEGER" property="channelId" />
      <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
      <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
      <result column="guide_price" jdbcType="DECIMAL" property="guidePrice" />
      <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit" />
      <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit" />
      <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, control_order_code, auth_org_id, auth_org_name, auth_level, org_goods_id, spu_id,
    goods_no, price_group_names, price_type_id, price_type_code, price_type_name, price_type_out_code,
    price, original_price, upper_limit, lower_limit, cur_name, manufacturer, jhi_specification,
    dosage, `status`, gmt_create, gmt_update, extend, extend1, version, created_by, updated_by,
    updated_by_name, channel_id, prodarea, goods_unit, goods_name, channel_out_code,
    channel_en_code, guide_price,dxs_date,effect_status,control_order_audit_time, order_type
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetailExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_manage_control_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
          limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
          limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_manage_control_order_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from price_manage_control_order_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetailExample">
    <!--@mbg.generated-->
    delete from price_manage_control_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_order_detail (control_order_code, auth_org_id, auth_org_name,
      auth_level, org_goods_id, spu_id,
      goods_no, price_group_names, price_type_id,
      price_type_code, price_type_name, price_type_out_code,
      price, original_price, upper_limit,
      lower_limit, cur_name, manufacturer,
      jhi_specification, dosage, `status`,
      gmt_create, gmt_update, extend,
      extend1, version, created_by,
      updated_by, updated_by_name, channel_id,
      prodarea, goods_unit, goods_name,
      channel_out_code, channel_en_code, guide_price,
      dxs_date,effect_status,order_type
      )
    values (#{controlOrderCode,jdbcType=VARCHAR}, #{authOrgId,jdbcType=BIGINT}, #{authOrgName,jdbcType=VARCHAR},
      #{authLevel,jdbcType=TINYINT}, #{orgGoodsId,jdbcType=BIGINT}, #{spuId,jdbcType=BIGINT},
      #{goodsNo,jdbcType=VARCHAR}, #{priceGroupNames,jdbcType=VARCHAR}, #{priceTypeId,jdbcType=BIGINT},
      #{priceTypeCode,jdbcType=VARCHAR}, #{priceTypeName,jdbcType=VARCHAR}, #{priceTypeOutCode,jdbcType=VARCHAR},
      #{price,jdbcType=DECIMAL}, #{originalPrice,jdbcType=DECIMAL}, #{upperLimit,jdbcType=DECIMAL},
      #{lowerLimit,jdbcType=DECIMAL}, #{curName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{jhiSpecification,jdbcType=VARCHAR}, #{dosage,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR},
      #{extend1,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT},
      #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}, #{channelId,jdbcType=INTEGER},
      #{prodarea,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
      #{channelOutCode,jdbcType=VARCHAR}, #{channelEnCode,jdbcType=VARCHAR}, #{guidePrice,jdbcType=DECIMAL},
      #{dxsDate,jdbcType=TIMESTAMP},#{effectStatus,jdbcType=INTEGER},#{orderType,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_order_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="controlOrderCode != null and controlOrderCode != ''">
        control_order_code,
      </if>
      <if test="authOrgId != null">
        auth_org_id,
      </if>
      <if test="authOrgName != null and authOrgName != ''">
        auth_org_name,
      </if>
      <if test="authLevel != null">
        auth_level,
      </if>
      <if test="orgGoodsId != null">
        org_goods_id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        goods_no,
      </if>
      <if test="priceGroupNames != null and priceGroupNames != ''">
        price_group_names,
      </if>
      <if test="priceTypeId != null">
        price_type_id,
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        price_type_code,
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        price_type_name,
      </if>
      <if test="priceTypeOutCode != null and priceTypeOutCode != ''">
        price_type_out_code,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="lowerLimit != null">
        lower_limit,
      </if>
      <if test="curName != null and curName != ''">
        cur_name,
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        manufacturer,
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        jhi_specification,
      </if>
      <if test="dosage != null and dosage != ''">
        dosage,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null and extend != ''">
        extend,
      </if>
      <if test="extend1 != null and extend1 != ''">
        extend1,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="prodarea != null and prodarea != ''">
        prodarea,
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        goods_unit,
      </if>
      <if test="goodsName != null and goodsName != ''">
        goods_name,
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        channel_out_code,
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code,
      </if>
      <if test="guidePrice != null">
        guide_price,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="controlOrderCode != null and controlOrderCode != ''">
        #{controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="authOrgId != null">
        #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null and authOrgName != ''">
        #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authLevel != null">
        #{authLevel,jdbcType=TINYINT},
      </if>
      <if test="orgGoodsId != null">
        #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceGroupNames != null and priceGroupNames != ''">
        #{priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeOutCode != null and priceTypeOutCode != ''">
        #{priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="curName != null and curName != ''">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null and dosage != ''">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null and extend != ''">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="extend1 != null and extend1 != ''">
        #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null and prodarea != ''">
        #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetailExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_manage_control_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_manage_control_order_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.controlOrderCode != null">
        control_order_code = #{record.controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="record.authOrgId != null">
        auth_org_id = #{record.authOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.authOrgName != null">
        auth_org_name = #{record.authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.authLevel != null">
        auth_level = #{record.authLevel,jdbcType=TINYINT},
      </if>
      <if test="record.orgGoodsId != null">
        org_goods_id = #{record.orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.priceGroupNames != null">
        price_group_names = #{record.priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeId != null">
        price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.priceTypeCode != null">
        price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeName != null">
        price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeOutCode != null">
        price_type_out_code = #{record.priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.originalPrice != null">
        original_price = #{record.originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.upperLimit != null">
        upper_limit = #{record.upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.lowerLimit != null">
        lower_limit = #{record.lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.curName != null">
        cur_name = #{record.curName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.jhiSpecification != null">
        jhi_specification = #{record.jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="record.dosage != null">
        dosage = #{record.dosage,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.extend1 != null">
        extend1 = #{record.extend1,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.prodarea != null">
        prodarea = #{record.prodarea,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelOutCode != null">
        channel_out_code = #{record.channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="record.channelEnCode != null">
        channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="record.guidePrice != null">
        guide_price = #{record.guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.controlOrderAuditTime != null">
        control_order_audit_time = #{record.controlOrderAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_manage_control_order_detail
    set id = #{record.id,jdbcType=BIGINT},
      control_order_code = #{record.controlOrderCode,jdbcType=VARCHAR},
      auth_org_id = #{record.authOrgId,jdbcType=BIGINT},
      auth_org_name = #{record.authOrgName,jdbcType=VARCHAR},
      auth_level = #{record.authLevel,jdbcType=TINYINT},
      org_goods_id = #{record.orgGoodsId,jdbcType=BIGINT},
      spu_id = #{record.spuId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      price_group_names = #{record.priceGroupNames,jdbcType=VARCHAR},
      price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      price_type_out_code = #{record.priceTypeOutCode,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      original_price = #{record.originalPrice,jdbcType=DECIMAL},
      upper_limit = #{record.upperLimit,jdbcType=DECIMAL},
      lower_limit = #{record.lowerLimit,jdbcType=DECIMAL},
      cur_name = #{record.curName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{record.jhiSpecification,jdbcType=VARCHAR},
      dosage = #{record.dosage,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      extend1 = #{record.extend1,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      prodarea = #{record.prodarea,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      channel_out_code = #{record.channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      guide_price = #{record.guidePrice,jdbcType=DECIMAL},
      control_order_audit_time = #{record.controlOrderAuditTime,jdbcType=TIMESTAMP},
      order_type = #{record.orderType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetail">
    <!--@mbg.generated-->
    update price_manage_control_order_detail
    <set>
      <if test="controlOrderCode != null and controlOrderCode != ''">
        control_order_code = #{controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="authOrgId != null">
        auth_org_id = #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null and authOrgName != ''">
        auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authLevel != null">
        auth_level = #{authLevel,jdbcType=TINYINT},
      </if>
      <if test="orgGoodsId != null">
        org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceGroupNames != null and priceGroupNames != ''">
        price_group_names = #{priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        price_type_id = #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeOutCode != null and priceTypeOutCode != ''">
        price_type_out_code = #{priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="curName != null and curName != ''">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null and dosage != ''">
        dosage = #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null and extend != ''">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="extend1 != null and extend1 != ''">
        extend1 = #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null and prodarea != ''">
        prodarea = #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        guide_price = #{guidePrice,jdbcType=DECIMAL},
      </if>

      <if test="controlOrderAuditTime != null">
        control_order_audit_time = #{controlOrderAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetail">
    <!--@mbg.generated-->
    update price_manage_control_order_detail
    set control_order_code = #{controlOrderCode,jdbcType=VARCHAR},
      auth_org_id = #{authOrgId,jdbcType=BIGINT},
      auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      auth_level = #{authLevel,jdbcType=TINYINT},
      org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      spu_id = #{spuId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      price_group_names = #{priceGroupNames,jdbcType=VARCHAR},
      price_type_id = #{priceTypeId,jdbcType=BIGINT},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      price_type_out_code = #{priceTypeOutCode,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      original_price = #{originalPrice,jdbcType=DECIMAL},
      upper_limit = #{upperLimit,jdbcType=DECIMAL},
      lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      cur_name = #{curName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      dosage = #{dosage,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      extend1 = #{extend1,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=INTEGER},
      prodarea = #{prodarea,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      guide_price = #{guidePrice,jdbcType=DECIMAL},
      control_order_audit_time = #{controlOrderAuditTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getCurrentControlLimitOfGoodsNo"  parameterType="map"
            resultMap="AssembleResultMap">
      SELECT
          a.org_id,
          a.org_level,
          b.goods_no,
          b.gmt_update,
          b.channel_id,
          b.price_type_code,
          b.guide_price,
          b.upper_limit,
          b.lower_limit
      FROM price_manage_control_org_detail a LEFT JOIN price_manage_control_order_detail b
                                                       ON a.control_order_code = b.control_order_code
      WHERE
          b.effect_status = #{queryParam.effectStatus,jdbcType=VARCHAR}
        AND b.`status` = #{queryParam.status,jdbcType=TINYINT}
        AND a.org_id = #{queryParam.orgId,jdbcType=BIGINT}
        AND b.goods_no = #{queryParam.goodsNo,jdbcType=VARCHAR}
        <if test="queryParam.channelIdList != null and queryParam.channelIdList.size() > 0">
            AND b.channel_id IN
            <foreach collection="queryParam.channelIdList" index="index" item="channelId" open="(" separator="," close=")">
                #{channelId}
            </foreach>
        </if>
        AND b.price_type_code = #{queryParam.priceTypeCode,jdbcType=BIGINT}
  </select>

  <select id="cursorScanControlOrderDetail" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_manage_control_order_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <update id="batchUpdatePriceManageControlOrderDetail" parameterType="map">
    update price_manage_control_order_detail
        set effect_status = #{effectStatus,jdbcType=INTEGER}, gmt_update=now()
            where
        <if test="ids != null and ids.size() != 0">
                id in
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
            </foreach>
        </if>
              and effect_status=0
  </update>

  <update id="updateControlOrderDetailDxsDate" parameterType="map">
    update price_manage_control_order_detail
        set dxs_date = #{dxsDate,jdbcType=TIMESTAMP} where control_order_code=#{controlOrderCode,jdbcType=VARCHAR}

  </update>

    <update id="updateControlOrderDetailAuditDate" parameterType="map">
        update price_manage_control_order_detail
        set control_order_audit_time = #{auditDate,jdbcType=TIMESTAMP} where control_order_code=#{controlOrderCode,jdbcType=VARCHAR}

    </update>
</mapper>
