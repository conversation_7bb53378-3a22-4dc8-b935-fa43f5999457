<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceControlNoticeMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceControlNotice">
    <!--@mbg.generated-->
    <!--@Table price_control_notice-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="control_order_id" jdbcType="VARCHAR" property="controlOrderId" />
    <result column="platform_id" jdbcType="BIGINT" property="platformId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price_type_id" jdbcType="BIGINT" property="priceTypeId" />
    <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="uncontrol_order_id" jdbcType="VARCHAR" property="uncontrolOrderId" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_by_name" jdbcType="VARCHAR" property="createdByName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="jhi_specification" jdbcType="VARCHAR" property="jhiSpecification" />
    <result column="dosage" jdbcType="VARCHAR" property="dosage" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="prodarea" jdbcType="VARCHAR" property="prodarea" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="channel_out_code" jdbcType="VARCHAR" property="channelOutCode" />
    <result column="channel_en_code" jdbcType="VARCHAR" property="channelEnCode" />
    <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit" />
    <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="guide_price" jdbcType="DECIMAL" property="guidePrice" />
    <result column="dxs_date" jdbcType="TIMESTAMP" property="dxsDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, control_order_id, platform_id, platform_name, business_id, business_name, store_id,
    store_name, goods_no, price_type_code, price_type_id, price_type_name, `status`,
    uncontrol_order_id, adjust_code, gmt_create, gmt_update, created_by, created_by_name,
    updated_by, updated_by_name, cur_name, manufacturer, jhi_specification, dosage, channel_id,
    channel_name, prodarea, goods_unit, goods_name, channel_out_code, channel_en_code,
    upper_limit, lower_limit, price, guide_price, dxs_date
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceControlNoticeExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_control_notice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
      <if test="limit != null">
          <if test="offset != null">
              limit ${offset}, ${limit}
          </if>
          <if test="offset == null">
              limit ${limit}
          </if>
      </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_control_notice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from price_control_notice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceControlNoticeExample">
    <!--@mbg.generated-->
    delete from price_control_notice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceControlNotice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_control_notice (control_order_id, platform_id, platform_name,
      business_id, business_name, store_id,
      store_name, goods_no, price_type_code,
      price_type_id, price_type_name, `status`,
      uncontrol_order_id, adjust_code, gmt_create,
      gmt_update, created_by, created_by_name,
      updated_by, updated_by_name, cur_name,
      manufacturer, jhi_specification, dosage,
      channel_id, channel_name, prodarea,
      goods_unit, goods_name, channel_out_code,
      channel_en_code, upper_limit, lower_limit,
      price, guide_price, dxs_date
      )
    values (#{controlOrderId,jdbcType=VARCHAR}, #{platformId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR},
      #{businessId,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT},
      #{storeName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{priceTypeCode,jdbcType=VARCHAR},
      #{priceTypeId,jdbcType=BIGINT}, #{priceTypeName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{uncontrolOrderId,jdbcType=VARCHAR}, #{adjustCode,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=BIGINT}, #{createdByName,jdbcType=VARCHAR},
      #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}, #{curName,jdbcType=VARCHAR},
      #{manufacturer,jdbcType=VARCHAR}, #{jhiSpecification,jdbcType=VARCHAR}, #{dosage,jdbcType=VARCHAR},
      #{channelId,jdbcType=INTEGER}, #{channelName,jdbcType=VARCHAR}, #{prodarea,jdbcType=VARCHAR},
      #{goodsUnit,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{channelOutCode,jdbcType=VARCHAR},
      #{channelEnCode,jdbcType=VARCHAR}, #{upperLimit,jdbcType=DECIMAL}, #{lowerLimit,jdbcType=DECIMAL},
      #{price,jdbcType=DECIMAL}, #{guidePrice,jdbcType=DECIMAL}, #{dxsDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceControlNotice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_control_notice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="controlOrderId != null and controlOrderId != ''">
        control_order_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="platformName != null and platformName != ''">
        platform_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null and businessName != ''">
        business_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null and storeName != ''">
        store_name,
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        goods_no,
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        price_type_code,
      </if>
      <if test="priceTypeId != null">
        price_type_id,
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        price_type_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="uncontrolOrderId != null and uncontrolOrderId != ''">
        uncontrol_order_id,
      </if>
      <if test="adjustCode != null and adjustCode != ''">
        adjust_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdByName != null and createdByName != ''">
        created_by_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name,
      </if>
      <if test="curName != null and curName != ''">
        cur_name,
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        manufacturer,
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        jhi_specification,
      </if>
      <if test="dosage != null and dosage != ''">
        dosage,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelName != null and channelName != ''">
        channel_name,
      </if>
      <if test="prodarea != null and prodarea != ''">
        prodarea,
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        goods_unit,
      </if>
      <if test="goodsName != null and goodsName != ''">
        goods_name,
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        channel_out_code,
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="lowerLimit != null">
        lower_limit,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="guidePrice != null">
        guide_price,
      </if>
      <if test="dxsDate != null">
        dxs_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="controlOrderId != null and controlOrderId != ''">
        #{controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null and platformName != ''">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null and businessName != ''">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null and storeName != ''">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="uncontrolOrderId != null and uncontrolOrderId != ''">
        #{uncontrolOrderId,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null and adjustCode != ''">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null and createdByName != ''">
        #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="curName != null and curName != ''">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null and dosage != ''">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelName != null and channelName != ''">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="prodarea != null and prodarea != ''">
        #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="guidePrice != null">
        #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        #{dxsDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceControlNoticeExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_control_notice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_control_notice
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.controlOrderId != null">
        control_order_id = #{record.controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeCode != null">
        price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeId != null">
        price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.priceTypeName != null">
        price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.uncontrolOrderId != null">
        uncontrol_order_id = #{record.uncontrolOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdByName != null">
        created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.curName != null">
        cur_name = #{record.curName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.jhiSpecification != null">
        jhi_specification = #{record.jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="record.dosage != null">
        dosage = #{record.dosage,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.channelName != null">
        channel_name = #{record.channelName,jdbcType=VARCHAR},
      </if>
      <if test="record.prodarea != null">
        prodarea = #{record.prodarea,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelOutCode != null">
        channel_out_code = #{record.channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="record.channelEnCode != null">
        channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="record.upperLimit != null">
        upper_limit = #{record.upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.lowerLimit != null">
        lower_limit = #{record.lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.guidePrice != null">
        guide_price = #{record.guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.dxsDate != null">
        dxs_date = #{record.dxsDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_control_notice
    set id = #{record.id,jdbcType=BIGINT},
      control_order_id = #{record.controlOrderId,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      uncontrol_order_id = #{record.uncontrolOrderId,jdbcType=VARCHAR},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      cur_name = #{record.curName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{record.jhiSpecification,jdbcType=VARCHAR},
      dosage = #{record.dosage,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      channel_name = #{record.channelName,jdbcType=VARCHAR},
      prodarea = #{record.prodarea,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      channel_out_code = #{record.channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      upper_limit = #{record.upperLimit,jdbcType=DECIMAL},
      lower_limit = #{record.lowerLimit,jdbcType=DECIMAL},
      price = #{record.price,jdbcType=DECIMAL},
      guide_price = #{record.guidePrice,jdbcType=DECIMAL},
      dxs_date = #{record.dxsDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceControlNotice">
    <!--@mbg.generated-->
    update price_control_notice
    <set>
      <if test="controlOrderId != null and controlOrderId != ''">
        control_order_id = #{controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null and platformName != ''">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null and businessName != ''">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null and storeName != ''">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        price_type_id = #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="uncontrolOrderId != null and uncontrolOrderId != ''">
        uncontrol_order_id = #{uncontrolOrderId,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null and adjustCode != ''">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null and createdByName != ''">
        created_by_name = #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="curName != null and curName != ''">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null and dosage != ''">
        dosage = #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelName != null and channelName != ''">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="prodarea != null and prodarea != ''">
        prodarea = #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="guidePrice != null">
        guide_price = #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        dxs_date = #{dxsDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceControlNotice">
    <!--@mbg.generated-->
    update price_control_notice
    set control_order_id = #{controlOrderId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_id = #{priceTypeId,jdbcType=BIGINT},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      uncontrol_order_id = #{uncontrolOrderId,jdbcType=VARCHAR},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_by_name = #{createdByName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      cur_name = #{curName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      dosage = #{dosage,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=INTEGER},
      channel_name = #{channelName,jdbcType=VARCHAR},
      prodarea = #{prodarea,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      upper_limit = #{upperLimit,jdbcType=DECIMAL},
      lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      price = #{price,jdbcType=DECIMAL},
      guide_price = #{guidePrice,jdbcType=DECIMAL},
      dxs_date = #{dxsDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_control_notice
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="control_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.controlOrderId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="platform_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.platformId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="platform_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.platformName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="business_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="store_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="store_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="goods_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.goodsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_type_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_type_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="price_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="uncontrol_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.uncontrolOrderId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="adjust_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.adjustCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="created_by_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdByName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="updated_by_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updatedByName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="cur_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.curName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="manufacturer = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.manufacturer,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="jhi_specification = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.jhiSpecification,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dosage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dosage,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="channel_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="prodarea = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.prodarea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="goods_unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.goodsUnit,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="goods_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.goodsName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_out_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelOutCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_en_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelEnCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="upper_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.upperLimit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="lower_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lowerLimit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.price,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="guide_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.guidePrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="dxs_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dxsDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_control_notice
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="control_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.controlOrderId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.controlOrderId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="platform_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.platformId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.platformId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="platform_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.platformName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.platformName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="business_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.businessName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="goods_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.goodsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="uncontrol_order_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.uncontrolOrderId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.uncontrolOrderId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="adjust_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.adjustCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.adjustCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtCreate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtUpdate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_by_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdByName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdByName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_by_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedByName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updatedByName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="cur_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.curName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.curName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="manufacturer = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.manufacturer != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.manufacturer,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="jhi_specification = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jhiSpecification != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.jhiSpecification,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dosage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dosage != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dosage,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="prodarea = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.prodarea != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.prodarea,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="goods_unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsUnit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.goodsUnit,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="goods_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.goodsName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_out_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelOutCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelOutCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_en_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelEnCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelEnCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="upper_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.upperLimit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.upperLimit,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="lower_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lowerLimit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.lowerLimit,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="guide_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.guidePrice != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.guidePrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="dxs_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dxsDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dxsDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_control_notice
    (control_order_id, platform_id, platform_name, business_id, business_name, store_id,
      store_name, goods_no, price_type_code, price_type_id, price_type_name, `status`,
      uncontrol_order_id, adjust_code, gmt_create, gmt_update, created_by, created_by_name,
      updated_by, updated_by_name, cur_name, manufacturer, jhi_specification, dosage,
      channel_id, channel_name, prodarea, goods_unit, goods_name, channel_out_code, channel_en_code,
      upper_limit, lower_limit, price, guide_price, dxs_date)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.controlOrderId,jdbcType=VARCHAR}, #{item.platformId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR},
        #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT},
        #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.priceTypeCode,jdbcType=VARCHAR},
        #{item.priceTypeId,jdbcType=BIGINT}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
        #{item.uncontrolOrderId,jdbcType=VARCHAR}, #{item.adjustCode,jdbcType=VARCHAR},
        #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=BIGINT},
        #{item.createdByName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedByName,jdbcType=VARCHAR},
        #{item.curName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.jhiSpecification,jdbcType=VARCHAR},
        #{item.dosage,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER}, #{item.channelName,jdbcType=VARCHAR},
        #{item.prodarea,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
        #{item.channelOutCode,jdbcType=VARCHAR}, #{item.channelEnCode,jdbcType=VARCHAR},
        #{item.upperLimit,jdbcType=DECIMAL}, #{item.lowerLimit,jdbcType=DECIMAL}, #{item.price,jdbcType=DECIMAL},
        #{item.guidePrice,jdbcType=DECIMAL}, #{item.dxsDate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceControlNotice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_control_notice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      control_order_id,
      platform_id,
      platform_name,
      business_id,
      business_name,
      store_id,
      store_name,
      goods_no,
      price_type_code,
      price_type_id,
      price_type_name,
      `status`,
      uncontrol_order_id,
      adjust_code,
      gmt_create,
      gmt_update,
      created_by,
      created_by_name,
      updated_by,
      updated_by_name,
      cur_name,
      manufacturer,
      jhi_specification,
      dosage,
      channel_id,
      channel_name,
      prodarea,
      goods_unit,
      goods_name,
      channel_out_code,
      channel_en_code,
      upper_limit,
      lower_limit,
      price,
      guide_price,
      dxs_date,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{controlOrderId,jdbcType=VARCHAR},
      #{platformId,jdbcType=BIGINT},
      #{platformName,jdbcType=VARCHAR},
      #{businessId,jdbcType=BIGINT},
      #{businessName,jdbcType=VARCHAR},
      #{storeId,jdbcType=BIGINT},
      #{storeName,jdbcType=VARCHAR},
      #{goodsNo,jdbcType=VARCHAR},
      #{priceTypeCode,jdbcType=VARCHAR},
      #{priceTypeId,jdbcType=BIGINT},
      #{priceTypeName,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT},
      #{uncontrolOrderId,jdbcType=VARCHAR},
      #{adjustCode,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP},
      #{createdBy,jdbcType=BIGINT},
      #{createdByName,jdbcType=VARCHAR},
      #{updatedBy,jdbcType=BIGINT},
      #{updatedByName,jdbcType=VARCHAR},
      #{curName,jdbcType=VARCHAR},
      #{manufacturer,jdbcType=VARCHAR},
      #{jhiSpecification,jdbcType=VARCHAR},
      #{dosage,jdbcType=VARCHAR},
      #{channelId,jdbcType=INTEGER},
      #{channelName,jdbcType=VARCHAR},
      #{prodarea,jdbcType=VARCHAR},
      #{goodsUnit,jdbcType=VARCHAR},
      #{goodsName,jdbcType=VARCHAR},
      #{channelOutCode,jdbcType=VARCHAR},
      #{channelEnCode,jdbcType=VARCHAR},
      #{upperLimit,jdbcType=DECIMAL},
      #{lowerLimit,jdbcType=DECIMAL},
      #{price,jdbcType=DECIMAL},
      #{guidePrice,jdbcType=DECIMAL},
      #{dxsDate,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      control_order_id = #{controlOrderId,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_id = #{priceTypeId,jdbcType=BIGINT},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      uncontrol_order_id = #{uncontrolOrderId,jdbcType=VARCHAR},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_by_name = #{createdByName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      cur_name = #{curName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      dosage = #{dosage,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=INTEGER},
      channel_name = #{channelName,jdbcType=VARCHAR},
      prodarea = #{prodarea,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      upper_limit = #{upperLimit,jdbcType=DECIMAL},
      lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      price = #{price,jdbcType=DECIMAL},
      guide_price = #{guidePrice,jdbcType=DECIMAL},
      dxs_date = #{dxsDate,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceControlNotice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_control_notice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="controlOrderId != null">
        control_order_id,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="priceTypeCode != null">
        price_type_code,
      </if>
      <if test="priceTypeId != null">
        price_type_id,
      </if>
      <if test="priceTypeName != null">
        price_type_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="uncontrolOrderId != null">
        uncontrol_order_id,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdByName != null">
        created_by_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
      <if test="curName != null">
        cur_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="jhiSpecification != null">
        jhi_specification,
      </if>
      <if test="dosage != null">
        dosage,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="prodarea != null">
        prodarea,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="channelOutCode != null">
        channel_out_code,
      </if>
      <if test="channelEnCode != null">
        channel_en_code,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="lowerLimit != null">
        lower_limit,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="guidePrice != null">
        guide_price,
      </if>
      <if test="dxsDate != null">
        dxs_date,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="controlOrderId != null">
        #{controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeName != null">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="uncontrolOrderId != null">
        #{uncontrolOrderId,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null">
        #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="curName != null">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null">
        #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="prodarea != null">
        #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null">
        #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null">
        #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="guidePrice != null">
        #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        #{dxsDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="controlOrderId != null">
        control_order_id = #{controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        price_type_id = #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeName != null">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="uncontrolOrderId != null">
        uncontrol_order_id = #{uncontrolOrderId,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null">
        created_by_name = #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="curName != null">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null">
        jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        dosage = #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="prodarea != null">
        prodarea = #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null">
        channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null">
        channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="guidePrice != null">
        guide_price = #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        dxs_date = #{dxsDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>


  <insert id="batchReplaceInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    replace into price_control_notice
    (control_order_id, platform_id, platform_name, business_id, business_name, store_id,
      store_name, goods_no, price_type_code, price_type_id, price_type_name, `status`,
      uncontrol_order_id, adjust_code, gmt_create, gmt_update, created_by, created_by_name,
      updated_by, updated_by_name, cur_name, manufacturer, jhi_specification, dosage,
      channel_id, channel_name, prodarea, goods_unit, goods_name, channel_out_code, channel_en_code,
      upper_limit, lower_limit, price, guide_price, dxs_date)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.controlOrderId,jdbcType=VARCHAR}, #{item.platformId,jdbcType=BIGINT}, #{item.platformName,jdbcType=VARCHAR},
        #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT},
        #{item.storeName,jdbcType=VARCHAR}, #{item.goodsNo,jdbcType=VARCHAR}, #{item.priceTypeCode,jdbcType=VARCHAR},
        #{item.priceTypeId,jdbcType=BIGINT}, #{item.priceTypeName,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT},
        #{item.uncontrolOrderId,jdbcType=VARCHAR}, #{item.adjustCode,jdbcType=VARCHAR},
        #{item.gmtCreate,jdbcType=TIMESTAMP}, #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.createdBy,jdbcType=BIGINT},
        #{item.createdByName,jdbcType=VARCHAR}, #{item.updatedBy,jdbcType=BIGINT}, #{item.updatedByName,jdbcType=VARCHAR},
        #{item.curName,jdbcType=VARCHAR}, #{item.manufacturer,jdbcType=VARCHAR}, #{item.jhiSpecification,jdbcType=VARCHAR},
        #{item.dosage,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER}, #{item.channelName,jdbcType=INTEGER},
        #{item.prodarea,jdbcType=VARCHAR}, #{item.goodsUnit,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
        #{item.channelOutCode,jdbcType=VARCHAR}, #{item.channelEnCode,jdbcType=VARCHAR},
        #{item.upperLimit,jdbcType=DECIMAL}, #{item.lowerLimit,jdbcType=DECIMAL}, #{item.price,jdbcType=DECIMAL},
        #{item.guidePrice,jdbcType=DECIMAL}, #{item.dxsDate,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
</mapper>
