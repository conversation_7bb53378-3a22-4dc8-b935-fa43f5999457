<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceManageControlOrderMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceManageControlOrder">
    <!--@mbg.generated-->
    <!--@Table price_manage_control_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="control_order_id" jdbcType="VARCHAR" property="controlOrderId" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="order_level" jdbcType="TINYINT" property="orderLevel" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="control_reason" jdbcType="VARCHAR" property="controlReason" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_by_name" jdbcType="VARCHAR" property="createdByName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="audit_by" jdbcType="BIGINT" property="auditBy" />
    <result column="audit_by_name" jdbcType="VARCHAR" property="auditByName" />
    <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
    <result column="audit_reason" jdbcType="VARCHAR" property="auditReason" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="price_type" jdbcType="VARCHAR" property="priceType" />
    <result column="control_type" jdbcType="INTEGER" property="controlType" />
    <result column="effect_time" jdbcType="TIMESTAMP" property="effectTime" />
    <result column="scheduled_time" jdbcType="TIMESTAMP" property="scheduledTime" />
    <result column="exec_status" jdbcType="INTEGER" property="execStatus" />
    <result column="effect_status" jdbcType="VARCHAR" property="effectStatus" />
    <result column="control_order_type" jdbcType="INTEGER" property="controlOrderType" />
    <result column="control_no_exec_reason" jdbcType="VARCHAR" property="controlNoExecReason" />
    <result column="control_order_name" jdbcType="VARCHAR" property="controlOrderName" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, control_order_id, org_id, org_name, order_level, audit_status, control_reason,
    `status`, gmt_create, gmt_update, extend, version, created_by, created_by_name, updated_by,
    updated_by_name, audit_by, audit_by_name, audit_date, audit_reason, channel, price_type,
    control_type, effect_time, scheduled_time, exec_status, effect_status, control_order_type,
    control_no_exec_reason, control_order_name, order_type
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_manage_control_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
      <if test="limit != null">
          <if test="offset != null">
              limit ${offset}, ${limit}
          </if>
          <if test="offset == null">
              limit ${limit}
          </if>
      </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_manage_control_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from price_manage_control_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderExample">
    <!--@mbg.generated-->
    delete from price_manage_control_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_order (control_order_id, org_id, org_name,
      order_level, audit_status, control_reason,
      `status`, gmt_create, gmt_update,
      extend, version, created_by,
      created_by_name, updated_by, updated_by_name,
      audit_by, audit_by_name, audit_date,
      audit_reason, channel, price_type,
      control_type, effect_time, scheduled_time,
      exec_status, effect_status, control_order_type,
      control_no_exec_reason, control_order_name, order_type)
    values (#{controlOrderId,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR},
      #{orderLevel,jdbcType=TINYINT}, #{auditStatus,jdbcType=TINYINT}, #{controlReason,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP},
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT},
      #{createdByName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR},
      #{auditBy,jdbcType=BIGINT}, #{auditByName,jdbcType=VARCHAR}, #{auditDate,jdbcType=TIMESTAMP},
      #{auditReason,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, #{priceType,jdbcType=VARCHAR},
      #{controlType,jdbcType=INTEGER}, #{effectTime,jdbcType=TIMESTAMP}, #{scheduledTime,jdbcType=TIMESTAMP},
      #{execStatus,jdbcType=INTEGER}, #{effectStatus,jdbcType=VARCHAR}, #{controlOrderType,jdbcType=INTEGER},
      #{controlNoExecReason,jdbcType=VARCHAR}, #{controlOrderName,jdbcType=VARCHAR}, #{orderType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrder" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="controlOrderId != null and controlOrderId != ''">
        control_order_id,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null and orgName != ''">
        org_name,
      </if>
      <if test="orderLevel != null">
        order_level,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="controlReason != null and controlReason != ''">
        control_reason,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null and extend != ''">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdByName != null and createdByName != ''">
        created_by_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name,
      </if>
      <if test="auditBy != null">
        audit_by,
      </if>
      <if test="auditByName != null and auditByName != ''">
        audit_by_name,
      </if>
      <if test="auditDate != null">
        audit_date,
      </if>
      <if test="auditReason != null and auditReason != ''">
        audit_reason,
      </if>
      <if test="channel != null and channel != ''">
        channel,
      </if>
      <if test="priceType != null and priceType != ''">
        price_type,
      </if>
      <if test="controlType != null">
        control_type,
      </if>
      <if test="effectTime != null">
        effect_time,
      </if>
      <if test="scheduledTime != null">
        scheduled_time,
      </if>
      <if test="execStatus != null">
        exec_status,
      </if>
      <if test="effectStatus != null and effectStatus != ''">
        effect_status,
      </if>
      <if test="controlOrderType != null">
        control_order_type,
      </if>
      <if test="controlNoExecReason != null and controlNoExecReason != ''">
        control_no_exec_reason,
      </if>
        <if test="controlOrderName != null and controlOrderName != ''">
            control_order_name,
        </if>
        <if test="orderType != null">
            order_type,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="controlOrderId != null and controlOrderId != ''">
        #{controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null and orgName != ''">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orderLevel != null">
        #{orderLevel,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="controlReason != null and controlReason != ''">
        #{controlReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null and extend != ''">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null and createdByName != ''">
        #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="auditBy != null">
        #{auditBy,jdbcType=BIGINT},
      </if>
      <if test="auditByName != null and auditByName != ''">
        #{auditByName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null">
        #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="auditReason != null and auditReason != ''">
        #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="channel != null and channel != ''">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="priceType != null and priceType != ''">
        #{priceType,jdbcType=VARCHAR},
      </if>
      <if test="controlType != null">
        #{controlType,jdbcType=INTEGER},
      </if>
      <if test="effectTime != null">
        #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduledTime != null">
        #{scheduledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="effectStatus != null and effectStatus != ''">
        #{effectStatus,jdbcType=VARCHAR},
      </if>
      <if test="controlOrderType != null">
        #{controlOrderType,jdbcType=INTEGER},
      </if>
      <if test="controlNoExecReason != null and controlNoExecReason != ''">
        #{controlNoExecReason,jdbcType=VARCHAR},
      </if>
        <if test="controlOrderName != null and controlOrderName != ''">
            #{controlOrderName,jdbcType=VARCHAR},
        </if>
        <if test="orderType != null">
            #{orderType,jdbcType=TINYINT},
        </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrderExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_manage_control_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_manage_control_order
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.controlOrderId != null">
        control_order_id = #{record.controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderLevel != null">
        order_level = #{record.orderLevel,jdbcType=TINYINT},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.controlReason != null">
        control_reason = #{record.controlReason,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdByName != null">
        created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditBy != null">
        audit_by = #{record.auditBy,jdbcType=BIGINT},
      </if>
      <if test="record.auditByName != null">
        audit_by_name = #{record.auditByName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditDate != null">
        audit_date = #{record.auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditReason != null">
        audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.priceType != null">
        price_type = #{record.priceType,jdbcType=VARCHAR},
      </if>
      <if test="record.controlType != null">
        control_type = #{record.controlType,jdbcType=INTEGER},
      </if>
      <if test="record.effectTime != null">
        effect_time = #{record.effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.scheduledTime != null">
        scheduled_time = #{record.scheduledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.execStatus != null">
        exec_status = #{record.execStatus,jdbcType=INTEGER},
      </if>
      <if test="record.effectStatus != null">
        effect_status = #{record.effectStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.controlOrderType != null">
        control_order_type = #{record.controlOrderType,jdbcType=INTEGER},
      </if>
      <if test="record.controlNoExecReason != null">
        control_no_exec_reason = #{record.controlNoExecReason,jdbcType=VARCHAR},
      </if>
        <if test="record.controlOrderName != null">
            control_order_name = #{record.controlOrderName,jdbcType=VARCHAR},
        </if>
        <if test="record.orderType != null">
            order_type = #{record.orderType,jdbcType=TINYINT},
        </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_manage_control_order
    set id = #{record.id,jdbcType=BIGINT},
      control_order_id = #{record.controlOrderId,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      order_level = #{record.orderLevel,jdbcType=TINYINT},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      control_reason = #{record.controlReason,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      audit_by = #{record.auditBy,jdbcType=BIGINT},
      audit_by_name = #{record.auditByName,jdbcType=VARCHAR},
      audit_date = #{record.auditDate,jdbcType=TIMESTAMP},
      audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      price_type = #{record.priceType,jdbcType=VARCHAR},
      control_type = #{record.controlType,jdbcType=INTEGER},
      effect_time = #{record.effectTime,jdbcType=TIMESTAMP},
      scheduled_time = #{record.scheduledTime,jdbcType=TIMESTAMP},
      exec_status = #{record.execStatus,jdbcType=INTEGER},
      effect_status = #{record.effectStatus,jdbcType=VARCHAR},
      control_order_type = #{record.controlOrderType,jdbcType=INTEGER},
      control_no_exec_reason = #{record.controlNoExecReason,jdbcType=VARCHAR},
      control_order_name = #{record.controlOrderName,jdbcType=VARCHAR},
      order_type = #{record.orderType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrder">
    <!--@mbg.generated-->
    update price_manage_control_order
    <set>
      <if test="controlOrderId != null and controlOrderId != ''">
        control_order_id = #{controlOrderId,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null and orgName != ''">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="orderLevel != null">
        order_level = #{orderLevel,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="controlReason != null and controlReason != ''">
        control_reason = #{controlReason,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null and extend != ''">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null and createdByName != ''">
        created_by_name = #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="auditBy != null">
        audit_by = #{auditBy,jdbcType=BIGINT},
      </if>
      <if test="auditByName != null and auditByName != ''">
        audit_by_name = #{auditByName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null">
        audit_date = #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="auditReason != null and auditReason != ''">
        audit_reason = #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="channel != null and channel != ''">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="priceType != null and priceType != ''">
        price_type = #{priceType,jdbcType=VARCHAR},
      </if>
      <if test="controlType != null">
        control_type = #{controlType,jdbcType=INTEGER},
      </if>
      <if test="effectTime != null">
        effect_time = #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduledTime != null">
        scheduled_time = #{scheduledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        exec_status = #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="effectStatus != null and effectStatus != ''">
        effect_status = #{effectStatus,jdbcType=VARCHAR},
      </if>
      <if test="controlOrderType != null">
        control_order_type = #{controlOrderType,jdbcType=INTEGER},
      </if>
      <if test="controlNoExecReason != null and controlNoExecReason != ''">
        control_no_exec_reason = #{controlNoExecReason,jdbcType=VARCHAR},
      </if>
        <if test="controlOrderName != null and controlOrderName != ''">
            control_order_name = #{controlOrderName,jdbcType=VARCHAR},
        </if>
        <if test="orderType != null">
            order_type = #{orderType,jdbcType=TINYINT},
        </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceManageControlOrder">
    <!--@mbg.generated-->
    update price_manage_control_order
    set control_order_id = #{controlOrderId,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      order_level = #{orderLevel,jdbcType=TINYINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      control_reason = #{controlReason,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_by_name = #{createdByName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      audit_by = #{auditBy,jdbcType=BIGINT},
      audit_by_name = #{auditByName,jdbcType=VARCHAR},
      audit_date = #{auditDate,jdbcType=TIMESTAMP},
      audit_reason = #{auditReason,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      price_type = #{priceType,jdbcType=VARCHAR},
      control_type = #{controlType,jdbcType=INTEGER},
      effect_time = #{effectTime,jdbcType=TIMESTAMP},
      scheduled_time = #{scheduledTime,jdbcType=TIMESTAMP},
      exec_status = #{execStatus,jdbcType=INTEGER},
      effect_status = #{effectStatus,jdbcType=VARCHAR},
      control_order_type = #{controlOrderType,jdbcType=INTEGER},
      control_no_exec_reason = #{controlNoExecReason,jdbcType=VARCHAR},
      control_order_name = #{controlOrderName,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


   <!--批量更新管控单状态-->
  <update id="batchUpdateControlOrderEffectStatus" parameterType="map">
    update price_manage_control_order
        set effect_status = #{effectStatus,jdbcType=INTEGER}
            where
        <if test="controlCodes != null and controlCodes.size() != 0">
                control_order_id in
            <foreach collection="controlCodes" index="index" item="item" open="(" separator="," close=")">
            #{item}
            </foreach>
        </if>
         and effect_status = 0

  </update>
</mapper>
