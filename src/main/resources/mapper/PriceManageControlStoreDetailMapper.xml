<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceManageControlStoreDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceManageControlStoreDetail">
    <!--@mbg.generated-->
    <!--@Table price_manage_control_store_detail-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="control_store_detail_id" jdbcType="VARCHAR" property="controlStoreDetailId" />
    <result column="control_order_code" jdbcType="VARCHAR" property="controlOrderCode" />
    <result column="auth_org_id" jdbcType="BIGINT" property="authOrgId" />
    <result column="auth_org_name" jdbcType="VARCHAR" property="authOrgName" />
    <result column="auth_level" jdbcType="TINYINT" property="authLevel" />
    <result column="org_goods_id" jdbcType="BIGINT" property="orgGoodsId" />
    <result column="spu_id" jdbcType="BIGINT" property="spuId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="price_group_names" jdbcType="VARCHAR" property="priceGroupNames" />
    <result column="price_type_id" jdbcType="BIGINT" property="priceTypeId" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
    <result column="price_type_out_code" jdbcType="VARCHAR" property="priceTypeOutCode" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="original_price" jdbcType="DECIMAL" property="originalPrice" />
    <result column="upper_limit" jdbcType="DECIMAL" property="upperLimit" />
    <result column="lower_limit" jdbcType="DECIMAL" property="lowerLimit" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="jhi_specification" jdbcType="VARCHAR" property="jhiSpecification" />
    <result column="dosage" jdbcType="VARCHAR" property="dosage" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="extend1" jdbcType="VARCHAR" property="extend1" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="prodarea" jdbcType="VARCHAR" property="prodarea" />
    <result column="goods_unit" jdbcType="VARCHAR" property="goodsUnit" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="channel_out_code" jdbcType="VARCHAR" property="channelOutCode" />
    <result column="channel_en_code" jdbcType="VARCHAR" property="channelEnCode" />
    <result column="guide_price" jdbcType="DECIMAL" property="guidePrice" />
    <result column="dxs_date" jdbcType="TIMESTAMP" property="dxsDate" />
    <result column="effect_status" jdbcType="INTEGER" property="effectStatus" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="control_order_audit_time" jdbcType="TIMESTAMP" property="controlOrderAuditTime" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, control_store_detail_id, control_order_code, auth_org_id, auth_org_name, auth_level, org_goods_id, spu_id,
    goods_no, price_group_names, price_type_id, price_type_code, price_type_name, price_type_out_code,
    price, original_price, upper_limit, lower_limit, cur_name, manufacturer, jhi_specification,
    dosage, `status`, gmt_create, gmt_update, extend, extend1, version, created_by, updated_by,
    updated_by_name, channel_id, prodarea, goods_unit, goods_name, channel_out_code,
    channel_en_code, guide_price, dxs_date, effect_status, business_id, business_name,
    store_name, store_id,control_order_audit_time, order_type
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetailExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_manage_control_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_manage_control_store_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from price_manage_control_store_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetailExample">
    <!--@mbg.generated-->
    delete from price_manage_control_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_store_detail (control_order_code, auth_org_id, auth_org_name,
      auth_level, org_goods_id, spu_id,
      goods_no, price_group_names, price_type_id,
      price_type_code, price_type_name, price_type_out_code,
      price, original_price, upper_limit,
      lower_limit, cur_name, manufacturer,
      jhi_specification, dosage, `status`,
      gmt_create, gmt_update, extend,
      extend1, version, created_by,
      updated_by, updated_by_name, channel_id,
      prodarea, goods_unit, goods_name,
      channel_out_code, channel_en_code, guide_price,
      dxs_date, effect_status, business_id,
      business_name, store_name, store_id,control_order_audit_time, order_type
      )
    values (#{controlOrderCode,jdbcType=VARCHAR}, #{authOrgId,jdbcType=BIGINT}, #{authOrgName,jdbcType=VARCHAR},
      #{authLevel,jdbcType=TINYINT}, #{orgGoodsId,jdbcType=BIGINT}, #{spuId,jdbcType=BIGINT},
      #{goodsNo,jdbcType=VARCHAR}, #{priceGroupNames,jdbcType=VARCHAR}, #{priceTypeId,jdbcType=BIGINT},
      #{priceTypeCode,jdbcType=VARCHAR}, #{priceTypeName,jdbcType=VARCHAR}, #{priceTypeOutCode,jdbcType=VARCHAR},
      #{price,jdbcType=DECIMAL}, #{originalPrice,jdbcType=DECIMAL}, #{upperLimit,jdbcType=DECIMAL},
      #{lowerLimit,jdbcType=DECIMAL}, #{curName,jdbcType=VARCHAR}, #{manufacturer,jdbcType=VARCHAR},
      #{jhiSpecification,jdbcType=VARCHAR}, #{dosage,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR},
      #{extend1,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT},
      #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}, #{channelId,jdbcType=INTEGER},
      #{prodarea,jdbcType=VARCHAR}, #{goodsUnit,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR},
      #{channelOutCode,jdbcType=VARCHAR}, #{channelEnCode,jdbcType=VARCHAR}, #{guidePrice,jdbcType=DECIMAL},
      #{dxsDate,jdbcType=TIMESTAMP}, #{effectStatus,jdbcType=INTEGER}, #{businessId,jdbcType=BIGINT},
      #{businessName,jdbcType=VARCHAR}, #{storeName,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT},
      #{controlOrderAuditTime,jdbcType=TIMESTAMP}, #{orderType,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_store_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="controlOrderCode != null and controlOrderCode != ''">
        control_order_code,
      </if>
      <if test="authOrgId != null">
        auth_org_id,
      </if>
      <if test="authOrgName != null and authOrgName != ''">
        auth_org_name,
      </if>
      <if test="authLevel != null">
        auth_level,
      </if>
      <if test="orgGoodsId != null">
        org_goods_id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        goods_no,
      </if>
      <if test="priceGroupNames != null and priceGroupNames != ''">
        price_group_names,
      </if>
      <if test="priceTypeId != null">
        price_type_id,
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        price_type_code,
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        price_type_name,
      </if>
      <if test="priceTypeOutCode != null and priceTypeOutCode != ''">
        price_type_out_code,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="lowerLimit != null">
        lower_limit,
      </if>
      <if test="curName != null and curName != ''">
        cur_name,
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        manufacturer,
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        jhi_specification,
      </if>
      <if test="dosage != null and dosage != ''">
        dosage,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null and extend != ''">
        extend,
      </if>
      <if test="extend1 != null and extend1 != ''">
        extend1,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="prodarea != null and prodarea != ''">
        prodarea,
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        goods_unit,
      </if>
      <if test="goodsName != null and goodsName != ''">
        goods_name,
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        channel_out_code,
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code,
      </if>
      <if test="guidePrice != null">
        guide_price,
      </if>
      <if test="dxsDate != null">
        dxs_date,
      </if>
      <if test="effectStatus != null">
        effect_status,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null and businessName != ''">
        business_name,
      </if>
      <if test="storeName != null and storeName != ''">
        store_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="controlOrderCode != null and controlOrderCode != ''">
        #{controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="authOrgId != null">
        #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null and authOrgName != ''">
        #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authLevel != null">
        #{authLevel,jdbcType=TINYINT},
      </if>
      <if test="orgGoodsId != null">
        #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceGroupNames != null and priceGroupNames != ''">
        #{priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeOutCode != null and priceTypeOutCode != ''">
        #{priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="curName != null and curName != ''">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null and dosage != ''">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null and extend != ''">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="extend1 != null and extend1 != ''">
        #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null and prodarea != ''">
        #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        #{dxsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectStatus != null">
        #{effectStatus,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null and businessName != ''">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null and storeName != ''">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetailExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_manage_control_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_manage_control_store_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.controlOrderCode != null">
        control_order_code = #{record.controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="record.authOrgId != null">
        auth_org_id = #{record.authOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.authOrgName != null">
        auth_org_name = #{record.authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.authLevel != null">
        auth_level = #{record.authLevel,jdbcType=TINYINT},
      </if>
      <if test="record.orgGoodsId != null">
        org_goods_id = #{record.orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.priceGroupNames != null">
        price_group_names = #{record.priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeId != null">
        price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.priceTypeCode != null">
        price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeName != null">
        price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeOutCode != null">
        price_type_out_code = #{record.priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.originalPrice != null">
        original_price = #{record.originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.upperLimit != null">
        upper_limit = #{record.upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.lowerLimit != null">
        lower_limit = #{record.lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.curName != null">
        cur_name = #{record.curName,jdbcType=VARCHAR},
      </if>
      <if test="record.manufacturer != null">
        manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="record.jhiSpecification != null">
        jhi_specification = #{record.jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="record.dosage != null">
        dosage = #{record.dosage,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.extend1 != null">
        extend1 = #{record.extend1,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.prodarea != null">
        prodarea = #{record.prodarea,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsUnit != null">
        goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelOutCode != null">
        channel_out_code = #{record.channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="record.channelEnCode != null">
        channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="record.guidePrice != null">
        guide_price = #{record.guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="record.dxsDate != null">
        dxs_date = #{record.dxsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.effectStatus != null">
        effect_status = #{record.effectStatus,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.controlOrderAuditTime != null">
        control_order_audit_time = #{record.controlOrderAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_manage_control_store_detail
    set id = #{record.id,jdbcType=BIGINT},
      control_order_code = #{record.controlOrderCode,jdbcType=VARCHAR},
      auth_org_id = #{record.authOrgId,jdbcType=BIGINT},
      auth_org_name = #{record.authOrgName,jdbcType=VARCHAR},
      auth_level = #{record.authLevel,jdbcType=TINYINT},
      org_goods_id = #{record.orgGoodsId,jdbcType=BIGINT},
      spu_id = #{record.spuId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      price_group_names = #{record.priceGroupNames,jdbcType=VARCHAR},
      price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      price_type_out_code = #{record.priceTypeOutCode,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      original_price = #{record.originalPrice,jdbcType=DECIMAL},
      upper_limit = #{record.upperLimit,jdbcType=DECIMAL},
      lower_limit = #{record.lowerLimit,jdbcType=DECIMAL},
      cur_name = #{record.curName,jdbcType=VARCHAR},
      manufacturer = #{record.manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{record.jhiSpecification,jdbcType=VARCHAR},
      dosage = #{record.dosage,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      extend1 = #{record.extend1,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      prodarea = #{record.prodarea,jdbcType=VARCHAR},
      goods_unit = #{record.goodsUnit,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      channel_out_code = #{record.channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      guide_price = #{record.guidePrice,jdbcType=DECIMAL},
      dxs_date = #{record.dxsDate,jdbcType=TIMESTAMP},
      effect_status = #{record.effectStatus,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      control_order_audit_time = #{record.controlOrderAuditTime,jdbcType=TIMESTAMP},
      order_type = #{record.orderType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetail">
    <!--@mbg.generated-->
    update price_manage_control_store_detail
    <set>
      <if test="controlOrderCode != null and controlOrderCode != ''">
        control_order_code = #{controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="authOrgId != null">
        auth_org_id = #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null and authOrgName != ''">
        auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authLevel != null">
        auth_level = #{authLevel,jdbcType=TINYINT},
      </if>
      <if test="orgGoodsId != null">
        org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null and goodsNo != ''">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceGroupNames != null and priceGroupNames != ''">
        price_group_names = #{priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        price_type_id = #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeCode != null and priceTypeCode != ''">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null and priceTypeName != ''">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeOutCode != null and priceTypeOutCode != ''">
        price_type_out_code = #{priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="curName != null and curName != ''">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null and manufacturer != ''">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null and jhiSpecification != ''">
        jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null and dosage != ''">
        dosage = #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null and extend != ''">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="extend1 != null and extend1 != ''">
        extend1 = #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null and updatedByName != ''">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null and prodarea != ''">
        prodarea = #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null and goodsUnit != ''">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null and goodsName != ''">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null and channelOutCode != ''">
        channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        guide_price = #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        dxs_date = #{dxsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectStatus != null">
        effect_status = #{effectStatus,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null and businessName != ''">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null and storeName != ''">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="controlOrderAuditTime != null">
        control_order_audit_time = #{controlOrderAuditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetail">
    <!--@mbg.generated-->
    update price_manage_control_store_detail
    set control_order_code = #{controlOrderCode,jdbcType=VARCHAR},
      auth_org_id = #{authOrgId,jdbcType=BIGINT},
      auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      auth_level = #{authLevel,jdbcType=TINYINT},
      org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      spu_id = #{spuId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      price_group_names = #{priceGroupNames,jdbcType=VARCHAR},
      price_type_id = #{priceTypeId,jdbcType=BIGINT},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      price_type_out_code = #{priceTypeOutCode,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      original_price = #{originalPrice,jdbcType=DECIMAL},
      upper_limit = #{upperLimit,jdbcType=DECIMAL},
      lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      cur_name = #{curName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      dosage = #{dosage,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      extend1 = #{extend1,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=INTEGER},
      prodarea = #{prodarea,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      guide_price = #{guidePrice,jdbcType=DECIMAL},
      dxs_date = #{dxsDate,jdbcType=TIMESTAMP},
      effect_status = #{effectStatus,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      control_order_audit_time = #{controlOrderAuditTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=TINYINT},
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_manage_control_store_detail
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="control_order_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.controlOrderCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="auth_org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.authOrgId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="auth_org_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.authOrgName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="auth_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.authLevel,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="org_goods_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.orgGoodsId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="spu_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.spuId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="goods_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.goodsNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_group_names = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceGroupNames,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_type_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="price_type_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price_type_out_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeOutCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.price,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="original_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.originalPrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="upper_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.upperLimit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="lower_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.lowerLimit,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="cur_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.curName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="manufacturer = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.manufacturer,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="jhi_specification = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.jhiSpecification,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dosage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dosage,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="extend = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.extend,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="extend1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.extend1,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="updated_by_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updatedByName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="prodarea = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.prodarea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="goods_unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.goodsUnit,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="goods_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.goodsName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_out_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelOutCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="channel_en_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.channelEnCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="guide_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.guidePrice,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="dxs_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dxsDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="effect_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.effectStatus,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="business_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.businessName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="store_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="store_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.storeId,jdbcType=BIGINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update price_manage_control_store_detail
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="control_order_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.controlOrderCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.controlOrderCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="auth_org_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.authOrgId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.authOrgId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="auth_org_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.authOrgName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.authOrgName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="auth_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.authLevel != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.authLevel,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="org_goods_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orgGoodsId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.orgGoodsId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="spu_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.spuId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.spuId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="goods_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.goodsNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_group_names = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceGroupNames != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceGroupNames,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price_type_out_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.priceTypeOutCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.priceTypeOutCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.price != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.price,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="original_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.originalPrice != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.originalPrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="upper_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.upperLimit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.upperLimit,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="lower_limit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lowerLimit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.lowerLimit,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="cur_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.curName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.curName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="manufacturer = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.manufacturer != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.manufacturer,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="jhi_specification = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jhiSpecification != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.jhiSpecification,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dosage = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dosage != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dosage,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_create = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtCreate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtCreate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="gmt_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gmtUpdate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gmtUpdate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="extend = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extend != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.extend,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="extend1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extend1 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.extend1,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.version,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="created_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createdBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createdBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updatedBy,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="updated_by_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updatedByName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updatedByName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="prodarea = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.prodarea != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.prodarea,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="goods_unit = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsUnit != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.goodsUnit,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="goods_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.goodsName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.goodsName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_out_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelOutCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelOutCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="channel_en_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelEnCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.channelEnCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="guide_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.guidePrice != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.guidePrice,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="dxs_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dxsDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dxsDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="effect_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.effectStatus != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.effectStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="business_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="business_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.businessName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="store_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.storeId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.storeId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_store_detail
    (control_store_detail_id,control_order_code, auth_org_id, auth_org_name, auth_level, org_goods_id, spu_id,
      goods_no, price_group_names, price_type_id, price_type_code, price_type_name, price_type_out_code,
      price, original_price, upper_limit, lower_limit, cur_name, manufacturer, jhi_specification,
      dosage, `status`, gmt_create, gmt_update, extend, extend1, version, created_by,
      updated_by, updated_by_name, channel_id, prodarea, goods_unit, goods_name, channel_out_code,
      channel_en_code, guide_price, dxs_date, effect_status, business_id, business_name,
      store_name, store_id,control_order_audit_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.controlStoreDetailId,jdbcType=VARCHAR},#{item.controlOrderCode,jdbcType=VARCHAR}, #{item.authOrgId,jdbcType=BIGINT}, #{item.authOrgName,jdbcType=VARCHAR},
        #{item.authLevel,jdbcType=TINYINT}, #{item.orgGoodsId,jdbcType=BIGINT}, #{item.spuId,jdbcType=BIGINT},
        #{item.goodsNo,jdbcType=VARCHAR}, #{item.priceGroupNames,jdbcType=VARCHAR}, #{item.priceTypeId,jdbcType=BIGINT},
        #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.priceTypeName,jdbcType=VARCHAR},
        #{item.priceTypeOutCode,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, #{item.originalPrice,jdbcType=DECIMAL},
        #{item.upperLimit,jdbcType=DECIMAL}, #{item.lowerLimit,jdbcType=DECIMAL}, #{item.curName,jdbcType=VARCHAR},
        #{item.manufacturer,jdbcType=VARCHAR}, #{item.jhiSpecification,jdbcType=VARCHAR},
        #{item.dosage,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=TIMESTAMP},
        #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR}, #{item.extend1,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT}, #{item.updatedBy,jdbcType=BIGINT},
        #{item.updatedByName,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER}, #{item.prodarea,jdbcType=VARCHAR},
        #{item.goodsUnit,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.channelOutCode,jdbcType=VARCHAR},
        #{item.channelEnCode,jdbcType=VARCHAR}, #{item.guidePrice,jdbcType=DECIMAL}, #{item.dxsDate,jdbcType=TIMESTAMP},
        #{item.effectStatus,jdbcType=INTEGER}, #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR},
        #{item.storeName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT}, #{item.controlOrderAuditTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_store_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      control_order_code,
      auth_org_id,
      auth_org_name,
      auth_level,
      org_goods_id,
      spu_id,
      goods_no,
      price_group_names,
      price_type_id,
      price_type_code,
      price_type_name,
      price_type_out_code,
      price,
      original_price,
      upper_limit,
      lower_limit,
      cur_name,
      manufacturer,
      jhi_specification,
      dosage,
      `status`,
      gmt_create,
      gmt_update,
      extend,
      extend1,
      version,
      created_by,
      updated_by,
      updated_by_name,
      channel_id,
      prodarea,
      goods_unit,
      goods_name,
      channel_out_code,
      channel_en_code,
      guide_price,
      dxs_date,
      effect_status,
      business_id,
      business_name,
      store_name,
      store_id,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{controlOrderCode,jdbcType=VARCHAR},
      #{authOrgId,jdbcType=BIGINT},
      #{authOrgName,jdbcType=VARCHAR},
      #{authLevel,jdbcType=TINYINT},
      #{orgGoodsId,jdbcType=BIGINT},
      #{spuId,jdbcType=BIGINT},
      #{goodsNo,jdbcType=VARCHAR},
      #{priceGroupNames,jdbcType=VARCHAR},
      #{priceTypeId,jdbcType=BIGINT},
      #{priceTypeCode,jdbcType=VARCHAR},
      #{priceTypeName,jdbcType=VARCHAR},
      #{priceTypeOutCode,jdbcType=VARCHAR},
      #{price,jdbcType=DECIMAL},
      #{originalPrice,jdbcType=DECIMAL},
      #{upperLimit,jdbcType=DECIMAL},
      #{lowerLimit,jdbcType=DECIMAL},
      #{curName,jdbcType=VARCHAR},
      #{manufacturer,jdbcType=VARCHAR},
      #{jhiSpecification,jdbcType=VARCHAR},
      #{dosage,jdbcType=VARCHAR},
      #{status,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP},
      #{extend,jdbcType=VARCHAR},
      #{extend1,jdbcType=VARCHAR},
      #{version,jdbcType=INTEGER},
      #{createdBy,jdbcType=BIGINT},
      #{updatedBy,jdbcType=BIGINT},
      #{updatedByName,jdbcType=VARCHAR},
      #{channelId,jdbcType=INTEGER},
      #{prodarea,jdbcType=VARCHAR},
      #{goodsUnit,jdbcType=VARCHAR},
      #{goodsName,jdbcType=VARCHAR},
      #{channelOutCode,jdbcType=VARCHAR},
      #{channelEnCode,jdbcType=VARCHAR},
      #{guidePrice,jdbcType=DECIMAL},
      #{dxsDate,jdbcType=TIMESTAMP},
      #{effectStatus,jdbcType=INTEGER},
      #{businessId,jdbcType=BIGINT},
      #{businessName,jdbcType=VARCHAR},
      #{storeName,jdbcType=VARCHAR},
      #{storeId,jdbcType=BIGINT},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      control_order_code = #{controlOrderCode,jdbcType=VARCHAR},
      auth_org_id = #{authOrgId,jdbcType=BIGINT},
      auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      auth_level = #{authLevel,jdbcType=TINYINT},
      org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      spu_id = #{spuId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      price_group_names = #{priceGroupNames,jdbcType=VARCHAR},
      price_type_id = #{priceTypeId,jdbcType=BIGINT},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      price_type_out_code = #{priceTypeOutCode,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      original_price = #{originalPrice,jdbcType=DECIMAL},
      upper_limit = #{upperLimit,jdbcType=DECIMAL},
      lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      cur_name = #{curName,jdbcType=VARCHAR},
      manufacturer = #{manufacturer,jdbcType=VARCHAR},
      jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      dosage = #{dosage,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      extend1 = #{extend1,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=INTEGER},
      prodarea = #{prodarea,jdbcType=VARCHAR},
      goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      guide_price = #{guidePrice,jdbcType=DECIMAL},
      dxs_date = #{dxsDate,jdbcType=TIMESTAMP},
      effect_status = #{effectStatus,jdbcType=INTEGER},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_manage_control_store_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="controlOrderCode != null">
        control_order_code,
      </if>
      <if test="authOrgId != null">
        auth_org_id,
      </if>
      <if test="authOrgName != null">
        auth_org_name,
      </if>
      <if test="authLevel != null">
        auth_level,
      </if>
      <if test="orgGoodsId != null">
        org_goods_id,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="priceGroupNames != null">
        price_group_names,
      </if>
      <if test="priceTypeId != null">
        price_type_id,
      </if>
      <if test="priceTypeCode != null">
        price_type_code,
      </if>
      <if test="priceTypeName != null">
        price_type_name,
      </if>
      <if test="priceTypeOutCode != null">
        price_type_out_code,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="originalPrice != null">
        original_price,
      </if>
      <if test="upperLimit != null">
        upper_limit,
      </if>
      <if test="lowerLimit != null">
        lower_limit,
      </if>
      <if test="curName != null">
        cur_name,
      </if>
      <if test="manufacturer != null">
        manufacturer,
      </if>
      <if test="jhiSpecification != null">
        jhi_specification,
      </if>
      <if test="dosage != null">
        dosage,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="extend1 != null">
        extend1,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="prodarea != null">
        prodarea,
      </if>
      <if test="goodsUnit != null">
        goods_unit,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="channelOutCode != null">
        channel_out_code,
      </if>
      <if test="channelEnCode != null">
        channel_en_code,
      </if>
      <if test="guidePrice != null">
        guide_price,
      </if>
      <if test="dxsDate != null">
        dxs_date,
      </if>
      <if test="effectStatus != null">
        effect_status,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="controlOrderCode != null">
        #{controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="authOrgId != null">
        #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null">
        #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authLevel != null">
        #{authLevel,jdbcType=TINYINT},
      </if>
      <if test="orgGoodsId != null">
        #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceGroupNames != null">
        #{priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeCode != null">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeOutCode != null">
        #{priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="curName != null">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null">
        #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="extend1 != null">
        #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null">
        #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null">
        #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null">
        #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        #{dxsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectStatus != null">
        #{effectStatus,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="controlOrderCode != null">
        control_order_code = #{controlOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="authOrgId != null">
        auth_org_id = #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null">
        auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authLevel != null">
        auth_level = #{authLevel,jdbcType=TINYINT},
      </if>
      <if test="orgGoodsId != null">
        org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceGroupNames != null">
        price_group_names = #{priceGroupNames,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        price_type_id = #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeCode != null">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeOutCode != null">
        price_type_out_code = #{priceTypeOutCode,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="originalPrice != null">
        original_price = #{originalPrice,jdbcType=DECIMAL},
      </if>
      <if test="upperLimit != null">
        upper_limit = #{upperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lowerLimit != null">
        lower_limit = #{lowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="curName != null">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="manufacturer != null">
        manufacturer = #{manufacturer,jdbcType=VARCHAR},
      </if>
      <if test="jhiSpecification != null">
        jhi_specification = #{jhiSpecification,jdbcType=VARCHAR},
      </if>
      <if test="dosage != null">
        dosage = #{dosage,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="extend1 != null">
        extend1 = #{extend1,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="prodarea != null">
        prodarea = #{prodarea,jdbcType=VARCHAR},
      </if>
      <if test="goodsUnit != null">
        goods_unit = #{goodsUnit,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="channelOutCode != null">
        channel_out_code = #{channelOutCode,jdbcType=VARCHAR},
      </if>
      <if test="channelEnCode != null">
        channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="guidePrice != null">
        guide_price = #{guidePrice,jdbcType=DECIMAL},
      </if>
      <if test="dxsDate != null">
        dxs_date = #{dxsDate,jdbcType=TIMESTAMP},
      </if>
      <if test="effectStatus != null">
        effect_status = #{effectStatus,jdbcType=INTEGER},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>

  <insert id="batchReplaceInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    replace into price_manage_control_store_detail
    (control_order_code, auth_org_id, auth_org_name, auth_level, org_goods_id, spu_id,
      goods_no, price_group_names, price_type_id, price_type_code, price_type_name, price_type_out_code,
      price, original_price, upper_limit, lower_limit, cur_name, manufacturer, jhi_specification,
      dosage, `status`, gmt_create, gmt_update, extend, extend1, version, created_by,
      updated_by, updated_by_name, channel_id, prodarea, goods_unit, goods_name, channel_out_code,
      channel_en_code, guide_price, dxs_date, effect_status, business_id, business_name,
      store_name, store_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.controlOrderCode,jdbcType=VARCHAR}, #{item.authOrgId,jdbcType=BIGINT}, #{item.authOrgName,jdbcType=VARCHAR},
        #{item.authLevel,jdbcType=TINYINT}, #{item.orgGoodsId,jdbcType=BIGINT}, #{item.spuId,jdbcType=BIGINT},
        #{item.goodsNo,jdbcType=VARCHAR}, #{item.priceGroupNames,jdbcType=VARCHAR}, #{item.priceTypeId,jdbcType=BIGINT},
        #{item.priceTypeCode,jdbcType=VARCHAR}, #{item.priceTypeName,jdbcType=VARCHAR},
        #{item.priceTypeOutCode,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, #{item.originalPrice,jdbcType=DECIMAL},
        #{item.upperLimit,jdbcType=DECIMAL}, #{item.lowerLimit,jdbcType=DECIMAL}, #{item.curName,jdbcType=VARCHAR},
        #{item.manufacturer,jdbcType=VARCHAR}, #{item.jhiSpecification,jdbcType=VARCHAR},
        #{item.dosage,jdbcType=VARCHAR}, #{item.status,jdbcType=TINYINT}, #{item.gmtCreate,jdbcType=TIMESTAMP},
        #{item.gmtUpdate,jdbcType=TIMESTAMP}, #{item.extend,jdbcType=VARCHAR}, #{item.extend1,jdbcType=VARCHAR},
        #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=BIGINT}, #{item.updatedBy,jdbcType=BIGINT},
        #{item.updatedByName,jdbcType=VARCHAR}, #{item.channelId,jdbcType=INTEGER}, #{item.prodarea,jdbcType=VARCHAR},
        #{item.goodsUnit,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.channelOutCode,jdbcType=VARCHAR},
        #{item.channelEnCode,jdbcType=VARCHAR}, #{item.guidePrice,jdbcType=DECIMAL}, #{item.dxsDate,jdbcType=TIMESTAMP},
        #{item.effectStatus,jdbcType=INTEGER}, #{item.businessId,jdbcType=BIGINT}, #{item.businessName,jdbcType=VARCHAR},
        #{item.storeName,jdbcType=VARCHAR}, #{item.storeId,jdbcType=BIGINT})
    </foreach>
  </insert>


    <sql id="Simple_Column_List">
        <!--@mbg.generated-->
        id, control_order_code,goods_no, price_type_id, price_type_code, price_type_name, price_type_out_code,
        price, original_price, upper_limit, lower_limit, channel_id,channel_out_code,
        channel_en_code, dxs_date, effect_status,store_name, store_id,business_id,extend1,control_order_audit_time, order_type
      </sql>

    <!--游标批量查询操作，方式数据过多，深度分页的话会oom-->
  <select id="cursorScanControlStoreDetail" parameterType="com.cowell.pricecenter.entity.PriceManageControlStoreDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Simple_Column_List" />
    from price_manage_control_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>


  <update id="batchUpdateControlStoreDetailStatus" parameterType="map">
    update price_manage_control_store_detail
        set effect_status = #{effectStatus,jdbcType=INTEGER}, gmt_update=NOW()
            where
        <if test="ids != null and ids.size() != 0">
                id in
            <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
            </foreach>
        </if>

  </update>
</mapper>
