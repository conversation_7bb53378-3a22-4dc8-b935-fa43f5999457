<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceDetailHistoryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceDetailHistory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
      <result column="dsx_price" jdbcType="DECIMAL" property="dsxPrice" />
      <result column="next_price_dxs_adjust_code" jdbcType="VARCHAR" property="nextPriceDxsAdjustCode" />
      <result column="next_price_dxs_date" jdbcType="TIMESTAMP" property="nextPriceDxsDate" />
      <result column="extend1" jdbcType="VARCHAR" property="extend1" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, business_name, store_id, store_name, goods_no, sku_id, adjust_code, price_type_code,
    price_type_name, price, updated_by, updated_by_name, status, gmt_create, gmt_update,
    extend, version, channel_id, dsx_price, next_price_dxs_adjust_code, next_price_dxs_date,extend1
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceDetailHistoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_detail_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_detail_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_detail_history
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceDetailHistoryExample">
    delete from price_detail_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceDetailHistory">
      insert into price_detail_history (id, business_id, business_name, store_id,
                                        store_name, goods_no, sku_id, adjust_code,
                                        price_type_code, price_type_name, price,
                                        updated_by, updated_by_name, `status`,
                                        gmt_create, gmt_update, extend,
                                        version, channel_id, dsx_price,
                                        next_price_dxs_adjust_code, next_price_dxs_date,extend1
      )
      values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT},
              #{storeName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{skuId,jdbcType=BIGINT}, #{adjustCode,jdbcType=VARCHAR},
              #{priceTypeCode,jdbcType=VARCHAR}, #{priceTypeName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL},
              #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT},
              #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR},
              #{version,jdbcType=INTEGER}, #{channelId,jdbcType=INTEGER}, #{dsxPrice,jdbcType=DECIMAL},
              #{nextPriceDxsAdjustCode,jdbcType=VARCHAR}, #{nextPriceDxsDate,jdbcType=TIMESTAMP}, #{extend1,jdbcType=VARCHAR}
             )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceDetailHistory">
    insert into price_detail_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
        <if test="skuId != null">
            sku_id,
        </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="priceTypeCode != null">
        price_type_code,
      </if>
      <if test="priceTypeName != null">
        price_type_name,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
        <if test="channelId != null">
            channel_id,
        </if>
        <if test="dsxPrice != null">
            dsx_price,
        </if>
        <if test="nextPriceDxsAdjustCode != null">
            next_price_dxs_adjust_code,
        </if>
        <if test="nextPriceDxsDate != null">
            next_price_dxs_date,
        </if>
        <if test="extend1 != null">
            extend1,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
        <if test="skuId != null">
            #{skuId,jdbcType=BIGINT},
        </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
        <if test="channelId != null">
            #{channelId,jdbcType=INTEGER},
        </if>
        <if test="dsxPrice != null">
            #{dsxPrice,jdbcType=DECIMAL},
        </if>
        <if test="nextPriceDxsAdjustCode != null">
            #{nextPriceDxsAdjustCode,jdbcType=VARCHAR},
        </if>
        <if test="nextPriceDxsDate != null">
            #{nextPriceDxsDate,jdbcType=TIMESTAMP},
        </if>
        <if test="extend1 != null">
            #{extend1,jdbcType=VARCHAR},
        </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceDetailHistoryExample" resultType="java.lang.Long">
    select count(*) from price_detail_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_detail_history
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
        <if test="record.skuId != null">
            sku_id = #{record.skuId,jdbcType=BIGINT},
        </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeCode != null">
        price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeName != null">
        price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
<!--        <if test="record.channelId != null">-->
<!--            channel_id = #{record.channelId,jdbcType=INTEGER},-->
<!--        </if>-->
        <if test="record.dsxPrice != null">
            dsx_price = #{record.dsxPrice,jdbcType=DECIMAL},
        </if>
        <if test="record.nextPriceDxsAdjustCode != null">
            next_price_dxs_adjust_code = #{record.nextPriceDxsAdjustCode,jdbcType=VARCHAR},
        </if>
        <if test="record.nextPriceDxsDate != null">
            next_price_dxs_date = #{record.nextPriceDxsDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.extend1 != null">
            extend1 = #{record.extend1,jdbcType=VARCHAR},
        </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_detail_history
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
        sku_id = #{record.skuId,jdbcType=BIGINT},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      dsx_price = #{record.dsxPrice,jdbcType=DECIMAL},
      next_price_dxs_adjust_code = #{record.nextPriceDxsAdjustCode,jdbcType=VARCHAR},
      next_price_dxs_date = #{record.nextPriceDxsDate,jdbcType=TIMESTAMP},
      extend1 = #{record.extend1,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceDetailHistory">
    update price_detail_history
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
        <if test="skuId != null">
            sku_id = #{skuId,jdbcType=BIGINT},
        </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeName != null">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
        <if test="channelId != null">
            channel_id = #{channelId,jdbcType=INTEGER},
        </if>
        <if test="dsxPrice != null">
            dsx_price = #{dsxPrice,jdbcType=DECIMAL},
        </if>
        <if test="nextPriceDxsAdjustCode != null">
            next_price_dxs_adjust_code = #{nextPriceDxsAdjustCode,jdbcType=VARCHAR},
        </if>
        <if test="nextPriceDxsDate != null">
            next_price_dxs_date = #{nextPriceDxsDate,jdbcType=TIMESTAMP},
        </if>
        <if test="extend1 != null">
            extend1 = #{extend1,jdbcType=VARCHAR},
        </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceDetailHistory">
    update price_detail_history
    set business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
        sku_id = #{skuId,jdbcType=BIGINT},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      channel_id = #{channelId,jdbcType=INTEGER},
        dsx_price = #{dsxPrice,jdbcType=DECIMAL},
        next_price_dxs_adjust_code = #{nextPriceDxsAdjustCode,jdbcType=VARCHAR},
        next_price_dxs_date = #{nextPriceDxsDate,jdbcType=TIMESTAMP},
        extend1 = #{extend1,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
