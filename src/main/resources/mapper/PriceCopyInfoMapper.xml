<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceCopyInfoMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceCopyInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="copy_no" jdbcType="VARCHAR" property="copyNo" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="new_store_id" jdbcType="BIGINT" property="newStoreId" />
    <result column="new_store_name" jdbcType="VARCHAR" property="newStoreName" />
    <result column="old_store_id" jdbcType="BIGINT" property="oldStoreId" />
    <result column="old_store_name" jdbcType="VARCHAR" property="oldStoreName" />
    <result column="batch_num" jdbcType="INTEGER" property="batchNum" />
    <result column="batched_num" jdbcType="INTEGER" property="batchedNum" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="adjust_price_version" jdbcType="VARCHAR" property="adjustPriceVersion" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, copy_no, business_id, new_store_id, new_store_name, old_store_id, old_store_name, 
    batch_num, batched_num, remark, status, gmt_create, gmt_update, extend, version, 
    created_by, updated_by, updated_by_name, audit_status, adjust_price_version, creator_id, 
    creator_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceCopyInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_copy_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_copy_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_copy_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceCopyInfoExample">
    delete from price_copy_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceCopyInfo">
    insert into price_copy_info (id, copy_no, business_id, 
      new_store_id, new_store_name, old_store_id, 
      old_store_name, batch_num, batched_num, 
      remark, status, gmt_create, 
      gmt_update, extend, version, 
      created_by, updated_by, updated_by_name, 
      audit_status, adjust_price_version, creator_id, 
      creator_name)
    values (#{id,jdbcType=BIGINT}, #{copyNo,jdbcType=VARCHAR}, #{businessId,jdbcType=BIGINT}, 
      #{newStoreId,jdbcType=BIGINT}, #{newStoreName,jdbcType=VARCHAR}, #{oldStoreId,jdbcType=BIGINT}, 
      #{oldStoreName,jdbcType=VARCHAR}, #{batchNum,jdbcType=INTEGER}, #{batchedNum,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=INTEGER}, #{adjustPriceVersion,jdbcType=VARCHAR}, #{creatorId,jdbcType=BIGINT}, 
      #{creatorName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceCopyInfo">
    insert into price_copy_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="copyNo != null">
        copy_no,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="newStoreId != null">
        new_store_id,
      </if>
      <if test="newStoreName != null">
        new_store_name,
      </if>
      <if test="oldStoreId != null">
        old_store_id,
      </if>
      <if test="oldStoreName != null">
        old_store_name,
      </if>
      <if test="batchNum != null">
        batch_num,
      </if>
      <if test="batchedNum != null">
        batched_num,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="adjustPriceVersion != null">
        adjust_price_version,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="copyNo != null">
        #{copyNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="newStoreId != null">
        #{newStoreId,jdbcType=BIGINT},
      </if>
      <if test="newStoreName != null">
        #{newStoreName,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreId != null">
        #{oldStoreId,jdbcType=BIGINT},
      </if>
      <if test="oldStoreName != null">
        #{oldStoreName,jdbcType=VARCHAR},
      </if>
      <if test="batchNum != null">
        #{batchNum,jdbcType=INTEGER},
      </if>
      <if test="batchedNum != null">
        #{batchedNum,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="adjustPriceVersion != null">
        #{adjustPriceVersion,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceCopyInfoExample" resultType="java.lang.Long">
    select count(*) from price_copy_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_copy_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.copyNo != null">
        copy_no = #{record.copyNo,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.newStoreId != null">
        new_store_id = #{record.newStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.newStoreName != null">
        new_store_name = #{record.newStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.oldStoreId != null">
        old_store_id = #{record.oldStoreId,jdbcType=BIGINT},
      </if>
      <if test="record.oldStoreName != null">
        old_store_name = #{record.oldStoreName,jdbcType=VARCHAR},
      </if>
      <if test="record.batchNum != null">
        batch_num = #{record.batchNum,jdbcType=INTEGER},
      </if>
      <if test="record.batchedNum != null">
        batched_num = #{record.batchedNum,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="record.adjustPriceVersion != null">
        adjust_price_version = #{record.adjustPriceVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.creatorId != null">
        creator_id = #{record.creatorId,jdbcType=BIGINT},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_copy_info
    set id = #{record.id,jdbcType=BIGINT},
      copy_no = #{record.copyNo,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      new_store_id = #{record.newStoreId,jdbcType=BIGINT},
      new_store_name = #{record.newStoreName,jdbcType=VARCHAR},
      old_store_id = #{record.oldStoreId,jdbcType=BIGINT},
      old_store_name = #{record.oldStoreName,jdbcType=VARCHAR},
      batch_num = #{record.batchNum,jdbcType=INTEGER},
      batched_num = #{record.batchedNum,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=INTEGER},
      adjust_price_version = #{record.adjustPriceVersion,jdbcType=VARCHAR},
      creator_id = #{record.creatorId,jdbcType=BIGINT},
      creator_name = #{record.creatorName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceCopyInfo">
    update price_copy_info
    <set>
      <if test="copyNo != null">
        copy_no = #{copyNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="newStoreId != null">
        new_store_id = #{newStoreId,jdbcType=BIGINT},
      </if>
      <if test="newStoreName != null">
        new_store_name = #{newStoreName,jdbcType=VARCHAR},
      </if>
      <if test="oldStoreId != null">
        old_store_id = #{oldStoreId,jdbcType=BIGINT},
      </if>
      <if test="oldStoreName != null">
        old_store_name = #{oldStoreName,jdbcType=VARCHAR},
      </if>
      <if test="batchNum != null">
        batch_num = #{batchNum,jdbcType=INTEGER},
      </if>
      <if test="batchedNum != null">
        batched_num = #{batchedNum,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="adjustPriceVersion != null">
        adjust_price_version = #{adjustPriceVersion,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceCopyInfo">
    update price_copy_info
    set copy_no = #{copyNo,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      new_store_id = #{newStoreId,jdbcType=BIGINT},
      new_store_name = #{newStoreName,jdbcType=VARCHAR},
      old_store_id = #{oldStoreId,jdbcType=BIGINT},
      old_store_name = #{oldStoreName,jdbcType=VARCHAR},
      batch_num = #{batchNum,jdbcType=INTEGER},
      batched_num = #{batchedNum,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      adjust_price_version = #{adjustPriceVersion,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=BIGINT},
      creator_name = #{creatorName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>