<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceOrderTabTypeDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceOrderTabTypeDetail">
    <!--@mbg.generated-->
    <!--@Table price_order_tab_type_detail-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="adjust_order_code" jdbcType="VARCHAR" property="adjustOrderCode" />
    <result column="org_tab_type" jdbcType="INTEGER" property="orgTabType" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="tab_type_value" jdbcType="VARCHAR" property="tabTypeValue" />
    <result column="tab_type_name" jdbcType="VARCHAR" property="tabTypeName" />
    <result column="control_order_code" jdbcType="VARCHAR" property="controlOrderCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, adjust_order_code, org_tab_type, gmt_create, gmt_update, tab_type_value, tab_type_name, 
    control_order_code
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceOrderTabTypeDetailExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_order_tab_type_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from price_order_tab_type_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from price_order_tab_type_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceOrderTabTypeDetailExample">
    <!--@mbg.generated-->
    delete from price_order_tab_type_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceOrderTabTypeDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_order_tab_type_detail (adjust_order_code, org_tab_type, gmt_create, 
      gmt_update, tab_type_value, tab_type_name, 
      control_order_code)
        values (#{adjustOrderCode,jdbcType=VARCHAR}, #{orgTabType,jdbcType=INTEGER}, 
        #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP},  
        #{tabTypeValue,jdbcType=VARCHAR},#{tabTypeName,jdbcType=VARCHAR}, 
        #{controlOrderCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceOrderTabTypeDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_order_tab_type_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustOrderCode != null and adjustOrderCode != ''">
        adjust_order_code,
      </if>
      <if test="orgTabType != null">
        org_tab_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="tabTypeValue != null and tabTypeValue != ''">
        tab_type_value,
      </if>
      <if test="tabTypeName != null and tabTypeName != ''">
        tab_type_name,
      </if>
      <if test="controlOrderCode != null and controlOrderCode != ''">
        control_order_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustOrderCode != null and adjustOrderCode != ''">
        #{adjustOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="orgTabType != null">
        #{orgTabType,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="tabTypeValue != null and tabTypeValue != ''">
        #{tabTypeValue,jdbcType=VARCHAR},
      </if>
      <if test="tabTypeName != null and tabTypeName != ''">
        #{tabTypeName,jdbcType=VARCHAR},
      </if>
      <if test="controlOrderCode != null and controlOrderCode != ''">
        #{controlOrderCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceOrderTabTypeDetailExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_order_tab_type_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_order_tab_type_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.adjustOrderCode != null">
        adjust_order_code = #{record.adjustOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgTabType != null">
        org_tab_type = #{record.orgTabType,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tabTypeValue != null">
        tab_type_value = #{record.tabTypeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.tabTypeName != null">
        tab_type_name = #{record.tabTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.controlOrderCode != null">
        control_order_code = #{record.controlOrderCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_order_tab_type_detail
    set id = #{record.id,jdbcType=INTEGER},
      adjust_order_code = #{record.adjustOrderCode,jdbcType=VARCHAR},
      org_tab_type = #{record.orgTabType,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      tab_type_value = #{record.tabTypeValue,jdbcType=VARCHAR},
      tab_type_name = #{record.tabTypeName,jdbcType=VARCHAR},
      control_order_code = #{record.controlOrderCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceOrderTabTypeDetail">
    <!--@mbg.generated-->
    update price_order_tab_type_detail
    <set>
      <if test="adjustOrderCode != null and adjustOrderCode != ''">
        adjust_order_code = #{adjustOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="orgTabType != null">
        org_tab_type = #{orgTabType,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="tabTypeValue != null and tabTypeValue != ''">
        tab_type_value = #{tabTypeValue,jdbcType=VARCHAR},
      </if>
      <if test="tabTypeName != null and tabTypeName != ''">
        tab_type_name = #{tabTypeName,jdbcType=VARCHAR},
      </if>
      <if test="controlOrderCode != null and controlOrderCode != ''">
        control_order_code = #{controlOrderCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceOrderTabTypeDetail">
    <!--@mbg.generated-->
    update price_order_tab_type_detail
    set adjust_order_code = #{adjustOrderCode,jdbcType=VARCHAR},
      org_tab_type = #{orgTabType,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      tab_type_value = #{tabTypeValue,jdbcType=VARCHAR},
      tab_type_name = #{tabTypeName,jdbcType=VARCHAR},
      control_order_code = #{controlOrderCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>