<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceOrderOperatorLogMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceOrderOperatorLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="price_order_type" jdbcType="INTEGER" property="priceOrderType" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="operator_user_id" jdbcType="BIGINT" property="operatorUserId" />
    <result column="operator_user_name" jdbcType="VARCHAR" property="operatorUserName" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="reject_reasion" jdbcType="VARCHAR" property="rejectReasion" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="oa_order_id" jdbcType="VARCHAR" property="oaOrderId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, price_order_type, order_id, operator_user_id, operator_user_name, order_status, 
    remark, reject_reasion, gmt_create, gmt_update, extend, oa_order_id
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceOrderOperatorLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_order_operator_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_order_operator_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from price_order_operator_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceOrderOperatorLogExample">
    delete from price_order_operator_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceOrderOperatorLog">
    insert into price_order_operator_log (id, price_order_type, order_id, 
      operator_user_id, operator_user_name, order_status, 
      remark, reject_reasion, gmt_create, 
      gmt_update, extend, oa_order_id
      )
    values (#{id,jdbcType=INTEGER}, #{priceOrderType,jdbcType=INTEGER}, #{orderId,jdbcType=VARCHAR}, 
      #{operatorUserId,jdbcType=BIGINT}, #{operatorUserName,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, 
      #{remark,jdbcType=VARCHAR}, #{rejectReasion,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{oaOrderId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceOrderOperatorLog">
    insert into price_order_operator_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="priceOrderType != null">
        price_order_type,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="operatorUserId != null">
        operator_user_id,
      </if>
      <if test="operatorUserName != null">
        operator_user_name,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="rejectReasion != null">
        reject_reasion,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="oaOrderId != null">
        oa_order_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="priceOrderType != null">
        #{priceOrderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="operatorUserId != null">
        #{operatorUserId,jdbcType=BIGINT},
      </if>
      <if test="operatorUserName != null">
        #{operatorUserName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="rejectReasion != null">
        #{rejectReasion,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="oaOrderId != null">
        #{oaOrderId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceOrderOperatorLogExample" resultType="java.lang.Long">
    select count(*) from price_order_operator_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_order_operator_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.priceOrderType != null">
        price_order_type = #{record.priceOrderType,jdbcType=INTEGER},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorUserId != null">
        operator_user_id = #{record.operatorUserId,jdbcType=BIGINT},
      </if>
      <if test="record.operatorUserName != null">
        operator_user_name = #{record.operatorUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderStatus != null">
        order_status = #{record.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.rejectReasion != null">
        reject_reasion = #{record.rejectReasion,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.oaOrderId != null">
        oa_order_id = #{record.oaOrderId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_order_operator_log
    set id = #{record.id,jdbcType=INTEGER},
      price_order_type = #{record.priceOrderType,jdbcType=INTEGER},
      order_id = #{record.orderId,jdbcType=VARCHAR},
      operator_user_id = #{record.operatorUserId,jdbcType=BIGINT},
      operator_user_name = #{record.operatorUserName,jdbcType=VARCHAR},
      order_status = #{record.orderStatus,jdbcType=INTEGER},
      remark = #{record.remark,jdbcType=VARCHAR},
      reject_reasion = #{record.rejectReasion,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      oa_order_id = #{record.oaOrderId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceOrderOperatorLog">
    update price_order_operator_log
    <set>
      <if test="priceOrderType != null">
        price_order_type = #{priceOrderType,jdbcType=INTEGER},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="operatorUserId != null">
        operator_user_id = #{operatorUserId,jdbcType=BIGINT},
      </if>
      <if test="operatorUserName != null">
        operator_user_name = #{operatorUserName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="rejectReasion != null">
        reject_reasion = #{rejectReasion,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="oaOrderId != null">
        oa_order_id = #{oaOrderId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceOrderOperatorLog">
    update price_order_operator_log
    set price_order_type = #{priceOrderType,jdbcType=INTEGER},
      order_id = #{orderId,jdbcType=VARCHAR},
      operator_user_id = #{operatorUserId,jdbcType=BIGINT},
      operator_user_name = #{operatorUserName,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      reject_reasion = #{rejectReasion,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      oa_order_id = #{oaOrderId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>