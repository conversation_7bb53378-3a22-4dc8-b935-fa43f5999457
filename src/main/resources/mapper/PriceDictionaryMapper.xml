<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceDictionaryMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceDictionary">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dict_code" jdbcType="VARCHAR" property="dictCode" />
    <result column="dict_type" jdbcType="INTEGER" property="dictType" />
    <result column="dict_name" jdbcType="VARCHAR" property="dictName" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, dict_code, dict_type, dict_name, parent_id, status, gmt_create, gmt_update, extend,
    version, created_by, updated_by, updated_by_name
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceDictionaryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from price_dictionary
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_dictionary
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceDictionaryExample">
    delete from price_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceDictionary">
    insert into price_dictionary (id, dict_code, dict_type,
      dict_name, parent_id, status,
      gmt_create, gmt_update, extend,
      version, created_by, updated_by,
      updated_by_name)
    values (#{id,jdbcType=BIGINT}, #{dictCode,jdbcType=VARCHAR}, #{dictType,jdbcType=INTEGER},
      #{dictName,jdbcType=VARCHAR}, #{parentId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR},
      #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT},
      #{updatedByName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceDictionary">
    insert into price_dictionary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="dictCode != null">
        dict_code,
      </if>
      <if test="dictType != null">
        dict_type,
      </if>
      <if test="dictName != null">
        dict_name,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="dictCode != null">
        #{dictCode,jdbcType=VARCHAR},
      </if>
      <if test="dictType != null">
        #{dictType,jdbcType=INTEGER},
      </if>
      <if test="dictName != null">
        #{dictName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceDictionaryExample" resultType="java.lang.Long">
    select count(*) from price_dictionary
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_dictionary
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.dictCode != null">
        dict_code = #{record.dictCode,jdbcType=VARCHAR},
      </if>
      <if test="record.dictType != null">
        dict_type = #{record.dictType,jdbcType=INTEGER},
      </if>
      <if test="record.dictName != null">
        dict_name = #{record.dictName,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_dictionary
    set id = #{record.id,jdbcType=BIGINT},
      dict_code = #{record.dictCode,jdbcType=VARCHAR},
      dict_type = #{record.dictType,jdbcType=INTEGER},
      dict_name = #{record.dictName,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceDictionary">
    update price_dictionary
    <set>
      <if test="dictCode != null">
        dict_code = #{dictCode,jdbcType=VARCHAR},
      </if>
      <if test="dictType != null">
        dict_type = #{dictType,jdbcType=INTEGER},
      </if>
      <if test="dictName != null">
        dict_name = #{dictName,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceDictionary">
    update price_dictionary
    set dict_code = #{dictCode,jdbcType=VARCHAR},
      dict_type = #{dictType,jdbcType=INTEGER},
      dict_name = #{dictName,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
