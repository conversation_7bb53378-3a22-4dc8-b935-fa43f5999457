<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceStoreSpuDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceStoreSpuDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="spu_id" jdbcType="BIGINT" property="spuId" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price_type_id" jdbcType="BIGINT" property="priceTypeId" />
    <result column="price_type_name" jdbcType="VARCHAR" property="priceTypeName" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="auth_org_id" jdbcType="BIGINT" property="authOrgId" />
    <result column="auth_org_name" jdbcType="VARCHAR" property="authOrgName" />
    <result column="auth_org_level" jdbcType="TINYINT" property="authOrgLevel" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="level" jdbcType="TINYINT" property="level" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="platform_id" jdbcType="BIGINT" property="platformId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="adjust_detail_id" jdbcType="BIGINT" property="adjustDetailId" />
    <result column="org_goods_id" jdbcType="BIGINT" property="orgGoodsId" />
    <result column="cur_name" jdbcType="VARCHAR" property="curName" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="op_code" jdbcType="VARCHAR" property="opCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="is_maintain" jdbcType="TINYINT" property="isMaintain" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="item_type" jdbcType="INTEGER" property="itemType" />
    <result column="dsx_price" jdbcType="DECIMAL" property="dsxPrice" />
    <result column="sync_date" jdbcType="BIGINT" property="syncDate" />
    <result column="item_id" jdbcType="BIGINT" property="itemId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, store_id, store_name, spu_id, sku_id, goods_no, price_type_code, price_type_id, 
    price_type_name, price, auth_org_id, auth_org_name, auth_org_level, org_id, org_name, 
    `level`, business_id, business_name, platform_id, platform_name, adjust_code, adjust_detail_id, 
    org_goods_id, cur_name, bar_code, op_code, `status`, gmt_create, gmt_update, extend, 
    version, created_by, updated_by, updated_by_name, is_maintain, `comment`, item_type, 
    dsx_price, sync_date, item_id
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceStoreSpuDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_store_spu_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from price_store_spu_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from price_store_spu_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceStoreSpuDetailExample">
    delete from price_store_spu_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceStoreSpuDetail" useGeneratedKeys="true">
    insert into price_store_spu_detail (store_id, store_name, spu_id, 
      sku_id, goods_no, price_type_code, 
      price_type_id, price_type_name, price, 
      auth_org_id, auth_org_name, auth_org_level, 
      org_id, org_name, `level`, 
      business_id, business_name, platform_id, 
      platform_name, adjust_code, adjust_detail_id, 
      org_goods_id, cur_name, bar_code, 
      op_code, `status`, gmt_create, 
      gmt_update, extend, version, 
      created_by, updated_by, updated_by_name, 
      is_maintain, `comment`, item_type, 
      dsx_price, sync_date, item_id
      )
    values (#{storeId,jdbcType=BIGINT}, #{storeName,jdbcType=VARCHAR}, #{spuId,jdbcType=BIGINT}, 
      #{skuId,jdbcType=BIGINT}, #{goodsNo,jdbcType=VARCHAR}, #{priceTypeCode,jdbcType=VARCHAR}, 
      #{priceTypeId,jdbcType=BIGINT}, #{priceTypeName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{authOrgId,jdbcType=BIGINT}, #{authOrgName,jdbcType=VARCHAR}, #{authOrgLevel,jdbcType=TINYINT}, 
      #{orgId,jdbcType=BIGINT}, #{orgName,jdbcType=VARCHAR}, #{level,jdbcType=TINYINT}, 
      #{businessId,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR}, #{platformId,jdbcType=BIGINT}, 
      #{platformName,jdbcType=VARCHAR}, #{adjustCode,jdbcType=VARCHAR}, #{adjustDetailId,jdbcType=BIGINT}, 
      #{orgGoodsId,jdbcType=BIGINT}, #{curName,jdbcType=VARCHAR}, #{barCode,jdbcType=VARCHAR}, 
      #{opCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{updatedByName,jdbcType=VARCHAR}, 
      #{isMaintain,jdbcType=TINYINT}, #{comment,jdbcType=VARCHAR}, #{itemType,jdbcType=INTEGER}, 
      #{dsxPrice,jdbcType=DECIMAL}, #{syncDate,jdbcType=BIGINT}, #{itemId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceStoreSpuDetail" useGeneratedKeys="true">
    insert into price_store_spu_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="spuId != null">
        spu_id,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="priceTypeCode != null">
        price_type_code,
      </if>
      <if test="priceTypeId != null">
        price_type_id,
      </if>
      <if test="priceTypeName != null">
        price_type_name,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="authOrgId != null">
        auth_org_id,
      </if>
      <if test="authOrgName != null">
        auth_org_name,
      </if>
      <if test="authOrgLevel != null">
        auth_org_level,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="level != null">
        `level`,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="adjustDetailId != null">
        adjust_detail_id,
      </if>
      <if test="orgGoodsId != null">
        org_goods_id,
      </if>
      <if test="curName != null">
        cur_name,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="opCode != null">
        op_code,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
        extend,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
      <if test="isMaintain != null">
        is_maintain,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="itemType != null">
        item_type,
      </if>
      <if test="dsxPrice != null">
        dsx_price,
      </if>
      <if test="syncDate != null">
        sync_date,
      </if>
      <if test="itemId != null">
        item_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        #{spuId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null">
        #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeName != null">
        #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="authOrgId != null">
        #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null">
        #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authOrgLevel != null">
        #{authOrgLevel,jdbcType=TINYINT},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        #{level,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="adjustDetailId != null">
        #{adjustDetailId,jdbcType=BIGINT},
      </if>
      <if test="orgGoodsId != null">
        #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="curName != null">
        #{curName,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="opCode != null">
        #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="isMaintain != null">
        #{isMaintain,jdbcType=TINYINT},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=INTEGER},
      </if>
      <if test="dsxPrice != null">
        #{dsxPrice,jdbcType=DECIMAL},
      </if>
      <if test="syncDate != null">
        #{syncDate,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        #{itemId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceStoreSpuDetailExample" resultType="java.lang.Long">
    select count(*) from price_store_spu_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update price_store_spu_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.spuId != null">
        spu_id = #{record.spuId,jdbcType=BIGINT},
      </if>
      <if test="record.skuId != null">
        sku_id = #{record.skuId,jdbcType=BIGINT},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeCode != null">
        price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.priceTypeId != null">
        price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="record.priceTypeName != null">
        price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=DECIMAL},
      </if>
      <if test="record.authOrgId != null">
        auth_org_id = #{record.authOrgId,jdbcType=BIGINT},
      </if>
      <if test="record.authOrgName != null">
        auth_org_name = #{record.authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="record.authOrgLevel != null">
        auth_org_level = #{record.authOrgLevel,jdbcType=TINYINT},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.level != null">
        `level` = #{record.level,jdbcType=TINYINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustDetailId != null">
        adjust_detail_id = #{record.adjustDetailId,jdbcType=BIGINT},
      </if>
      <if test="record.orgGoodsId != null">
        org_goods_id = #{record.orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="record.curName != null">
        cur_name = #{record.curName,jdbcType=VARCHAR},
      </if>
      <if test="record.barCode != null">
        bar_code = #{record.barCode,jdbcType=VARCHAR},
      </if>
      <if test="record.opCode != null">
        op_code = #{record.opCode,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.isMaintain != null">
        is_maintain = #{record.isMaintain,jdbcType=TINYINT},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.itemType != null">
        item_type = #{record.itemType,jdbcType=INTEGER},
      </if>
      <if test="record.dsxPrice != null">
        dsx_price = #{record.dsxPrice,jdbcType=DECIMAL},
      </if>
      <if test="record.syncDate != null">
        sync_date = #{record.syncDate,jdbcType=BIGINT},
      </if>
      <if test="record.itemId != null">
        item_id = #{record.itemId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update price_store_spu_detail
    set id = #{record.id,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      spu_id = #{record.spuId,jdbcType=BIGINT},
      sku_id = #{record.skuId,jdbcType=BIGINT},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price_type_id = #{record.priceTypeId,jdbcType=BIGINT},
      price_type_name = #{record.priceTypeName,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      auth_org_id = #{record.authOrgId,jdbcType=BIGINT},
      auth_org_name = #{record.authOrgName,jdbcType=VARCHAR},
      auth_org_level = #{record.authOrgLevel,jdbcType=TINYINT},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      `level` = #{record.level,jdbcType=TINYINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      adjust_detail_id = #{record.adjustDetailId,jdbcType=BIGINT},
      org_goods_id = #{record.orgGoodsId,jdbcType=BIGINT},
      cur_name = #{record.curName,jdbcType=VARCHAR},
      bar_code = #{record.barCode,jdbcType=VARCHAR},
      op_code = #{record.opCode,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      is_maintain = #{record.isMaintain,jdbcType=TINYINT},
      `comment` = #{record.comment,jdbcType=VARCHAR},
      item_type = #{record.itemType,jdbcType=INTEGER},
      dsx_price = #{record.dsxPrice,jdbcType=DECIMAL},
      sync_date = #{record.syncDate,jdbcType=BIGINT},
      item_id = #{record.itemId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceStoreSpuDetail">
    update price_store_spu_detail
    <set>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="spuId != null">
        spu_id = #{spuId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=BIGINT},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeCode != null">
        price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="priceTypeId != null">
        price_type_id = #{priceTypeId,jdbcType=BIGINT},
      </if>
      <if test="priceTypeName != null">
        price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="authOrgId != null">
        auth_org_id = #{authOrgId,jdbcType=BIGINT},
      </if>
      <if test="authOrgName != null">
        auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      </if>
      <if test="authOrgLevel != null">
        auth_org_level = #{authOrgLevel,jdbcType=TINYINT},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="level != null">
        `level` = #{level,jdbcType=TINYINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="adjustDetailId != null">
        adjust_detail_id = #{adjustDetailId,jdbcType=BIGINT},
      </if>
      <if test="orgGoodsId != null">
        org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      </if>
      <if test="curName != null">
        cur_name = #{curName,jdbcType=VARCHAR},
      </if>
      <if test="barCode != null">
        bar_code = #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="opCode != null">
        op_code = #{opCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="isMaintain != null">
        is_maintain = #{isMaintain,jdbcType=TINYINT},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="itemType != null">
        item_type = #{itemType,jdbcType=INTEGER},
      </if>
      <if test="dsxPrice != null">
        dsx_price = #{dsxPrice,jdbcType=DECIMAL},
      </if>
      <if test="syncDate != null">
        sync_date = #{syncDate,jdbcType=BIGINT},
      </if>
      <if test="itemId != null">
        item_id = #{itemId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceStoreSpuDetail">
    update price_store_spu_detail
    set store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      spu_id = #{spuId,jdbcType=BIGINT},
      sku_id = #{skuId,jdbcType=BIGINT},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price_type_id = #{priceTypeId,jdbcType=BIGINT},
      price_type_name = #{priceTypeName,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      auth_org_id = #{authOrgId,jdbcType=BIGINT},
      auth_org_name = #{authOrgName,jdbcType=VARCHAR},
      auth_org_level = #{authOrgLevel,jdbcType=TINYINT},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      `level` = #{level,jdbcType=TINYINT},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      adjust_detail_id = #{adjustDetailId,jdbcType=BIGINT},
      org_goods_id = #{orgGoodsId,jdbcType=BIGINT},
      cur_name = #{curName,jdbcType=VARCHAR},
      bar_code = #{barCode,jdbcType=VARCHAR},
      op_code = #{opCode,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      is_maintain = #{isMaintain,jdbcType=TINYINT},
      `comment` = #{comment,jdbcType=VARCHAR},
      item_type = #{itemType,jdbcType=INTEGER},
      dsx_price = #{dsxPrice,jdbcType=DECIMAL},
      sync_date = #{syncDate,jdbcType=BIGINT},
      item_id = #{itemId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>