<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.AdjustPriceOrderMqResultMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceOrderMqResult">
    <!--@mbg.generated -->
    <!--@Table adjust_price_order_mq_result -->
    <!-- generated on Mon Mar 21 14:40:37 CST 2022 -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="adjust_execute_time" jdbcType="TIMESTAMP" property="adjustExecuteTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated -->
    id, adjust_code, store_id, business_id, adjust_execute_time, `status`, gmt_create,
    gmt_update
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderMqResultExample" resultMap="BaseResultMap">
    <!--@mbg.generated -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from adjust_price_order_mq_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated -->
    select
    <include refid="Base_Column_List" />
    from adjust_price_order_mq_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated -->
    delete from adjust_price_order_mq_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderMqResultExample">
    <!--@mbg.generated -->
    delete from adjust_price_order_mq_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderMqResult" useGeneratedKeys="true">
    <!--@mbg.generated -->
    insert into adjust_price_order_mq_result (adjust_code, store_id, business_id,
      adjust_execute_time, `status`, gmt_create,
      gmt_update)
    values (#{adjustCode,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT},
      #{adjustExecuteTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective"  parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderMqResult">
    insert into adjust_price_order_mq_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="adjustExecuteTime != null">
        adjust_execute_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="adjustExecuteTime != null">
        #{adjustExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </trim>
      ON DUPLICATE KEY UPDATE
      business_id = values(business_id),
      adjust_execute_time = values(adjust_execute_time),
      `status` = values(status)
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderMqResultExample" resultType="java.lang.Long">
    <!--@mbg.generated -->
    select count(*) from adjust_price_order_mq_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated -->
    update adjust_price_order_mq_result
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.adjustCode != null">
        adjust_code = #{row.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="row.storeId != null">
        store_id = #{row.storeId,jdbcType=BIGINT},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=BIGINT},
      </if>
      <if test="row.adjustExecuteTime != null">
        adjust_execute_time = #{row.adjustExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.status != null">
        `status` = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.gmtCreate != null">
        gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.gmtUpdate != null">
        gmt_update = #{row.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated -->
    update adjust_price_order_mq_result
    set id = #{row.id,jdbcType=BIGINT},
      adjust_code = #{row.adjustCode,jdbcType=VARCHAR},
      store_id = #{row.storeId,jdbcType=BIGINT},
      business_id = #{row.businessId,jdbcType=BIGINT},
      adjust_execute_time = #{row.adjustExecuteTime,jdbcType=TIMESTAMP},
      `status` = #{row.status,jdbcType=TINYINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{row.gmtUpdate,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderMqResult">
    <!--@mbg.generated -->
    update adjust_price_order_mq_result
    <set>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="adjustExecuteTime != null">
        adjust_execute_time = #{adjustExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderMqResult">
    <!--@mbg.generated -->
    update adjust_price_order_mq_result
    set adjust_code = #{adjustCode,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      adjust_execute_time = #{adjustExecuteTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
