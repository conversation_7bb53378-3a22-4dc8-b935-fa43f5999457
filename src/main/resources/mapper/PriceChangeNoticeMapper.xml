<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceChangeNoticeMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceChangeNotice">
    <!--@mbg.generated-->
    <!--@Table price_change_notice-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="adjust_name" jdbcType="VARCHAR" property="adjustName" />
    <result column="adjust_price_type" jdbcType="VARCHAR" property="adjustPriceType" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="adjust_price_version" jdbcType="VARCHAR" property="adjustPriceVersion" />
    <result column="platform_id" jdbcType="BIGINT" property="platformId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="adjust_created_by" jdbcType="BIGINT" property="adjustCreatedBy" />
    <result column="adjust_created_by_name" jdbcType="VARCHAR" property="adjustCreatedByName" />
    <result column="adjust_audit_by" jdbcType="BIGINT" property="adjustAuditBy" />
    <result column="adjust_audit_by_name" jdbcType="VARCHAR" property="adjustAuditByName" />
    <result column="adjust_effect_time" jdbcType="TIMESTAMP" property="adjustEffectTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="notice_type" jdbcType="INTEGER" property="noticeType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, adjust_code, adjust_name, adjust_price_type, channel, adjust_price_version, platform_id,
    platform_name, business_id, business_name, store_id, store_name, adjust_created_by,
    adjust_created_by_name, adjust_audit_by, adjust_audit_by_name, adjust_effect_time,
    `status`, reason, gmt_create, gmt_update, city_code, notice_type
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceChangeNoticeExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_change_notice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_change_notice
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from price_change_notice
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceChangeNoticeExample">
    <!--@mbg.generated-->
    delete from price_change_notice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceChangeNotice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_change_notice (adjust_code, adjust_name, adjust_price_type,
      channel, adjust_price_version, platform_id,
      platform_name, business_id, business_name,
      store_id, store_name, adjust_created_by,
      adjust_created_by_name, adjust_audit_by, adjust_audit_by_name,
      adjust_effect_time, `status`, reason,
      gmt_create, gmt_update, city_code,
      notice_type)
    values (#{adjustCode,jdbcType=VARCHAR}, #{adjustName,jdbcType=VARCHAR}, #{adjustPriceType,jdbcType=VARCHAR},
      #{channel,jdbcType=VARCHAR}, #{adjustPriceVersion,jdbcType=VARCHAR}, #{platformId,jdbcType=BIGINT},
      #{platformName,jdbcType=VARCHAR}, #{businessId,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR},
      #{storeId,jdbcType=BIGINT}, #{storeName,jdbcType=VARCHAR}, #{adjustCreatedBy,jdbcType=BIGINT},
      #{adjustCreatedByName,jdbcType=VARCHAR}, #{adjustAuditBy,jdbcType=BIGINT}, #{adjustAuditByName,jdbcType=VARCHAR},
      #{adjustEffectTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, #{reason,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{cityCode,jdbcType=VARCHAR},
      #{noticeType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceChangeNotice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_change_notice
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="adjustName != null">
        adjust_name,
      </if>
      <if test="adjustPriceType != null">
        adjust_price_type,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="adjustPriceVersion != null">
        adjust_price_version,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="adjustCreatedBy != null">
        adjust_created_by,
      </if>
      <if test="adjustCreatedByName != null">
        adjust_created_by_name,
      </if>
      <if test="adjustAuditBy != null">
        adjust_audit_by,
      </if>
      <if test="adjustAuditByName != null">
        adjust_audit_by_name,
      </if>
      <if test="adjustEffectTime != null">
        adjust_effect_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="noticeType != null">
        notice_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="adjustName != null">
        #{adjustName,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceType != null">
        #{adjustPriceType,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceVersion != null">
        #{adjustPriceVersion,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="adjustCreatedBy != null">
        #{adjustCreatedBy,jdbcType=BIGINT},
      </if>
      <if test="adjustCreatedByName != null">
        #{adjustCreatedByName,jdbcType=VARCHAR},
      </if>
      <if test="adjustAuditBy != null">
        #{adjustAuditBy,jdbcType=BIGINT},
      </if>
      <if test="adjustAuditByName != null">
        #{adjustAuditByName,jdbcType=VARCHAR},
      </if>
      <if test="adjustEffectTime != null">
        #{adjustEffectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="noticeType != null">
        #{noticeType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceChangeNoticeExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_change_notice
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_change_notice
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustName != null">
        adjust_name = #{record.adjustName,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustPriceType != null">
        adjust_price_type = #{record.adjustPriceType,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustPriceVersion != null">
        adjust_price_version = #{record.adjustPriceVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustCreatedBy != null">
        adjust_created_by = #{record.adjustCreatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.adjustCreatedByName != null">
        adjust_created_by_name = #{record.adjustCreatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustAuditBy != null">
        adjust_audit_by = #{record.adjustAuditBy,jdbcType=BIGINT},
      </if>
      <if test="record.adjustAuditByName != null">
        adjust_audit_by_name = #{record.adjustAuditByName,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustEffectTime != null">
        adjust_effect_time = #{record.adjustEffectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cityCode != null">
        city_code = #{record.cityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.noticeType != null">
        notice_type = #{record.noticeType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_change_notice
    set id = #{record.id,jdbcType=BIGINT},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      adjust_name = #{record.adjustName,jdbcType=VARCHAR},
      adjust_price_type = #{record.adjustPriceType,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      adjust_price_version = #{record.adjustPriceVersion,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      adjust_created_by = #{record.adjustCreatedBy,jdbcType=BIGINT},
      adjust_created_by_name = #{record.adjustCreatedByName,jdbcType=VARCHAR},
      adjust_audit_by = #{record.adjustAuditBy,jdbcType=BIGINT},
      adjust_audit_by_name = #{record.adjustAuditByName,jdbcType=VARCHAR},
      adjust_effect_time = #{record.adjustEffectTime,jdbcType=TIMESTAMP},
      `status` = #{record.status,jdbcType=TINYINT},
      reason = #{record.reason,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      city_code = #{record.cityCode,jdbcType=VARCHAR},
      notice_type = #{record.noticeType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceChangeNotice">
    <!--@mbg.generated-->
    update price_change_notice
    <set>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="adjustName != null">
        adjust_name = #{adjustName,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceType != null">
        adjust_price_type = #{adjustPriceType,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceVersion != null">
        adjust_price_version = #{adjustPriceVersion,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="adjustCreatedBy != null">
        adjust_created_by = #{adjustCreatedBy,jdbcType=BIGINT},
      </if>
      <if test="adjustCreatedByName != null">
        adjust_created_by_name = #{adjustCreatedByName,jdbcType=VARCHAR},
      </if>
      <if test="adjustAuditBy != null">
        adjust_audit_by = #{adjustAuditBy,jdbcType=BIGINT},
      </if>
      <if test="adjustAuditByName != null">
        adjust_audit_by_name = #{adjustAuditByName,jdbcType=VARCHAR},
      </if>
      <if test="adjustEffectTime != null">
        adjust_effect_time = #{adjustEffectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="noticeType != null">
        notice_type = #{noticeType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceChangeNotice">
    <!--@mbg.generated-->
    update price_change_notice
    set adjust_code = #{adjustCode,jdbcType=VARCHAR},
      adjust_name = #{adjustName,jdbcType=VARCHAR},
      adjust_price_type = #{adjustPriceType,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      adjust_price_version = #{adjustPriceVersion,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      adjust_created_by = #{adjustCreatedBy,jdbcType=BIGINT},
      adjust_created_by_name = #{adjustCreatedByName,jdbcType=VARCHAR},
      adjust_audit_by = #{adjustAuditBy,jdbcType=BIGINT},
      adjust_audit_by_name = #{adjustAuditByName,jdbcType=VARCHAR},
      adjust_effect_time = #{adjustEffectTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=TINYINT},
      reason = #{reason,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      city_code = #{cityCode,jdbcType=VARCHAR},
      notice_type = #{noticeType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
