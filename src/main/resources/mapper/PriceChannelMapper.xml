<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.PriceChannelMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceChannel">
    <!--@mbg.generated-->
    <!--@Table price_channel-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="channel_en_code" jdbcType="VARCHAR" property="channelEnCode" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="operate_user_id" jdbcType="BIGINT" property="operateUserId" />
    <result column="operate_user_name" jdbcType="VARCHAR" property="operateUserName" />
    <result column="channel_status" jdbcType="INTEGER" property="channelStatus" />
    <result column="enable_status" jdbcType="INTEGER" property="enableStatus" />
    <result column="out_channel_code" jdbcType="VARCHAR" property="outChannelCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, channel_id, channel_en_code, channel_name, gmt_create, gmt_update, operate_user_id,
    operate_user_name, channel_status, enable_status, out_channel_code, remark
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.PriceChannelExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from price_channel
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from price_channel
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceChannelExample">
    <!--@mbg.generated-->
    delete from price_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceChannel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_channel (channel_id, channel_en_code, channel_name,
      gmt_create, gmt_update, operate_user_id,
      operate_user_name, channel_status, enable_status,
      out_channel_code)
    values (#{channelId,jdbcType=INTEGER}, #{channelEnCode,jdbcType=VARCHAR}, #{channelName,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{operateUserId,jdbcType=BIGINT},
      #{operateUserName,jdbcType=VARCHAR}, #{channelStatus,jdbcType=INTEGER}, #{enableStatus,jdbcType=INTEGER},
      #{outChannelCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.PriceChannel" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into price_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code,
      </if>
      <if test="channelName != null and channelName != ''">
        channel_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="operateUserId != null">
        operate_user_id,
      </if>
      <if test="operateUserName != null and operateUserName != ''">
        operate_user_name,
      </if>
      <if test="channelStatus != null">
        channel_status,
      </if>
      <if test="enableStatus != null">
        enable_status,
      </if>
      <if test="outChannelCode != null and outChannelCode != ''">
        out_channel_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operateUserId != null">
        #{operateUserId,jdbcType=BIGINT},
      </if>
      <if test="operateUserName != null and operateUserName != ''">
        #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="channelStatus != null">
        #{channelStatus,jdbcType=INTEGER},
      </if>
      <if test="enableStatus != null">
        #{enableStatus,jdbcType=INTEGER},
      </if>
      <if test="outChannelCode != null and outChannelCode != ''">
        #{outChannelCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceChannelExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from price_channel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update price_channel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.channelId != null">
        channel_id = #{record.channelId,jdbcType=INTEGER},
      </if>
      <if test="record.channelEnCode != null">
        channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="record.channelName != null">
        channel_name = #{record.channelName,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operateUserId != null">
        operate_user_id = #{record.operateUserId,jdbcType=BIGINT},
      </if>
      <if test="record.operateUserName != null">
        operate_user_name = #{record.operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="record.channelStatus != null">
        channel_status = #{record.channelStatus,jdbcType=INTEGER},
      </if>
      <if test="record.enableStatus != null">
        enable_status = #{record.enableStatus,jdbcType=INTEGER},
      </if>
      <if test="record.outChannelCode != null">
        out_channel_code = #{record.outChannelCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update price_channel
    set id = #{record.id,jdbcType=INTEGER},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      channel_en_code = #{record.channelEnCode,jdbcType=VARCHAR},
      channel_name = #{record.channelName,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      operate_user_id = #{record.operateUserId,jdbcType=BIGINT},
      operate_user_name = #{record.operateUserName,jdbcType=VARCHAR},
      channel_status = #{record.channelStatus,jdbcType=INTEGER},
      enable_status = #{record.enableStatus,jdbcType=INTEGER},
      out_channel_code = #{record.outChannelCode,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceChannel">
    <!--@mbg.generated-->
    update price_channel
    <set>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelEnCode != null and channelEnCode != ''">
        channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      </if>
      <if test="channelName != null and channelName != ''">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="operateUserId != null">
        operate_user_id = #{operateUserId,jdbcType=BIGINT},
      </if>
      <if test="operateUserName != null and operateUserName != ''">
        operate_user_name = #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="channelStatus != null">
        channel_status = #{channelStatus,jdbcType=INTEGER},
      </if>
      <if test="enableStatus != null">
        enable_status = #{enableStatus,jdbcType=INTEGER},
      </if>
      <if test="outChannelCode != null and outChannelCode != ''">
        out_channel_code = #{outChannelCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceChannel">
    <!--@mbg.generated-->
    update price_channel
    set channel_id = #{channelId,jdbcType=INTEGER},
      channel_en_code = #{channelEnCode,jdbcType=VARCHAR},
      channel_name = #{channelName,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      operate_user_id = #{operateUserId,jdbcType=BIGINT},
      operate_user_name = #{operateUserName,jdbcType=VARCHAR},
      channel_status = #{channelStatus,jdbcType=INTEGER},
      enable_status = #{enableStatus,jdbcType=INTEGER},
      out_channel_code = #{outChannelCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>
