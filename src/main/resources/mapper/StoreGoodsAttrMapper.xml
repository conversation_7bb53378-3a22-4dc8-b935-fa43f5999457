<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.StoreGoodsAttrMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.StoreGoodsAttr">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_code" jdbcType="VARCHAR" property="storeCode" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="store_attr_code" jdbcType="VARCHAR" property="storeAttrCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_by_id" jdbcType="BIGINT" property="createById" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_by_id" jdbcType="BIGINT" property="updateById" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, business_code, business_name, store_id, store_code, store_name, 
    goods_no, goods_name, `comment`, `label`, store_attr_code, `type`, `status`, gmt_create, 
    gmt_update, create_by, create_by_id, update_by, update_by_id, version, extend
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttrExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from store_goods_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from store_goods_attr
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from store_goods_attr
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttrExample">
    delete from store_goods_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttr">
    insert into store_goods_attr (id, business_id, business_code, 
      business_name, store_id, store_code, 
      store_name, goods_no, goods_name, 
      `comment`, `label`, store_attr_code, 
      `type`, `status`, gmt_create, 
      gmt_update, create_by, create_by_id, 
      update_by, update_by_id, version, 
      extend)
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{businessCode,jdbcType=VARCHAR}, 
      #{businessName,jdbcType=VARCHAR}, #{storeId,jdbcType=BIGINT}, #{storeCode,jdbcType=VARCHAR}, 
      #{storeName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, 
      #{comment,jdbcType=VARCHAR}, #{label,jdbcType=VARCHAR}, #{storeAttrCode,jdbcType=VARCHAR}, 
      #{type,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtUpdate,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{createById,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=VARCHAR}, #{updateById,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}, 
      #{extend,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttr">
    insert into store_goods_attr
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessCode != null">
        business_code,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeCode != null">
        store_code,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="comment != null">
        `comment`,
      </if>
      <if test="label != null">
        `label`,
      </if>
      <if test="storeAttrCode != null">
        store_attr_code,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createById != null">
        create_by_id,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateById != null">
        update_by_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        #{label,jdbcType=VARCHAR},
      </if>
      <if test="storeAttrCode != null">
        #{storeAttrCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        #{updateById,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttrExample" resultType="java.lang.Long">
    select count(*) from store_goods_attr
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update store_goods_attr
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessCode != null">
        business_code = #{record.businessCode,jdbcType=VARCHAR},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeCode != null">
        store_code = #{record.storeCode,jdbcType=VARCHAR},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null">
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsName != null">
        goods_name = #{record.goodsName,jdbcType=VARCHAR},
      </if>
      <if test="record.comment != null">
        `comment` = #{record.comment,jdbcType=VARCHAR},
      </if>
      <if test="record.label != null">
        `label` = #{record.label,jdbcType=VARCHAR},
      </if>
      <if test="record.storeAttrCode != null">
        store_attr_code = #{record.storeAttrCode,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        `type` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createById != null">
        create_by_id = #{record.createById,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateById != null">
        update_by_id = #{record.updateById,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update store_goods_attr
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_code = #{record.businessCode,jdbcType=VARCHAR},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_code = #{record.storeCode,jdbcType=VARCHAR},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      goods_name = #{record.goodsName,jdbcType=VARCHAR},
      `comment` = #{record.comment,jdbcType=VARCHAR},
      `label` = #{record.label,jdbcType=VARCHAR},
      store_attr_code = #{record.storeAttrCode,jdbcType=VARCHAR},
      `type` = #{record.type,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=INTEGER},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_by_id = #{record.createById,jdbcType=BIGINT},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_by_id = #{record.updateById,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      extend = #{record.extend,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttr">
    update store_goods_attr
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessCode != null">
        business_code = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeCode != null">
        store_code = #{storeCode,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        goods_name = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        `comment` = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="label != null">
        `label` = #{label,jdbcType=VARCHAR},
      </if>
      <if test="storeAttrCode != null">
        store_attr_code = #{storeAttrCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createById != null">
        create_by_id = #{createById,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateById != null">
        update_by_id = #{updateById,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.StoreGoodsAttr">
    update store_goods_attr
    set business_id = #{businessId,jdbcType=BIGINT},
      business_code = #{businessCode,jdbcType=VARCHAR},
      business_name = #{businessName,jdbcType=VARCHAR},
      store_id = #{storeId,jdbcType=BIGINT},
      store_code = #{storeCode,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      goods_name = #{goodsName,jdbcType=VARCHAR},
      `comment` = #{comment,jdbcType=VARCHAR},
      `label` = #{label,jdbcType=VARCHAR},
      store_attr_code = #{storeAttrCode,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_by_id = #{createById,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_by_id = #{updateById,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>