<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.AdjustPriceOrderLogMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceOrderLog">
    <!--@mbg.generated-->
    <!--@Table adjust_price_order_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="exec_status" jdbcType="TINYINT" property="execStatus" />
    <result column="effect_status" jdbcType="TINYINT" property="effectStatus" />
    <result column="effect_time" jdbcType="TIMESTAMP" property="effectTime" />
    <result column="scheduled_time" jdbcType="TIMESTAMP" property="scheduledTime" />
    <result column="offline_effect_status" jdbcType="TINYINT" property="offlineEffectStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="created_by_name" jdbcType="VARCHAR" property="createdByName" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="updated_by_name" jdbcType="VARCHAR" property="updatedByName" />
    <result column="audit_by" jdbcType="BIGINT" property="auditBy" />
    <result column="audit_by_name" jdbcType="VARCHAR" property="auditByName" />
    <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
    <result column="audit_reason" jdbcType="VARCHAR" property="auditReason" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, adjust_code, audit_status, exec_status, effect_status, effect_time, scheduled_time, 
    offline_effect_status, `status`, gmt_create, created_by, created_by_name, updated_by, 
    updated_by_name, audit_by, audit_by_name, audit_date, audit_reason, extend
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderLogExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from adjust_price_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from adjust_price_order_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from adjust_price_order_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderLogExample">
    <!--@mbg.generated-->
    delete from adjust_price_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into adjust_price_order_log (adjust_code, audit_status, exec_status, 
      effect_status, effect_time, scheduled_time, 
      offline_effect_status, `status`, gmt_create, 
      created_by, created_by_name, updated_by, 
      updated_by_name, audit_by, audit_by_name, 
      audit_date, audit_reason, extend
      )
    values (#{adjustCode,jdbcType=VARCHAR}, #{auditStatus,jdbcType=TINYINT}, #{execStatus,jdbcType=TINYINT}, 
      #{effectStatus,jdbcType=TINYINT}, #{effectTime,jdbcType=TIMESTAMP}, #{scheduledTime,jdbcType=TIMESTAMP}, 
      #{offlineEffectStatus,jdbcType=TINYINT}, #{status,jdbcType=TINYINT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=BIGINT}, #{createdByName,jdbcType=VARCHAR}, #{updatedBy,jdbcType=BIGINT}, 
      #{updatedByName,jdbcType=VARCHAR}, #{auditBy,jdbcType=BIGINT}, #{auditByName,jdbcType=VARCHAR}, 
      #{auditDate,jdbcType=TIMESTAMP}, #{auditReason,jdbcType=VARCHAR}, #{extend,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into adjust_price_order_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="execStatus != null">
        exec_status,
      </if>
      <if test="effectStatus != null">
        effect_status,
      </if>
      <if test="effectTime != null">
        effect_time,
      </if>
      <if test="scheduledTime != null">
        scheduled_time,
      </if>
      <if test="offlineEffectStatus != null">
        offline_effect_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdByName != null">
        created_by_name,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedByName != null">
        updated_by_name,
      </if>
      <if test="auditBy != null">
        audit_by,
      </if>
      <if test="auditByName != null">
        audit_by_name,
      </if>
      <if test="auditDate != null">
        audit_date,
      </if>
      <if test="auditReason != null">
        audit_reason,
      </if>
      <if test="extend != null">
        extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="execStatus != null">
        #{execStatus,jdbcType=TINYINT},
      </if>
      <if test="effectStatus != null">
        #{effectStatus,jdbcType=TINYINT},
      </if>
      <if test="effectTime != null">
        #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduledTime != null">
        #{scheduledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineEffectStatus != null">
        #{offlineEffectStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null">
        #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="auditBy != null">
        #{auditBy,jdbcType=BIGINT},
      </if>
      <if test="auditByName != null">
        #{auditByName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null">
        #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="auditReason != null">
        #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderLogExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from adjust_price_order_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update adjust_price_order_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.auditStatus != null">
        audit_status = #{record.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="record.execStatus != null">
        exec_status = #{record.execStatus,jdbcType=TINYINT},
      </if>
      <if test="record.effectStatus != null">
        effect_status = #{record.effectStatus,jdbcType=TINYINT},
      </if>
      <if test="record.effectTime != null">
        effect_time = #{record.effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.scheduledTime != null">
        scheduled_time = #{record.scheduledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.offlineEffectStatus != null">
        offline_effect_status = #{record.offlineEffectStatus,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdByName != null">
        created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedByName != null">
        updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditBy != null">
        audit_by = #{record.auditBy,jdbcType=BIGINT},
      </if>
      <if test="record.auditByName != null">
        audit_by_name = #{record.auditByName,jdbcType=VARCHAR},
      </if>
      <if test="record.auditDate != null">
        audit_date = #{record.auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.auditReason != null">
        audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update adjust_price_order_log
    set id = #{record.id,jdbcType=BIGINT},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      audit_status = #{record.auditStatus,jdbcType=TINYINT},
      exec_status = #{record.execStatus,jdbcType=TINYINT},
      effect_status = #{record.effectStatus,jdbcType=TINYINT},
      effect_time = #{record.effectTime,jdbcType=TIMESTAMP},
      scheduled_time = #{record.scheduledTime,jdbcType=TIMESTAMP},
      offline_effect_status = #{record.offlineEffectStatus,jdbcType=TINYINT},
      `status` = #{record.status,jdbcType=TINYINT},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      created_by_name = #{record.createdByName,jdbcType=VARCHAR},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      updated_by_name = #{record.updatedByName,jdbcType=VARCHAR},
      audit_by = #{record.auditBy,jdbcType=BIGINT},
      audit_by_name = #{record.auditByName,jdbcType=VARCHAR},
      audit_date = #{record.auditDate,jdbcType=TIMESTAMP},
      audit_reason = #{record.auditReason,jdbcType=VARCHAR},
      extend = #{record.extend,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderLog">
    <!--@mbg.generated-->
    update adjust_price_order_log
    <set>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="execStatus != null">
        exec_status = #{execStatus,jdbcType=TINYINT},
      </if>
      <if test="effectStatus != null">
        effect_status = #{effectStatus,jdbcType=TINYINT},
      </if>
      <if test="effectTime != null">
        effect_time = #{effectTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduledTime != null">
        scheduled_time = #{scheduledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="offlineEffectStatus != null">
        offline_effect_status = #{offlineEffectStatus,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="createdByName != null">
        created_by_name = #{createdByName,jdbcType=VARCHAR},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="updatedByName != null">
        updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      </if>
      <if test="auditBy != null">
        audit_by = #{auditBy,jdbcType=BIGINT},
      </if>
      <if test="auditByName != null">
        audit_by_name = #{auditByName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null">
        audit_date = #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="auditReason != null">
        audit_reason = #{auditReason,jdbcType=VARCHAR},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderLog">
    <!--@mbg.generated-->
    update adjust_price_order_log
    set adjust_code = #{adjustCode,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      exec_status = #{execStatus,jdbcType=TINYINT},
      effect_status = #{effectStatus,jdbcType=TINYINT},
      effect_time = #{effectTime,jdbcType=TIMESTAMP},
      scheduled_time = #{scheduledTime,jdbcType=TIMESTAMP},
      offline_effect_status = #{offlineEffectStatus,jdbcType=TINYINT},
      `status` = #{status,jdbcType=TINYINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=BIGINT},
      created_by_name = #{createdByName,jdbcType=VARCHAR},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      updated_by_name = #{updatedByName,jdbcType=VARCHAR},
      audit_by = #{auditBy,jdbcType=BIGINT},
      audit_by_name = #{auditByName,jdbcType=VARCHAR},
      audit_date = #{auditDate,jdbcType=TIMESTAMP},
      audit_reason = #{auditReason,jdbcType=VARCHAR},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>