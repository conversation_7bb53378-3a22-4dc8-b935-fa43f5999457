<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cowell.pricecenter.mapper.PriceSyncRecordMapper" >
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.PriceSyncRecord" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="business_id" property="businessId" jdbcType="BIGINT" />
    <result column="store_id" property="storeId" jdbcType="BIGINT" />
    <result column="store_name" property="storeName" jdbcType="VARCHAR" />
    <result column="goods_no" property="goodsNo" jdbcType="VARCHAR" />
    <result column="sku_name" property="skuName" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="BIGINT" />
    <result column="price_type" property="priceType" jdbcType="INTEGER" />
    <result column="sync_result" property="syncResult" jdbcType="TINYINT" />
    <result column="sync_warn_desc" property="syncWarnDesc" jdbcType="VARCHAR" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="gmt_create" property="gmtCreate" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
    <result column="gmt_update" property="gmtUpdate" jdbcType="TIMESTAMP" />
    <result column="status" property="status" jdbcType="TINYINT" />
    <result column="extend" property="extend" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="channel" property="channel" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, business_id, store_id, store_name, goods_no, sku_name, price, price_type, sync_result, 
    sync_warn_desc, created_by, gmt_create, updated_by, gmt_update, status, extend, version, 
    channel
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.cowell.pricecenter.entity.PriceSyncRecordExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from price_sync_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from price_sync_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from price_sync_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.PriceSyncRecordExample" >
    delete from price_sync_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.cowell.pricecenter.entity.PriceSyncRecord" >
    insert into price_sync_record (id, business_id, store_id, 
      store_name, goods_no, sku_name, 
      price, price_type, sync_result, 
      sync_warn_desc, created_by, gmt_create, 
      updated_by, gmt_update, status, 
      extend, version, channel
      )
    values (#{id,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT}, 
      #{storeName,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{price,jdbcType=BIGINT}, #{priceType,jdbcType=INTEGER}, #{syncResult,jdbcType=TINYINT}, 
      #{syncWarnDesc,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{updatedBy,jdbcType=VARCHAR}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT}, 
      #{extend,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{channel,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.cowell.pricecenter.entity.PriceSyncRecord" >
    insert into price_sync_record
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="businessId != null" >
        business_id,
      </if>
      <if test="storeId != null" >
        store_id,
      </if>
      <if test="storeName != null" >
        store_name,
      </if>
      <if test="goodsNo != null" >
        goods_no,
      </if>
      <if test="skuName != null" >
        sku_name,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="priceType != null" >
        price_type,
      </if>
      <if test="syncResult != null" >
        sync_result,
      </if>
      <if test="syncWarnDesc != null" >
        sync_warn_desc,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="gmtCreate != null" >
        gmt_create,
      </if>
      <if test="updatedBy != null" >
        updated_by,
      </if>
      <if test="gmtUpdate != null" >
        gmt_update,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="extend != null" >
        extend,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="channel != null" >
        channel,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null" >
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null" >
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null" >
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null" >
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=BIGINT},
      </if>
      <if test="priceType != null" >
        #{priceType,jdbcType=INTEGER},
      </if>
      <if test="syncResult != null" >
        #{syncResult,jdbcType=TINYINT},
      </if>
      <if test="syncWarnDesc != null" >
        #{syncWarnDesc,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null" >
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null" >
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null" >
        #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        #{version,jdbcType=INTEGER},
      </if>
      <if test="channel != null" >
        #{channel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.PriceSyncRecordExample" resultType="java.lang.Integer" >
    select count(*) from price_sync_record
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update price_sync_record
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null" >
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.storeId != null" >
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null" >
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.goodsNo != null" >
        goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.skuName != null" >
        sku_name = #{record.skuName,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=BIGINT},
      </if>
      <if test="record.priceType != null" >
        price_type = #{record.priceType,jdbcType=INTEGER},
      </if>
      <if test="record.syncResult != null" >
        sync_result = #{record.syncResult,jdbcType=TINYINT},
      </if>
      <if test="record.syncWarnDesc != null" >
        sync_warn_desc = #{record.syncWarnDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtCreate != null" >
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null" >
        updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.gmtUpdate != null" >
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.status != null" >
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.extend != null" >
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null" >
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.channel != null" >
        channel = #{record.channel,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update price_sync_record
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      sku_name = #{record.skuName,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=BIGINT},
      price_type = #{record.priceType,jdbcType=INTEGER},
      sync_result = #{record.syncResult,jdbcType=TINYINT},
      sync_warn_desc = #{record.syncWarnDesc,jdbcType=VARCHAR},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=VARCHAR},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      status = #{record.status,jdbcType=TINYINT},
      extend = #{record.extend,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=INTEGER},
      channel = #{record.channel,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.PriceSyncRecord" >
    update price_sync_record
    <set >
      <if test="businessId != null" >
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null" >
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null" >
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null" >
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        sku_name = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=BIGINT},
      </if>
      <if test="priceType != null" >
        price_type = #{priceType,jdbcType=INTEGER},
      </if>
      <if test="syncResult != null" >
        sync_result = #{syncResult,jdbcType=TINYINT},
      </if>
      <if test="syncWarnDesc != null" >
        sync_warn_desc = #{syncWarnDesc,jdbcType=VARCHAR},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null" >
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null" >
        updated_by = #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null" >
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null" >
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="extend != null" >
        extend = #{extend,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="channel != null" >
        channel = #{channel,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.PriceSyncRecord" >
    update price_sync_record
    set business_id = #{businessId,jdbcType=BIGINT},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      sku_name = #{skuName,jdbcType=VARCHAR},
      price = #{price,jdbcType=BIGINT},
      price_type = #{priceType,jdbcType=INTEGER},
      sync_result = #{syncResult,jdbcType=TINYINT},
      sync_warn_desc = #{syncWarnDesc,jdbcType=VARCHAR},
      created_by = #{createdBy,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=VARCHAR},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      status = #{status,jdbcType=TINYINT},
      extend = #{extend,jdbcType=VARCHAR},
      version = #{version,jdbcType=INTEGER},
      channel = #{channel,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>