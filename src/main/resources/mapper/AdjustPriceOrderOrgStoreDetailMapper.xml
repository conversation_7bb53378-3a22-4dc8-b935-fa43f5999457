<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cowell.pricecenter.mapper.AdjustPriceOrderOrgStoreDetailMapper">
  <resultMap id="BaseResultMap" type="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="org_store_detail_id" jdbcType="VARCHAR" property="orgStoreDetailId" />
    <result column="adjust_code" jdbcType="VARCHAR" property="adjustCode" />
    <result column="org_id" jdbcType="BIGINT" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="adjust_price_level" jdbcType="INTEGER" property="adjustPriceLevel" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="platform_id" jdbcType="BIGINT" property="platformId" />
    <result column="platform_name" jdbcType="VARCHAR" property="platformName" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="sku_id" jdbcType="BIGINT" property="skuId" />
    <result column="price_type_code" jdbcType="VARCHAR" property="priceTypeCode" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="adjust_execute_time" jdbcType="TIMESTAMP" property="adjustExecuteTime" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="extend" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, org_store_detail_id, adjust_code, org_id, org_name, adjust_price_level, store_id,
    store_name, business_id, business_name, platform_id, platform_name, goods_no, sku_id, price_type_code,price,
    channel_id, `status`, adjust_execute_time,gmt_create, gmt_update, extend
  </sql>
  <select id="selectByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from adjust_price_order_org_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from adjust_price_order_org_store_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from adjust_price_order_org_store_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetailExample">
    delete from adjust_price_order_org_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail" useGeneratedKeys="true">
    insert into adjust_price_order_org_store_detail (org_store_detail_id, adjust_code, org_id,
      org_name, adjust_price_level, store_id,
      store_name, business_id, business_name,
      platform_id, platform_name,  goods_no, sku_id,
      price_type_code, price, channel_id, `status`,
      adjust_execute_time, gmt_create, gmt_update, extend
      )
    values (#{orgStoreDetailId,jdbcType=VARCHAR}, #{adjustCode,jdbcType=VARCHAR}, #{orgId,jdbcType=BIGINT},
      #{orgName,jdbcType=VARCHAR}, #{adjustPriceLevel,jdbcType=INTEGER}, #{storeId,jdbcType=BIGINT},
      #{storeName,jdbcType=VARCHAR}, #{businessId,jdbcType=BIGINT}, #{businessName,jdbcType=VARCHAR},
      #{platformId,jdbcType=BIGINT}, #{platformName,jdbcType=VARCHAR},  #{goodsNo,jdbcType=VARCHAR}, #{skuId,jdbcType=BIGINT},
      #{priceTypeCode,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{channelId,jdbcType=INTEGER}, #{status,jdbcType=TINYINT},
      #{adjustExecuteTime,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtUpdate,jdbcType=TIMESTAMP}, #{extend,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail" useGeneratedKeys="true">
    insert into adjust_price_order_org_store_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orgStoreDetailId != null">
        org_store_detail_id,
      </if>
      <if test="adjustCode != null">
        adjust_code,
      </if>
      <if test="orgId != null">
        org_id,
      </if>
      <if test="orgName != null">
        org_name,
      </if>
      <if test="adjustPriceLevel != null">
        adjust_price_level,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="platformId != null">
        platform_id,
      </if>
      <if test="platformName != null">
        platform_name,
      </if>
        <if test="goodsNo != null">
            goods_no,
        </if>
        <if test="skuId != null">
            sku_id,
        </if>
        <if test="priceTypeCode != null">
            price_type_code,
        </if>
        <if test="price != null">
            price,
        </if>
        <if test="channelId != null">
            channel_id,
        </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="adjustExecuteTime != null">
        adjust_execute_time,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="extend != null">
            extend,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orgStoreDetailId != null">
        #{orgStoreDetailId,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceLevel != null">
        #{adjustPriceLevel,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        #{platformName,jdbcType=VARCHAR},
      </if>
        <if test="goodsNo != null">
            #{goodsNo,jdbcType=VARCHAR},
        </if>
        <if test="skuId != null">
            #{skuId,jdbcType=BIGINT},
        </if>
        <if test="priceTypeCode != null">
            #{priceTypeCode,jdbcType=VARCHAR},
        </if>
        <if test="price != null">
            #{price,jdbcType=DECIMAL},
        </if>
        <if test="channelId != null">
            #{channelId,jdbcType=INTEGER},
        </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="adjustExecuteTime != null">
        #{adjustExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        #{extend,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetailExample" resultType="java.lang.Long">
    select count(*) from adjust_price_order_org_store_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update adjust_price_order_org_store_detail
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.orgStoreDetailId != null">
        org_store_detail_id = #{record.orgStoreDetailId,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustCode != null">
        adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="record.orgId != null">
        org_id = #{record.orgId,jdbcType=BIGINT},
      </if>
      <if test="record.orgName != null">
        org_name = #{record.orgName,jdbcType=VARCHAR},
      </if>
      <if test="record.adjustPriceLevel != null">
        adjust_price_level = #{record.adjustPriceLevel,jdbcType=INTEGER},
      </if>
      <if test="record.storeId != null">
        store_id = #{record.storeId,jdbcType=BIGINT},
      </if>
      <if test="record.storeName != null">
        store_name = #{record.storeName,jdbcType=VARCHAR},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.businessName != null">
        business_name = #{record.businessName,jdbcType=VARCHAR},
      </if>
      <if test="record.platformId != null">
        platform_id = #{record.platformId,jdbcType=BIGINT},
      </if>
      <if test="record.platformName != null">
        platform_name = #{record.platformName,jdbcType=VARCHAR},
      </if>
        <if test="record.goodsNo != null">
            goods_no = #{record.goodsNo,jdbcType=VARCHAR},
        </if>
        <if test="record.skuId != null">
            sku_id = #{record.skuId,jdbcType=BIGINT},
        </if>
        <if test="record.priceTypeCode != null">
            price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
        </if>
        <if test="record.price != null">
            price = #{record.price,jdbcType=DECIMAL},
        </if>
        <if test="record.channelId != null">
            channel_id = #{record.channelId,jdbcType=INTEGER},
        </if>
        <if test="record.result != null">
            result = #{record.result,jdbcType=VARCHAR},
        </if>
      <if test="record.status != null">
        `status` = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.adjustExecuteTime != null">
        adjust_execute_time = #{record.adjustExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtCreate != null">
        gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.gmtUpdate != null">
        gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.extend != null">
        extend = #{record.extend,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update adjust_price_order_org_store_detail
    set id = #{record.id,jdbcType=BIGINT},
      org_store_detail_id = #{record.orgStoreDetailId,jdbcType=VARCHAR},
      adjust_code = #{record.adjustCode,jdbcType=VARCHAR},
      org_id = #{record.orgId,jdbcType=BIGINT},
      org_name = #{record.orgName,jdbcType=VARCHAR},
      adjust_price_level = #{record.adjustPriceLevel,jdbcType=INTEGER},
      store_id = #{record.storeId,jdbcType=BIGINT},
      store_name = #{record.storeName,jdbcType=VARCHAR},
      business_id = #{record.businessId,jdbcType=BIGINT},
      business_name = #{record.businessName,jdbcType=VARCHAR},
      platform_id = #{record.platformId,jdbcType=BIGINT},
      platform_name = #{record.platformName,jdbcType=VARCHAR},
      goods_no = #{record.goodsNo,jdbcType=VARCHAR},
      sku_id = #{record.skuId,jdbcType=BIGINT},
      price_type_code = #{record.priceTypeCode,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DECIMAL},
      channel_id = #{record.channelId,jdbcType=INTEGER},
      `status` = #{record.status,jdbcType=TINYINT},
      adjust_execute_time = #{record.adjustExecuteTime,jdbcType=TIMESTAMP},
      gmt_create = #{record.gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{record.gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{record.extend,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail">
    update adjust_price_order_org_store_detail
    <set>
      <if test="orgStoreDetailId != null">
        org_store_detail_id = #{orgStoreDetailId,jdbcType=VARCHAR},
      </if>
      <if test="adjustCode != null">
        adjust_code = #{adjustCode,jdbcType=VARCHAR},
      </if>
      <if test="orgId != null">
        org_id = #{orgId,jdbcType=BIGINT},
      </if>
      <if test="orgName != null">
        org_name = #{orgName,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceLevel != null">
        adjust_price_level = #{adjustPriceLevel,jdbcType=INTEGER},
      </if>
      <if test="storeId != null">
        store_id = #{storeId,jdbcType=BIGINT},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="platformId != null">
        platform_id = #{platformId,jdbcType=BIGINT},
      </if>
      <if test="platformName != null">
        platform_name = #{platformName,jdbcType=VARCHAR},
      </if>
        <if test="goodsNo != null">
            goods_no = #{goodsNo,jdbcType=VARCHAR},
        </if>
        <if test="skuId != null">
            sku_id = #{skuId,jdbcType=BIGINT},
        </if>
        <if test="priceTypeCode != null">
            price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
        </if>
        <if test="price != null">
            price = #{price,jdbcType=DECIMAL},
        </if>
        <if test="channelId != null">
            channel_id = #{channelId,jdbcType=INTEGER},
        </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="adjustExecuteTime != null">
        adjust_execute_time = #{adjustExecuteTime,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtUpdate != null">
        gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="extend != null">
        extend = #{extend,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail">
    update adjust_price_order_org_store_detail
    set org_store_detail_id = #{orgStoreDetailId,jdbcType=VARCHAR},
      adjust_code = #{adjustCode,jdbcType=VARCHAR},
      org_id = #{orgId,jdbcType=BIGINT},
      org_name = #{orgName,jdbcType=VARCHAR},
      adjust_price_level = #{adjustPriceLevel,jdbcType=INTEGER},
      store_id = #{storeId,jdbcType=BIGINT},
      store_name = #{storeName,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=BIGINT},
      business_name = #{businessName,jdbcType=VARCHAR},
      platform_id = #{platformId,jdbcType=BIGINT},
      platform_name = #{platformName,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      sku_id = #{skuId,jdbcType=BIGINT},
      price_type_code = #{priceTypeCode,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      channel_id = #{channelId,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      adjust_execute_time = #{adjustExecuteTime,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_update = #{gmtUpdate,jdbcType=TIMESTAMP},
      extend = #{extend,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
