package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.PriceWarnService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 价格执价异常预警
 */
@JobHandler(value = "pushPriceWarnResultNoticeJobHandler")
@Slf4j
@Component
public class PushPriceWarnResultNoticeJobHandler extends IJobHandler {

    @Autowired
    private PriceWarnService priceWarnService;

    @Override
    @NewSpan
    public ReturnT<String> execute(String s) {
        log.info("PushPriceWarnResultNoticeJobHandler|XXL-JOB, start-----------------");
        try {
        	priceWarnService.pushPriceWarnResultNoticeMsg();
        } catch (Exception e) {
        	log.error("PushPriceWarnResultNoticeJobHandler|XXL-JOB价格预警异常",e);
            return FAIL;
        }
        return SUCCESS;
    }
}
