package com.cowell.pricecenter.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.entity.PricePushTask;
import com.cowell.pricecenter.entity.PricePushTaskExample;
import com.cowell.pricecenter.enums.PushResultEnum;
import com.cowell.pricecenter.enums.StatusEnum;
import com.cowell.pricecenter.mapper.PricePushTaskMapper;
import com.cowell.pricecenter.mq.producer.PricePushTaskExeProducer;
import com.cowell.pricecenter.service.IStoreGoodsAttrService;
import com.cowell.pricecenter.service.PriceCenterComparePriceService;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 定时清理门店商品属性 蓝标绿标F
 * @date 2025-04-22
 */
@JobHandler(value = "storeGoodsAttrJobHandler")
@Component
public class StoreGoodsAttrJobHandler extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(StoreGoodsAttrJobHandler.class);
    @Autowired
    private IStoreGoodsAttrService storeGoodsAttrService;

    @NewSpan("storeGoodsAttrJobHandler")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("开始执行xxl job：定时清理门店商品属性 蓝标绿标param={}", param);
        JSONObject jsonObject = JSON.parseObject(param);
        Integer dayOffset = jsonObject.getInteger("dayOffset");
        //根据传入的天数值 获取当前日期前 or 后的日期
        Date deleteBeforeNow = DateUtils.getDateByFormat(dayOffset);
        log.info("开始执行xxl job：定时清理门店商品属性 deleteBeforeNow={}", deleteBeforeNow);
        storeGoodsAttrService.deleteBatch(null, null, deleteBeforeNow, Constants.BATCH_DELETE_SIZE_10000);
        return SUCCESS;
    }
}
