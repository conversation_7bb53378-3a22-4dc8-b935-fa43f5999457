package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.PriceBusinessDetailInitService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 初始化连锁最后一次采购价
 * <AUTHOR>
 * @date 2022/9/16 11:28
 */
@JobHandler(value = "initCGJJobHandler")
@Component
public class InitCGJJobHandler  extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(AdjustPriceOrderJobHandler.class);

    @Autowired
    private PriceBusinessDetailInitService priceBusinessDetailInitService;

    @NewSpan("initCGJJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始执行xxl job：【initCGJJobHandler】");
        if (StringUtils.isBlank(s)) {
            return SUCCESS;
        }
        String[] businessIdStrArray = s.split(",");
        for (String businessIdStr : businessIdStrArray) {
            priceBusinessDetailInitService.initCGJByBusiness(Long.parseLong(businessIdStr));
        }
        return SUCCESS;
    }
}
