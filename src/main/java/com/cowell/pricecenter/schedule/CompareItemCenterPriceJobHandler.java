package com.cowell.pricecenter.schedule;

import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.config.AdjustPriceConfig;
import com.cowell.pricecenter.service.IPriceToolsService;
import com.cowell.pricecenter.service.OmsService;
import com.cowell.pricecenter.service.PriceSync2PlatformRecordService;
import com.cowell.pricecenter.service.PriceWarnRecordService;
import com.cowell.pricecenter.service.dto.CrmStoreBusinessDTO;
import com.cowell.pricecenter.service.dto.CrmStoreDTO;
import com.cowell.pricecenter.service.dto.PriceSyncRecordDTO;
import com.cowell.pricecenter.service.dto.PriceWarningRecordDTO;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.service.impl.PriceToolsServiceImpl;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Description: 删除15天前的价格告警及价格同步日志
 * @Author: liubw
 * @Date: 2019/3/19 8:14 PM
 */
@Slf4j
@JobHandler(value = "compareItemCenterPriceJobHandler")
@Component
public class CompareItemCenterPriceJobHandler extends IJobHandler {


    private final Logger logger = LoggerFactory.getLogger(CompareItemCenterPriceJobHandler.class);

    @Autowired
    @Qualifier("taskExecutorTrace3")
    private AsyncTaskExecutor asyncTaskExecutor3;
    @Autowired
    private StoreService storeService;
    @Autowired
    private IPriceToolsService iPriceToolsService;
    @Value("${compare.item.center.price:}")
    private String priceCompare;

    @Autowired
    private AdjustPriceConfig adjustPriceConfig;


    @NewSpan("CompareItemCenterPriceJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {

        String businessIdStr = this.getBusinessId(s);

        if (StringUtils.isBlank(businessIdStr)){
            return ReturnT.SUCCESS;
        }

        long businessId = Long.parseLong(businessIdStr);

        try {
            //按连锁下的所有门店配置多个（一个门店配置一个）
            List<CrmStoreBusinessDTO> storeList = storeService.getCrmStoreByBusinessIds(new Long[]{businessId});
            for (CrmStoreBusinessDTO crmStore: storeList) {
                List<CrmStoreDTO> crmStoreDTOList = crmStore.getCrmStoreDTOList();
                logger.info("门店价格对比商家中心crmStoreDTOList返回数量{}", crmStoreDTOList.size());
                for (CrmStoreDTO crmStoreDTO : crmStoreDTOList) {
                    asyncTaskExecutor3.execute(()->{
                        iPriceToolsService.compareItemCenterPriceToUpdate(businessId,crmStoreDTO.getId().toString(),"LSJ", adjustPriceConfig.getDefaultChannelId());
                    });
                }
            }
        } catch (Exception e) {
            log.error("<==定时任务门店价格对比商家中心失败，businessId：{}",businessId);
        }
        return ReturnT.SUCCESS;
    }

    private String getBusinessId(String s){
        log.info("定时刷新门店价格信息 参数:{} 配置:{}",s,priceCompare);
        if (StringUtils.isBlank(priceCompare)){
            return null;
        }

        if (StringUtils.isNotBlank(s)){
            return s;
        }

        SimpleDateFormat sdf =new SimpleDateFormat("HH");
        String appointHour = sdf.format(new Date());

        //根据指针获取comId
        String[] businessIdArr = priceCompare.split(";");
        log.info("配置的连锁列表:{} 当前指针:{}", JSONObject.toJSONString(businessIdArr),appointHour);

        String timeToBusinessId = "";

        for (String businessIdStr: businessIdArr) {
            if (businessIdStr.contains("time-"+appointHour+":")){
                timeToBusinessId = businessIdStr;
            }
        }

        if (StringUtils.isBlank(timeToBusinessId)){
            log.info("定时刷新门店价格信息 指针对应的连锁不存在:{}",appointHour);
            return "";
        }

        String[] appointHourBusinessIdArr = timeToBusinessId.split(":");

        return appointHourBusinessIdArr[1];
    }
}
