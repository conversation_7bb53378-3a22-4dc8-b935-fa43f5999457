package com.cowell.pricecenter.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.service.IPriceStoreDetailNewIdBatchUpdateService;
import com.cowell.pricecenter.utils.TracerBean;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Span;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * Price Store Detail批量更新new_id字段XXL-Job处理器
 * 支持512张表(128*4个数据源)的批量更新任务调度
 *
 * 参数格式示例:
 * - 更新所有表: {"mode":"db","dsIndex":0}
 * - 更新指定表: {"mode":"table","dsIndex":0,"tableName":"price_store_detail_1"}
 * - 更新指定门店: {"mode":"store","dsIndex":0,"tableName":"price_store_detail_1","storeId":"123"}
 * - 查询进度: {"mode":"progress","dsIndex":0,"tableName":"price_store_detail_1","storeId":"123"}
 * - 停止任务: {"mode":"stop"}
 *
 * <AUTHOR>
 */
@JobHandler(value = "priceStoreDetailNewIdBatchUpdateHandler")
@Component
public class PriceStoreDetailNewIdBatchUpdateHandler extends IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(PriceStoreDetailNewIdBatchUpdateHandler.class);

    @Autowired
    private IPriceStoreDetailNewIdBatchUpdateService batchUpdateService;

    @Autowired
    private TracerBean tracerBean;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        logger.info("XXL-JOB任务开始执行，参数: {}", param);
        XxlJobLogger.log("Price Store Detail批量更新任务开始执行，参数: {}", param);
        Span span = tracerBean.startSpan();
        try {
            // 解析参数
            JobParams jobParams = parseJobParams(param);
            // 根据模式执行不同的操作
            switch (jobParams.getMode()) {
                case "db":
                    executeAllTablesUpdate(jobParams);
                    break;
                case "table":
                    executeTableUpdate(jobParams);
                    break;
                case "store":
                    executeStoreUpdate(jobParams);
                    break;
                case "progress":
                    executeProgressQuery(jobParams);
                    break;
                case "stop":
                    executeStopTask();
                    break;
                default:
                    String errorMsg = "未知的任务模式: " + jobParams.getMode();
                    logger.warn(errorMsg);
                    XxlJobLogger.log(errorMsg);
                    return new ReturnT<>(ReturnT.FAIL_CODE, errorMsg);
            }

            String successMsg = "任务执行完成";
            logger.info(successMsg);
            XxlJobLogger.log(successMsg);
            return ReturnT.SUCCESS;

        } catch (Exception e) {
            String errorMsg = "XXL-JOB任务执行失败: " + e.getMessage();
            logger.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            return new ReturnT<>(ReturnT.FAIL_CODE, errorMsg);
        }finally {
            tracerBean.close(span);
        }
    }

    /**
     * 执行单库全表更新任务（异步）
     */
    private void executeAllTablesUpdate(JobParams params) {
        XxlJobLogger.log("开始执行所有表的批量更新任务");
        // 检查是否有任务正在运行
        if (batchUpdateService.isTaskRunning()) {
            String msg = "批量更新任务已在运行中，跳过本次执行";
            logger.warn(msg);
            XxlJobLogger.log(msg);
            return;
        }
        try {
            long startTime = System.currentTimeMillis();
            XxlJobLogger.log("批量更新所有表任务开始，开始时间: {}",
                           new java.util.Date(startTime).toString());
            batchUpdateService.batchUpdateAllTables(params.getDsIndex());
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            XxlJobLogger.log("批量更新所有表任务完成，耗时: {} ms", duration);
        } catch (Exception e) {
            logger.error("异步执行批量更新所有表任务失败", e);
            XxlJobLogger.log("异步执行批量更新所有表任务失败: {}", e.getMessage());
        }
        XxlJobLogger.log("批量更新所有表任务已提交异步执行");
    }

    /**
     * 执行指定表更新任务
     */
    private void executeTableUpdate(JobParams params) {
        validateTableParams(params);
        XxlJobLogger.log("开始更新数据源{} 表{}", params.getDsIndex(), params.getTableName());
        try {
            int updatedCount = batchUpdateService.batchUpdateTable(
                params.getDsIndex(),
                params.getTableName(),
                null
            );
            XxlJobLogger.log("数据源{} 表{} 更新完成，共更新{}条记录",params.getDsIndex(), params.getTableName(), updatedCount);
        } catch (Exception e) {
            String errorMsg = String.format("更新数据源%d 表%s 失败: %s",
                                           params.getDsIndex(), params.getTableName(), e.getMessage());
            logger.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            throw e;
        }
    }

    /**
     * 执行指定门店更新任务
     */
    private void executeStoreUpdate(JobParams params) {
        validateStoreParams(params);
        XxlJobLogger.log("开始更新数据源{} 表{} 门店{}",params.getDsIndex(), params.getTableName(), params.getStoreId());
        try {
            int updatedCount = batchUpdateService.batchUpdateTable(
                params.getDsIndex(),
                params.getTableName(),
                params.getStoreId()
            );
            XxlJobLogger.log("数据源{} 表{} 门店{} 更新完成，共更新{}条记录",
                           params.getDsIndex(), params.getTableName(),
                           params.getStoreId(), updatedCount);

        } catch (Exception e) {
            String errorMsg = String.format("更新数据源%d 表%s 门店%s 失败: %s",
                                           params.getDsIndex(), params.getTableName(),
                                           params.getStoreId(), e.getMessage());
            logger.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            throw e;
        }
    }

    /**
     * 执行进度查询任务
     */
    private void executeProgressQuery(JobParams params) {
        validateTableParams(params);
        XxlJobLogger.log("查询数据源{} 表{} {} 的进度",
                       params.getDsIndex(), params.getTableName(),
                       params.getStoreId() != null ? "门店" + params.getStoreId() : "全部门店");

        try {
            batchUpdateService.showProgress(
                params.getDsIndex(),
                params.getTableName(),
                params.getStoreId()
            );
            XxlJobLogger.log("进度查询完成");
        } catch (Exception e) {
            String errorMsg = String.format("查询数据源%d 表%s 进度失败: %s",
                                           params.getDsIndex(), params.getTableName(), e.getMessage());
            logger.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            throw e;
        }
    }

    /**
     * 执行停止任务
     */
    private void executeStopTask() {
        XxlJobLogger.log("执行停止当前批量更新任务");

        try {
            if (batchUpdateService.isTaskRunning()) {
                batchUpdateService.stopCurrentTask();
                XxlJobLogger.log("已向运行中的任务发送停止信号");
            } else {
                XxlJobLogger.log("当前没有运行中的批量更新任务");
            }

        } catch (Exception e) {
            String errorMsg = "停止任务失败: " + e.getMessage();
            logger.error(errorMsg, e);
            XxlJobLogger.log(errorMsg);
            throw e;
        }
    }

    /**
     * 解析任务参数
     */
    private JobParams parseJobParams(String param) {
        JobParams jobParams = new JobParams();
        if (StringUtils.hasText(param)) {
            try {
                JSONObject jsonParam = JSON.parseObject(param);
                jobParams.setMode(jsonParam.getString("mode"));
                if (jsonParam.containsKey("dsIndex")) {
                    jobParams.setDsIndex(jsonParam.getInteger("dsIndex"));
                }
                if (jsonParam.containsKey("tableName")) {
                    jobParams.setTableName(jsonParam.getString("tableName"));
                }
                if (jsonParam.containsKey("storeId")) {
                    jobParams.setStoreId(jsonParam.getString("storeId"));
                }
            } catch (Exception e) {
                logger.warn("解析任务参数失败，使用默认参数，原始参数: {}", param, e);
                jobParams.setMode("error"); // 默认为全表更新
            }
        } else {
            jobParams.setMode("error"); // 默认为全表更新
        }
        return jobParams;
    }

    /**
     * 验证表更新参数
     */
    private void validateTableParams(JobParams params) {
        if (params.getDsIndex() == null || params.getDsIndex() < 0 || params.getDsIndex() > 3) {
            throw new IllegalArgumentException("数据源索引必须在0-3之间");
        }

        if (!StringUtils.hasText(params.getTableName())) {
            throw new IllegalArgumentException("表名不能为空");
        }

        if (!params.getTableName().startsWith("price_store_detail_")) {
            throw new IllegalArgumentException("表名格式错误，必须以price_store_detail_开头");
        }
    }

    /**
     * 验证门店更新参数
     */
    private void validateStoreParams(JobParams params) {
        validateTableParams(params);

        if (!StringUtils.hasText(params.getStoreId())) {
            throw new IllegalArgumentException("门店ID不能为空");
        }
    }

    /**
     * 任务参数封装类
     */
    private static class JobParams {
        private String mode = "db";
        private Integer dsIndex;
        private String tableName;
        private String storeId;

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public Integer getDsIndex() {
            return dsIndex;
        }

        public void setDsIndex(Integer dsIndex) {
            this.dsIndex = dsIndex;
        }

        public String getTableName() {
            return tableName;
        }

        public void setTableName(String tableName) {
            this.tableName = tableName;
        }

        public String getStoreId() {
            return storeId;
        }

        public void setStoreId(String storeId) {
            this.storeId = storeId;
        }

        @Override
        public String toString() {
            return String.format("JobParams{mode='%s', dsIndex=%d, tableName='%s', storeId='%s'}",
                               mode, dsIndex, tableName, storeId);
        }
    }
}
