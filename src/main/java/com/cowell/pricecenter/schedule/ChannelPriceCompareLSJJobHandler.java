package com.cowell.pricecenter.schedule;

import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.enums.PriceSyncTaskTypeEnum;
import com.cowell.pricecenter.mq.producer.PriceSyncTaskProducer;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskVO;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.MdmBusinessBaseToRedisDTO;
import com.cowell.pricecenter.service.dto.MdmStoreBaseToRedisDTO;
import com.cowell.pricecenter.web.rest.vo.ForestGoodsVO;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskRejectedException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.RejectedExecutionException;
import java.util.stream.Collectors;

/**
 * @Description: 渠道价格对比零售价
 * @Author: liw
 * @Date: 2019/3/19 8:14 PM
 */
@Slf4j
@JobHandler(value = "channelPriceCompareLSJJobHandler")
@Component
public class ChannelPriceCompareLSJJobHandler extends IJobHandler {

    @Autowired
    @Qualifier("taskExecutorTrace3")
    private AsyncTaskExecutor asyncTaskExecutor3;
    @Autowired
    private FeignStoreService feignStoreService;
    @Autowired
    private PriceSyncTaskProducer priceSyncTaskProducer;
    @Value("${appoint.time.channel.price.compare.lsj:}")
    private String priceCompareAppointTimeByLSJ;


    @NewSpan("ChannelPriceCompareLSJJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("<==ChannelPriceCompareLSJJobHandler||execute||请求参数:{}",s);
        try {

            if (StringUtils.isEmpty(s)){
                log.info("<==ChannelPriceCompareLSJJobHandler||execute||请求参数为空");
                return ReturnT.SUCCESS;
            }

            String[] businessIdAndPriceType = s.split(":");
            log.info("<==ChannelPriceCompareLSJJobHandler||execute||请求参数为:{}", JSONObject.toJSONString(businessIdAndPriceType));

            String businessIdStr = businessIdAndPriceType[0];
            String priceTypeCode = businessIdAndPriceType[1];

            List<MdmBusinessBaseToRedisDTO> mdmBusinessBaseToRedisDTOS = Lists.newArrayList();

            //如果是全连锁 需要查询 接口
            if ("12345".equals(businessIdStr)){
                //一个小时对比两个连锁
                mdmBusinessBaseToRedisDTOS = this.getPriceCompareAppointTimeBusinessIdByLSJ();
            }else{
                MdmBusinessBaseToRedisDTO mdmBusinessBaseToRedisDTO = new MdmBusinessBaseToRedisDTO();
                mdmBusinessBaseToRedisDTO.setBusinessId(Long.parseLong(businessIdStr));
                mdmBusinessBaseToRedisDTOS.add(mdmBusinessBaseToRedisDTO);
            }



            if (CollectionUtils.isEmpty(mdmBusinessBaseToRedisDTOS)){
                log.info("<==ChannelPriceCompareLSJJobHandler||execute||根据配置获取到的连锁信息为空");
                return ReturnT.SUCCESS;
            }


            for (MdmBusinessBaseToRedisDTO item: mdmBusinessBaseToRedisDTOS) {
                int sleepCount = 0;
                try{

                    asyncTaskExecutor3.execute(()->{

                        List<MdmStoreBaseToRedisDTO> storeBaseToRedisDTOS = feignStoreService.findMdmStoreByBusinessId(item.getBusinessId());

                        if (CollectionUtils.isEmpty(storeBaseToRedisDTOS)){
                            log.warn("<==ChannelPriceCompareLSJJobHandler||execute||根据连锁ID获取门店为空 :{}",item.toString());
                            return;
                        }

                        for (MdmStoreBaseToRedisDTO mdmStoreBaseToRedisDTO: storeBaseToRedisDTOS) {
                            Long storeId = mdmStoreBaseToRedisDTO.getStoreId();

                            PriceSyncTaskVO priceSyncTaskVO = new PriceSyncTaskVO();
                            priceSyncTaskVO.setPriceTypeCode(priceTypeCode);
                            priceSyncTaskVO.setBusinessId(item.getBusinessId());
                            priceSyncTaskVO.setStoreId(storeId);
                            priceSyncTaskVO.setType(PriceSyncTaskTypeEnum.CHANNEL_PRICE_COMPARE_LSJ.getType());
                            priceSyncTaskProducer.sendPriceSyncTask(priceSyncTaskVO);
                        }
                    });
                }catch (RejectedExecutionException e2){
                    log.warn("<==ChannelPriceCompareLSJJobHandler||execute||RejectedExecutionException||异常:",e2);
                    try {
                        sleepCount++;
                        Thread.sleep(1000);
                    } catch (InterruptedException ite) {
                        log.error("任务执行sleep错误:",ite);
                    }
                }catch (Exception e3){
                    log.warn("<==ChannelPriceCompareLSJJobHandler||execute||Exception||异常:",e3);
                }finally {
                    if (sleepCount > 10) {
                        log.error("任务执行" + sleepCount + "仍然失败，抛弃");
                    }
                }
            }
        } catch (Exception e){
            log.warn("");
        }
        return ReturnT.SUCCESS;
    }


    private List<MdmBusinessBaseToRedisDTO> getPriceCompareAppointTimeBusinessIdByLSJ(){
        log.info("定时刷新门店渠道价格和零售价格信息 配置:{}",priceCompareAppointTimeByLSJ);

        List<MdmBusinessBaseToRedisDTO> mdmBusinessBaseToRedisDTOS = feignStoreService.findAllMdmBusinessBase();

        if (org.apache.commons.lang3.StringUtils.isEmpty(priceCompareAppointTimeByLSJ)){
            return new ArrayList<>();
        }

        //连锁对应map
        Map<Long, MdmBusinessBaseToRedisDTO> map = mdmBusinessBaseToRedisDTOS.stream().collect(Collectors.toMap(MdmBusinessBaseToRedisDTO::getBusinessId, item -> item,(k1,k2)->k1));

        SimpleDateFormat sdf =new SimpleDateFormat("HH");
        String appointHour = sdf.format(new Date());

        //根据指针获取comId
        String[] comIdArr = priceCompareAppointTimeByLSJ.split(";");
        log.info("定时刷新门店渠道价格和零售价格信息 配置的连锁列表:{} 当前指针:{}",JSONObject.toJSONString(comIdArr),appointHour);

        String timeToComId = "";

        for (String comIdStr: comIdArr) {
            if (comIdStr.contains("time-"+appointHour+":")){
                timeToComId = comIdStr;
            }
        }

        if (org.apache.commons.lang.StringUtils.isBlank(timeToComId)){
            log.info("定时刷新门店渠道价格和零售价格信息 指针对应的连锁不存在:{}",appointHour);
            return new ArrayList<>();
        }


        List<MdmBusinessBaseToRedisDTO> newMdmBusinessBaseToRedisDTOS = Lists.newArrayList();

        String[] appointHourComIdArr = timeToComId.split(":");
        String businessIds = appointHourComIdArr[1];

        if (StringUtils.isEmpty(businessIds) || "00000".equals(businessIds)){
            return new ArrayList<>();
        }

        for (String businessIdStr : businessIds.split(",")){
            newMdmBusinessBaseToRedisDTOS.add(map.get(Long.parseLong(businessIdStr)));
        }

        return newMdmBusinessBaseToRedisDTOS;
    }
}
