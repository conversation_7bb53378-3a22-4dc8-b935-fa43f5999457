package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.IAdjustOrderPriceHistoryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 价格调整记录汇总
 */
@JobHandler(value = "priceHistoryCollectJobHandler")
@Slf4j
@Component
public class AdjustPriceHistoryCollectJobHandler extends IJobHandler {

	@Resource
    private IAdjustOrderPriceHistoryService adjustOrderPriceHistoryService;

	/**入参格式
	 * yyyy-mm-dd,yyyy-mm-dd
	 */
    @Override
    @NewSpan
    public ReturnT<String> execute(String startDateAndendDate) {
        log.info("AdjustPriceHistoryCollectJobHandler|XXL-JOB, start,startDateAndendDate:{}-----------------",startDateAndendDate);
        try {
        	String startTimeStr = null;
        	String endTimeStr = null;
        	if(StringUtils.isNotBlank(startDateAndendDate)) {
        		String[] split = startDateAndendDate.split(",");
        		startTimeStr = split[0];
        		endTimeStr = split[1];
        	}
        	adjustOrderPriceHistoryService.priceHistorySummaryData(startTimeStr,endTimeStr);
        	log.info("AdjustPriceHistoryCollectJobHandler|XXL-JOB, end,startDateAndendDate:{}-----------------",startDateAndendDate);
        } catch (Exception e) {
        	log.error("AdjustPriceHistoryCollectJobHandler|XXL-JOB价格调整记录汇总",e);
            return FAIL;
        }
        return SUCCESS;
    }
}
