package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.PriceManageControlStoreDetailService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 价格管控门店明细生效任务
 * <AUTHOR>
 */
@JobHandler(value = "priceControlStoreDetailEffectHandler")
@Component
public class PriceControlStoreDetailEffectHandler extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(PriceControlStoreDetailEffectHandler.class);

    @Autowired
    private PriceManageControlStoreDetailService priceManageControlStoreDetailService;

    @NewSpan("priceControlStoreDetailEffectHandler")
    @Override
    public ReturnT<String> execute(String s) {
        log.info("PriceControlStoreDetailEffectHandler|execute|开始执行......|s={}", s);
        priceManageControlStoreDetailService.executePriceControl();
        return SUCCESS;
    }
}
