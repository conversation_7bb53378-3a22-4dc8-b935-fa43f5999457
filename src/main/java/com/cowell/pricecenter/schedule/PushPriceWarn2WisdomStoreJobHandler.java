package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.PriceWarnRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * @Description: 推送价格预警到智店通
 * @Author: liubw
 * @Date: 2019/3/19 8:14 PM
 */
@Slf4j
@JobHandler(value = "pushPriceWarn2WisdomStoreJobHandler")
@Component
public class PushPriceWarn2WisdomStoreJobHandler extends IJobHandler {

    @Autowired
    private Tracer tracer;

    @Autowired
    private PriceWarnRecordService priceWarnRecordService;

    @NewSpan("pushPriceWarn2WisdomStoreJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Span span = tracer.createSpan("pushPriceWarn2WisdomStoreJobHandler");
        try {
            priceWarnRecordService.pushPriceWarn2WisdomStore(s);
        }catch (Exception e){
            log.error("<==执行xxl job||推送价格预警到智店通" ,e);
        }finally {
            tracer.close(span);
        }
        return ReturnT.SUCCESS;
    }
}
