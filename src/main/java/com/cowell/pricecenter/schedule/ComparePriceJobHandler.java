package com.cowell.pricecenter.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.entity.PricePushTask;
import com.cowell.pricecenter.entity.PricePushTaskExample;
import com.cowell.pricecenter.enums.PushResultEnum;
import com.cowell.pricecenter.enums.StatusEnum;
import com.cowell.pricecenter.mapper.PricePushTaskMapper;
import com.cowell.pricecenter.mq.producer.PricePushTaskExeProducer;
import com.cowell.pricecenter.service.PriceCenterComparePriceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 价格比对
 * @date 2024-07-26
 */
@JobHandler(value = "comparePriceJobHandler")
@Component
public class ComparePriceJobHandler extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(ComparePriceJobHandler.class);
    @Autowired
    private PriceCenterComparePriceService centerComparePriceService;

    @NewSpan("comparePriceJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始执行xxl job：定时比对价格param={}", s);
        JSONObject param = JSON.parseObject(s);
        Boolean exeFlag = param.getBoolean("exeFlag");
        if (!exeFlag) {
            return SUCCESS;
        }
        String comId = param.getString("comId");
        String storeNo = param.getString("storeNo");
        Long businessId = param.getLong("businessId");
        Long storeId = param.getLong("storeId");
        Long channelStoreId = param.getLong("channelStoreId");
        centerComparePriceService.comparePrice(comId, storeNo, storeId, channelStoreId);
        return SUCCESS;
    }
}
