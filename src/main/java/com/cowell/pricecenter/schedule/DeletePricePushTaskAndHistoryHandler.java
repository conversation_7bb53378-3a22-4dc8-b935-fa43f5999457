package com.cowell.pricecenter.schedule;

import cn.hutool.core.util.PageUtil;
import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.entity.PricePushHistoryExample;
import com.cowell.pricecenter.entity.PricePushTask;
import com.cowell.pricecenter.entity.PricePushTaskExample;
import com.cowell.pricecenter.enums.StatusEnum;
import com.cowell.pricecenter.mapper.PricePushTaskMapper;
import com.cowell.pricecenter.param.DeletePricePushTaskAndHistoryParam;
import com.cowell.pricecenter.service.PricePushHistoryService;
import com.github.pagehelper.PageHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 删除价格推送历史
 * <AUTHOR>
 * @date 2022/4/26 17:46
 */
@JobHandler(value = "deletePricePushTaskAndHistoryHandler")
@Component
public class DeletePricePushTaskAndHistoryHandler extends IJobHandler {

    private final Logger logger = LoggerFactory.getLogger(DeletePricePushTaskAndHistoryHandler.class);


    @Autowired
    private PricePushTaskMapper pricePushTaskMapper;

    @Autowired
    private PricePushHistoryService pricePushHistoryService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("DeletePricePushTaskAndHistoryHandler｜s={}",s);
        if (StringUtils.isBlank(s)) {
            return SUCCESS;
        }
        CompletableFuture.runAsync(()->{
            doDelete(s);
        });
        return SUCCESS;
    }

    @NewSpan("deletePricePushTaskAndHistoryHandler")
    public void doDelete(String s){
        logger.info("DeletePricePushTaskAndHistoryHandler｜doDelete={}",s);
        DeletePricePushTaskAndHistoryParam param = JSON.parseObject(s, DeletePricePushTaskAndHistoryParam.class);
        PricePushTaskExample taskExample = new PricePushTaskExample();
        PricePushTaskExample.Criteria criteria = taskExample.createCriteria();
        Integer beforeDays = param.getBeforeDays();
        LocalDateTime localDateTime = null;
        if (Objects.nonNull(beforeDays)) {
            localDateTime = LocalDateTime.now();
            localDateTime = localDateTime.minusDays(beforeDays.longValue());
            criteria.andGmtCreateLessThanOrEqualTo(localDateTime);
        }

        if (Objects.nonNull(param.getStatus())) {
            criteria.andStatusEqualTo(param.getStatus());
        }

        if (StringUtils.isNotBlank(param.getPushCode())) {
            criteria.andPushCodeEqualTo(param.getPushCode());
        }

        if (Objects.nonNull(param.getBatch())) {
            criteria.andBatchEqualTo(param.getBatch());
        }

        if (StringUtils.isNotBlank(param.getAdjustCode())) {
            criteria.andAdjustCodeEqualTo(param.getAdjustCode());
        }
        long count = pricePushTaskMapper.countByExample(taskExample);
        logger.info("DeletePricePushTaskAndHistoryHandler｜count={}",count);
        if (count == 0){
            logger.info("DeletePricePushTaskAndHistoryHandler｜没有要处理的数据");
            return;
        }
        int totalPage = PageUtil.totalPage(Integer.parseInt(count + ""), param.getPageSize());
        for (int i = 0; i < totalPage; i++) {
            PageHelper.startPage(1, param.getPageSize(),false);
            // 查询task
            List<PricePushTask> pricePushTasks = pricePushTaskMapper.selectByExample(taskExample);
            if (CollectionUtils.isNotEmpty(pricePushTasks)) {
                // 修改task状态为删除
                PricePushTask update = new PricePushTask();
                update.setStatus((byte) StatusEnum.DELETE.getCode());
                pricePushTaskMapper.updateByExampleSelective(update, taskExample);
                pricePushTasks.forEach(task -> {
                    // 删除history
                    PricePushHistoryExample historyExample = new PricePushHistoryExample();
                    historyExample.createCriteria()
                        .andPushCodeEqualTo(task.getPushCode())
                        .andBatchEqualTo(task.getBatch());
                    int delCount = pricePushHistoryService.deleteByExample(historyExample);
                    logger.info("DeletePricePushTaskAndHistoryHandler｜delete｜task={}|delCount={}",task.getPushCode(),delCount);
                });
            }

            // 删除task
            PricePushTaskExample deleteExample = new PricePushTaskExample();
            PricePushTaskExample.Criteria deleteExampleCriteria = deleteExample.createCriteria();
            deleteExampleCriteria.andStatusEqualTo(StatusEnum.DELETE.getCode());
            if (Objects.nonNull(localDateTime)) {
                deleteExampleCriteria.andGmtCreateLessThanOrEqualTo(localDateTime);
            }
            pricePushTaskMapper.deleteByExample(deleteExample);
        }
    }
}
