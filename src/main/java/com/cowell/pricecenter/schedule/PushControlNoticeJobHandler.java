package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.enums.PushTypeEnum;
import com.cowell.pricecenter.service.PriceManageControlOrderReadService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

@JobHandler(value = "controlNoticePushJobHandler")
@Slf4j
@Component
public class PushControlNoticeJobHandler extends IJobHandler {

    @Autowired
    private PriceManageControlOrderReadService priceManageControlOrderReadService;

    @Override
    @NewSpan
    public ReturnT<String> execute(String param) {
        try {
            log.info("开始执行xxl job：PushControlNoticeJobHandler|param:{}.", param);
            if(StringUtils.isBlank(param)){
                return SUCCESS;
            }
            if(PushTypeEnum.MESSAGE.getCode().equals(param)){
                Boolean messageResult = priceManageControlOrderReadService.pushCobtrolMessage();
                log.info("PushControlNoticeJobHandler|messageResult:{}.", messageResult);
            }else if(PushTypeEnum.BACKLOG.getCode().equals(param)){
                Boolean backLogResult = priceManageControlOrderReadService.pushBackLog();
                log.info("PushControlNoticeJobHandler|backLogResult:{}.", backLogResult);
            }
        }catch (Exception e){
            log.info("pushNoPriceJobHandler_Exception", e);
            return FAIL;
        }
        return SUCCESS;
    }
}
