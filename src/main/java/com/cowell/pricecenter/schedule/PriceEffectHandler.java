package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.enums.AdjustPriceVersionEnum;
import com.cowell.pricecenter.service.IAdjustPriceOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 价格生效（dsxPrice->price）
 * <p>价格实际生效</p>
 * <p>查询出预生效的调价单，进行价格修改</p>
 */
@JobHandler(value = "priceEffectHandler")
@Component
public class PriceEffectHandler extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(PriceEffectHandler.class);

    @Autowired
    private IAdjustPriceOrderService adjustPriceOrderService;

    @Value("${scheduleChangeDsxPriceToPrice:true}")
    private boolean scheduleChangeDsxPriceToPrice;

    @Autowired
    @Qualifier("effectXxlThreadExecutor")
    private AsyncTaskExecutor effectXxlThreadExecutor;

    @NewSpan("priceEffectHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("开始执行xxl job：【调价格生效价单】】");
        log.info("开始执行xxl job：【价格生效】");

        if (StringUtils.isBlank(s)) {
            s = AdjustPriceVersionEnum.VERSION_2_0.getVersion();
        }
        Date date = new Date();

//        try {
//            /*
//            在价格 待生效->生效 之前，推送审核时间是昨天，生效时间是今天、待生效的价格给海典；
//            防止出现跨天执行的调价单还未执行结束，部分门店价格已经执行了 待生效->生效 逻辑，导致此部分商品在中台生效却未推送海典。
//             */
//            adjustPriceOrderService.pushPriceToPOS(s);
//        } catch (Exception e) {
//            log.error("pushPriceToPOS", e);
//        }

        String finalS = s;
        if (scheduleChangeDsxPriceToPrice) {
            try {
                // 执行价格 待生效->生效 逻辑
                effectXxlThreadExecutor.execute(()->adjustPriceOrderService.distributeChangeDsxPriceToPrice(finalS));
            } catch (Exception e) {
                log.error("distributeChangeDsxPriceToPrice", e);
            }
        }

        try {
            // 查询出审核通过、已生效、且生效时间为今天的调价单，执行价格预生效
            // 目的是使价格按照生效时间顺序生效
            effectXxlThreadExecutor.execute(()->adjustPriceOrderService.effectAdjustPriceOrderAgain(finalS, date));
        } catch (Exception e) {
            log.error("effectAdjustPriceOrderAgain", e);
        }

        return SUCCESS;
    }
}
