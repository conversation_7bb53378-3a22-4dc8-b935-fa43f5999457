package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.IAdjustPriceOrderLogService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 删除调价单重试日志
 *
 * <AUTHOR>
 * @date 2023/4/23 10:04
 */
@JobHandler(value = "deleteAdjustPriceOrderLogJobHandler")
@Component
public class DeleteAdjustPriceOrderLogJobHandler extends IJobHandler {
    private final Logger log = LoggerFactory.getLogger(DeleteAdjustPriceOrderLogJobHandler.class);

    @Resource
    private IAdjustPriceOrderLogService adjustPriceOrderLogService;

    @NewSpan("deleteAdjustPriceOrderLogJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始执行xxl job：【删除调价单重试日志任务】");
        int beforeDays = 90;
        if (StringUtils.isNotBlank(s)) {
            beforeDays = Integer.parseInt(s);
        }
        adjustPriceOrderLogService.deleteHistoryLog(beforeDays);
        return SUCCESS;
    }
}
