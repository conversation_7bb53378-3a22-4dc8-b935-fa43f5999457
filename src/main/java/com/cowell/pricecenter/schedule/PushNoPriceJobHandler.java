package com.cowell.pricecenter.schedule;

import com.cowell.permission.dto.EmployeeDTO;
import com.cowell.permission.dto.QueryEmployeeDTO;
import com.cowell.pricecenter.entity.UnPriceStoreDetail;
import com.cowell.pricecenter.enums.RoleEnum;
import com.cowell.pricecenter.mapper.extension.UnPriceStoreDetailExMapper;
import com.cowell.pricecenter.mq.producer.PricePushDZTMsgProducer;
import com.cowell.pricecenter.service.dto.request.ZDTMsgPushDTO;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.net.URLEncoder;
import java.util.List;

/**
 * @Description: 没定价商品推送给连锁商采人员
 * @Author: liubw
 * @Date: 2019/3/19 8:14 PM
 */
@JobHandler(value = "pushNoPriceJobHandler")
@Component
public class PushNoPriceJobHandler extends IJobHandler{

    private final Logger log = LoggerFactory.getLogger(PushNoPriceJobHandler.class);
    @Autowired
    private UnPriceStoreDetailExMapper unPriceStoreDetailExMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PricePushDZTMsgProducer pricePushDZTMsgProducer;
    @Value("${business.showUnprice.url}")
    private String showUnPriceUrl;
    @NewSpan("pushNoPriceJobHandler")
    @Override
    public ReturnT<String> execute(String s) {
        try {
            List<UnPriceStoreDetail> unPriceStoreDetailList = unPriceStoreDetailExMapper.queryDistinctBusinessId(null);
            if (CollectionUtils.isNotEmpty(unPriceStoreDetailList)) {
                for (UnPriceStoreDetail unPriceStoreDetail : unPriceStoreDetailList) {
                    long businessId = unPriceStoreDetail.getBusinessId();
                    //调权限取出店长手机号
                    QueryEmployeeDTO queryEmployeeDTO = new QueryEmployeeDTO();
                    queryEmployeeDTO.setIdType(2);
                    queryEmployeeDTO.setOrgId(businessId);
                    queryEmployeeDTO.setRoleCodes(new String[]{RoleEnum.LSSCZY.getCode()});
                    ResponseEntity<List<EmployeeDTO>> ob = permissionService.listEmployeesByOrgIdAndRole(queryEmployeeDTO);
                    if (null != ob && null != ob.getBody()) {
                        List<EmployeeDTO> list = ob.getBody();
                        if (CollectionUtils.isNotEmpty(list)) {
                            EmployeeDTO employeeDetailDTO = list.get(0);
                            long userdId = employeeDetailDTO.getUserId();
                            ZDTMsgPushDTO zDTMsgPushDTO = new ZDTMsgPushDTO();
                            zDTMsgPushDTO.setTitle("您有商品需要设置价格");
                            zDTMsgPushDTO.setDescription("点击查看明细");
                            zDTMsgPushDTO.setReceiverIds(new Long[]{userdId});
                            zDTMsgPushDTO.setType(34);
                            zDTMsgPushDTO.setAppType(0);
                            zDTMsgPushDTO.setIdentification("pricecenter_" + System.currentTimeMillis());
                            zDTMsgPushDTO.setUrl("gjhealth://cstore/hybrid/index?url=" + URLEncoder.encode(showUnPriceUrl + "?businessId=" + businessId));
                            pricePushDZTMsgProducer.sendMq(zDTMsgPushDTO);
                        }
                    }
                }
            }
        }catch (Exception e){
            log.info("pushNoPriceJobHandler_Exception", e);
        }
        return SUCCESS;
    }
}
