package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.OmsService;
import com.cowell.pricecenter.service.PriceSync2PlatformRecordService;
import com.cowell.pricecenter.service.PriceWarnRecordService;
import com.cowell.pricecenter.service.dto.PriceSyncRecordDTO;
import com.cowell.pricecenter.service.dto.PriceWarningRecordDTO;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * @Description: 删除15天前的价格告警及价格同步日志
 * @Author: liubw
 * @Date: 2019/3/19 8:14 PM
 */
@Slf4j
@JobHandler(value = "deletePriceWarnAndSyncRecordJobHandler")
@Component
@Deprecated
public class DeletePriceWarnAndSyncRecordJobHandler extends IJobHandler {

    @Autowired
    private Tracer tracer;

    @Autowired
    private OmsService omsService;

    @Autowired
    private PriceWarnRecordService priceWarnRecordService;

    @Autowired
    private PriceSync2PlatformRecordService priceSync2PlatformRecordService;

    @NewSpan("deletePriceWarnAndSyncRecordJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Span span = tracer.createSpan("DeletePriceWarnAndSyncRecordJobHandler");
        try {
            List<Long> businessIds = omsService.getBusinessIds();
            if(CollectionUtils.isEmpty(businessIds)){
                log.warn("<==PriceWarnRecordService||deletePriceWarnAndSyncRecordJobHandler||连锁ids为空");
                return ReturnT.SUCCESS;
            }

            //当前日期-15
            Date beforeDate = DateUtils.getDateByFormat(-15);
            businessIds.forEach(businessId -> {
                try {
                    PriceWarningRecordDTO dto = new PriceWarningRecordDTO();
                    dto.setBusinessId(businessId);
                    dto.setGmtCreate(beforeDate);
                    priceWarnRecordService.deletePriceWarnARecord(dto);
                    PriceSyncRecordDTO syncDto = new PriceSyncRecordDTO();
                    syncDto.setBusinessId(businessId);
                    syncDto.setGmtCreate(beforeDate);
                    priceSync2PlatformRecordService.deleteSyncRecordRecord(syncDto);
                } catch (Exception e) {
                    log.error("<==定时任务删除告警及同步日志失败，businessId：{}",businessId);
                }
            });
        } finally {
            tracer.close(span);
        }
        return ReturnT.SUCCESS;
    }
}
