package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.entity.PricePushTask;
import com.cowell.pricecenter.entity.PricePushTaskExample;
import com.cowell.pricecenter.enums.PushResultEnum;
import com.cowell.pricecenter.enums.StatusEnum;
import com.cowell.pricecenter.mapper.PricePushTaskMapper;
import com.cowell.pricecenter.mq.producer.PricePushTaskExeProducer;
import com.cowell.pricecenter.service.IAdjustPriceOrderService;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @Description: 未收到POS处理成功标志的推送记录重新发送
 * @Author: liubw
 * @Date: 2019/3/19 8:14 PM
 */
@JobHandler(value = "pushPriceJobHandler")
@Component
public class PushPriceJobHandler extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(PushPriceJobHandler.class);

    @Autowired
    private PricePushTaskMapper pricePushTaskMapper;
    @Autowired
    private PricePushTaskExeProducer pricePushTaskExeProducer;

    @NewSpan("pushPriceJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始执行xxl job：【定时推送价格给POS】");
        PricePushTaskExample example = new PricePushTaskExample();
        example.createCriteria()
            .andResultEqualTo(PushResultEnum.ING.getCode())
            .andStatusEqualTo(StatusEnum.NORMAL.getCode());
        List<PricePushTask> taskList = pricePushTaskMapper.selectByExample(example);
        for (PricePushTask task : taskList) {
            pricePushTaskExeProducer.sendMq(task);
        }

        return SUCCESS;
    }
}
