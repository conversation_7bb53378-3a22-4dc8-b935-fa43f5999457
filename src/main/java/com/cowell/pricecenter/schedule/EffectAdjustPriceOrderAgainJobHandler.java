package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.enums.AdjustPriceVersionEnum;
import com.cowell.pricecenter.service.IAdjustPriceOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 查询出审核通过、预生效、且生效时间为今天或者生效时间为今天的调价单，执行价格生效
 * <p>处理同一商品有不同日期的价格的生效逻辑</p>
 * @since 调价单2.0
 */
@JobHandler(value = "effectAdjustPriceOrderAgainJobHandler")
@Component
@Deprecated
public class EffectAdjustPriceOrderAgainJobHandler extends IJobHandler {

    private final Logger log = LoggerFactory.getLogger(EffectAdjustPriceOrderAgainJobHandler.class);

    @Autowired
    private IAdjustPriceOrderService adjustPriceOrderService;

    @NewSpan("effectAdjustPriceOrderAgainJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        XxlJobLogger.log("开始执行xxl job：【effectAdjustPriceOrderAgainJobHandler】】");
        log.info("开始执行xxl job：【effectAdjustPriceOrderAgainJobHandler】");

        if (StringUtils.isBlank(s)) {
            s = AdjustPriceVersionEnum.VERSION_2_0.getVersion();
        }
        adjustPriceOrderService.effectAdjustPriceOrderAgain(s, new Date());

        return SUCCESS;
    }
}
