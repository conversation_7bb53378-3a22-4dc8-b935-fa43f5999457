package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.PushNoticeMsgService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

/**
 * 推送价格结果
 */
@JobHandler(value = "pushAdjustEffectResultNoticeJobHandler")
@Slf4j
@Component
public class PushAdjustEffectResultNoticeJobHandler extends IJobHandler {

    @Autowired
    private PushNoticeMsgService pushNoticeMsgService;

    @Override
    @NewSpan
    public ReturnT<String> execute(String s) {
        XxlJobLogger.log("PushAdjustEffectResultNoticeJobHandler|XXL-JOB, start-----------------");
        try {
            pushNoticeMsgService.pushAdjustEffectResultNoticeMsg();
        } catch (Exception e) {
            XxlJobLogger.log(e);
            return FAIL;
        }
        XxlJobLogger.log("PushAdjustEffectResultNoticeJobHandler|XXL-JOB, finish");
        return SUCCESS;
    }
}
