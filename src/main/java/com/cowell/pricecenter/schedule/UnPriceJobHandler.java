package com.cowell.pricecenter.schedule;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.pricecenter.entity.PriceStoreDetailExample;
import com.cowell.pricecenter.entity.UnPriceStoreDetail;
import com.cowell.pricecenter.entity.UnPriceStoreDetailExample;
import com.cowell.pricecenter.enums.OrgTypeEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.mapper.UnPriceStoreDetailMapper;
import com.cowell.pricecenter.mapper.extension.PriceStoreDetailExMapper;
import com.cowell.pricecenter.mapper.extension.UnPriceStoreDetailExMapper;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.dto.request.ItemSkuParamVo;
import com.cowell.pricecenter.service.dto.response.ImportPriceStoreDetailResponse;
import com.cowell.pricecenter.service.dto.response.NewSpuVo;
import com.cowell.pricecenter.service.dto.response.StoreDTO;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.web.rest.errors.BusinessException;
import com.cowell.pricecenter.web.rest.util.CommonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 定时将门店的价格进行缓存
 * @Author: liubw
 * @Date: 2019/3/19 8:14 PM
 */
@JobHandler(value = "unPriceJobHandler")
@Component
public class UnPriceJobHandler extends IJobHandler {

    private final Logger logger = LoggerFactory.getLogger(UnPriceJobHandler.class);

    @Autowired
    private PriceStoreDetailExMapper priceStoreDetailExMapper;
    @Autowired
    private UnPriceStoreDetailMapper unPriceStoreDetailMapper;
    @Autowired
    private UnPriceStoreDetailExMapper unPriceStoreDetailExMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private SearchService searchService;
    @Autowired
    private RedissonClient redissonClient;
    @Value("${price.refresh.business}")
    private String refreshBusiness;

    private static final String KEY = "ES-TASK-2-PRICE-SEARCH-";
    private static final String PRICED_STORE_KEY = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.PRICED_STORE_KEY;

    @NewSpan("cachePriceJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        logger.info("开始执行xxl job：【定时将门店的价格进行缓存】");
        List<String> buinessIdList = CommonUtils.split(refreshBusiness);
        logger.info("需要定时处理的连锁{}", buinessIdList);
        //每个连锁分别清空数据，然后重新计算
        for (String businessIdStr : buinessIdList) {
            Long businessId = Long.parseLong(businessIdStr.split("#")[0]);
            String businessName = businessIdStr.split("#")[1];

            UnPriceStoreDetailExample unExample = new UnPriceStoreDetailExample();
            unExample.createCriteria().andBusinessIdEqualTo(businessId);
            unPriceStoreDetailMapper.deleteByExample(unExample);

            Long orgId = getOrgList(Arrays.asList(businessId));
            if (orgId == 0) {
                continue;
            }
            Map<Long, List<OrgTreeDTO>> treeMap = listOrgTreesByRootOrgIdAndTypesBatch(Arrays.asList(orgId));
            //机构树处理，拼接成门店列表
            List<StoreDTO> storeDTOList = new ArrayList<>();
            Iterator<Map.Entry<Long, List<OrgTreeDTO>>> iterator = treeMap.entrySet().iterator();
            Map.Entry<Long, List<OrgTreeDTO>> entry;
            while (iterator.hasNext()) {
                entry = iterator.next();
                List<OrgTreeDTO> orgDTOList = entry.getValue();
                addStoreDTO(orgDTOList, storeDTOList);
            }
            int businessCount = 0;

            PriceStoreDetailExample example;
            for (StoreDTO storeDTO : storeDTOList) {
                Long storeId = storeDTO.getId();
                if (storeId == null) {
                    continue;
                }
                example = new PriceStoreDetailExample();
                example.createCriteria().andStoreIdEqualTo(storeId);
                List<String> goodsNoListOrigin = priceStoreDetailExMapper.queryPriceStoreDetailGoodsNo(example);
                Set<String> tempSet = new HashSet<>(goodsNoListOrigin);
                List<String> goodsNoList = new ArrayList<>(tempSet);
                if (CollectionUtils.isEmpty(goodsNoList)) {
                    continue;
                }
                //把已定价商品的数量，按门店的维度放到缓存中
                RMapCache<Long, Integer> priceRMapCache = redissonClient.getMapCache(PRICED_STORE_KEY + storeId);
                priceRMapCache.put(storeId, goodsNoList.size(), 2, TimeUnit.HOURS);
                businessCount += goodsNoList.size();

                //获取门店所有商品列表
                RBucket<String> bucket = redissonClient.getBucket(KEY + storeId);
                if(StringUtils.isEmpty(bucket.get())){
                    continue;
                }
                List<String> allList = transAllList(storeId, bucket.get());
                //取没有定价的商品列表
                allList.removeAll(goodsNoList);
                List<NewSpuVo> spuList = getNewSpuList(businessId, storeId, allList);
                if (CollectionUtils.isEmpty(spuList)) {
                    continue;
                }

                //插入未定价商品表
                String storeName = storeDTO.getShortName();
                insertUnPriceListBatch(businessId, businessName, storeId, storeName, spuList);
            }
            RMapCache<Long, Integer> priceRMapCache = redissonClient.getMapCache(PRICED_STORE_KEY + businessId);
            priceRMapCache.put(businessId, businessCount, 2, TimeUnit.HOURS);
        }

        return SUCCESS;
    }

    private void insertUnPriceListBatch(long businessId, String businessName, long storeId, String storeName, List<NewSpuVo> spuList) {
        List<UnPriceStoreDetail> record = new ArrayList<>();
        for (NewSpuVo vo : spuList) {
            UnPriceStoreDetail detail = new UnPriceStoreDetail();
            detail.setStoreId(storeId);
            detail.setStoreName(storeName);
            detail.setGoodsNo(vo.getGoodsNo());
            detail.setBusinessId(businessId);
            detail.setBusinessName(businessName);
            detail.setPlatformId(0L);
            detail.setPlatformName("");
            detail.setCurName(vo.getCurName());
            detail.setBarCode(vo.getBarCode());
            detail.setOpCode(vo.getOpCode());
            detail.setJhiSpecification(vo.getJhiSpecification());
            detail.setDosage(vo.getDosageformsid());
            detail.setManufacturer(vo.getFactoryid());
            detail.setExtend(vo.getGoodsNo() + "_" + vo.getCurName() + "_" + vo.getOpCode());
            record.add(detail);
        }
        int i = unPriceStoreDetailExMapper.batchInsert(record);
    }

    private List<NewSpuVo> getNewSpuList(Long businessId, Long storeId, List<String> goodsNoList) {
        ResponseEntity<List<NewSpuVo>> responseEntity;

        ItemSkuParamVo paramVo = new ItemSkuParamVo();
        paramVo.setBusinessIdList(Arrays.asList(businessId));
        paramVo.setStoreId(storeId);
        paramVo.setGoodsNoList(goodsNoList);
        logger.info("ES搜索门店所有在售商品，查询参数:{}", paramVo);
        try {
            responseEntity = searchService.getNewSpulistStore(paramVo);
        } catch (Exception e) {
            logger.warn("ES搜索门店所有在售商品异常,", e);
            throw new BusinessException(ReturnCodeEnum.GET_ES_RESULT_ERROR);
        }
        return responseEntity.getBody();
    }


    private Long getOrgList(List<Long> businessIdList) {
        logger.info("根据businessIdList查询机构list参数:{}", businessIdList);
        ResponseEntity<List<OrgDTO>> responseEntity = permissionService.listOrgByOutId(OrgTypeEnum.BUSINESS.getCode(), businessIdList);
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
            throw new BusinessException("调用权限系统失败");
        }
        List<OrgDTO> orgList = responseEntity.getBody();
        if (CollectionUtils.isEmpty(orgList)) {
            return 0L;
        }
        return orgList.get(0).getId();
    }

    public Map<Long, List<OrgTreeDTO>> listOrgTreesByRootOrgIdAndTypesBatch(List<Long> orgIds) {
        Integer[] orgTypes = new Integer[1];
        orgTypes[0] = OrgTypeEnum.STORE.getCode();

        logger.info("根据类型批量查询组织机构节点下指定类型组成的阉割版的树开始,参数:{}", orgIds);
        ResponseEntity<Map<Long, List<OrgTreeDTO>>> responseEntity;
        try {
            responseEntity = permissionService.listOrgTreesByRootOrgIdAndTypesBatch(orgIds, orgTypes);

        } catch (Exception e) {
            logger.error("根据类型批量查询组织机构节点下指定类型组成的阉割版的树异常:", e);
            throw new BusinessException(ReturnCodeEnum.GET_AUTH_ERROR);
        }
        return responseEntity.getBody();
    }

    /**
     * 根据机构ID查询出所有的门店，但是会有片区等存在，需要递归只把门店取出
     *
     * @param orgTreeDTOS  机构ID查询的原始数据
     * @param storeDTOList 过滤后只有门店的集合
     */
    public void addStoreDTO(List<OrgTreeDTO> orgTreeDTOS, List<StoreDTO> storeDTOList) {
        if (CollectionUtils.isEmpty(orgTreeDTOS)) {
            return;
        }
        for (OrgTreeDTO childrenDTO : orgTreeDTOS) {
            if (childrenDTO.getType() == OrgTypeEnum.STORE.getCode()) {
                StoreDTO storeDTO = new StoreDTO();
                storeDTO.setOrgId(childrenDTO.getId());
                storeDTO.setId(childrenDTO.getOutId());
                storeDTO.setName(childrenDTO.getName());
                storeDTO.setShortName(childrenDTO.getShortName());
                storeDTOList.add(storeDTO);
            }
            List<OrgTreeDTO> grandChildren = childrenDTO.getChildren();
            addStoreDTO(grandChildren, storeDTOList);
        }
    }

    private List<String> transAllList(Long storeId, String allStr) {
        allStr = allStr.replace("[", "").replace("]", "").replace("\"", "")
            .replaceAll("\"null\",", "").replaceAll("null,", "");
        return CommonUtils.split(allStr);
    }
//
}
