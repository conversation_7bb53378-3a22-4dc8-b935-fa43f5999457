package com.cowell.pricecenter.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.service.tag.StoreAttrTagService;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

@Slf4j
@Component
public class SyncStoreAttrTask {

    @Autowired
    private StoreAttrTagService storeAttrTagService;

    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor taskExecutor;

    @XxlJob("SyncPriceStoreAttrJob")
    @NewSpan("SyncPriceStoreAttrJobNewSpan")
    public ReturnT<String> execute(String param) {
        log.info("SyncPriceStoreAttr-start:{}",param);
        JSONObject jsonObject = JSON.parseObject(param);
        Long time = System.currentTimeMillis();

        try {
            // 异步执行，但不等待结果
            CompletableFuture.supplyAsync(() -> {
                try {
                    storeAttrTagService.syncPriceStoreCommonData(jsonObject);
                    log.info("Async sync completed successfully");
                    return "success";
                } catch (Exception e) {
                    log.error("Async sync failed", e);
                    return "failed";
                }
            }, taskExecutor);
            log.info("SyncPriceStoreAttr-submitted async task");
            return ReturnT.SUCCESS;
        } catch (BusinessErrorException e) {
            log.error("SyncPriceStoreAttr-err",e);
            return ReturnT.FAIL;
        } catch (Exception e) {
            log.error("SyncPriceStoreAttr-err",e);
            return ReturnT.FAIL;
        }
    }
}

