package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.IAdjustPriceOrderLogService;
import com.cowell.pricecenter.service.IAdjustPriceOrderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 执价失败发送消息
 *
 * <AUTHOR>
 * @date 2025/5/19 16:50
 */
@JobHandler(value = "priceExeFailureMessageJobHandler")
@Component
public class PriceExeFailureMessageJobHandler extends IJobHandler {
    private final Logger log = LoggerFactory.getLogger(PriceExeFailureMessageJobHandler.class);

    @Resource
    private IAdjustPriceOrderService adjustPriceOrderService;

    @NewSpan("priceExeFailureMessageJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始执行xxl job：【执价失败发送消息任务】");
        adjustPriceOrderService.priceExeFailureMessage();
        return SUCCESS;
    }
}
