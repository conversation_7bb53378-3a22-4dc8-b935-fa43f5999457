package com.cowell.pricecenter.schedule;

import com.cowell.pricecenter.service.IAdjustPriceOrderDetailService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Component;

import java.util.Calendar;

/**
 * 调价单重试
 *
 * 重试条件：
 * 1、查询MQResult表，查询出创建时间是N小时前的调价单，重新执行。
 * 2、查询出调价单表中执行状态为执行中的、生效时间是1天前的数据
 *
 * <AUTHOR>
 * @date 2023/4/23 10:04
 */
@JobHandler(value = "adjustPriceOrderRetryJobHandler")
@Component
public class AdjustPriceOrderRetryJobHandler extends IJobHandler {
    private final Logger log = LoggerFactory.getLogger(AdjustPriceOrderRetryJobHandler.class);

    @Autowired
    private IAdjustPriceOrderDetailService adjustPriceOrderDetailService;

    @NewSpan("adjustPriceOrderRetryJobHandler")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("开始执行xxl job：【调价单重试任务】");


        int beforeHours = 24;
        if (StringUtils.isNotBlank(s)) {
            beforeHours = Integer.parseInt(s);
        }

        adjustPriceOrderDetailService.retryExecuteAdjustPriceOrderByMqResult(beforeHours);
        return SUCCESS;
    }
}
