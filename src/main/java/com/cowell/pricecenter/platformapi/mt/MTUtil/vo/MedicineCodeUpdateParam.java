package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

public class MedicineCodeUpdateParam {
    //原始的app_medicine_code
    private String app_medicine_code_origin;
    //药品的UPC码
    private String upc;
    //新的app_medicine_code
    private String app_medicine_code_new;

    public MedicineCodeUpdateParam() {
    }

    public String getApp_medicine_code_origin() {
        return app_medicine_code_origin;
    }

    public void setApp_medicine_code_origin(String app_medicine_code_origin) {
        this.app_medicine_code_origin = app_medicine_code_origin;
    }

    public String getUpc() {
        return upc;
    }

    public void setUpc(String upc) {
        this.upc = upc;
    }

    public String getApp_medicine_code_new() {
        return app_medicine_code_new;
    }

    public void setApp_medicine_code_new(String app_medicine_code_new) {
        this.app_medicine_code_new = app_medicine_code_new;
    }

    @Override
    public String toString() {
        return "AppMedicineCodeUpdateVo{" +
                "app_medicine_code_origin='" + app_medicine_code_origin + '\'' +
                ", upc='" + upc + '\'' +
                ", app_medicine_code_new='" + app_medicine_code_new + '\'' +
                '}';
    }
}
