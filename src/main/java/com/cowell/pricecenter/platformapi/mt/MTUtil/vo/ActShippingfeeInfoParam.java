package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

import java.util.List;

public class ActShippingfeeInfoParam {
    private String app_poi_code;
    private Long act_id;
    private Integer start_time;
    private Integer end_time;
    private String weeks_time;
    private String period;
    private List<ActShippingItemParam> act_detail;
    private String max_price;
    private String actStatus;


    public ActShippingfeeInfoParam() {
    }

    public String getApp_poi_code() {
        return app_poi_code;
    }

    public void setApp_poi_code(String app_poi_code) {
        this.app_poi_code = app_poi_code;
    }

    public Long getAct_id() {
        return act_id;
    }

    public void setAct_id(Long act_id) {
        this.act_id = act_id;
    }

    public Integer getStart_time() {
        return start_time;
    }

    public void setStart_time(Integer start_time) {
        this.start_time = start_time;
    }

    public Integer getEnd_time() {
        return end_time;
    }

    public void setEnd_time(Integer end_time) {
        this.end_time = end_time;
    }

    public String getWeeks_time() {
        return weeks_time;
    }

    public void setWeeks_time(String weeks_time) {
        this.weeks_time = weeks_time;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }


    public List<ActShippingItemParam> getAct_detail() {
        return act_detail;
    }

    public void setAct_detail(List<ActShippingItemParam> act_detail) {
        this.act_detail = act_detail;
    }

    public String getMax_price() {
        return max_price;
    }

    public void setMax_price(String max_price) {
        this.max_price = max_price;
    }

    public String getActStatus() {
        return actStatus;
    }

    public void setActStatus(String actStatus) {
        this.actStatus = actStatus;
    }

    @Override
    public String toString() {
        return "ActShippingfeeInfoParam{" +
                "app_poi_code='" + app_poi_code + '\'' +
                ", act_id=" + act_id +
                ", start_time=" + start_time +
                ", end_time=" + end_time +
                ", weeks_time='" + weeks_time + '\'' +
                ", period='" + period + '\'' +
                ", act_detail=" + act_detail +
                ", max_price='" + max_price + '\'' +
                ", actStatus='" + actStatus + '\'' +
                '}';
    }
}
