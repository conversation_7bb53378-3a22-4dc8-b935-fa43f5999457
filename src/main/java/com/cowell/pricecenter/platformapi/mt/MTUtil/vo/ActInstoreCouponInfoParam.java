package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

import java.util.List;

public class ActInstoreCouponInfoParam {

    private Integer start_time;
    private Integer end_time;

    //查询参数
    private String app_poi_code;
    private Long actId;
    private Integer status;
    private Integer remain_days;
    private List<ActInstoreCouponItemParam> act_data;

    public Integer getStart_time() {
        return start_time;
    }

    public void setStart_time(Integer start_time) {
        this.start_time = start_time;
    }

    public Integer getEnd_time() {
        return end_time;
    }

    public void setEnd_time(Integer end_time) {
        this.end_time = end_time;
    }

    public String getApp_poi_code() {
        return app_poi_code;
    }

    public void setApp_poi_code(String app_poi_code) {
        this.app_poi_code = app_poi_code;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRemain_days() {
        return remain_days;
    }

    public void setRemain_days(Integer remain_days) {
        this.remain_days = remain_days;
    }

    public List<ActInstoreCouponItemParam> getAct_data() {
        return act_data;
    }

    public void setAct_data(List<ActInstoreCouponItemParam> act_data) {
        this.act_data = act_data;
    }

    @Override
    public String toString() {
        return "WmAppInstoreCouponInfo{" +
                "start_time=" + start_time +
                ", end_time=" + end_time +
                ", app_poi_code='" + app_poi_code + '\'' +
                ", actId=" + actId +
                ", status=" + status +
                ", remain_days=" + remain_days +
                ", act_data=" + act_data +
                '}';
    }
}
