package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

public class ActInstoreCouponItemParam {
    private Double limit_price;
    private Double coupon_price;
    private Integer user_type;
    private Integer validity_days;
    private Integer stock;

    //查询参数
    private Integer sent_count;
    private Integer use_count;

    public Double getLimit_price() {
        return limit_price;
    }

    public void setLimit_price(Double limit_price) {
        this.limit_price = limit_price;
    }

    public Double getCoupon_price() {
        return coupon_price;
    }

    public void setCoupon_price(Double coupon_price) {
        this.coupon_price = coupon_price;
    }

    public Integer getUser_type() {
        return user_type;
    }

    public void setUser_type(Integer user_type) {
        this.user_type = user_type;
    }

    public Integer getValidity_days() {
        return validity_days;
    }

    public void setValidity_days(Integer validity_days) {
        this.validity_days = validity_days;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getSent_count() {
        return sent_count;
    }

    public void setSent_count(Integer sent_count) {
        this.sent_count = sent_count;
    }

    public Integer getUse_count() {
        return use_count;
    }

    public void setUse_count(Integer use_count) {
        this.use_count = use_count;
    }

    @Override
    public String toString() {
        return "ActInstoreCouponItemParam{" +
                "limit_price=" + limit_price +
                ", coupon_price=" + coupon_price +
                ", user_type=" + user_type +
                ", validity_days=" + validity_days +
                ", stock=" + stock +
                ", sent_count=" + sent_count +
                ", use_count=" + use_count +
                '}';
    }
}
