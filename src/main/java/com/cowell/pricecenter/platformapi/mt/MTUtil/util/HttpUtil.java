package com.cowell.pricecenter.platformapi.mt.MTUtil.util;

import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.platformapi.mt.MTUtil.constants.ErrorEnum;
import com.cowell.pricecenter.platformapi.mt.MTUtil.constants.RequestMethodTypeEnum;
import com.cowell.pricecenter.platformapi.mt.MTUtil.exception.ApiOpException;
import com.cowell.pricecenter.platformapi.mt.MTUtil.exception.ApiSysException;
import com.cowell.pricecenter.platformapi.mt.MTUtil.factory.URLFactory;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.ByteArrayBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by yangzhiqi on 15/10/15.
 */
public class HttpUtil {

    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    private static CloseableHttpClient httpClient = HttpClients.createDefault();

    static {
        PoolingHttpClientConnectionManager poolingHttpClientConnectionManager = new PoolingHttpClientConnectionManager();
        poolingHttpClientConnectionManager.setMaxTotal(PropertiesUtil.getHttpClientPoolMaxTotal());
        poolingHttpClientConnectionManager.setDefaultMaxPerRoute(PropertiesUtil.getHttpClientPoolDefaultMaxPerRoute());
        poolingHttpClientConnectionManager.setMaxPerRoute(URLFactory.getRequestUrlRoute(), PropertiesUtil.getHttpClientPoolMaxPerRoute());
        httpClient = HttpClients.custom()
                .setConnectionManager(poolingHttpClientConnectionManager)
                .build();
    }

    public static String request(String urlPrefix,String urlHasParamsNoSig,String sig,Map<String, String> systemParamsMap,
                                 Map<String,String> applicationParamsMap,String requestMethodType,RequestConfig.Builder requestConfigBuilder )
    throws ApiSysException {
        if(RequestMethodTypeEnum.POST.getCode().equals(requestMethodType)){
            String url = URLFactory.genOnlyHasSysParamsAndSigUrl(urlPrefix, systemParamsMap, sig);
            return requestOfPost(url,applicationParamsMap,requestConfigBuilder);
        }else {
            String url = URLFactory.genUrlForGetRequest(urlHasParamsNoSig, sig);
            return requestOfGet(url,requestConfigBuilder);
        }
    }

    public static String request(String urlPrefix,String urlHasParamsNoSig,String sig,Map<String, String> systemParamsMap,
                                 Map<String,String> applicationParamsMap, byte[] fileData, String imgName, String requestMethodType,
                                 RequestConfig.Builder requestConfigBuilder ) throws ApiSysException{
        if(RequestMethodTypeEnum.POST.getCode().equals(requestMethodType)){
            String url = URLFactory.genOnlyHasSysParamsAndSigUrl(urlPrefix, systemParamsMap, sig);
            return requestOfPost(url,applicationParamsMap, fileData, imgName, requestConfigBuilder);
        }else {
            String url = URLFactory.genUrlForGetRequest(urlHasParamsNoSig, sig);
            return requestOfGet(url,requestConfigBuilder);
        }
    }

    /**
     * 请求以POST方式
     * @param url 美团的接口url
     * @param applicationParamsMap 参数列表
     * @return
     */
    private static String requestOfPost(String url,Map<String,String> applicationParamsMap,RequestConfig.Builder requestConfigBuilder)
        throws ApiSysException{
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfigBuilder.build());
        CloseableHttpResponse response = null;
        try {
            //设置参数
            List<BasicNameValuePair> nameValuePairList = ConvertUtil.convertToEntity(applicationParamsMap);
            UrlEncodedFormEntity uefEntity = new UrlEncodedFormEntity(nameValuePairList,"UTF-8");
            httpPost.setEntity(uefEntity);
            long start = System.currentTimeMillis() ;
            response = httpClient.execute(httpPost);
            log.info("requestOfPost耗时：{}", System.currentTimeMillis() - start);
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity, "UTF-8");
        } catch (Exception e) {
            throw new ApiSysException(e);
        }finally {
            httpPost.releaseConnection();
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                throw new ApiSysException(e);
            }
        }

    }

    /**
     * 请求以POST方式
     * @param url 美团的接口url
     * @param applicationParamsMap 参数列表
     * @return
     */
    private static String requestOfPost(String url,Map<String,String> applicationParamsMap, byte[] fileData,
                                        String imgName, RequestConfig.Builder requestConfigBuilder) throws ApiSysException{
        HttpPost httpPost = new HttpPost(url);
        httpPost.setConfig(requestConfigBuilder.build());
        CloseableHttpResponse response = null;
        try {
            //设置参数
            List<NameValuePair> nameValuePairs = new ArrayList<>();
            List<BasicNameValuePair> nameValuePairList = ConvertUtil.convertToEntity(applicationParamsMap);
            nameValuePairs.addAll(nameValuePairList);

            MultipartEntity entity = new MultipartEntity(HttpMultipartMode.BROWSER_COMPATIBLE, null, Charset.forName("utf-8"));
            ByteArrayBody byteArray = new ByteArrayBody(fileData, imgName);
            entity.addPart("file", byteArray);

            URLEncodedUtils.format(nameValuePairs, "UTF-8");
            Iterator<NameValuePair> it = nameValuePairs.iterator();
            while(it.hasNext()){
                NameValuePair param = it.next();
                entity.addPart(param.getName(), new StringBody(param.getValue(), Charset.forName("utf8")));
            }

            httpPost.setEntity(entity);
            long start = System.currentTimeMillis() ;
            response = httpClient.execute(httpPost);
            log.info("requestOfPost2耗时：{}", System.currentTimeMillis() - start);
            HttpEntity responseEntity = response.getEntity();
            return EntityUtils.toString(responseEntity, "UTF-8");
        } catch (Exception e) {
            throw new ApiSysException(e);
        }finally {
            httpPost.releaseConnection();
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                throw new ApiSysException(e);
            }
        }

    }

    /**
     * 请求以GET方式
     * @param url
     * @return
     */
    private static String requestOfGet(String url,RequestConfig.Builder requestConfigBuilder) throws ApiSysException{
        HttpGet httpGet = new HttpGet(url);
        httpGet.setConfig(requestConfigBuilder.build());
        CloseableHttpResponse response = null;
        try {
            long start = System.currentTimeMillis() ;
            response = httpClient.execute(httpGet);
            log.info("requestOfGet耗时：{}", System.currentTimeMillis() - start);
            HttpEntity entity = response.getEntity();
            return EntityUtils.toString(entity, "UTF-8");
        } catch (Exception e) {
            throw new ApiSysException(e);
        }finally {
            try {
                httpGet.releaseConnection();
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                throw new ApiSysException(e);
            }
        }

    }

    public static String httpResultHandler(String httpResult) throws ApiOpException, ApiSysException{
        return httpResultHandler(httpResult, false);
    }

    public static String httpResultHandler(String httpResult, boolean partSuccess) throws ApiOpException, ApiSysException{
        log.info("解析美团返回结果:{}",httpResult);
        if (httpResult == null || httpResult.equals("")) {
            throw new ApiSysException(ErrorEnum.SYS_ERR);
        }
        JSONObject resultObj = null;
        try {
            resultObj = JSONObject.parseObject(httpResult);
        } catch (Exception e) {
            throw new ApiSysException(ErrorEnum.SYS_ERR);
        }
        if (resultObj == null || resultObj.get("data") == null) {
            throw new ApiSysException(ErrorEnum.SYS_ERR);
        }

        String dataStr = String.valueOf(resultObj.get("data"));
        if (dataStr.equals("ng") || dataStr.equalsIgnoreCase("null")) {
            Object errObject = resultObj.get("error");
            if (errObject == null || String.valueOf(errObject).equals("") || String.valueOf(errObject).equalsIgnoreCase("null")) {
                throw new ApiSysException(ErrorEnum.SYS_ERR);
            }

            JSONObject errJsonObject = null;
            try {
                errJsonObject = JSONObject.parseObject(String.valueOf(errObject));
            } catch (Exception e) {
                throw new ApiSysException(ErrorEnum.SYS_ERR);
            }
            if (errJsonObject == null || errJsonObject.get("code") == null || errJsonObject.get("code").equals("")
                    || String.valueOf(errJsonObject.get("code")).equalsIgnoreCase("null")) {
                throw new ApiSysException(ErrorEnum.SYS_ERR);
            }

            Integer errorCode = null;
            try {
                errorCode = Integer.parseInt(String.valueOf(errJsonObject.get("code")));
            } catch (Exception e) {
                throw new ApiSysException(ErrorEnum.SYS_ERR);
            }
            if (errorCode == null) {
                throw new ApiSysException(ErrorEnum.SYS_ERR);
            } else {
                if (errJsonObject.get("msg") == null || errJsonObject.get("msg").equals("")
                        || String.valueOf(errJsonObject.get("msg")).equalsIgnoreCase("null")) {
                    throw new ApiSysException(ErrorEnum.SYS_ERR);
                } else {
                    String errorMsg = String.valueOf(errJsonObject.get("msg"));
                    throw new ApiOpException(errorCode, errorMsg);
                }

            }
        } else if (StringUtil.isNotBlank(resultObj.getString("msg"))){
            // data=ok && msg not empty, is partSuccess, return msg; else return data.
            dataStr = resultObj.getString("msg");
        }
        return dataStr;
    }
}
