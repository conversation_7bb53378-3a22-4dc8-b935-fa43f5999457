package com.cowell.pricecenter.platformapi.mt.MTUtil;

import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.builder.dto.MTQueryPriceFailBaseDTO;
import com.cowell.pricecenter.builder.dto.MTQueryPriceFailDTO;
import com.cowell.pricecenter.platformapi.mt.MTUtil.constants.PoiQualificationEnum;
import com.cowell.pricecenter.platformapi.mt.MTUtil.exception.ApiOpException;
import com.cowell.pricecenter.platformapi.mt.MTUtil.exception.ApiSysException;
import com.cowell.pricecenter.platformapi.mt.MTUtil.factory.APIFactory;
import com.cowell.pricecenter.platformapi.mt.MTUtil.vo.MedicineParam;
import com.cowell.pricecenter.platformapi.mt.MTUtil.vo.PoiParam;
import com.cowell.pricecenter.platformapi.mt.MTUtil.vo.SystemParam;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.cowell.pricecenter.constant.PriceSyncConstant.*;

/**
 * Created by zhangyuanbo02 on 15/12/9.
 */
public class Test {

    private final static SystemParam sysPram = new SystemParam("2661", "cfae0691de992d0c745bf7e1016425b4");
    //    private final static String appPoiCode = "ceshi_POI_II";
    private final static String appPoiCode = "t_dJ97XE4Y9G";

//    public static void main(String[] args) {
////        poiGetIds();
////        poiMget();
//
//
//
//
//
//        //syncRecord(resultMap,goodsList);
//
//    }

    private void testSyncPrice(){




        List<String> goodsList = Arrays.asList("102945","1013833");

        List<MedicineParam> medicine_data = Lists.newArrayList();

        MedicineParam m1 = new MedicineParam();

        m1.setApp_medicine_code("102945");
        m1.setPrice("102");
        MedicineParam m2 = new MedicineParam();

        m2.setApp_medicine_code("1013833");
        m2.setPrice("102");


        medicine_data.add(m1);
        medicine_data.add(m2);


        Map<String,Object> resultMap = Maps.newHashMap();
        try {
            String data = APIFactory.getMedicineAPI().medicinePrice(sysPram,
                appPoiCode,medicine_data);

            System.out.println("data==="+data);
            resultMap.put(MT_RESULT_MAP_DATA,MT_RESULT_DATA_OK);

            if (!"ok".equals(data)){
                resultMap.put(MT_RESULT_MAP_DATA,MT_RESULT_DATA_PART_SUCCESS);
                resultMap.put(MT_RESULT_MAP_MSG,data);
                System.out.println("---"+resultMap.toString());

            }


        } catch (ApiOpException e) {

            ApiOpException apiOpException = null;
            /**
             * ApiOpException
             * 第一种情况 "code": 803,"msg": "不存在此门店"
             * 第二种情况 "code": 1,"msg": "[{\"app_medicine_code\":\"1011387\",\"error_msg\":\"不存在此药品\"}]"
             */
            apiOpException = (ApiOpException) e;

            resultMap.put(MT_RESULT_MAP_DATA,"fail");
            resultMap.put(MT_RESULT_MAP_CODE,apiOpException.getCode());
            resultMap.put(MT_RESULT_MAP_MSG,apiOpException.getMsg());

            System.out.println("ApiOpException");

        } catch (ApiSysException e) {
            e.printStackTrace();

            System.out.println("ApiSysException");
        }



        System.out.println("-finlay-"+resultMap.toString());



    }







//
//    private static void syncRecord(Map<String,Object> resultMap,List<String> goodsNoList){
//
//        List<String> successGoodsList = Lists.newArrayList();
//        List<String> failGoodsList = Lists.newArrayList();
//        List<String> partFailGoodsList = Lists.newArrayList();
//        //同步商品失败的原因
//        Map<String,String> failGoodsReasonMap = Maps.newHashMap();
//
//        switch (resultMap.get(MT_RESULT_MAP_DATA)+""){
//
//            case MT_RESULT_DATA_OK:
//                //全部成功
//                successGoodsList = goodsNoList;
//                break;
//            case MT_RESULT_DATA_PART_SUCCESS:
//                //部分成功 取出失败的商品编码
//                List<MTQueryPriceFailBaseDTO> failBaseDTOList = JSONObject.parseArray(resultMap.get(MT_RESULT_MAP_MSG).toString(),MTQueryPriceFailBaseDTO.class);
//                failBaseDTOList.forEach(item->{
//                    partFailGoodsList.add(item.getApp_medicine_code());
//                    failGoodsReasonMap.put(item.getApp_medicine_code(),item.getError_msg());
//                });
//
//                //把剩下的给 成功的
//                successGoodsList = receiveDefectList(goodsNoList, partFailGoodsList);
//
//                break;
//            case MT_RESULT_DATA_FAIL:
//                //全部失败
//                failGoodsList = goodsNoList;
//
//                //获取失败的code
//                Integer code = (Integer) resultMap.get(MT_RESULT_MAP_CODE);
//
//                //第一种情况   "code": 803,"msg": "不存在此门店" 见所有商品同步失败的原因 为 不存在此门店
//                if (code == 803){
//                    failGoodsList.forEach(item-> failGoodsReasonMap.put(item,"不存在此门店"));
//                }
//
//                //第二种情况   "code": 1,"msg": "[{\"app_medicine_code\":\"1011387\",\"error_msg\":\"不存在此药品\"}]"
//                if (code == 1){
//                    // 取出失败的商品编码 对应的原因
//                    List<MTQueryPriceFailBaseDTO> allFailBaseDTOList = JSONObject.parseArray(resultMap.get(MT_RESULT_MAP_MSG).toString(),MTQueryPriceFailBaseDTO.class);
//
//                    Map<String,String> goodsAllFailReason = Maps.newHashMap();
//
//                    if (CollectionUtils.isNotEmpty(allFailBaseDTOList)){
//                        goodsAllFailReason = allFailBaseDTOList.stream().collect(Collectors.toMap(MTQueryPriceFailBaseDTO::getApp_medicine_code, MTQueryPriceFailBaseDTO::getError_msg,(k1, k2)->k1));
//                    }
//
//                    //循环全部商品编码 设置失败原因
//                    Map<String, String> finalGoodsAllFailReason = goodsAllFailReason;
//                    failGoodsList.forEach(item->{
//                        failGoodsReasonMap.put(item, finalGoodsAllFailReason.get(item));
//                    });
//                }
//
//                break;
//
//            default:break;
//        }
//
//
//        System.out.println("successGoodsList=="+JSONObject.toJSONString(successGoodsList));
//        System.out.println("failGoodsList=="+JSONObject.toJSONString(failGoodsList));
//        System.out.println("partFailGoodsList=="+JSONObject.toJSONString(partFailGoodsList));
//        System.out.println("failGoodsReasonMap=="+JSONObject.toJSONString(failGoodsReasonMap));
//
//
//
//
//    }





    public static void poiSave() {
        PoiParam PoiPram = new PoiParam();
        PoiPram.setApp_poi_code("ceshi_poi1");
        PoiPram.setName("我的门店");
        PoiPram.setAddress("我的门店的地址");
        PoiPram.setLatitude(40.810249);
        PoiPram.setLongitude(117.502289);
        PoiPram.setPhone("13425355733");
        PoiPram.setShipping_fee(2f);
        PoiPram.setShipping_time("09:00-13:30,19:00-21:40");
        PoiPram.setPic_url("http://cdwuf.img46.wal8.com/img46/525101_20150811114016/144299728957.jpg");
        PoiPram.setOpen_level(1);
        PoiPram.setIs_online(1);
        PoiPram.setPre_book_min_days(1);
        PoiPram.setPre_book_max_days(2);
        PoiPram.setApp_brand_code("zhajisong");
        PoiPram.setThird_tag_name("麻辣烫");

        try {
            String result = APIFactory.getPoiAPI().poiSave(sysPram, PoiPram);
            System.out.println(result);
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiGetIds() {
        try {
            String result = APIFactory.getPoiAPI().poiGetIds(sysPram);
            System.out.println(result);
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiMget() {
        try {
            List<PoiParam> Poslist = APIFactory.getPoiAPI().poiMget(sysPram, "B0301020002,B0301050003,B0301070005,B0301090006");
            System.out.println(Poslist);
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiTagList() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiTagList(sysPram));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiOpen() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiOpen(sysPram, "ceshi_poi1"));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiClose() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiClose(sysPram, "ceshi_poi1"));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiOffline() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiOffline(sysPram, "ceshi_poi1", "缺货"));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiOnline() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiOnline(sysPram, "ceshi_poi1"));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    @Deprecated
    public static void poiQualifySave() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiQualifySave(sysPram, "ceshi_poi1",
                PoiQualificationEnum.BUSINESS_LICENSE,
                "http://cdwuf.img46.wal8.com/img46/525101_20150811114016/***********.jpg",
                "2016-01-01", "天安门", "11019123"));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiSendTimeSave() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiSendTimeSave(sysPram, "ceshi_poi1,ceshi_100", 50));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiAdditionalSave() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiAdditionalSave(sysPram, "ceshi_poi1","<EMAIL>", "zhajisong", "8888"));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }

    public static void poiUpdatePromotionInfo() {
        try {
            System.out.println(APIFactory.getPoiAPI().poiUpdatePromotionInfo(sysPram, "ceshi_poi1", "这是一条用于测试的门店公告信息"));
        } catch (ApiOpException e) {
            e.printStackTrace();
        } catch (ApiSysException e) {
            e.printStackTrace();
        }
    }



//    public static void main(String[] args) {
//        byte[] imgData = {85,112,108,111,97,100,115,47,80,114,111,100,117,99,116,115,47,49,53,48,55,47,48,49,47,105,109,103,46,106,112,103};
//        try {
//            System.out.println(
//                APIFactory.getImageApi().imageUpload(systemParam, appPoiCode, imgData, "ceshi.jpg"));
//        } catch (ApiOpException e) {
//            e.printStackTrace();
//        } catch (ApiSysException e) {
//            e.printStackTrace();
//        }
//    }


}
