package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

/**
 * Created by ya<PERSON><PERSON><PERSON> on 15/10/15.
 */
public class PoiParam {
    private Integer app_id;
    //APP方门店ID
    private String app_poi_code;
    private String name;
    private String address;
    private Double latitude;
    private Double longitude;
    private String pic_url;
    private String phone;
    private String standby_tel;
    private Float shipping_fee;
    private String shipping_time;
    private String promotion_info;
    private String remark;
    private Integer open_level;
    private Integer is_online;
    private Integer invoice_support;
    private Integer invoice_min_price;
    private String invoice_description;
    private Integer city_id;
    private Integer location_id;
    private Long ctime;
    private Long utime;
    private String third_tag_name;
    private String tag_name;
    private String settlement_poi_id;
    private String app_brand_code;
    private Integer pre_book;
    private Integer time_select;
    private Integer pre_book_min_days;
    private Integer pre_book_max_days;
    private String pic_url_large;
    private Integer mt_type_id;

    public Integer getApp_id() {
        return app_id;
    }

    public PoiParam setApp_id(Integer app_id) {
        this.app_id = app_id;
        return this;
    }

    public String getApp_poi_code() {
        return app_poi_code;
    }

    public PoiParam setApp_poi_code(String app_poi_code) {
        this.app_poi_code = app_poi_code;
        return this;
    }

    public String getName() {
        return name;
    }

    public PoiParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getAddress() {
        return address;
    }

    public PoiParam setAddress(String address) {
        this.address = address;
        return this;
    }

    public Double getLatitude() {
        return latitude;
    }

    public PoiParam setLatitude(Double latitude) {
        this.latitude = latitude;
        return this;
    }

    public Double getLongitude() {
        return longitude;
    }

    public PoiParam setLongitude(Double longitude) {
        this.longitude = longitude;
        return this;
    }

    public String getPic_url() {
        return pic_url;
    }

    public PoiParam setPic_url(String pic_url) {
        this.pic_url = pic_url;
        return this;
    }

    public String getPhone() {
        return phone;
    }

    public PoiParam setPhone(String phone) {
        this.phone = phone;
        return this;
    }

    public String getStandby_tel() {
        return standby_tel;
    }

    public PoiParam setStandby_tel(String standby_tel) {
        this.standby_tel = standby_tel;
        return this;
    }

    public Float getShipping_fee() {
        return shipping_fee;
    }

    public PoiParam setShipping_fee(Float shipping_fee) {
        this.shipping_fee = shipping_fee;
        return this;
    }

    public String getShipping_time() {
        return shipping_time;
    }

    public PoiParam setShipping_time(String shipping_time) {
        this.shipping_time = shipping_time;
        return this;
    }

    public String getPromotion_info() {
        return promotion_info;
    }

    public PoiParam setPromotion_info(String promotion_info) {
        this.promotion_info = promotion_info;
        return this;
    }

    public String getRemark() {
        return remark;
    }

    public PoiParam setRemark(String remark) {
        this.remark = remark;
        return this;
    }

    public Integer getOpen_level() {
        return open_level;
    }

    public PoiParam setOpen_level(Integer open_level) {
        this.open_level = open_level;
        return this;
    }

    public Integer getIs_online() {
        return is_online;
    }

    public PoiParam setIs_online(Integer is_online) {
        this.is_online = is_online;
        return this;
    }

    public Integer getInvoice_support() {
        return invoice_support;
    }

    public PoiParam setInvoice_support(Integer invoice_support) {
        this.invoice_support = invoice_support;
        return this;
    }

    public Integer getInvoice_min_price() {
        return invoice_min_price;
    }

    public PoiParam setInvoice_min_price(Integer invoice_min_price) {
        this.invoice_min_price = invoice_min_price;
        return this;
    }

    public String getInvoice_description() {
        return invoice_description;
    }

    public PoiParam setInvoice_description(String invoice_description) {
        this.invoice_description = invoice_description;
        return this;
    }

    public Integer getCity_id() {
        return city_id;
    }

    public PoiParam setCity_id(Integer city_id) {
        this.city_id = city_id;
        return this;
    }

    public Integer getLocation_id() {
        return location_id;
    }

    public PoiParam setLocation_id(Integer location_id) {
        this.location_id = location_id;
        return this;
    }

    public Long getCtime() {
        return ctime;
    }

    public PoiParam setCtime(Long ctime) {
        this.ctime = ctime;
        return this;
    }

    public Long getUtime() {
        return utime;
    }

    public PoiParam setUtime(Long utime) {
        this.utime = utime;
        return this;
    }

    public String getThird_tag_name() {
        return third_tag_name;
    }

    public PoiParam setThird_tag_name(String third_tag_name) {
        this.third_tag_name = third_tag_name;
        return this;
    }

    public String getTag_name() {
        return tag_name;
    }

    public PoiParam setTag_name(String tag_name) {
        this.tag_name = tag_name;
        return this;
    }

    public String getSettlement_poi_id() {
        return settlement_poi_id;
    }

    public PoiParam setSettlement_poi_id(String settlement_poi_id) {
        this.settlement_poi_id = settlement_poi_id;
        return this;
    }

    public String getApp_brand_code() {
        return app_brand_code;
    }

    public PoiParam setApp_brand_code(String app_brand_code) {
        this.app_brand_code = app_brand_code;
        return this;
    }

    public Integer getPre_book() {
        return pre_book;
    }

    public PoiParam setPre_book(Integer pre_book) {
        this.pre_book = pre_book;
        return this;
    }

    public Integer getTime_select() {
        return time_select;
    }

    public PoiParam setTime_select(Integer time_select) {
        this.time_select = time_select;
        return this;
    }

    public Integer getPre_book_min_days() {
        return pre_book_min_days;
    }

    public PoiParam setPre_book_min_days(Integer pre_book_min_days) {
        this.pre_book_min_days = pre_book_min_days;
        return this;
    }

    public Integer getPre_book_max_days() {
        return pre_book_max_days;
    }

    public PoiParam setPre_book_max_days(Integer pre_book_max_days) {
        this.pre_book_max_days = pre_book_max_days;
        return this;
    }

    public String getPic_url_large() {
        return pic_url_large;
    }

    public PoiParam setPic_url_large(String pic_url_large) {
        this.pic_url_large = pic_url_large;
        return this;
    }

    public Integer getMt_type_id() {
        return mt_type_id;
    }

    public PoiParam setMt_type_id(Integer mt_type_id) {
        this.mt_type_id = mt_type_id;
        return this;
    }

    @Override
    public String toString() {
        return "PoiParam [" +
                "app_id=" + app_id +
                ", app_poi_code='" + app_poi_code + '\'' +
                ", name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", latitude=" + latitude +
                ", longitude=" + longitude +
                ", pic_url='" + pic_url + '\'' +
                ", phone='" + phone + '\'' +
                ", standby_tel='" + standby_tel + '\'' +
                ", shipping_fee=" + shipping_fee +
                ", shipping_time='" + shipping_time + '\'' +
                ", promotion_info='" + promotion_info + '\'' +
                ", remark='" + remark + '\'' +
                ", open_level=" + open_level +
                ", is_online=" + is_online +
                ", invoice_support=" + invoice_support +
                ", invoice_min_price=" + invoice_min_price +
                ", invoice_description='" + invoice_description + '\'' +
                ", city_id=" + city_id +
                ", location_id=" + location_id +
                ", ctime=" + ctime +
                ", utime=" + utime +
                ", third_tag_name='" + third_tag_name + '\'' +
                ", tag_name='" + tag_name + '\'' +
                ", settlement_poi_id='" + settlement_poi_id + '\'' +
                ", app_brand_code='" + app_brand_code + '\'' +
                ", pre_book=" + pre_book +
                ", time_select=" + time_select +
                ", pre_book_min_days=" + pre_book_min_days +
                ", pre_book_max_days=" + pre_book_max_days +
                ", pic_url_large='" + pic_url_large + '\'' +
                ", mt_type_id=" + mt_type_id +
                ']';
    }
}
