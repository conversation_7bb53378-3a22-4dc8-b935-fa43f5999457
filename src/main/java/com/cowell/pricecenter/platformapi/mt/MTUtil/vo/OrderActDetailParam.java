package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

public class OrderActDetailParam {
    private Long actId;
    private Integer type;
    private String remark;
    private Double mtCharge;
    private Double poiCharge;
    private Integer count;

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Double getMtCharge() {
        return mtCharge;
    }

    public void setMtCharge(Double mtCharge) {
        this.mtCharge = mtCharge;
    }

    public Double getPoiCharge() {
        return poiCharge;
    }

    public void setPoiCharge(Double poiCharge) {
        this.poiCharge = poiCharge;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "OrderActDetailParam{" +
                "actId=" + actId +
                ", type=" + type +
                ", remark='" + remark + '\'' +
                ", mtCharge=" + mtCharge +
                ", poiCharge=" + poiCharge +
                ", count=" + count +
                '}';
    }
}
