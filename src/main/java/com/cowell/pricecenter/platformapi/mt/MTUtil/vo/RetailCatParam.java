package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

import java.util.List;

public class RetailCatParam {
    private String code;
    private String name;
    private String sequence;
    private List<RetailCatParam> children;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public RetailCatParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getSequence() {
        return sequence;
    }

    public RetailCatParam setSequence(String sequence) {
        this.sequence = sequence;
        return this;
    }

    public List<RetailCatParam> getChildren() {
        return children;
    }

    public RetailCatParam setChildren(List<RetailCatParam> children) {
        this.children = children;
        return this;
    }

    @Override
    public String toString() {
        return "RetailCatParam{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", sequence='" + sequence + '\'' +
                ", children=" + children +
                '}';
    }
}
