package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

public class ActRetailDiscountParam {
    private Long item_id;
    private Double act_price;
    private Double origin_price;
    private String app_food_code;
    //每日活动库存
    private Integer day_limit;
    private Integer start_time;
    private Integer end_time;
    //每单限购数量
    private Integer order_limit;
    private String period;
    private Integer user_type;
    private String weeks_time;
    private String name;
    private Integer stock;
    //活动状态，0:过期，1:生效，2:待生效
    private Integer status;
    //折扣方式，0:按比例，1:按价格
    private Integer setting_type;
    //折扣系数，需大于0小于9.8，且最多两位小数
    private Double discount_coefficient;

    //在折扣活动中的序号
    private Integer sequence;

    public String getApp_food_code() {
        return app_food_code;
    }

    public void setApp_food_code(String app_food_code) {
        this.app_food_code = app_food_code;
    }

    public Integer getUser_type() {
        return user_type;
    }

    public void setUser_type(Integer user_type) {
        this.user_type = user_type;
    }

    public Integer getStart_time() {
        return start_time;
    }

    public void setStart_time(Integer start_time) {
        this.start_time = start_time;
    }

    public Integer getEnd_time() {
        return end_time;
    }

    public void setEnd_time(Integer end_time) {
        this.end_time = end_time;
    }

    public Integer getOrder_limit() {
        return order_limit;
    }

    public void setOrder_limit(Integer order_limit) {
        this.order_limit = order_limit;
    }

    public Integer getDay_limit() {
        return day_limit;
    }

    public void setDay_limit(Integer day_limit) {
        this.day_limit = day_limit;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getWeeks_time() {
        return weeks_time;
    }

    public void setWeeks_time(String weeks_time) {
        this.weeks_time = weeks_time;
    }

    public Integer getSetting_type() {
        return setting_type;
    }

    public void setSetting_type(Integer setting_type) {
        this.setting_type = setting_type;
    }

    public Double getAct_price() {
        return act_price;
    }

    public void setAct_price(Double act_price) {
        this.act_price = act_price;
    }

    public Double getDiscount_coefficient() {
        return discount_coefficient;
    }

    public void setDiscount_coefficient(Double discount_coefficient) {
        this.discount_coefficient = discount_coefficient;
    }

    public Long getItem_id() {
        return item_id;
    }

    public void setItem_id(Long item_id) {
        this.item_id = item_id;
    }

    public Double getOrigin_price() {
        return origin_price;
    }

    public void setOrigin_price(Double origin_price) {
        this.origin_price = origin_price;
    }

    public Integer getStock() {
        return stock;
    }

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    @Override
    public String toString() {
        return "ActRetailDiscountParam{" +
                "app_food_code='" + app_food_code + '\'' +
                ", user_type=" + user_type +
                ", start_time=" + start_time +
                ", end_time=" + end_time +
                ", order_limit=" + order_limit +
                ", day_limit=" + day_limit +
                ", period='" + period + '\'' +
                ", weeks_time='" + weeks_time + '\'' +
                ", setting_type=" + setting_type +
                ", act_price=" + act_price +
                ", discount_coefficient=" + discount_coefficient +
                ", item_id=" + item_id +
                ", origin_price=" + origin_price +
                ", stock=" + stock +
                ", status=" + status +
                ", name='" + name + '\'' +
                ", sequence=" + sequence +
                '}';
    }
}
