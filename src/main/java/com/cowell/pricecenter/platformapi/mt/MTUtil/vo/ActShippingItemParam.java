package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

public class ActShippingItemParam {

    private Double limitPrice;
    private Double discountPrice;
    private Double poiCharge;
    private Double mtCharge;

    public Double getLimitPrice() {
        return limitPrice;
    }

    public void setLimitPrice(Double limitPrice) {
        this.limitPrice = limitPrice;
    }

    public Double getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(Double discountPrice) {
        this.discountPrice = discountPrice;
    }

    public Double getPoiCharge() {
        return poiCharge;
    }

    public void setPoiCharge(Double poiCharge) {
        this.poiCharge = poiCharge;
    }

    public Double getMtCharge() {
        return mtCharge;
    }

    public void setMtCharge(Double mtCharge) {
        this.mtCharge = mtCharge;
    }

    @Override
    public String toString() {
        return "ActShippingItemParam{" +
                "limitPrice=" + limitPrice +
                ", discountPrice=" + discountPrice +
                ", poiCharge=" + poiCharge +
                ", mtCharge=" + mtCharge +
                '}';
    }
}
