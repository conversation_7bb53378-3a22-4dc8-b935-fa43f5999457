package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

import java.util.List;

public class MultiPoisInitDataParam {
    //操作类型：1-创建，2-更新
    private Integer type;

    //要同步的门店code
    private String app_poi_codes;

    //1-同步APP下全部门店，其他值不处理
    private Integer is_all_pois;

    //待更新、创建的商品信息
    private List<RetailParam> retail_info;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getApp_poi_codes() {
        return app_poi_codes;
    }

    public void setApp_poi_codes(String app_poi_codes) {
        this.app_poi_codes = app_poi_codes;
    }

    public Integer getIs_all_pois() {
        return is_all_pois;
    }

    public void setIs_all_pois(Integer is_all_pois) {
        this.is_all_pois = is_all_pois;
    }

    public List<RetailParam> getRetail_info() {
        return retail_info;
    }

    public void setRetail_info(List<RetailParam> retail_info) {
        this.retail_info = retail_info;
    }

    @Override
    public String toString() {
        return "MultiPoisInitData{" +
                "type=" + type +
                ", app_poi_codes='" + app_poi_codes + '\'' +
                ", is_all_pois=" + is_all_pois +
                ", retail_info=" + retail_info +
                '}';
    }
}
