package com.cowell.pricecenter.platformapi.mt.MTUtil.vo;

import java.util.List;

public class OrderSkuBenefitDetailParam {
    private String app_food_code;
    private String name;
    private String sku_id;
    private Integer count;
    private Double totalOriginPrice;
    private Double  originPrice;
    private Double totalReducePrice;
    private Double totalActivityPrice;
    private Double activityPrice;
    private Double totalMtCharge;
    private Double totalPoiCharge;
    private Double totalBoxPrice;
    private Double boxPrice;
    private Double boxNumber;
    private List<OrderActDetailParam> wmAppOrderActDetails;

    public String getApp_food_code() {
        return app_food_code;
    }

    public void setApp_food_code(String app_food_code) {
        this.app_food_code = app_food_code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSku_id() {
        return sku_id;
    }

    public void setSku_id(String sku_id) {
        this.sku_id = sku_id;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getTotalOriginPrice() {
        return totalOriginPrice;
    }

    public void setTotalOriginPrice(Double totalOriginPrice) {
        this.totalOriginPrice = totalOriginPrice;
    }

    public Double getOriginPrice() {
        return originPrice;
    }

    public void setOriginPrice(Double originPrice) {
        this.originPrice = originPrice;
    }

    public Double getTotalReducePrice() {
        return totalReducePrice;
    }

    public void setTotalReducePrice(Double totalReducePrice) {
        this.totalReducePrice = totalReducePrice;
    }

    public Double getTotalActivityPrice() {
        return totalActivityPrice;
    }

    public void setTotalActivityPrice(Double totalActivityPrice) {
        this.totalActivityPrice = totalActivityPrice;
    }

    public Double getActivityPrice() {
        return activityPrice;
    }

    public void setActivityPrice(Double activityPrice) {
        this.activityPrice = activityPrice;
    }

    public Double getTotalMtCharge() {
        return totalMtCharge;
    }

    public void setTotalMtCharge(Double totalMtCharge) {
        this.totalMtCharge = totalMtCharge;
    }

    public Double getTotalPoiCharge() {
        return totalPoiCharge;
    }

    public void setTotalPoiCharge(Double totalPoiCharge) {
        this.totalPoiCharge = totalPoiCharge;
    }

    public Double getTotalBoxPrice() {
        return totalBoxPrice;
    }

    public void setTotalBoxPrice(Double totalBoxPrice) {
        this.totalBoxPrice = totalBoxPrice;
    }

    public Double getBoxPrice() {
        return boxPrice;
    }

    public void setBoxPrice(Double boxPrice) {
        this.boxPrice = boxPrice;
    }

    public Double getBoxNumber() {
        return boxNumber;
    }

    public void setBoxNumber(Double boxNumber) {
        this.boxNumber = boxNumber;
    }

    public List<OrderActDetailParam> getWmAppOrderActDetails() {
        return wmAppOrderActDetails;
    }

    public void setWmAppOrderActDetails(List<OrderActDetailParam> wmAppOrderActDetails) {
        this.wmAppOrderActDetails = wmAppOrderActDetails;
    }

    @Override
    public String toString() {
        return "OrderSkuParam{" +
                "app_food_code='" + app_food_code + '\'' +
                ", name='" + name + '\'' +
                ", sku_id='" + sku_id + '\'' +
                ", count=" + count +
                ", totalOriginPrice=" + totalOriginPrice +
                ", originPrice=" + originPrice +
                ", totalReducePrice=" + totalReducePrice +
                ", totalActivityPrice=" + totalActivityPrice +
                ", activityPrice=" + activityPrice +
                ", totalMtCharge=" + totalMtCharge +
                ", totalPoiCharge=" + totalPoiCharge +
                ", totalBoxPrice=" + totalBoxPrice +
                ", boxPrice=" + boxPrice +
                ", boxNumber=" + boxNumber +
                ", wmAppOrderActDetails=" + wmAppOrderActDetails +
                '}';
    }
}
