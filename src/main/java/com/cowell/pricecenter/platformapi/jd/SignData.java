package com.cowell.pricecenter.platformapi.jd;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 京东到家接口调用签名参数
 * @date 2018-07-24 10:07
 */

public class SignData {

    /**
     * 采用OAuth授权方式为必填参数。
     */
    private String token;

    /**
     * 应用的app_key
     */
    private String app_key;

    /**
     * 时间戳，格式为yyyy-MM-dd HH:mm:ss，例如：2011-06-16 13:23:30。京东API服务端允许客户端请求时间误差为6分钟
     */
    private String timestamp;

    /**
     * 暂时只支持json
     */
    private String format;

    /**
     * API协议版本，可选值:1.0.
     */
    private String v;

    /**
     * 签名
     */
    private String jd_param_json;

    /**
     * 签名参数
     */
    private Map<String, String> signMap;

    /**
     * sign的生成规则(转为大写(MD5(appSecret + (系统参数+应用参数) 升序 + appSecret)) )
     */
    private String sign;

    /**
     * 加密参数所用
     */
    private String appSecret;

    public String getToken() {
        return token;
    }

    public String getapp_key() {
        return app_key;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public String getFormat() {
        return format;
    }

    public String getV() {
        return v;
    }

    public String getJd_param_json() {
        return jd_param_json;
    }

    public Map<String, String> getSignMap() {
        return signMap;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public SignData(String token, String app_key, String timestamp, String format,
                    String v, String sign, String jd_param_json,
                    Map<String, String> signMap, String appSecret) {
        this.token = token;
        this.app_key = app_key;
        this.timestamp = timestamp;
        this.format = format;
        this.v = v;
        this.jd_param_json = jd_param_json;
        this.sign = sign;
        this.appSecret = appSecret;
        this.signMap = new HashMap<>();
        this.signMap.put("token", token);
        this.signMap.put("app_key", app_key);
        this.signMap.put("timestamp", timestamp);
        this.signMap.put("format", format);
        this.signMap.put("v", v);
        this.signMap.put("jd_param_json", jd_param_json);

    }


    public static SignData.Builder custom() {
        return new Builder();
    }

    public static class Builder {

        /**
         * 采用OAuth授权方式为必填参数。
         */
        private String token;

        /**
         * 应用的app_key
         */
        private String app_key;

        /**
         * 时间戳，格式为yyyy-MM-dd HH:mm:ss，例如：2011-06-16 13:23:30。京东API服务端允许客户端请求时间误差为6分钟
         */
        private String timestamp;

        /**
         * 暂时只支持json
         */
        private String format;

        /**
         * API协议版本，可选值:1.0.
         */
        private String v;

        /**
         * 应用级参数
         */
        private String jd_param_json;

        /**
         * 签名
         */
        private String sign;

        /**
         * 加密参数所用
         */
        private String appSecret;

        /**
         * 签名参数
         */
        private Map<String, String> signMap;

        public Builder setToken(String token) {
            this.token = token;
            return this;
        }


        public Builder setTimestamp(String timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        public Builder setFormat(String format) {
            this.format = format;
            return this;
        }

        public Builder setV(String v) {
            this.v = v;
            return this;
        }

        public Builder setJd_param_json(String jd_param_json) {
            this.jd_param_json = jd_param_json;
            return this;
        }

        public Builder setSign(String sign) {
            this.sign = sign;
            return this;
        }

        public Builder setApp_key(String app_key) {
            this.app_key = app_key;
            return this;
        }

        public Builder setAppSecret(String appSecret) {
            this.appSecret = appSecret;
            return this;
        }

        public Builder setSignMap(Map<String, String> signMap) {
            this.signMap = signMap;
            return this;
        }

        public SignData build() {
            return new SignData(token, app_key, timestamp,
                format, v, sign, jd_param_json, signMap, appSecret);
        }

    }

}
