package com.cowell.pricecenter.platformapi.jd;

import com.cowell.pricecenter.constant.PriceSyncConstant;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * 类说明 京东签名
 *
 * @Author: liw
 * @Date: 2020-03-05 13:48
 */
public class RequestParamUtils {


    /**
     * 获取 京东签名 参数
     * @param token
     * @param app_key
     * @param timestamp
     * @param app_secret
     * @param jd_param_json
     * @param v
     * @return
     */
    public static MultiValueMap<String, Object> getRequestParam(String token, String app_key, String timestamp,
                                                                String app_secret, String jd_param_json, String v) {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<String, Object>();

        params.put(PriceSyncConstant.JD_TOKEN_FIELD, new ArrayList<>(Arrays.asList(token)));

        params.put(PriceSyncConstant.JD_APP_KEY_FIELD, new ArrayList<>(Arrays.asList(app_key)));

        timestamp = new DateTime().minusMinutes(2).toString(PriceSyncConstant.DATE_FORMAT_);

        SignData data = SignData.custom().setApp_key(app_key).setAppSecret(app_secret).setTimestamp(timestamp)
            .setJd_param_json(jd_param_json).setToken(token).setV(PriceSyncConstant.JD_VERSION_FIELD_VALUE)
            .setFormat(PriceSyncConstant.JD_FORMAT_FIELD_VALUE).build();

        //加密之后字符串 sign
        params.put(PriceSyncConstant.JD_SIGN_FIELD, new ArrayList<>(Arrays.asList(SignHelper.getSign(data))));

        //timestamp
        params.put(PriceSyncConstant.JD_TIME_STAMP_FIELD, new ArrayList<>(Arrays.asList(timestamp)));

        //format
        params.put(PriceSyncConstant.JD_FORMAT_FIELD, new ArrayList<>(Arrays.asList(PriceSyncConstant.JD_FORMAT_FIELD_VALUE)));
        //v
        params.put(PriceSyncConstant.JD_VERSION_FIELD, new ArrayList<>(Arrays.asList(v)));
        if (!StringUtils.isEmpty(jd_param_json)) {
            params.put(PriceSyncConstant.JD_PARAM_JSON_FIELD, new ArrayList<>(Arrays.asList(jd_param_json)));
        }
        return params;
    }
}
