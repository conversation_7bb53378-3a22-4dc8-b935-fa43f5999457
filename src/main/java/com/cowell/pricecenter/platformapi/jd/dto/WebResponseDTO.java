package com.cowell.pricecenter.platformapi.jd.dto;

import com.cowell.pricecenter.enums.OmsReturnCodeEnum;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @Description: 响应对象通过
 * @date 2018-07-19 15:23
 */
public class WebResponseDTO<T> {

    /**
     * 成功标识
     */
    public static final String SUCCESS_CODE_VALUE = "0";

    /**
     * 0:成功，其他失败
     */
    private String code;

    /**
     * 描述
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    /**
     * 获取 code
     *
     * @return code
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置 code
     *
     * @param code
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取 msg
     *
     * @return msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 设置 msg
     *
     * @param msg
     */
    public void setMsg(String msg) {
        this.msg = msg;
    }

    /**
     * data
     *
     * @return the data
     */

    public T getData() {
        return data;
    }

    /**
     * @param data the data to set
     */
    public void setData(T data) {
        this.data = data;
    }

    /**
     * 成功结果 (code="0",msg="",data=data)
     *
     * @param data 响应数据
     * @return <R> 响应结果
     */
    public static <R> WebResponseDTO<R> success(R data) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode("0");
        result.setMsg("success");
        result.setData(data);
        return result;
    }

    /**
     * @param code 响应码
     * @param msg  响应信息
     * @return <R> 返回结果
     */
    public static <R> WebResponseDTO<R> success(String code, String msg) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }

    /**
     * @param code 响应码
     * @param msg  响应信息
     * @param omsOrderId  订单号
     * @return <R> 返回结果
     */
    public static <R> WebResponseDTO<R> success(String code, String msg,String omsOrderId) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData((R) omsOrderId);
        return result;
    }

    /**
     * 失败结果
     *
     * @param code 错误码
     * @param msg  错误信息
     * @param data 响应数据
     * @return <R> 返回结果
     */
    public static <R> WebResponseDTO<R> error(String code, String msg, R data) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }

    public static <R> WebResponseDTO<R> successOms(OmsReturnCodeEnum omsReturnCodeEnum) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(omsReturnCodeEnum.getCode());
        result.setMsg(omsReturnCodeEnum.getMsg());
        result.setData(null);
        return result;
    }
    public static <R> WebResponseDTO<R> successOms(R data,OmsReturnCodeEnum omsReturnCodeEnum) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(omsReturnCodeEnum.getCode());
        result.setMsg(omsReturnCodeEnum.getMsg());
        result.setData(data);
        return result;
    }

    public static <R> WebResponseDTO<R> errorOms(R data,OmsReturnCodeEnum omsReturnCodeEnum) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(omsReturnCodeEnum.getCode());
        result.setMsg(omsReturnCodeEnum.getMsg());
        result.setData(data);
        return result;
    }
    public static <R> WebResponseDTO<R> errorOms(String code, String msg) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(null);
        return result;
    }
    public static <R> WebResponseDTO<R> errorOms(R data,String code, String msg) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(data);
        return result;
    }
    public static <R> WebResponseDTO<R> successOms() {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(OmsReturnCodeEnum.SUCCESS_200.getCode());
        result.setMsg(OmsReturnCodeEnum.SUCCESS_200.getMsg());
        result.setData(null);
        return result;
    }

    public static <R> WebResponseDTO<R> successOms(R data) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(OmsReturnCodeEnum.SUCCESS_200.getCode());
        result.setMsg(OmsReturnCodeEnum.SUCCESS_200.getMsg());
        result.setData(data);
        return result;
    }
    /**
     * 失败结果
     *
     * @param code 错误码
     * @param msg  错误信息
     * @return <R> 响应结果
     */
    public static <R> WebResponseDTO<R> error(String code, String msg) {
        WebResponseDTO<R> result = new WebResponseDTO<R>();
        result.setCode(code);
        result.setMsg(msg);
        result.setData(null);
        return result;
    }

    /**
     * 是否成功！
     *
     * @return
     */
    public boolean isSuccess() {
        return SUCCESS_CODE_VALUE.equals(this.code);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
