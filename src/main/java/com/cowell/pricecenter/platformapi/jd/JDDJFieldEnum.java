package com.cowell.pricecenter.platformapi.jd;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @ProjectName oms
 * @Description:
 * @date 2019/08/19 18:45
 */
@AllArgsConstructor
public enum JDDJFieldEnum {
    TOKEN("token"),
    APPKEY("app_key"),
    SIGN("sign"),
    TIMESTAMP("timestamp"),
    FORMAT("format"),
    VERSION("v"),
    OUTSTATIONNO("outStationNo"),
    STATIONNO("stationNo"),
    OUTSKUID("outSkuId"),
    PRICE("price"),
    CODE("code"),
    MSG("msg"),
    SUCCESS("success"),
    ERRORCODE("errorCode"),
    ERRORMESSAGE("errorMessage"),
    JDPARAMJSON("jd_param_json"),
    DATA("data"),
    RESULT("result"),
    ITEMNAME("itemName"),
    SKUPRICEINFOLIST("skuPriceInfoList"),
    USESCOPE("useScope"),
    CHANNELMAPPING("channel_mapping"),
    PAGENO("pageNo"),
    PAGESIZE("pageSize"),
    ISFILTERDEL("isFilterDel"),
    SKUIDS("skuIds"),
    SKUID("skuId"),
    WARESTATUSVALUE("wareStatusValue"),
    ;
    @Getter
    @Setter
    private String field;
}
