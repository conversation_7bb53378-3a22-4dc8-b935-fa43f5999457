package com.cowell.pricecenter.platformapi.jd;

import org.springframework.util.DigestUtils;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: 京东到家接口签名工具
 * @date 2018-07-24 10:07
 */
public class SignHelper {

    /**
     * 获取MD5加密之后的串
     *
     * @param signData
     * @return String
     */
    public static String getSign(SignData signData) {
        String sysStr = concatParams(signData.getSignMap());
        StringBuilder resultStr = new StringBuilder();
        resultStr.append(signData.getAppSecret()).append(sysStr).append(signData.getAppSecret());
        return DigestUtils.md5DigestAsHex(resultStr.toString().getBytes()).toUpperCase();
    }

    /**
     * 拼接需要加密的参数
     *
     * @param params2 参数列表
     * @return String
     */
    private static String concatParams(Map<String, String> params2) {
        Object[] key_arr = params2.keySet().toArray();
        Arrays.sort(key_arr);
        StringBuilder str = new StringBuilder();
        for (Object key : key_arr) {
            String val = params2.get(key);
            str.append(key).append(val);
        }
        return str.toString();
    }

}
