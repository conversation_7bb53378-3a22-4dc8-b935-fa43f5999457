package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version OrderDiscount.java, 2019/7/25 15:47
 */
@Data
public class OrderDiscount {
    /**
     * 优惠类型
     */
    private String  type;
    /**
     * 优惠金额 单位：分
     */
    private String  fee;
    /**
     * 活动ID
     */
    private String  activity_id;
    /**
     * 平台承担金额
     */
    private String  baidu_rate;
    /**
     * 商户承担金额
     */
    private String  shop_rate;
    /**
     * 代理商承担金额
     */
    private String  agent_rate;
    /**
     * 物流承担金额
     */
    private String  logistics_rate;
    /**
     * 优惠描述
     */
    private String  desc;
    /**
     * 特价菜(单品
     */
    private List<SaleGoods>    products;
}
