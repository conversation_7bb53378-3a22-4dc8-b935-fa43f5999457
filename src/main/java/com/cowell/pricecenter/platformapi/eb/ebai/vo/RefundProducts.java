package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2019-08-02 14:46
 */
@Data
public class RefundProducts {
    /**
     * 退款商品sku码
     */
    private String sku_id;
    /**
     * 退款商品upc
     */
    private String upc;
    /**
     * 退款商品upc
     */
    private String custom_sku_id;
    /**
     * 退款商品名称
     */
    private String name;
    /**
     * 退款商品数量
     */
    private String number;
    /**
     * 退款商品退用户金额,单位:分
     */
    private String total_refund;
    /**
     * 商家退还给平台补贴的金额,单位:分
     */
    private String shop_ele_refund;
}
