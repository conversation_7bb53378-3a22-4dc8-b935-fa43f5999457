package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version EbaiRequestDTO.java, 2019/7/25 10:58
 */
@Data
public class EbaiResponseDTO<T> {
    /**
     * 请求命令
     */
    private String cmd;
    /**
     * 版本,默认3
     */
    private String version = "3";
    /**
     * 时间戳
     */
    private Integer timestamp ;
    /**
     * 请求流水号
     */
    private String  ticket;
    /**
     * 对接方账号
     */
    private String  source;
    /**
     * 签名,md5
     */
    private String  sign;
    /**
     * 是否加密,如AES
     */
    private String  encrypt;
    /**
     *
     */
    private EbaiResponseData<T>   body;

}
