package com.cowell.pricecenter.platformapi.eb.ebai;

import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.enums.EBaiFieldEnum;
import com.google.common.collect.Maps;
import com.google.gson.Gson;

import java.lang.reflect.Field;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class SignUtil {
	/**
	 * 根据参数获取签名
	 * @return String
	 */
	public static String getSign(Map<String, Object> data) {
		TreeMap<String, Object> arr = new TreeMap<String, Object>();
		Gson gson = new Gson();
		arr.put("body", gson.toJson(data.get("body")));
		arr.put("cmd", data.get("cmd"));
		arr.put("encrypt", "");
		arr.put("secret", data.get("secret"));
		arr.put("source", data.get("source"));
		arr.put("ticket", data.get("ticket"));
		arr.put("timestamp", data.get("timestamp"));
		arr.put("version", data.get("version"));
		StringBuilder strSignTmp = new StringBuilder("");
		Iterator it = arr.keySet().iterator();
		while (it.hasNext()) {
			String key = it.next().toString();
			strSignTmp.append(key + "=" + arr.get(key) + "&");
		}
		String strSign = strSignTmp.toString().substring(0, strSignTmp.length() - 1);
		String sign = getMD5(strSign.toString());
		return sign;
	}

    /**
     * 根据参数获取签名
     *
     * @param
     * @return String
     */
    public static String getSign(Map<String, Object> data, Map<String, Object> config) {
        TreeMap<String, Object> arr = new TreeMap<String, Object>();
        arr.put("body", data.get("body"));
        arr.put("cmd", data.get("cmd"));
        arr.put("encrypt", "");
        arr.put("secret", config.get("secret"));
        arr.put("source", data.get("source"));
        arr.put("ticket", data.get("ticket"));
        arr.put("timestamp", data.get("timestamp"));
        arr.put("version", data.get("version"));
        StringBuilder strSignTmp = new StringBuilder("");
        Iterator it = arr.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next().toString();
            strSignTmp.append(key + "=" + arr.get(key) + "&");
        }
        String strSign = strSignTmp.toString().substring(0, strSignTmp.length() - 1);
        String sign = getMD5(strSign.toString());
        return sign;
    }

    /**
     * 根据参数获取签名
     *
     * @param
     * @return String
     */
    public static String getSignString(Map<String, String> data, Map<String, Object> config) {
        TreeMap<String, Object> arr = new TreeMap<String, Object>();
        arr.put("body", data.get("body"));
        arr.put("cmd", data.get("cmd"));
        arr.put("encrypt", "");
        arr.put("secret", config.get("secret"));
        arr.put("source", data.get("source"));
        arr.put("ticket", data.get("ticket"));
        arr.put("timestamp", data.get("timestamp"));
        arr.put("version", data.get("version"));
        StringBuilder strSignTmp = new StringBuilder("");
        Iterator it = arr.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next().toString();
            strSignTmp.append(key + "=" + arr.get(key) + "&");
        }
        String strSign = strSignTmp.toString().substring(0, strSignTmp.length() - 1);
        String sign = getMD5(strSign.toString());
        return sign;
    }

    /**
     * 根据参数获取签名
     *
     * @param
     * @return String
     */
    public static String getReqparamSign(Map<String, Object> data) {
        TreeMap<String, Object> arr = Maps.newTreeMap();
        arr.put(EBaiFieldEnum.BODY.getField(), data.get(EBaiFieldEnum.BODY.getField()));
        arr.put(EBaiFieldEnum.CMD.getField(), data.get(EBaiFieldEnum.CMD.getField()));
        arr.put(EBaiFieldEnum.ENCRYPT.getField(), "");
        arr.put(EBaiFieldEnum.APPSECRET.getField(), data.get(EBaiFieldEnum.APPSECRET.getField()));
        arr.put(EBaiFieldEnum.SOURCE.getField(), data.get(EBaiFieldEnum.SOURCE.getField()));
        arr.put(EBaiFieldEnum.TICKET.getField(), data.get(EBaiFieldEnum.TICKET.getField()));
        arr.put(EBaiFieldEnum.TIMESTAMP.getField(), data.get(EBaiFieldEnum.TIMESTAMP.getField()));
        arr.put(EBaiFieldEnum.VERSION.getField(), data.get(EBaiFieldEnum.VERSION.getField()));
        StringBuilder strSignTmp = new StringBuilder("");
        Iterator it = arr.keySet().iterator();
        while (it.hasNext()) {
            String key = it.next().toString();
            strSignTmp.append(key + "=" + arr.get(key) + "&");
        }
        String strSign = strSignTmp.toString().substring(0, strSignTmp.length() - 1);
        String sign = getMD5(strSign.toString());
        return sign;
    }
    /**
     * 校验签名是否正确
     *
     * @param
     * @return boolean
     */
    public static boolean checkSign(Map<String, Object> data, Map<String, Object> config) {
        String signFrom = getSign(data, config);
        String sign = data.get("sign").toString();
        if (signFrom.equals(sign)) {
            return true;
        } else {
            return false;
        }
    }

	/**
	 * 获取MD5
	 * @return String
	 */
	public static String getMD5(String input) {
		try {
			MessageDigest md = MessageDigest.getInstance("MD5");
			byte[] messageDigest = md.digest(input.getBytes());
			BigInteger number = new BigInteger(1, messageDigest);
			String hashtext = number.toString(16);
			while (hashtext.length() < 32) {
				hashtext = "0" + hashtext;
			}
			return hashtext.toUpperCase();
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		}
	}

    /**
     * 中文转unicode,如果参数中有中文，需要转一下
     *
     * @param gbString
     * @return
     */
    public static String gbEncoding(final String gbString) {
        char[] utfBytes = gbString.toCharArray();
        String unicodeBytes = "";
        for (int i = 0; i < utfBytes.length; i++) {
            String hexB = Integer.toHexString(utfBytes[i]);
            if (hexB.length() <= 2) {
                hexB = "00" + hexB;
            }
            unicodeBytes = unicodeBytes + "\\u" + hexB;
        }
        return unicodeBytes;
    }

    /**
     * 对象转化成map
     * @param object
     * @return
     */
    public static Map<String,String> convertToMap(Object object) throws IllegalAccessException {
        Map<String,String> resultMap = new HashMap<>();

        Field[] fields = object.getClass().getDeclaredFields();
        for (Field field : fields){
            field.setAccessible(true);
            Class typeClazz = field.getType();
            String key = field.getName();
            String val = null;
            if(List.class.isAssignableFrom(typeClazz)){
                val = JSON.toJSONString(field.get(object));
            }else {
                val = String.valueOf(field.get(object));
            }
            if (val != null && !"".equals(val) && !"null".equals(val) && !"NULL".equals(val)) {
                resultMap.put(key, val);
            }
        }

        return resultMap;
    }

}
