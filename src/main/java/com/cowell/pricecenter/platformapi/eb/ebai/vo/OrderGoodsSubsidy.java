package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version OrderGoods.java, 2019/7/25 15:31
 */
@Data
public class OrderGoodsSubsidy {

    /**
     * 总优惠金额;单位分;
     */
    private  Long discount;
    /**
     * 平台承担费用;单位分;
     */
    private  Long baidu_rate;
    /**
     * 商户承担费用;单位分;
     */
    private  Long shop_rate;
    /**
     * 代理商承担费用;单位分;
     */
    private  Long agent_rate;
    /**
     * 物流承担费用;单位分;
     */
    private  Long logistics_rate;
    /**
     * 订单优惠信息明细
     */
    private  List<OrderGoodsSubsidyDiscountDetail> discount_detail;
}
