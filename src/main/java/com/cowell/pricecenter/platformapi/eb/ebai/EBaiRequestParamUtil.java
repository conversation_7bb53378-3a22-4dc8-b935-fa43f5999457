package com.cowell.pricecenter.platformapi.eb.ebai;

import com.cowell.pricecenter.enums.EBaiFieldEnum;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.UUID;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-03-05 21:36
 */
public class EBaiRequestParamUtil {

    /**
     * 饿百系统及配置参数
     * @param methodName 方法名
     * @param source 配置信息 source
     * @param appSecret 配置信息 appSecret
     * @return
     */
    public static Map<String,Object> getSystemParam(String methodName, String source,String appSecret){
        Map<String,Object> map = Maps.newHashMap();
        //请求命令
        map.put(EBaiFieldEnum.CMD.getField(),methodName);
        //时间戳
        map.put(EBaiFieldEnum.TIMESTAMP.getField(),System.currentTimeMillis()/1000);
        //版本,默认3
        map.put(EBaiFieldEnum.VERSION.getField(),Integer.valueOf(EBaiFieldEnum.VERSIONVALUE.getField()));
        //请求流水号
        map.put(EBaiFieldEnum.TICKET.getField(), StringUtils.upperCase(UUID.randomUUID().toString()));
        //对接方账号
        map.put(EBaiFieldEnum.SOURCE.getField(),source);
        //输入参数计算后的签名结果
        map.put(EBaiFieldEnum.APPSECRET.getField(),appSecret);
        return map;
    }
}
