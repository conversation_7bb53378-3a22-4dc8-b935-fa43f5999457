package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version OrderExt.java, 2019/7/25 15:23
 */
@Data
public class OrderExt {

    /**
     * 订购人电话
     */
    private String  giver_phone;
    /**
     * 祝福语
     */
    private String  greeting;
    /**
     * 部分退款标识, 1 商户发起 2 用户发起
     */
    private String  part_refund;
    /**
     * 1表示订单完成前用户全单取消申请流程，2表示订单完成后用户全单退款申请流程
     */
    private String  user_cancel;
    /**
     * 淘系订单标识, 0 非淘系订单 1 淘系订单
     */
    private String  taoxi_flag;
    /**
     * 全单退状态（订单为全单退订单时会有此字段）10:发起申请,20:客服介入,30:客服拒绝,40:客服同意,50:商户拒绝,60:商户同意,70:申请失效
     */
    private Long  online_cancel_status;
    /**
     * 部分退状态（订单为部分退订单时会有此字段）10表示商家/用户发起部分退款申请 20表示部分退款成功 30用户申请仲裁,客服介入 40表示部分退款失败 50表示商家拒绝用户发起的部分退款申
     */
    private Long  part_refund_status;

}
