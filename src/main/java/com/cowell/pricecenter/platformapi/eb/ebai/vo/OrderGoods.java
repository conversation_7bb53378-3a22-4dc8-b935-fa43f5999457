package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version OrderGoods.java, 2019/7/25 15:31
 */
@Data
public class OrderGoods {

    /**
     * 商品ID
     */
    private String baidu_product_id;
    /**
     * 合作商品ID;未设置为空;
     */
    private String custom_sku_id;
    /**
     * 商品UPC
     */
    private String upc;
    /**
     * 菜品类型,1.单品 2 套餐 3 配料
     */
    private String product_type;
    /**
     * 商品名称
     */
    private String product_name;
    /**
     * 商品份数
     */
    private String product_amount;
    /**
     * 价格
     */
    private String product_price;
    /**
     * 商品属性
     */
    private List<OrderGoodsFeature>   product_features;
    /**
     * 商品总价，单位：分
     */
    private Long  product_fee;
    /**
     * 餐盒总价，单位：分
     */
    private Long  package_fee;
    /**
     * 餐盒单价，单位：分
     */
    private Long  package_price;
    /**
     * 餐盒数量
     */
    private Long  package_amount;
    /**
     * 商品和商品维度餐盒费总价，单位：分，暂不支持商品维度餐盒费;
     */
    private Long  total_fee;
    /**
     * 0 代表普通配送，1代表冷链配送
     */
    private Long  supply_type;
    /**
     * 商品唯一串
     */
    private String product_custom_index;
    /**
     * 订单优惠信息;一维数组;
     */
    private List<OrderGoodsSubsidy>   product_subsidy;
    /**
     * 处方药编号;不存在为空;
     */
    private Long  prescription_id;
}
