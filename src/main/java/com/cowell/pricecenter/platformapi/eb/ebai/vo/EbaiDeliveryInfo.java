package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version EbaiDeliveryInfo.java, 2019/8/14 13:31
 */
@Data
public class EbaiDeliveryInfo {

    /**
     * 订单ID
     运单状态 
     0:无配送状态 1:待请求配送 2:生成运单,待分配配送商 3:请求配送 4:待分配骑士 7:骑士接单 8:骑士取餐,配送中 21:骑士到店 
     15:配送取消 16:配送成功 17:配送异常 18:商家自行配送 19:商家不再配送 20:物流拒单
     运单子状态 
     0:不存在对应子状态 1:商家取消 2:配送商取消 3:用户取消 4:物流系统取消 
     5:呼叫配送晚 6:餐厅出餐问题 7:商户中断配送 8:用户不接电话 9:用户退单 10:用户地址错误 11:超出服务范围 12:骑手标记异常 
     13:系统自动标记异常-订单超过3小时未送达 14:其他异常 15:超市取消/异常 
     101:只支持在线订单 102:超出服务范围 103:请求配送过晚,无法呼叫 104:系统异常 105:超出营业时间
     配送员姓名
     配送员手机号
     记录更新时间
     */

private String order_id;
private String status;
private String  substatus;
private String name;
private String phone;
private String update_time;

}
