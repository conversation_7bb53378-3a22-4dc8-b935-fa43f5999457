package com.cowell.pricecenter.platformapi.eb.ebai.vo;

import lombok.Data;

import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version EbaiOrderDetail.java, 2019/7/25 11:21
 */
@Data
public class EbaiOrderDetail{

    private String source ;

    private EbaiShop shop ;

    private EbaiOrder order ;

    private OrderUser user ;

    /**
     * 订单商品信息数组，数组内第一个层数组为分袋，第二层数组为商品信息，见下data-products-dish(单菜品)
     */
    private List<List<OrderGoods>>    products;

    private List<OrderDiscount> discount;


}
