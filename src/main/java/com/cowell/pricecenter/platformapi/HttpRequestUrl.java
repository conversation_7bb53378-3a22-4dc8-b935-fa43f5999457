package com.cowell.pricecenter.platformapi;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-03-05 14:34
 */
public class HttpRequestUrl {

    /**
     * 批量 修改价格 接口：
     *
     * https://openapi.jddj.com/djapi/venderprice/updateStationPrice
     *
     */
    public static final String JD_UPDATE_STATION_PRICE_VENDER_URL = "https://openapi.jddj.com/%s/venderprice/updateStationPrice";
    /**
     * 根据到家商品编码和到家门店编码修改门店价格接口
     *
     * https://openapi.jddj.com/djapi/price/updateStationPrice
     *
     */
    public static final String JD_UPDATE_STATION_PRICE_URL = "https://openapi.jddj.com/%s/price/updateStationPrice";

    /**
     * 根据商家商品编码、商家门店编号修改门店价格接口
     *
     *https://openapi.jddj.com/djapi/price/changePrice
     */
    public static final String JD_CHANGE_PRICE_URL = "https://openapi.jddj.com/%s/price/changePrice";

    public static final String EB_UPDATE_PRICE_URL = "https://api-be.ele.me/";

    public static final String JD_API_EVN_VALUE_FIELD = "djapi";

    /**
     * 京东到家限流 标识
     */
    public static final String JD_JDDJRET_CODE= "10032";

    public static final String JD_SUCCESS_CODE= "0";

    /**
     * 批量 修改价格
     */
    public static final String EB_UPDATE_PRICE_BATCH = "sku.price.update.batch";
    /**
     * 单个 修改价格
     */
    public static final String EB_UPDATE_PRICE_ONE = "sku.price.update.one";

    /**
     * 京东到家限流 标识
     */
    public static final Integer EB_RESPONSE_BODY_SUCCESS_CODE= 0;
    public static final String EB_RESPONSE_BODY_LIMIT_CODE= "您的请求数已超过阈值";

    public static final Integer EB_SYNC_PRICE_GOODS_LIMIT = 95;

    public static final Integer MT_SYNC_PRICE_GOODS_LIMIT = 195;

    public static final Integer JD_SYNC_PRICE_GOODS_LIMIT = 45;



}
