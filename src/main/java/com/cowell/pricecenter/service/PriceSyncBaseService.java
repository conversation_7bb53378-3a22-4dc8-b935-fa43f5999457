package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.cowell.pricecenter.constant.PriceSyncConstant;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.response.ItemSkuVo;
import com.cowell.pricecenter.service.dto.response.PageResult;
import com.cowell.pricecenter.service.dto.response.PriceDictionaryDTO;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import com.cowell.pricecenter.service.feign.ItemCenterCpservice;
import com.cowell.pricecenter.service.feign.ItemFeignService;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.service.feign.facade.ItemSearchEngineFacadeService;
import com.cowell.pricecenter.web.rest.vo.MdmDataTransformDTO;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class PriceSyncBaseService{

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceSyncBaseService.class);

    @Autowired
    private StoreService storeService;

    @Autowired
    private PriceDictionaryService priceDictionaryService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ItemFeignService itemFeignService;

    @Autowired
    private PriceSyncConfigService priceSyncConfigService;

    @Autowired
    private ItemSearchEngineFacadeService itemSearchEngineFacadeService;

    @Autowired
    private ItemCenterCpservice itemCenterCpservice;

    @Autowired
    private IItemCenterService itemCenterService;


    private static final String PRICE_SYNC_MDM_INFO = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_SYNC_MDM_KEY;
    private static final String PRICE_PRICETYPE_INFO = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_PRICETYPE_INFO;
    private static final String PRICE_SPU_INFO = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_SPU_INFO;
    private static final String PRICE_BUSINESS_NAME_INFO = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_BUSINESS_NAME_INFO;
    private static final String PRICE_STORE_NAME_INFO = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_STORE_NAME_INFO;
    private static final String PRICE_ITEMID_INFO = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_ITEMID_INFO;
    private static final String PRICE_STORE_INFO_PREFIX = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_STORE_INFO_KEY;

    private final Cache<String, String> LOCAL_CACHE = CacheBuilder
        .newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(5, TimeUnit.MINUTES)
        .build();


    public Long queryStoreGoodsItemIdCache(Long businessId, Long storeId, String goodsNo) {
        String cacheKey = businessId + "_" + storeId + "_" + goodsNo;
        RBucket<Long> rBucket = redissonClient.getBucket(PRICE_ITEMID_INFO + cacheKey);
        if (null != rBucket && rBucket.size() != 0 && rBucket.get() != 0) {
            return rBucket.get();
        } else {
            GoodsDatailReqDTO goodsDatailReqDTO = new GoodsDatailReqDTO();
            goodsDatailReqDTO.setBusinessId(businessId);
            goodsDatailReqDTO.setGoodsNo(goodsNo);
            goodsDatailReqDTO.setItemType((byte) 0);
            goodsDatailReqDTO.setStoreId(storeId);
            goodsDatailReqDTO.setLimitSize(1);
            ItemCenterQueryConfig itemCenterQueryConfig = new ItemCenterQueryConfig(TableNameEnum.ITEM_BASE.getTableName());
            itemCenterQueryConfig.setQueryFields(Lists.newArrayList(ItemBaseFieldsEnum.ITEM_ID.getFieldName()));
            goodsDatailReqDTO.setQueryConfigs(Lists.newArrayList(itemCenterQueryConfig));
            List<ItemSkuVo> itemSkuVos = itemCenterService.queryItem(goodsDatailReqDTO);
            Long itemId = null;
            if (CollectionUtils.isNotEmpty(itemSkuVos)) {
            	itemId = itemSkuVos.get(0).getItemId();
            	//放缓存
                rBucket.set(itemId, 20, TimeUnit.MINUTES);
            }
            return itemId;
        }
    }


    public String businessChannelListCache(Long businessId) throws Exception {
//        String cacheKey = "" + businessId;
        String key = PriceSyncConstant.PRICE_BUSINESS_CHANNEL_LIST_INFO + businessId;
        return LOCAL_CACHE.get(key, () -> priceSyncConfigService.channelTypeStr(businessId));
//        RBucket<String> rBucket = redissonClient.getBucket();
//        if (null != rBucket && rBucket.size() != 0 && StringUtils.isNotEmpty(rBucket.get())) {
//            //LOGGER.info("读取到缓存中的Oms取连锁渠道配置数据：{}", priceRMapCache.get(cacheKey));
//            return rBucket.get();
//        } else {
//            String channelList = priceSyncConfigService.channelTypeStr(businessId);
//            LOGGER.info("{}调Oms取连锁渠道配置返回： {}", businessId,channelList );
//            if (StringUtils.isNotBlank(channelList)) {
//                //放缓存
//                rBucket.set(channelList, 1, TimeUnit.HOURS);
//                return channelList;
//            } else {
//                return null;
//            }
//        }
    }

    /**
     * 获取渠道名称
     * @return
     */
    public List<OrderChannelTypeVO> channelListCache() {

        List<OrderChannelTypeVO> orderChannelTypeVOList = Lists.newArrayList();

        for (OrderChannelEnum channelEnum: OrderChannelEnum.values()){


            if (channelEnum.getPriceSyncChannel() == 0){
                continue;
            }

            OrderChannelTypeVO orderChannelTypeVO = new OrderChannelTypeVO();

            orderChannelTypeVO.setChannelType(channelEnum.getType()+"");
            orderChannelTypeVO.setChannelCode(channelEnum.getPlatform());
            orderChannelTypeVO.setChannelName(channelEnum.getDesc());
            orderChannelTypeVO.setShowChannel(channelEnum.getShowChannel());
            orderChannelTypeVOList.add(orderChannelTypeVO);
        }
        return orderChannelTypeVOList;
    }

    public Map<String, PriceType> getPriceTypeMapsCache() {
        String cacheKey = "_1";
        Map<String, PriceType> priceTypeMap;
        RBucket<Map<String, PriceType>> rBucket = null;
//            = redissonClient.getBucket(PRICE_PRICETYPE_INFO + cacheKey);
        if (null != rBucket && null != (priceTypeMap = rBucket.get())) {
            //LOGGER.info("读取到缓存中的价格类型数据：{}", priceRMapCache.get(cacheKey));
            return priceTypeMap;
        } else {
            List<PriceDictionaryDTO> dictionaryDTOS = priceDictionaryService.getChildrenByCode(DictCodeEnum.PRICE_TYPE.getCode());
            if (CollectionUtils.isEmpty(dictionaryDTOS)) {
                return null;
            }
            priceTypeMap = new HashMap<>(16);
            for (PriceDictionaryDTO dictionaryDTO : dictionaryDTOS) {
                priceTypeMap.put(dictionaryDTO.getDictCode(), PriceDictConverter.dictToPriceType(dictionaryDTO));
            }
//            LOGGER.info("从库中读取到价格类型数据：{}", priceTypeMap);
            //放缓存
//            rBucket.set(priceTypeMap, 1, TimeUnit.HOURS);
            return priceTypeMap;
        }
    }

    public SpuNewVo getNewSpuListCache(long businessId, long goodsNo) {
        String cacheKey = businessId + "_" + goodsNo;

        RBucket<SpuNewVo> rBucket = redissonClient.getBucket(PRICE_SPU_INFO + cacheKey);
        if (null != rBucket && rBucket.size() != 0 && null != rBucket.get()) {
            //LOGGER.info("{}根据goodsNo取sku信息{}读取到缓存中的数据：{}", businessId, goodsNo, priceRMapCache.get(cacheKey));
            return rBucket.get();
        } else {
            List<Long> goodsNos = new ArrayList<>();
            goodsNos.add(goodsNo);
            //LOGGER.info("根据goodsNo取sku信息{}调search入参： {}", businessId, goodsNos);
//            ResponseEntity<List<SpuNewVo>> rep = searchService.getNewSpuList(businessId, goodsNos);
            PageResult<SpuNewVo> rep = itemSearchEngineFacadeService.getNewSpuList(businessId, goodsNos);
            LOGGER.info("根据goodsNo取sku信息{}调{}search返回： {}", businessId, goodsNos, rep);
//            if (null == rep || CollectionUtils.isEmpty(rep.getBody()) || null == rep.getBody().get(0)) {
//                LOGGER.info("根据goodsNo取sku信息{}调search返回空", goodsNos);
//                return null;
//            }
            if (null == rep || CollectionUtils.isEmpty(rep.getRows()) || null == rep.getRows().get(0)) {
                LOGGER.info("根据goodsNo取sku信息{}调search返回空", goodsNos);
                return null;
            }
            SpuNewVo spuNewVo = rep.getRows().get(0);
            //放缓存
            rBucket.set(spuNewVo, 1, TimeUnit.DAYS);
            return spuNewVo;
        }
    }

    public SpuNewVo getSpuInfo(long businessId, String goodsNo) {
        String cacheKey = businessId + "_" + goodsNo;

        SpuNewVo spuNewVo = new SpuNewVo();
        RBucket<SpuNewVo> rBucket = redissonClient.getBucket(PRICE_SPU_INFO + cacheKey);
        if (null != rBucket && rBucket.size() != 0 && null != rBucket.get()) {
            return rBucket.get();
        } else {
            List<Long> goodsNos = new ArrayList<>();
            goodsNos.add(Long.parseLong(goodsNo));
//            ResponseEntity<List<SpuNewVo>> rep = searchService.getNewSpuList(businessId, goodsNos);
            PageResult<SpuNewVo> rep = itemSearchEngineFacadeService.getNewSpuList(businessId, goodsNos);
            LOGGER.info("根据goodsNo取sku信息{}调{}search返回： {}", businessId, goodsNos, rep);
//            if (null == rep || CollectionUtils.isEmpty(rep.getBody()) || null == rep.getBody().get(0)) {
//                LOGGER.info("根据goodsNo取sku信息{}调search返回空", goodsNos);
//                return spuNewVo;
//            }
            if (null == rep || CollectionUtils.isEmpty(rep.getRows()) || null == rep.getRows().get(0)) {
                LOGGER.info("根据goodsNo取sku信息{}调search返回空", goodsNos);
                return spuNewVo;
            }
            spuNewVo = rep.getRows().get(0);
            //放缓存
            rBucket.set(spuNewVo, 1, TimeUnit.DAYS);
            return spuNewVo;
        }
    }


    public MdmDataTransformDTO getMdmCache(String busNo, String compId) throws Exception {
        String cacheKey ="_" + busNo;

        RBucket<MdmDataTransformDTO> rBucket = redissonClient.getBucket(PRICE_SYNC_MDM_INFO + cacheKey);
        //LOGGER.info("根据mdm码转成连锁信息{} 读取到缓存priceRMapCache：{}", busNo, priceRMapCache);
        MdmDataTransformDTO mdmDataTransformDTO;

        if (null != rBucket && (mdmDataTransformDTO = rBucket.get()) != null) {
            //LOGGER.info("根据mdm码转成连锁信息{} 读取到缓存中的数据：{}", busNo, priceRMapCache.get(cacheKey));
            return mdmDataTransformDTO;
        } else {
            List<String> storeNos = new ArrayList<>();
            storeNos.add(busNo);
            mdmDataTransformDTO = new MdmDataTransformDTO();
            mdmDataTransformDTO.setTransFormType(MdmDataEnum.TransformTypeEnum.MDM.getCode());
            mdmDataTransformDTO.setDataType(MdmDataEnum.TransformDataTypeEnum.STORE.getCode());
            //mdmDataTransformDTO.setComId(compId);
            mdmDataTransformDTO.setStoreNos(storeNos);

            LOGGER.info("根据mdm码转成连锁信息{} 调store入参：{}", busNo, mdmDataTransformDTO);
            mdmDataTransformDTO = storeService.transformMdmData(mdmDataTransformDTO);
            LOGGER.info("根据mdm码转成连锁信息{} 调store返回: {}", busNo, mdmDataTransformDTO);
            if (null == mdmDataTransformDTO || CollectionUtils.isEmpty(mdmDataTransformDTO.getStoreIds())) {
                LOGGER.info("根据mdm码转成连锁信息{} 调store返回啥也没取到，跳过当前数据", busNo);
                return null;
            }
            //放缓存
            rBucket.set(mdmDataTransformDTO, 1, TimeUnit.DAYS);
            return mdmDataTransformDTO;
        }

    }


    /**
     * 获取连锁名称
     *
     * @param businessId
     * @return
     */
    public String getBusinessName(Long businessId) {
        //取连锁名称
        String businessName = "";
        RBucket<String> rBucket = redissonClient.getBucket(PRICE_BUSINESS_NAME_INFO + businessId);
        //LOGGER.info("Redis中获取到的连锁名称信息:{} ",rBucket.get());
        if (StringUtils.isNotBlank(businessName = rBucket.get())) {
            return businessName;
        }

        try {
            //获取连锁名
            BussinessInfoDTO bussinessInfoDTO = storeService.getBusinessInfoById(businessId);
            if (bussinessInfoDTO != null && bussinessInfoDTO.getBusinessName() != null) {

                businessName = StringUtils.isBlank(bussinessInfoDTO.getShortName()) ? bussinessInfoDTO.getBusinessName() : bussinessInfoDTO.getShortName();

                rBucket.set(businessName, 1, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            LOGGER.warn("调用storeService.getBusinessInfoById异常 连锁:{} :", businessId, e);
        }

        return businessName;
    }

    /**
     * 获取门店名称
     *
     * @param storeId
     * @return
     */
    public String getStoreName(Long storeId) {
        //获取门店名称
        String storeName = "";
        RBucket<String> rBucket = redissonClient.getBucket(PRICE_STORE_NAME_INFO + storeId);
        //LOGGER.info("Redis中获取到的门店名称信息:{} ",rBucket.get());
        if (StringUtils.isNotBlank(storeName = rBucket.get())) {
            return storeName;
        }
        try {
            ResponseEntity<StoreBriefDTO> responseEntity = storeService.getStoreById(storeId);
            if (responseEntity.getStatusCodeValue() == HttpStatus.OK.value() && responseEntity.getBody() != null) {
                StoreBriefDTO storeBriefDTO = responseEntity.getBody();
                storeName = StringUtils.isBlank(storeBriefDTO.getShortName()) ? storeBriefDTO.getStoreName() : storeBriefDTO.getShortName();


                rBucket.set(storeName, 1, TimeUnit.HOURS);
            }
        } catch (Exception e) {
            LOGGER.error("调用storeService.getStoreBriefInfo异常 门店:{} :", storeId, e);
        }


        return storeName;
    }

    /**
     * 通过店铺id获取店铺详情
     *
     * @param storeId 店铺id
     * @return CrmStoreDTO
     */
    public StoreBriefDTO getStoreDTOByStoreId(Long storeId) {
        StoreBriefDTO crmStoreDTO = null;
        try {
            if (Objects.nonNull(storeId)) {
                RBucket<String> rBucket = redissonClient.getBucket(PRICE_STORE_INFO_PREFIX + storeId + "");
                if (Objects.isNull(rBucket) || StringUtils.isBlank(rBucket.get())) {
                    crmStoreDTO = storeService.getStoreById(storeId).getBody();
                    LOGGER.info("PriceSyncBaseService|getStoreDTOByStoreId|获取店铺信息|crmStoreDTO:{}", crmStoreDTO);
                    rBucket.set(JSON.toJSONString(crmStoreDTO));
                    rBucket.expire(7, TimeUnit.DAYS);
                } else {
                    crmStoreDTO = JSON.parseObject(rBucket.get(), StoreBriefDTO.class);
                }

                LOGGER.info("PriceSyncBaseService|getStoreDTOByStoreId|查询店铺详情|crmStoreDTO={}", crmStoreDTO);
            }
        } catch (Exception e) {
            LOGGER.error("PriceSyncBaseService|getStoreDTOByStoreId|通过storeId查询店铺详情异常!|storeId={}", storeId, e);
        }
        return crmStoreDTO;
    }

}
