package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceManageControlOrder;
import com.cowell.pricecenter.entity.PriceManageControlOrderDetail;
import com.cowell.pricecenter.entity.PriceManageControlOrderDetailExample;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderManagePriceDTO;
import com.cowell.pricecenter.service.dto.ControlOrderDetailQueryDTO;
import com.cowell.pricecenter.service.dto.PriceOrderPriceTypeAndChannelChange;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/3/17 11:31
 */

public interface PriceManageControlOrderDetailReadService {


    long countByExample(PriceManageControlOrderDetailExample example);

    List<PriceManageControlOrderDetail> selectByExample(PriceManageControlOrderDetailExample example);

    PriceManageControlOrderDetail selectByPrimaryKey(Long id);

    /**
     * 通过查询条件查询管控单明细
     * @param queryDTO 查询参数
     * @return List<PriceManageControlOrderDetail>
     */
    List<PriceManageControlOrderDetail> getControlOrderDetailList(ControlOrderDetailQueryDTO queryDTO);

    /**
     * 查询管控单详情总数
     * @param queryDTO 查询参数
     * @return long 返回总数
     */
    long getControlOrderDetailCount(ControlOrderDetailQueryDTO queryDTO);

    /**
     * 根据商品编码，组织机构ID，价格类型Code，渠道ID获取最新一条生效的管控单明细数据
     * @param goodsNo
     * @param orgIdList
     * @param priceTypeCode
     * @param channelId
     * @param effectTime
     * @return
     */
    Optional<AdjustPriceOrderManagePriceDTO> getNewestAdjustPriceOrderManagePriceDTO(String goodsNo, List<Long> orgIdList,
                                                                                     String priceTypeCode, Integer channelId, Date effectTime);

    /**
     * 重新编辑管控单明细，因为价格类型和渠道有可能改变
     * @param priceManageControlEditOrder
     * @param orderPriceTypeAndChannelChange
     * @param userDTO
     * @param operateTimeDate
     */
    void resetOrderDetailsByChannelsAndPriceTypes(PriceManageControlOrder priceManageControlEditOrder, PriceOrderPriceTypeAndChannelChange orderPriceTypeAndChannelChange, TokenUserDTO userDTO, Date operateTimeDate);
}



