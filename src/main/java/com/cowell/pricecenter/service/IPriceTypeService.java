package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.service.dto.request.NewPriceTypeParam;
import com.cowell.pricecenter.service.dto.request.PriceTypeParam;
import com.cowell.pricecenter.service.dto.response.PriceTypeDTO;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import java.util.Map;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;


public interface IPriceTypeService {

    boolean newPriceType(NewPriceTypeParam param);

    CommonResponse createPriceType(NewPriceTypeParam newPriceTypeParam);

    CommonResponse updatePriceType(PriceTypeParam newPriceTypeParam);

    Page<PriceTypeDTO> getPriceTypeList(Byte isUse, String name, Pageable pageable);

    PriceTypeDTO getPriceType(Long id);

    /**
     * 根据Code获取价格类型对象
     * @param priceTypeCode
     * @return
     */
    Optional<PriceType> getPriceTypeByCode(String priceTypeCode);

    /**
     * 根据Codes获取价格类型列表
     * @param priceTypeCodes
     * @return
     */
    List<PriceType> getPriceTypeListByCodes(List<String> priceTypeCodes);

    List<PriceType> getAllPriceTypeList();

    List<PriceTypeDTO> getAllPriceType();

    void updatePriceTypeEditable(String code, Integer isEdit);

    /**
     * 获取所有有效的价格类型Map
     * @return
     */
    Map<String, PriceType> getAllPriceTypeMap();

    /**
     * 获取指定的价格类型Map
     * @return
     */
    Map<String, PriceType> getPriceTypeMap(List<String> priceTypeCodeList);

    /**
     * 获取用户的价格类型Map
     *
     * @return
     */
    Map<String, PriceType> getUserPriceTypeMap(Long userId);
}
