package com.cowell.pricecenter.service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import com.cowell.pricecenter.service.dto.ImportErrorDataDTO;
import com.cowell.pricecenter.service.dto.response.AdjustImportResultDTO;
import com.cowell.pricecenter.service.dto.response.ExportFileCubeVO;

public interface ImportDataResultService {

	/**
	 * 
	 * @Title: sendImportErrorData   
	 * @Description: 保存导入异常数据
	 * @param: @param importErrorData      
	 * @return: void      
	 * @throws
	 */
	void sendImportErrorData(ImportErrorDataDTO importErrorData);
	/**
	 * 
	 * @Title: sendImportResult   
	 * @Description: 发送导入结果   
	 * @param: @param redisKey
	 * @param: @param total
	 * @param: @param successCount
	 * @param: @param failCount
	 * @param: @param status      
	 * @return: void      
	 * @throws
	 */
	void sendImportResult(String redisKey, int total,int successCount,int failCount,int status);
	/**
	 * 
	 * @Title: getAdjustImportResult   
	 * @Description: 查询导入结果   
	 * @param: @param redisKey
	 * @param: @return      
	 * @return: AdjustImportResultDTO      
	 * @throws
	 */
	AdjustImportResultDTO getAdjustImportResult(String redisKey);
	/**
	 * 
	 * @Title: selectErrorData   
	 * @Description: 查询导入失败数据   
	 * @param: @param dataKey
	 * @param: @param structureKey
	 * @param: @param page
	 * @param: @param pageSize
	 * @param: @return      
	 * @return: ExportFileCubeVO<Map<String,Object>>      
	 * @throws
	 */
	ExportFileCubeVO<Map<String, Object>> selectErrorData(String dataKey,String structureKey,Integer page,Integer pageSize);
}
