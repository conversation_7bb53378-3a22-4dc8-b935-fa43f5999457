package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.PriceCopyParam;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;

/**
 * @Auther: 张晨
 * @Date: 2018/10/29 17:17
 * @Description:
 */
public interface IPriceCopyService {
    /**
     * 根据连锁ID获取设置过价格的所有门店
     *
     * @param businessId 连锁ID
     * @return 门店ID和名称集合
     */
    CommonResponse getPricedStoreByBusinessId(Long businessId);

    /**
     * 根据连锁ID获取未设置过价格的门店
     *
     * @param orgId 机构ID
     * @return 门店ID和名称集合
     */
    CommonResponse getNonPricedStoreByBusinessId(Long orgId);

    /**
     * 价格复制
     * @param priceCopyParam
     * @return
     */
    CommonResponse priceCopy(PriceCopyParam priceCopyParam);

    /**
     * 查询价格复制历史
     * @param priceCopyParam
     * @return
     */
    CommonResponse queryPriceCopyInfo(PriceCopyParam priceCopyParam);


}
