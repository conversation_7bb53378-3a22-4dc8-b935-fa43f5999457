package com.cowell.pricecenter.service;

import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.enums.CommonEnums;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.service.dto.request.MDMStoreGroupDTO;
import com.cowell.pricecenter.service.feign.ErpBizSupportService;
import com.cowell.pricecenter.web.rest.errors.BusinessException;
import com.cowell.pricecenter.web.rest.util.Assert;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class PriceMdmDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceMdmDataService.class);

    public static final String SYS_USER_MDM_SENDER = "MDM_SENDER";

    public static final Long SYS_USER_MDM_SENDER_USER_ID = -99L;

    // 接收MDM数据 保存到标签中台 门店组 使用
    public static final String MDM_RECEIVED_STORE_GROUP_TAG_ID = "MDM_RECEIVED_STORE_GROUP_TAG_ID";


    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IPermissionExtService permissionExtService;

    @Autowired
    private ErpBizSupportService erpBizSupportService;

    /**
     * 根据实体ID列表获取实体列表
     * @param EntityIdList 实体ID列表
     * @return CrmStoreDTO
     */
    public List<String> getEntityListByTypeAndEntityIdList(Integer entityType,List<String> EntityIdList) {
        LOGGER.info("getEntityListByTypeAndEntityIdList|entityType={} |EntityIdList={}", entityType, EntityIdList);
        Assert.isTrue(CommonEnums.MdmDataBizEntryTypeEnum.containsEnum(entityType), "entityType参数错误");
        if(CollectionUtils.isEmpty(EntityIdList)){
            throw new BusinessException(ReturnCodeEnum.ERROR_SIZE_TOO_LONG);
        }
        MDMStoreGroupDTO param = new MDMStoreGroupDTO();
        param.setEntityIdList(EntityIdList);
        param.setEntityType(entityType);
        try {
            return erpBizSupportService.getEntityListByTypeAndEntityIdList(param).getBody();
        }catch (Exception e){
           LOGGER.error("getEntityListByTypeAndEntityIdList| 数据查询失败", e);
        }
        return Lists.newArrayList();
    }


    /**
     * 根据实体ID列表获取实体引用列表
     * @param EntityIdList 实体ID列表
     * @return CrmStoreDTO
     */
    public Map<String, List<String>> getEntityMapByTypeAndEntityIdList(Integer entityType, List<String> EntityIdList) {
        LOGGER.info("getEntityMapByTypeAndEntityIdList|entityType={} |EntityIdList={}", entityType, EntityIdList);
        Assert.isTrue(CommonEnums.MdmDataBizEntryTypeEnum.containsEnum(entityType), "entityType参数错误");
        if(CollectionUtils.isEmpty(EntityIdList) || EntityIdList.size() > PriceConstant.BATCH_COMMIT_SIZE){
            throw new BusinessException(ReturnCodeEnum.ERROR_SIZE_TOO_LONG);
        }
        MDMStoreGroupDTO param = new MDMStoreGroupDTO();
        param.setEntityIdList(EntityIdList);
        param.setEntityType(entityType);
        try {
            return erpBizSupportService.getEntityMapByTypeAndEntityIdList(param).getBody();
        }catch (Exception e){
            LOGGER.error("getEntityListByTypeAndEntityIdList| 数据查询失败", e);
        }
        return new HashMap<>();
    }




}
