package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AlertContent;

/**
 * <AUTHOR>
 * @date 2021/12/17 14:12
 */
public interface IAlertService {
    /**
     * 告警
     * @param alertContent AlertContent
     */
    void alert(AlertContent alertContent);

    /**
     * 告警
     * @param subject 主题
     * @param message 消息
     */
    void alert(String subject, String message);

    /**
     * 告警
     * @param subject 主题
     * @param message 消息
     * @param throwable 异常信息
     */
    void alert(String subject, String message, Throwable throwable);
}
