package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceBusinessDetailHistory;
import com.cowell.pricecenter.entity.PriceBusinessDetailHistoryExample;
import com.cowell.pricecenter.service.dto.request.AdjustPriceOrderDetailEditV2;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:25
 */
public interface PriceBusinessDetailHistoryService {


    long countByExample(PriceBusinessDetailHistoryExample example);

    int deleteByExample(PriceBusinessDetailHistoryExample example);

    int deleteByPrimaryKey(String id);

    int insert(PriceBusinessDetailHistory record);

    int insertSelective(PriceBusinessDetailHistory record);

    List<PriceBusinessDetailHistory> selectByExample(PriceBusinessDetailHistoryExample example);

    PriceBusinessDetailHistory selectByPrimaryKey(String id);

    int updateByExampleSelective(PriceBusinessDetailHistory record, PriceBusinessDetailHistoryExample example);

    int updateByExample(PriceBusinessDetailHistory record, PriceBusinessDetailHistoryExample example);

    int updateByPrimaryKeySelective(PriceBusinessDetailHistory record);

    int updateByPrimaryKey(PriceBusinessDetailHistory record);
    
    int batchInsert(List<PriceBusinessDetailHistory> list);
    
    void deleteHistoryRefPrice(String adjustCode,String goodsNo, Integer channelId,Long businessId,List<String> priceTypeCodes);

}

