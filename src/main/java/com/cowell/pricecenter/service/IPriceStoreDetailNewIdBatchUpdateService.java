package com.cowell.pricecenter.service;

/**
 * Price Store Detail批量更新new_id字段服务接口
 * 支持512张表(128*4个数据源)的批量更新
 *
 * <AUTHOR>
 */
public interface IPriceStoreDetailNewIdBatchUpdateService {

    /**
     * 批量更新所有表的new_id字段
     */
    void batchUpdateAllTables(int dsIndex);

    /**
     * 批量更新指定表的new_id字段
     *
     * @param dsIndex 数据源索引(0-3)
     * @param tableName 表名
     * @param storeId 门店ID，为null时更新所有门店
     * @return 更新的记录数
     */
    int batchUpdateTable(int dsIndex, String tableName, String storeId);

    /**
     * 显示指定表的更新进度
     *
     * @param dsIndex 数据源索引
     * @param tableName 表名
     * @param storeId 门店ID，为null时显示所有门店进度
     */
    void showProgress(int dsIndex, String tableName, String storeId);

    /**
     * 停止当前正在执行的批量更新任务
     */
    void stopCurrentTask();

    /**
     * 检查任务是否正在运行
     *
     * @return true表示任务正在运行
     */
    boolean isTaskRunning();
}
