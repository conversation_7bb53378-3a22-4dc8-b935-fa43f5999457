package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceManageControlOrder;
import com.cowell.pricecenter.entity.PriceManageControlOrderExample;
import com.cowell.pricecenter.service.dto.ControlOrderLimitDTO;
import com.cowell.pricecenter.service.dto.ControlOrderQueryDTO;
import com.cowell.pricecenter.service.dto.PriceControlOrderQueryDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.AdjustPriceOrderV2VO;
import com.cowell.pricecenter.service.dto.response.ExportFileCubeVO;
import com.cowell.pricecenter.service.dto.response.NoticeReminder;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.controlOrder.ControlOrderNoticeListVO;
import com.cowell.pricecenter.service.dto.response.controlOrder.PriceManageControlOrderVO;
import com.cowell.pricecenter.service.dto.response.controlOrder.PriceOperatorLogDTO;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public interface PriceManageControlOrderReadService {

    long countByExample(PriceManageControlOrderExample example);

    List<PriceManageControlOrder> selectByExample(PriceManageControlOrderExample example);

    PriceManageControlOrder selectByPrimaryKey(Long id);

    /**
     * 通过条件查询管控单列表
     * @param queryDTO
     * @return
     */
    List<PriceManageControlOrder> getControlOrderListByParam(ControlOrderQueryDTO queryDTO);

    /**
     * 通过查询条件查询管控单
     * @param queryDTO 查询条件
     * @return List<PriceManageControlOrder>
     */
    List<PriceManageControlOrder> getPriceManageControlOrderList(PriceControlOrderQueryDTO queryDTO);
    
    LinkedHashMap<String, String> getControlOrderDetailDownloadFieldMap(String adjustPriceTypeCodes);

    PageResult controlOrderList(ControlOrderListParam param, Integer controlOrderType);

    PriceManageControlOrderVO controlOrderBaseInfo(Long controlOrderId);

    PageResult controlOrderGoodsList(Long controlOrderId, String goodsNo, Integer page, Integer pageSize);

    /**
     * 获取管控单参考价格范围
     * @param queryParamList
     * @return
     */
    List<ControlOrderLimitDTO> findCurrentControlLimitOfGoodsNo(List<ControlOrderQueryParam> queryParamList);

    /**
     * 渠道列表
     * @return
     */
    List<OptionDto> getPriceChannelList();

    /**
     * 价格类型列表
     * @return
     */
    List<OptionDto> getPriceTypeList(ControlOrderStatusParam param);

    /**
     * 权限商品范围
     * @return
     */
    List<OptionDto> getPermGoodsList();

    /**
     * 价格管控通知列表查询
     * @param param
     * @return
     */
    PageResult<ControlOrderNoticeListVO> controlNoticeList(ControlOrderNoticeParam param);

    /**
     * 管控通知相关列表统一接口
     * @param param
     * @return
     */
    PageResult<ControlOrderNoticeListVO> controlNoticeUnifyList(ControlOrderNoticeParam param);

    /**
     * 查询生成的调价单 信息
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<AdjustPriceOrderV2VO> adjustPriceOrderList(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO);

    /**
     * 获取连锁企业信息通过管控单编码
     * @param param
     * @return
     */
    List<OptionDto> getBusinessInfoByCode(ControlOrderStatusParam param);

    /**
     * 获取毛利区间基准门店信息通过管控单编码
     * @param param
     * @return
     */
    List<OptionDto> getStoreInfoByCode(ControlOrderStatusParam param);

    /**
     * 管控单明细带分页
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<Map<String, Object>> listControlOrderDetailsPage(ControlOrderDetailListV2Param param, TokenUserDTO userDTO);

    /**
     * 管控单商品明细导出
     * @param controlOrderId
     */
    ExportFileCubeVO<Map<String, Object>> exportControlOrderDetailsFile(Long controlOrderId, Integer page, Integer pageSize);

    /**
     * 异步导出管控单通知文件到下载中心
     * @param controlOrderNoticeIdList
=     */
    ExportFileCubeVO<Map<String, Object>> asyncExportControlOrderNoticeFile(List<Long> controlOrderNoticeIdList, Integer page, Integer pageSize);

    /**
     * 门店商品列表-APP
     * @param param
     * @return
     */
    PageResult<ControlOrderNoticeListVO> controlNoticeAppList(ControlOrderNoticeParam param);

    /**
     * 管控通知商品列表
     * @param param
     * @return
     */
    PageResult<ControlOrderNoticeListVO> notControlNoticeGoodsList(ControlOrderNoticeParam param);

    /**
     * 操作日志跟踪
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<PriceOperatorLogDTO> operatorLogList(PriceOperatorLogParam param, TokenUserDTO userDTO);

    /**
     * 消息通知
     * @return
     */
    Boolean pushCobtrolMessage();

    /**
     * 待办事项
     * @return
     */
    Boolean pushBackLog();

    /**
     * 获取所有价格列表
     * @return
     */
    List<OptionDto> getAllPriceTypeList();

    /**
     * 获取所有渠道
     * @return
     */
    List<OptionDto> getAllPriceChannelList();

    /**
     * 统一下拉框接口
     * @param param
     * @return
     */
    List<OptionDto> controlSelectUnifyList(ControlOrderStatusParam param);

    /**
     * 获取提醒
     * @return
     */
    NoticeReminder getNoticeReminder();
    
    /**
     * 调价单类型列表
     * @return
     */
    List<OptionDto> getAdjustTypeList();
    
    PageResult controlOrderList(ControlOrderListParam param);
}
