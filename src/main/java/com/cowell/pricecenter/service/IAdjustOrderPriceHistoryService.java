package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.AdjustOrderPriceHistoryExportParam;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.AdjustOrderPriceHistoryDTO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;

/**
 * @program: pricecenter
 * @description: 调价单调价历史服务接口
 * @author: jmlu
 * @create: 2022-09-19 17:07
 **/

public interface IAdjustOrderPriceHistoryService {

    /**
     * 缓存调价单调价历史
     * @param param
     */
    void initAdjustOrderPriceHistoryCache(AdjustOrderPriceHistoryExportParam param, TokenUserDTO userDTO);

    /**
     * 从redis缓存中取数据
     * @param searchCode NOT NULL
     * @param page >= 1
     * @return
     */
    PageResult<AdjustOrderPriceHistoryDTO> listAdjustOrderPriceHistoryFromCache(String searchCode, Integer page);
    
    /**
     * 价格调整记录汇总
     * @param startTimeStr
     * @param endTimeStr
     */
    void priceHistorySummaryData(String startTimeStr,String endTimeStr);

}
