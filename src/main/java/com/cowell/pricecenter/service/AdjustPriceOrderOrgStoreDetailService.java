/**
* @mbg.generated
* generator on Wed Apr 20 17:49:51 CST 2022
*/
package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetailExample;
import com.cowell.pricecenter.entity.PricePushTask;
import com.cowell.pricecenter.enums.AdjustPriceOrderOrgStoreDetailEnum;
import com.cowell.pricecenter.param.OrgStoreDetailSplitDTO;
import com.cowell.pricecenter.service.dto.request.AdjustPriceBusinessIdPriceParam;
import com.cowell.pricecenter.service.dto.response.AdjustPriceBusinessIdPriceDTO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.query.AdjustPriceOrderOrgStoreDetailQuery;
import com.cowell.pricecenter.web.rest.vo.PricePushResultVO;

import java.util.List;

public interface AdjustPriceOrderOrgStoreDetailService {
    /**
     * countByExample
     *
     * @param example example
     * @return long long
     */
    long countByExample(AdjustPriceOrderOrgStoreDetailExample example);

    /**
     * deleteByExample
     *
     * @param example example
     * @return int int
     */
    int deleteByExample(AdjustPriceOrderOrgStoreDetailExample example);

    /**
     * deleteByPrimaryKey
     *
     * @param id id
     * @return int int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert
     *
     * @param row row
     * @return int int
     */
    int insert(AdjustPriceOrderOrgStoreDetail row);

    /**
     * insertSelective
     *
     * @param row row
     * @return int int
     */
    int insertSelective(AdjustPriceOrderOrgStoreDetail row);

    /**
     * selectByExample
     *
     * @param example example
     * @return List<AdjustPriceOrderOrgStoreDetail> List<AdjustPriceOrderOrgStoreDetail>
     */
    List<AdjustPriceOrderOrgStoreDetail> selectByExample(AdjustPriceOrderOrgStoreDetailExample example);


    /**
     * selectByPrimaryKey
     *
     * @param id id
     * @return AdjustPriceOrderOrgStoreDetail AdjustPriceOrderOrgStoreDetail
     */
    AdjustPriceOrderOrgStoreDetail selectByPrimaryKey(Long id);

    /**
     * updateByExampleSelective
     *
     * @param row     row
     * @param example example
     * @return int int
     */
    int updateByExampleSelective(AdjustPriceOrderOrgStoreDetail row, AdjustPriceOrderOrgStoreDetailExample example);

    /**
     * updateByExample
     *
     * @param row     row
     * @param example example
     * @return int int
     */
    int updateByExample(AdjustPriceOrderOrgStoreDetail row, AdjustPriceOrderOrgStoreDetailExample example);

    /**
     * updateByPrimaryKeySelective
     *
     * @param row row
     * @return int int
     */
    int updateByPrimaryKeySelective(AdjustPriceOrderOrgStoreDetail row);

    /**
     * updateByPrimaryKey
     *
     * @param row row
     * @return int int
     */
    int updateByPrimaryKey(AdjustPriceOrderOrgStoreDetail row);

    /**
     * 查询
     *
     * @param query AdjustPriceOrderOrgStoreDetailQuery
     * @return List<AdjustPriceOrderOrgStoreDetail>
     */
    List<AdjustPriceOrderOrgStoreDetail> query(AdjustPriceOrderOrgStoreDetailQuery query);

    /**
     * 分页查询
     *
     * @param query    AdjustPriceOrderOrgStoreDetailQuery
     * @param page     当前页
     * @param pageSize 每页数量
     * @return List<AdjustPriceOrderOrgStoreDetail>
     */
    List<AdjustPriceOrderOrgStoreDetail> page(AdjustPriceOrderOrgStoreDetailQuery query, int page, int pageSize);

    /**
     * 分页查询门店数据
     * @param query
     * @param page
     * @param pageSize
     * @return
     */
    List<AdjustPriceOrderOrgStoreDetail> distinctStorePage(AdjustPriceOrderOrgStoreDetailQuery query, int page, int pageSize);

    /**
     * 获取调价级别
     *
     * @param adjustCode 调价单号
     * @param storeId    门店ID
     * @param goodsNo    商品编码
     * @param priceTypeCode    价格类型
     * @param channelId    渠道
     * @param skuId    skuId
     * @return 调价级别，未查询到数据时返回-1
     */
    int getAdjustPriceLevel(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId);

    /**
     * 获取调价级别详情
     *
     * @param adjustCode 调价单号
     * @param storeId    门店ID
     * @param goodsNo    商品编码
     * @param priceTypeCode    价格类型
     * @param channelId    渠道
     * @param skuId    skuId
     * @return AdjustPriceOrderOrgStoreDetail
     */
    AdjustPriceOrderOrgStoreDetail getAdjustPriceLevelDetail(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId);

    /**
     * 先根据调价单号删除所有调价单组织门店所有数据
     *
     * @param adjustCode
     */
    void deleteByAdjustCode(String adjustCode);

    /**
     * 批量插入调价单组织门店数据
     *
     * @param orderOrgStoreDetailList
     */
    void batchInsert(List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList);

    /**
     * 更新状态
     * @param adjustCode 调价单号
     * @param storeId 门店ID
     * @param goodsNo 商品编码
     * @param priceTypeCode 价格类型
     * @param channelId 渠道
     * @param status 状态枚举 {@link AdjustPriceOrderOrgStoreDetailEnum.Status}
     * @param result 执行结果
     * @return int
     */
    int updateStatus(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId,
                     AdjustPriceOrderOrgStoreDetailEnum.Status status, String result);

    /**
     * 记录校验结果
     * @param adjustCode 调价单号
     * @param storeId 门店ID
     * @param goodsNo 商品编码
     * @param priceTypeCode 价格类型
     * @param channelId 渠道
     * @param result 执行结果
     * @return int
     */
    int recordResult(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId, String result);
    /**
     *
     * @Title: supplementExtendData
     * @Description: 真实补充extend相关数据
     * @param: @param adjustCode
     * @return: void
     * @throws
     */
    void supplementExtendData(OrgStoreDetailSplitDTO splitParam);

    /**
     *
     * @Title: updateOrgStoreExtend
     * @Description: 修改adjust_price_order_org_store_detail扩展字段
     * @param: @param taskResult
     * @return: void
     * @throws
     */
    int updateOrgStoreExtend(PricePushResultVO taskResult);



    /**
     * 根据调价单号和businessId列表分页获取数据
     * @param adjustCode
     * @param businessIds
     * @param page
     * @param pageSize
     * @return
     */
    List<AdjustPriceOrderOrgStoreDetail> listByAdjustCodeAndBusinessIds(String adjustCode, List<Long> businessIds, Integer page, Integer pageSize);

    /**
     *
     * @param adjustCode
     * @param storeOrgIds
     * @param goodsnoList
     * @param page
     * @param pageSize
     * @return
     */
    List<AdjustPriceOrderOrgStoreDetail> listByAdjustCodeAndStoreOrgIdsAndGoodnoes(String adjustCode, List<Long> storeOrgIds,
        List<String> goodsnoList, int page, int pageSize);


    /**
    *
    * @Title: supplementSplitExtendData
    * @Description: 拆分补充extend相关数据
    * @param: @param adjustCode
    * @return: void
    * @throws
    */
   void supplementSplitExtendData(String adjustCode);
   
   /**
    * 
    * @param query
    * @param page
    * @param pageSize
    * @return
    */
   PageResult<AdjustPriceBusinessIdPriceDTO> listAdjustBusinessGoods(AdjustPriceBusinessIdPriceParam query);
}

