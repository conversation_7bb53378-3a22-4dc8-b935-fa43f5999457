package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.ControlOrderEditParam;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;

/**
 * @program: pricecenter
 * @description: 管控单表的基础服务接口
 * @author: jmlu
 * @create: 2022-08-04 11:03
 **/

public interface IPriceManageControlOrderBaseService {

    /**
     * 新增管控单类型校验
     * @param param
     */
    void checkAddPriceManageControlOrder(ControlOrderEditParam param);

    /**
     * 校验名称和原因
     * @param controlOrderName
     * @param controlNoExecReason
     */
    void checkControlOrderNameAndReason(String controlOrderName, String controlNoExecReason);

    /**
     * 逻辑性删除管控单ID，只更新数据可用状态
     * @param id
     * @param userDTO
     */
    void deleteOrderLogicById(Long id, TokenUserDTO userDTO);
}
