package com.cowell.pricecenter.service;

import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.pricecenter.service.dto.request.AdjustPriceGroupParam;
import com.cowell.pricecenter.service.dto.request.PriceGroupParam;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import java.util.List;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Auther: 张晨
 * @Date: 2018/10/29 17:17
 * @Description:
 */
public interface CommonService{

    List<OrgLevelVO> getDistinctStoreByPriceGroupId(Long orgId, String priceGroupId);

}
