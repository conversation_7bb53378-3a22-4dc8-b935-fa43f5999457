package com.cowell.pricecenter.service;


import java.util.List;
import java.util.Map;

/**
 * redis 缓存服务接口
 */
public interface IRedissonCacheService {

    /**
     * set操作
     *
     * @param key
     * @param obj
     * @return
     */
    public boolean set(String key, Object obj);

    /**
     * set操作 有效时间
     *
     * @param key
     * @param obj
     * @return
     */
    public boolean setNx(String key, Object obj, long expire);

    /**
     * 根据key获取对象实体
     *
     * @param key
     * @return
     */
    public String get(String key);

    /**
     * 根据key删除
     *
     * @param key
     * @return
     */
    public boolean deleteByKey(String... key);

    /**
     * 根据正则key删除
     *
     * @param pattern
     * @return
     */
    public boolean deleteByPattern(String pattern);


    /**
     * 根据key 和 泛型获取List
     *
     * @param key
     * @param t
     * @return
     */
    public <T> List<T> getList(String key, Class<T> t);

    /**
     * 根据key和list 放入缓存
     *
     * @param key
     * @param list
     * @return
     */
    public <T> boolean setList(String key, List<T> list);

//    /**
//     * 根据key枷锁
//     * @param key
//     * @param waitTime  获取锁等待时间(以秒为单位) 默认5秒
//     * @param unLockTime     获取到锁自动解锁时间(以秒为单位) 默认10秒
//     * @return
//     */
//    public boolean getLock(String key,Long waitTime,Long unLockTime);

    /**
     * 根据key枷锁
     *
     * @param key
     * @return
     */
    public boolean getLock(String key);

    /**
     * 解锁
     *
     * @param key
     */
    public void unLock(String key);

    /*=======================================================================================================*/

    /**
     * @param key
     * @param retryTimes 重试次数
     * @return
     */
    public boolean getLock(String key, int retryTimes);

    /**
     * @param key
     * @param retryTimes  重试次数
     * @param sleepMillis 时间间隔
     * @return
     */
    public boolean getLock(String key, int retryTimes, long sleepMillis);

    /**
     * @param key
     * @param expire 到期时间
     * @return
     */
    public boolean getLock(String key, long expire);

    /**
     * @param key
     * @param expire     到期时间
     * @param retryTimes 重试次数
     * @return
     */
    public boolean getLock(String key, long expire, int retryTimes);

    /**
     * @param key
     * @param expire      持锁时间,单位毫秒
     * @param retryTimes  重试次数
     * @param sleepMillis 重试的间隔时间
     * @return
     */
    public boolean getLock(String key, long expire, int retryTimes, long sleepMillis);

    /*=======================================================================================================*/


    /**
     * 根据正则key查询实体
     * @param pattern
     * @return
     */
//    public Collection<String> findByPattern(String pattern);

    /**
     * setMap
     *
     * @param key
     * @param paramMap
     */
    public <T> void setMap(String key, Map<String, T> paramMap);

    /**
     * 获取Map
     *
     * @param key
     * @return
     */
    public <T> Map<String, T> getMap(String key, Class<T> t);


    /**
     * 自增key后获取
     * @param key
     * @return
     */
    public Long incr(String key);

    /**
     * 自增并设置过期时间
     * @param key
     * @param time
     * @return
     */
    public Long incrAndExpire(String key, long time);
}
