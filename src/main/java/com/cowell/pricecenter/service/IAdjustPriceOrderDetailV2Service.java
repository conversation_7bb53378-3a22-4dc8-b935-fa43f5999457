package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.AdjustPriceOrderDetail;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail;
import com.cowell.pricecenter.enums.CommonEnums;
import com.cowell.pricecenter.mq.vo.AdjustPriceOrderDetailSupplementDataVO;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.rule.engine.sdk.enums.FunctionResultTypeEnum;
import com.cowell.rule.engine.sdk.model.FunctionParam;
import com.cowell.rule.engine.sdk.model.FunctionResult;
import com.cowell.rule.engine.sdk.model.RuleFunctionMain;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.*;

/**
 * @program: pricecenter
 * @description: 调价单明细2.0版本的服务接口
 * @author: jmlu
 * @create: 2022-03-24 10:50
 **/

public interface IAdjustPriceOrderDetailV2Service {

    /**
     * 查询调价单明细列表带分页
     * @param param
     * @param userDTO
     * @param isCanEdit
     * @return
     */
    PageResult<Map<String, Object>> listAdjustPriceOrderDetailsPageById(AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO, boolean isCanEdit, boolean isAudit);

    /**
     * 查询调价单明细列表带分页
     * @param param
     * @param userDTO
     * @param isCanEdit
     * @return
     */
    PageResult<Map<String, Object>> listAdjustPriceOrderDetailsPageByCode(AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO, boolean isCanEdit, boolean isAudit);

    /**
     * 批量添加调价单明细
     * @param param
     * @param userDTO
     * @return
     */
    void addAdjustPriceOrderDetails(AdjustPriceOrderDetailAddV2Param param, TokenUserDTO userDTO, Integer createDataSource);

    /**
     * 编辑调价单明细
     * @param param
     * @param userDTO
     * @return
     */
    void editAdjustPriceOrderDetail(AmisTableEditParam param, TokenUserDTO userDTO);

    /**
     * 删除调价单明细
     * @param adjustPriceOrderId
     * @param goodsNo
     * @param userDTO
     */
    void deleteAdjustPriceOrderDetail(Long adjustPriceOrderId, String goodsNo, String adjustDetailMergeIds, TokenUserDTO userDTO);

    /**
     * 删除所有调价单明细
     * @param adjustPriceOrderId
     * @param userDTO
     */
    void deleteAdjustPriceOrderDetails(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 删除调价单明细
     * @param adjustPriceOrderId
     * @param goodsNoList
     * @param userDTO
     */
    void deleteAdjustPriceOrderDetails(Long adjustPriceOrderId, List<String> goodsNoList, List<Long> detaildList, TokenUserDTO userDTO, List<Long> skuIdList);

    /**
     * excel文件导入调价单明细
     * @param importAdjustPriceOrder 导入内容list
     * @param userDTO 登录用户
     */
    void importAdjustPriceOrderDetails(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO);

    /**
     * 调价单补全商品信息和涉及到管控的相关信息
     * @param mqResult
     */
    void supplementAdjustPriceOrderDetailData(AdjustPriceOrderDetailSupplementDataVO mqResult);

    /**
     * 根据商品编码，渠道ID，价格类型Code获取调价单明细数据
     * @param adjustCode 调价单号
     * @param goodsNo 商品编码
     * @param channelId 渠道ID
     * @param priceTypeCode 价格类型Code
     * @return
     */
    Optional<AdjustPriceOrderDetail> getAdjustPriceOrderDetail(String adjustCode, String goodsNo, Integer channelId, String priceTypeCode);

    /**
     * 根据新的价格类型和渠道Id的重置调价单明细
     * @param adjustPriceOrder
     * @param orderPriceTypeAndChannelChange
     */
    void resetOrderDetailsByChannelsAndPriceTypes(AdjustPriceOrder adjustPriceOrder, PriceOrderPriceTypeAndChannelChange orderPriceTypeAndChannelChange,
        TokenUserDTO userDTO, Date operateTime);

    /**
     * 根据调价单号非物理删除调价单明细
     * @param adjustCode
     * @param userDTO
     * @param operateTime
     */
    void nonPhysicalDeleteByAdjustCode(String adjustCode, TokenUserDTO userDTO, Date operateTime);


    /**
     * 根据调价单号查询商品编码列表（去重）
     * @param adjustCode
     * @return
     */
    List<String> getGoodsNoListbyAdjustCode(@Param("adjustCode") String adjustCode);

    /**
     * 异步导出调价单明细文件到下载中心
     * @param adjustPriceOrderId
     */
    ExportFileCubeVO<Map<String, Object>> exportAdjustPriceOrderDetailsFile(Long adjustPriceOrderId,String adjustPriceOrderCode,Integer page,
    		Integer pageSize, String goodsNo,Long userId,Long workcode,Boolean isPortal);

    /**
     * 根据调价单单号，商品编码，价格类型Code，不抛出异常，参数为空则返回空
     * @param adjustCode
     * @param goodsNo
     * @param priceTypeCode
     * @return
     */
    Optional<AdjustPriceOrderDetail> getSingleAdjustPriceOrderDetail(String adjustCode, String goodsNo,
        String priceTypeCode,List<Long> detaildList);

    /**
     * 查看调价单明细是否添加/excel导入完成
     * @param adjustPriceOrderId
     * @param userDTO
     */
    boolean isAdjustPriceOrderDetailImportCompleted(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 查看调价单明细是否添加/excel导入完成
     * @param adjustCode
     * @param userDTO
     */
    boolean isAdjustPriceOrderDetailImportCompletedByCode(String adjustCode, TokenUserDTO userDTO);

    /**
     * 根据调价单编码、商品编码、价格类型物理删除数据
     * @param adjustCode
     * @param goodsNo
     * @param priceTypeCode
     */
    void deleteByAdjustCodeAndGoodsNoAndPriceType(String adjustCode, String goodsNo, String priceTypeCode);

    /**
     * 调价单明细数据是否都已经完整
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    boolean isAdjustPriceOrderDetailDataFull(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 调价单明细数据是否都已经完整
     * @param adjustCode
     * @param userDTO
     * @return
     */
    boolean isAdjustPriceOrderDetailDataFullByCode(String adjustCode, TokenUserDTO userDTO);

    /**
     * 调价单明细数据不完整的商品编码
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    List<String> getGoodsNoListForDetailDataNotFull(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 调价单明细数据不完整的商品编码
     * @param adjustCode
     * @param userDTO
     * @return
     */
    List<String> getGoodsNoListForDetailDataNotFullByCode(String adjustCode, TokenUserDTO userDTO);

    /**
     * 根据调价单编码和价格类型列表获取按照商品编码和价格类型Group后的数据
     * @param adjustCode
     * @param priceTypeCodeList
     * @return
     */
     List<AdjustPriceOrderDetail> selectOrderDetailV2ListGroupByGoodAndPriceType(String adjustCode, List<String> priceTypeCodeList);

    /**
     * 给OA移动提供的调价单明细带分页
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<OAMobileAdjustPriceOrderDetailVO> listMobileAdjustPriceOrderDetailsPageById(AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO);

    /**
     * 给OA移动提供的调价单明细带分页
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<OAMobileAdjustPriceOrderDetailVO> listMobileAdjustPriceOrderDetailsPageByCode(AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO);


    /**
     * 校验调价单不完整的相关字段调价单明细数据
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    List<AdjustPriceOrderDetail> getToCheckOrderDetailListForDetailDataNotFull(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 校验调价单不完整的相关字段调价单明细数据
     * @param adjustCode
     * @param userDTO
     * @return
     */
    List<AdjustPriceOrderDetail> getToCheckOrderDetailListForDetailDataNotFullByCode(String adjustCode, TokenUserDTO userDTO);

    /**
     * 是否有商品明细
     * @param adjustCode
     * @param userDTO
     * @return
     */
    boolean hasGoodDetail(String adjustCode, TokenUserDTO userDTO);

    /**
     * amis的表格立即保存调价单商品明细数据
     * @param param
     * @param userDTO
     */
    void immediatelyEditAdjustPriceOrderDetail(Map<String, String> param, TokenUserDTO userDTO);

    /**
     * 根据复制价格类型和渠道数据
     * @param adjustPriceOrderId
     */
    List<AdjustPriceOrderDetail> copyByAdjustPriceTypeAndChannelCopyList(Long adjustPriceOrderId);

    /**
     * 根据调价单编码，价格类型，渠道获取所有明细数据
     * @param adjustCode
     * @param priceTypeCode
     * @param channelId
     * @return
     */
    List<AdjustPriceOrderDetail> listByAdjustCodeAndPriceTypeCodeAndChannelId(String adjustCode, String priceTypeCode, Integer channelId);

    /**
     * 删除所有不在调价单价格类型中明细数据
     * @param adjustCode
     * @param priceTypeCodes
     */
    void deleteAllDataNotInAdjustPriceTypeCodes(String adjustCode, String priceTypeCodes);
    /**
     *
     * @Title: addOrEditPriceOrderGoodsOrgDetail
     * @Description: 新增/编辑调价单商品机构
     * @param: @param param
     * @param: @param userDTO
     * @return: void
     * @throws
     */
    void addOrEditPriceOrderGoodsOrgDetail(PriceOrderGoodsOrgDetailV2Param param,TokenUserDTO userDTO);
    /**
     *
     * @Title: getAdjustPriceOrderGoodsOrg
     * @Description: 查询商品机构
     * @param: @param adjustCode
     * @param: @param adjustDetailMergeIds
     * @param: @return
     * @return: AdjustPriceOrderOrgDetailVO
     * @throws
     */
    AdjustPriceOrderOrgDetailVO getAdjustPriceOrderGoodsOrg(Long adjustPriceOrderId,String adjustDetailMergeIds);

    /**
     *
     * @Title: getAdjustImportResult
     * @Description: 获取调价单导入结果
     * @param: @param adjustCode
     * @param: @return
     * @return: AdjustImportResult
     * @throws
     */
    AdjustImportResultDTO getAdjustImportResult(Long adjustPriceOrderId,String downloadCode);
    /**
     * 异步导出异常调价单明细文件到下载中心
     * @param adjustPriceOrderId
     */
    ExportFileCubeVO<Map<String, Object>> asyncExportErrorAdjustPriceOrderDetailsFile(Long adjustPriceOrderId, Integer page, Integer pageSize);

    /**
     *
     * @Title: saveAdjustPriceDetailImage
     * @Description: 保存商品明细审批相关字段
     * @param: @param param
     * @return: void
     * @throws
     */
    void saveAdjustPriceDetailAuditInfo(AdjustPriceOrderDetailImageV2Param param);

    /**
     * 调价单明细调价原因为空的
     * @param adjustCode
     * @return
     */
    boolean countAdjustPriceOrderDetailReasonIsNull(String adjustCode);

    /**
     *
     * @Title: updateAdjustPriceOrderDetailAuditStatus
     * @Description: oa审批预拒绝改为拒绝
     * @param: @param adjustCode
     * @param: @param auditStatusNew
     * @param: @param auditStatusOld
     * @return: void
     * @throws
     */
    void updateAdjustPriceOrderDetailAuditStatus(String adjustCode,Integer auditStatusNew,Integer auditStatusOld,String operatorUserName,Boolean init);

    /**
     *
     * @Title: sendToSupplementDataMsgs
     * @Description: 发送需要补全的调价单明细信息
     * @param: @param insertAdjustPriceOrderDetailList
     * @param: @param dataFull
     * @return: void
     * @throws
     */
    void sendToSupplementDataMsgs(boolean isPortal,List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList,Boolean dataFull);

    /**
     * 调价单明细没有补充完整的数据
     * @param adjustCode
     * @return
     */
    List<AdjustPriceOrderDetail> selectAdjustPriceOrderDetailSupplementNotCompleted(String adjustCode);

    /**
     * 改变调价单明细行 重算管控状态
     * @param mqResult
     */
    void changeItemSuppLimitStatus(AdjustPriceOrderDetailSupplementDataVO mqResult);

    /**
     *
     * @Title: updateAdjustPriceOrderDetailLimitStatus
     * @Description: 根据调价单号 刷整单状态为数据已补全
     * @param: @param adjustCode
     * @param: @param limitStatusNew
     * @return: void
     * @throws
     */
    void updateAdjustPriceOrderDetailLimitStatus(String adjustCode,Integer limitStatusNew,Integer dataFullNew);

    /**
     * 检查调价单明细价格是否填写完整
     * @param adjustCode
     * @return
     */
	boolean checkAdjustPriceIsNull(String adjustCode);

	/**
	 * 连锁级别以上orgId 返回连锁sapCode，连锁级别一下orgId 返回门店sapCode
	 * @param item
	 */
	void fillOrgIdsSapCode(Map<String, Object> item);

	/**
     * 异步导出异常初始化新店文件到下载中心
     * @param adjustPriceOrderId
     */
    ExportFileCubeVO<Map<String, Object>> asyncExportErrorInitNewStoreDetailsFile(String downloadCode, Integer page);

	void updateExtend1BusinessIds(List<String> adjustCodeList);

	/**
	 * 根据调价单id查询明细数量
	 * @param adjustPriceOrderId
	 * @return
	 */
	long findAdjustPriceOrderDetailCount(Long adjustPriceOrderId);

	/**
     * b2c调价单补全商品信息和涉及到管控的相关信息
     * @param mqResult
     */
    void supplementAdjustB2CPriceOrderDetailData(AdjustPriceOrderDetailSupplementDataVO supplementDataVO);

    /**
     * b2c调价单 excel文件导入调价单明细
     * @param importAdjustPriceOrder 导入内容list
     * @param userDTO 登录用户
     */
    void importAdjustPriceOrderDetailsForB2C(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO);

    /**
     * 根据调价单号查询调价单明细skuid
     * @param adjustCode
     * @return
     */
    List<String> getSkuIdListbyAdjustCode(String adjustCode);

    /**
     * 根据调价单号查询调价单明细id
     * @param adjustCode
     * @return
     */
    List<Long> selectAdjustPriceOrderDetailIdByAdjustCode(String adjustCode,List<Integer> channelIdList,List<String> priceTypeCodes);
    /**
     * 根据调价单号和调价单明细id删除调价单明细门店id缓存
     * @param adjustCode
     * @param idList
     */
    void deleteAdjustPriceOrderDetailStoreIdCache(String adjustCode,List<Long> idList);

    /**
     * 保存调价单明细门店id缓存
     * @param adjustCode
     * @param detailStoreIdList
     */
    void saveAdjustPriceOrderDetailStoreIdCache(String adjustCode,List<AdjustPriceOrderDetailStoreIdCacheDTO> detailStoreIdList);

    /**
     * 根据调价单id查询所有明细验证医保限价并且拦截医保限价
     * @param adjustPriceOrder
     * @return
     */
    boolean interceptMedicarePrice(AdjustPriceOrder adjustPriceOrder);

    /**
     * 规则引擎调用的医保限价验证函数方法名
     * @param ruleFunctionMain
     * @return
     */
    public FunctionResult interceptMedicarePriceLimitResult(RuleFunctionMain ruleFunctionMain);

    /**
     * 获取单体加盟版定调价 门店id
     * @param adjustPriceOrder
     * @return
     */
    Long getSingleStoreFranchisePricingByStoreId(AdjustPriceOrder adjustPriceOrder);
}
