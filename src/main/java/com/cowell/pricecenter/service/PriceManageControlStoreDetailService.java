package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceManageControlStoreDetail;
import com.cowell.pricecenter.entity.PriceManageControlStoreDetailExample;
import com.cowell.pricecenter.service.dto.ControlOrderStoreDetailQueryDTO;
import com.cowell.pricecenter.service.dto.ControlStoreGoodsDetailResultDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/22 14:49
 */

public interface PriceManageControlStoreDetailService {


    long countByExample(PriceManageControlStoreDetailExample example);

    int deleteByExample(PriceManageControlStoreDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PriceManageControlStoreDetail record);

    int insertSelective(PriceManageControlStoreDetail record);

    List<PriceManageControlStoreDetail> selectByExample(PriceManageControlStoreDetailExample example);

    PriceManageControlStoreDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(PriceManageControlStoreDetail record, PriceManageControlStoreDetailExample example);

    int updateByExample(PriceManageControlStoreDetail record, PriceManageControlStoreDetailExample example);

    int updateByPrimaryKeySelective(PriceManageControlStoreDetail record);

    int updateByPrimaryKey(PriceManageControlStoreDetail record);

    int insertOrUpdate(PriceManageControlStoreDetail record);

    int insertOrUpdateSelective(PriceManageControlStoreDetail record);

    int updateBatch(List<PriceManageControlStoreDetail> list);

    int updateBatchSelective(List<PriceManageControlStoreDetail> list);

    int batchInsert(List<PriceManageControlStoreDetail> list);

    /**
     * 通过查询条件批量查询数据
     * @param detailQueryDTO 查询条件
     * @return List<PriceManageControlStoreDetail>
     */
    List<PriceManageControlStoreDetail> getControlStoreDetailByParam(ControlOrderStoreDetailQueryDTO detailQueryDTO);

    /**
     * 保存到门店的管控明细
     * @param resultDTO
     */
    void savePriceControlStoreDetail(ControlStoreGoodsDetailResultDTO resultDTO);

    /**
     * 执行价格管控
     * 查询到价格管控单生效时间是当天
     * 然后查询到管控门店商品明细中待生效的商品
     * 最后查询到门店剩下的价格不收管控的品将价格修改掉
     */
    void executePriceControl();

    /**
     * 通过查询参数查询门店管控商品明细总数
     * @param queryDTO 查询参数
     * @return long 总数
     */
    long countControlStoreDetailByParam(ControlOrderStoreDetailQueryDTO queryDTO);

    /**
     * 通过游标的方式扫描管控单门店商品明细
     * @param queryDTO 查询参数
     */
    void scanControlStoreDetail(ControlOrderStoreDetailQueryDTO queryDTO);

    /**
     * 将门店商品价格不符合管控范围内的商品进行重置价格
     * @param storeDetails
     */
    void updateControlOrderStoreDetail(List<PriceManageControlStoreDetail> storeDetails);
}


