package com.cowell.pricecenter.service.feign.facade;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.service.dto.CommonResultDTO;
import com.cowell.pricecenter.service.dto.ItemBaseDTO;
import com.cowell.pricecenter.service.dto.ItemQueryDTO;
import com.cowell.pricecenter.service.dto.PageResultDTO;
import com.cowell.pricecenter.service.dto.request.ForestGoodsParam;
import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.request.SpuNewParamVo;
import com.cowell.pricecenter.service.dto.request.SpuQueryParamVo;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.feign.ItemSearchEngineService;
import com.cowell.pricecenter.service.feign.vo.SpuListVO;
import com.cowell.pricecenter.service.feign.vo.SpuQueryParamVO;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/13 16:20
 */
@Slf4j
@Service
public class ItemSearchEngineFacadeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ItemSearchEngineFacadeService.class);

    @Autowired
    private ItemSearchEngineService itemSearchEngineService;

    public PageResult<SpuNewVo> getNewSpuList(long businessId, List<Long> goodsNos){
        ResponseEntity<CommonRes<PageResult<SpuNewVo>>> rep = itemSearchEngineService.getNewSpuList(businessId, goodsNos.stream().map(String::valueOf).collect(Collectors.joining(",")));
        if (null == rep || Objects.isNull(rep.getBody()) || Objects.isNull(rep.getBody().getData())) {
            return new PageResult<>();
        }
        return rep.getBody().getData();
    }

    public List<SpuNewVo> getNewSpuList4Post(SpuNewParamVo param) {
        PageResult<SpuNewVo> newSpuListPage = getNewSpuListPage(param);

        if (CollectionUtils.isEmpty(newSpuListPage.getRows())) {
            log.debug("查询ES返回结果为空，参数:{}", param);
            return new ArrayList<>();
        }
        return newSpuListPage.getRows();

    }

    public PageResult<SpuNewVo> getNewSpuListPage(SpuNewParamVo param) {
        Map<String, Object> map = objectToMap(param);
        List<Long> businessIdList = param.getBusinessIdList();
        if (CollectionUtils.isNotEmpty(businessIdList)) {
            map.put("businessId", businessIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        List<Long> goodsNos = param.getGoodsNos();
        if (CollectionUtils.isNotEmpty(goodsNos)) {
            map.put("goodsNo", goodsNos.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("goodsNos");
        }
        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNo", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        List<Integer> tagList = param.getTagList();
        if (CollectionUtils.isNotEmpty(tagList)) {
            map.put("tagList", tagList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }

        Integer pageSize = param.getPageSize();
        if (Objects.nonNull(pageSize)) {
            map.put("perPage", pageSize);
            map.remove("pageSize");
        }

        ResponseEntity<CommonRes<PageResult<SpuNewVo>>> response = itemSearchEngineService.getNewSpuList4Post(map);

        if (null == response
            || Objects.isNull(response.getBody())
            || Objects.isNull(response.getBody().getData())) {
            log.debug("查询ES返回结果为空，参数:{}", map);
            return new PageResult<>();
        }
        return response.getBody().getData();
    }

    public PageResponse<List<ForestGoodsDTO>> getSupAllList(ForestGoodsParam param) {
        Map<String, Object> map = objectToMap(param);

        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNo", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        List<Long> spuIds = param.getSpuIds();
        if (CollectionUtils.isNotEmpty(spuIds)) {
            map.put("spuId", spuIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("spuIds");
        }
        Integer pageSize = param.getPageSize();
        if (Objects.nonNull(pageSize)) {
            map.put("perPage", pageSize);
            map.remove("pageSize");
        }

        ResponseEntity<CommonRes<PageResult<SpuListVO>>> response = itemSearchEngineService.getSupAllList(map);
        if (null == response
            || Objects.isNull(response.getBody())
            || Objects.isNull(response.getBody().getData())) {
            log.debug("查询ES返回结果为空，参数:{}", map);
            return new PageResponse<>();
        }
        PageResult<SpuListVO> pageResult = response.getBody().getData();
        List<SpuListVO> rows = pageResult.getRows();
        List<ForestGoodsDTO> collect = rows.stream().map(spuListVO -> {
            ForestGoodsDTO forestGoodsDTO = new ForestGoodsDTO();
            BeanUtils.copyProperties(spuListVO, forestGoodsDTO);
            return forestGoodsDTO;
        }).collect(Collectors.toList());

        PageResponse<List<ForestGoodsDTO>> pageResponse = new PageResponse<>();
        pageResponse.setResult(collect);
        pageResponse.setTotalSize(pageResult.getTotal());
        pageResponse.setPage(param.getPage());
        pageResponse.setPageSize(param.getPageSize());

        return pageResponse;
    }

    public PageResult<SpuListVO> getSpuList(SpuQueryParamVO param) {
        Map<String, Object> map = objectToMap(param);

        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNo", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        List<Long> spuIds = param.getSpuIds();
        if (CollectionUtils.isNotEmpty(spuIds)) {
            map.put("spuId", spuIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("spuIds");
        }
        Integer pageSize = param.getPageSize();
        if (Objects.nonNull(pageSize)) {
            map.put("perPage", pageSize);
            map.remove("pageSize");
        }
        String keyWord = param.getKeyWord();
        if (StringUtils.isNotBlank(keyWord)) {
            map.put("keyword", keyWord);
            map.remove("keyWord");
        }

        ResponseEntity<CommonRes<PageResult<SpuListVO>>> response = itemSearchEngineService.getSupAllList(map);

        if (null == response
            || Objects.isNull(response.getBody())
            || Objects.isNull(response.getBody().getData())) {
            log.debug("查询ES返回结果为空，参数:{}", map);
            return new PageResult<>();
        }

        return response.getBody().getData();
    }

    public PageResponse<List<SpuListVo>> getSupContentsList(SpuQueryParamVo param) {
        Map<String, Object> map = objectToMap(param);
        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNo", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        List<Long> spuIds = param.getSpuIds();
        if (CollectionUtils.isNotEmpty(spuIds)) {
            map.put("spuId", spuIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("spuIds");
        }
        Integer pageSize = param.getPageSize();
        if (Objects.nonNull(pageSize)) {
            map.put("perPage", pageSize);
            map.remove("pageSize");
        }

        ResponseEntity<CommonRes<PageResult<SpuNewVo>>> response = itemSearchEngineService.getNewSpuList4Post(map);
        if (null == response
            || Objects.isNull(response.getBody())
            || Objects.isNull(response.getBody().getData())) {
            log.debug("查询ES返回结果为空，参数:{}", map);
            return new PageResponse<>();
        }

        PageResult<SpuNewVo> pageResult = response.getBody().getData();
        List<SpuNewVo> rows = pageResult.getRows();
        List<SpuListVo> collect = rows.stream().map(spuListVO -> {
            SpuListVo vo = new SpuListVo();
            BeanUtils.copyProperties(spuListVO, vo);
            return vo;
        }).collect(Collectors.toList());

        PageResponse<List<SpuListVo>> pageResponse = new PageResponse<>();
        pageResponse.setResult(collect);
        pageResponse.setTotalSize(pageResult.getTotal());
        pageResponse.setPage(param.getPage());
        pageResponse.setPageSize(param.getPageSize());

        return pageResponse;
    }

    private static Map<String, Object> objectToMap(Object object){
        Map<String,Object> dataMap = new HashMap<>();
        Class<?> clazz = object.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            try {
                field.setAccessible(true);
                dataMap.put(field.getName(),field.get(object));
            } catch (IllegalAccessException e) {
                log.error("对象转map异常", e);
            }
        }
        return dataMap;
    }

    public List<ItemBaseDTO> queryItemBaseList(ItemQueryDTO itemVo) {
        Map<String, Object> pageParam = new HashMap<>();
        pageParam.putAll(BeanUtil.beanToMap(itemVo));
        pageParam.put("page", itemVo.getPage());
        pageParam.put("perPage", itemVo.getPageSize());
        if (CollectionUtils.isNotEmpty(itemVo.getItemIdList())) {
            pageParam.put("itemIdList", null);
            pageParam.put("itemId", Joiner.on(",").join(itemVo.getItemIdList()));
        }
        Long storeId = itemVo.getStoreId();
        if (storeId != null){
            pageParam.put("storeId", itemVo.getStoreId());
        }
        Long itemUnionId = itemVo.getItemUnionId();
        if (itemUnionId != null){
            pageParam.put("itemUnionId", itemVo.getItemUnionId());
        }
        Long businessId = itemVo.getBusinessId();
        if (businessId != null){
            pageParam.put("businessId", itemVo.getBusinessId());
        }
        Long channelStoreId = itemVo.getChannelStoreId();
        if (channelStoreId != null){
            pageParam.put("channelBizStoreId", itemVo.getChannelStoreId());
        }
        Integer channelType = itemVo.getChannelType();
        if (channelType != null){
            pageParam.put("channelType", itemVo.getChannelType());
        }
        CommonResultDTO<PageResultDTO<ItemBaseDTO>> result = null;
        try {
            LOGGER.info("ItemSearchEngineServiceImpl|queryItemBaseList:param={}", JSON.toJSONString(pageParam));
            result = itemSearchEngineService.getItemListInner(pageParam);
        } catch (Exception e) {
            LOGGER.error("ItemSearchEngineServiceImpl|queryItemBaseList: 异常了", e);
        }

        if (result == null || result.getData() == null) {
            return Lists.newArrayList();
        }
        PageResultDTO<ItemBaseDTO> itemBaseDTOPageResultDTO = result.getData();
        if (Objects.isNull(itemBaseDTOPageResultDTO)) {
            return Lists.newArrayList();
        }
        return itemBaseDTOPageResultDTO.getRows();
    }

}
