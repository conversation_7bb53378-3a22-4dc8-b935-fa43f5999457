package com.cowell.pricecenter.service.feign.vo;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

/**
 * @program: pricecenter
 * @description: 价格众数返回结果
 * @author: jmlu
 * @create: 2022-04-07 10:57
 **/

@Data
public class PriceModeResult {

    /**
     * 商品编码
     */
    private String goodsNo;
    
    /**
     * 商品对应的价格信息list
     */
    private List<PriceDetailVO> priceDetailVOS;

    /**
     * 众数（零售价）
     */
    private BigDecimal modePriceWeightedValue;

    /**
     * 众数（会员价）
     */
    private BigDecimal modeMemberPriceWeightedValue;

    /**
     * 众数（拆零金额）
     */
    private BigDecimal scatteredPriceWeightedValue;

    /**
     * "众数（会员拆零金额）"
     */
    private BigDecimal scatteredMemberPriceWeightedValue;

    /**
     * 众数成本
     */
    private BigDecimal modePriceCost;
    
}
