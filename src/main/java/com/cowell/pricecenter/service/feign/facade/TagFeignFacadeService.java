package com.cowell.pricecenter.service.feign.facade;

import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.enums.EntityTypeEnum;
import com.cowell.pricecenter.enums.TagBizTypeEnum;
import com.cowell.pricecenter.enums.TagTypeEnums;
import com.cowell.pricecenter.service.dto.request.EntityTagQueryParam;
import com.cowell.pricecenter.service.dto.request.TagPageParam;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.feign.TagFeignService;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> hcqin
 * @description : 标签服务
 * @date : 2022/5/30 10:19
 */
@Service
@Slf4j
public class TagFeignFacadeService {

    @Autowired
    private TagFeignService tagFeignService;

    /**
     * 查询实体 的标签集合
     * @param
     * @return
     */
    public Map<String,List<Long>> findEntityTagList(List<String> entityCodeList, EntityTypeEnum entityType,TagBizTypeEnum tagBizType){
        Map<String,List<Long>> tagMap = new HashMap<>();
        if(CollectionUtils.isEmpty(entityCodeList)){
            return tagMap;
        }
        EntityTagQueryParam param = new EntityTagQueryParam();
        param.setEntityType(entityType.getType());
        param.setTagBizType(tagBizType.getCode());
        param.setPrePage(entityCodeList.size());
        param.setEntityCodeList(entityCodeList.stream().distinct().collect(Collectors.toList()));
        param.setManageFlag(0);
        List<EntityTagQueryServerDto> tagDtoList = entityTagQuery(param);
        if(CollectionUtils.isEmpty(tagDtoList)){
            return tagMap;
        }
        for(EntityTagQueryServerDto dto:tagDtoList) {
            if(CollectionUtils.isNotEmpty(dto.getTagList())){
                tagMap.put(dto.getEntityCode(),dto.getTagList().stream().map(EntityTagQueryServerDetailDto::getTagId).collect(Collectors.toList()));
            }
        }
        return tagMap;
    }

    /**
     * 调用 tag服务，查询商品标签集合
     * @param param
     * @return
     */
    private List<EntityTagQueryServerDto> entityTagQuery(EntityTagQueryParam param){
        long startTime = System.currentTimeMillis();
        List<EntityTagQueryServerDto> tagDtoList = new ArrayList<>();
        try {
            log.info("调用tag服务，查询商品标签入参:{}", JSON.toJSONString(param));
            ResponseEntity<CommonRes<PageResult<EntityTagQueryServerDto>>> responseEntity = tagFeignService.entityTagQuery(param);
            if(responseEntity.getStatusCode() == HttpStatus.OK){
                PageResult<EntityTagQueryServerDto> pageResult =responseEntity.getBody().getData();
                tagDtoList = pageResult.getRows();
            }
        }catch (Exception e){
            log.error("调用tag服务，查询商品标签返回异常:error:", e);
            //throw new BusinessErrorException("调用tag服务，查询商品标签返回异常,error:{}",e.getMessage());
        }
        log.info("调用tag服务，查询商品标签返回，耗时:{}", System.currentTimeMillis() - startTime);
        return tagDtoList;
    }

    /**
     * 根据门店组id查询门店组标签根据门店组
     * @param groupId
     * @return
     */
    public TagDTO getTagDTOByStoreGroupId(Long groupId) {
        TagPageParam tagParam = new TagPageParam();
        tagParam.setBizType(TagBizTypeEnum.MARKETING.getCode());
        tagParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
        tagParam.setEntityType(EntityTypeEnum.STORE.getType());
        tagParam.setTagId(groupId);
        tagParam.setPage(1L);
        tagParam.setPageSize(1);
        ResponseEntity<CommonRes<PageResult<TagDTO>>> commonRes = tagFeignService.queryByPage(tagParam);
        if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
            || Objects.isNull(commonRes.getBody().getData()) || CollectionUtils.isEmpty(commonRes.getBody().getData().getRows())){
            throw new BusinessErrorException("门店组不存在");
        }
        TagDTO tagDTO = commonRes.getBody().getData().getRows().get(0);
        return tagDTO;
    }

    /**
     * 根据门店组 tagIdList 查询门店组集合
     * @param storeGroupIdList
     * @return
     */
    public List<TagDTO> getTagDTOListByStoreGroupId(List<Long> storeGroupIdList) {
        TagPageParam tagParam = new TagPageParam();
        tagParam.setBizType(TagBizTypeEnum.MARKETING.getCode());
        tagParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
        tagParam.setEntityType(EntityTypeEnum.STORE.getType());
        tagParam.setTagIdList(storeGroupIdList);
        tagParam.setPage(1L);
        tagParam.setPageSize(storeGroupIdList.size());
        ResponseEntity<CommonRes<PageResult<TagDTO>>> commonRes = tagFeignService.queryByPage(tagParam);
        if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
            || Objects.isNull(commonRes.getBody().getData()) || CollectionUtils.isEmpty(commonRes.getBody().getData().getRows())){
            throw new BusinessErrorException("门店组不存在");
        }
        return commonRes.getBody().getData().getRows();
    }

}
