package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.CommonResultDTO;
import com.cowell.pricecenter.service.dto.ItemBaseDTO;
import com.cowell.pricecenter.service.dto.PageResultDTO;
import com.cowell.pricecenter.service.dto.response.CommonRes;
import com.cowell.pricecenter.service.dto.response.PageResult;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import com.cowell.pricecenter.service.feign.vo.SpuListVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/6/13 11:26
 */
@FeignClient(name = "item-search-engine")
public interface ItemSearchEngineService {
    @RequestMapping(value = "/api/intranet/general/305/a4011c8ebbf44a21adbf1c83291fb811", method = RequestMethod.GET, headers = {"bizCode=pricecenter"})
    @Timed
    ResponseEntity<CommonRes<PageResult<SpuNewVo>>> getNewSpuList(@RequestParam(value = "businessId") long businessId,
                                                                  @RequestParam(value = "goodsNo") String goodsNos);


    @RequestMapping(value = "/api/intranet/general/305/799b6cd35f09477d8983989ad579d338", method = RequestMethod.GET, headers = {"bizCode=pricecenter"})
    @ResponseBody
    @Timed
    ResponseEntity<CommonRes<PageResult<SpuNewVo>>> getNewSpuList4Post(@RequestParam Map<String, Object> map);

    @RequestMapping(value = "/api/intranet/general/305/06cfe3d53653402c9a219be8ba55a84a", method = RequestMethod.GET, headers = {"bizCode=pricecenter"})
    @ResponseBody
    @Timed
    ResponseEntity<CommonRes<PageResult<SpuListVO>>> getSupAllList(@RequestParam Map<String, Object> map);


    @ApiOperation("查询itemBase")
    @GetMapping(value = "/api/intranet/general/305/1a0283d946cf41ceb340b227d2e207dc", headers = {"bizCode=B2B"})
    CommonResultDTO<PageResultDTO<ItemBaseDTO>> getItemListInner(@RequestParam(value = "params") Map<String, Object> params);
}
