package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.OmsO2oConfigDTO;
import com.cowell.pricecenter.service.dto.OmsO2oConfigDetailDTO;
import com.cowell.pricecenter.service.dto.OrderChannelTypeVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * 获取门店信息rpc服务
 *
 * <AUTHOR>
 * @date 2018/5/22
 */
@FeignClient(name = "oms")
public interface OmsFeignService {


    @GetMapping("/api/internal/oms/o2o/config/detail/detail/param")
    @Timed
    ResponseEntity<OmsO2oConfigDetailDTO> detailByBusinessIdAndChannel(@RequestParam("businessId") Long businessId, @RequestParam("channel") Integer channel);

    /**
     * 获取oms渠道的连锁id
     * @return
     */
    @GetMapping("/api/internal/businessid/list")
    @Timed
    ResponseEntity<Set<String>> getBusinessIds();



    @GetMapping("/api/noauth/oms/o2o/config/channel/feign")
    @Timed
    ResponseEntity<List<OrderChannelTypeVO>> channelList();

}
