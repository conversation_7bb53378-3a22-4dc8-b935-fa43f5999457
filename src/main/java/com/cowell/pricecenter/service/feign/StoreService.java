package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.builder.dto.StoreChannelMappingDTO;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.web.rest.vo.MdmDataTransformDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 获取门店信息rpc服务
 * Created by liw on 2018/5/22.
 */
@FeignClient(name = "store-sync")
public interface StoreService {


    @PostMapping("/api/mdmBusinessBase/transform")
    @Timed
    MdmDataTransformDTO transformMdmData(@Valid @RequestBody MdmDataTransformDTO mdmDataTransformDTO);


    @PostMapping("/api/internal/scm/getByConditions")
    @Timed
    ResponseEntity<List<StoreChannelMappingDTO>> getStoreChannelMappingByConditions(@RequestBody StoreChannelMappingDTO dto);

    /**
     * 根据用户id获取所属门店列表
     * @param userId
     * @return
     * */
    @RequestMapping(value = "/api/internal/getStoreIdsByUserId", method = RequestMethod.GET)
    @Timed
    public List<Long> getStoreIdsByUserId(@RequestParam(value = "userId") Long userId);
    @PostMapping("/api/internal/file/uploadfileByByte")
    @Timed
    ResponseEntity<Map<String, Object>> uploadfileByByte(@RequestParam(value = "fileName") String fileName, @RequestBody byte[] content);


    /**
     * 调用store店铺与第三方店铺绑定关联关系数据
     *
     * @param channel    渠道
     * @param storeId    crm storeId
     * @param bindStatus 绑定状态
     * @return ResponseEntity<List   <   StoreChannelMappingDTO>>
     */
    @PostMapping("api/internal/bind/getmappingstore")
    @Timed
    ResponseEntity<List<StoreChannelMappingDTO>> getMappingStore(@RequestParam("channel") Integer channel,
                                                                 @RequestParam("storeId") Long storeId,
                                                                 @RequestParam("bindStatus") Integer bindStatus);

    /**
     * 查询连锁信息
     * @param id
     * @return
     */
    @RequestMapping(value = "/api/business-infos/getOneById", method = RequestMethod.GET)
    @Timed
    public BussinessInfoDTO getBusinessInfoById(@RequestParam(value = "id") Long id);


    /**
     * 根据门店id  获取门店简略信息
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/api/internal/crm-stores-findOne", method = RequestMethod.GET)
    @Timed
    public ResponseEntity<StoreBriefDTO> getStoreById(@RequestParam(value = "id") Long id);

    /**
     * 通过多个连锁ID，获取对应的多个门店信息
     *
     * @param businessIds 连锁ID数组
     * @return 门店数组
     */
    @RequestMapping(value = "/api/getCrmStoreByBusinessIds", method = RequestMethod.GET)
    @Timed
    List<CrmStoreBusinessDTO> getCrmStoreByBusinessIds(@RequestParam(value = "businessIds") Long[] businessIds);




    @GetMapping("/api/internal/mdmStoreBase/findByStoreNo")
    @Timed
    ResponseEntity<MdmStoreBaseDTO> findByStoreNoInternal(@RequestParam("storeNo") String storeNo);


    /**
     * 获取所有 mdm 的连锁id
     * @return
     */
    @PostMapping("/api/internal/mdmBusinessBase/findAllMdmBusinessBase")
    @Timed
    List<MdmBusinessBaseDTO> findAllMdmBusinessBase();


    /**
     * 根据连锁id查询该连锁下的所有门店信息
     *
     * @param businessId
     */
    @GetMapping("/api/internal/mdmStoreBase/findAllStoreByBusinessId")
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findMdmStoreByBusinessId(@RequestParam("businessId") Long businessId);

    /**
     * 根据store id获取mdm的门店信息
     *
     * @param storeId
     * @return
     */
    @GetMapping("/api/internal/mdmStoreBase/findByStoreId")
    @Timed
    ResponseEntity<MdmStoreBaseDTO> findByStoreId(@RequestParam("storeId") Long storeId);
    /**
     * 根据store id获取mdm的门店信息
     *
     * @param storeIds
     * @return
     */
    @PostMapping("/api/internal/mdmStoreBase/findStoreByStoreIds")
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findStoreByStoreIds(@RequestBody List<Long> storeIds);

    /**
     * 获取连锁门店映射关系
     * @param businessIdList
     * @param channelList
     * @return
     */
    @GetMapping(value = "/api/internal/store/channel/mapping/listByBusinessIdList")
    @Timed
    ResponseEntity<List<StoreChannelMappingDTO>> getAllStoreChannelMappingByBusinessIdList(@RequestParam(value = "businessIdList",required = false)List<Long> businessIdList,@RequestParam(value = "channelList",required = false) List<Integer> channelList);

    /**
     * 获取连锁门店映射关系
     * @param businessIdList
     * @param channelList
     * @return
     */
    @GetMapping(value = "/api/batch/store/channel/mapping/list")
    @Timed
    ResponseEntity<List<StoreChannelMappingDTO>> list(@RequestParam(value = "businessIdList",required = false)Long businessId,@RequestParam(value = "channelMappingStoreIdList",required = false) List<Long> channelMappingStoreIdList,
    		@RequestParam(value = "channelList",required = false) List<Integer> channelList, @RequestParam(value = "channelStoreIdList",required = false)List<Long> channelStoreIdList);

    /**
     *   通过ComId获取门店
    *  @params:  * @param null
    *  @Author:  fwyang
    *  @Description:
    *  @version: 1.0
    *  @Date: 15:16 2024/1/30
    */
    @GetMapping(value = "/api/mdmStoreBase/findMdmStoreByComId")
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findMdmStoreByComId(@RequestParam(value = "comId")String comId,@RequestParam(value = "mdmFlag",required = false) Boolean mdmFlag);
    
    /**
     * 根据连锁id查询连锁信息
     *
     * @param businessId
     */
    @GetMapping("/api/internal/findMdmBusinessBaseByBusinessIds")
    @Timed
    ResponseEntity<List<MdmBusinessBaseDTO>> findMdmBusinessBaseByBusinessIds(@RequestParam("businessIds") List<Long> businessIds);
    
    /**
     * 根据连锁id查询连锁信息
     *
     * @param businessId
     */
    @GetMapping("/api/internal/findMdmBusinessBaseByBusinessIds")
    @Timed
    ResponseEntity<List<MdmBusinessBaseDTO>> findMdmBusinessBaseByBusinessIds(@RequestParam("businessIds") List<Long> businessIds,@RequestParam(value = "bizCode") String bizCode ,@RequestParam(value = "bizType") Integer bizType);
    
    /**
     * 根据门店id与扩展标识获取门店主数据信息
     *
     * @param businessId
     */
    @PostMapping(value = "/api/internal/mdmStoreBase/findStoreByStoreIdsAndExtend")
    @Timed
    ResponseEntity<List<MdmStoreBaseDTO>> findStoreByStoreIdsAndExtend(@RequestParam("storeIds") List<Long> storeIds,@RequestParam("extendFlag") Boolean extendFlag);
}
