package com.cowell.pricecenter.service.feign.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 价格VO
 */
@Data
public class PriceDetailVO implements Serializable{
	
	private static final long serialVersionUID = -5907646736358922077L;

	@ApiModelProperty("机构类型，100集团，300平台，500连锁，700片区")
    private Integer orgType;

    @ApiModelProperty("查询类型，1按照orgId查询，2按照orgOutId查询")
    private Integer queryType;

    @ApiModelProperty("orgId")
    private Long orgId;

    @ApiModelProperty("orgOutId")
    private Long orgOutId;

    @ApiModelProperty("商品编码")
    private String goodsNo;

    @ApiModelProperty("众数（零售价）")
    private BigDecimal modePrice;

    @ApiModelProperty("众数（会员价）")
    private BigDecimal modeMemberPrice;

    @ApiModelProperty("企业下门店总数量")
    private Integer storeQuantity;

    @ApiModelProperty("众数（拆零金额）")
    private BigDecimal scatteredPrice;

    @ApiModelProperty("众数（会员拆零金额）")
    private BigDecimal scatteredMemberPrice;

    @ApiModelProperty("众数成本价")
    private BigDecimal modePriceCost;
    
    @ApiModelProperty("最后一次采购价")
    private BigDecimal lastPurchasingPrice;
    
    @ApiModelProperty("建议价格")
    private BigDecimal suggestPrice;
}
