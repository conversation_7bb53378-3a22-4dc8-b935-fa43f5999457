package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.OmsO2oConfigDetailDTO;
import com.cowell.pricecenter.service.dto.OrderChannelTypeVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * 对比商品价格
 *
 * <AUTHOR>
 * @date 2018/5/22
 */
@FeignClient(name = "bam")
public interface BamFeignService {


    @GetMapping("/api/internal/compare/price/businessIdAndStoreIdAndGoods")
    @Timed
    ResponseEntity<OmsO2oConfigDetailDTO> queryBJStoreGoodsPriceInternal(@RequestParam(value = "businessId")Long businessId,
                                                                         @RequestParam(value = "storeId")Long storeId,
                                                                         @RequestParam(value = "goodsNos") List<String> goodsNos);


}
