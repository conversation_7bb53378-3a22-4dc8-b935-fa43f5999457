package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.B2cGoodsPriceInfoDTO;
import com.cowell.pricecenter.service.dto.PriceLimitControlWarnDTO;
import com.cowell.pricecenter.service.dto.response.CommonRes;
import com.cowell.pricecenter.service.dto.response.PriceOrgGoodsDTO;
import com.cowell.pricecenter.service.dto.response.PriceStoreDetailVo;
import com.cowell.pricecenter.service.dto.response.RowsDTO;
import com.cowell.pricecenter.service.dto.response.RowsItemDTO;
import com.cowell.pricecenter.service.dto.response.RowsResult;
import com.cowell.pricecenter.service.dto.response.amis.AmisCommonResponse;
import com.cowell.pricecenter.service.dto.response.amis.CommonResult;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;

import io.swagger.annotations.ApiOperation;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;


@FeignClient(name = "nyuwa-erp")
public interface NyuwaErpService {

    @ApiOperation(value = "b2c商品成本信息查询")
    @GetMapping(value = {"/api/intranet/mdd/218/b2c_goods_price_info/list"})
    @Timed
    AmisCommonResponse<PageResult<B2cGoodsPriceInfoDTO>> getBusinessPrice(@RequestParam("business_id") Long businessId,
                                                                        @RequestParam("goods_id") String goodsNo,
                                                                          @RequestParam("channel_id") Integer channelId,
                                                                        @RequestParam(value = "page") Integer page,
                                                                        @RequestParam(value = "perPage") Integer pageSize);

    @ApiOperation(value = "查询门店价格")
    @GetMapping(value = {"/api/internal/general/218/efbe6bdd26fe48ce855522586b39e457"})
    @Timed
    ResponseEntity<CommonRes<PageResult<PriceStoreDetailVo>>> getPriceStoreDetailList(@RequestParam Map<String, Object> map);

    @ApiOperation(value = "根据门店id、商品编码、价格类型、渠道id查询门店id(去重)")
    @GetMapping(value = {"/api/internal/general/218/110b888f749841349ec2927b069173b1"})
    @Timed
    ResponseEntity<CommonRes<RowsResult<RowsDTO>>> getStoreIdByGoodsNoAndPriceTypeAndChannelId(@RequestParam Map<String, Object> map);

    @ApiOperation(value = "根据商品编码查询调价单号（去重）")
    @GetMapping(value = {"/api/internal/general/218/80c9e33eb86143069caf7b2e74c71920"})
    @Timed
    ResponseEntity<CommonRes<RowsResult<RowsDTO>>> getAdjustCodeListByGoodsNo(@RequestParam Map<String, Object> map);

    @ApiOperation(value = "根据商品编码等条件查询定调价商品目录")
    @GetMapping(value = {"/api/internal/general/218/bea974efc1e6421081c24cebab6b7152"})
    @Timed
    ResponseEntity<CommonRes<PageResult<PriceOrgGoodsDTO>>> getPriceOrgGoodsList(@RequestParam Map<String, Object> map);

    @ApiOperation(value = "查询医保管控预警")
    @GetMapping(value = "/api/intranet/mdd/218/price_limit_control_warn/list")
    ResponseEntity<CommonRes<PageResult<PriceLimitControlWarnDTO>>> getPriceLimitControlWarnList(@RequestParam Map<String, Object> map);
}
