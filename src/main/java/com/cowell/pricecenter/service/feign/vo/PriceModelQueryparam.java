package com.cowell.pricecenter.service.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @program: pricecenter
 * @description: 价格众数查询参数
 * @author: jmlu
 * @create: 2022-04-07 10:53
 **/

@Data
public class PriceModelQueryparam implements Serializable {

    private static final long serialVersionUID = 4131715354994684190L;


    @ApiModelProperty("商品编码")
    private String goodsNo;

    @ApiModelProperty("机构信息")
    private List<PriceModelQueryOrgParam> orgParams;

    public PriceModelQueryparam() {

    }

    public PriceModelQueryparam(String goodsNo, List<PriceModelQueryOrgParam> orgParams) {
        this.goodsNo = goodsNo;
        this.orgParams = orgParams;
    }
}
