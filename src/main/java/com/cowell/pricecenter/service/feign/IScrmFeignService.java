package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.request.FileDownloadTaskDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = "scrm")
public interface IScrmFeignService {

    @RequestMapping(value = "/api/noauth/fileDownloadTask", method = RequestMethod.POST)
    @Timed
    @ApiOperation(value = "新建文件下载任务")
    FileDownloadTaskDTO createFileDownloadTask(@RequestBody FileDownloadTaskDTO fileDownloadTaskDTO);

    @RequestMapping(value = "/api/noauth/fileDownloadTask", method = RequestMethod.PUT)
    @Timed
    @ApiOperation(value = "更新文件下载任务 ")
    FileDownloadTaskDTO updateFileDownloadTask(@RequestBody FileDownloadTaskDTO fileDownloadTaskDTO);

    @RequestMapping(value = "/api/fileDownloadTask/{id}", method = RequestMethod.GET)
    @Timed
    @ApiOperation(value = "查询下载文件 ")
    FileDownloadTaskDTO fileDownloadTask(@PathVariable("id") Long id);
}
