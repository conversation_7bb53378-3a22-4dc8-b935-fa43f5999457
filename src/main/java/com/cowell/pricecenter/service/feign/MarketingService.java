package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.feign.vo.PriceModelQueryparam;
import com.cowell.pricecenter.service.dto.request.NewSpuQueryParam;
import com.cowell.pricecenter.service.dto.response.CommonNewSpuVo;
import com.cowell.pricecenter.service.feign.vo.PriceModeResult;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @program: pricecenter
 * @description: 促销系统提供的服务
 * @author: jmlu
 * @create: 2022-04-07 10:32
 **/

@FeignClient(name = "marketing")
public interface MarketingService {

    /**
     * 获取商品在组织机构中价格众数
     */
    @RequestMapping(value = "/api/internal/thirdParty/common/getOrgPriceByGoodsNo", method = RequestMethod.POST)
    @Timed
    ResponseEntity<PriceModeResult> getPriceModelByGoodsnoAndOrgs(@RequestBody PriceModelQueryparam priceModelQueryparam);


    /**
     * 批量获取价格众数
     */
    @RequestMapping(value = "/api/internal/thirdParty/common/getOrgPriceByGoodsNoList", method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<PriceModeResult>> getPriceModelByGoodsNoListAndOrgs(@RequestBody List<PriceModelQueryparam> priceModelQueryParams);
    
    /**
     * 查询集团商品信息
     */
    @RequestMapping(value = "/api/promotion/goodsRule/searchGoods", method = RequestMethod.GET)
    @Timed
    List<CommonNewSpuVo> getCommonNewSpuVo(@RequestParam Map<String, Object> map);


}
