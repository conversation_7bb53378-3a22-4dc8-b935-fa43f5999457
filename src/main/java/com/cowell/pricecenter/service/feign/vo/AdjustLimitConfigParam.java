package com.cowell.pricecenter.service.feign.vo;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: pricecenter
 * @description: queryPromotionBusinessConfig方法参数
 * @author: jmlu
 * @create: 2022-12-11 11:44
 **/

@Getter
@Setter
@ToString
public class AdjustLimitConfigParam {

    private Long businessId;

    /**
     * 配置类型， 见PromotionConfigTypeEnum
     */
    private int configType;
    
    private List<String> goodNoList;

    public static AdjustLimitConfigParam getInstance(Long businessId, int configType,List<String> goodNoList) {
        AdjustLimitConfigParam param = new AdjustLimitConfigParam();
        param.setBusinessId(businessId);
        param.setConfigType(configType);
        param.setGoodNoList(goodNoList);
        return param;
    }

}
