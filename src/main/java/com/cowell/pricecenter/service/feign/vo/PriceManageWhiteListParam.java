package com.cowell.pricecenter.service.feign.vo;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * @ClassName:  PriceManageWhiteListParam   
 * @Description:queryPriceManageWhiteList方法参数  
 * @author: my
 * @date:   2023年1月5日 上午10:21:39      
 * @Copyright:
 */

@Getter
@Setter
@ToString
public class PriceManageWhiteListParam {

    /**
     * 配置类型， 见PriceManageWhiteListEnum
     */
    private int whitelistType;
    
    private List<Long> businessIdList;
    
    private Long userId;

}
