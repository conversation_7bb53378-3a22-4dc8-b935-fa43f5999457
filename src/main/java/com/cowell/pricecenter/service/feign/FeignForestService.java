package com.cowell.pricecenter.service.feign;

import java.util.List;
import java.util.Map;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.cowell.pricecenter.service.dto.PropertyParamDto;

@FeignClient(name = "forest")
public interface FeignForestService {
	/**
     * 查询商品编码对应spu集合
     * @param
     * @return
     */
    @PostMapping("/api/internal/spu/property")
    ResponseEntity<Map<String, Map<String,Object>>> getProperty(@RequestBody List<PropertyParamDto> propertyParamDtoList);
}
