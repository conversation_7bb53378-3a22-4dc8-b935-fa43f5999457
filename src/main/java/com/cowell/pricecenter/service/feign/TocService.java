package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.feign.vo.DistributedIDResultVO;
import java.util.List;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @program: pricecenter
 * @description TOC服务，包含分发器
 * @author: jmlu
 * @create: 2022-04-01 13:44
 **/

@FeignClient(name="toc")
public interface TocService {

    /**
     * 获取一个ID
     * @param biz
     * @return
     */
    @GetMapping("/api/internal/getId")
    @Timed
    ResponseEntity<DistributedIDResultVO> getNextId(@RequestParam("biz") String biz);

    /**
     * 批量获取ID列表，count最大数量支持200，超过后将抛异常
     * @param biz
     * @param count
     * @return
     */
    @GetMapping("/api/internal/getIdBatch")
    @Timed
    ResponseEntity<List<Long>> getNextIdBatch(@RequestParam("biz") String biz, @RequestParam("count") Integer count);

}
