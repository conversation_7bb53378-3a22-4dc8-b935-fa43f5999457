package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.request.StockGoodsBatchCodeParam;
import com.cowell.pricecenter.service.dto.response.StockGoodsBatchCodeDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * POS-feign客户端服务类
 * @Author: Susu
 * @Description:
 * @Date: 2021/6/7 14:59
 * com.cowell.pricecenter.service.feign
 */
@FeignClient(name = "pos")
public interface PosFeignService {

    /**
     *  价签管理查询
     * @param param
     * @return
     */
    @RequestMapping(value = "/api/internal/StockGoodsBatchCode/es/getPriceTagInfo", method = RequestMethod.POST)
    @Timed
    PageResponse<List<StockGoodsBatchCodeDTO>> getPriceTagInfo(@RequestBody StockGoodsBatchCodeParam param);
}
