package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.config.CustomOAuth2FeignConfiguration;
import com.cowell.pricecenter.service.dto.BackLogRespose;
import com.cowell.pricecenter.service.dto.request.BackLogCreateParam;
import com.cowell.pricecenter.service.dto.response.BacklogTypeDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "digitalstore",configuration = CustomOAuth2FeignConfiguration.class)
public interface DigitalstoreService {

    @ApiOperation(value = "创建待办事项", notes = "创建待办事项")
    @PostMapping("/api/internal/backlog/createBacklog")
    @Timed
    ResponseEntity<BackLogRespose> createBacklog(@RequestBody BackLogCreateParam createParam);

    /**
     * 根据AppId/待办类型编码获取待办事项类型配置信息
     * @param appId appId
     * @param typeCode 待办类型编码
     * @return 类型配置信息list
     */
    @GetMapping("/api/internal/backlogType/getListByAppId")
    @Timed
    ResponseEntity<List<BacklogTypeDTO>> getListByAppId(@RequestParam("appId") String appId, @RequestParam("typeCode") String typeCode);


}
