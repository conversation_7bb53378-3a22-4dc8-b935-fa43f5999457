package com.cowell.pricecenter.service.feign.vo;

import com.cowell.pricecenter.service.dto.GoodsClassification;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * @program: pricecenter
 * @description: SearchApi的商品信息查询结果对象
 * @author: jmlu
 * @create: 2022-03-28 14:27
 **/

@Data
public class SpuListVO implements Serializable {

    private Long id;

    /**
     *通用名名称
     */
    private String curName;

    /**
     * 商品名称
     */
    private String name;
    /**
     * 规格
     */
    private String jhiSpecification;

    /**
     * 生产厂商
     */
    private String producter;

    /**
     * 商品名助记码
     */
    private String curOpCode;

    /**
     * 剂型
     */
    private String dosageForms;

    /**
     * 图片
     */
    private String picUrl;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 商品助记码
     */
    private String opCode;

    /**
     * 高济商品唯一编码
     */
    private String goodsNo;

    /**
     * 批准文号
     */
    private String apprdocno;

    /**
     * 商品大类
     */
    private String groupno;

    /**
     * 商品中类
     */
    private String subclass;

    /**
     * 类推等级
     */
    private String pushlevel;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 : 正常 -1 : 删除
     */
    private Integer status;

    /**
     * 单位
     */
    private String unit;

    /**
     * 产地
     */
    private String habitat;

    /**
     * 分类子类
     */
    private String categoryId;

    /**
     * 商品属性 新冠标签
     */
    private String covidType;

    /**
     * 生产厂商
     */
    private String factoryid;
    /**
     * 单位
     */
    private String goodsunit;
    /**
     * 产地
     */
    private String prodarea;

    /**
     * DTPgood
     */
    @JsonProperty("DTPgood")
    private String DTPgood;


    public SpuNewVo toSpuNewVO() {
        SpuNewVo spuNewVo = new SpuNewVo();
        BeanUtils.copyProperties(this, spuNewVo);
        spuNewVo.setFactoryid(this.getProducter());
        spuNewVo.setDosageformsid(this.getDosageForms());
        spuNewVo.setGoodsunit(this.getUnit());
        spuNewVo.setProdarea(this.getHabitat());
        return spuNewVo;
    }

}
