package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.GoodsDatailReqDTO;
import com.cowell.pricecenter.service.dto.ItemPriceSync;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import java.util.List;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 调用itemcenter
 *
 * @author: liubw
 * @date 2018/10/29 8:22 PM
 */

@FeignClient(name = "itemcenter")
public interface ItemFeignService{

    /**
     * 营销分类列表
     *
     * @param businessId，parentId,type0是营销分离 查询参数
     */
    @RequestMapping(value = "/api/noauth/category/list", method = RequestMethod.GET)
    @Timed
    List<SaleCategoryDTO> categoryList(@RequestParam(value = "businessId") long businessId,
                                       @RequestParam(value = "parentId") long parentId);

    /**
     * 获取当前分类的最顶层父类
     *
     * @param categoryId 查询参数
     */
    @RequestMapping(value = "/api/noauth/category/getTopCategory", method = RequestMethod.GET)
    @Timed
    SaleCategoryDTO getTopCategory(@RequestParam(value = "categoryId") long categoryId);

    /**
     * 获取类目下末级分类关联的商品唯一码
     *
     * @param businessId
     * @param categoryId
     * @return
     */
    @RequestMapping(value = "/api/noauth/getitemunionids", method = RequestMethod.GET)
    @Timed
    ResponseEntity<List<String>> getitemunionids(@RequestParam(value = "businessId") long businessId,
                                                 @RequestParam(value = "categoryId") long categoryId);


    @RequestMapping(value = "/api/internal/item/queryStoreGoodsItemId", method = RequestMethod.POST)
    Long queryStoreGoodsItemId(@RequestBody GoodsDatailReqDTO goodsDatailReqDTO);



    @RequestMapping(value = "/api/noauth/item/getStoreItemPrice", method = RequestMethod.GET)
    @Timed
    ResponseEntity<List<ItemPriceSync>> getStoreItemPrice(@RequestParam(value = "businessId") long businessId,
                                                          @RequestParam(value = "storeId") long storeId);

}
