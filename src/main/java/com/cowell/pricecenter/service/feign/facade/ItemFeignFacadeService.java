package com.cowell.pricecenter.service.feign.facade;

import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.dto.response.SaleCategoryDTO;
import com.cowell.pricecenter.service.feign.ItemFeignService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 商品中台feign客户端（门户服务类）
 * @Author: Susu
 * @Description:
 * @Date: 2021/6/7 18:11
 * com.cowell.pricecenter.service.feign.facade
 */
@Slf4j
@Service
public class ItemFeignFacadeService {
    /**
     * redis存储时间
     */
    public static final int REDIS_TIME = 10;
    /**
     * redis的key前缀
     */
    private static final String PRICE_TAG_INFO = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.PRICE_TAG_SYNC_KEY;
    /**
     * 营销分类缓存key
     */
    private static final String TOP_CATEGORY = "_TOP_CATEGORY_";
    @Autowired
    private ItemFeignService itemFeignService;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 根据分类id获取分类的信息
     * (缓存机制)
     * @param saleCategoryId
     * @return
     */
    public SaleCategoryDTO getSaleCategoryDTO(Long saleCategoryId) {
        try {
            log.info(" 获取当前分类的最顶层父类入参:{}", saleCategoryId);
            // 判断是否为空值
            if(saleCategoryId==null){
                return null;
            }
            String key =  TOP_CATEGORY + saleCategoryId;
            SaleCategoryDTO dto = null;
            RBucket<String> bucket = redissonClient.getBucket(PRICE_TAG_INFO + key);
            String jsonStr = bucket.get();
            if(StringUtils.isNotEmpty(jsonStr)){
                dto = JSONObject.parseObject(jsonStr, SaleCategoryDTO.class);

            }
            if(dto!=null){
                return dto;
            }
            dto = itemFeignService.getTopCategory(saleCategoryId);
            log.info(" 获取当前分类的最顶层父类返回:{}", dto);
            if(dto!=null){
                bucket.set(JSONObject.toJSONString(dto), REDIS_TIME, TimeUnit.MINUTES);
            }
            return dto;
        } catch (Exception e) {
            log.warn("getSaleCategoryDTO",e);
        }
        return null;
    }

}
