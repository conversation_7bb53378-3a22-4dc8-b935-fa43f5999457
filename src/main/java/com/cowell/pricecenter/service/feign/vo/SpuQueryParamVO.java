package com.cowell.pricecenter.service.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @program: pricecenter
 * @description: SearchApi的商品信息查询参数
 * @author: jmlu
 * @create: 2022-03-28 14:24
 **/

@Data
public class SpuQueryParamVO implements Serializable {

    private static final long serialVersionUID = 2166282670096438145L;

    @ApiModelProperty("当前页数：默认1")
    private int page = 1;

    @ApiModelProperty("显示数据条数：默认10")
    private int pageSize = 10;

    @ApiModelProperty("根据商品编码/通用名/通用名助记码查询")
    private String keyWord;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("spuIds")
    private List<Long> spuIds;

    @ApiModelProperty("goodsNoList")
    private List<String> goodsNoList;

    @ApiModelProperty("barCode")
    private String barCode;

}
