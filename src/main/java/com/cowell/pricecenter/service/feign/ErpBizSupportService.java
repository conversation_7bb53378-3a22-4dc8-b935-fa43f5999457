package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.JoinStoreGoodsInfoDTO;
import com.cowell.pricecenter.service.dto.MakePriceSuggestResultDTO;
import com.cowell.pricecenter.service.dto.request.AdjustPriceHistoryRecordDTO;
import com.cowell.pricecenter.service.dto.request.MDMStoreGroupDTO;
import com.cowell.pricecenter.service.dto.request.PriceOrgGoodsParam;
import com.cowell.pricecenter.service.feign.vo.AdjustLimitConfigParam;
import com.cowell.pricecenter.service.feign.vo.AdjustLimitConfigRow;
import com.cowell.pricecenter.service.feign.vo.PriceManageWhiteListParam;
import com.cowell.pricecenter.service.feign.vo.PriceManageWhiteListVO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @program: pricecenter
 * @description: erp-biz-support提供的服务
 * @author: jmlu
 * @create: 2022-12-11 11:40
 **/


@FeignClient(name = "erp-biz-support")
public interface ErpBizSupportService {

    /**
     * 商品调价限制配置接口
     * @return
     */
    @RequestMapping(value = "/api/internal/price/businessConfig/queryPromotionBusinessConfig", method = {RequestMethod.POST})
    @ResponseBody
    @Timed
    ResponseEntity<List<AdjustLimitConfigRow>> queryAdjustLimitConfig(@RequestBody AdjustLimitConfigParam param);

    /**
     * 调价管控用户机构白名单配置接口
     * @return
     */
    @RequestMapping(value = "/api/internal/price/queryPosPriceManageWhileList", method = {RequestMethod.POST})
    @ResponseBody
    @Timed
    ResponseEntity<List<PriceManageWhiteListVO>> queryPriceManageWhiteList(@RequestBody PriceManageWhiteListParam param);

    /**
     * 批量保存价格调整记录汇总接口
     * @return
     */
    @RequestMapping(value = "/api/internal/price/saveAdjustPriceHistoryRecord/version/2.0", method = {RequestMethod.POST})
    @ResponseBody
    @Timed
    ResponseEntity saveAdjustPriceHistoryRecord(@RequestBody List<AdjustPriceHistoryRecordDTO> list);

    /**
     * 根据调价单号删除价格调整记录汇总
     * @param adjustCode
     * @return
     */
    @RequestMapping(value = "/api/internal/price/hisRecord/deleBatchByAdjustCode/version/2.0", method = {RequestMethod.GET})
    @ResponseBody
    @Timed
    ResponseEntity deleBatchByAdjustCode(@RequestParam("adjustCode") String adjustCode);

    /**
     * 定调价根据连锁id和商品 编码查询定价建议价格
     * @return
     */
    @RequestMapping(value = "/api/internal/price/selectConfirmPriceByBusinessGoodsNo", method = {RequestMethod.GET})
    @ResponseBody
    @Timed
    ResponseEntity<List<MakePriceSuggestResultDTO>> selectConfirmPriceByBusinessGoodsNo(@RequestParam(value = "businessIdList")  List<Long> businessIdList, @RequestParam(value = "goodsNoList")  List<String> goodsNoList);


    /**
     * mdm敏感品 根据实体ID列表获取实体列表
     * @return
     */
    @RequestMapping(value = "/api/internal/price/mdm/getEntityListByTypeAndEntityIdList", method = {RequestMethod.POST})
    @ResponseBody
    @Timed
    ResponseEntity<List<String>> getEntityListByTypeAndEntityIdList(@RequestBody MDMStoreGroupDTO param);

    /**
     * mdm敏感品 根据实体ID列表获取实体引用列表
     * @param param
     * @return
     */
    @RequestMapping(value = "/api/internal/price/mdm/getEntityMapByTypeAndEntityIdList", method = {RequestMethod.POST})
    @ResponseBody
    @Timed
    ResponseEntity<Map<String, List<String>>> getEntityMapByTypeAndEntityIdList(@RequestBody MDMStoreGroupDTO param);

    /**
     * 批量更新价格限制预警的调价单状态
     * @param idListe
     * @return
     */
    @RequestMapping(value = "/api/internal/limitControlWarn/updatePriceLimitControlWarnAdjustStatusByIds", method = {RequestMethod.GET})
    @ResponseBody
    @Timed
    ResponseEntity updatePriceLimitControlWarnAdjustStatusByIds(@RequestParam("idList") List<Long> idListe);

    /**
     * 根据门店id和商品编码集合查询加盟店商品信息
     * @return
     */
    @RequestMapping(value = "/api/internal/getJoinStoreGoodsList", method = {RequestMethod.POST})
    @ResponseBody
    @Timed
    ResponseEntity<List<JoinStoreGoodsInfoDTO>> getJoinStoreGoodsList(@RequestBody PriceOrgGoodsParam param);
}
