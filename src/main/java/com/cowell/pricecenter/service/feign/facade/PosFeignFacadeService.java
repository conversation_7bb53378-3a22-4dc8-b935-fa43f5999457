package com.cowell.pricecenter.service.feign.facade;

import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.request.StockGoodsBatchCodeParam;
import com.cowell.pricecenter.service.dto.response.StockGoodsBatchCodeDTO;
import com.cowell.pricecenter.service.feign.PosFeignService;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * POS项目feign客户端（门户服务类）
 * @Author: Susu
 * @Description:
 * @Date: 2021/6/7 18:08
 * com.cowell.pricecenter.service.feign.facade
 */
@Slf4j
@Service
public class PosFeignFacadeService {

    @Autowired
    private PosFeignService posFeignService;

    /**
     * 通过查询参数分页获取结果集
     * @param param
     * @return
     */
    private PageResponse<List<StockGoodsBatchCodeDTO>> getPriceTagInfo(StockGoodsBatchCodeParam param){
        try {
            if(param==null){
                return null;
            }
            Long businessId = param.getBusinessId();
            Long storeId = param.getStoreId();
            List<Long> storeIdList = param.getStoreIdList();
            if(businessId==null){
                return null;
            }
            if(storeId==null&& CollectionUtils.isEmpty(storeIdList)){
                return null;
            }
            return posFeignService.getPriceTagInfo(param);
        } catch (Exception e) {
            log.warn("PosFeignFacadeService.getPriceTagInfo",e);
        }
        return null;
    }

    /**
     * 通过查询参数分页获取结果集合
     * @param businessId
     * @param storeId
     * @param keyWord
     * @param marketCateName
     * @param stockBeyondZero
     * @param goodsPositionCodeList
     * @param priceMax
     * @param priceMin
     * @param pushlevel
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageResponse<List<StockGoodsBatchCodeDTO>> getPriceTagInfoList(Long businessId,
                                                                          Long storeId,
                                                                          String keyWord,
                                                                          String marketCateName,
                                                                          Integer stockBeyondZero,
                                                                          List<String> goodsPositionCodeList,
                                                                          String priceMax,
                                                                          String priceMin,
                                                                          String pushlevel, Integer pageNum,
                                                                          Integer pageSize){

        try {
            StockGoodsBatchCodeParam param = new StockGoodsBatchCodeParam();
            // 连锁id
            param.setBusinessId(businessId);
            // 门店id
            param.setStoreId(storeId);
            // 商品编码、通用名、条形码
            param.setKeyWord(keyWord);
            // 营销分类名称，营销分类是连接符的中文，只能使用中文
            param.setMarketCateName(marketCateName);
            // 库存数量是否大于0
            param.setStockBeyondZero(stockBeyondZero);
            // 货位编码
            param.setGoodsPositionCodeList(goodsPositionCodeList);
            // es存储的零售价是实际零售价的百倍，so乘以100传递参数
            try {
                if(!StringUtils.isEmpty(priceMin)){
                    priceMin = BigDecimalUtils.convertFenByYuan(priceMin).toString();
                }
                if(!StringUtils.isEmpty(priceMax)){
                    priceMax = BigDecimalUtils.convertFenByYuan(priceMax).toString();
                }
            } catch (Exception e) {
                log.warn("getPriceTagInfoList",e);
            }
            // 零售价
            param.setPriceMax(priceMax);
            param.setPriceMin(priceMin);
            // 销售属性
            param.setPushlevel(pushlevel);
            // 分页参数
            param.setPageNum(pageNum);
            param.setPageSize(pageSize);

            PageResponse<List<StockGoodsBatchCodeDTO>> priceTagInfo = getPriceTagInfo(param);

            return priceTagInfo;
        } catch (Exception e) {
            log.warn("getPriceTagInfoList",e);
        }
        return null;
    }


}
