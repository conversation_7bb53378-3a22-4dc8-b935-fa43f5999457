package com.cowell.pricecenter.service.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 
 * @ClassName:  PriceManageWhitelistVO   
 * @Description:调价管控用户机构白名单
 * @author: my
 * @date:   2023年1月5日 上午10:15:26      
 * @Copyright:
 */
@Getter
@Setter
@ToString
public class PriceManageWhiteListVO {
	
	@ApiModelProperty("白名单类型 1用户白名单 2机构白名单")
    private Integer  whitelistType;
	/**
     * 连锁Id
     */
    @ApiModelProperty("连锁Id")
	private Long businessId;
    /**
     * 用户Id
     */
    @ApiModelProperty("用户Id")
    private Long userId;
}
