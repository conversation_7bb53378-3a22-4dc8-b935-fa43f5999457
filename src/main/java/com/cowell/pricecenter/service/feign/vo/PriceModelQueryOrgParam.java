package com.cowell.pricecenter.service.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

/**
 * @program: pricecenter
 * @description: 价格众数查询组织参数
 * @author: jmlu
 * @create: 2022-04-07 10:55
 **/

@Data
public class PriceModelQueryOrgParam implements Serializable {

    private static final long serialVersionUID = -8245807369153757993L;

    @ApiModelProperty("机构类型，100集团，300平台，500连锁，700片区")
    private Integer orgType;

    @ApiModelProperty("orgId")
    private Long orgId;

    @ApiModelProperty("orgOutId")
    private Long orgOutId;

    public PriceModelQueryOrgParam(Integer orgType, Long orgId, Long orgOutId) {
        this.orgType = orgType;
        this.orgId = orgId;
        this.orgOutId = orgOutId;
    }

    public PriceModelQueryOrgParam(Long orgId) {
        this.orgId = orgId;
    }
}
