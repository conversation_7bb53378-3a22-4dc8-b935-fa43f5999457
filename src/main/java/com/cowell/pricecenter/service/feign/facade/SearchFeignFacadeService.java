package com.cowell.pricecenter.service.feign.facade;

import com.beust.jcommander.internal.Lists;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.service.dto.request.ForestGoodsParam;
import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.request.PriceItemSkuParamVo;
import com.cowell.pricecenter.service.dto.response.ForestGoodsDTO;
import com.cowell.pricecenter.service.dto.response.PageResult;
import com.cowell.pricecenter.service.dto.response.PriceItemSkuVo;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.web.rest.errors.BusinessException;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * ES服务feign客户端（门户服务类）
 * @Author: Susu
 * @Description:
 * @Date: 2021/6/7 18:14
 * com.cowell.pricecenter.service.feign.facade
 */
@Slf4j
@Service
public class SearchFeignFacadeService {

    @Autowired
    private SearchService searchService;

    @Autowired
    private ItemSearchEngineFacadeService itemSearchEngineFacadeService;


    /**
     * 通过商品编码获取价签商品集合
     * @param businessId
     * @param storeId
     * @param goodsNoList
     * @return
     */
    public List<PriceItemSkuVo> getPriceItemSkuVoList(Long businessId, Long storeId, List<String> goodsNoList) {
        PriceItemSkuParamVo skuParamVo = new PriceItemSkuParamVo();
        skuParamVo.setBusinessId(businessId);
        skuParamVo.setStoreId(storeId);
        skuParamVo.setGoodsNoList(goodsNoList);
        skuParamVo.setPageSize(goodsNoList.size());
        skuParamVo.setPage(1);
        List<Long> goodsNos = goodsNoList.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        List<PriceItemSkuVo> itemSkuVoList = Lists.newArrayList();
        try {
            log.info("ES搜索商品信息参数:{}", skuParamVo);
            PageResult<SpuNewVo> rep = itemSearchEngineFacadeService.getNewSpuList(businessId.longValue(), goodsNos);
            if (null != rep && org.apache.commons.collections.CollectionUtils.isNotEmpty(rep.getRows())) {
            	itemSkuVoList = rep.getRows().stream().map(v -> {
            		PriceItemSkuVo record = new PriceItemSkuVo();
                    BeanUtils.copyProperties(v, record);
                    return record;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("ES搜索商品信息异常,", e);
            throw new BusinessException(ReturnCodeEnum.GET_ES_RESULT_ERROR);
        }
        return itemSkuVoList;
    }

    /**
     * 授权目录分页查询
     * @param goodsNoList
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageResponse<List<ForestGoodsDTO>> pageForestGoodsDTOList(List<String> goodsNoList,Integer pageNum,Integer pageSize) {
        try {
            ForestGoodsParam searchParam = new ForestGoodsParam();
            searchParam.setGoodsNoList(goodsNoList);
            searchParam.setPage(pageNum);
            searchParam.setPageSize(pageSize);
            PageResponse<List<ForestGoodsDTO>> response = itemSearchEngineFacadeService.getSupAllList(searchParam);
            List<ForestGoodsDTO> goodsList = response.getResult();
            return response;
        } catch (Exception e) {
            log.warn("pageForestGoodsDTOList",e);
        }
        return null;
    }

    /**
     * 通过商品编码获取商品集合
     * @param goodsNoList
     * @return
     */
    public List<ForestGoodsDTO> getForestGoodsDTOList(List<String> goodsNoList) {
        if(CollectionUtils.isEmpty(goodsNoList)){
            return null;
        }
        Integer pageNum = 1;
        Integer pageSize = goodsNoList.size();
        PageResponse<List<ForestGoodsDTO>> response = pageForestGoodsDTOList(goodsNoList,pageNum,pageSize);
        if(response==null){
            return null;
        }
        List<ForestGoodsDTO> goodsList = response.getResult();
        return goodsList;
    }

}

