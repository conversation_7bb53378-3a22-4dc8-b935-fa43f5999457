package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.permission.dto.EmployeeDetailDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.permission.dto.QueryOrgDTO;
import com.cowell.pricecenter.client.AuthorizedFeignClient;
import com.cowell.pricecenter.service.dto.request.RequestBodyDTO;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * erpSaas服务
 */
@FeignClient(name = "erpsaas")
public interface ErpSaasService {

    /**
     * 提交OA申请记录/SAP单据
     */
    @RequestMapping(value = "/api/noauth/erp/accept", method = RequestMethod.POST)
    @Timed
    ResponseEntity<String> accept(@RequestBody RequestBodyDTO requestBodyDTO);

    /**
     * 消费erpsaas消息成功，回调方法
     *
     * @param id
     * @return
     */
    @GetMapping("/api/internal/process/data/{id}")
    public ResponseEntity<Boolean> confirmProcessComplete(@PathVariable("id") Long id);


}
