package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.entity.AlertContent;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2021/12/17 17:49
 */
@FeignClient(name = "weblog")
public interface WebLogFeignService {

    @PostMapping("/api/intranet/alert")
    @Timed
    String alert(@RequestBody AlertContent content);
}
