package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.StockGoodsBatchCodeSimpleInfo;
import com.cowell.pricecenter.service.dto.StockGoodsPagableQueryParam;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "stockcenter-api")
public interface StockcenterFeignClient {

    @ApiOperation(value = "查询门店商品库存", notes = "查询门店商品库存")
    @PostMapping(value = "/api/internal/stock/goodsBatchCode/page")
    @Timed
    public ResponseEntity<PageInfo<StockGoodsBatchCodeSimpleInfo>> goodsBatchCodePage(@RequestBody StockGoodsPagableQueryParam param);

}
