package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.request.SapUnifyRequestDTO;
import com.cowell.pricecenter.service.dto.response.SapCommonResultDTO;
import com.cowell.pricecenter.service.dto.response.SapUnifyResponseDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "purchase-data")
public interface IPurchaseDataFeignService {

    @Timed
    @ApiOperation(value = "SAP-RFC统一调用", notes = "SAP-RFC统一调用")
    @PostMapping("/api/noauth/sap/rfcConnect")
    ResponseEntity<SapCommonResultDTO<SapUnifyResponseDTO>> rfcConnect(@RequestBody SapUnifyRequestDTO sapUnifyRequest);
}
