package com.cowell.pricecenter.service.feign;

import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.GoodsDatailReqDTO;
import com.cowell.pricecenter.service.dto.GoodsMarketCateQueryVO;
import com.cowell.pricecenter.service.dto.ItemSkuQueryApiDTO;
import com.cowell.pricecenter.service.dto.MarketCateVo;
import com.cowell.pricecenter.service.dto.response.ItemSkuVo;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/9 10:26
 */
@FeignClient(name = "item-center-cp-sync")
public interface ItemCenterCpSyncService {
	@RequestMapping(value = "/api/internal/sku/page/query", method = RequestMethod.POST, headers = {"bizCode=B2C"})
    @Timed
    ResponseEntity<PageResult<ItemSkuVo>> query(@RequestBody ItemSkuQueryApiDTO itemSkuQuery);

	@RequestMapping(value = "/api/internal/sync/item/query", method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<ItemSkuVo>> queryStoreGoodsItemId(@RequestBody GoodsDatailReqDTO goodsDatailReqDTO);

    @RequestMapping(value = "/api/noauth/marketing/category/query", method = RequestMethod.POST)
    @Timed
    ResponseEntity<PageResult<MarketCateVo>> getMarketingCateItemByItemUnionId(@RequestBody GoodsMarketCateQueryVO goodsMarketCateQueryVO);

    @RequestMapping(value = "/api/internal/sku/query", method = RequestMethod.POST)
    @Timed
    ResponseEntity<List<ItemSkuVo>> querySkuByParam(@RequestBody ItemSkuQueryApiDTO itemSkuQuery);
}
