package com.cowell.pricecenter.service.feign.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @program: pricecenter
 * @description: 调价限制配置行
 * @author: jmlu
 * @create: 2022-12-11 12:28
 **/

@Getter
@Setter
@ToString
public class AdjustLimitConfigRow {

	@ApiModelProperty("集团/连锁 3:集团,其他值为连锁")
    private Long businessId;
	
	@ApiModelProperty("配置类型 1.仅允许上调, 2.仅允许下调 3.不允许调价")
    private Integer  bizOption;
	
    private String bizCode;

    private String bizValue;

}
