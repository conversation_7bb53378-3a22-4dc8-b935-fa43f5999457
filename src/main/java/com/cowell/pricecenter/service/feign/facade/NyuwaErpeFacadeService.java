package com.cowell.pricecenter.service.feign.facade;
import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.request.PriceOrgGoodsParam;
import com.cowell.pricecenter.service.dto.request.StorePriceParamVo;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.feign.NyuwaErpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NyuwaErpeFacadeService {
    @Autowired
    private NyuwaErpService nyuwaErpService;


    public PageResponse<List<PriceStoreDetailVo>> getPriceStoreDetailList(StorePriceParamVo param) {
        Map<String, Object> map = objectToMap(param);

        List<Long> businessIdList = param.getBusinessIdList();
        if (CollectionUtils.isNotEmpty(businessIdList)) {
            map.put("businessIds", businessIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("businessIdList");
        }
        List<Long> storeIdList = param.getStoreIdList();
        if (CollectionUtils.isNotEmpty(storeIdList)) {
            map.put("storeIds", storeIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("storeIdList");
        }
        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNos", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        List<String> priceTypeCodeList = param.getPriceTypeCodeList();
        if (CollectionUtils.isNotEmpty(priceTypeCodeList)) {
            map.put("priceTypeCodes", priceTypeCodeList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("priceTypeCodeList");
        }
        List<Integer> channelIdList = param.getChannelIdList();
        if (CollectionUtils.isNotEmpty(channelIdList)) {
            map.put("channelId", channelIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("channelIdList");
        }
        Integer pageSize = param.getPageSize();
        if (Objects.nonNull(pageSize)) {
            map.put("perPage", pageSize);
            map.remove("pageSize");
        }
        ResponseEntity<CommonRes<PageResult<PriceStoreDetailVo>>> response = nyuwaErpService.getPriceStoreDetailList(map);
        if (null == response
            || Objects.isNull(response.getBody())
            || Objects.isNull(response.getBody().getData())) {
            log.debug("NyuwaErpeFacadeService.getPriceStoreDetailList|没有查询到数据:{}", map);
            return new PageResponse<>();
        }
        PageResult<PriceStoreDetailVo> pageResult = response.getBody().getData();
        List<PriceStoreDetailVo> rows = pageResult.getRows();
        PageResponse<List<PriceStoreDetailVo>> pageResponse = new PageResponse<>();
        pageResponse.setResult(rows);
        pageResponse.setTotalSize(pageResult.getTotal());
        pageResponse.setPage(param.getPage());
        pageResponse.setPageSize(param.getPageSize());
        return pageResponse;
    }

    public List<Long> getStoreIdByGoodsNoAndPriceTypeAndChannelId(StorePriceParamVo param) {
        Map<String, Object> map = objectToMap(param);

        List<Long> storeIdList = param.getStoreIdList();
        if (CollectionUtils.isNotEmpty(storeIdList)) {
            map.put("storeIds", storeIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("storeIdList");
        }
        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNos", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        List<String> priceTypeCodeList = param.getPriceTypeCodeList();
        if (CollectionUtils.isNotEmpty(priceTypeCodeList)) {
            map.put("priceTypeCodes", priceTypeCodeList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("priceTypeCodeList");
        }
        List<Integer> channelIdList = param.getChannelIdList();
        if (CollectionUtils.isNotEmpty(channelIdList)) {
            map.put("channelId", channelIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
            map.remove("channelIdList");
        }
        List<Long> resultList = null;
        log.info("NyuwaErpeFacadeService|getStoreIdByGoodsNoAndPriceTypeAndChannelId|请求入参:{}",map);
        ResponseEntity<CommonRes<RowsResult<RowsDTO>>> response = nyuwaErpService.getStoreIdByGoodsNoAndPriceTypeAndChannelId(map);
        if(null!=response && null!=response.getBody() && null!=response.getBody().getData() && null!=response.getBody().getData().getRows() &&
        		CollectionUtils.isNotEmpty(response.getBody().getData().getRows().getItemList())) {
        	resultList = response.getBody().getData().getRows().getItemList().stream().map(s -> Long.parseLong(s.getKey().trim())).collect(Collectors.toList());
        }
        log.info("getStoreIdByGoodsNoAndPriceTypeAndChannelId|storeIdList:{}",resultList);
        return resultList;
    }

    public List<String> getAdjustCodeListByGoodsNo(StorePriceParamVo param) {
        Map<String, Object> map = objectToMap(param);
        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNos", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        ResponseEntity<CommonRes<RowsResult<RowsDTO>>> response = nyuwaErpService.getAdjustCodeListByGoodsNo(map);
        if(null!=response && null!=response.getBody() && null!=response.getBody().getData() && null!=response.getBody().getData().getRows() &&
        		CollectionUtils.isNotEmpty(response.getBody().getData().getRows().getItemList())) {
        	return response.getBody().getData().getRows().getItemList().stream().map(RowsItemDTO::getKey).collect(Collectors.toList());
        }
        return null;
    }

    public PageResponse<List<PriceOrgGoodsDTO>> getPriceOrgGoodsList(PriceOrgGoodsParam param) {
        Map<String, Object> map = objectToMap(param);
        List<String> goodsNoList = param.getGoodsNoList();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            map.put("goodsNos", String.join(",", goodsNoList));
            map.remove("goodsNoList");
        }
        List<Long> orgIdList = param.getOrgIds();
        if (CollectionUtils.isNotEmpty(orgIdList)) {
            map.put("orgIds", orgIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        List<Long> spuIds = param.getSpuIds();
        if (CollectionUtils.isNotEmpty(spuIds)) {
            map.put("spuIds", spuIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        }
        if(StringUtils.isNotEmpty(param.getKeyWord())){
            map.put("keyWord", param.getKeyWord());
        }
        Integer pageSize = param.getPageSize();
        if (Objects.nonNull(pageSize)) {
            map.put("perPage", pageSize);
            map.remove("pageSize");
        }
        ResponseEntity<CommonRes<PageResult<PriceOrgGoodsDTO>>> response = nyuwaErpService.getPriceOrgGoodsList(map);
        if (null == response
            || Objects.isNull(response.getBody())
            || Objects.isNull(response.getBody().getData())) {
            log.debug("NyuwaErpeFacadeService.getPriceOrgGoodsList|没有查询到数据:{}", map);
            return new PageResponse<>();
        }
        PageResult<PriceOrgGoodsDTO> pageResult = response.getBody().getData();
        List<PriceOrgGoodsDTO> rows = pageResult.getRows();
        PageResponse<List<PriceOrgGoodsDTO>> pageResponse = new PageResponse<>();
        pageResponse.setResult(rows);
        pageResponse.setTotalSize(pageResult.getTotal());
        pageResponse.setPage(param.getPage());
        pageResponse.setPageSize(param.getPageSize());
        return pageResponse;
    }

    private static Map<String, Object> objectToMap(Object object){
        Map<String,Object> dataMap = new HashMap<>();
        Class<?> clazz = object.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            try {
                field.setAccessible(true);
                dataMap.put(field.getName(),field.get(object));
            } catch (IllegalAccessException e) {
                log.error("转换失败", e);
            }
        }
        return dataMap;
    }

}
