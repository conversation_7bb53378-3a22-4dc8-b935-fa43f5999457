package com.cowell.pricecenter.service.feign;

import com.alibaba.fastjson.JSONObject;
import com.codahale.metrics.annotation.Timed;
import com.cowell.pricecenter.service.dto.StoreAttrInfoDTO;
import com.cowell.pricecenter.service.dto.response.amis.AmisCommonResponse;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/1/9 16:19
 */
@FeignClient(name = "nyuwa")
public interface INyuwaFeign {

    @GetMapping("/api/intranet/mdd/{appId}/comparison_model_configuration/get")
    @Timed
    AmisCommonResponse<JSONObject> queryCompareConfig(@PathVariable("appId") String appId, @RequestParam("id") String id);

    @PostMapping("/api/intranet/mdd/89/store_goods_attr/batchCreate")
    @Timed
    AmisCommonResponse<JSONObject> storeGoodsAttrBatchCreate(@RequestBody Map batchInfo);

    @PostMapping("/api/intranet/mdd/89/store_goods_attr/create")
    @Timed
    AmisCommonResponse<JSONObject> storeGoodsAttrCreate(@RequestBody StoreAttrInfoDTO infoDTO);

}
