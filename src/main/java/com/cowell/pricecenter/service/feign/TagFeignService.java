package com.cowell.pricecenter.service.feign;

import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "tag")
public interface TagFeignService {

    @ApiOperation("保存标签")
    @PostMapping("/api/optimize/tag/service/save")
    ResponseEntity<Object> save(@RequestBody TagSaveParam tagParam);

    @ApiOperation(value = "标签打标")
    @PostMapping("/api/intranet/optimize/tag/markByUser")
    ResponseEntity<CommonRes> tagMark(@RequestBody TagMarkParentParam tagMarkParam);

    @ApiOperation("查询标签列表")
    @PostMapping("/api/optimize/tag/queryByPage")
    ResponseEntity<CommonRes<PageResult<TagDTO>>> queryByPage(@RequestBody TagPageParam tagParam);

    @ApiOperation("标签查询实体分页-管理端&服务端")
    @PostMapping("/api/optimize/tag/query/tagEntityQuery")
    ResponseEntity<CommonRes<PageResult<TagEntityQueryDTO>>> manageTagEntityQuery(@RequestBody TagEntityQueryServerParam param);

    @ApiOperation("查询标签列表（带实体数量）-根据实体反查标签列表")
    @PostMapping("/api/optimize/tag/query/tagListQuery")
    ResponseEntity<CommonRes<PageResult<TagListQueryDTO>>> tagListQueryByEntityCode(@RequestBody TagListQueryParam param);

    @ApiOperation("标签查询实体Code-服务端")
    @PostMapping("/api/optimize/tag/query/tagEntityQuery")
    ResponseEntity<CommonRes<PageResult<String>>> tagEntityCodeListQuery(@RequestBody TagEntityQueryServerParam param);

    @ApiOperation("批量查询实体所有标签,返回实体标签dto集合")
    @PostMapping("/api/intranet/optimize/tag/query/entityTagQuery")
    ResponseEntity<CommonRes<PageResult<EntityTagQueryServerDto>>> entityTagQuery(@RequestBody EntityTagQueryParam param);

    @ApiOperation("查询标签列表")
    @PostMapping("/api/internal/optimize/tag/queryByPage")
    ResponseEntity<CommonRes<PageResult<TagDTO>>> queryByPageInternal(@RequestBody TagPageParam tagParam);

    @ApiOperation(value = "标签打标")
    @PostMapping("/api/internal/optimize/tag/internal/mark")
    ResponseEntity<CommonRes> toMark(@RequestBody TagMarkParentParam tagMarkParam);
    }
