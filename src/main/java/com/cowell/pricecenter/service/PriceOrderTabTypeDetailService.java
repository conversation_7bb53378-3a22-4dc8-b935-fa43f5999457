package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceOrderTabTypeDetail;
import java.util.List;
import com.cowell.pricecenter.entity.PriceOrderTabTypeDetailExample;
    /**
 * <AUTHOR>
 * @date  2022/3/18 10:55
 */

public interface PriceOrderTabTypeDetailService{


    long countByExample(PriceOrderTabTypeDetailExample example);

    int deleteByExample(PriceOrderTabTypeDetailExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(PriceOrderTabTypeDetail record);

    int insertSelective(PriceOrderTabTypeDetail record);

    List<PriceOrderTabTypeDetail> selectByExample(PriceOrderTabTypeDetailExample example);

    PriceOrderTabTypeDetail selectByPrimaryKey(Integer id);

    int updateByExampleSelective(PriceOrderTabTypeDetail record,PriceOrderTabTypeDetailExample example);

    int updateByExample(PriceOrderTabTypeDetail record,PriceOrderTabTypeDetailExample example);

    int updateByPrimaryKeySelective(PriceOrderTabTypeDetail record);

    int updateByPrimaryKey(PriceOrderTabTypeDetail record);

    /**
      * 物理删除调价单调价单Tab所有数据
      * @param adjustCode
      */
    void deleteByAdjustCode(String adjustCode);


    /**
      * 获取调价单的组织Tab值列表
      * @param adjustCode
      * @return
      */
    List<String> getAdjustPriceOrderTabTypeValues(String adjustCode);
}
