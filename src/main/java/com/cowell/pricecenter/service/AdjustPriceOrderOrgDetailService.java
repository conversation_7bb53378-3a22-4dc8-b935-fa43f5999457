package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.vo.OrgLevelVO;
import java.util.List;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetailExample;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail;
import java.util.Map;

/**
 * <AUTHOR>
 * @date  2022/3/18 10:59
 */

public interface AdjustPriceOrderOrgDetailService{


    long countByExample(AdjustPriceOrderOrgDetailExample example);

    int deleteByExample(AdjustPriceOrderOrgDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(AdjustPriceOrderOrgDetail record);

    int insertSelective(AdjustPriceOrderOrgDetail record);

    List<AdjustPriceOrderOrgDetail> selectByExample(AdjustPriceOrderOrgDetailExample example);

    AdjustPriceOrderOrgDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(AdjustPriceOrderOrgDetail record,AdjustPriceOrderOrgDetailExample example);

    int updateByExample(AdjustPriceOrderOrgDetail record,AdjustPriceOrderOrgDetailExample example);

    int updateByPrimaryKeySelective(AdjustPriceOrderOrgDetail record);

    int updateByPrimaryKey(AdjustPriceOrderOrgDetail record);

    int batchInsert(List<AdjustPriceOrderOrgDetail> list);

    /**
     * 根据调价单删除调价单组织明细
     * @param adjustCode
     */
    void deleteByAdjustCode(String adjustCode);

    /**
     * 根据调价单获取调价单组织明细列表
     * @param adjustCode
     * @return
     */
    List<AdjustPriceOrderOrgDetail> listByAdjustCode(String adjustCode);

    /**
      * 根据调价单获取调价单组织个数
      * @param adjustCode
      * @return
      */
    Long countByAdjustCode(String adjustCode);

    /**
     * 根据调价单获取调价单组织ID列表
     * @param adjustCode
     * @return
     */
    List<Long> listOrgIdListByAdjustCode(String adjustCode);

    /**
     * 根据调价单获取调价单组织ID和组织层级列表
     * @param adjustCode
     * @return
     */
    List<OrgLevelVO> listOrgLevelVOListByAdjustCode(String adjustCode);

    /**
     * 根据调价单获取调价单组织ID和组织层级Map
     * @param adjustCode
     * @return
     */
    Map<Long, OrgLevelVO> listOrgLevelVOMapByAdjustCode(String adjustCode);
}
