package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.StoreGroupDetailDTO;
import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.request.StoreGroupDTO;
import com.cowell.pricecenter.service.dto.request.StoreGroupEditParam;
import com.cowell.pricecenter.service.dto.request.StoreGroupQueryParam;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface StoreGroupService {
    PageResponse<List<StoreGroupDTO>> getStoreGroupList(Long userId, StoreGroupQueryParam param) throws Exception;

    PageResponse<List<StoreGroupDetailDTO>> getStoreGroupDetail(Long userId, String groupCode, Integer page, Integer pageSize , HttpServletRequest request) throws Exception;

    CommonResponse<Object> editStoreGroup(Long userId, String userName, StoreGroupEditParam param, HttpServletRequest request) throws Exception;

    CommonResponse<List<Long>> storeGroupImport(MultipartFile file, Long resourceId, Long userId) throws Exception;

    CommonResponse<Object> storeGroupUpdateStatus(Long groupId, Byte groupStatus, Long userId, String userName) throws Exception;

    List<StoreGroupDTO> getStoreGroupByStoreGroupIdList(Long userId,Integer tagType,List<Long> storeGroupIdList);

    List<String> tagEntityCodeListQuery(Long tagId);

}
