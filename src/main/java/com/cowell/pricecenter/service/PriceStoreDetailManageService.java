package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.security.dto.TokenUserDTO;
import com.cowell.pricecenter.service.dto.PriceStoreDetailDeleteParam;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 3/22/21 3:59 PM
 */
public interface PriceStoreDetailManageService {

    /**
     * 删除价格
     * @param params 参数
     * @param tokenUser token
     */
    void deletePrice(List<PriceStoreDetailDeleteParam> params, TokenUserDTO tokenUser);

    /**
     * 查询商品价格
     */
    List<PriceStoreDetail> getPriceByGoodsNo(@RequestBody PriceStoreDetailDeleteParam param);
}
