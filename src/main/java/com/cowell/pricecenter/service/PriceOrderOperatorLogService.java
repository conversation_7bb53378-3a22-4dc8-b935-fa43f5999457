package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceOrderOperatorLog;
import com.cowell.pricecenter.entity.PriceOrderOperatorLogExample;
import com.cowell.pricecenter.service.dto.OaSource;
import com.cowell.pricecenter.service.dto.OaTarget;
import com.cowell.pricecenter.service.dto.OperatorLog;
import com.cowell.pricecenter.service.dto.response.OperatorLogDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:06
 */

public interface PriceOrderOperatorLogService {


    long countByExample(PriceOrderOperatorLogExample example);

    int deleteByExample(PriceOrderOperatorLogExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(PriceOrderOperatorLog record);

    int insertSelective(PriceOrderOperatorLog record);

    List<PriceOrderOperatorLog> selectByExample(PriceOrderOperatorLogExample example);

    PriceOrderOperatorLog selectByPrimaryKey(Integer id);

    int updateByExampleSelective(PriceOrderOperatorLog record, PriceOrderOperatorLogExample example);

    int updateByExample(PriceOrderOperatorLog record, PriceOrderOperatorLogExample example);

    int updateByPrimaryKeySelective(PriceOrderOperatorLog record);

    int updateByPrimaryKey(PriceOrderOperatorLog record);


    List<OperatorLogDTO> getLogList(Integer orderType, String orderId);

    boolean saveLog(OperatorLog operatorLog);

    /**
     * 推送OA
     *
     * @param oaTarget  OA目标对象
     */
    boolean pushToOA(OaTarget oaTarget);

    /**
     * 接收OA
     *
     * @param oaSource  OA来源对象
     */
    boolean acceptOA(OaSource oaSource);

}
