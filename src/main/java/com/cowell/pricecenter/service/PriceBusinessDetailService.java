package com.cowell.pricecenter.service;

import java.util.List;

import com.cowell.pricecenter.entity.PriceBusinessDetail;
import com.cowell.pricecenter.entity.PriceBusinessDetailExample;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:25
 */
public interface PriceBusinessDetailService {


    long countByExample(PriceBusinessDetailExample example);

    int deleteByExample(PriceBusinessDetailExample example);

    int deleteByPrimaryKey(String id);

    int insert(PriceBusinessDetail record);

    int insertSelective(PriceBusinessDetail record);

    List<PriceBusinessDetail> selectByExample(PriceBusinessDetailExample example);

    PriceBusinessDetail selectByPrimaryKey(String id);

    int updateByExampleSelective(PriceBusinessDetail record, PriceBusinessDetailExample example);

    int updateByExample(PriceBusinessDetail record, PriceBusinessDetailExample example);

    int updateByPrimaryKeySelective(PriceBusinessDetail record);

    int updateByPrimaryKey(PriceBusinessDetail record);

    int batchInsert(List<PriceBusinessDetail> list);

    int updateByBusinessGoodsSelective(PriceBusinessDetail record);

    int batchDelete(List<PriceBusinessDetail> list);

    void save(List<PriceBusinessDetail> list);

    int deletePriceBusinessDetail(Long businessId,String goodsNo);

}


