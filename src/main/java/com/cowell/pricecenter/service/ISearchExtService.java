package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.request.SpuQueryParamVo;
import com.cowell.pricecenter.service.dto.response.PriceStoreDetailVo;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import com.cowell.pricecenter.service.feign.vo.SpuListVO;
import com.cowell.pricecenter.service.feign.vo.SpuQueryParamVO;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @program: pricecenter
 * @description: ES搜索接口封装接口
 * @author: jmlu
 * @create: 2022-03-30 14:45
 **/

public interface ISearchExtService {

    /**
     * 根据商品编码列表,查询商品基础信息Map, 不限制大小
     * @param goodsNoList
     * @return
     */
    Map<String, SpuListVO> getSpuVOMapAll(List<String> goodsNoList);

    /**
     * 根据商品编码列表,查询商品基础信息Map, 商品列表的个数不能大于50
     * @param goodsNoList
     * @return
     */
    Map<String, SpuListVO> getSpuVOMap(List<String> goodsNoList);

    /**
     * 根据商品编码列表,查询商品基础信息列表
     * @param goodsNo
     * @return
     */
    Optional<SpuListVO> getSpuInfo(String goodsNo);


    /**
     * 根据商品查询调价单列表(去重)
     * @param goodsNo
     * @return
     */
    List<String> getAdjustCodeListByGoodsNo(String goodsNo);

    /**
     * 查询企业级商品(商品编码 -> 商品信息), 默认从redis的缓存中取
     *
     * @param businessIdList
     * @param goodsNoLsit
     * @return
     */
    Map<String, SpuNewVo> getSKuMap(List<Long> businessIdList, List<String> goodsNoLsit);

    /**
     * 查询商品信息
     *
     * @param spuQueryParamVO
     * @return
     */
    List<SpuListVO> getSpuList(SpuQueryParamVO spuQueryParamVO);

    /**
     * 查询商品信息(商品编码 -> 商品信息)
     *
     * @param spuQueryParamVO
     * @return
     */
    Map<String, SpuListVO> getSpuMap(SpuQueryParamVO spuQueryParamVO);

    /**
     *  商品在公司是否做过价格
     * @param goodsno
     * @param businessId
     * @return
     */
    Boolean isExistByGoodsAndCompany(String goodsno, Long businessId);

    /**
     *
     * @Title: getByGoodsNoAndStoreIdListAndPriceTypeCode
     * @Description: 根据门店集合、商品编码、价格类型查执价数据
     * @param: @param storeIds
     * @param: @param goodsNo
     * @param: @param priceTypeCode
     * @param: @return
     * @return: List<PriceStoreDetailVo>
     * @throws
     */
    List<PriceStoreDetailVo> getByGoodsNoAndStoreIdListAndPriceTypeCode(List<Long> storeIds,String goodsNo,String priceTypeCode);

    /**
     * 获取商品
     * @param spuQueryParamVo
     * @return
     */
    PageResponse<List<SpuListVO>> getSupContentsList(SpuQueryParamVo spuQueryParamVo);

}
