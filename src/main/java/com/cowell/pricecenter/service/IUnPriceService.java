package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.service.dto.request.NewPriceTypeParam;
import com.cowell.pricecenter.service.dto.request.PriceTypeParam;
import com.cowell.pricecenter.service.dto.response.PriceTypeDTO;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;


public interface IUnPriceService{


    CommonResponse getUnPrice(long businessId);

}
