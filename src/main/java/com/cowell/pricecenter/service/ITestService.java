package com.cowell.pricecenter.service;


public interface ITestService {


    /**
     * 重新分库分表
     * @param id
     * @param startTime
     * @param endTime
     * @param page
     * @param pageSize
     * @return
     */
    int fixPriceStoreDetail(Long id,String startTime,String endTime,Integer page,Integer pageSize);

    /**
     * 查询 价格总条数
     * @param businessId
     * @param storeId
     * @param version
     * @return
     */
    Long countTotal(Long businessId,Long storeId,Integer version);

    /**
     * 修改Version
     * @param source 要查询的值
     * @param target 要改成的值
     */
    void updatePriceStoreDetailVersion(Integer source,Integer target);

}
