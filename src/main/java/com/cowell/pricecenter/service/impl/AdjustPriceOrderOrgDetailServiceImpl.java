package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.PriceOrderTabTypeDetailExample;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgDetailExtMapper;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import java.util.Collections;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.cowell.pricecenter.mapper.AdjustPriceOrderOrgDetailMapper;
import java.util.List;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetailExample;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgDetail;
import com.cowell.pricecenter.service.AdjustPriceOrderOrgDetailService;
/**
 * <AUTHOR>
 * @date  2022/3/18 10:59
 */

@Service
public class AdjustPriceOrderOrgDetailServiceImpl implements AdjustPriceOrderOrgDetailService{

    private final Logger logger = LoggerFactory.getLogger(AdjustPriceOrderOrgDetailServiceImpl.class);

    @Resource
    private AdjustPriceOrderOrgDetailMapper adjustPriceOrderOrgDetailMapper;

    @Resource
    private AdjustPriceOrderOrgDetailExtMapper adjustPriceOrderOrgDetailExtMapper;

    @Override
    public long countByExample(AdjustPriceOrderOrgDetailExample example) {
        return adjustPriceOrderOrgDetailMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(AdjustPriceOrderOrgDetailExample example) {
        return adjustPriceOrderOrgDetailMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return adjustPriceOrderOrgDetailMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(AdjustPriceOrderOrgDetail record) {
        return adjustPriceOrderOrgDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(AdjustPriceOrderOrgDetail record) {
        return adjustPriceOrderOrgDetailMapper.insertSelective(record);
    }

    @Override
    public List<AdjustPriceOrderOrgDetail> selectByExample(AdjustPriceOrderOrgDetailExample example) {
        return adjustPriceOrderOrgDetailMapper.selectByExample(example);
    }

    @Override
    public AdjustPriceOrderOrgDetail selectByPrimaryKey(Long id) {
        return adjustPriceOrderOrgDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(AdjustPriceOrderOrgDetail record,AdjustPriceOrderOrgDetailExample example) {
        return adjustPriceOrderOrgDetailMapper.updateByExampleSelective(record,example);
    }

    @Override
    public int updateByExample(AdjustPriceOrderOrgDetail record,AdjustPriceOrderOrgDetailExample example) {
        return adjustPriceOrderOrgDetailMapper.updateByExample(record,example);
    }

    @Override
    public int updateByPrimaryKeySelective(AdjustPriceOrderOrgDetail record) {
        return adjustPriceOrderOrgDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(AdjustPriceOrderOrgDetail record) {
        return adjustPriceOrderOrgDetailMapper.updateByPrimaryKey(record);
    }

    @Override
    public int batchInsert(List<AdjustPriceOrderOrgDetail> list) {
        return adjustPriceOrderOrgDetailExtMapper.batchInsert(list);
    }

    @Override
    public void deleteByAdjustCode(String adjustCode) {
        if (StringUtils.isEmpty(adjustCode)) {
            logger.error("===>[AdjustPriceOrderOrgDetailServiceImpl.deleteByAdjustCode] adjustCode 不能为空", adjustCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrderOrgDetailExample example = new AdjustPriceOrderOrgDetailExample();
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode);
        deleteByExample(example);
    }

    @Override
    public List<AdjustPriceOrderOrgDetail> listByAdjustCode(String adjustCode) {
        if (StringUtils.isEmpty(adjustCode)) {
            logger.error("===>[AdjustPriceOrderOrgDetailServiceImpl.listByAdjustCode] adjustCode 不能为空", adjustCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrderOrgDetailExample example = new AdjustPriceOrderOrgDetailExample();
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode);

        List<AdjustPriceOrderOrgDetail> orgDetailList = selectByExample(example);
        if (orgDetailList == null) {
            orgDetailList = Collections.emptyList();
        }
        return orgDetailList;
    }

    @Override
    public Long countByAdjustCode(String adjustCode) {
        if (StringUtils.isEmpty(adjustCode)) {
            logger.error("===>[AdjustPriceOrderOrgDetailServiceImpl.countByAdjustCode] adjustCode 不能为空", adjustCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrderOrgDetailExample example = new AdjustPriceOrderOrgDetailExample();
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode);

        Long count = countByExample(example);
        return count == null ? 0L : count;
    }

    @Override
    public List<Long> listOrgIdListByAdjustCode(String adjustCode) {
        return listByAdjustCode(adjustCode).stream().map(AdjustPriceOrderOrgDetail::getOrgId)
            .collect(Collectors.toList());
    }

    @Override
    public List<OrgLevelVO> listOrgLevelVOListByAdjustCode(String adjustCode) {
        return listByAdjustCode(adjustCode).stream()
            .map(orgDetail -> new OrgLevelVO(orgDetail.getOrgId(), orgDetail.getOrgLevel()))
            .collect(Collectors.toList());
    }

    @Override
    public Map<Long, OrgLevelVO> listOrgLevelVOMapByAdjustCode(String adjustCode) {
        return listByAdjustCode(adjustCode).stream()
            .map(orgDetail -> new OrgLevelVO(orgDetail.getOrgId(), orgDetail.getOrgLevel()))
            .collect(Collectors.toMap(OrgLevelVO::getOrgId, Function.identity() ));
    }
}
