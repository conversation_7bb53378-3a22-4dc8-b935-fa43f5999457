package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.PriceManageControlOrder;
import com.cowell.pricecenter.entity.PriceManageControlOrderDetail;
import com.cowell.pricecenter.entity.PriceManageControlOrderDetailExample;
import com.cowell.pricecenter.entity.PriceManageControlOrgDetail;
import com.cowell.pricecenter.enums.DistributedIDTypeEnum;
import com.cowell.pricecenter.enums.OrgLevelEnum;
import com.cowell.pricecenter.mapper.PriceManageControlOrderDetailMapper;
import com.cowell.pricecenter.mq.producer.ControlOrderGoodsDetailCalProducer;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.response.StoreDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.ibatis.cursor.Cursor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/17 11:31
 */

@Service
public class PriceManageControlOrderDetailServiceImpl extends BasePermissionService implements PriceManageControlOrderDetailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceManageControlOrderDetailServiceImpl.class);

    private static final Integer DEFAULT_BATCH_PAGE_SIZE = 1000;

    @Resource
    private PriceManageControlOrderDetailMapper priceManageControlOrderDetailMapper;

    @Autowired
    private PriceManageControlOrgDetailService orgDetailService;

    @Autowired
    private PriceManageControlOrderDetailReadService priceManageControlOrderDetailReadService;

    @Autowired
    private PriceManageControlOrderReadService priceManageControlOrderReadService;

    @Autowired
    private ITocExtService tocExtService;

    @Autowired
    private ControlOrderGoodsDetailCalProducer calProducer;

    @Autowired
    private FeignStoreService feignStoreService;

    @Autowired()
    @Qualifier(value = "controlOrderDetailThreadExecutor")
    private AsyncTaskExecutor taskExecutor;

    /**
     * 所有连锁的缓存数据
     */
    private static final Map<Long, MdmBusinessBaseToRedisDTO> businessCacheMap = Maps.newConcurrentMap();


    @PostConstruct
    public void initAllBusinessId() {
        List<MdmBusinessBaseToRedisDTO> businessList = feignStoreService.findAllMdmBusinessBase();
        if (CollectionUtils.isEmpty(businessList)) {
            return;
        }

        for (MdmBusinessBaseToRedisDTO mdmBusinessBaseToRedisDTO : businessList) {
            businessCacheMap.put(mdmBusinessBaseToRedisDTO.getBusinessId(), mdmBusinessBaseToRedisDTO);
        }

    }


    @Override
    public int deleteByExample(PriceManageControlOrderDetailExample example) {
        return priceManageControlOrderDetailMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return priceManageControlOrderDetailMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PriceManageControlOrderDetail record) {
        return priceManageControlOrderDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceManageControlOrderDetail record) {
        return priceManageControlOrderDetailMapper.insertSelective(record);
    }


    @Override
    public int updateByExampleSelective(PriceManageControlOrderDetail record, PriceManageControlOrderDetailExample example) {
        return priceManageControlOrderDetailMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceManageControlOrderDetail record, PriceManageControlOrderDetailExample example) {
        return priceManageControlOrderDetailMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceManageControlOrderDetail record) {
        return priceManageControlOrderDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceManageControlOrderDetail record) {
        return priceManageControlOrderDetailMapper.updateByPrimaryKey(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cursorScanControlOrderDetailList(String controlOrderCode) {

        PriceControlOrderQueryDTO queryDTO = new PriceControlOrderQueryDTO();
        queryDTO.setControlOrderId(controlOrderCode);
        List<PriceManageControlOrder> list = priceManageControlOrderReadService.getPriceManageControlOrderList(queryDTO);
        if (CollectionUtils.isEmpty(list) || Objects.isNull(list.get(0))) {
            LOGGER.info("PriceManageControlOrderServiceImpl|generateControlNotice|未查询审核通过需要生效的管控单!");
            return;
        }

        PriceManageControlOrder controlOrder = list.get(0);
        ControlOrderDetailQueryDTO orderDetailQueryDTO = new ControlOrderDetailQueryDTO();
        orderDetailQueryDTO.setControlOrderCode(controlOrder.getControlOrderId());
        long count = priceManageControlOrderDetailReadService.getControlOrderDetailCount(orderDetailQueryDTO);
        if (0L == count) {
            LOGGER.info("PriceManageControlOrderServiceImpl|generateControlNotice|该管控单下关联的明细总数为0!|orderId={}",
                controlOrder.getControlOrderId());
            return;
        }

        ControlOrderOrgDetailQueryDTO orderOrgDetailQueryDTO = new ControlOrderOrgDetailQueryDTO();
        orderOrgDetailQueryDTO.setControlOrderCodes(Lists.newArrayList(controlOrder.getControlOrderId()));
        List<PriceManageControlOrgDetail> orgList = orgDetailService.getControlOrgDetailList(orderOrgDetailQueryDTO);
        if (CollectionUtils.isEmpty(orgList)) {
            LOGGER.info("PriceManageControlOrderServiceImpl|generateControlNotice|生成管控通知当前管控单选择的机构为空|orderId={}",
                controlOrder.getControlOrderId());
            return;
        }

        Integer totalPage = new BigDecimal(Math.ceil(new BigDecimal(count).divide(new BigDecimal(DEFAULT_BATCH_PAGE_SIZE)).doubleValue())).intValue();

        PriceManageControlOrderDetailExample orderDetailExample = new PriceManageControlOrderDetailExample();
        orderDetailExample.createCriteria().andControlOrderCodeIn(Lists.newArrayList(controlOrderCode));
        List<Long> orgIds = orgList.stream().map(org -> org.getOrgId()).collect(Collectors.toList());

        OrgLevelEnum orgLevelEnum = OrgLevelEnum.getType(controlOrder.getOrderLevel());
        Map<Long, List<StoreDTO>> storeMap;
        switch (orgLevelEnum) {
            case STORE:
                List<StoreDTO> storeDTOS = getStoreListByOrgId(orgIds, controlOrder.getOrderLevel());
                if (CollectionUtils.isEmpty(storeDTOS)) {
                    LOGGER.info("PriceManageControlOrderServiceImpl|generateControlNotice|通过orgId未查询到门店信息");
                    return;
                }
                storeMap = storeDTOS.stream().collect(Collectors.groupingBy(StoreDTO::getOrgId));
                break;
            default:
                storeMap = getOrgStoreList(orgIds);
                break;

        }

        //将map中的门店进行过滤(是不是mdm的门店)
        final Map<Long, List<StoreDTO>> finalStoreMap = filterStoreMap(storeMap);

        List<PriceManageControlOrderDetail> orderDetailList = Lists.newArrayList();
        try (Cursor<PriceManageControlOrderDetail> cursor = priceManageControlOrderDetailMapper.cursorScanControlOrderDetail(orderDetailExample)) {
            Iterator iterator = cursor.iterator();
            while (iterator.hasNext()) {
                PriceManageControlOrderDetail storeDetail = (PriceManageControlOrderDetail) iterator.next();
                if (orderDetailList.stream().count() >= 100L) {
                    List<PriceManageControlOrderDetail> orderDetailListTemp = Lists.newArrayList();
                    orderDetailListTemp.addAll(orderDetailList);
                    taskExecutor.execute(() -> {
                        getControlStoreDetailList(controlOrder, orgIds, orderDetailListTemp, finalStoreMap);
                    });

                    orderDetailList.clear();
                }
                orderDetailList.add(storeDetail);
            }

            //分批剩下的数据进行生成明细
            if (CollectionUtils.isNotEmpty(orderDetailList)) {
                getControlStoreDetailList(controlOrder, orgIds, orderDetailList, finalStoreMap);
            }

        } catch (Exception e) {
            LOGGER.error("PriceManageControlStoreDetailServiceImpl|scanControlStoreDetail|通过游标查询管控单门店商品明细异常!", e);
        }
    }


    /**
     * 过滤storeMap中的门店数据
     * @param storeMap
     * @return Map<Long, List < StoreDTO>>
     */
    private Map<Long, List<StoreDTO>> filterStoreMap(Map<Long, List<StoreDTO>> storeMap) {

        Map<Long, List<StoreDTO>> map = Maps.newHashMap();
        if (MapUtils.isEmpty(storeMap)) {
            return map;
        }

        storeMap.forEach((key, value) -> {
            List<Long> storeIds = value.stream().filter(Objects::nonNull).map(StoreDTO::getId).distinct().collect(Collectors.toList());
            List<List<Long>> storeList = Lists.partition(storeIds, 50);
            List<MdmStoreBaseDTO> mdmStoreList = Lists.newArrayList();
            storeList.forEach(list -> {
                //通过查询到的门店id去mdm_store表中进行过滤
                mdmStoreList.addAll(feignStoreService.getStoreListByStoreIds(list));
            });

            if (CollectionUtils.isEmpty(mdmStoreList)) {
                map.put(key, value);
                return;
            }

            //过滤出是mdm的门店
            Map<Long, MdmStoreBaseDTO> mdmStoreMap = mdmStoreList.stream().collect(Collectors.toMap(MdmStoreBaseDTO::getStoreId,
                Function.identity(), (a, b) -> b));

            //将门店对应的连锁设置连锁名称
            map.put(key, value.stream().filter(orgStore -> mdmStoreMap.containsKey(orgStore.getId())).map(orgStore -> {
                MdmStoreBaseDTO mdmStore = mdmStoreMap.get(orgStore.getId());
                StoreDTO store = new StoreDTO();
                store.setId(mdmStore.getStoreId());
                store.setBusinessId(mdmStore.getBusinessId());
                store.setOrgId(orgStore.getOrgId());
                store.setName(orgStore.getName());
                store.setShortName(orgStore.getShortName());
                MdmBusinessBaseToRedisDTO redisDTO = businessCacheMap.get(store.getBusinessId());
                if (Objects.isNull(redisDTO)) {
                    return null;
                }

                store.setBusinessId(redisDTO.getBusinessId());
                store.setBusinessName(redisDTO.getComName());
                return store;
            }).filter(Objects::nonNull).collect(Collectors.toList()));

            //将用不到的数据进行清空，释放内存
            if (CollectionUtils.isNotEmpty(value)) {
                value.clear();
            }
        });

        return map;
    }


    /**
     * 获取管控到门店的明细
     * @param auditPassOrder 审核通过的管控单
     * @param controlOrgIds 管控单所选择的机构
     * @param auditPassDetailList 审核通过的管控明细
     * @param storeMap 审核通过的管控选择机构对应的门店id
     */
    private void getControlStoreDetailList(PriceManageControlOrder auditPassOrder,
                                           List<Long> controlOrgIds,
                                           List<PriceManageControlOrderDetail> auditPassDetailList,
                                           Map<Long, List<StoreDTO>> storeMap) {

        if (Objects.isNull(auditPassOrder)) {
            LOGGER.info("PriceManageControlOrderServiceImpl|getControlStoreDetailList|审核通过的管控单为空!");
            return;
        }

        if (CollectionUtils.isEmpty(controlOrgIds)) {
            LOGGER.info("PriceManageControlOrderServiceImpl|getControlStoreDetailList|管控单选择的机构id为空!");
            return;
        }

        if (CollectionUtils.isEmpty(auditPassDetailList)) {
            LOGGER.info("PriceManageControlOrderServiceImpl|getControlStoreDetailList|审核通过的管控单明细为空!");
            return;
        }

        if (MapUtils.isEmpty(storeMap)) {
            LOGGER.info("PriceManageControlOrderServiceImpl|getControlStoreDetailList|管控单审核通过选中的机构下的门店id为空");
            return;
        }

        LOGGER.info("PriceManageControlOrderServiceImpl|getControlStoreDetailList|auditPassDetailList.size={}",
            auditPassDetailList.size());

        List<PriceManageControlStoreDetailDTO> insertList = Lists.newArrayList();
        Map<Long, Long> orgIdMap = controlOrgIds.stream().collect(Collectors.toMap(orgId -> orgId, Function.identity(), (a, b) -> b));
        auditPassDetailList.stream().forEach(orderDetail -> {
            for (Long orgId : orgIdMap.keySet()) {
                List<StoreDTO> storeDTOList = storeMap.get(orgId);
                if (CollectionUtils.isEmpty(storeDTOList)) {
                    continue;
                }

                String uuid = UUID.randomUUID().toString();
                storeDTOList.forEach(storeDTO -> {
                    if (Objects.isNull(storeDTO)) {
                        return;
                    }

                    insertList.add(getPriceManageControlStoreDetail(auditPassOrder, orderDetail, storeDTO));
                    if (insertList.size() >= 100) {
                        getControlStoreGoodsDetailResultDTO(auditPassOrder.getControlOrderId(), insertList, uuid);

                    }
                });
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            getControlStoreGoodsDetailResultDTO(auditPassOrder.getControlOrderId(), insertList, UUID.randomUUID().toString());
        }
    }

    private void getControlStoreGoodsDetailResultDTO(String controlOrderCode,
                                                     List<PriceManageControlStoreDetailDTO> insertList,
                                                     String uuid) {

        List<PriceManageControlStoreDetailDTO> distinctList = insertList.stream().collect(Collectors.collectingAndThen
            (Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(t -> t.getStoreId() + "_"
                + t.getGoodsNo() + "_" + t.getPriceTypeCode() + "_" + t.getChannelId()))), ArrayList::new)
        );

        ControlStoreGoodsDetailResultDTO groupResultDTO = new ControlStoreGoodsDetailResultDTO();
        groupResultDTO.setUuid(uuid);
        groupResultDTO.setChildGroupUUID(UUID.randomUUID().toString());
        groupResultDTO.setControlOrderCode(controlOrderCode);
        groupResultDTO.setTotalInsertNum(distinctList.stream().count());
        groupResultDTO.setCurrentGroupInsertNum(distinctList.stream().count());
        getControlStoreDetailId(distinctList);
        groupResultDTO.setInsertList(distinctList);
        calProducer.sendMq(groupResultDTO);
        insertList.clear();
        distinctList.clear();
    }

    private void getControlStoreDetailId(List<PriceManageControlStoreDetailDTO> insertList) {

        if (CollectionUtils.isEmpty(insertList)) {
            return;
        }

        long[] ids = new long[]{};
        try {
            ids = tocExtService.getDistributedIDList(DistributedIDTypeEnum.CONTROL_ORDER_STORE_DETAIL.getBiz(), insertList.size());
        } catch (Exception e) {
            LOGGER.error("PriceManageControlOrderDetailServiceImpl|getPriceManageControlStoreDetail|分布式id获取异常!", e);
        }

        if (ArrayUtils.isNotEmpty(ids)) {
            for (int i = 0; i < insertList.size(); i++) {
                insertList.get(i).setControlStoreDetailId(ids[i] + "");
            }
        }
    }


    private PriceManageControlStoreDetailDTO getPriceManageControlStoreDetail(PriceManageControlOrder controlOrder,
                                                                              PriceManageControlOrderDetail orderDetail,
                                                                              StoreDTO storeDTO) {

        PriceManageControlStoreDetailDTO insertControlStoreDetail = new PriceManageControlStoreDetailDTO();

        BeanUtils.copyProperties(orderDetail, insertControlStoreDetail);
        //门店id
        insertControlStoreDetail.setStoreId(storeDTO.getId());
        insertControlStoreDetail.setStoreName(storeDTO.getShortName());
        //门店名称
        insertControlStoreDetail.setBusinessId(storeDTO.getBusinessId());
        insertControlStoreDetail.setBusinessName(storeDTO.getBusinessName());
        insertControlStoreDetail.setOrderDetail(orderDetail);
        return insertControlStoreDetail;
    }

    @Override
    public void updateControlOrderDetailDxsDate(String controlOrderCode, LocalDateTime dxsDate) {
        priceManageControlOrderDetailMapper.updateControlOrderDetailDxsDate(controlOrderCode, dxsDate);
    }


}



