package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.PriceDetailHistory;
import com.cowell.pricecenter.entity.PriceDetailHistoryExample;
import com.cowell.pricecenter.mapper.PriceDetailHistoryMapper;
import com.cowell.pricecenter.service.IPriceDetailHistoryService;
import com.cowell.pricecenter.service.dto.request.PriceDetailHistoryParam;
import com.cowell.pricecenter.web.rest.util.Pagination;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 审核日志处理
 *
 * @author: liubw
 * @date 2018/10/29 3:47 PM
 */
@Service("priceDetailHistory")
public class PriceDetailHistoryServiceImpl implements IPriceDetailHistoryService {

    private final Logger logger = LoggerFactory.getLogger(PriceDetailHistoryServiceImpl.class);

    @Autowired
    private PriceDetailHistoryMapper priceDetailHistoryMapper;

    @Override
    public Pagination<PriceDetailHistory> getPriceDetailHistory(PriceDetailHistoryParam param) {
        logger.info("getPriceDetailHistory,param:{}", param);

        Map<String, Object> resultMap = getHistoryDetailResult(param);
        Pagination<PriceDetailHistory> pagination = new Pagination<>(param.getPage(), param.getPageSize());
        pagination.setSource((List<PriceDetailHistory>) resultMap.get("list"));
        pagination.setHitCount(Long.parseLong(resultMap.get("totalSize").toString()));

        return pagination;
    }

    private Map<String, Object> getHistoryDetailResult(PriceDetailHistoryParam param) {
        Map<String, Object> resultMap = new HashMap<>();

        PriceDetailHistoryExample example = new PriceDetailHistoryExample();
        PriceDetailHistoryExample.Criteria criteria = example.createCriteria();

        if (param.getBusinessId() != null) {
            criteria.andBusinessIdEqualTo(param.getBusinessId());
        }
        if (param.getStoreId() != null) {
            criteria.andStoreIdEqualTo(param.getStoreId());
        }
        if (param.getGoodsNo() != null) {
            criteria.andGoodsNoEqualTo(param.getGoodsNo());
        }
        long count = priceDetailHistoryMapper.countByExample(example);

        Integer page = param.getPage();
        Integer pageSize = param.getPageSize();
        if (param.getPage() == null || param.getPage() <= 1) {
            page = 1;
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            page = 10;
        }
        example.setOffset((page-1) * pageSize);
        example.setLimit(pageSize);
        List<PriceDetailHistory> list = priceDetailHistoryMapper.selectByExample(example);
        resultMap.put("list",list);
        resultMap.put("totalSize",count);
        return resultMap;
    }
}
