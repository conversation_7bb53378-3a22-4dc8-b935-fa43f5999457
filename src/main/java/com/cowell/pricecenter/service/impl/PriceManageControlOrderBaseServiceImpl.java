package com.cowell.pricecenter.service.impl;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.pricecenter.entity.PriceManageControlOrder;
import com.cowell.pricecenter.entity.PriceManageControlOrderExample;
import com.cowell.pricecenter.enums.GoodsChooseWayEnum;
import com.cowell.pricecenter.enums.ManageControlOrderTableTypeEnum;
import com.cowell.pricecenter.enums.PermissionFullScopeEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.enums.StatusEnum;
import com.cowell.pricecenter.enums.WarnTypeEnum;
import com.cowell.pricecenter.mapper.PriceManageControlOrderMapper;
import com.cowell.pricecenter.security.SecurityUtils;
import com.cowell.pricecenter.service.IPermissionExtService;
import com.cowell.pricecenter.service.IPriceManageControlOrderBaseService;
import com.cowell.pricecenter.service.dto.request.ControlOrderEditParam;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.WarnRuleDTO;
import com.cowell.pricecenter.utils.RequestHeaderContextUtils;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: pricecenter
 * @description: 管控单表的基础服务接口实现
 * @author: jmlu
 * @create: 2022-08-04 16:07
 **/

@Service
public class PriceManageControlOrderBaseServiceImpl implements IPriceManageControlOrderBaseService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceManageControlOrderBaseServiceImpl.class);

    @Autowired
    private IPermissionExtService permissionExtService;

    @Autowired
    private PriceManageControlOrderMapper priceManageControlOrderMapper;

    private static final Integer NAME_MAX_LENTG = 20;

    private static final Integer REASON_MAX_LENTG = 50;


    @Override
    public void checkAddPriceManageControlOrder(ControlOrderEditParam param) {
        addControlCheck(param);
        checkControlOrderNameAndReason(param.getControlOrderName(), param.getControlReason());
    }

    @Override
    public void checkControlOrderNameAndReason(String controlOrderName, String controlNoExecReason) {
        if(org.apache.commons.lang.StringUtils.isBlank(controlOrderName)){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_NAME_REASON);
        }

        if(controlOrderName.length() > NAME_MAX_LENTG){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_NAME_LENGTH);
        }

        if(StringUtils.isNotBlank(controlNoExecReason) && controlNoExecReason.length() > REASON_MAX_LENTG){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_REASON_LENGTH);
        }
    }

    @Override
    public void deleteOrderLogicById(Long id, TokenUserDTO userDTO) {
        if (id != null) {
        	PriceManageControlOrderExample example = new PriceManageControlOrderExample();
            PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(id);
            PriceManageControlOrder updateData = null;
            if (priceManageControlOrder != null) {
            	updateData = new PriceManageControlOrder();
            	updateData.setStatus(StatusEnum.DELETE.getCode());
            	updateData.setUpdatedBy(userDTO.getUserId());
            	updateData.setUpdatedByName(userDTO.getName());
                example.createCriteria().andControlOrderIdEqualTo(priceManageControlOrder.getControlOrderId()).andIdEqualTo(id);
                priceManageControlOrderMapper.updateByExampleSelective(updateData, example);
            }
        }
    }

    /**
     * 添加管控单机构校验
     * @param param
     */
    private void addControlCheck(ControlOrderEditParam param) {
        TokenUserDTO userDTO = SecurityUtils.getCurrentUserToken();
        List<OrgDTO> orgVOS = permissionExtService.getUserDataScopeChildOrgByOrgId(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), param.getOrgIdList(), 0);
        if (CollectionUtils.isEmpty(orgVOS)) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_ORG_NULL);
        }

        long typeCount = orgVOS.stream().filter(v-> Objects.nonNull(v.getType())).map(v->v.getType()).distinct().count();
        LOGGER.info("addControlCheck|typeCount,{}.",typeCount);
        if(typeCount > 1L){
            LOGGER.info("addControlCheck|不能跨级选择");
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_ORG_CROSS);
        }

        if(!allIsFullScope(orgVOS)){
            LOGGER.info("addControlCheck|组织机构中存在没有全部下级权限的节点");
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_ORG_FULLSCOPE);
        }
    }

    private boolean allIsFullScope(List<OrgDTO> orgVOS){
        if(CollectionUtils.isEmpty(orgVOS)){
            return false;
        }
        return orgVOS.stream().filter(v-> PermissionFullScopeEnum.isAll(v.getIsFullScope())).map(v->v.getIsFullScope()).count() == orgVOS.size();
    }

}
