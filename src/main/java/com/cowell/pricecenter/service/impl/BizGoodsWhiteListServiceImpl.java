package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.cache.CacheVar;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.BizGoodsWhiteListService;
import com.cowell.pricecenter.service.ISearchExtService;
import com.cowell.pricecenter.service.ImportAdjustPriceOrderDetailDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.service.feign.TagFeignService;
import com.cowell.pricecenter.service.feign.facade.ItemSearchEngineFacadeService;
import com.cowell.pricecenter.service.feign.vo.SpuListVO;
import com.cowell.pricecenter.service.feign.vo.SpuQueryParamVO;
import com.cowell.pricecenter.utils.AsyncExport;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.cowell.pricecenter.web.rest.errors.AmisBusinessException;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RSet;
import org.redisson.api.RSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.api.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class BizGoodsWhiteListServiceImpl implements BizGoodsWhiteListService {
    private final Logger logger = LoggerFactory.getLogger(BizGoodsWhiteListServiceImpl.class);

    @Autowired
    private TagFeignService tagFeignService;
    @Autowired
    private ISearchExtService searchExtService;
    @Autowired
    private AsyncExport asyncExport;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ItemSearchEngineFacadeService itemSearchEngineFacadeService;

    // 查询es最大请求数量
    @Value("${whiteList.query.page.max:200}")
    private Integer whiteListQueryPageMax;

    public static final Integer PAGESIZE = 200;

    @Override
    public void editWhiteList(BizGoodsWhiteListParam param, TokenUserDTO userDTO) {
        try {
            Optional.ofNullable(param.getBusinessId()).orElseThrow(() -> new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "连锁ID为空"));
            if (CollectionUtils.isEmpty(param.getGoodsNos())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "请录入商品信息");
            }
            List<TagDTO> tagDTOS = findTagByBusinessId(param.getBusinessId());
            Long tagId = getTagId(tagDTOS, param.getBusinessId(), param.getBusinessShortName(), userDTO);
            //打标
            saveTagEntity(userDTO, param.getGoodsNos(), tagId, param.getBusinessId(), YNEnum.YES);
        } catch (Exception e) {
            logger.error("编辑商品白名单异常:", e);
            throw e;
        }
    }

    private void saveTagEntity(TokenUserDTO userDTO, List<String> goodsNos, Long tagId, Long businessId, YNEnum ynEnum) {
        TagMarkParentParam tagMarkParentParam = new TagMarkParentParam();

        tagMarkParentParam.setUserId(userDTO.getUserId());
        tagMarkParentParam.setUserName(userDTO.getName());
        TagMarkParam tagMarkParam = new TagMarkParam();
        tagMarkParam.setTagCodeList(Sets.newHashSet(businessId.toString()));
        tagMarkParam.setTagType(TagTypeEnums.WHITE_LIST.getType());
        tagMarkParam.setTagsId(Sets.newHashSet(tagId));
        tagMarkParam.setEntityMarkParams(goodsNos.stream().map(v -> {
            EntityMarkParam entityMarkParam = new EntityMarkParam();
            entityMarkParam.setEntityTitle(v);
            entityMarkParam.setEntityCode(v);
            return entityMarkParam;
        }).collect(Collectors.toSet()));

        tagMarkParentParam.setTagMarkParamList(Lists.newArrayList(tagMarkParam));
        tagMarkParentParam.setEntityType(EntityTypeEnum.GOODS.getType());
        tagMarkParentParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
        tagMarkParentParam.setMarkStatus(ynEnum.getType());//1打标 0去标
        tagMarkParentParam.setNeedMarkBaseTag(YNEnum.NO.getType());
        logger.info("tagFeignService.tagMark param:{}", tagMarkParentParam);
        ResponseEntity<CommonRes> markRes = tagFeignService.tagMark(tagMarkParentParam);
        logger.info("tagFeignService.tagMark 结果:{}", JSON.toJSONString(markRes));
    }

    private Long getTagId(List<TagDTO> tagDTOS, Long businessId, String businessShortName, TokenUserDTO userDTO) {
        Long tagId;
        if (CollectionUtils.isEmpty(tagDTOS)) {
            // 不存在 新增白名单标签
            TagSaveParam saveParam = new TagSaveParam();
            saveParam.setEntityType(EntityTypeEnum.GOODS.getType());
            saveParam.setTagCode(businessId.toString());
            saveParam.setTitle(businessShortName.trim());
            saveParam.setTagDesc(businessShortName.trim());
            saveParam.setBizType(TagBizTypeEnum.MARKETING.getCode());// 2 商家中心
            saveParam.setTagType(TagTypeEnums.WHITE_LIST.getType());
            saveParam.setTagLevel(TagLevelEnum.LEVEL_1.getCode());//中台约定1
            saveParam.setTagProperty(TagPropertyEnum.BASE.getType());
            saveParam.setModifierId(userDTO.getUserId());
            saveParam.setModifierName(userDTO.getName());
            saveParam.setCreatorId(userDTO.getUserId());
            saveParam.setCreatorName(userDTO.getName());
            logger.info("tagFeignService.save save param -> {}", saveParam);
            ResponseEntity<Object> saveRes = tagFeignService.save(saveParam);
            if (saveRes == null
                || !(saveRes.getStatusCode().equals(HttpStatus.OK) || saveRes.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, saveRes.getBody().toString());
            }
            try {
                return Long.valueOf(saveRes.getBody().toString());
            } catch (Exception e) {
                throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, "调用标签编辑白名单异常");
            }

        } else {
            // 存在 修改
            TagDTO tagDTO = tagDTOS.get(0);
            TagSaveParam saveParam = new TagSaveParam();
            BeanUtils.copyProperties(tagDTO, saveParam);
            saveParam.setEntityType(EntityTypeEnum.GOODS.getType());
            saveParam.setBizType(TagBizTypeEnum.MARKETING.getCode());// 2 商家中心
            saveParam.setTagType(tagDTO.getType());
            saveParam.setTagLevel(TagLevelEnum.LEVEL_1.getCode());//中台约定1
            saveParam.setTagProperty(TagPropertyEnum.BASE.getType());
            saveParam.setCreatorId(userDTO.getUserId());
            saveParam.setCreatorName(userDTO.getName());
            saveParam.setModifierId(userDTO.getUserId());
            saveParam.setModifierName(userDTO.getName());
            logger.info("tagFeignService.save update param -> {}", saveParam);
            ResponseEntity<Object> saveRes = tagFeignService.save(saveParam);
            return tagDTO.getId();
        }

    }

    private List<TagDTO> findTagByBusinessId(Long businessId) {
        TagPageParam tagParam = new TagPageParam();
        tagParam.setBizType(TagBizTypeEnum.MARKETING.getCode());
        tagParam.setTagType(TagTypeEnums.WHITE_LIST.getType());
        tagParam.setTagCodeList(Lists.newArrayList(businessId.toString()));
        tagParam.setPage(1L);
        tagParam.setPageSize(1);
        logger.info("tagFeignService.queryByPage param:{}", tagParam);
        ResponseEntity<CommonRes<PageResult<TagDTO>>> commonRes = tagFeignService.queryByPage(tagParam);
        if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
            || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())) {
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, "查询白名单异常");
        }
        return commonRes.getBody().getData().getRows();
    }

    private List<TagDTO> batchFindTagByBusinessId(Set<Long> businessIdList) {
        TagPageParam tagParam = new TagPageParam();
        tagParam.setBizType(TagBizTypeEnum.MARKETING.getCode());
        tagParam.setTagType(TagTypeEnums.WHITE_LIST.getType());
        tagParam.setTagCodeList(businessIdList.stream().map(v -> v.toString()).collect(Collectors.toList()));
        tagParam.setPage(1L);
        tagParam.setPageSize(businessIdList.size());
        logger.info("tagFeignService.queryByPage param:{}", tagParam);
        ResponseEntity<CommonRes<PageResult<TagDTO>>> commonRes = tagFeignService.queryByPage(tagParam);
        if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
            || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())) {
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, "查询白名单异常");
        }
        return commonRes.getBody().getData().getRows();
    }

    @Override
    public com.cowell.pricecenter.service.dto.response.amis.PageResult<WhiteListDTO> getWhiteList(SearchWhiteListParam param, TokenUserDTO userDTO) {
        try {
            if (CollectionUtils.isEmpty(param.getBusinessIdList())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "连锁ID不能为空");
            }
            List<TagDTO> tagDTOS = batchFindTagByBusinessId(param.getBusinessIdList());
            if (CollectionUtils.isEmpty(tagDTOS)) {
                logger.info("所选连锁没有维护商品白名单 businessIdList:{}", param.getBusinessIdList());
                return new com.cowell.pricecenter.service.dto.response.amis.PageResult(0L, new ArrayList());
            }
            Map<String, TagDTO> tagMap = tagDTOS.stream().collect(Collectors.toMap(TagDTO::getTagCode, Function.identity(), (k1, k2) -> k1));
            SpuNewParamVo paramVo = new SpuNewParamVo();
            paramVo.setTagList(tagDTOS.stream().map(TagDTO::getTagIndex).collect(Collectors.toList()));
            if (Objects.nonNull(param.getCateMarketId())) {
                paramVo.setCategoryId(param.getCateMarketId());
            }
            if (StringUtils.isNotBlank(param.getGoodsline())) {
                paramVo.setGoodsline(param.getGoodsline());
            }
            if (StringUtils.isNotBlank(param.getSpecialattributes())) {
                paramVo.setSpecialattributes(param.getSpecialattributes());
            }
            if (StringUtils.isNotBlank(param.getPushlevel())) {
                paramVo.setPushlevel(param.getPushlevel());
            }
            TagEntityQueryServerParam queryServerParam = new TagEntityQueryServerParam();
            queryServerParam.setTagCodeList(param.getBusinessIdList().stream().map(v -> v.toString()).collect(Collectors.toList()));
            queryServerParam.setTagType(TagTypeEnums.WHITE_LIST.getType());
            queryServerParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
            queryServerParam.setEntityType(EntityTypeEnum.GOODS.getType());
            queryServerParam.setResultType(ResultTypeEnums.ENTITYCODE.getType());
            queryServerParam.setManagerFlag(YNEnum.YES.getType());
            if (param.getBusinessIdList().size() > 1) {
                // 多个连锁取白名单交集
                Set<String> goodsNoSet = new HashSet<>();
                for (int i = 1; ; i++) {
                    queryServerParam.setPage(i);
                    logger.info("标签查询实体Code tagFeignService.tagEntityCodeListQuery param:{}", queryServerParam);
                    ResponseEntity<CommonRes<PageResult<String>>> commonRes = tagFeignService.tagEntityCodeListQuery(queryServerParam);
                    if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
                        || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())) {
                        throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, "标签查询实体Code异常");
                    }
                    PageResult<String> data = commonRes.getBody().getData();
                    goodsNoSet.addAll(data.getRows());
                    if (data.getTotal() <= queryServerParam.getPage() * queryServerParam.getPrePage() || data.getRows().size() <= 0) {
                        logger.info("标签查询实体Code 完成");
                        break;
                    }
                }
                if (CollectionUtils.isEmpty(goodsNoSet)) {
                    logger.info("多个连锁取交集为空,直接返回空商品");
                    return new com.cowell.pricecenter.service.dto.response.amis.PageResult(0L, new ArrayList());
                }
                paramVo.setGoodsNoList(Lists.newArrayList(goodsNoSet));
                if (StringUtils.isNotBlank(param.getGoodsNo())) {
                    if (!goodsNoSet.contains(param.getGoodsNo())) {
                        return new com.cowell.pricecenter.service.dto.response.amis.PageResult(0L, new ArrayList());
                    }
                    paramVo.setGoodsNoList(Lists.newArrayList(param.getGoodsNo()));
                } else if (CollectionUtils.isNotEmpty(param.getGoodsNos())) {
                    List<String> goodsNos = param.getGoodsNos().stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                    goodsNoSet = goodsNoSet.stream().filter(v -> goodsNos.contains(v)).collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(goodsNoSet)) {
                        return new com.cowell.pricecenter.service.dto.response.amis.PageResult(0L, new ArrayList());
                    }
                    paramVo.setGoodsNoList(Lists.newArrayList(goodsNoSet));
                }
            } else {
                if (StringUtils.isNotBlank(param.getGoodsNo())) {
                    paramVo.setGoodsNoList(Lists.newArrayList(param.getGoodsNo()));
                } else if (CollectionUtils.isNotEmpty(param.getGoodsNos())) {
                    paramVo.setGoodsNoList(param.getGoodsNos());
                }
            }
            paramVo.setBusinessId(param.getBusinessIdList().stream().findFirst().get());
            paramVo.setPage(param.getPage());
            paramVo.setPageSize(param.getSize());
            TagDTO tagDTO = tagMap.get(paramVo.getBusinessId().toString());
            PageResponse<List<SpuNewVo>> pageResponse = getTagGoodsBySearch(paramVo);
            return new com.cowell.pricecenter.service.dto.response.amis.PageResult(pageResponse.getTotalSize(), pageResponse.getResult().stream().map(v -> {
                WhiteListDTO dto = new WhiteListDTO();
                BeanUtils.copyProperties(v, dto);
                dto.setBusinessShortName(tagDTO.getTitle());
                dto.setOperaterId(tagDTO.getModifierId());
                dto.setOperater(tagDTO.getModifierName());
                dto.setOperateDate(tagDTO.getGmtUpdate());
                return dto;
            }).collect(Collectors.toList()));
        } catch (Exception e) {
            logger.error("获取商品白名单列表异常:", e);
            throw e;
        }
    }

    private PageResponse<List<SpuNewVo>> getTagGoodsBySearch(SpuNewParamVo paramVo) {
        //typeData = 1 带分页
        paramVo.setTypeData(1);
        logger.info("调用搜索查询商品信息 searchService.getNewSpuList4Post param:{}", paramVo);
        PageResult<SpuNewVo> spuListPage = itemSearchEngineFacadeService.getNewSpuListPage(paramVo);
//        ResponseEntity<PageResponse<List<SpuNewVo>>> responseEntity = searchService.getNewSpuList4Post(paramVo);
        PageResponse<List<SpuNewVo>> pageResponse = new PageResponse<>();
        pageResponse.setPage(paramVo.getPage());
        pageResponse.setPageSize(paramVo.getPageSize());
        pageResponse.setTotalSize(spuListPage.getTotal());
        pageResponse.setResult(spuListPage.getRows());
        return pageResponse;
    }

    @Override
    public void whiteListRemoveGoods(BizGoodsWhiteListParam param, TokenUserDTO userDTO) {
        try {
            Optional.ofNullable(param.getBusinessId()).orElseThrow(() -> new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "连锁ID为空"));
            if (CollectionUtils.isEmpty(param.getGoodsNos())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "商品信息为空");
            }
            List<TagDTO> tagDTOS = findTagByBusinessId(param.getBusinessId());
            if (CollectionUtils.isEmpty(tagDTOS)) {
                logger.info("连锁ID:{},没有维护白名单", param.getBusinessId());
                return;
            }
            //去标
            saveTagEntity(userDTO, param.getGoodsNos(), tagDTOS.get(0).getId(), param.getBusinessId(), YNEnum.NO);

        } catch (Exception e) {
            logger.error("白名单删除商品异常:", e);
            throw e;
        }
    }

    @Override
    public List<String> getWhiteListAllGoodsNoList(SearchWhiteListParam param, TokenUserDTO userDTO) {
        try {
            if (CollectionUtils.isEmpty(param.getBusinessIdList())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "连锁ID不能为空");
            }
            if (param.getBusinessIdList().size() != 1) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "仅支持单连锁查询");
            }
            List<TagDTO> tagDTOS = batchFindTagByBusinessId(param.getBusinessIdList());
            if (CollectionUtils.isEmpty(tagDTOS)) {
                logger.info("所选连锁没有维护商品白名单 businessIdList:{}", param.getBusinessIdList());
                return new ArrayList<>();
            }
            TagDTO tagDTO = tagDTOS.get(0);
            SpuNewParamVo paramVo = new SpuNewParamVo();
            paramVo.setTagList(Lists.newArrayList(tagDTO.getTagIndex()));
            if (Objects.nonNull(param.getCateMarketId())) {
                paramVo.setCategoryId(param.getCateMarketId());
            }
            if (StringUtils.isNotBlank(param.getGoodsNo())) {
                paramVo.setGoodsNoList(Lists.newArrayList(param.getGoodsNo()));
            }
            if (StringUtils.isNotBlank(param.getGoodsline())) {
                paramVo.setGoodsline(param.getGoodsline());
            }
            if (StringUtils.isNotBlank(param.getSpecialattributes())) {
                paramVo.setSpecialattributes(param.getSpecialattributes());
            }
            if (StringUtils.isNotBlank(param.getPushlevel())) {
                paramVo.setPushlevel(param.getPushlevel());
            }
            paramVo.setBusinessId(param.getBusinessIdList().stream().findFirst().get());
            List<String> goodsNos = new ArrayList<>();
            for (int i = 1 ;; i++) {
                paramVo.setPage(i);
                paramVo.setPageSize(whiteListQueryPageMax);
                PageResponse<List<SpuNewVo>> pageResponse = getTagGoodsBySearch(paramVo);
                List<SpuNewVo> spuNewVos = pageResponse.getResult();
                if (pageResponse.getTotalSize() == 0L || CollectionUtils.isEmpty(spuNewVos)) {
                    break;
                }
                goodsNos.addAll(spuNewVos.stream().map(SpuNewVo::getGoodsNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList()));
                if (i * whiteListQueryPageMax >= pageResponse.getTotalSize()) {
                    break;
                }
            }
            return goodsNos;
        } catch (Exception e) {
            logger.error("获取白名单下所有商品编码异常:", e);
            throw e;
        }

    }

    @Override
	public void addCacheWhiteList(BizGoodsWhiteListParam param, TokenUserDTO userDTO) {
		if(null==param || CollectionUtils.isEmpty(param.getGoodsNos()) || StringUtils.isBlank(param.getUuid())) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, ReturnCodeEnum.PARAM_ERROR.getMessage());
		}
		saveCacheWhiteList(param.getGoodsNos(), param.getUuid());
	}

    /**
     * 保存redis
     * @param goodsNoList
     * @param userDTO
     */
    private void saveCacheWhiteList(List<String> goodsNoList,String uuid) {
    	RList<String> cacheWhiteList = redissonClient.getList(getGoodsWhiteListRedisKey(uuid));
		Lists.partition(goodsNoList, 100).forEach(subList -> {
			cacheWhiteList.removeAll(subList);
		});
		cacheWhiteList.addAll(goodsNoList);
		cacheWhiteList.expire(Constants.TIMETOLIVEHOURS,TimeUnit.HOURS);
    }

	/**
	 * 商品白名单临时存储key
	 * @param uuid
	 * @return
	 */
    private String getGoodsWhiteListRedisKey(String uuid) {
	   return RedisKeysConstant.GOODS_WHITELIST_ITEM+uuid;
    }

   /**
	 * 商品白名单导入结果key
	 * @param uuid
	 * @return
	 */
    private String getGoodsWhiteListImportResultRedisKey(String uuid) {
	   return RedisKeysConstant.GOODS_WHITELIST_IMPORT_RESULT+uuid;
    }

    /**
	 * 商品白名单导入异常明细key
	 * @param uuid
	 * @return
	 */
    private String getGoodsWhiteListImportErrorItemRedisKey(String uuid) {
	   return RedisKeysConstant.GOODS_WHITELIST_IMPORT_ERROR_ITEM+uuid;
    }

	@Override
	public com.cowell.pricecenter.service.dto.response.amis.PageResult<WhiteListDTO> getCacheWhiteList(SearchWhiteListParam param, TokenUserDTO userDTO) {
		if(null==param || StringUtils.isBlank(param.getUuid())) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, ReturnCodeEnum.PARAM_ERROR.getMessage());
		}
		RList<String> cacheWhiteList = redissonClient.getList(getGoodsWhiteListRedisKey(param.getUuid()));
		List<String> goodsNoList = Lists.newArrayList();
		int total = 0;
		if(StringUtils.isNotBlank(param.getGoodsNo())) {
			int index = cacheWhiteList.indexOf(param.getGoodsNo());
			if(index>=0) {
				String goodsNo = cacheWhiteList.get(index);
				goodsNoList.add(goodsNo);
				total = goodsNoList.size();
			}
		}else {
			int fromIndex = (param.getPage()-1)*param.getSize();
			int toIndex = fromIndex + param.getSize()-1;
			goodsNoList = cacheWhiteList.range(fromIndex, toIndex);
			total = cacheWhiteList.size();
		}
		if(total==0) {
			return new com.cowell.pricecenter.service.dto.response.amis.PageResult<WhiteListDTO>(Long.valueOf(total),Lists.newArrayList());
		}
		SpuQueryParamVO paramVo = new SpuQueryParamVO();
        paramVo.setGoodsNoList(goodsNoList);
        paramVo.setPage(1);
        paramVo.setPageSize(goodsNoList.size());
        List<SpuListVO> spuList = searchExtService.getSpuList(paramVo);
        if (CollectionUtils.isEmpty(spuList)) {
        	throw new BusinessErrorException("没有获取到商品信息");
        }
        return new com.cowell.pricecenter.service.dto.response.amis.PageResult<WhiteListDTO>(Long.valueOf(total), spuList.stream().map(v -> {
            WhiteListDTO dto = new WhiteListDTO();
            BeanUtils.copyProperties(v, dto);
            dto.setHabitat(v.getProdarea());
            dto.setUnit(v.getGoodsunit());
            return dto;
        }).collect(Collectors.toList()));
	}

	@Override
	public void deleteCacheWhiteList(SearchWhiteListParam param, TokenUserDTO userDTO) {
		if(null==param || StringUtils.isBlank(param.getGoodsNo()) || StringUtils.isBlank(param.getUuid())) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, ReturnCodeEnum.PARAM_ERROR.getMessage());
		}
		RList<String> cacheWhiteList = redissonClient.getList(getGoodsWhiteListRedisKey(param.getUuid()));
		cacheWhiteList.remove(param.getGoodsNo());
	}

	@Override
	public void importCacheWhiteList(BizGoodsWhiteListParam param) {
		if(null==param || StringUtils.isBlank(param.getUuid()) || CollectionUtils.isEmpty(param.getGoodsNos())) {
    		throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, ReturnCodeEnum.PARAM_ERROR.getMessage());
    	}
		List<ImportExcelModel> importList = Lists.newArrayList();
		String importResultRedisKey = getGoodsWhiteListImportResultRedisKey(param.getUuid());
    	RBucket<AdjustImportResultDTO> bucket = redissonClient.getBucket(importResultRedisKey);
    	if(bucket.isExists()) {
    		bucket.delete();
    	}
		//验证商品编码
		try {
			importList = checkGoodsNoIsExist(param.getGoodsNos());
			List<ImportExcelModel> passList = importList.stream().filter(x -> x.getResult()).collect(Collectors.toList());
			List<ImportExcelModel> noPassList = importList.stream().filter(x -> !x.getResult()).collect(Collectors.toList());
			AdjustImportResultDTO importResult = new AdjustImportResultDTO();
			importResult.setTotal(param.getGoodsNos().size());
			importResult.setSuccessCount(passList.size());
			importResult.setFailCount(noPassList.size());
			importResult.setStatus(ImportStatusEnum.IMPORTING.getCode());
			redissonClient.getBucket(importResultRedisKey).set(importResult,15,TimeUnit.MINUTES);
			if(CollectionUtils.isNotEmpty(noPassList)) {
				saveCacheNoExistGoodsNoData(noPassList, param.getUuid());
			}
			if(CollectionUtils.isNotEmpty(passList)) {
				List<String> goodsNoList = passList.stream().map(ImportExcelModel::getGoodsNo).distinct().collect(Collectors.toList());
				TokenUserDTO userDTO = new TokenUserDTO();
				userDTO.setUserId(param.getUserId());
				saveCacheWhiteList(goodsNoList, param.getUuid());
			}
			importResult.setStatus(ImportStatusEnum.IMPORT_FINISH.getCode());
			redissonClient.getBucket(importResultRedisKey).set(importResult,15,TimeUnit.MINUTES);
		} catch (Exception e) {
			AdjustImportResultDTO importResult = new AdjustImportResultDTO();
			importResult.setTotal(param.getGoodsNos().size());
			importResult.setSuccessCount(0);
			importResult.setFailCount(param.getGoodsNos().size());
			importResult.setStatus(ImportStatusEnum.IMPORT_FAIL.getCode());
			redissonClient.getBucket(importResultRedisKey).set(importResult,15,TimeUnit.MINUTES);
		}
	}

	/**
	 * 把导入异常数据存储redis
	 * @param noPassList
	 * @param userId
	 */
	private void saveCacheNoExistGoodsNoData(List<ImportExcelModel> noPassList,String uuid) {
		String key = getGoodsWhiteListImportErrorItemRedisKey(uuid);
		RList<ImportExcelModel> resultList = redissonClient.getList(key);
		resultList.clear();
		resultList.addAll(noPassList);
		resultList.expire(15,TimeUnit.MINUTES);
	}

	/**
    *
    * @Title: checkGoodsNoIsExist
    * @Description: 验证商品编码是存在
    * @param: @param param
    * @return: void
    * @throws
    */
   private List<ImportExcelModel> checkGoodsNoIsExist(List<String> goodsNoList) {
	   List<ImportExcelModel> importList = Lists.newArrayList();
	   List<List<String>> partition = Lists.partition(goodsNoList, 50);
	   SpuQueryParamVO paramVo = null;
	   PageResult<SpuListVO> pageResponse = null;
	   ImportExcelModel importExcel = null;
	   for (List<String> subGoodsNoList : partition) {
           paramVo = new SpuQueryParamVO();
           paramVo.setGoodsNoList(subGoodsNoList);
           paramVo.setPage(1);
           paramVo.setPageSize(subGoodsNoList.size());
           pageResponse = itemSearchEngineFacadeService.getSpuList(paramVo);
	       if (pageResponse == null
                   || null == pageResponse.getRows()) {
	    	   for (String goodsNo : subGoodsNoList) {
	    		   importExcel = new ImportExcelModel();
	    		   importExcel.setGoodsNo(goodsNo);
	    		   importExcel.setMessage(ReturnCodeEnum.GOODS_NOT_EXIST.getMessage());
	    		   importExcel.setResult(false);
	    		   importList.add(importExcel);
	    	   }
	    	   continue;
            }
            List<SpuListVO> result = pageResponse.getRows();
    		Map<String, SpuListVO> goodsNoMap = result.stream().collect(Collectors.toMap(SpuListVO::getGoodsNo, Function.identity(), (v1, v2) -> v1));
    		for (String goodsNo : subGoodsNoList) {
    			importExcel = new ImportExcelModel();
    			importExcel.setGoodsNo(goodsNo);
    			if(goodsNoMap.containsKey(goodsNo)) {
    				importExcel.setResult(true);
    			}else {
    				importExcel.setMessage(ReturnCodeEnum.GOODS_NOT_EXIST.getMessage());
    				importExcel.setResult(false);
    			}
    			importList.add(importExcel);
	    	}
	   }
	   return importList;
   }

	@Override
	public AdjustImportResultDTO importCacheWhiteListStatus(BizGoodsWhiteListParam param) {
		if(null==param || StringUtils.isBlank(param.getUuid())) {
    		throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, ReturnCodeEnum.PARAM_ERROR.getMessage());
    	}
		AdjustImportResultDTO importResult = new AdjustImportResultDTO();
		String key = getGoodsWhiteListImportResultRedisKey(param.getUuid());
		RBucket<AdjustImportResultDTO> bucket = redissonClient.getBucket(key);
		if(bucket.isExists()) {
    		importResult = bucket.get();
    	}
		return importResult;
	}

	@Override
	public ExportFileCubeVO<ImportExcelModel> asyncExportErrorWhiteListDetailsFile(SearchWhiteListParam param) {
		if(null==param || StringUtils.isBlank(param.getUuid())) {
    		throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, ReturnCodeEnum.PARAM_ERROR.getMessage());
    	}
		int fromIndex = (param.getPage()-1)*param.getSize();
		int toIndex = fromIndex + param.getSize()-1;
		String key = getGoodsWhiteListImportErrorItemRedisKey(param.getUuid());
		RList<ImportExcelModel> resultList = redissonClient.getList(key);
		List<ImportExcelModel> exceptionDetail = resultList.range(fromIndex, toIndex);
		if(CollectionUtils.isEmpty(exceptionDetail)) {
			exceptionDetail = Lists.newArrayList();
		}
		ExportFileCubeVO<ImportExcelModel> exportFileCubeVO = new ExportFileCubeVO<ImportExcelModel>();
		exportFileCubeVO.setDataList(exceptionDetail);
		exportFileCubeVO.setFieldMap(ImportExcelModel.getExportFieldMap());
		return exportFileCubeVO;
	}

	@Override
	public void submitWhiteList(BizGoodsWhiteListParam param, TokenUserDTO userDTO) {
        AdjustImportResultDTO result = new AdjustImportResultDTO();
		try {
			Optional.ofNullable(param.getUuid()).orElseThrow(() -> new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "唯一id不能为空"));
            if (CollectionUtils.isEmpty(param.getBusinessIdList())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "连锁ID不能为空");
            }
            RList<String> cacheWhiteList = redissonClient.getList(getGoodsWhiteListRedisKey(param.getUuid()));
            if (CollectionUtils.isEmpty(cacheWhiteList)) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "商品白名单已过期，请重新添加商品");
            }
            //不做企业级商品验证20230720
            //checkBusinessInGoodsNo(param.getBusinessIdList(), cacheWhiteList, param.getUuid());
            String businessName = "";
            List<TagDTO> tagDTOS = null;
            List<String> goodsNoList = null;
            for (Long businessId : param.getBusinessIdList()) {
            	businessName = CacheVar.getBusinessName(businessId);
            	tagDTOS = findTagByBusinessId(businessId);
                Long tagId = getTagId(tagDTOS, businessId, businessName, userDTO);
                for (int page = 1;; page++) {
                    int fromIndex = (page-1)*whiteListQueryPageMax;
        			int toIndex = fromIndex + whiteListQueryPageMax-1;
        			goodsNoList = cacheWhiteList.range(fromIndex, toIndex);
        			if(CollectionUtils.isEmpty(goodsNoList)) {
        				break;
        			}
                    //打标
                    saveTagEntity(userDTO, goodsNoList, tagId, businessId, YNEnum.YES);
                }
			}
            redissonClient.getList(getGoodsWhiteListRedisKey(param.getUuid())).clear();
            result.setStatus(ImportStatusEnum.IMPORT_FINISH.getCode());
            result.setMsg("提交完成");
            redissonClient.getBucket(RedisKeysConstant.SUBMIT_GOODS_WHITELIST_RESULT+param.getUuid()).set(result,10, TimeUnit.MINUTES);
        } catch (Exception e) {
            result.setStatus(ImportStatusEnum.IMPORT_FAIL.getCode());
            result.setMsg("提交异常");
            redissonClient.getBucket(RedisKeysConstant.SUBMIT_GOODS_WHITELIST_RESULT+param.getUuid()).set(result,10, TimeUnit.MINUTES);
            logger.error("提交商品白名单异常:", e);
            throw e;
        }
	}

	private void checkBusinessInGoodsNo(List<Long> businessIdList,List<String> goodsNoList,String uuid) {
		RList<String> cacheWhiteList = redissonClient.getList(getGoodsWhiteListRedisKey(uuid));
        if (CollectionUtils.isEmpty(cacheWhiteList)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "商品白名单已过期，请重新添加商品");
        }
        StringBuffer businessGoodsNoSb = new StringBuffer();
        StringBuffer goodsSb = null;
		// 打标时需要校验商品是不是该连锁下的
		for (Long businessId : businessIdList) {
			goodsSb = new StringBuffer();
			String businessName = CacheVar.getBusinessName(businessId);
			for (int page = 1;; page++) {
                int fromIndex = (page-1)*whiteListQueryPageMax;
    			int toIndex = fromIndex + whiteListQueryPageMax-1;
    			goodsNoList = cacheWhiteList.range(fromIndex, toIndex);
    			if(CollectionUtils.isEmpty(goodsNoList)) {
    				break;
    			}
    			SpuNewParamVo paramVo = new SpuNewParamVo();
    	        paramVo.setBusinessId(businessId);
    	        paramVo.setGoodsNoList(goodsNoList);
    	        paramVo.setPage(1);
    	        paramVo.setPageSize(goodsNoList.size());
    	        PageResponse<List<SpuNewVo>> pageResponse = getTagGoodsBySearch(paramVo);
    	        if (CollectionUtils.isEmpty(pageResponse.getResult())) {
    	        	goodsSb.append(Joiner.on(",").join(goodsNoList));
    	        	continue;
    	        }
    	        //过滤后的商品编码
    	        List<String> goodsNos = pageResponse.getResult().stream().map(SpuNewVo::getGoodsNo).filter(StringUtils :: isNotBlank).distinct().collect(Collectors.toList());
    	        //取交差集
    	        List<String> diffList = goodsNoList.stream().filter(item -> !goodsNos.contains(item)).collect(Collectors.toList());
    	        if(CollectionUtils.isNotEmpty(diffList)) {
    	        	goodsSb.append(Joiner.on(",").join(diffList));
    	        }
            }
			if(StringUtils.isNotBlank(goodsSb)){
				String business = StringUtils.isEmpty(businessName)?String.valueOf(businessId):businessName;
				businessGoodsNoSb.append(business).append("连锁不包含以下商品《").append(goodsSb.toString()).append("》");
			}
		}
		if(StringUtils.isNotBlank(businessGoodsNoSb)) {
			throw new AmisBusinessException(ReturnCodeEnum.PARAM_ERROR, businessGoodsNoSb.toString());
		}
	}
}
