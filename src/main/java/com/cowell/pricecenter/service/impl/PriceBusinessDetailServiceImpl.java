package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.service.PriceBusinessDetailHistoryService;
import com.cowell.pricecenter.utils.IdGeneratorUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import com.cowell.pricecenter.mapper.PriceBusinessDetailMapper;
import com.cowell.pricecenter.mapper.extension.PriceBusinessDetailExtMapper;
import com.cowell.pricecenter.service.PriceBusinessDetailService;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:25
 */
@Service
public class PriceBusinessDetailServiceImpl implements PriceBusinessDetailService {

    @Resource
    private PriceBusinessDetailMapper priceBusinessDetailMapper;
    @Resource
    private PriceBusinessDetailExtMapper priceBusinessDetailExtMapper;

    @Autowired
    private PriceBusinessDetailHistoryService priceBusinessDetailHistoryService;

    @Override
    public long countByExample(PriceBusinessDetailExample example) {
        return priceBusinessDetailMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PriceBusinessDetailExample example) {
        return priceBusinessDetailMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(String id) {
        return priceBusinessDetailMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PriceBusinessDetail record) {
        return priceBusinessDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceBusinessDetail record) {
        return priceBusinessDetailMapper.insertSelective(record);
    }

    @Override
    public List<PriceBusinessDetail> selectByExample(PriceBusinessDetailExample example) {
        return priceBusinessDetailMapper.selectByExample(example);
    }

    @Override
    public PriceBusinessDetail selectByPrimaryKey(String id) {
        return priceBusinessDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(PriceBusinessDetail record, PriceBusinessDetailExample example) {
        return priceBusinessDetailMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceBusinessDetail record, PriceBusinessDetailExample example) {
        return priceBusinessDetailMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceBusinessDetail record) {
        return priceBusinessDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceBusinessDetail record) {
        return priceBusinessDetailMapper.updateByPrimaryKey(record);
    }

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public int updateByBusinessGoodsSelective(PriceBusinessDetail record) {
		return priceBusinessDetailExtMapper.updateByBusinessGoodsSelective(record);
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public int batchInsert(List<PriceBusinessDetail> list) {
		return priceBusinessDetailExtMapper.batchInsert(list);
	}

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public int batchDelete(List<PriceBusinessDetail> list) {
		return priceBusinessDetailExtMapper.batchDelete(list);
	}
	
	@Override
	public int deletePriceBusinessDetail(Long businessId, String goodsNo) {
		return priceBusinessDetailExtMapper.deletePriceBusinessDetail(businessId, goodsNo);
	}

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(List<PriceBusinessDetail> priceBusinessDetailList) {
        // 先删除，在增加
        this.batchDelete(priceBusinessDetailList);
        this.batchInsert(priceBusinessDetailList);

        List<Long> batchIdList = IdGeneratorUtils.getPriceBusinessDetailHistoryIdBatch(priceBusinessDetailList.size());
        List<PriceBusinessDetailHistory> priceBusinessDetailHistoryList = priceBusinessDetailList.stream().map(priceBusinessDetail -> {
            PriceBusinessDetailHistory history = new PriceBusinessDetailHistory();
            BeanUtils.copyProperties(priceBusinessDetail, history);
            history.setId(String.valueOf(batchIdList.remove(0)));
            return history;
        }).collect(Collectors.toList());
        priceBusinessDetailHistoryService.batchInsert(priceBusinessDetailHistoryList);
    }

}


