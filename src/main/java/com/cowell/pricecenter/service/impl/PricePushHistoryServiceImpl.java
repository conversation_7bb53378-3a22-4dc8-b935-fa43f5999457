package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.cowell.pricecenter.cache.CacheVar;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.PriceCopyInfo;
import com.cowell.pricecenter.entity.PriceCopyInfoExample;
import com.cowell.pricecenter.entity.PricePushHistory;
import com.cowell.pricecenter.entity.PricePushHistoryExample;
import com.cowell.pricecenter.enums.DealStepStatusEnum;
import com.cowell.pricecenter.enums.PriceManageStatusApiEnum;
import com.cowell.pricecenter.enums.PriceTypeModeEnum;
import com.cowell.pricecenter.enums.PriceUpdateResultEnum;
import com.cowell.pricecenter.enums.PushResultEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.PriceCopyInfoMapper;
import com.cowell.pricecenter.mapper.PricePushHistoryMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgStoreDetailExtMapper;
import com.cowell.pricecenter.mapper.extension.PricePushHistoryExtMapper;
import com.cowell.pricecenter.mq.producer.PricePushProducer;
import com.cowell.pricecenter.param.PricePushHistoryParam;
import com.cowell.pricecenter.service.FeignStoreService;
import com.cowell.pricecenter.service.IAdjustPriceOrderV2Service;
import com.cowell.pricecenter.service.IPermissionExtService;
import com.cowell.pricecenter.service.PriceManageControlOrderReadService;
import com.cowell.pricecenter.service.PricePushHistoryService;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderExtend;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderOrgStoreDetailExtend;
import com.cowell.pricecenter.service.dto.MdmStoreBaseDTO;
import com.cowell.pricecenter.service.dto.PriceCopyInfoExtend;
import com.cowell.pricecenter.service.dto.PricePushHistoryDTO;
import com.cowell.pricecenter.service.dto.PricePushHistoryStatisticsDTO;
import com.cowell.pricecenter.service.dto.request.ControlOrderStatusParam;
import com.cowell.pricecenter.service.dto.response.ForestGoodsDTO;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.feign.facade.SearchFeignFacadeService;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.service.vo.PricePushVo;
import com.cowell.pricecenter.utils.PriceUtil;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.vo.PricePushResultVO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Maps;

import cn.hutool.log.Log;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/7 17:51
 */

@Service
public class PricePushHistoryServiceImpl implements PricePushHistoryService {

	private final Logger logger = LoggerFactory.getLogger(PricePushHistoryServiceImpl.class);
	
    @Resource
    private PricePushHistoryMapper pricePushHistoryMapper;

    @Resource
    private FeignStoreService feignStoreService;

    @Resource
    private PricePushHistoryExtMapper pricePushHistoryExtMapper;

    @Resource
    private PriceManageControlOrderReadService priceManageControlOrderReadService;

    @Resource
    private SearchFeignFacadeService searchFeignFacadeService;
    
    @Resource
    private IPermissionExtService permissionExtService;
    
    @Autowired
    private IAdjustPriceOrderV2Service priceOrderV2Service;
    
    @Autowired
    private PricePushProducer pricePushProducer;
    
    @Resource
    private AdjustPriceOrderMapper adjustPriceOrderMapper;
    
    @Autowired
    private PriceCopyInfoMapper priceCopyInfoMapper;
    
    @Autowired
    private AdjustPriceOrderOrgStoreDetailExtMapper adjustPriceOrderOrgStoreDetailExtMapper;


    @Override
    public long countByExample(PricePushHistoryExample example) {
        return pricePushHistoryMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PricePushHistoryExample example) {
        return pricePushHistoryMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return pricePushHistoryMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PricePushHistory record) {
        return pricePushHistoryMapper.insert(record);
    }

    @Override
    public int insertOrUpdate(PricePushHistory record) {
        return pricePushHistoryMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(PricePushHistory record) {
        return pricePushHistoryMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int insertSelective(PricePushHistory record) {
        return pricePushHistoryMapper.insertSelective(record);
    }

    @Override
    public List<PricePushHistory> selectByExample(PricePushHistoryExample example) {
        return pricePushHistoryMapper.selectByExample(example);
    }

    @Override
    public PricePushHistory selectByPrimaryKey(Long id) {
        return pricePushHistoryMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(PricePushHistory record, PricePushHistoryExample example) {
        return pricePushHistoryMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PricePushHistory record, PricePushHistoryExample example) {
        return pricePushHistoryMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(PricePushHistory record) {
        return pricePushHistoryMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PricePushHistory record) {
        return pricePushHistoryMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<PricePushHistory> list) {
        return pricePushHistoryMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<PricePushHistory> list) {
        return pricePushHistoryMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<PricePushHistory> list) {
        return pricePushHistoryMapper.batchInsert(list);
    }

    @Override
    public void updateFailedDetail(String pushCode, Integer batch, PricePushHistory historyUpdate, List<PricePushResultVO.Detail> priceList) {
        if (CollectionUtils.isEmpty(priceList)) {
            return;
        }
        String storeNo = priceList.get(0).getStoreId();
        MdmStoreBaseDTO mdmStoreBase = feignStoreService.getMdmStoreBase(storeNo);
        if (Objects.nonNull(mdmStoreBase)) {
            priceList.forEach(item -> {
                // 更新失败的状态
                PricePushHistoryExample updateFailHistoryExample = new PricePushHistoryExample();
                PricePushHistoryExample.Criteria updateHistoryExampleCriteria = updateFailHistoryExample.createCriteria();
                updateHistoryExampleCriteria.andStoreIdEqualTo(mdmStoreBase.getStoreId());
                updateHistoryExampleCriteria.andPushCodeEqualTo(pushCode);
                updateHistoryExampleCriteria.andBatchEqualTo(batch);
                updateHistoryExampleCriteria.andGoodsNoEqualTo(item.getGoodsNo());
                updateHistoryExampleCriteria.andPriceTypeCodeEqualTo(item.getPriceTypeCode());

                // 明细级别只有成功和失败，没有部分成功
                if (Objects.nonNull(historyUpdate.getReceiveStatus()) && !historyUpdate.getReceiveStatus().equals(PushResultEnum.SUCCESS.getCode())) {
                    historyUpdate.setReceiveStatus(PriceUpdateResultEnum.FAILED.getResult());
                    historyUpdate.setReceiveMsg(item.getMsg());
                }
                if (Objects.nonNull(historyUpdate.getEffectStatus()) && !historyUpdate.getEffectStatus().equals(PushResultEnum.SUCCESS.getCode())) {
                    historyUpdate.setEffectStatus(PriceUpdateResultEnum.FAILED.getResult());
                    historyUpdate.setEffectMsg(item.getMsg());
                }
                this.updateByExampleSelective(historyUpdate, updateFailHistoryExample);
            });
        }
    }

    @Override
    public int updateDetailByPushCodeAndBatch(String pushCode, Integer batch, PricePushHistory historyUpdate) {
        // 批量更新成功
        PricePushHistoryExample updateHistoryExample = new PricePushHistoryExample();
        PricePushHistoryExample.Criteria criteria = updateHistoryExample.createCriteria();
        criteria.andPushCodeEqualTo(pushCode)
            .andBatchEqualTo(batch);
        if (Objects.nonNull(historyUpdate.getReceiveStatus())) {
            // 更新线下POS接收状态，发送失败的一定接收失败，不需要更新接收状态
            criteria.andSendStatusNotEqualTo(PriceUpdateResultEnum.FAILED.getResult());
        }
        if (Objects.nonNull(historyUpdate.getEffectStatus())
            && Objects.equals(historyUpdate.getEffectStatus(), PriceUpdateResultEnum.SUCCESS.getResult())) {
            // 更新线下POS生效状态，预生效失败的一定不会执价成功，不需要更新生效状态
            criteria.andReceiveStatusNotEqualTo(PriceUpdateResultEnum.FAILED.getResult());
//            criteria.andEffectStatusEqualTo(PriceUpdateResultEnum.NO_RESULT.getResult());
        }
        return this.updateByExampleSelective(historyUpdate, updateHistoryExample);
    }

    @Override
    public PricePushHistoryStatisticsDTO selectPricePushHistoryStatistics(PricePushHistoryParam param) {
    	if (null==param || StringUtils.isBlank(param.getAdjustCode()) || null==param.getEffectStatus()) {
            throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR);
        }
    	//价格复制和调价单执价结果分别在不同的表里
    	boolean isPriceCopy = false;
    	if(param.getAdjustCode().startsWith(Constants.PRICE_COPU_INFO_PREFIX)) {
    		isPriceCopy = true;
    	}
    	AdjustPriceOrder order = null;
    	PriceCopyInfo copyInfo = null;
    	if(isPriceCopy) {
    		PriceCopyInfoExample example = new PriceCopyInfoExample();
    		example.createCriteria().andCopyNoEqualTo(param.getAdjustCode());
    		List<PriceCopyInfo> list = priceCopyInfoMapper.selectByExample(example);
    		if(CollectionUtils.isEmpty(list)) {
    			throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR);
    		}
    		copyInfo = list.get(0);
    	}else {
    		order = priceOrderV2Service.selectByAdjustCode(param.getAdjustCode());
        	if(null==order) {
        		throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR);
        	}
    	}
    	orgIdConvertBusinessId(param);
    	setPriceTypeCodeList(param);
    	PricePushHistoryStatisticsDTO statistics = null;
    	if(isPriceCopy) {
    		statistics = pricePushHistoryExtMapper.selectPricePushHistoryStatistics(param);
    	}else {
    		if(param.getEffectStatus()==2) {
        		param.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
        	}
    		statistics = adjustPriceOrderOrgStoreDetailExtMapper.selectPricePushHistoryStatistics(param);
    	}
    	if(null==statistics) {
    		statistics = new PricePushHistoryStatisticsDTO();
    	}
    	if(statistics.getFailCount()==0) {
    		return statistics;
    	}
    	boolean isAfreshAdjustPrice = false;//是否已重复执价
    	if(isPriceCopy) {
    		PriceCopyInfoExtend infoExtend = JSON.parseObject(copyInfo.getExtend(), PriceCopyInfoExtend.class);
    		if(null!=infoExtend && null!=infoExtend.getAfreshAdjustPriceStatusForResult() && infoExtend.getAfreshAdjustPriceStatusForResult()) {
    			isAfreshAdjustPrice = true;
    		}
    	}else {
    		Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(order.getExtend());
        	if(instance.isPresent()) {
        		AdjustPriceOrderExtend adjustPriceOrderExtend = instance.get();
        		if(null!=adjustPriceOrderExtend.getAfreshAdjustPriceStatusForResult() && adjustPriceOrderExtend.getAfreshAdjustPriceStatusForResult()) {
        			isAfreshAdjustPrice = true;
        		}
        	}
    	}
    	
    	if(isPriceCopy && !isAfreshAdjustPrice) {
    		//当调价单/复制单执价明细失败数量不为0时，且明细的错误信息为“商品编码不存在”时
    		int effectErrorCount = pricePushHistoryExtMapper.selectPricePushHistoryEffectError(param.getAdjustCode());
    		if(effectErrorCount>0) {
    			statistics.setShowAfreshAdjustPriceButton(true);
    		}
    	}
    	if(!isPriceCopy && !isAfreshAdjustPrice) {
    		//当调价单/复制单执价明细失败数量不为0时，且明细的错误信息为“商品编码不存在”时
    		int effectErrorCount = adjustPriceOrderOrgStoreDetailExtMapper.selectPricePushHistoryEffectError(param.getAdjustCode());
    		if(effectErrorCount>0) {
    			statistics.setShowAfreshAdjustPriceButton(true);
    		}
    	}
    	return statistics;
    }
    
    /**
     * 
     * @Title: orgIdConvertBusinessId   
     * @Description: 页面查询 根据orgId转换 连锁id和门店id 
     * @param: @param param      
     * @return: void      
     * @throws
     */
    private void orgIdConvertBusinessId(PricePushHistoryParam param) {
    	List<Long> orgIdList = Lists.newArrayList();
        if(null!=param.getBusinessId()) {
        	orgIdList.add(param.getBusinessId());
        }
        Map<Long, OrgLevelVO> orgLevelMap = null;
        if(CollectionUtils.isNotEmpty(orgIdList)) {
        	orgLevelMap = permissionExtService.listPermissionByOrgIds(orgIdList);
        }
        if(MapUtils.isNotEmpty(orgLevelMap)) {
        	if(null!=param.getBusinessId()) {
        		OrgLevelVO orgLevelVO = orgLevelMap.get(param.getBusinessId());
        		param.setBusinessId(orgLevelVO.getOutId());
            }
        }
    }

	@Override
	public PageResult<PricePushHistoryDTO> selectPricePushHistoryPage(PricePushHistoryParam param) {
		if (null==param || StringUtils.isBlank(param.getAdjustCode()) || null==param.getEffectStatus()) {
            throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR);
        }
        if (param.getPage() < 1) {
        	param.setPage(1);
        }
        if (param.getSize() < 1) {
        	param.setSize(10);
        }
        //价格复制和调价单执价结果分别在不同的表里
    	boolean isPriceCopy = false;
    	if(param.getAdjustCode().startsWith(Constants.PRICE_COPU_INFO_PREFIX)) {
    		isPriceCopy = true;
    	}
        orgIdConvertBusinessId(param);
        setPriceTypeCodeList(param);
		Page page = PageHelper.startPage(param.getPage(), param.getSize());
        List<PricePushHistory> hisList = null;
        if(isPriceCopy) {
        	hisList = pricePushHistoryExtMapper.selectPricePushHistoryPage(param);
        }else {
        	if(param.getEffectStatus()==2) {
        		param.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
        	}
        	hisList = adjustPriceOrderOrgStoreDetailExtMapper.selectPricePushHistoryPage(param);
        	if(CollectionUtils.isNotEmpty(hisList)) {
        		Optional<AdjustPriceOrderOrgStoreDetailExtend> instance = null;
        		AdjustPriceOrderOrgStoreDetailExtend detailExtend = null;
        		for (PricePushHistory pricePushHistory : hisList) {
        			instance = AdjustPriceOrderOrgStoreDetailExtend.getInstance(pricePushHistory.getExtend());
        			if(instance.isPresent()) {
        				detailExtend = instance.get();
        				pricePushHistory.setPushCode(detailExtend.getPushCode());
        				pricePushHistory.setSendStatus(detailExtend.getSendStatus());
            			pricePushHistory.setSendMsg(detailExtend.getSendMsg());
            			pricePushHistory.setReceiveStatus(detailExtend.getReceiveStatus());
            			pricePushHistory.setReceiveMsg(detailExtend.getReceiveMsg());
            			pricePushHistory.setEffectStatus(detailExtend.getEffectStatus());
            			pricePushHistory.setEffectMsg(detailExtend.getEffectMsg());
        			}
				}
        	}
        }
        
        List<PricePushHistoryDTO> hisDtoList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(hisList)) {
        	List<OptionDto> allPriceChannelList = priceManageControlOrderReadService.getAllPriceChannelList();
        	Map<String, OptionDto> allPriceChannelMap = Maps.newHashMap();
        	if(CollectionUtils.isNotEmpty(allPriceChannelList)) {
        		allPriceChannelMap = allPriceChannelList.stream().collect(Collectors.toMap(k -> k.getValue(), part -> part ,(v1,v2)->v2));
        	}
        	List<String> goodsNoList = hisList.stream().distinct().map(PricePushHistory::getGoodsNo).collect(Collectors.toList());
        	//取商品名称和商品描述时，从ES查询
            List<ForestGoodsDTO> resultGoodsList = searchFeignFacadeService.getForestGoodsDTOList(goodsNoList);
            Map<String, ForestGoodsDTO> goodsMap = Maps.newHashMap();
            // 是否为空集合
            if(CollectionUtils.isNotEmpty(resultGoodsList)){
                goodsMap = resultGoodsList.stream().collect(Collectors.toMap(ForestGoodsDTO::getGoodsNo, Function.identity(), (v1, v2) -> v1));
            }
        	PricePushHistoryDTO hisDto = null;
        	StringBuffer errorMsg = null;
        	for (PricePushHistory item : hisList) {
        		errorMsg = new StringBuffer();
        		hisDto = new PricePushHistoryDTO();
        		BeanUtils.copyProperties(item, hisDto);
        		if(null!=item.getPrice()) {
        			hisDto.setPrice(PriceUtil.getYuanFromFen(item.getPrice()));
        		}
        		ForestGoodsDTO goodsDTO = goodsMap.get(hisDto.getGoodsNo());
        		hisDto.setBusinessName(CacheVar.getBusinessName(hisDto.getBusinessId()));
        		if(StringUtils.isBlank(hisDto.getStoreName())) {
        			hisDto.setStoreName(CacheVar.getStoreName(hisDto.getStoreId()));
        		}
        		hisDto.setMdmStore(CacheVar.getSapCode(hisDto.getStoreId()));
        		hisDto.setGoodsName(null==goodsDTO?"":goodsDTO.getName());
        		hisDto.setSpec(null==goodsDTO?"":goodsDTO.getJhiSpecification());
        		hisDto.setManufacturer(null==goodsDTO?"":goodsDTO.getFactoryid());
        		hisDto.setUnit(null==goodsDTO?"":goodsDTO.getGoodsunit());
        		hisDto.setChannelName(allPriceChannelMap.get(String.valueOf(hisDto.getChannelId())).getLabel());
        		if(isPriceCopy) {
        			hisDto.setSendStatusName(PriceUpdateResultEnum.getDesc(hisDto.getSendStatus()));
            		hisDto.setReceiveStatusName(PriceUpdateResultEnum.getDesc(hisDto.getReceiveStatus()));
            		hisDto.setEffectStatusName(PriceUpdateResultEnum.getDesc(hisDto.getEffectStatus()));
            		if(null!=hisDto.getSendStatus() && hisDto.getSendStatus()==PriceUpdateResultEnum.FAILED.getResult().intValue()) {
            			errorMsg.append(hisDto.getSendMsg());
            		}else if(null!=hisDto.getReceiveStatus() && hisDto.getReceiveStatus()==PriceUpdateResultEnum.FAILED.getResult().intValue()) {
    					errorMsg.append(hisDto.getReceiveMsg());
    				}else if(null!=hisDto.getEffectStatus() && hisDto.getEffectStatus()==PriceUpdateResultEnum.FAILED.getResult().intValue()) {
    					errorMsg.append(hisDto.getEffectMsg());
    				}
        		}else {
        			hisDto.setSendStatusName(DealStepStatusEnum.getName(hisDto.getSendStatus()));
            		hisDto.setReceiveStatusName(DealStepStatusEnum.getName(hisDto.getReceiveStatus()));
            		hisDto.setEffectStatusName(DealStepStatusEnum.getName(hisDto.getEffectStatus()));
            		if(null!=hisDto.getSendStatus() && hisDto.getSendStatus()==DealStepStatusEnum.EXE_FAIL.getCode()) {
            			errorMsg.append(hisDto.getSendMsg());
            		}else if(null!=hisDto.getReceiveStatus() && hisDto.getReceiveStatus()==DealStepStatusEnum.EXE_FAIL.getCode()) {
    					errorMsg.append(hisDto.getReceiveMsg());
    				}else if(null!=hisDto.getEffectStatus() && hisDto.getEffectStatus()==DealStepStatusEnum.EXE_FAIL.getCode()) {
    					errorMsg.append(hisDto.getEffectMsg());
    				}
        		}
        		hisDto.setErrorMessage(errorMsg.toString());
        		if(StringUtils.isBlank(hisDto.getPriceTypeName())) {
        			Optional<PriceTypeModeEnum> ptmEnum = PriceTypeModeEnum.getByPriceCode(hisDto.getPriceTypeCode());
        			if(ptmEnum.isPresent()) {
        				PriceTypeModeEnum priceTypeModeEnum = ptmEnum.get();
        				hisDto.setPriceTypeName(priceTypeModeEnum.getPriceTypeName());
        			}
        		}
        		hisDtoList.add(hisDto);
			}
        }
        return new PageResult<>(page.getTotal(), hisDtoList);
	}
	
	/**
	 * 
	 * @Title: setPriceTypeCodeList   
	 * @Description: 查询价格类型   
	 * @param: @param param      
	 * @return: void      
	 * @throws
	 */
	private void setPriceTypeCodeList(PricePushHistoryParam param) {
		ControlOrderStatusParam priceTypeCodeStatus = new ControlOrderStatusParam();
		priceTypeCodeStatus.setApiCode(PriceManageStatusApiEnum.PRICE_TYPE.getCode());
		priceTypeCodeStatus.setUserId(param.getUserId());
        List<OptionDto> optionDtoList = priceManageControlOrderReadService.controlSelectUnifyList(priceTypeCodeStatus);
        if(CollectionUtils.isNotEmpty(optionDtoList)) {
    		List<String> priceTypeCodeList = optionDtoList.stream().map(OptionDto::getValue).collect(Collectors.toList());
    		param.setPriceTypeCodeList(priceTypeCodeList);
        }
	}

    @Override
    public int updateSendStatus(String pushCode, Integer batch, PushResultEnum resultEnum) {

        PricePushHistory historyUpdate = new PricePushHistory();
        historyUpdate.setSendStatus(resultEnum.getCode());

        PricePushHistoryExample updateFailHistoryExample = new PricePushHistoryExample();
        PricePushHistoryExample.Criteria updateHistoryExampleCriteria = updateFailHistoryExample.createCriteria();
        updateHistoryExampleCriteria.andPushCodeEqualTo(pushCode);
        updateHistoryExampleCriteria.andBatchEqualTo(batch);

        return this.updateByExampleSelective(historyUpdate, updateFailHistoryExample);
    }

	@Override
	public void afreshEffectAdjustPrice(String adjustCode) {
		List<PricePushHistory> hisList = pricePushHistoryExtMapper.selectPricePushHistoryEffectErrorData(adjustCode);
		if(CollectionUtils.isEmpty(hisList)) {
			return;
		}
		PricePushVo pushVo = null;
		for (PricePushHistory pricePushHistory : hisList) {
			// 入mq，做推送
	        pushVo = new PricePushVo();
	        pushVo.setPushCode(pricePushHistory.getPushCode());
	        pushVo.setBusinessId(String.valueOf(pricePushHistory.getBusinessId()));
	        pricePushProducer.sendMq(pushVo);
		}
		if(adjustCode.startsWith(Constants.PRICE_COPU_INFO_PREFIX)) {
			setPriceCopyInfoExtend(adjustCode);
		}else {
			setAdjustPriceOrderExtend(adjustCode);
		}
	}
	
	private void setAdjustPriceOrderExtend(String adjustCode) {
		//更改调价单主表 执价结果页面是否已重复执价操作
		AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
		AdjustPriceOrder priceOrder = new AdjustPriceOrder();
		Optional<AdjustPriceOrderExtend> orderExtend = AdjustPriceOrderExtend.getInstance(adjustPriceOrder.getExtend());
		AdjustPriceOrderExtend adjustPriceOrderExtend = null;
		if(orderExtend.isPresent()) {
			adjustPriceOrderExtend = orderExtend.get();
			adjustPriceOrderExtend.setAfreshAdjustPriceStatusForResult(true);
		}else {
			adjustPriceOrderExtend = new AdjustPriceOrderExtend();
			adjustPriceOrderExtend.setAfreshAdjustPriceStatusForResult(true);
		}
		priceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(adjustPriceOrderExtend));
		priceOrder.setId(adjustPriceOrder.getId());
		adjustPriceOrderMapper.updateByPrimaryKeySelective(priceOrder);
	}
	
	private void setPriceCopyInfoExtend(String adjustCode) {
		//更改PriceCopyInfo表 执价结果页面是否已重复执价操作
		PriceCopyInfoExample example = new PriceCopyInfoExample();
		example.createCriteria().andCopyNoEqualTo(adjustCode);
		List<PriceCopyInfo> list = priceCopyInfoMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(list)) {
			return;
		}
		PriceCopyInfo priceCopyInfo = list.get(0);
		PriceCopyInfoExtend infoExtend = JSON.parseObject(priceCopyInfo.getExtend(), PriceCopyInfoExtend.class);
		if(null==infoExtend) {
			infoExtend = new PriceCopyInfoExtend();
		}
		infoExtend.setAfreshAdjustPriceStatusForResult(true);
		PriceCopyInfo info = new PriceCopyInfo();
		info.setId(priceCopyInfo.getId());
		info.setExtend(JSON.toJSONString(infoExtend));
		priceCopyInfoMapper.updateByPrimaryKeySelective(info);
	}
}


