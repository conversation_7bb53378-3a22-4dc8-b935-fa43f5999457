package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.cache.IDataCacheManager;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.PriceGoodsList;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.PriceGoodsListMapper;
import com.cowell.pricecenter.mq.producer.ExternalPriceExecuteProducer;
import com.cowell.pricecenter.service.IPriceService;
import com.cowell.pricecenter.service.PriceSyncBaseService;
import com.cowell.pricecenter.service.SapPushPriceDataService;
import com.cowell.pricecenter.service.dto.ItemSkuQueryApiDTO;
import com.cowell.pricecenter.service.dto.MdmBusinessBaseDTO;
import com.cowell.pricecenter.service.dto.MdmStoreBaseDTO;
import com.cowell.pricecenter.service.dto.SapPushPriceDataDTO;
import com.cowell.pricecenter.service.dto.request.EntityMarkParam;
import com.cowell.pricecenter.service.dto.request.TagMarkParam;
import com.cowell.pricecenter.service.dto.request.TagMarkParentParam;
import com.cowell.pricecenter.service.dto.response.CommonRes;
import com.cowell.pricecenter.service.dto.response.ItemSkuVo;
import com.cowell.pricecenter.service.feign.ItemCenterCpSyncService;
import com.cowell.pricecenter.service.feign.ItemCenterCpservice;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.service.feign.TagFeignService;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/16 1:47 PM
 */
@Service
public class SapPushPriceDataServiceImpl implements SapPushPriceDataService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SapPushPriceDataServiceImpl.class);

    /**
     * 默认处理价格数据大小
     * 100条
     */
    private static final Integer DEFAULT_PROCESS_PRICE_DATA_SIZE = 500;

    private static final String ERPSAAS_REQUEST_REQUEST_BODY_FIELD = "requestBody";

    private static final String ERPSAAS_REQUEST_TABLE_FIELD_TABLE = "Table";

    private static final String ERPSAAS_REQUEST_BDATA_FIELD_BDATA = "bdata";

    /**
     * 缓存sku数据
     * key=businessId+storeId+channelStoreId+channelType+goodsNo
     */
    private static final String OHC_SKU_LOCAL_CACHE_KEY = "OHC_SKU_LOCAL_CACHE_KEY_%s_%s_%s_%s_%s";

    public static final Map<String, MdmBusinessBaseDTO> MDM_BUSINESS_DATA_MAP = Maps.newHashMap();

    public static final Map<String, MdmStoreBaseDTO> MDM_STORE_DATA_MAP = Maps.newHashMap();

    public static final Map<String, PriceType> PRICE_TYPE_MAP = Maps.newHashMap();

    @ApolloJsonValue("${WarehouseChannelStoreMapping:{\"3680\":\"3681\"}}")
    private JSONObject warehouseChannelStoreMapping;

    /**
     * b2b同步sap价格是否到门店
     */
    @Value("${b2b.price.sync.store:false}")
    private Boolean syncPriceToStore;

    /**
     * B2B加盟商城锁价品不促销标签id
     */
    @Value("${B2bJoinMall.goods.tagId:}")
    private Long b2bJoinMallGoodsTagId;

    /**
     * 商城锁价品打标&去标连锁白名单
     */
    @Value("${B2bJoinMall.goods.comId:}")
    private String b2bJoinMallGoodsComId;

    /**
     * 同步sap目录价到数据库底表.(医保限价预警和制单限价使用)
     */
    @Value("${list.price.save.db:true}")
    private Boolean saveListPriceToDb;

    @Autowired
    @Qualifier("taskExecutorTrace3")
    private AsyncTaskExecutor asyncTaskExecutor3;

    @Autowired
    private ExternalPriceExecuteProducer executeProducer;

    @Autowired
    private IPriceService priceService;

    @Autowired
    private PriceSyncBaseService priceSyncBaseService;

    @Deprecated
    @Autowired
    private ItemCenterCpservice itemCenterCpservice;

    @Autowired
    private ItemCenterCpSyncService itemCenterCpSyncService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private TagFeignService tagFeignService;

    @Autowired
    private PriceGoodsListMapper priceGoodsListMapper;

    @Override
    public void processData(String messageBody) throws Exception {
        JSONObject json = JSONObject.parseObject(messageBody);
        String requestBody = json.getString(ERPSAAS_REQUEST_REQUEST_BODY_FIELD);
        JSONObject sapJson = JSONObject.parseObject(requestBody);
        Object table = sapJson.get(ERPSAAS_REQUEST_TABLE_FIELD_TABLE);
        List<SapPushPriceDataDTO> priceList = null;
        if (table instanceof List) {
            JSONArray array = sapJson.getJSONArray(ERPSAAS_REQUEST_TABLE_FIELD_TABLE);
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                String data = obj.getString(ERPSAAS_REQUEST_BDATA_FIELD_BDATA);
                priceList = JSONArray.parseArray(data, SapPushPriceDataDTO.class);
            }
        } else {
            JSONObject obj = sapJson.getJSONObject(ERPSAAS_REQUEST_TABLE_FIELD_TABLE);
            String data = obj.getString(ERPSAAS_REQUEST_BDATA_FIELD_BDATA);
            priceList = JSONArray.parseArray(data, SapPushPriceDataDTO.class);
        }

        if (CollectionUtils.isEmpty(priceList)) {
            LOGGER.info("SapPushPriceDataServiceImpl|processData|sap推送的目录价为空！");
            return;
        }

        if (priceList.size() > DEFAULT_PROCESS_PRICE_DATA_SIZE) {
            throw new IllegalArgumentException("SAP推送的目录价数据大小超过100条，拒绝处理！数量=" + priceList.size() + "条");
        }

        try {
            processWarehouseItem(priceList);
        } catch (Exception e) {
            LOGGER.error("SapPushPriceDataServiceImpl|处理大仓商品价格异常", e);
        }

        try {
            saveListPrice(priceList);
        } catch (Exception e) {
            LOGGER.error("SapPushPriceDataServiceImpl|处理大仓商品价格异常", e);
        }

        if (Objects.isNull(syncPriceToStore)) {
            syncPriceToStore = false;
        }

        if (!syncPriceToStore) {
            LOGGER.info("SapPushPriceDataServiceImpl|processData|SAP价格推送到门店开关配置不需要同步到门店|syncPriceToStore={}", syncPriceToStore);
            return;
        }

        for (SapPushPriceDataDTO priceData : priceList) {
            String comId = priceData.getComId();
            String storeNo = priceData.getStoreNo();
            String goodsNo = priceData.getGoodsNo();
            //仓对应的运营店的id
            Long wareHouseStoreId;
            Long businessId;
            //门店id
            Long storeId;
            LOGGER.info("SapPushPriceDataServiceImpl|processData|comId={},storeNo={},goodsNo={}", priceData.getComId(), priceData.getStoreNo(), priceData.getGoodsNo());
            if (StringUtils.isEmpty(storeNo)) {
                continue;
            }

            if (Objects.isNull(priceData.getPrice())) {
                LOGGER.info("SapPushPriceDataServiceImpl|processData|sap推送价格商品价格为空！storeNo={},goodsNo={}", storeNo, goodsNo);
                priceData.setPrice(new BigDecimal("0"));
            }

            //通过仓的门店编码获取运营店的编码
            String warehouseNoStr = warehouseChannelStoreMapping.getString(comId);
            String[] warehouseNoArray = warehouseNoStr.split(",\\s*");
            List<String> warehouseNoList = Arrays.asList(warehouseNoArray);
            if (!warehouseNoList.contains(storeNo)){
                continue;
            }

            //通过运营店的编码获取门店信息
            MdmStoreBaseDTO mdmStoreBaseDTO = MDM_STORE_DATA_MAP.get(storeNo);
            wareHouseStoreId = Objects.isNull(mdmStoreBaseDTO) ? null : mdmStoreBaseDTO.getStoreId();
            if (Objects.isNull(wareHouseStoreId)) {
                LOGGER.info("SapPushPriceDataServiceImpl|processData|查询大仓对应的运营店id为空！|warehouseNo={}", storeNo);
                continue;
            }
            priceData.setStoreId(wareHouseStoreId);
            if (Objects.isNull(MDM_STORE_DATA_MAP.get(storeNo))) {
                try {
                    ResponseEntity<MdmStoreBaseDTO> response = storeService.findByStoreNoInternal(storeNo);
                    if (Objects.isNull(response) || Objects.isNull(response.getBody())) {
                        throw new BusinessErrorException("通过store服务未查询到门店storeNo" + storeNo);
                    }
                    MDM_STORE_DATA_MAP.put(storeNo, response.getBody());
                } catch (Exception e) {
                    LOGGER.error("SapPushPriceDataServiceImpl|processData|缓存为查到门店，通过接口调用查询异常storeNo={}", storeNo, e);
                    throw new BusinessErrorException("查询store服务查询门店信息异常！storeNo=" + storeNo);
                }

            }
            MdmStoreBaseDTO store = MDM_STORE_DATA_MAP.get(storeNo);
            //加盟类型 01-直营店 02-直营加盟 03-直营加盟-松散式加盟
            //直营的不用同步目录价
            if (StringUtils.isNotEmpty(store.getJoinType()) && StringUtils.equals("01", store.getJoinType())) {
                continue;
            }

            storeId = Objects.nonNull(store) ? store.getStoreId() : null;
            if (Objects.isNull(storeId)) {
                LOGGER.warn("SapPushPriceDataServiceImpl|processData|履约店id为空！storeNo={}", storeNo);
                continue;
            }
            priceData.setChannelStoreId(storeId);
            MdmBusinessBaseDTO mdmBusinessBaseDTO = MDM_BUSINESS_DATA_MAP.get(comId);
            businessId = Objects.isNull(mdmBusinessBaseDTO) ? mdmStoreBaseDTO.getBusinessId() : mdmBusinessBaseDTO.getBusinessId();
            if (Objects.isNull(businessId)) {
                LOGGER.warn("SapPushPriceDataServiceImpl|processData|查询大仓对应连锁id为空！comId={}", comId);
                continue;
            }

            priceData.setBusinessId(businessId);
            priceData.setQuerySkuFlag(1);
            String priceStringData = JSONObject.toJSONString(priceData);
            String key = String.format("storeNo:%s_goodsNo:%s", priceData.getStoreNo(), priceData.getGoodsNo());
            executeProducer.sendMessage(priceStringData, key);
        }

    }

    private void saveListPrice(List<SapPushPriceDataDTO> priceList) throws Exception {
        if(saveListPriceToDb){
            asyncTaskExecutor3.execute(()->{
                Set<String> storeNos = priceList.stream().map(SapPushPriceDataDTO::getStoreNo).collect(Collectors.toSet());
                for (String storeNo : storeNos) {
                    if (!MDM_STORE_DATA_MAP.containsKey(storeNo)) {
                        try {
                            ResponseEntity<MdmStoreBaseDTO> response = storeService.findByStoreNoInternal(storeNo);
                            if (Objects.nonNull(response) && Objects.nonNull(response.getBody())) {
                                MDM_STORE_DATA_MAP.put(storeNo, response.getBody());
                            } else {
                                LOGGER.warn("SapPushPriceDataServiceImpl|saveListPrice|未查询到门店信息，storeNo={}", storeNo);
                            }
                        } catch (Exception e) {
                            LOGGER.error("SapPushPriceDataServiceImpl|saveListPrice|查询门店信息异常，storeNo={}", storeNo, e);
                        }
                    }
                }
                List<PriceGoodsList> priceGoodsLists = new ArrayList<>();
                for (SapPushPriceDataDTO priceData : priceList) {
                    MdmStoreBaseDTO storeInfo = MDM_STORE_DATA_MAP.get(priceData.getStoreNo());
                    if (Objects.isNull(storeInfo)||Objects.isNull(storeInfo.getStoreId())) {
                        LOGGER.warn("SapPushPriceDataServiceImpl|saveListPrice|门店信息缺失，跳过处理，storeNo={}", priceData.getStoreNo());
                        continue;
                    }
                    PriceGoodsList priceGoodsList = new PriceGoodsList();
                    priceGoodsList.setBusinessId(storeInfo.getBusinessId());
                    priceGoodsList.setStoreNo(priceData.getStoreNo());
                    priceGoodsList.setStoreId(storeInfo.getStoreId());
                    priceGoodsList.setGoodsCode(priceData.getGoodsNo());
                    priceGoodsList.setListPrice(priceData.getPrice());
                    priceGoodsList.setStatus(new Byte(StatusEnum.NORMAL.getCode() + ""));
                    priceGoodsList.setGmtCreate(new Date());
                    priceGoodsList.setGmtUpdate(new Date());
                    priceGoodsList.setCreatedBy("SAP");
                    priceGoodsList.setCreatedByName("SAP");
                    priceGoodsList.setUpdatedBy("SAP");
                    priceGoodsList.setUpdatedByName("SAP");
                    priceGoodsLists.add(priceGoodsList);
                }

                if (!CollectionUtils.isEmpty(priceGoodsLists)) {
                    Map<String, PriceGoodsList> priceGoodsListMap = priceGoodsLists.stream().collect(Collectors.toMap(p -> p.getStoreNo() + p.getGoodsCode(), p -> p, (p1, p2) -> p2));
                    priceGoodsLists = new ArrayList<>(priceGoodsListMap.values());

                    List<PriceGoodsList> existingPriceGoodsLists = priceGoodsListMapper.selectByStoreAndGoodsPairs(priceGoodsLists);
                    Map<String, PriceGoodsList> existingPriceGoodsListMap = existingPriceGoodsLists.stream().collect(Collectors.toMap(p -> p.getStoreNo() + p.getGoodsCode(), p -> p));

                    List<PriceGoodsList> toUpdate = new ArrayList<>();
                    List<PriceGoodsList> toInsert = new ArrayList<>();

                    for (PriceGoodsList priceGoodsList : priceGoodsLists) {
                        if (existingPriceGoodsListMap.containsKey(priceGoodsList.getStoreNo() + priceGoodsList.getGoodsCode())) {
                            toUpdate.add(priceGoodsList);
                        } else {
                            toInsert.add(priceGoodsList);
                        }
                    }

                    if (!CollectionUtils.isEmpty(toUpdate)) {
                        priceGoodsListMapper.updateBatch(toUpdate);
                        LOGGER.info("SapPushPriceDataServiceImpl|saveListPrice|批量更新PriceGoodsList成功，数量={}", toUpdate.size());
                    }
                    if (!CollectionUtils.isEmpty(toInsert)) {
                        priceGoodsListMapper.insertBatch(toInsert);
                        LOGGER.info("SapPushPriceDataServiceImpl|saveListPrice|批量插入PriceGoodsList成功，数量={}", toInsert.size());
                    }
                }
            });

        }
    }
    private void processWarehouseItem(List<SapPushPriceDataDTO> priceList) throws Exception {
        Map<String, List<SapPushPriceDataDTO>> collect = priceList.stream().collect(Collectors.groupingBy(SapPushPriceDataDTO::getGoodsNo));
        for (String key : collect.keySet()) {
            if (CollectionUtils.isEmpty(collect.get(key))) {
                continue;
            }
            for (SapPushPriceDataDTO priceDataDTO : collect.get(key)) {
                if (Objects.isNull(priceDataDTO)) {
                    continue;
                }
                SapPushPriceDataDTO wareHousePriceData = new SapPushPriceDataDTO();
                BeanUtils.copyProperties(priceDataDTO, wareHousePriceData);
                //通过仓的门店编码获取运营店的编码
                String warehouseNoStr = warehouseChannelStoreMapping.getString(priceDataDTO.getComId());
//                LOGGER.info("SapPushPriceDataServiceImpl|processWarehouseItem|大仓店|warehouseNoStr={}", warehouseNoStr);
                String[] warehouseNoArray = warehouseNoStr.split(",\\s*");
                List<String> warehouseNoList = Arrays.asList(warehouseNoArray);
                String warehouseNo = StringUtils.isEmpty(priceDataDTO.getStoreNo()) ? warehouseNoArray[0] : priceDataDTO.getStoreNo();
//                LOGGER.info("SapPushPriceDataServiceImpl|processWarehouseItem|大仓店|warehouseNo={}", warehouseNo);
                if (!warehouseNoList.contains(warehouseNo)){
                    continue;
                }
                //通过运营店的编码获取门店信息
                MdmStoreBaseDTO mdmStoreBaseDTO = MDM_STORE_DATA_MAP.get(warehouseNo);
                Long wareHouseStoreId = Objects.isNull(mdmStoreBaseDTO) ? null : mdmStoreBaseDTO.getStoreId();
                if (Objects.isNull(wareHouseStoreId)) {
                    LOGGER.info("SapPushPriceDataServiceImpl|processWarehouseItem|处理大仓的商品！|warehouseNo={}", warehouseNo);
                    continue;
                }
                if (Objects.isNull(MDM_BUSINESS_DATA_MAP.get(priceDataDTO.getComId()))){
                    wareHousePriceData.setBusinessId(mdmStoreBaseDTO.getBusinessId());
                }else {
                    wareHousePriceData.setBusinessId(MDM_BUSINESS_DATA_MAP.get(priceDataDTO.getComId()).getBusinessId());
                }

                wareHousePriceData.setStoreNo(warehouseNo);
                wareHousePriceData.setStoreId(wareHouseStoreId);
                wareHousePriceData.setChannelStoreId(wareHouseStoreId);
                wareHousePriceData.setSkuId(0L);
                wareHousePriceData.setSpuId(0L);
                //不需要查询sku
                wareHousePriceData.setQuerySkuFlag(0);
                String priceStringData = JSONObject.toJSONString(wareHousePriceData);
                String priceKey = String.format("storeNo:%s_goodsNo:%s", wareHousePriceData.getStoreNo(), wareHousePriceData.getGoodsNo());
//                LOGGER.info("SapPushPriceDataServiceImpl|processWarehouseItem|大仓价格数据|wareHousePriceData={}", wareHousePriceData);
                executeProducer.sendMessage(priceStringData, priceKey);
            }
        }
    }

    @Override
    public void saveStorePrice(String messageBody) {
        if (StringUtils.isEmpty(messageBody)) {
            LOGGER.info("SapPushPriceDataServiceImpl|saveStorePrice|sap推送价格数据为空！");
            return;
        }
        SapPushPriceDataDTO priceDataDTO = JSONObject.parseObject(messageBody, SapPushPriceDataDTO.class);
        PriceStoreDetail priceStoreDetail = buildPriceStoreDetail(priceDataDTO);
        int result = priceService.savePrice(priceStoreDetail);
        LOGGER.info("SapPushPriceDataServiceImpl|saveStorePrice|价格执行结果={},priceStoreDetail={}", result, JSONObject.toJSONString(priceStoreDetail));

    }

    /**
     * 门店商品打标/去标
     *
     * @param messageBody
     */
    @Retryable(value = {Exception.class}, backoff = @Backoff(delay = 2000, multiplier = 2))
    @Override
    public void saveStoreGoodsTagMark(String messageBody) {
        if (StringUtils.isEmpty(messageBody)) {
            LOGGER.info("SapPushPriceDataServiceImpl|saveStoreGoodsTagMark|sap推送价格数据为空！");
            return;
        }
        SapPushPriceDataDTO priceDataDTO = JSONObject.parseObject(messageBody, SapPushPriceDataDTO.class);
        String[] comIds = b2bJoinMallGoodsComId.split(",");
        if (StringUtils.isEmpty(priceDataDTO.getComId()) || !Arrays.asList(comIds).contains(priceDataDTO.getComId())){
            return;
        }
        //目录价
        BigDecimal price = priceDataDTO.getPrice();
        //价格上限（最高价）
        BigDecimal upperLimit = priceDataDTO.getUpperLimit();
        //价格下限（最低价）
        BigDecimal lowerLimit = priceDataDTO.getLowerLimit();
        //查询商品sku信息
        ItemSkuVo sku = getSkuByGoodsNo(priceDataDTO.getBusinessId(), priceDataDTO.getStoreId(),
            priceDataDTO.getChannelStoreId(), ItemChannelTypeEnum.PARTICIPATE_B2B.getType(),
            priceDataDTO.getGoodsNo(), priceDataDTO.getStoreNo());
        TagMarkParentParam markParentParam = new TagMarkParentParam();
        TagMarkParam tagMarkParam = new TagMarkParam();
        EntityMarkParam entityMarkParam = new EntityMarkParam();

        entityMarkParam.setEntityCode(priceDataDTO.getGoodsNo());
        entityMarkParam.setEntityTitle(sku.getItemName());
        tagMarkParam.setTagsId(Sets.newHashSet(b2bJoinMallGoodsTagId));
        tagMarkParam.setEntityMarkParams(Sets.newHashSet(entityMarkParam));

        markParentParam.setTagMarkParamList(Lists.newArrayList(tagMarkParam));
        markParentParam.setUserId(0L);
        markParentParam.setUserName("价格中台商品打标");
        markParentParam.setEntityType(EntityTypeEnum.GOODS.getType());
        markParentParam.setTagBizType(TagBizTypeEnum.B2B_JOIN_MALL.getCode());
        markParentParam.setNeedMarkBaseTag(YNEnum.NO.getType());
        if (price.compareTo(upperLimit) == 0 && price.compareTo(lowerLimit) == 0){
            //目录价=最高价=最低价 进行商品打标
            markParentParam.setMarkStatus(YNEnum.YES.getType());
        }else {
            //去标
            markParentParam.setMarkStatus(YNEnum.NO.getType());
        }
        try {
            ResponseEntity<CommonRes> result = tagFeignService.tagMark(markParentParam);
            LOGGER.info("SapPushPriceDataServiceImpl|saveStoreGoodsTagMark|执行结果={}",JSON.toJSONString(result));
        }catch (Exception e){
            LOGGER.error("SapPushPriceDataServiceImpl|saveStoreGoodsTagMark|执行异常",e);
            throw e;
        }
    }

    @PostConstruct
    public void initStoreData() {
        List<MdmBusinessBaseDTO> mdmBusinessBaseDTOList = storeService.findAllMdmBusinessBase();
        if (!CollectionUtils.isEmpty(mdmBusinessBaseDTOList)) {
            for (MdmBusinessBaseDTO mdmBusinessBaseDTO : mdmBusinessBaseDTOList) {
                MDM_BUSINESS_DATA_MAP.put(mdmBusinessBaseDTO.getComId(), mdmBusinessBaseDTO);
                ResponseEntity<List<MdmStoreBaseDTO>> store = storeService.findMdmStoreByComId(mdmBusinessBaseDTO.getComId(), false);
                if (!CollectionUtils.isEmpty(store.getBody())) {
                    store.getBody().stream().forEach(storeData -> {
                        MDM_STORE_DATA_MAP.put(storeData.getStoreNo(), storeData);
                    });
                }
            }
        }

        initPriceType();


    }

    /**
     * 初始化价格类型枚举字典数据
     */
    private void initPriceType() {
        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (MapUtils.isEmpty(priceTypeMap)) {
            LOGGER.info("初始化价格类型为空！");
            return;
        }
        priceTypeMap.forEach((key, value) -> {
            PRICE_TYPE_MAP.put(key, value);
        });
    }

    /**
     * 查询sku信息
     * @param storeId 门店id
     * @param channelStoreId 渠道门店id
     * @param channelType 渠道类型
     * @param goodsNo 商品编码
     * @param storeNo 门店编码
     * @return ItemSkuVo
     */
    private ItemSkuVo getSkuByGoodsNo(Long businessId, Long storeId, Long channelStoreId, Integer channelType,
                                      String goodsNo, String storeNo) {

        try {
            IDataCacheManager<String, JSONObject> cacheManager = new OHCDataCacheManagerImpl();
//        storeId+channelStoreId+channelType+goodsNo
            String key = String.format(OHC_SKU_LOCAL_CACHE_KEY, businessId, storeId, channelStoreId, channelType, goodsNo);
            ItemSkuVo sku = cacheManager.getCache(key, ItemSkuVo.class);
            if (Objects.nonNull(sku) && !Objects.equals(0L, sku.getItemSkuId())) {
                return sku;
            }

            //Sku = businessId+wareHouseStoreId+storeId+goodsNo 去商品中心那边进行查询获取到sku
            ItemSkuQueryApiDTO itemSkuQueryApiDTO = new ItemSkuQueryApiDTO();
            itemSkuQueryApiDTO.setChannelStoreId(channelStoreId);
            itemSkuQueryApiDTO.setStoreId(storeId);
            itemSkuQueryApiDTO.setChannelType(channelType);
            itemSkuQueryApiDTO.setSkuMerchantCode(goodsNo);
            itemSkuQueryApiDTO.setBusinessId(businessId);
            ResponseEntity<List<ItemSkuVo>> responseEntity = itemCenterCpSyncService.querySkuByParam(itemSkuQueryApiDTO);
            if (Objects.isNull(responseEntity) || CollectionUtils.isEmpty(responseEntity.getBody())) {
                LOGGER.error("SapPushPriceDataServiceImpl|getSkuByGoodsNo|未查询到sku,businessId={},storeId={},storeNo={},channelStoreId={},channelType={},goodsNo={}",
                    businessId, storeId, storeNo, channelStoreId, channelType, goodsNo);
                throw new BusinessErrorException("通过门店编码和商品编码查询sku为空=" + businessId + ",storeId=" +
                    storeId + ",channelStoreId=" + channelStoreId + ",goodsNo=" + goodsNo + ",storeNo=" + storeNo);
            }
            ItemSkuVo skuVo = responseEntity.getBody().get(0);
            //一天时间
            cacheManager.setCache(key, skuVo, 86400000L);
            return skuVo;
        } catch (Exception e) {
            LOGGER.error("SapPushPriceDataServiceImpl|getSkuByGoodsNo|查询sku异常businessId={}, storeId={},channelStoreId={},channelType={},goodsNo={}",
                businessId, storeId, channelStoreId, channelType, goodsNo, e);
            throw new BusinessErrorException("通过门店编码和商品编码查询sku异常businessId=" + businessId + "storeId" +
                storeId + "channelStoreId" + channelStoreId + "goodsNo" + goodsNo);
        }
    }

    private PriceStoreDetail buildPriceStoreDetail(SapPushPriceDataDTO priceData) {
        PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
        if (Objects.isNull(priceData.getPrice())) {
            priceStoreDetail.setPrice(new BigDecimal("0"));
        } else {
            BigDecimal price = priceData.getPrice().multiply(new BigDecimal("100").setScale(PriceConstant.PRICE_SCALE, BigDecimal.ROUND_FLOOR));
            priceStoreDetail.setPrice(price);
        }
        priceStoreDetail.setLevel((byte) OrgLevelEnum.BUSINESS.getCode());
        priceStoreDetail.setOrgName("");
        priceStoreDetail.setOrgId(0L);
        priceStoreDetail.setAuthOrgId(0L);
        priceStoreDetail.setAuthOrgName("0");
        priceStoreDetail.setAuthOrgLevel((byte) OrgLevelEnum.BUSINESS.getCode());
        priceStoreDetail.setOrgGoodsId(0L);

        priceStoreDetail.setAdjustCode("0");
        priceStoreDetail.setAdjustDetailId(11L);
        PriceType priceType = PRICE_TYPE_MAP.get(priceData.getPriceTypeCode());
        //必填项
        priceStoreDetail.setSpuId(Objects.isNull(priceData.getSpuId()) ? 0L : priceData.getSpuId());
        priceStoreDetail.setSkuId(Objects.isNull(priceData.getSkuId()) ? 0L : priceData.getSkuId());
        priceStoreDetail.setStoreId(priceData.getStoreId());
        priceStoreDetail.setChannelStoreId(priceData.getChannelStoreId());
        priceStoreDetail.setGoodsNo(priceData.getGoodsNo());
        priceStoreDetail.setPriceTypeCode(Objects.nonNull(priceType) ? priceType.getCode() : PTypeEnum.MLJ.getCode());
        priceStoreDetail.setPriceTypeId(Objects.nonNull(priceType) ? priceType.getId() : null);
        priceStoreDetail.setPriceTypeName(Objects.nonNull(priceType) ? priceType.getName() : null);
        priceStoreDetail.setChannelId(priceData.getChannelId());
        priceStoreDetail.setBusinessId(priceData.getBusinessId());
        priceStoreDetail.setSyncDate(System.currentTimeMillis());
        priceStoreDetail.setUpdatedBy(0L);
        priceStoreDetail.setUpdatedByName("SAP-PUSH-MLJ");
        priceStoreDetail.setGmtUpdate(Objects.isNull(priceData.getGmtCreate()) ? new Date() : priceData.getGmtCreate());
        priceStoreDetail.setGmtUpdate(Objects.isNull(priceData.getGmtCreate()) ? new Date() : priceData.getGmtCreate());
        priceStoreDetail.setSyncDate(Objects.isNull(priceData.getGmtCreate()) ? System.currentTimeMillis() : priceData.getGmtCreate().getTime());
        priceStoreDetail.setPriceGroup(priceData.getPriceGroup());
        Map<String, Object> extend = new HashMap<>();
        //价格上限
        BigDecimal upperLimit = priceData.getUpperLimit();
        if (Objects.nonNull(upperLimit)) {
            extend.put("upperLimit", upperLimit.multiply(new BigDecimal("100").setScale(PriceConstant.PRICE_SCALE, BigDecimal.ROUND_FLOOR)));
        }
        //价格下限
        BigDecimal lowerLimit = priceData.getLowerLimit();
        if (Objects.nonNull(lowerLimit)) {
            extend.put("lowerLimit", lowerLimit.multiply(new BigDecimal("100").setScale(PriceConstant.PRICE_SCALE, BigDecimal.ROUND_FLOOR)));
        }
        priceStoreDetail.setExtend(JSON.toJSONString(extend));
        //sap 0：作废  1：有效；
        if (StringUtils.isNotEmpty(priceData.getStatus())) {
            if (StringUtils.equals("0", priceData.getStatus())) {
                priceStoreDetail.setStatus(new Byte(StatusEnum.DISCARD.getCode() + ""));
            } else if (StringUtils.equals("1", priceData.getStatus())) {
                priceStoreDetail.setStatus(new Byte(StatusEnum.NORMAL.getCode() + ""));
            }
        } else {
            priceStoreDetail.setStatus(new Byte(StatusEnum.STATUS_IS_NULL.getCode() + ""));
        }

        if (priceData.getQuerySkuFlag() == 0) {
            return priceStoreDetail;
        }

        ItemSkuVo sku = getSkuByGoodsNo(priceData.getBusinessId(), priceData.getStoreId(),
            priceData.getChannelStoreId(), ItemChannelTypeEnum.PARTICIPATE_B2B.getType(),
            priceData.getGoodsNo(), priceData.getStoreNo());
        if (Objects.nonNull(sku) && !Objects.equals(0L, sku.getItemSkuId())) {
            priceData.setSkuId(sku.getItemSkuId());
            priceData.setSpuId(sku.getSpuId());
            priceStoreDetail.setSpuId(Objects.isNull(priceData.getSpuId()) ? 0L : priceData.getSpuId());
            priceStoreDetail.setSkuId(priceData.getSkuId());
        }
        return priceStoreDetail;

    }
}
