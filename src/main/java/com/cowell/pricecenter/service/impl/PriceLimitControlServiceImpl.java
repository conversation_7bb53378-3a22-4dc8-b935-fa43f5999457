package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.OrgTypeEnum;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.enums.StatusEnum;
import com.cowell.pricecenter.mapper.PriceGoodsListMapper;
import com.cowell.pricecenter.mapper.PriceLimitControlConfigMapper;
import com.cowell.pricecenter.mapper.PriceLimitControlGoodsConfigMapper;
import com.cowell.pricecenter.mapper.PriceLimitControlOrgExpandMapper;
import com.cowell.pricecenter.mapper.extension.PriceLimitControlConfigExtMapper;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.security.SecurityUtils;
import com.cowell.pricecenter.service.IPermissionExtService;
import com.cowell.pricecenter.service.IPriceLimitControlService;
import com.cowell.pricecenter.service.ISearchExtService;
import com.cowell.pricecenter.service.dto.PriceLimitControlStoreCache;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.CommonRes;
import com.cowell.pricecenter.service.dto.response.OrgStoreBusinessDTO;
import com.cowell.pricecenter.service.dto.response.PriceLimitControlDetailDTO;
import com.cowell.pricecenter.service.dto.response.amis.CommonResult;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.limitControl.PriceLimitControlConfigDTO;
import com.cowell.pricecenter.service.feign.vo.SpuListVO;
import com.cowell.pricecenter.web.rest.errors.BusinessException;
import com.cowell.pricecenter.web.rest.util.Assert;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RBuckets;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class PriceLimitControlServiceImpl implements IPriceLimitControlService {

    private final Logger log = LoggerFactory.getLogger(PriceLimitControlServiceImpl.class);

    @Autowired
    private IPermissionExtService permissionExtService;
    @Autowired
    private PriceLimitControlConfigMapper priceLimitControlConfigMapper;
    @Autowired
    private PriceLimitControlGoodsConfigMapper priceLimitControlGoodsConfigMapper;
    @Autowired
    private PriceLimitControlOrgExpandMapper priceLimitControlOrgExpandMapper;
    @Autowired
    private PriceLimitControlConfigExtMapper priceLimitControlConfigExtMapper;
    @Autowired
    private ISearchExtService searchExtService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private PriceGoodsListMapper priceGoodsListMapper;

    private static final Integer LIMIT_TYPE_MEDICAL_INSURANCE = 1;
    private static final byte STATUS_ACTIVE = 0;
    private static final byte STATUS_DISABLED = -1;


    @Override
    public CommonResult queryPriceLimitControlList(PriceLimitControlQueryParam param) {
        log.info("queryPriceLimitControlList - 开始执行，参数: {}", param);
        TokenUserDTO currentUserToken = permissionExtService.getCurrentUserToken();
        List<Long> userDataStoreOrgIdList = permissionExtService.retainUserDataScopeOrgIdListByType(currentUserToken.getUserId(), OrgTypeEnum.STORE.getCode(), param.getStoreOrgIds());
        if (CollectionUtils.isEmpty(userDataStoreOrgIdList)) {
            log.info("queryPriceLimitControlList - 用户门店数据范围为空，返回空结果");
            return CommonResult.ok(new PageResult<>(0L, new ArrayList<>()));
        }
        List<Long> userDataBusinessOrgIdList = permissionExtService.retainUserDataScopeOrgIdListByType(currentUserToken.getUserId(), OrgTypeEnum.BUSINESS.getCode(), param.getBusinessOrgIds());
        if (CollectionUtils.isEmpty(userDataBusinessOrgIdList)) {
            log.info("queryPriceLimitControlList - 用户业务机构数据范围为空，返回空结果");
            return CommonResult.ok(new PageResult<>(0L, new ArrayList<>()));
        }
        try {
            List<Long> finalRuleIds = queryRuleIdsByConditions(param);
            if (CollectionUtils.isEmpty(finalRuleIds)) {
                log.info("queryPriceLimitControlList - 未找到匹配的规则ID，返回空结果");
                return CommonResult.ok(new PageResult<>(0L, new ArrayList<>()));
            }
            PriceLimitControlConfigExample example = new PriceLimitControlConfigExample();
            example.createCriteria().andIdIn(finalRuleIds);
            example.setOffset((long) (param.getPage() - 1) * param.getPageSize());
            example.setLimit(param.getPageSize());
            example.setOrderByClause("gmt_create desc");
            List<PriceLimitControlConfig> list = priceLimitControlConfigMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(list)) {
                List<PriceLimitControlConfigDTO> dtoList = list.stream()
                    .map(this::convertToPriceLimitControlConfigDTO)
                    .collect(Collectors.toList());
                return CommonResult.ok(new PageResult<>((long) finalRuleIds.size(), dtoList));
            }
            return CommonResult.ok(new PageResult<>(0L, new ArrayList<>()));
        } catch (Exception e) {
            log.error("queryPriceLimitControlList - 执行异常", e);
            return new CommonResult(ReturnCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    @Transactional
    public CommonResult saveOrUpdatePriceLimitControl(PriceLimitControlParam param) {
        log.info("saveOrUpdatePriceLimitControl - 开始执行，参数: {}", param);
        if (param.getOperateType() == 1) { // 保存机构
            return saveDraft(param);
        } else { // 提交并生效
            return submitAndActivateRule(param);
        }
    }

    @Override
    @Transactional
    public CommonRes batchImportGoods(Long ruleId, List<PriceLimitControlGoodsExcel> excelGoodsList) {
        log.info("batchImportGoods - 开始执行，规则ID: {}, 商品数量: {}", ruleId, excelGoodsList.size());
        Map<String, String> errorMap = new HashMap<>();
        PriceLimitControlGoodsExcelSaveDTO data = new PriceLimitControlGoodsExcelSaveDTO();
        data.setRuleId(ruleId);
        PriceLimitControlConfig config = priceLimitControlConfigMapper.selectByPrimaryKey(ruleId);
        if (config == null) {
            log.info("batchImportGoods - 规则ID {} 不存在", ruleId);
            return new CommonRes(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则ID " + ruleId + " 不存在");
        }
        List<Long> storeIds = getStoreIdsByRuelId(ruleId);
        if (CollectionUtils.isEmpty(storeIds)) {
            log.info("batchImportGoods - 规则 {} 未关联任何门店", ruleId);
            return new CommonRes(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则 " + ruleId + " 未关联任何门店");
        }

        // 1. 校验商品信息和内部重复
        Map<String, SpuListVO> spuMap = validateSpuAndInternalDuplicates(ruleId, excelGoodsList, errorMap);
        // 3. 构建并保存商品配置
        List<PriceLimitControlGoodsConfig> goodsToInsert = new ArrayList<>();
        for (PriceLimitControlGoodsExcel goodsInfo : excelGoodsList) {
            if(!errorMap.containsKey(goodsInfo.getGoodsNo())) {
                PriceLimitControlGoodsConfig goodsConfig = buildGoodsConfigFromExcel(ruleId, goodsInfo, spuMap.get(goodsInfo.getGoodsNo()));
                goodsConfig.setStatus((byte)StatusEnum.NORMAL.getCode());
                goodsConfig.setReason(errorMap.get(goodsInfo.getGoodsNo()));
                goodsToInsert.add(goodsConfig);
            }
        }
        if (CollectionUtils.isNotEmpty(goodsToInsert)) {
            String conflictReason = findConflictInCache(ruleId, storeIds, goodsToInsert);
            if (conflictReason != null) {
              log.info("batchImportGoods 存在冲突: {}", conflictReason);
                goodsToInsert.forEach(g -> {
                    if (StringUtils.isNotBlank(g.getReason())) {
                        errorMap.merge(g.getGoodsNo(), g.getReason(), (o, n) -> o + "; " + n);
                    }
                });
             }
            List<PriceLimitControlGoodsConfig> insertList = goodsToInsert.stream().filter(v -> StringUtils.isBlank(v.getReason())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(insertList)){
                priceLimitControlConfigExtMapper.batchInsertGoodsConfig(insertList);
            }
        }
        //更新商品数量
        updateRuleGoodsCount(ruleId);
        List<PriceLimitControlGoodsExcel> resultList = excelGoodsList.stream().peek(g -> g.setErrorMessage(errorMap.get(g.getGoodsNo()))).collect(Collectors.toList());
        data.setGoodsExcelList(resultList);
        log.info("batchImportGoods - 执行完成");
        return new CommonRes(0,"成功" ,data);
    }

    private List<Long> getStoreIdsByRuelId(Long ruleId) {
        PriceLimitControlOrgExpandExample example = new PriceLimitControlOrgExpandExample();
        example.createCriteria().andPriceLimitControlIdEqualTo(ruleId).andStatusEqualTo((byte) 0);
        return priceLimitControlOrgExpandMapper.selectByExample(example).stream().map(PriceLimitControlOrgExpand::getStoreId).collect(Collectors.toList());
    }
    @Override
    @Transactional
    public CommonResult updatePriceLimitControlStatus(PriceLimitControlQueryParam param) {
        log.info("updatePriceLimitControlStatus - 开始执行，参数: {}", param);
        PriceLimitControlConfig existConfig = priceLimitControlConfigMapper.selectByPrimaryKey(param.getId());
        if (existConfig == null) {
            log.info("updatePriceLimitControlStatus - 规则不存在，ID: {}", param.getId());
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则不存在");
        }
        if (param.getStatus() != null && param.getStatus() == STATUS_ACTIVE) {
            return enableRule(existConfig);
        } else if (param.getStatus() != null && param.getStatus() == STATUS_DISABLED) {
            return disableRule(existConfig);
        } else {
            log.info("updatePriceLimitControlStatus - 无效的状态操作，状态: {}", param.getStatus());
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "无效的状态操作");
        }
    }

    // =================================================================================================================
    // Private Helper Methods
    // =================================================================================================================

    /**
     * 保存草稿信息，主要处理机构变更
     */
    private CommonResult saveDraft(PriceLimitControlParam param) {
        log.info("saveDraft - 开始执行，参数: {}", param);
        if (CollectionUtils.isEmpty(param.getOrgIds())) {
            log.info("saveDraft - 机构不能为空");
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "机构不能为空");
        }

        List<PriceLimitControlOrgExpand> oldOrgList = priceLimitControlOrgExpandMapper.selectByExample(createOrgExpandExample(param.getId()));
        Set<Long> oldOrgSet = oldOrgList.stream().map(PriceLimitControlOrgExpand::getStoreOrgId).collect(Collectors.toSet());
        Set<Long> newOrgSet = new HashSet<>(param.getOrgIds());
        if (!oldOrgSet.equals(newOrgSet)) {
            priceLimitControlConfigExtMapper.deleteOrgExpandByRuleId(param.getId());
            priceLimitControlConfigExtMapper.deleteGoodsConfigByRuleId(param.getId());
            saveControlOrg(param);
            log.info("saveDraft - 旧机构和商品配置清理完成，并保存新机构");
        }
        updateMainConfig(param, priceLimitControlConfigMapper.selectByPrimaryKey(param.getId()));
        return new CommonResult(ReturnCodeEnum.SUCCESS);
    }

    /**
     * 提交并激活规则
     */
    @Transactional(rollbackFor = Exception.class)
    private CommonResult submitAndActivateRule(PriceLimitControlParam param) {
        log.info("submitAndActivateRule - 开始执行，参数: {}", param);
        Assert.isTrue(StringUtils.isNotBlank(param.getRuleName()),"规则名称不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getPriceTypes()),"价格类型不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getWarningPeriod()),"预警周期不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getWarningPeriodValue()),"预警周期值不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getInterventionMeasures()),"干预措施不能为空");
        Assert.isTrue((Objects.nonNull(param.getMarkupAmountLimit() )|| Objects.nonNull(param.getMarkupRatioLimit())),"上调金额上限和上调比例上限 不能都为空");
        // 1. 基础规则校验
        PriceLimitControlConfig existConfig = priceLimitControlConfigMapper.selectByPrimaryKey(param.getId());
        if (existConfig == null) {
            log.info("submitAndActivateRule - 规则不存在，ID: {}", param.getId());
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则不存在");
        }
        if (priceLimitControlConfigExtMapper.checkRuleNameUnique(param.getRuleName(), param.getId()) > 0) {
            log.info("submitAndActivateRule - 规则名称已存在，名称: {}", param.getRuleName());
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则名称已存在");
        }
        // 2. 获取所有无错误的商品
        List<PriceLimitControlGoodsConfig> allGoods = getConfigGoods(param.getId(),false);
        if (CollectionUtils.isEmpty(allGoods) || allGoods.stream().anyMatch(g -> StringUtils.isNotBlank(g.getReason())) ) {
            log.info("submitAndActivateRule - 导入商品有误或为空");
            return new CommonResult(ReturnCodeEnum.UPDATE_ERROR.getCode(), "商品不能为空,请先添加商品");
        }
        // 3. 最终冲突校验
        List<Long> storeIds = getStoreIdsByRuelId(param.getId());
        if (CollectionUtils.isEmpty(storeIds)) {
            log.info("submitAndActivateRule - 门店为空");
            return new CommonResult(ReturnCodeEnum.UPDATE_ERROR.getCode(), "限价机构不能为空，请先配置机构");
        }
        String conflictReason = findConflictInCache(existConfig.getId(), storeIds, allGoods);
        if (conflictReason != null) {
            // 数据库原子更新：先删后插
            priceLimitControlConfigExtMapper.deleteGoodsConfigByRuleId(existConfig.getId());
            priceLimitControlConfigExtMapper.batchInsertGoodsConfig(allGoods);
            log.info("enableRule 存在冲突: {}", conflictReason);
            return new CommonResult(ReturnCodeEnum.UPDATE_ERROR.getCode(), "商品有异常信息，请先编辑异常商品");
        }
        // 6. 更新商品数量
        updateRuleGoodsCount(param.getId());
        // 8. 添加新缓存 (基于新的机构配置)
        addNewCache(param, storeIds, allGoods);
        // 8. 更新主规则信息
        existConfig.setStatus((byte)StatusEnum.NORMAL.getCode());
        updateMainConfig(param, existConfig);
        log.info("submitAndActivateRule - 执行完成");
        return CommonResult.ok();
    }

    /**
     * 启用规则
     */
    private CommonResult enableRule(PriceLimitControlConfig existConfig) {
        log.info("enableRule -参数 existConfig: {}", JSON.toJSONString(existConfig));
        List<PriceLimitControlGoodsConfig> allGoods = getConfigGoods(existConfig.getId(),false);
        if (CollectionUtils.isEmpty(allGoods) || allGoods.stream().anyMatch(g -> StringUtils.isNotBlank(g.getReason())) ) {
            log.info("enableRule - 导入商品有误或为空");
            return new CommonResult(ReturnCodeEnum.UPDATE_ERROR.getCode(), "商品不能为空,请先添加商品");
        }
        List<Long> storeIds = getStoreIdsByRuelId(existConfig.getId());
        List<PriceLimitControlOrgExpand> oldOrgList = priceLimitControlOrgExpandMapper.selectByExample(createOrgExpandExample(existConfig.getId()));
        oldOrgList.forEach(org -> {
            storeIds.add(org.getStoreId());
        });
        if (CollectionUtils.isEmpty(storeIds)) {
            log.info("enableRule - 门店为空");
            return new CommonResult(ReturnCodeEnum.UPDATE_ERROR.getCode(), "限价机构不能为空，请先配置机构");
        }
        // 校验每个商品，标记冲突
        String conflictReason = findConflictInCache(existConfig.getId(), storeIds, allGoods);
        if (conflictReason != null) {
            // 数据库原子更新：先删后插
            priceLimitControlConfigExtMapper.deleteGoodsConfigByRuleId(existConfig.getId());
            priceLimitControlConfigExtMapper.batchInsertGoodsConfig(allGoods);
            log.info("enableRule 存在冲突: {}", conflictReason);
            return new CommonResult(ReturnCodeEnum.UPDATE_ERROR.getCode(), "商品有异常信息，请先编辑异常商品");
        }
        // 为激活的商品添加缓存
        PriceLimitControlParam cacheParam = new PriceLimitControlParam();
        BeanUtils.copyProperties(existConfig, cacheParam);
        addNewCache(cacheParam, storeIds, allGoods);
        // 更新主规则状态
        updateRuleStatus(existConfig, STATUS_ACTIVE);
        log.info("enableRule - 执行完成");
        return new CommonResult(ReturnCodeEnum.SUCCESS.getCode(), String.format("启用成功，激活了 %d 个商品。", allGoods.size()));
    }

    /**
     * 禁用规则
     */
    private CommonResult disableRule(PriceLimitControlConfig existConfig) {
        //清理旧缓存状态
        clearOldCacheState(existConfig);
        // 更新主规则状态
        updateRuleStatus(existConfig, STATUS_DISABLED);
        log.info("disableRule - 执行完成");
        return new CommonResult(ReturnCodeEnum.SUCCESS);
    }

    private void updateRuleStatus(PriceLimitControlConfig config, byte status) {
        log.info("updateRuleStatus - 更新规则状态，规则ID: {}, 状态: {}", config.getId(), status);
        PriceLimitControlConfig configToUpdate = new PriceLimitControlConfig();
        configToUpdate.setId(config.getId());
        configToUpdate.setStatus(status);
        configToUpdate.setGmtUpdate(new Date());
        configToUpdate.setUpdatedBy(SecurityUtils.getCurrentUserToken().getUserId());
        configToUpdate.setUpdatedByName(SecurityUtils.getCurrentUserToken().getName());
        configToUpdate.setVersion(config.getVersion() + 1);
        priceLimitControlConfigMapper.updateByPrimaryKeySelective(configToUpdate);
        log.info("updateRuleStatus - 规则状态更新完成");
    }

    /**
     * 校验SPU信息和导入列表内部的重复项
     */
    private Map<String, SpuListVO> validateSpuAndInternalDuplicates(Long ruleId, List<PriceLimitControlGoodsExcel> goodsList, Map<String, String> errorMap) {
        // 检查内部重复
        Set<String> uniqueGoodsNos = new HashSet<>();
        Set<String> duplicateGoodsNos = goodsList.stream()
            .map(PriceLimitControlGoodsExcel::getGoodsNo)
            .filter(n -> !uniqueGoodsNos.add(n))
            .collect(Collectors.toSet());
        duplicateGoodsNos.forEach(gn -> {
            errorMap.put(gn, "导入列表中存在重复的商品编码");
            log.info("validateSpuAndInternalDuplicates - 发现重复商品编码: {}", gn);
        });
        // 检查SPU是否存在
        List<String> goodsNos = goodsList.stream().map(PriceLimitControlGoodsExcel::getGoodsNo).distinct().collect(Collectors.toList());
        Map<String, SpuListVO> spuMap = searchExtService.getSpuVOMapAll(goodsNos);
        goodsList.forEach(g -> {
            if (!spuMap.containsKey(g.getGoodsNo())) {
                errorMap.merge(g.getGoodsNo(), "商品编码不存在或无效", (o, n) -> o + "; " + n);
            }
        });
        List<String> goodsNoList = goodsList.stream().map(PriceLimitControlGoodsExcel::getGoodsNo).filter(goodsNo -> !errorMap.containsKey(goodsNo)).collect(Collectors.toList());
        PriceLimitControlGoodsConfigExample example=new PriceLimitControlGoodsConfigExample();
        example.createCriteria().andPriceLimitControlIdEqualTo(ruleId).andGoodsNoIn(goodsNoList);
        List<PriceLimitControlGoodsConfig> priceLimitControlGoodsConfigs = priceLimitControlGoodsConfigMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(priceLimitControlGoodsConfigs)){
            priceLimitControlGoodsConfigs.forEach(g -> {
                errorMap.merge(g.getGoodsNo(), "商品已存在", (o, n) -> o + "; " + n);
            });
        }
        log.info("validateSpuAndInternalDuplicates - 校验完成，错误信息: {}", errorMap);
        return spuMap;
    }
    /**
     * 获取无错误（reason is null）的商品
     */
    private List<PriceLimitControlGoodsConfig> getConfigGoods(Long ruleId, Boolean filterError) {
        log.info("getConfigGoods - 获取商品配置，规则ID: {}, 过滤错误: {}", ruleId, filterError);
        PriceLimitControlGoodsConfigExample example =new PriceLimitControlGoodsConfigExample();
        example.createCriteria().andPriceLimitControlIdEqualTo(ruleId);
        List<PriceLimitControlGoodsConfig> stagedGoods = priceLimitControlGoodsConfigMapper.selectByExample(example);
        if (filterError) {
            return stagedGoods.stream()
                .filter(g -> StringUtils.isBlank(g.getReason()))
                .collect(Collectors.toList());
        }
        return stagedGoods;
    }

    /**
     * 在缓存中查找冲突，如果找到，返回错误信息字符串
     * 使用批量Redis查询优化性能，收集所有冲突商品信息
     */
    private String findConflictInCache(Long ruleId, List<Long> storeIds, List<PriceLimitControlGoodsConfig> goods) {
        if (CollectionUtils.isEmpty(storeIds) || CollectionUtils.isEmpty(goods)) {
            log.info("findConflictInCache - 门店或商品列表为空，无需校验");
            return null;
        }
        try {
            // 1. 构建所有需要查询的缓存key列表和映射关系
            List<String> allKeys = new ArrayList<>();
            Map<String, String> keyToGoodsNoMap = new HashMap<>(); // key -> goodsNo 的映射

            for (PriceLimitControlGoodsConfig good : goods) {
                for (Long storeId : storeIds) {
                    String key = RedisKeysConstant.getPriceLimitControlKey(storeId, good.getGoodsNo());
                    allKeys.add(key);
                    keyToGoodsNoMap.put(key, good.getGoodsNo());
                }
            }
            // 2. 收集所有冲突信息
            List<String> conflictMessages = new ArrayList<>();
            Map<String, Long> goodsConflictMap = new HashMap<>(); // goodsNo -> conflictRuleId

            // 3. 分批批量查询Redis缓存，每批200个key
            int batchSize = 200;
            Lists.partition(allKeys, batchSize).forEach(keyBatch -> {
                try {
                    String[] keyArray = keyBatch.toArray(new String[0]);
                    RBuckets buckets = redissonClient.getBuckets();
                    Map<String, String> cacheResults = buckets.get(keyArray);
                    // 处理查询结果，检查冲突
                    processConflictResults(ruleId, cacheResults, keyToGoodsNoMap, goodsConflictMap, goods);
                } catch (Exception e) {
                    log.error("批量查询Redis缓存失败，批次大小: {}", keyBatch.size(), e);
                }
            });

            // 4. 构建冲突错误信息
            if (!goodsConflictMap.isEmpty()) {
                for (Map.Entry<String, Long> entry : goodsConflictMap.entrySet()) {
                    String goodsNo = entry.getKey();
                    Long conflictRuleId = entry.getValue();
                    conflictMessages.add(String.format("商品 [%s] 与现有规则 [%s] 冲突", goodsNo, conflictRuleId));
                }
                String result = "提交失败：" + String.join(", ", conflictMessages);
                log.info("findConflictInCache - 发现 {} 个商品冲突", goodsConflictMap.size());
                return result;
            }
            return null;
        } catch (Exception e) {
            log.error("findConflictInCache 执行异常", e);
            return null;
        }
    }

    /**
     * 处理冲突查询结果，检查规则冲突并设置商品reason
     *
     * @param ruleId 当前规则ID
     * @param cacheResults 缓存查询结果
     * @param keyToGoodsNoMap key到商品编码的映射
     * @param goodsConflictMap 商品冲突映射
     * @param goods 商品配置列表
     */
    private void processConflictResults(Long ruleId, Map<String, String> cacheResults,
            Map<String, String> keyToGoodsNoMap, Map<String, Long> goodsConflictMap,
            List<PriceLimitControlGoodsConfig> goods) {

        if (MapUtils.isEmpty(cacheResults)) {
            return;
        }

        for (Map.Entry<String, String> entry : cacheResults.entrySet()) {
            String key = entry.getKey();
            String cacheValue = entry.getValue();
            if (StringUtils.isBlank(cacheValue)) {
                continue;
            }

            try {
                PriceLimitControlStoreCache cache = JSON.parseObject(cacheValue, PriceLimitControlStoreCache.class);
                if (cache != null && cache.getRuleId() != null && !cache.getRuleId().equals(ruleId)) {
                    String goodsNo = keyToGoodsNoMap.get(key);
                    if (StringUtils.isNotBlank(goodsNo) && !goodsConflictMap.containsKey(goodsNo)) {
                        goodsConflictMap.put(goodsNo, cache.getRuleId());
                        // 设置商品的reason字段
                        goods.stream()
                            .filter(good -> goodsNo.equals(good.getGoodsNo()))
                            .findFirst()
                            .ifPresent(good -> good.setReason(String.format("现有规则 [%s] 冲突", cache.getRuleId())));
                    }
                }
            } catch (Exception e) {
                log.warn("解析缓存数据失败，key: {}", key, e);
            }
        }
    }

    /**
     * 清理规则变更前的缓存状态
     */
    private void clearOldCacheState(PriceLimitControlConfig existConfig) {
        log.info("clearOldCacheState - 旧缓存状态清理参数={}",existConfig);
        List<PriceLimitControlGoodsConfig> previouslyActiveGoods = getConfigGoods(existConfig.getId(),true);
        if (!CollectionUtils.isEmpty(previouslyActiveGoods)) {
           List<Long> storeIds = getStoreIdsByRuelId(existConfig.getId());
            if (!CollectionUtils.isEmpty(storeIds)) {
                clearHistoricalCache(storeIds, previouslyActiveGoods);
            }
        }
        log.info("clearOldCacheState - 旧缓存状态清理完成");
    }


    /**
     * 保存机构信息
     */
    private void saveControlOrg(PriceLimitControlParam param) {
        log.info("saveControlOrg - 开始保存机构信息，规则ID: {}", param.getId());
        String orgIds = param.getOrgIds().stream().map(Object::toString).collect(Collectors.joining(","));
        List<OrgStoreBusinessDTO> orgStoreBusinessBatch = permissionExtService.getOrgStoreBusinessBatch(orgIds);
        Map<Long, OrgStoreBusinessDTO> map = orgStoreBusinessBatch.stream().filter(v->Objects.nonNull(v.getOutId())).collect(Collectors.toMap(OrgStoreBusinessDTO::getId, Function.identity(), (k1, k2) -> k1));
        List<PriceLimitControlOrgExpand> orgList = new ArrayList<>();
        for (Long orgId : param.getOrgIds()) {
            if (map.containsKey(orgId)){
                orgList.add(createOrgExpand(param.getId(), orgId, map.get(orgId)));
            }
        }
        if (!CollectionUtils.isEmpty(orgList)) {
            log.info("saveControlOrg - 批量插入机构配置，数量: {}", orgList.size());
            priceLimitControlConfigExtMapper.batchInsertOrgExpand(orgList);
        }
        log.info("saveControlOrg - 机构信息保存完成");
    }

    /**
     * 更新主配置信息
     */
    private void updateMainConfig(PriceLimitControlParam param, PriceLimitControlConfig existConfig) {
        log.info("updateMainConfig - 开始更新主配置信息，规则ID: {}", param.getId());
        BeanUtils.copyProperties(param, existConfig);
        existConfig.setGmtUpdate(new Date());
        existConfig.setStatus(existConfig.getStatus());
        existConfig.setUpdatedBy(SecurityUtils.getCurrentUserToken().getUserId());
        existConfig.setUpdatedByName(SecurityUtils.getCurrentUserToken().getName());
        existConfig.setVersion(existConfig.getVersion() + 1);
        existConfig.setOrgIds(param.getOrgIds().stream().map(String::valueOf).collect(Collectors.joining(",")));
        priceLimitControlConfigMapper.updateByPrimaryKeySelective(existConfig);
        //有设置为null的需求单独实现
        priceLimitControlConfigExtMapper.updateConfigLimitValue(existConfig);
        log.info("updateMainConfig - 主配置信息更新完成");
    }

    /**
     * 异步清除历史缓存
     */
    @Async
    public void clearHistoricalCache(List<Long> storeIds, List<PriceLimitControlGoodsConfig> goodsList) {
        log.info("clearHistoricalCache - 异步清除历史缓存，门店数量: {}, 商品数量: {}", storeIds.size(), goodsList.size());
        if (CollectionUtils.isEmpty(storeIds) || CollectionUtils.isEmpty(goodsList)) {
            log.info("clearHistoricalCache - 门店或商品列表为空，跳过清除");
            return;
        }
        RBatch batch = redissonClient.createBatch();
        for (Long storeId : storeIds) {
            for (PriceLimitControlGoodsConfig goods : goodsList) {
                batch.getBucket(RedisKeysConstant.getPriceLimitControlKey(storeId, goods.getGoodsNo())).deleteAsync();
            }
        }
        batch.execute();
        log.info("clearHistoricalCache - 历史缓存清除批处理执行完成");
    }

    /**
     * 异步添加新缓存
     */
    @Async
    public void addNewCache(PriceLimitControlParam param, List<Long> storeIds, List<PriceLimitControlGoodsConfig> goodsList) {
        log.info("addNewCache - 异步添加新缓存，门店数量: {}, 商品数量: {}", storeIds.size(), goodsList.size());
        if (CollectionUtils.isEmpty(storeIds) || CollectionUtils.isEmpty(goodsList)) {
            log.info("addNewCache - 门店或商品列表为空，跳过添加");
            return;
        }
        PriceGoodsListExample example = new PriceGoodsListExample();
        example.createCriteria().andStoreIdIn(storeIds).andGoodsCodeIn(goodsList.stream().map(PriceLimitControlGoodsConfig::getGoodsNo).collect(Collectors.toList()));
        List<PriceGoodsList> priceGoodsLists = priceGoodsListMapper.selectByExample(example);
        // 假设priceGoodsLists已经被正确初始化
        Map<String, BigDecimal> priceMap = priceGoodsLists.stream()
                .filter(item -> item.getStoreId() != null && item.getGoodsCode() != null)
                .collect(HashMap::new,(map, item) ->
                        map.put(item.getStoreId() + "_" + item.getGoodsCode(), item.getListPrice()), HashMap::putAll);

        RBatch batch = redissonClient.createBatch();
        for (Long storeId : storeIds) {
            for (PriceLimitControlGoodsConfig goods : goodsList) {
                PriceLimitControlStoreCache cache = createStoreCache(param, storeId, goods,priceMap);
               // log.info(" key={} addNewCache cache={}",RedisKeysConstant.getPriceLimitControlKey(storeId, goods.getGoodsNo()),JSON.toJSONString(cache));
                batch.getBucket(RedisKeysConstant.getPriceLimitControlKey(storeId, goods.getGoodsNo()))
                    .setAsync(JSON.toJSONString(cache));
            }
        }
        batch.execute();
        log.info("addNewCache - 新缓存添加批处理执行完成");
    }


    private PriceLimitControlStoreCache createStoreCache(PriceLimitControlParam param, Long storeId, PriceLimitControlGoodsConfig goods, Map<String, BigDecimal> listPriceMap) {
        PriceLimitControlStoreCache cache = new PriceLimitControlStoreCache();
        cache.setRuleId(param.getId());
        cache.setStoreId(storeId);
        cache.setGoodsNo(goods.getGoodsNo());
        cache.setInterventionMeasures(param.getInterventionMeasures());
        // 获取SAP目录价格
        BigDecimal sapListPrice = null;
        if (listPriceMap != null) {
            String priceKey = storeId + "_" + goods.getGoodsNo();
            if (listPriceMap.containsKey(priceKey)) {
                sapListPrice = listPriceMap.get(priceKey);
            }
        }

        String[] priceTypes = param.getPriceTypes().split(",");
        for (String priceType : priceTypes) {
            if ("LSJ".equals(priceType.trim())) {
                BigDecimal costPrice = calculateCostPrice(goods.getNationalTalkPrice(), goods.getOnlinePrice(), sapListPrice);
                BigDecimal limitPrice = calculateLimitPriceWithCost(costPrice, param.getMarkupRatioLimit(), param.getMarkupAmountLimit());
                cache.setLsjLimitPrice(limitPrice != null ? limitPrice.setScale(4, RoundingMode.HALF_UP) : null);
                //log.info(" createStoreCache - LSJ成本价: {}, 计算限价: {}", costPrice, cache.getLsjLimitPrice());
            } else if ("HYJ".equals(priceType.trim())) {
                BigDecimal costPrice = calculateCostPrice(goods.getNationalTalkPrice(), goods.getOnlinePrice(), sapListPrice);
                BigDecimal limitPrice = calculateLimitPriceWithCost(costPrice, param.getMarkupRatioLimit(), param.getMarkupAmountLimit());
                cache.setHyjLimitPrice(limitPrice != null ? limitPrice.setScale(4, RoundingMode.HALF_UP) : null);
                //log.info("createStoreCache - HYJ成本价: {}, 计算限价: {}", costPrice, cache.getHyjLimitPrice());
            }
        }
        log.info("createStoreCache - 门店缓存对象创建完成 cache={}",JSON.toJSONString(cache));
        return cache;
    }

    /**
     * 计算成本价格
     * 1、有国谈价格，直接取国谈价格
     * 2、没有国谈价格，取min（挂网价格/SAP目录价格）
     * 3、如果国谈价格、挂网价格、SAP目录价格都没有，则返回null
     */
    private BigDecimal calculateCostPrice(BigDecimal nationalTalkPrice, BigDecimal onlinePrice, BigDecimal sapListPrice) {
        log.info("calculateCostPrice - 计算成本价格，国谈价格: {}, 挂网价格: {}, SAP目录价格: {}",
                nationalTalkPrice, onlinePrice, sapListPrice);

        // 1、有国谈价格，直接取国谈价格
        if (nationalTalkPrice != null && nationalTalkPrice.compareTo(BigDecimal.ZERO) > 0) {
            return nationalTalkPrice;
        }

        // 2、没有国谈价格，取min（挂网价格/SAP目录价格）
        BigDecimal minPrice = null;
        if (onlinePrice != null && onlinePrice.compareTo(BigDecimal.ZERO) > 0) {
            minPrice = onlinePrice;
        }
        if (sapListPrice != null && sapListPrice.compareTo(BigDecimal.ZERO) > 0) {
            if (minPrice == null) {
                minPrice = sapListPrice;
            } else {
                minPrice = minPrice.min(sapListPrice);
            }
        }

        if (minPrice != null) {
            return minPrice;
        }
        // 3、如果国谈价格、挂网价格、SAP目录价格都没有，则返回null
        return null;
    }

    /**
     * 基于成本价计算限价
     * 计算预警值= min {成本价 ×（1+上调比例上限（%）），成本价+上调金额上限（元）}
     * 如果成本价为空，则设置预警值为最高值 9999999元
     */
    private BigDecimal calculateLimitPriceWithCost(BigDecimal costPrice, BigDecimal markupRatioLimit, BigDecimal markupAmountLimit) {
        log.info("calculateLimitPriceWithCost - 计算限价，成本价格: {}, 加价比例: {}, 加价金额: {}",
                costPrice, markupRatioLimit, markupAmountLimit);

        // 如果成本价为空，则设置预警值为最高值 9999999元
        if (costPrice == null || costPrice.compareTo(BigDecimal.ZERO) <= 0) {
            BigDecimal maxLimitPrice = new BigDecimal("9999999");
            log.info("calculateLimitPriceWithCost - 成本价为空或小于等于0，返回最高限价: {}", maxLimitPrice);
            return maxLimitPrice;
        }

        BigDecimal priceAfterRatio = null;
        BigDecimal priceAfterAmount = null;

        // 计算按比例加价后的价格：成本价 ×（1+上调比例上限（%））
        if (markupRatioLimit != null && markupRatioLimit.compareTo(BigDecimal.ZERO) >= 0) {
            priceAfterRatio = costPrice.multiply(BigDecimal.ONE.add(markupRatioLimit.divide(new BigDecimal("100"))));
        }

        // 计算按金额加价后的价格：成本价+上调金额上限（元）
        if (markupAmountLimit != null && markupAmountLimit.compareTo(BigDecimal.ZERO) >= 0) {
            priceAfterAmount = costPrice.add(markupAmountLimit);
        }

        // 取两者的最小值
        if (priceAfterRatio != null && priceAfterAmount != null) {
            BigDecimal result = priceAfterRatio.min(priceAfterAmount);
            return result;
        } else if (priceAfterRatio != null) {
            return priceAfterRatio;
        } else if (priceAfterAmount != null) {
            return priceAfterAmount;
        } else {
            // 如果比例和金额都为空，返回成本价
            return costPrice;
        }
    }

    private PriceLimitControlGoodsConfig buildGoodsConfigFromExcel(Long ruleId, PriceLimitControlGoodsExcel goodsExcel, SpuListVO spuInfo) {
        PriceLimitControlGoodsConfig config = new PriceLimitControlGoodsConfig();
        TokenUserDTO currentUser = SecurityUtils.getCurrentUserToken();
        Date now = new Date();

        config.setPriceLimitControlId(ruleId);
        config.setGoodsNo(goodsExcel.getGoodsNo());
        if (spuInfo != null) {
            config.setGoodsName(spuInfo.getName());
            config.setJhiSpecification(spuInfo.getJhiSpecification());
            config.setManufacturer(spuInfo.getFactoryid());
            config.setUnit(spuInfo.getGoodsunit());
        }
        config.setOnlinePrice(goodsExcel.getListingPrice());
        config.setNationalTalkPrice(goodsExcel.getNegotiatedPrice());
        config.setGmtCreate(now);
        config.setGmtUpdate(now);
        config.setCreatedBy(currentUser.getUserId());
        config.setCreatedByName(currentUser.getName());
        config.setUpdatedBy(currentUser.getUserId());
        config.setUpdatedByName(currentUser.getName());
        config.setVersion(1);
        return config;
    }

    private void updateRuleGoodsCount(Long ruleId) {
        long currentCount = priceLimitControlConfigExtMapper.countGoodsConfigByRuleIdAndKeyword(ruleId, (int) STATUS_ACTIVE, null);
        PriceLimitControlConfig updateConfig = new PriceLimitControlConfig();
        updateConfig.setId(ruleId);
        updateConfig.setGoodsCount(currentCount);
        priceLimitControlConfigMapper.updateByPrimaryKeySelective(updateConfig);
        log.info("updateRuleGoodsCount - 规则 {} 商品数量更新为: {}", ruleId, currentCount);
    }

    private List<Long> queryRuleIdsByConditions(PriceLimitControlQueryParam param) {
        log.info("queryRuleIdsByConditions - 开始查询规则ID，参数: {}", param);
        List<Long> ruleIds = priceLimitControlConfigExtMapper.selectRuleIdsByMainCondition(param);
        if (CollectionUtils.isEmpty(ruleIds)) {
            log.info("queryRuleIdsByConditions - 主条件查询未找到规则ID");
            return new ArrayList<>();
        }
        boolean hasStoreCondition = !CollectionUtils.isEmpty(param.getBusinessOrgIds()) || !CollectionUtils.isEmpty(param.getStoreOrgIds());
        if (!hasStoreCondition) {
            log.info("queryRuleIdsByConditions - 无门店条件，尝试获取用户数据范围");
            Long userId = SecurityUtils.getCurrentUserToken().getUserId();
            List<Long> userDataStoreOrgIdList = permissionExtService.retainUserDataScopeOrgIdListByType(userId, OrgTypeEnum.STORE.getCode(), null);
            if (userDataStoreOrgIdList.size() > Constants.MAX_STORE_COUNT_TRANSFORM_BUSINESS) {
                param.setBusinessOrgIds(permissionExtService.retainUserDataScopeOrgIdListByType(userId, OrgTypeEnum.BUSINESS.getCode(), null));
                param.setStoreOrgIds(null);
            } else {
                param.setBusinessOrgIds(null);
                param.setStoreOrgIds(userDataStoreOrgIdList);
            }
        }
        ruleIds = priceLimitControlConfigExtMapper.selectRuleIdsByStoreCondition(ruleIds, param.getStoreOrgIds(), param.getBusinessOrgIds());
        if (CollectionUtils.isEmpty(ruleIds)) {
            return new ArrayList<>();
        }
        if (StringUtils.isNotBlank(param.getGoodsNo())) {
            ruleIds = priceLimitControlConfigExtMapper.selectRuleIdsByGoodsCondition(ruleIds, param.getGoodsNo(), null);
            if (CollectionUtils.isEmpty(ruleIds)) {
                return new ArrayList<>();
            }
        }
        return ruleIds;
    }

    private PriceLimitControlOrgExpandExample createOrgExpandExample(Long ruleId) {
        log.info("createOrgExpandExample - 创建机构扩展示例，规则ID: {}", ruleId);
        PriceLimitControlOrgExpandExample example = new PriceLimitControlOrgExpandExample();
        example.createCriteria().andPriceLimitControlIdEqualTo(ruleId).andStatusEqualTo((byte) 0);
        log.info("createOrgExpandExample - 机构扩展示例创建完成");
        return example;
    }

    public Long getRuleId() {
        log.info("getRuleId - 获取下一个可用的规则ID");
        TokenUserDTO currentUser = SecurityUtils.getCurrentUserToken();
        if (Objects.isNull(currentUser) || Objects.isNull(currentUser.getUserId())) {
            throw new BusinessException(ReturnCodeEnum.INVALID_TOKEN);
        }
        try {
            //登录用户历史草稿单
            PriceLimitControlConfigExample example= new PriceLimitControlConfigExample();
            example.createCriteria().andCreatedByEqualTo(currentUser.getUserId()).andLimitTypeEqualTo(LIMIT_TYPE_MEDICAL_INSURANCE)
                    .andStatusEqualTo((byte) StatusEnum.STATUS_IS_NULL.getCode());
            List<PriceLimitControlConfig> priceLimitControlConfigs = priceLimitControlConfigMapper.selectByExample(example);
            if(!CollectionUtils.isEmpty(priceLimitControlConfigs)){
                return priceLimitControlConfigs.get(0).getId();
            }
            PriceLimitControlConfig record = new PriceLimitControlConfig();
            record.setLimitType(LIMIT_TYPE_MEDICAL_INSURANCE);
            record.setStatus((byte) StatusEnum.STATUS_IS_NULL.getCode());
            record.setRuleName("");
            record.setPriceTypes("");
            record.setOrgIds("");
            record.setCreatedBy(currentUser.getUserId());
            record.setCreatedByName(currentUser.getName());
            priceLimitControlConfigMapper.insertSelective(record);
            log.info("getRuleId - 插入新规则记录，ID: {}", record.getId());
            return record.getId();
        } catch (Exception e) {
            log.error("getRuleId - 获取规则ID异常", e);
            throw new BusinessException(ReturnCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public CommonResult queryGoodsList(PriceLimitControlQueryParam param) {
        log.info("queryGoodsList - 开始执行，参数: {}", param);
        if (param.getId() == null) {
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则ID不能为空");
        }
        try {
            long total = priceLimitControlConfigExtMapper.countGoodsConfigByRuleIdAndKeyword(param.getId(), null, param.getGoodsKeyword());
            if (total == 0) {
                return CommonResult.ok(new PageResult<>(0L, new ArrayList<>()));
            }
            List<PriceLimitControlGoodsConfig> list = priceLimitControlConfigExtMapper.selectGoodsConfigByRuleId(
                param.getId(),
                null,
                param.getGoodsKeyword(),
                (long) (param.getPage() - 1) * param.getPageSize(),
                param.getPageSize()
            );
            return CommonResult.ok(new PageResult<>(total, list));
        } catch (Exception e) {
            log.error("queryGoodsList - 执行异常", e);
            return new CommonResult(ReturnCodeEnum.SYSTEM_ERROR);
        }
    }

    private PriceLimitControlConfigDTO convertToPriceLimitControlConfigDTO(PriceLimitControlConfig config) {
        log.info("convertToPriceLimitControlConfigDTO - 转换规则配置DTO，规则ID: {}", config.getId());
        PriceLimitControlConfigDTO dto = new PriceLimitControlConfigDTO();
        BeanUtils.copyProperties(config, dto);
        dto.setStatusDesc(getStatusDesc(config.getStatus()));
        dto.setOrgIdsCount(StringUtils.isBlank(config.getOrgIds()) ? 0L : (long) config.getOrgIds().split(",").length);
        dto.setPriceTypesDesc(getPriceTypesDesc(config.getPriceTypes()));
        log.info("convertToPriceLimitControlConfigDTO - 转换完成");
        return dto;
    }

    private String getStatusDesc(Byte status) {
        if (status == null) return "草稿";
        switch (status) {
            case 0: return "启用";
            case -1: return "停用";
            default: return "草稿";
        }
    }

    private String getPriceTypesDesc(String priceTypes) {
        log.info("getPriceTypesDesc - 获取价格类型描述，价格类型: {}", priceTypes);
        if (StringUtils.isBlank(priceTypes)) return "";
        return Arrays.stream(priceTypes.split(","))
            .map(String::trim)
            .map(PTypeEnum::getDescByCode)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.joining("、"));
    }

    private PriceLimitControlOrgExpand createOrgExpand(Long ruleId, Long orgId, OrgStoreBusinessDTO orgStoreBusinessDTO) {
        log.info("createOrgExpand - 创建机构扩展对象，规则ID: {}, 机构ID: {}", ruleId, orgId);
        PriceLimitControlOrgExpand orgExpand = new PriceLimitControlOrgExpand();
        orgExpand.setPriceLimitControlId(ruleId);
        orgExpand.setStoreOrgId(orgId);
        if (orgStoreBusinessDTO != null && orgStoreBusinessDTO.getType().equals(OrgTypeEnum.STORE.getCode())) {
            orgExpand.setStoreId(orgStoreBusinessDTO.getOutId());
            orgExpand.setStoreName(orgStoreBusinessDTO.getShortName());
            if (orgStoreBusinessDTO.getBusiness() != null) {
                orgExpand.setBusinessId(orgStoreBusinessDTO.getBusiness().getOutId());
                orgExpand.setBusinessName(orgStoreBusinessDTO.getBusiness().getShortName());
                orgExpand.setBusinessOrgId(orgStoreBusinessDTO.getBusiness().getId());
                orgExpand.setPlatformId(orgStoreBusinessDTO.getBusiness().getParentId());
            }
        }
        orgExpand.setStatus((byte) 0);
        Date now = new Date();
        orgExpand.setGmtCreate(now);
        orgExpand.setGmtUpdate(now);
        TokenUserDTO currentUser = SecurityUtils.getCurrentUserToken();
        orgExpand.setCreatedBy(currentUser.getUserId());
        orgExpand.setCreatedByName(currentUser.getName());
        orgExpand.setUpdatedBy(currentUser.getUserId());
        orgExpand.setUpdatedByName(currentUser.getName());
        orgExpand.setVersion(1);
        log.info("createOrgExpand - 机构扩展对象创建完成");
        return orgExpand;
    }

    // =================================================================================================================
    // Unused or to be implemented methods from interface
    // =================================================================================================================
    @Override
    public CommonResult queryPriceLimitControlDetail(PriceLimitControlQueryParam param) {
        log.info("queryPriceLimitControlDetail - 开始执行，参数: {}", param);
        try {
            PriceLimitControlConfig config = priceLimitControlConfigMapper.selectByPrimaryKey(param.getId());
            if (config == null) {
                return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则不存在");
            }
            PriceLimitControlDetailDTO detail = new PriceLimitControlDetailDTO();
            BeanUtils.copyProperties(config, detail);
            return CommonResult.ok(detail);
        } catch (Exception e) {
            log.error("queryPriceLimitControlDetail - 执行异常", e);
            return new CommonResult(ReturnCodeEnum.SYSTEM_ERROR);
        }
    }

    @Override
    public CommonResult deletePriceLimitControl(PriceLimitControlQueryParam param) {
        log.info("deletePriceLimitControl - 开始执行，参数: {}", param);
        PriceLimitControlConfig existConfig = priceLimitControlConfigMapper.selectByPrimaryKey(param.getId());
        if (existConfig == null) {
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则不存在");
        }
        disableRule(existConfig);
        log.info("deletePriceLimitControl - 执行完成");
        return new CommonResult(ReturnCodeEnum.SUCCESS);
    }

    @Override
    @Transactional
    public CommonResult batchDeleteGoods(Long ruleId, List<Long> goodsIds) {
        log.info("batchDeleteGoods - 开始执行，规则ID: {}, 商品ID列表: {}", ruleId, goodsIds);
        if (ruleId == null) {
            return new CommonResult(ReturnCodeEnum.PARAM_ERROR.getCode(), "规则ID和商品编码列表不能为空");
        }
        PriceLimitControlGoodsConfigExample example=new PriceLimitControlGoodsConfigExample();
        PriceLimitControlGoodsConfigExample.Criteria criteria = example.createCriteria();
        criteria.andPriceLimitControlIdEqualTo(ruleId);
        if (!CollectionUtils.isEmpty(goodsIds)) {
            criteria.andIdIn(goodsIds);
        }
        priceLimitControlGoodsConfigMapper.deleteByExample(example);
        updateRuleGoodsCount(ruleId);
        return new CommonResult(ReturnCodeEnum.SUCCESS);
    }
}
