package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.cowell.pricecenter.enums.AdjustLimitConfigEnum;
import com.cowell.pricecenter.enums.PriceManageWhiteListEnum;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.ErpBizSupportExtService;
import com.cowell.pricecenter.service.feign.ErpBizSupportService;
import com.cowell.pricecenter.service.feign.vo.AdjustLimitConfigParam;
import com.cowell.pricecenter.service.feign.vo.AdjustLimitConfigRow;
import com.cowell.pricecenter.service.feign.vo.PriceManageWhiteListParam;
import com.cowell.pricecenter.service.feign.vo.PriceManageWhiteListVO;
import com.cowell.pricecenter.utils.Md5Utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

/**
 * @program: pricecenter
 * @description: ErpBizSupport的feign的扩展实现类
 * @author: jmlu
 * @create: 2022-12-11 12:32
 **/

@Service
public class ErpBizSupportExtServiceImpl implements ErpBizSupportExtService {

    private final Logger logger = LoggerFactory.getLogger(ErpBizSupportExtServiceImpl.class);
    @Resource
    private ErpBizSupportService erpBizSupportService;

    @Resource
    private RedissonClient redissonClient;


    @Override
    public List<AdjustLimitConfigRow> queryAdjustLimitConfig(AdjustLimitConfigEnum limitConfigEnum, Long businessId,String goodsNo) {
        List<AdjustLimitConfigRow> result = Collections.emptyList();
        if (businessId == null || limitConfigEnum == null) {
            return result;
        }
        String goodsNoKey = StringUtils.isBlank(goodsNo)?"":goodsNo;
        String key = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.ADJUST_LIMIT_CONFIG_BUSINESS_TYPE +
            businessId + "_" + limitConfigEnum.getCode()+"_"+goodsNoKey;
        RBucket<List<AdjustLimitConfigRow>> limitConfigBucket = redissonClient.getBucket(key);
        try {
            result = limitConfigBucket.get();
            if (result == null) {
                result = Collections.emptyList();
            }
        } catch (Exception e) {
            result = Collections.emptyList();
            logger.info("ErpBizSupportExtServiceImpl|queryAdjustLimitConfig| error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
            AdjustLimitConfigParam param = AdjustLimitConfigParam.getInstance(businessId, limitConfigEnum.getCode(),StringUtils.isBlank(goodsNo)?null:Lists.newArrayList(goodsNo));
            ResponseEntity<List<AdjustLimitConfigRow>> responseEntity = erpBizSupportService.queryAdjustLimitConfig(param);
            if(responseEntity != null && responseEntity.getStatusCode() == HttpStatus.OK &&
                responseEntity.getBody() != null) {
            	result = responseEntity.getBody();
                limitConfigBucket.set(result,1, TimeUnit.MINUTES);
            }
        }
        logger.info("ErpBizSupportExtServiceImpl|queryAdjustLimitConfig|result.size : {}", result.size());
        return result;
    }


	@Override
	public List<PriceManageWhiteListVO> queryPriceManageWhiteList(PriceManageWhiteListEnum whiteListEnum,
			List<Long> businessIdList, Long userId) {
		String redisKey = "";
		List<PriceManageWhiteListVO> result = Collections.emptyList();
		if(null==whiteListEnum || (CollectionUtils.isEmpty(businessIdList) && userId==null)) {
			return result;
		}
		if(CollectionUtils.isNotEmpty(businessIdList)) {
			businessIdList.sort(Comparator.naturalOrder());
			String newIds = StringUtils.join(businessIdList.toArray(),"_");
			redisKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.ADJUST_PRICE_MANAGE_WHILELIST_TYPE +
		            whiteListEnum.getCode()+"_"+Md5Utils.MD5Encode(newIds);
		}else {
			redisKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.ADJUST_PRICE_MANAGE_WHILELIST_TYPE +
		            whiteListEnum.getCode()+"_"+userId;
		}
		RBucket<List<PriceManageWhiteListVO>> whiteListBucket = redissonClient.getBucket(redisKey);
        try {
            result = whiteListBucket.get();
            if (result == null) {
                result = Collections.emptyList();
            }
        } catch (Exception e) {
            result = Collections.emptyList();
            logger.info("ErpBizSupportExtServiceImpl|queryPriceManageWhiteList| error", e);
        }
        if (CollectionUtils.isEmpty(result)) {
        	PriceManageWhiteListParam param = new PriceManageWhiteListParam();
        	param.setWhitelistType(whiteListEnum.getCode());
        	param.setBusinessIdList(businessIdList);
        	param.setUserId(userId);
            ResponseEntity<List<PriceManageWhiteListVO>> responseEntity = erpBizSupportService.queryPriceManageWhiteList(param);
            if(responseEntity != null && responseEntity.getStatusCode() == HttpStatus.OK &&
                responseEntity.getBody() != null) {
            	result = responseEntity.getBody();
            	whiteListBucket.set(result,1, TimeUnit.MINUTES);
            }
        }
        logger.info("ErpBizSupportExtServiceImpl|queryPriceManageWhiteList|result.size : {}", result.size());
        return result;
	}
	
}
