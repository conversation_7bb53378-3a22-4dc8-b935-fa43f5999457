package com.cowell.pricecenter.service.impl;

import com.beust.jcommander.internal.Lists;
import com.cowell.pricecenter.service.dto.ControlOrderOrgDetailQueryDTO;
import java.util.Collections;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.cowell.pricecenter.mapper.PriceManageControlOrgDetailMapper;
import com.cowell.pricecenter.entity.PriceManageControlOrgDetail;
import com.cowell.pricecenter.entity.PriceManageControlOrgDetailExample;
import com.cowell.pricecenter.service.PriceManageControlOrgDetailService;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:02
 */

@Service
public class PriceManageControlOrgDetailServiceImpl implements PriceManageControlOrgDetailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceManageControlOrgDetailServiceImpl.class);

    @Resource
    private PriceManageControlOrgDetailMapper priceManageControlOrgDetailMapper;

    @Override
    public long countByExample(PriceManageControlOrgDetailExample example) {
        return priceManageControlOrgDetailMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PriceManageControlOrgDetailExample example) {
        return priceManageControlOrgDetailMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return priceManageControlOrgDetailMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PriceManageControlOrgDetail record) {
        return priceManageControlOrgDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceManageControlOrgDetail record) {
        return priceManageControlOrgDetailMapper.insertSelective(record);
    }

    @Override
    public List<PriceManageControlOrgDetail> selectByExample(PriceManageControlOrgDetailExample example) {
        return priceManageControlOrgDetailMapper.selectByExample(example);
    }

    @Override
    public PriceManageControlOrgDetail selectByPrimaryKey(Long id) {
        return priceManageControlOrgDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(PriceManageControlOrgDetail record, PriceManageControlOrgDetailExample example) {
        return priceManageControlOrgDetailMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceManageControlOrgDetail record, PriceManageControlOrgDetailExample example) {
        return priceManageControlOrgDetailMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceManageControlOrgDetail record) {
        return priceManageControlOrgDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceManageControlOrgDetail record) {
        return priceManageControlOrgDetailMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<PriceManageControlOrgDetail> getControlOrgDetailList(ControlOrderOrgDetailQueryDTO detailQueryDTO) {
        if (Objects.isNull(detailQueryDTO)) {
            LOGGER.info("PriceManageControlOrgDetailServiceImpl|getControlOrgDetailList|查询条件为空");
            return Lists.newArrayList();
        }

        PriceManageControlOrgDetailExample detailExample = new PriceManageControlOrgDetailExample();
        PriceManageControlOrgDetailExample.Criteria criteria = detailExample.createCriteria();
        if (StringUtils.isNotBlank(detailQueryDTO.getControlOrderId())) {
            criteria.andControlOrderCodeEqualTo(detailQueryDTO.getControlOrderId());
        }

        if (CollectionUtils.isNotEmpty(detailQueryDTO.getControlOrderCodes())) {
            criteria.andControlOrderCodeIn(detailQueryDTO.getControlOrderCodes());
        }
        return priceManageControlOrgDetailMapper.selectByExample(detailExample);

    }

    @Override
    public List<PriceManageControlOrgDetail> getControlOrgDetailListByCode(String controlOrderCode) {
        List<PriceManageControlOrgDetail> result = Collections.emptyList();
        if (StringUtils.isNotBlank(controlOrderCode)) {
            ControlOrderOrgDetailQueryDTO queryDTO = new ControlOrderOrgDetailQueryDTO();
            queryDTO.setControlOrderId(controlOrderCode);
            result = getControlOrgDetailList(queryDTO);
        }
        return result;
    }

}
