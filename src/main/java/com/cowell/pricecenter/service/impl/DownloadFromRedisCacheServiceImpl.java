package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.IDownloadFromRedisCacheService;
import java.util.concurrent.TimeUnit;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: pricecenter
 * @description: 通过Redis缓存进行下载服务实现
 * @author: jmlu
 * @create: 2022-09-22 09:29
 **/

@Service
public class DownloadFromRedisCacheServiceImpl implements IDownloadFromRedisCacheService {

    @Autowired
    private RedissonClient redissonClient;

    private static final String PAGE_COUNT_SUFFIX = "_page_count";

    @Override
    public void cacheDownloadPageCount(String prefixKey, String downloadCode, Integer pageCount) {
        if (pageCount == null) {
            pageCount = 0;
        }
        String pageCountCacheKey = RedisKeysConstant.PROJECT_NAME + prefixKey + downloadCode + PAGE_COUNT_SUFFIX;
        RBucket<Integer> pageCountCache = redissonClient.getBucket(pageCountCacheKey);
        pageCountCache.set(pageCount,1, TimeUnit.HOURS);
    }

    @Override
    public Integer getDownloadPageCount(String prefixKey, String downloadCode) {
        String pageCountCacheKey = RedisKeysConstant.PROJECT_NAME + prefixKey + downloadCode + PAGE_COUNT_SUFFIX;
        RBucket<Integer> pageCountCache = redissonClient.getBucket(pageCountCacheKey);
        return pageCountCache.get();
    }

}
