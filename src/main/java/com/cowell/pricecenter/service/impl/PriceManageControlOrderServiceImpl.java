package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.permission.vo.OrgVO;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.*;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderDetailExMapper;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrderDetailExtMapper;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrderExtMapper;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrgDetailExtMapper;
import com.cowell.pricecenter.mq.producer.ControlOrderDetailSupplementDataProducer;
import com.cowell.pricecenter.mq.vo.ControlOrderDetailSupplementDataVO;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.AdjustImportResultDTO;
import com.cowell.pricecenter.service.dto.response.CommonRes;
import com.cowell.pricecenter.service.dto.response.ExportFileCubeVO;
import com.cowell.pricecenter.service.dto.response.amis.CommonResult;
import com.cowell.pricecenter.service.dto.response.amis.IdLongData;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.controlOrder.ControlExtendVO;
import com.cowell.pricecenter.service.dto.response.controlOrder.ControlOrderExtendDTO;
import com.cowell.pricecenter.service.dto.response.controlOrder.TypeInfo;
import com.cowell.pricecenter.service.feign.ErpBizSupportService;
import com.cowell.pricecenter.service.feign.NyuwaErpService;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.cowell.pricecenter.service.feign.vo.PriceModeResult;
import com.cowell.pricecenter.service.feign.vo.SpuListVO;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.utils.PriceUtil;
import com.cowell.pricecenter.utils.RequestHeaderContextUtils;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.cowell.pricecenter.web.rest.errors.AmisBusinessException;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import com.cowell.pricecenter.web.rest.util.CommonUtils;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.cowell.pricecenter.web.rest.util.OrgTypeLevelUtils;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/17 11:22
 */

@Service
public class PriceManageControlOrderServiceImpl extends BasePermissionService implements IPriceManageControlOrderService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceManageControlOrderServiceImpl.class);

    @Value("${default.audit.duration:30}")
    private long defaultAuditDuration = 30;

    private static final Integer DEFAULT_GAP_DURATION_ = 0;

    /**
     * 管控单excel批量导入最大商品数量
     */
    @Value("${adjust.excel.import.goods.size:1000}")
    private int maxExcelImportSize;

    @Autowired
    private IAdjustPriceOrderDetailV2Service adjustPriceOrderDetailV2Service;

    @Autowired
    private PriceManageControlOrgDetailMapper priceManageControlOrgDetailMapper;

    @Autowired
    private PriceManageControlOrderDetailService priceManageControlOrderDetailService;

    @Autowired
    private PriceManageControlOrderDetailReadService priceManageControlOrderDetailReadService;

    @Autowired
    private PriceManageControlOrderReadService priceManageControlOrderReadService;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;

    @Autowired
    private NoSequenceService noSequenceService;

    @Autowired
    private IMarketingExtService marketingExtService;

    @Autowired
    private PriceManageControlOrgDetailMapper controlOrgDetailMapper;

    @Autowired
    private PriceManageControlOrderMapper priceManageControlOrderMapper;

    @Autowired
    private PriceManageControlOrgDetailExtMapper controlOrgDetailExtendMapper;

    @Autowired
    private PermissionService permissionService;

    @Resource
    private ITocExtService tocExtService;

    @Autowired
    private ISearchExtService searchExtService;

    @Autowired
    private PriceManageControlOrderDetailMapper priceManageControlOrderDetailMapper;

    @Autowired
    private PriceManageControlOrderExtMapper priceManageControlOrderExtMapper;

    @Resource
    private IBasePriceOrderService basePriceOrderService;

    @Autowired
    private IAdjustPriceOrderV2Service adjustPriceOrderV2Service;

    @Autowired
    private IPermissionExtService permissionExtService;

    @Autowired
    private PriceControlNoticeMapper priceControlNoticeMapper;

    @Autowired
    private PriceManageControlOrderDetailExtMapper priceManageControlOrderDetailExtMapper;

    @Resource
    private ControlOrderDetailSupplementDataProducer supplementDataProducer;

    @Autowired
    private AdjustPriceOrderMapper adjustPriceOrderMapper;

    @Autowired
    private AdjustPriceOrderDetailMapper adjustPriceOrderDetailMapper;

    @Autowired
    private IPriceManageControlOrderBaseService priceManageControlOrderBaseService;

    @Autowired
    private ImportDataResultService importDataResultService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
	private TransactionTemplate transactionTemplate;

    @Autowired
    private NyuwaErpService nyuwaErpService;
    @Autowired
    private AdjustPriceOrderDetailExMapper adjustPriceOrderDetailExMapper;
    @Autowired
    @Qualifier("sendAdjustOrderDetailThreadExecutor")
    private AsyncTaskExecutor sendAdjustOrderDetailThreadExecutor;
    @Autowired
    private ErpBizSupportService erpBizSupportService;

    @Override
    public int deleteByExample(PriceManageControlOrderExample example) {
        return priceManageControlOrderMapper.deleteByExample(example);
    }

    @Override
    public int insert(PriceManageControlOrder record) {
        return priceManageControlOrderMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceManageControlOrder record) {
        return priceManageControlOrderMapper.insertSelective(record);
    }

    @Override
    public int updateByExampleSelective(PriceManageControlOrder record, PriceManageControlOrderExample example) {
        return priceManageControlOrderMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceManageControlOrder record, PriceManageControlOrderExample example) {
        return priceManageControlOrderMapper.updateByExample(record, example);

    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return priceManageControlOrderMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceManageControlOrder record) {
        return priceManageControlOrderMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceManageControlOrder record) {
        return priceManageControlOrderMapper.updateByPrimaryKey(record);
    }

    @Override
    public void generateControlStoreDetail(String controlOrderCode) {
        priceManageControlOrderDetailService.cursorScanControlOrderDetailList(controlOrderCode);

    }


    @Override
    public void batchUpdateControlOrderDetail(List<Long> ids, Integer effectStatus) {
        priceManageControlOrderDetailMapper.batchUpdatePriceManageControlOrderDetail(ids, effectStatus);
    }

    @Override
    public void batchUpdateControlOrder(List<String> controlCodes, Integer effectStatus) {
        priceManageControlOrderMapper.batchUpdateControlOrderEffectStatus(controlCodes, effectStatus);
    }

    /**
     * 新增管控单
     *
     * @param param
     * @param userDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult addControlOrder(ControlOrderEditParam param, TokenUserDTO userDTO) {
        if (CollectionUtils.isEmpty(param.getOrgIdList())) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CHOOSE_ORG);
        }
        try {
            //默认校验管控单
            if (param.getOrderType() == ManageControlOrderTableTypeEnum.CONTROL_TYPE.getType()) {
                priceManageControlOrderBaseService.checkAddPriceManageControlOrder(param);
                //名称重复校验
                checkRepeateControlName(param);
            }
            List<OrgDTO> orgVOS = permissionExtService.findIsFullScopeOrgIdList(userDTO.getUserId(), com.beust.jcommander.internal.Lists.newArrayList(param.getOrgIdList()));
            if (CollectionUtils.isEmpty(orgVOS)) {
                LOGGER.warn("addControlOrder 传入机构信息不存在");
                throw new AmisBadRequestException(ReturnCodeEnum.ERROR_ORG_NULL);
            }

            Integer orgType = orgVOS.stream().filter(v -> Objects.nonNull(v.getId())).map(v -> v.getType()).distinct().collect(Collectors.toList()).stream().findFirst().get();
            Integer orgLevel = OrgTypeLevelUtils.getLevelByTypeV2(orgType);
            Long userOrgId = param.getUserOrgId();
            OrgLevelVO orgLevelVO = permissionExtService.getOrgLevelVOByOrgId(userOrgId);

            if (Objects.nonNull(param.getId())) {
                PriceManageControlOrder priceManageControlEditOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getId());
                if (!ControlOrderAuditStatusEnum.isControlOrderEdit(priceManageControlEditOrder.getAuditStatus())) {
                    throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NOT_EDIT);
                }
                param.setControlOrderCode(priceManageControlEditOrder.getControlOrderId());
                //删除重新
                deleteControlOrderOrgByCode(priceManageControlEditOrder.getControlOrderId());
                //更新管控单
                updatePriceManageControlOrder(param, userDTO, orgLevel, orgLevelVO);
                param.setId(priceManageControlEditOrder.getId());

                //价格类型和渠道类型有变动的处理情况
                dealControlOrderDetailByChannelAndPriceTypeChange(param, priceManageControlEditOrder, userDTO);
            } else {
                param.setControlOrderCode(noSequenceService.priceNoSequence(null, param.getNoSequenceTypeEnum()));
                //插入管控单
                insertPriceManageControlOrder(param, userDTO, orgLevel, orgLevelVO);
            }
            //插入组织信息
            insertPriceManageControlOrgDetail(param, orgVOS);
            return CommonResult.ok(IdLongData.getInstance(param.getId()));
        } catch (Exception e) {
            LOGGER.warn("addControlOrder|warn", e);
            throw e;
        }
    }

    /**
     * 通过管控单号删除组织信息
     * @param controlOrderCode
     */
    protected void deleteControlOrderOrgByCode(String controlOrderCode) {
        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        orgDetailExample.createCriteria().andControlOrderCodeEqualTo(controlOrderCode);
        priceManageControlOrgDetailMapper.deleteByExample(orgDetailExample);
    }

    /**
     * 插入管控单信息
     * @param param
     * @param userDTO
     */
     public void insertPriceManageControlOrder(ControlOrderEditParam param, TokenUserDTO userDTO, Integer orgLevel, OrgLevelVO orgLevelVO){
        if(Objects.nonNull(param.getId())){
            LOGGER.warn("insertPriceManageControlOrder|管控但ID不为空，不能插入");
            return;
        }
        PriceManageControlOrder manageControlOrder = new PriceManageControlOrder();
        manageControlOrder.setControlOrderId(param.getControlOrderCode());
        manageControlOrder.setOrgId(orgLevelVO.getOrgId());
        manageControlOrder.setOrgName(orgLevelVO.getOrgName());
        manageControlOrder.setOrderLevel(orgLevel);
        manageControlOrder.setAuditStatus(ControlOrderAuditStatusEnum.CALCULATING.getCode());
        manageControlOrder.setControlReason(param.getControlReason());
        manageControlOrder.setStatus(DeleteStatusEnum.NORMAL.getCode());
        manageControlOrder.setCreatedBy(userDTO.getUserId());
        manageControlOrder.setCreatedByName(userDTO.getName());
        manageControlOrder.setUpdatedBy(userDTO.getUserId());
        manageControlOrder.setUpdatedByName(userDTO.getName());
        manageControlOrder.setAuditBy(0L);
        manageControlOrder.setAuditByName("");
        manageControlOrder.setAuditReason("");
        manageControlOrder.setChannel(org.apache.commons.lang3.StringUtils.join(param.getChannelList(), ","));
        manageControlOrder.setPriceType(org.apache.commons.lang3.StringUtils.join(param.getPriceTypeList(), ","));
        manageControlOrder.setControlType(param.getControlType());
        manageControlOrder.setEffectStatus(String.valueOf(PriceEffectStatusEnum.UN_EFFECT.getCode()));
        manageControlOrder.setControlOrderType(param.getControlOrderType());
        manageControlOrder.setControlOrderName(param.getControlOrderName());
        manageControlOrder.setOrderType(param.getOrderType().byteValue());
        manageControlOrder.setExtend(param.getExtend());
        priceManageControlOrderMapper.insertSelective(manageControlOrder);
        param.setId(manageControlOrder.getId());
    }

    /**
     * 更新管控单
     * @param userDTO
     */
    protected void updatePriceManageControlOrder(ControlOrderEditParam param, TokenUserDTO userDTO, Integer orgLevel, OrgLevelVO orgLevelVO){
        //只有制单中、待提交、驳回的可以编辑
        PriceManageControlOrderExample controlOrderExample = new PriceManageControlOrderExample();
        controlOrderExample.createCriteria().andIdEqualTo(param.getId()).andAuditStatusIn(com.beust.jcommander.internal.Lists.newArrayList(AuditStatusEnum.IN_PREPARATION.getCode(), AuditStatusEnum.UN_SUBMITTED.getCode(), AuditStatusEnum.AUDIT_REJECTED.getCode()));

        PriceManageControlOrder manageControlOrder = new PriceManageControlOrder();
        manageControlOrder.setControlReason(param.getControlReason());
        manageControlOrder.setUpdatedBy(userDTO.getUserId());
        manageControlOrder.setUpdatedByName(userDTO.getName());
        manageControlOrder.setOrgId(orgLevelVO.getOrgId());
        manageControlOrder.setOrgName(orgLevelVO.getOrgName());
        manageControlOrder.setOrderLevel(orgLevel);
        manageControlOrder.setAuditStatus(AuditStatusEnum.IN_PREPARATION.getCode());
        manageControlOrder.setChannel(org.apache.commons.lang3.StringUtils.join(param.getChannelList(), ","));
        manageControlOrder.setPriceType(org.apache.commons.lang3.StringUtils.join(param.getPriceTypeList(), ","));
        manageControlOrder.setControlType(param.getControlType());
        manageControlOrder.setEffectStatus(String.valueOf(PriceEffectStatusEnum.UN_EFFECT.getCode()));
        manageControlOrder.setControlOrderType(param.getControlOrderType());
        manageControlOrder.setControlOrderName(param.getControlOrderName());
        manageControlOrder.setControlType(param.getControlType());
        manageControlOrder.setExtend(param.getExtend());
        priceManageControlOrderMapper.updateByExampleSelective(manageControlOrder, controlOrderExample);
    }

    /**
     * 插入组织信息
     * @param param
     * @param orgVOS
     */
    protected void insertPriceManageControlOrgDetail(ControlOrderEditParam param, List<OrgDTO> orgVOS){
        List<PriceManageControlOrgDetail> orgDetailList = new ArrayList<>();
        for (OrgDTO orgVO : orgVOS) {
            PriceManageControlOrgDetail controlOrgDetail = new PriceManageControlOrgDetail();
            controlOrgDetail.setControlOrderCode(param.getControlOrderCode());
            controlOrgDetail.setOrgId(orgVO.getId());
            controlOrgDetail.setOrgLevel(OrgTypeLevelUtils.getLevelByTypeV2(orgVO.getType()));
            controlOrgDetail.setOrgName(orgVO.getName());
            controlOrgDetail.setGmtCreate(new Date());
            controlOrgDetail.setGmtUpdate(new Date());
            controlOrgDetail.setOrderType((byte)1);
            orgDetailList.add(controlOrgDetail);
        }

        if (CollectionUtils.isNotEmpty(orgDetailList)) {
            for (List<PriceManageControlOrgDetail> orgDetails : com.google.common.collect.Lists.partition(orgDetailList, 200)) {
                controlOrgDetailExtendMapper.batchInsert(orgDetails);
            }
        }
    }

    /**
     * 校验名称重复
     * @param param
     */
    private void checkRepeateControlName(ControlOrderEditParam param) {
        //名称重复校验
        PriceManageControlOrderExample orderExample = new PriceManageControlOrderExample();
        orderExample.setLimit(1);
        PriceManageControlOrderExample.Criteria criteria = orderExample.createCriteria();
        if (Objects.nonNull(param.getId())) {
            criteria.andIdNotEqualTo(param.getId());
        }
        criteria.andControlOrderNameEqualTo(StringUtils.trim(param.getControlOrderName()));
        criteria.andOrderTypeEqualTo(param.getOrderType().byteValue());
        long nameCount = priceManageControlOrderMapper.countByExample(orderExample);
        LOGGER.info("checkRepeateControlName|nameCount:{}.", nameCount);
        if (nameCount > 0) {
            throw new AmisBadRequestException(ReturnCodeEnum.NAME_REPEAT);
        }
    }

    private void dealControlOrderDetailByChannelAndPriceTypeChange(ControlOrderEditParam param, PriceManageControlOrder priceManageControlEditOrder, TokenUserDTO userDTO) {
        LocalDateTime operateTime = LocalDateTime.now();
        Date operateTimeDate = Date.from(operateTime.atZone(ZoneId.systemDefault()).toInstant());
        if (Objects.nonNull(param.getStep()) && param.getStep() == PricePageStepEnum.STEP_1.getStep()) {
            PriceOrderPriceTypeAndChannelChange orderPriceTypeAndChannelChange = basePriceOrderService.getChangeChannelAndPriceType(priceManageControlEditOrder.getChannel(), priceManageControlEditOrder.getPriceType(),
                param.getChannelList().stream().filter(v -> StringUtils.isNotBlank(v)).map(v -> Integer.parseInt(v)).collect(Collectors.toList()), param.getPriceTypeList());
            priceManageControlOrderDetailReadService.resetOrderDetailsByChannelsAndPriceTypes(priceManageControlEditOrder, orderPriceTypeAndChannelChange, userDTO, operateTimeDate);
        }
    }


    /**
     * 编辑管控单
     *
     * @param param
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long editControlOrder(ControlOrderEditParam param) {
        PriceManageControlOrder manageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getId());
        if (manageControlOrder == null) {
            LOGGER.error("editControlOrder 编辑管控单基本信息，未查询到管控单，id={}", param.getId());
            return param.getId();
        }
        if (StringUtils.isNotBlank(param.getControlOrderName())) {
            manageControlOrder.setControlOrderName(param.getControlOrderName());
        }
        if (CollectionUtils.isNotEmpty(param.getChannelList())) {
            manageControlOrder.setChannel(StringUtils.join(param.getChannelList(), ","));
        }
        if (CollectionUtils.isNotEmpty(param.getPriceTypeList())) {
            manageControlOrder.setPriceType(StringUtils.join(param.getPriceTypeList(), ","));
        }
        if (param.getControlType() != null) {
            manageControlOrder.setControlType(param.getControlType());
        }
        //管控原因
        if (StringUtils.isNotBlank(param.getControlReason())) {
            manageControlOrder.setControlReason(param.getControlReason());
        } else {
            manageControlOrder.setControlReason("");
        }
        //处理机构信息
        if (CollectionUtils.isNotEmpty(param.getOrgIdList())) {
            List<OrgInfoDTO> orgVOS = permissionService.listOrgInfoByIdWithoutType(param.getOrgIdList());
            if (CollectionUtils.isEmpty(orgVOS)) {
                throw new AmisBadRequestException(ReturnCodeEnum.ERROR_ORG_NULL);
            }
            if (orgVOS.stream().map(OrgVO::getType).distinct().count() > 1L) {
                throw new AmisBadRequestException(ReturnCodeEnum.ERROR_ORG_NULL);
            }

            List<PriceManageControlOrgDetail> orgDetailList = new ArrayList<>();
            for (OrgVO orgVO : orgVOS) {
                PriceManageControlOrgDetail controlOrgDetail = new PriceManageControlOrgDetail();
                controlOrgDetail.setControlOrderCode(manageControlOrder.getControlOrderId());
                controlOrgDetail.setOrgId(orgVO.getId());
                controlOrgDetail.setOrgLevel(OrgTypeLevelUtils.getLevelByTypeV2(orgVO.getType()));
                controlOrgDetail.setOrgName(orgVO.getName());
                controlOrgDetail.setGmtCreate(new Date());
                controlOrgDetail.setGmtUpdate(new Date());
                controlOrgDetail.setOrderType((byte)1);
                orgDetailList.add(controlOrgDetail);
            }
            PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
            orgDetailExample.createCriteria().andControlOrderCodeEqualTo(manageControlOrder.getControlOrderId());
            controlOrgDetailMapper.deleteByExample(orgDetailExample);
            if (CollectionUtils.isNotEmpty(orgDetailList)) {
                for (List<PriceManageControlOrgDetail> orgDetails : Lists.partition(orgDetailList, 200)) {
                    controlOrgDetailExtendMapper.batchInsert(orgDetails);
                }
            }
        }
        priceManageControlOrderMapper.updateByPrimaryKeySelective(manageControlOrder);
        return param.getId();
    }


    /**
     * 编辑管控单规则信息
     * @param param
     * @return PriceManageControlOrder manageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getId());
    if (manageControlOrder == null) {
     */
    @Override
    public Long editRuleControlOrder(ControlOrderEditParam param) {
        if (Objects.isNull(param) || Objects.isNull(param.getScheduledTime())) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_EFFECT_NULL);
        }
        PriceManageControlOrder manageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getId());
        if (manageControlOrder == null) {
            LOGGER.warn("editRuleControlOrder 编辑管控单规则信息，未查询到管控单，id={}", param.getId());
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }
        LocalDateTime scheduledDate = LocalDate.parse(param.getScheduledTime(), DateTimeFormatter.ISO_DATE).atStartOfDay();
        if (!scheduledDate.isAfter(LocalDateTime.now())) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_EFFECT_TIME_LATER);
        }
        PriceManageControlOrder updateControlOrder = new PriceManageControlOrder();
        updateControlOrder.setId(manageControlOrder.getId());
        updateControlOrder.setScheduledTime(scheduledDate);
        priceManageControlOrderMapper.updateByPrimaryKeySelective(updateControlOrder);
        return param.getId();
    }


    /**
     * 删除管控单
     *
     * @param controlOrderId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteControlOrder(Long controlOrderId) {
        int ret = 0;
        PriceManageControlOrder manageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlOrderId);
        if (manageControlOrder == null) {
            LOGGER.info("deleteControlOrder 要删除的管控单不存在 controlOrderId={}", controlOrderId);
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }
        //判断管控单状态，只有制单中和驳回状态的才可以被删除
        if (ControlOrderAuditStatusEnum.CALCULATING.getCode() != manageControlOrder.getAuditStatus()
            && ControlOrderAuditStatusEnum.UN_SUBMITTED.getCode() != manageControlOrder.getAuditStatus()
            && ControlOrderAuditStatusEnum.AUDIT_REJECTED.getCode() != manageControlOrder.getAuditStatus()) {
            LOGGER.info("deleteControlOrder 要删除的管控单不是制单中或审核驳回状态，不允许删除 controlOrderId={}", controlOrderId);
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_NOTSUPPORT_DELETE);
        }
        //删除管控单信息
        ret += priceManageControlOrderMapper.deleteByPrimaryKey(controlOrderId);

        //删除门店信息
        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        orgDetailExample.createCriteria().andControlOrderCodeEqualTo(manageControlOrder.getControlOrderId());
        ret += controlOrgDetailMapper.deleteByExample(orgDetailExample);

        //删除商品信息
        PriceManageControlOrderDetailExample orderDetailExample = new PriceManageControlOrderDetailExample();
        orderDetailExample.createCriteria().andControlOrderCodeEqualTo(manageControlOrder.getControlOrderId());
        ret += priceManageControlOrderDetailMapper.deleteByExample(orderDetailExample);
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void controlOrderGoodsAdd(ControlOrderGoodsParam param, TokenUserDTO userDTO){
        List<PriceManageControlOrderDetail> controlOrderDetailList = batchControlOrderGoodsAdd(param, userDTO);
        batchInsertAndAsyncCompleteData(controlOrderDetailList);
    }

    /**
     * 批量新增明细
     *
     * @param param
     */
    private List<PriceManageControlOrderDetail> batchControlOrderGoodsAdd(ControlOrderGoodsParam param, TokenUserDTO userDTO) {
        PriceManageControlOrder manageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getId());
        if (manageControlOrder == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }

        List<String> hasAddGoodsNoList = getGoodsNoListbyControlCode(manageControlOrder.getControlOrderId());
        List<String> toAddGoodsNoList = param.getGoodsCodeList().stream()
            .distinct().
                filter(goodsNo -> !hasAddGoodsNoList.contains(goodsNo))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(toAddGoodsNoList)) {
            LOGGER.warn("controlOrderGoodsAdd|toAddGoodsNoList|为空");
            return new ArrayList<PriceManageControlOrderDetail>();
        }

        //价格类型
        List<String> priceTypeList = priceTypeToList(manageControlOrder.getPriceType());
        //渠道
        List<Integer> channelList = channelToList(manageControlOrder.getChannel());
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(manageControlOrder.getPriceType());
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(manageControlOrder.getChannel());

        int size = channelList.size() * priceTypeList.size() * toAddGoodsNoList.size();
        if (size <= 0) {
            LOGGER.warn("添加明细商品失败");
            return new ArrayList<PriceManageControlOrderDetail>();
        }
        List<PriceManageControlOrderDetail> controlOrderDetailList = new ArrayList<>(size);
        long[] adjustDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.PRICE_MANAGE_CONTROL_ORDER_DETAIL.getBiz(), size);
        int idIndex = 0;
        for (String goodsCode : toAddGoodsNoList) {
            for (String priceTypeCode : priceTypeList) {

                PriceType priceType = priceTypeMap.get(priceTypeCode);
                if (priceType == null) {
                    LOGGER.error("<===[controlOrderGoodsAdd]  priceTypeCode: {} 不存在", priceTypeCode);
                    throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR);
                }

                for (Integer channel : channelList) {

                    PriceChannel priceChannel = priceChannelMap.get(channel);
                    if (priceChannel == null) {
                        LOGGER.error("<===[controlOrderGoodsAdd]  channelId: {} 不存在", channel);
                        throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR);
                    }

                    PriceManageControlOrderDetail controlOrderDetail = new PriceManageControlOrderDetail();
                    controlOrderDetail.setControlOrderDetailId(String.valueOf(adjustDetailIds[idIndex++]));
                    controlOrderDetail.setControlOrderCode(manageControlOrder.getControlOrderId());
                    controlOrderDetail.setAuthOrgId(manageControlOrder.getOrgId());
                    controlOrderDetail.setAuthOrgName(manageControlOrder.getOrgName());
                    controlOrderDetail.setAuthLevel(manageControlOrder.getOrderLevel());
                    controlOrderDetail.setOrgGoodsId(null);
                    controlOrderDetail.setGoodsNo(goodsCode);
                    controlOrderDetail.setPriceGroupNames("");
                    controlOrderDetail.setPriceTypeId(priceType.getId());
                    controlOrderDetail.setPriceTypeCode(priceTypeCode);
                    controlOrderDetail.setPriceTypeName(priceType.getName());
                    controlOrderDetail.setPriceTypeOutCode(null);// 取不到直接为空
                    controlOrderDetail.setPrice(BigDecimal.ZERO);
                    controlOrderDetail.setOriginalPrice(BigDecimal.ZERO);
                    controlOrderDetail.setUpperLimit(null);
                    controlOrderDetail.setLowerLimit(null);
                    controlOrderDetail.setStatus(StatusEnum.NORMAL.getCode());
                    controlOrderDetail.setEffectStatus(EffectStatusEnum.NO_EFFECT.getCode());
                    controlOrderDetail.setDxsDate(manageControlOrder.getScheduledTime());
                    controlOrderDetail.setGmtCreate(LocalDateTime.now());
                    controlOrderDetail.setGmtUpdate(LocalDateTime.now());
                    controlOrderDetail.setExtend1(ControlOrderExtendDTO.getJSONFormatStr(DataCompletionEnum.NOT_COMPLETE.getCode(), DataFullEnum.NOT_FULL.getCode()));
                    controlOrderDetail.setExtend("");
                    controlOrderDetail.setVersion(0);
                    controlOrderDetail.setCreatedBy(userDTO.getUserId());
                    controlOrderDetail.setUpdatedBy(userDTO.getUserId());
                    controlOrderDetail.setUpdatedByName(userDTO.getUserName());
                    controlOrderDetail.setChannelId(channel);
                    controlOrderDetail.setChannelOutCode(priceChannel.getOutChannelCode());
                    controlOrderDetail.setChannelEnCode(priceChannel.getChannelEnCode());
                    controlOrderDetail.setGuidePrice(null);
                    controlOrderDetail.setOrderType((byte)1);
                    controlOrderDetailList.add(controlOrderDetail);
                }

            }
        }

        return controlOrderDetailList;
    }

    public List<String> getGoodsNoListbyControlCode(String controlOrderCode) {
        if (org.springframework.util.StringUtils.isEmpty(controlOrderCode)) {
            LOGGER.error("<===[AdjustPriceOrderV2ServiceImpl.getGoodsNoListbyControlCode]  参数都不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<String> hasAddGoodsNoList = priceManageControlOrderDetailExtMapper.getGoodsNoListbyControlCode(controlOrderCode);
        if (hasAddGoodsNoList == null) {
            hasAddGoodsNoList = Collections.emptyList();
        }
        return hasAddGoodsNoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchOrSingleAddAdjustPriceOrder(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO) {
        //医保限价预警
        if(CommonEnums.BizTypeEnum.PRICE_LIMIT_CONTROL_WARN.getCode().equals(param.getBizTypeCode())){
            return batchOrSingleAddAdjustPriceOrderByIdList(param,userDTO);
        }
        List<PriceControlNotice> noticeList = checkControlOrderNoticeRelation(param, Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.REJECTED_DEAL.getCode()));
        try {
            if (param.getIdList().size() == 1) {
                PriceControlNotice priceControlNotice = noticeList.stream().findFirst().get();
                priceControlNotice.setPriceTypeCodeJoin(priceControlNotice.getPriceTypeCode());
                Long adjusePriceOrderId = addAdjustPriceOrderCommon(priceControlNotice, Lists.newArrayList(priceControlNotice.getGoodsNo()), Lists.newArrayList(priceControlNotice.getStoreId()), 1L, userDTO, param.getUserOrgId(),null).stream().findFirst().get();
                //更改通知单状态
                updateOnlyPriceNoticeStatus(param.getIdList());
                return Lists.newArrayList(adjusePriceOrderId);
            }
            List<Long> adjusePriceOrderIdList = Lists.newArrayList();
            //按照价格类型、渠道、生效时间分组
            Map<String, List<PriceControlNotice>> baseInfoMap = noticeList.stream().collect(Collectors.groupingBy(v -> new StringBuilder().append(v.getPriceTypeCode()).append(Constants.LINE).append(v.getChannelId()).append(Constants.LINE).append(v.getDxsDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).toString()));
            baseInfoMap.forEach((baseKey, notices) -> {
                //把相同商品编码的门店id汇总 设置调价单明细机构
                Map<String, List<Long>> goodsNoMapperStoreIdsMap = notices.stream().collect(Collectors.
                    groupingBy(PriceControlNotice::getGoodsNo,Collectors.mapping(PriceControlNotice::getStoreId, Collectors.toList())));
                List<Long> storeIdList = notices.stream().map(v -> v.getStoreId()).distinct().collect(Collectors.toList());
                Long goodsCount = notices.stream().map(v -> v.getGoodsNo()).distinct().count();
                List<String> goodsNoList = notices.stream().filter(v->StringUtils.isNotBlank(v.getGoodsNo())).map(v->v.getGoodsNo()).distinct().collect(Collectors.toList());
                //信息合并
                PriceControlNotice controlNotice = notices.stream().findFirst().get();
                controlNotice.setPriceTypeCodeJoin(controlNotice.getPriceTypeCode());
                adjusePriceOrderIdList.addAll(addAdjustPriceOrderCommon(controlNotice, goodsNoList, storeIdList, goodsCount, userDTO, param.getUserOrgId(),goodsNoMapperStoreIdsMap));
            });

            LOGGER.info("batchOrSingleAddAdjustPriceOrder|adjusePriceOrderIdList:{}.",adjusePriceOrderIdList);
            //更改通知单状态
            updateOnlyPriceNoticeStatus(param.getIdList());
            return adjusePriceOrderIdList;
        } catch (Exception e) {
            LOGGER.warn("batchOrSingleAddAdjustPriceOrder|生成调价单失败", e);
            throw e;
        }
    }

    /**
     * 医保限价预警转调价单明细基本信息
     * @param param
     * @return
     */
    private List<AutoPriceAdjustOrderDetailDTO> convertPriceLimitWarnToAutoPriceAdjustOrderDetail(ControlNoticeAddAdjustParam param){
        List<AutoPriceAdjustOrderDetailDTO> autoPriceAdjustOrderDetailList = Lists.newArrayList();
        List<PriceLimitControlWarnDTO> priceLimitWarnList = getPriceLimitControlWarnList(param.getIdList());
        if(CollectionUtils.isEmpty(priceLimitWarnList)){
            throw new AmisBadRequestException("根据id未查到限价管控预警数据");
        }
        AutoPriceAdjustOrderDetailDTO detail = null;
        for(PriceLimitControlWarnDTO warn : priceLimitWarnList){
            detail = new AutoPriceAdjustOrderDetailDTO();
            detail.setChannelId(warn.getChannelId());
            detail.setStoreId(warn.getStoreId());
            detail.setStoreName(warn.getStoreName());
            detail.setStoreOrgId(warn.getStoreOrgId());
            detail.setPriceTypeCode(warn.getPriceType());
            detail.setGoodsNo(warn.getGoodsNo());
            detail.setSuggestedPrice(warn.getSuggestedPrice());
            autoPriceAdjustOrderDetailList.add(detail);
        }
        return autoPriceAdjustOrderDetailList;
    }

    /**
     *
     * @Title: assembleAdjustOrder
     * @Description: 根据价格调整通知组合调价单
     * @param: @param noticeList
     * @param: @return
     * @return: List<Map<String,List<PriceControlNotice>>>
     * @throws
     */
    private List<Map<String, List<PriceControlNotice>>> assembleAdjustOrder(List<PriceControlNotice> noticeList) {
    	List<Map<String, List<PriceControlNotice>>> adjustOrderList = Lists.newArrayList();
		//按照渠道、生效时间分组。不同不会分到一个调价单
		Map<String, List<PriceControlNotice>> channelMap = noticeList.stream().collect(Collectors.groupingBy(v -> new StringBuilder().append(v.getChannelId()).append("_").append(v.getDxsDate()).toString()));
		channelMap.forEach((key,groupNoticeList) -> {
			//拼接价格类型：商品集合
			Map<String, List<PriceControlNotice>> priceTypeCodeMap = new HashMap<String, List<PriceControlNotice>>();
			//根据商品编码分组
		    Map<String, List<PriceControlNotice>> goodsNoMap = groupNoticeList.stream().collect(Collectors.groupingBy(v -> new StringBuilder().append(v.getGoodsNo()).toString()));
		    goodsNoMap.forEach((goodsNo,dataList) -> {
		    	//按照 PriceTypeCode 进行倒排 拼接。
				List<PriceControlNotice> sortList = dataList.stream().sorted(Comparator.comparing(PriceControlNotice::getPriceTypeCode).reversed()).collect(Collectors.toList());
				String priceTypeCodeJoinStr = sortList.stream().map(v -> v.getPriceTypeCode()).distinct().collect(Collectors.joining(PriceConstant.VALUE_SEPARATOR));
				if(!priceTypeCodeMap.containsKey(priceTypeCodeJoinStr)) {
					priceTypeCodeMap.put(priceTypeCodeJoinStr, dataList);
					return;
				}
				priceTypeCodeMap.get(priceTypeCodeJoinStr).addAll(dataList);
				sortList.clear();
				dataList.clear();
		    });
		    adjustOrderList.add(priceTypeCodeMap);
		    groupNoticeList.clear();
		});
        return adjustOrderList;
    }

    /**
     * 只更新状态
     * @param idList
     */
    private void updateOnlyPriceNoticeStatus(List<Long> idList){
        //更改通知单状态
        PriceControlNoticeExample controlNoticeExample = new PriceControlNoticeExample();
        controlNoticeExample.createCriteria().andIdIn(idList).andStatusIn(com.google.common.collect.Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.REJECTED_DEAL.getCode()));
        PriceControlNotice priceControlNotice = new PriceControlNotice();
        priceControlNotice.setStatus(PriceNoticeStatusEnum.ING_DEAL.getCode());
        priceControlNoticeMapper.updateByExampleSelective(priceControlNotice, controlNoticeExample);
    }

    /**
     * 校验管控通知相关操作参数
     * @param param
     * @param statusList
     * @return
     */
    protected List<PriceControlNotice> checkControlOrderNoticeRelation(ControlNoticeAddAdjustParam param, List<Integer> statusList){
        if(CollectionUtils.isEmpty(param.getIdList())){
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<String> priceTypeCodeList = permissionExtService.getPriceTypeList().stream().filter(Objects::nonNull).map(v->v.getCode()).collect(Collectors.toList());
        List<Integer> priceChannelIdList = permissionExtService.getPriceChannelList().stream().filter(Objects::nonNull).map(v->Integer.parseInt(v.getCode())).collect(Collectors.toList());

        PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
        noticeExample.createCriteria()
            .andIdIn(param.getIdList())
            .andPriceTypeCodeIn(priceTypeCodeList)
            .andChannelIdIn(priceChannelIdList)
            .andStatusIn(statusList);

        List<PriceControlNotice> noticeList = priceControlNoticeMapper.selectByExample(noticeExample);
        if(CollectionUtils.isEmpty(noticeList)){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_DATA_NULL);
        }
        List<String> controlOrderNoList = noticeList.stream().filter(v->StringUtils.isNotBlank(v.getControlOrderId())).map(v->v.getControlOrderId()).distinct().collect(Collectors.toList());

        if(basePriceOrderService.containsEffectControlOrder(controlOrderNoList)){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_EFFECT);
        }

        if(noticeList.size() < param.getIdList().size()){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NOT_WRONG);
        }
        long uncontrolCount = noticeList.stream().filter(v->PriceNoticeStatusEnum.isUncontrol(v.getStatus())).count();
        if(uncontrolCount < param.getIdList().size()){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NOT_WRONG);
        }
        return noticeList;
    }

    /**
     * 生成调价单通用方法
     * @param controlNotice
     * @param userDTO
     * @return
     */
    protected List<Long> addAdjustPriceOrderCommon(PriceControlNotice controlNotice, List<String> goodsNoList, List<Long> storeIdList, Long goodsCount, TokenUserDTO userDTO, Long userOrgId,Map<String, List<Long>> goodsNoMapperStoreIdsMap){
        List<Long> adjusePriceOrderIdList = com.google.common.collect.Lists.newArrayList();
        //生成调价单头信息
        AdjustPriceOrderV2Param adjustPriceOrderV2Param = createAdjustPriceOrderV2Param(controlNotice,storeIdList,goodsCount, userOrgId, userDTO);
        Long adjustPriceOrderId = adjustPriceOrderV2Service.addAdjustPriceOrder(adjustPriceOrderV2Param, userDTO);
        adjusePriceOrderIdList.add(adjustPriceOrderId);
        //调价单明细信息
        adjustPriceOrderDetailV2Service.addAdjustPriceOrderDetails(new AdjustPriceOrderDetailAddV2Param(adjustPriceOrderId, goodsNoList, Constants.AUTO_ADJUST_REASON, goodsNoMapperStoreIdsMap),userDTO,CreateAdjustDataSourceEnum.CONTROL_NOTICE_ADD.getCode());
        return adjusePriceOrderIdList;
    }

    /**
     * 组织调价单表头参数
     * @param controlNotice
     * @param storeIdList
     * @return
     */
    protected AdjustPriceOrderV2Param createAdjustPriceOrderV2Param(PriceControlNotice controlNotice, List<Long> storeIdList, Long goodsCount, Long userOrgId, TokenUserDTO userDTO) {
        AdjustPriceOrderV2Param adjustPriceOrderV2Param = new AdjustPriceOrderV2Param();
        List<TypeInfo<String,String>> typeInfoList = permissionExtService.getPermGoodsList().stream().filter(v-> org.apache.commons.lang3.StringUtils.isNotBlank(v.getCode())).collect(Collectors.toList());
        adjustPriceOrderV2Param.setAdjustName(getIncrNum(null,userDTO));
        adjustPriceOrderV2Param.setOrgIdList(permissionExtService.listOrgIdByOutId(storeIdList, OrgTypeEnum.STORE.getCode()));
        adjustPriceOrderV2Param.setPriceTypeCodeList(com.google.common.collect.Lists.newArrayList(controlNotice.getPriceTypeCode()));
        adjustPriceOrderV2Param.setChannelIdList(com.google.common.collect.Lists.newArrayList(controlNotice.getChannelId()));
        adjustPriceOrderV2Param.setChannelIds(Objects.nonNull(controlNotice.getChannelId())?controlNotice.getChannelId().toString():"");
        adjustPriceOrderV2Param.setPriceTypeCodes(controlNotice.getPriceTypeCodeJoin());
        adjustPriceOrderV2Param.setGoodsScope(CollectionUtils.isEmpty(typeInfoList)?-1:Integer.parseInt(typeInfoList.get(0).getCode()));
        adjustPriceOrderV2Param.setAdjustType(AdjustTypeEnum.SUPER_CONTROL.getCode());
        adjustPriceOrderV2Param.setAdjustReason(Constants.CONTROL_NOTICE_CREATE);
        adjustPriceOrderV2Param.setScheduledTime(Objects.isNull(controlNotice.getDxsDate())?null:Date.from(controlNotice.getDxsDate().atZone(ZoneId.systemDefault()).toInstant()));
        adjustPriceOrderV2Param.setScheduledTimeSecondLong(Objects.isNull(controlNotice.getDxsDate())?"":String.valueOf(controlNotice.getDxsDate().toEpochSecond(ZoneOffset.of("+8"))));
        adjustPriceOrderV2Param.setUserOrgId(Objects.isNull(userOrgId)?0L:userOrgId);
        adjustPriceOrderV2Param.setStep(PricePageStepEnum.STEP_12.getStep());
        adjustPriceOrderV2Param.setExecStatus(ExecEnum.EXEC_PLAN.getCode());
        adjustPriceOrderV2Param.setOrgTabType(PriceOrderTabTypeEnum.STORE_LIST.getType());
        adjustPriceOrderV2Param.setTabTypeValList(com.beust.jcommander.internal.Lists.newArrayList(1));
        adjustPriceOrderV2Param.setGoodsCount(goodsCount.intValue());
        return adjustPriceOrderV2Param;
    }

    /**
     * 提交审核之后。更改通知表状态（价格通知）
     * @param unPriceManageControlOrder
     */
    protected void updatePriceControlNoticeStatus(PriceManageControlOrder unPriceManageControlOrder) {
        //更改状态 PriceNoticeStatusEnum
        Optional<ControlExtendVO> optionalControlExtendVO = ControlExtendVO.getInstance(unPriceManageControlOrder.getExtend());
        if(!optionalControlExtendVO.isPresent()){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UNCONTROL_NOT_FULL);
        }
        String noticeIds = optionalControlExtendVO.get().getNoticeIds();
        if(StringUtils.isBlank(noticeIds)){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UNCONTROL_NOT_FULL);
        }
        List<Long> idList = Arrays.asList(noticeIds.split(",")).stream().map(v->Long.parseLong(v)).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(idList)){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UNCONTROL_NOT_FULL);
        }
        //更改通知单状态
        updateOnlyPriceNoticeStatus(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchOrSingleAddNotControlApply(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO) {
        try {
            //校验
            priceManageControlOrderBaseService.checkControlOrderNameAndReason(param.getControlOrderName(), param.getControlNoExecReason());

            PriceManageControlOrder unPriceManageControlOrder = new PriceManageControlOrder();
            if (Objects.nonNull(param.getIsEditUncontrol()) && param.getIsEditUncontrol()) {
                if (StringUtils.isBlank(param.getUnControlOrderCode())) {
                    LOGGER.warn("batchOrSingleAddNotControlApply|不管控单编码为空");
                    return;
                }
                //不执行管控单提交审核
                unPriceManageControlOrder = editNoControlOrderList(param);
            } else {
                List<PriceControlNotice> noticeList = checkControlOrderNoticeRelation(param, Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.REJECTED_DEAL.getCode()));

                //获取通知里的管控单
                List<String> controlOrderNoList = noticeList.stream().filter(v -> StringUtils.isNotBlank(v.getControlOrderId())).map(v -> v.getControlOrderId()).distinct().collect(Collectors.toList());
                param.setControlOrderCodeList(controlOrderNoList);
                //复制管控单信息，用来生成不管控申请单，并在拓展信息里打上管控单的单号；明细就不用复制了，按照一个管控单生成一个通知的逻辑，管控和不管控单共用一个明细就行
                unPriceManageControlOrder = createNoControlOrderList(param, userDTO);
            }

            if (Objects.isNull(unPriceManageControlOrder)) {
                LOGGER.warn("batchOrSingleAddNotControlApply|不管控单信息为空");
                return;
            }

            //发送OA
            Date afterEffectDate = Objects.isNull(unPriceManageControlOrder.getScheduledTime()) ? null : Date.from(unPriceManageControlOrder.getScheduledTime().atZone(ZoneId.systemDefault()).toInstant());
            basePriceOrderService.submitOAAduit(unPriceManageControlOrder.getControlOrderId(), unPriceManageControlOrder.getControlOrderName(), afterEffectDate, unPriceManageControlOrder.getControlReason(),
                PriceOrderTypeEnum.CONTROL_NON_EXEC, PriceManageTypeEnum.NOT_APPLY, AuditStatusEnum.getEnum(unPriceManageControlOrder.getAuditStatus()), OaJumpUrlKeyEnum.CONTROL_NON_EXEC, userDTO, new Date(),null,null,null);

            //更改状态 PriceNoticeStatusEnum 不执行管控单号
            updatePriceControlNoticeStatus(unPriceManageControlOrder);
        } catch (Exception e) {
            LOGGER.warn("batchOrSingleAddNotControlApply|warn", e);
            throw e;
        }

    }

    /**
     * 通过管控单生成不管控单
     * @param param
     * @return
     */
    protected PriceManageControlOrder createNoControlOrderList(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO){
        if(CollectionUtils.isEmpty(param.getControlOrderCodeList())){
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        if(basePriceOrderService.containsEffectControlOrder(param.getControlOrderCodeList())){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_EFFECT);
        }

        List<String> ccontrolOrderNoList = param.getControlOrderCodeList();
        List<String> relationUnControlCodeStrList = priceManageControlOrderExtMapper.selectControlOrderNoListRelationUnControl(new ControlExtendParam(ccontrolOrderNoList));
        //查出来是逗号分割的管控单
        List<String> relationUnControlCodeList = Lists.newArrayList();
        relationUnControlCodeStrList.stream().forEach(v->{
            if(v.contains(",")){
                relationUnControlCodeList.addAll(Arrays.asList(v.split(",")));
            }else{
                relationUnControlCodeList.add(v);
            }
        });

        List<String> finalControlOrderCodeList = ccontrolOrderNoList.stream().filter(v->!relationUnControlCodeList.contains(v)).distinct().collect(Collectors.toList());

        if(CollectionUtils.isEmpty(finalControlOrderCodeList)){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UNCREATE_UNCONTROL);
        }
        PriceManageControlOrderExample controlOrderExample  = new PriceManageControlOrderExample();
        controlOrderExample.createCriteria().andControlOrderIdIn(finalControlOrderCodeList);
        List<PriceManageControlOrder> priceManageControlOrderList = priceManageControlOrderMapper.selectByExample(controlOrderExample);

        StringBuilder channelSb = new StringBuilder();
        StringBuilder priceTypeSb = new StringBuilder();
        StringBuilder controlOrderIdSb = new StringBuilder();
        priceManageControlOrderList.forEach(order->{
            if(org.apache.commons.lang.StringUtils.isNotBlank(order.getChannel())){
                channelSb.append(order.getChannel()).append(",");
            }
            if(org.apache.commons.lang.StringUtils.isNotBlank(order.getPriceType())){
                priceTypeSb.append(order.getPriceType()).append(",");
            }
            controlOrderIdSb.append(order.getControlOrderId()).append(",");
        });

        PriceManageControlOrder unPriceManageControlOrder = new PriceManageControlOrder();
        BeanUtils.copyProperties(priceManageControlOrderList.get(0), unPriceManageControlOrder);
        unPriceManageControlOrder.setExtend(ControlExtendVO.getJSONFormatStr(controlOrderIdSb.deleteCharAt(controlOrderIdSb.lastIndexOf(",")).toString(), org.apache.commons.lang.StringUtils.join(param.getIdList(), ",")));
        unPriceManageControlOrder.setControlOrderId(noSequenceService.priceNoSequence(null, NoSequenceTypeEnum.PRICECENTER_CONTROL));
        unPriceManageControlOrder.setChannel(channelSb.deleteCharAt(channelSb.lastIndexOf(",")).toString());
        unPriceManageControlOrder.setPriceType(priceTypeSb.deleteCharAt(priceTypeSb.lastIndexOf(",")).toString());
        unPriceManageControlOrder.setControlNoExecReason(param.getControlNoExecReason());
        unPriceManageControlOrder.setControlOrderName(param.getControlOrderName());
        unPriceManageControlOrder.setControlOrderType(ControlOrderTypeEnum.UNCONTROL_ORDER.getCode());
        unPriceManageControlOrder.setAuditStatus(AuditStatusEnum.UN_AUDIT.getCode());
        unPriceManageControlOrder.setCreatedBy(userDTO.getUserId());
        unPriceManageControlOrder.setCreatedByName(userDTO.getName());
        unPriceManageControlOrder.setUpdatedBy(userDTO.getUserId());
        unPriceManageControlOrder.setUpdatedByName(userDTO.getName());


        if(Objects.nonNull(unPriceManageControlOrder)){
            priceManageControlOrderExtMapper.batchInsert(com.beust.jcommander.internal.Lists.newArrayList(unPriceManageControlOrder));
        }
        return unPriceManageControlOrder;
    }

    /**
     * 编辑不执行管控单
     * @return
     */
    protected PriceManageControlOrder editNoControlOrderList(ControlNoticeAddAdjustParam param){
        PriceManageControlOrder unPriceManageControlOrder = priceManageControlOrderExtMapper.selectByControlOrderCode(param.getUnControlOrderCode());
        unPriceManageControlOrder.setControlNoExecReason(param.getControlNoExecReason());
        if(StringUtils.isNotBlank(param.getControlOrderName())){
            unPriceManageControlOrder.setControlOrderName(param.getControlOrderName());
        }
        unPriceManageControlOrder.setAuditStatus(AuditStatusEnum.UN_AUDIT.getCode());
        priceManageControlOrderMapper.updateByPrimaryKeySelective(unPriceManageControlOrder);
        return unPriceManageControlOrder;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitControlOrderToAuditById(Long controlOrderId, TokenUserDTO userDTO) {
        if (Objects.isNull(controlOrderId)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlOrderId);
        if (Objects.isNull(priceManageControlOrder)) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }

        try {
            Date afterEffectDate = Date.from(priceManageControlOrder.getScheduledTime().atZone(ZoneId.systemDefault()).toInstant());

            basePriceOrderService.submitOAAduit(priceManageControlOrder.getControlOrderId(), priceManageControlOrder.getControlOrderName(), afterEffectDate, priceManageControlOrder.getControlReason(),
                PriceOrderTypeEnum.CONTROL, PriceManageTypeEnum.FACTORY, AuditStatusEnum.UN_AUDIT, OaJumpUrlKeyEnum.CONTROL, userDTO, new Date(),null,null,null);

            priceManageControlOrder.setAuditStatus(AuditStatusEnum.UN_AUDIT.getCode());
            priceManageControlOrderMapper.updateByPrimaryKey(priceManageControlOrder);
        } catch (Exception e) {
            LOGGER.warn("submitControlOrderToAuditById|提交审核失败", e);
            throw e;
        }
    }

    @Override
    public void deleteGoodsDetail(ControlGoodsDelParam param) {
        if (Objects.isNull(param) || CollectionUtils.isEmpty(param.getGoodsCodeList()) || Objects.isNull(param.getControlOrderId())) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getControlOrderId());
        // || AuditStatusEnum.IN_PREPARATION.getCode() != priceManageControlOrder.getAuditStatus() || AuditStatusEnum.AUDIT_REJECTED.getCode() != priceManageControlOrder.getAuditStatus()
        if(priceManageControlOrder == null || AuditStatusEnum.IN_PREPARATION.getCode() != priceManageControlOrder.getAuditStatus()){
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_DELETE_FAIL);
        }
        PriceManageControlOrderDetailExample orderDetailExample = new PriceManageControlOrderDetailExample();
        orderDetailExample.createCriteria().andControlOrderCodeEqualTo(priceManageControlOrder.getControlOrderId()).andGoodsNoIn(param.getGoodsCodeList());
        priceManageControlOrderDetailMapper.deleteByExample(orderDetailExample);
    }

    @Override
    public void saveControlOrder(Long id, TokenUserDTO userDTO) {
        if (Objects.isNull(id)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        //单据当前状态必须是制单中 && 数据补全完毕 DataCompletionEnum  && 数据是否完整 DataFullEnum
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(id);
        if (Objects.isNull(priceManageControlOrder)) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }

        int notCompleted = priceManageControlOrderDetailExtMapper.countControlOrderDetailSupplementNotCompleted(priceManageControlOrder.getControlOrderId());
        if (notCompleted > 0) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, Constants.CONTROL_DATA_UNREADY);
        }

        //判断数据是否完整
        List<PriceManageControlOrderDetail> notFullOrderDetail = priceManageControlOrderDetailExtMapper.getToCheckOrderDetailListForDetailDataNotFull(priceManageControlOrder.getControlOrderId());
        if (CollectionUtils.isNotEmpty(notFullOrderDetail)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, getNotFullDetailErrorMessage(notFullOrderDetail));
        }

        if (AuditStatusEnum.IN_PREPARATION.getCode() != priceManageControlOrder.getAuditStatus()) {
            LOGGER.info("saveControlOrder|状态已经变了|重复保存");
            return;
        }
        basePriceOrderService.recordNewAuditLog(priceManageControlOrder.getControlOrderId(), PriceOrderTypeEnum.CONTROL, PriceManageTypeEnum.FACTORY, AuditStatusEnum.UN_SUBMITTED, userDTO, new Date());
        //制单中  更改位  待提交
        PriceManageControlOrderExample controlOrderExample = new PriceManageControlOrderExample();
        controlOrderExample.createCriteria().andIdEqualTo(id).andAuditStatusEqualTo(AuditStatusEnum.IN_PREPARATION.getCode());
        PriceManageControlOrder controlOrder = new PriceManageControlOrder();
        controlOrder.setAuditStatus(AuditStatusEnum.UN_SUBMITTED.getCode());
        priceManageControlOrderMapper.updateByExampleSelective(controlOrder, controlOrderExample);
    }

    /**
     *
     * @param notFullOrderDetailList
     * @return
     */
    private String getNotFullDetailErrorMessage(List<PriceManageControlOrderDetail> notFullOrderDetailList) {
        StringBuilder errorMessage = new StringBuilder();
        errorMessage.append(ReturnCodeEnum.ENCOMPELTE_PRICE.getMessage());
        errorMessage.append("没有填写的数据如下：\n");
        for (PriceManageControlOrderDetail orderDetail : notFullOrderDetailList) {
            errorMessage.append("商品编码: ");
            errorMessage.append(orderDetail.getGoodsNo());
            errorMessage.append(", 价格类型:");
            errorMessage.append(orderDetail.getPriceTypeName());
            errorMessage.append(";\n");
        }
        return errorMessage.toString();
    }

    @Override
    public boolean isControlOrderDetailImportCompleted(Long id, TokenUserDTO userDTO) {
        LOGGER.info("<===[PriceManageControlOrderServiceImpl.isControlOrderDetailImportCompleted] id: {}, userDTO: {}", id, userDTO);
        boolean result = false;
        if (id == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(id);
        if (Objects.isNull(priceManageControlOrder)) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }

        boolean isAllDataInsert = isDetailDataRowSuccess(priceManageControlOrder);

        if (isAllDataInsert) {
            int notCompleted = priceManageControlOrderDetailExtMapper.countControlOrderDetailSupplementNotCompleted(priceManageControlOrder.getControlOrderId());
            result = notCompleted > 0 ? false : true;
        }

        return result;
    }


    /**
     * 管控单商品明细个数是否正确，个数应该等于 渠道个数 * 价格类型个数 * 商品个数
     * @param priceManageControlOrder
     */
    private boolean isDetailDataRowSuccess(PriceManageControlOrder priceManageControlOrder) {
        PriceManageControlOrderDetailExample detailExample = new PriceManageControlOrderDetailExample();
        detailExample.createCriteria()
            .andControlOrderCodeEqualTo(priceManageControlOrder.getControlOrderId())
            .andStatusEqualTo(StatusEnum.NORMAL.getCode());
        long count = priceManageControlOrderDetailMapper.countByExample(detailExample);

        int priceTypeCount = Splitter.on(PriceConstant.VALUE_SEPARATOR).splitToList(priceManageControlOrder.getPriceType()).size();
        int channelCount = Splitter.on(PriceConstant.VALUE_SEPARATOR).splitToList(priceManageControlOrder.getChannel()).size();
        int goodsCount = priceManageControlOrderDetailExtMapper.countOfGoodsByControlOrderCode(priceManageControlOrder.getControlOrderId());
        boolean result = count == priceTypeCount * channelCount * goodsCount ? true : false;

        return result;
    }


    @Override
    public void editControlOrderDetail(AmisTableEditV2Param param, TokenUserDTO userDTO) {
        Date now = new Date();
        checkAmisTableEditParam(param);
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getControlOrderId());
        basePriceOrderService.checkControlOrderIsNullAndOwner(priceManageControlOrder, userDTO);

        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(priceManageControlOrder.getPriceType());
        List<Map<String, String>> rowDiffList = param.getRowsDiff();
        List<Map<String, Object>> rowList = param.getRows();
        for (int i = 0; i < rowDiffList.size(); i++) {
            editOneControlOrderDetail(rowDiffList.get(i), rowList.get(i), userDTO, now, priceManageControlOrder, priceTypeCodeList);
        }

    }

    @Override
    public void immediatelySaveAndEditControlOrderDetail(Map<String, Object> param, TokenUserDTO userDTO) {
        LOGGER.info("<===[PriceManageControlOrderServiceImpl.immediatelySaveAndEditControlOrderDetail] param:{}, userDTO: {}", param, userDTO);
        if (param == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        Date now = new Date();
        String controlOrderId = String.valueOf(param.get("controlOrderId"));
        if (controlOrderId == null) {
            LOGGER.info("<===[PriceManageControlOrderServiceImpl.immediatelySaveAndEditControlOrderDetail] 管控单号和商品编码都不能为空");
            throw new AmisBusinessException(ReturnCodeEnum.PARAM_ERROR);
        }
        Long controlOrderCode = Long.parseLong(controlOrderId);
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlOrderCode);
        basePriceOrderService.checkControlOrderIsNullAndOwner(priceManageControlOrder, userDTO);
        basePriceOrderService.checkControlOrderIsNullAndCanChange(priceManageControlOrder);
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(priceManageControlOrder.getPriceType());

        Map<String, String>  rowDiff = getAmisRowDiffFromMap(param, priceTypeCodeList);
        editOneControlOrderDetail(rowDiff, param, userDTO, now, priceManageControlOrder, priceTypeCodeList);
    }

    /**
     * 找出所有可能被编辑的字段的值
     * @param param
     */
    private Map<String, String> getAmisRowDiffFromMap(Map<String, Object> param, List<String> priceTypeCodeList) {
        Map<String, String> rowDiff = new HashMap<>();
        for (String priceTypeCode : priceTypeCodeList) {
            for (ControlOrderDetailPriceTypeColumnEnum priceColumnEnum : ControlOrderDetailPriceTypeColumnEnum.values()) {
                priceColumnEnum.setAttrMapValue().accept(rowDiff, param, priceTypeCode);
            }
        }
        for (ControlOrderDetailLastColumnEnum lastColumnEnum : ControlOrderDetailLastColumnEnum.values()) {
            lastColumnEnum.setAttrMapValue().accept(rowDiff,param);
        }
        return rowDiff;
    }

    /**
     * 校验编辑管控单明细参数
     * @param param
     */
    protected void checkAmisTableEditParam(AmisTableEditV2Param param) {
        if(Objects.isNull(param)){
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
    }

    /**
     * 编辑管控单明细
     * @param rowDiff
     * @param row
     * @param userDTO
     * @param now
     * @param priceManageControlOrder
     * @param priceTypeCodeList
     */
    private void editOneControlOrderDetail(Map<String, String> rowDiff, Map<String, Object> row, TokenUserDTO userDTO, Date now, PriceManageControlOrder priceManageControlOrder, List<String> priceTypeCodeList) {
        String goodsNo = String.valueOf(row.get(ControlOrderDetailBaseColumnEnum.COLUMN_1.getName()));
        String reason = rowDiff.get(ControlOrderDetailLastColumnEnum.COLUMN_1.getName());
        List<String> editPriceTypeCodeList = priceTypeCodeList;
        if (Objects.isNull(reason)) {
            editPriceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromAmisDiffRow(rowDiff.keySet());
        }
        for (String priceTypeCode : editPriceTypeCodeList) {
            updateControlOrderDetailByPriceCode(userDTO, now, priceManageControlOrder, rowDiff, row, goodsNo, reason, priceTypeCode);
        }
    }

    /**
     * 编辑入库操作
     * @param userDTO
     * @param now
     * @param priceManageControlOrder
     * @param rowDiff
     * @param goodsNo
     * @param reason
     * @param priceTypeCode
     */
    private void updateControlOrderDetailByPriceCode(TokenUserDTO userDTO, Date now, PriceManageControlOrder priceManageControlOrder, Map<String, String> rowDiff, Map<String, Object> row, String goodsNo, String reason, String priceTypeCode) {

        Optional<PriceManageControlOrderDetail> priceManageControlOrderDetail = getSingleControlOrderDetail(priceManageControlOrder.getControlOrderId(),
            goodsNo, priceTypeCode);
        if (!priceManageControlOrderDetail.isPresent()) {
            LOGGER.error("<===[PriceManageControlOrderServiceImpl.updateControlOrderDetailByPriceCode] controlOrderCode:{}, goodsNo: {}, priceTypeCode: {}明细数据不存在", priceManageControlOrder.getControlOrderId(),
                goodsNo, priceTypeCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, new StringBuilder().append(goodsNo).append(" 该商品的价格类型: ").append(priceTypeCode).append(" 的明细数据不存在").toString());
        }
        PriceManageControlOrderDetail priceManageControlOrderDetail1 = priceManageControlOrderDetail.get();
        ControlOrderDetailEditV2 controlOrderDetailEditV2 = new ControlOrderDetailEditV2();
        controlOrderDetailEditV2.setControlOrderCode(priceManageControlOrder.getControlOrderId());
        controlOrderDetailEditV2.setGoodsNo(goodsNo);
        controlOrderDetailEditV2.setPriceTypeCode(priceTypeCode);
        controlOrderDetailEditV2.setOriginalPrice(priceManageControlOrderDetail1.getOriginalPrice());
        Optional<ControlOrderExtendDTO> detailExtend1Optional = ControlOrderExtendDTO.getInstance(priceManageControlOrderDetail1.getExtend1());
        detailExtend1Optional.ifPresent(extend1 -> {
            extend1.setReason(org.apache.commons.lang.StringUtils.isNotBlank(reason) ? reason : "");
            extend1.setDataFull(DataFullEnum.HAS_FULL.getCode());
            controlOrderDetailEditV2.setExtend1(extend1);
            controlOrderDetailEditV2.setExtend1Str(ControlOrderExtendDTO.toJSONFormatStr(extend1));
        });
        controlOrderDetailEditV2.setGmtUpdate(LocalDateTime.now());
        controlOrderDetailEditV2.setUpdatedBy(userDTO.getUserId());
        controlOrderDetailEditV2.setUpdatedByName(userDTO.getName());

        //校验必填项
        String guidePriceKey = new StringBuilder().append(ControlOrderDetailPriceTypeColumnEnum.COLUMN_4.getName()).append("_").append(priceTypeCode).toString();
        String upperLimitKey = new StringBuilder().append(ControlOrderDetailPriceTypeColumnEnum.COLUMN_6.getName()).append("_").append(priceTypeCode).toString();
        String lowerLimitKey = new StringBuilder().append(ControlOrderDetailPriceTypeColumnEnum.COLUMN_8.getName()).append("_").append(priceTypeCode).toString();

        for (Map.Entry<String, String> rowDiffEntry : rowDiff.entrySet()) {
            if (Objects.isNull(rowDiffEntry.getValue())) {
                LOGGER.info("rowDiffEntry.getValue()  为空");
                continue;
            }
            Optional<ControlOrderDetailPriceTypeColumnEnum> goodPriceColumnEnumOptional = ControlOrderDetailPriceTypeColumnEnum.getByName(rowDiffEntry.getKey());
            Optional<ControlOrderDetailLastColumnEnum> lastColumnEnumOptional = ControlOrderDetailLastColumnEnum.getByName(rowDiffEntry.getKey());

            if (goodPriceColumnEnumOptional.isPresent() && (rowDiffEntry.getKey().equals(guidePriceKey) || rowDiffEntry.getKey().equals(upperLimitKey) || rowDiffEntry.getKey().equals(lowerLimitKey))) {
                goodPriceColumnEnumOptional.get().setAttrValue().accept(controlOrderDetailEditV2, rowDiffEntry.getValue());
            } else if (lastColumnEnumOptional.isPresent()) {
                lastColumnEnumOptional.get().setAttrValue().accept(controlOrderDetailEditV2, rowDiffEntry.getValue());
            }
        }

        if(CommonUtils.objWithNull(row.get(guidePriceKey)) && CommonUtils.objWithNull(row.get(upperLimitKey)) && CommonUtils.objWithNull(row.get(lowerLimitKey))){
            BigDecimal guidePrice = new BigDecimal(row.get(guidePriceKey).toString());
            BigDecimal upperLimitPrice = new BigDecimal(row.get(upperLimitKey).toString());
            BigDecimal lowerLimitPrice = new BigDecimal(row.get(lowerLimitKey).toString());
            if (upperLimitPrice.compareTo(lowerLimitPrice) < 0) {
                throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UPPER_SMALL_LOWER);
            }
            if (guidePrice.compareTo(upperLimitPrice) > 0 || guidePrice.compareTo(lowerLimitPrice) < 0) {
                throw new AmisBadRequestException(ReturnCodeEnum.ERROR_GUIDE_BETWEEN_UPPER_LOWER);
            }
        }

        if(Objects.isNull(controlOrderDetailEditV2.getGuidePrice()) || Objects.isNull(controlOrderDetailEditV2.getUpperLimit()) || Objects.isNull(controlOrderDetailEditV2.getLowerLimit())){
            ControlOrderExtendDTO extendDTO = controlOrderDetailEditV2.getExtend1();
            if(extendDTO != null){
                extendDTO.setDataFull(DataFullEnum.NOT_FULL.getCode());
                controlOrderDetailEditV2.setExtend1Str(ControlOrderExtendDTO.toJSONFormatStr(extendDTO));
            }
        }

        priceManageControlOrderDetailExtMapper.updateByAdjustCode(controlOrderDetailEditV2);
    }

    /**
     * 获取单个管控单明细
     * @param controlOrderCode
     * @param goodsNo
     * @param priceTypeCode
     * @return
     */
    private Optional<PriceManageControlOrderDetail> getSingleControlOrderDetail(String controlOrderCode, String goodsNo, String priceTypeCode) {
        Optional<PriceManageControlOrderDetail> result = Optional.empty();
        if (controlOrderCode != null && goodsNo != null && priceTypeCode != null) {
            ControlOrderDetailListV2Param param = new ControlOrderDetailListV2Param();
            param.setOffset(0);
            param.setSize(1);
            param.setControlOrderCode(controlOrderCode);
            param.setGoodsKeyword(goodsNo);
            param.setPriceTypeCodeList(com.google.common.collect.Lists.newArrayList(priceTypeCode));
            List<PriceManageControlOrderDetail> priceManageControlOrderDetailList = priceManageControlOrderDetailExtMapper.selectRemoveChannelAttOrderDetailV2ListPage(param);
            if (!org.springframework.util.CollectionUtils.isEmpty(priceManageControlOrderDetailList)) {
                result = Optional.of(priceManageControlOrderDetailList.get(0));
            }
        }
        return result;
    }


    @Override
    public void supplementControlOrderDetailData(ControlOrderDetailSupplementDataVO supplementDataVO) {
        LocalDateTime now = LocalDateTime.now();
        String controlOrderCode = supplementDataVO.getControlOrderCode();
        String goodsNo = supplementDataVO.getGoodsNo();
        String priceTypeCode = supplementDataVO.getPriceTypeCode();
        PriceManageControlOrder priceManageControlOrder = basePriceOrderService.getPriceManageControlOrderByCode(controlOrderCode);
        Optional<SpuListVO> goodsInfoOptional = searchExtService.getSpuInfo(goodsNo);
        //商品编码不存在，则删除明细,不处理这条消息
        if (!goodsInfoOptional.isPresent()) {
            deleteByControlCodeAndGoodsNoAndPriceType(controlOrderCode, goodsNo, priceTypeCode);
        } else {
            List<OrgLevelVO> orgLevelVOList = listOrgLevelVOListByControlCode(supplementDataVO.getControlOrderCode());
            List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(priceManageControlOrder.getChannel());
            ControlOrderDetailEditV2 detailEditV2 = getControlOrderDetailEditV2FromGoodInfo(supplementDataVO.getControlOrderCode(), goodsInfoOptional.get());
            Map<String, ControlOrderLimitDTO> controlOrderLimitDTOMap = getControlOrderManagePriceDTO(Arrays.asList(supplementDataVO), channelIdList, orgLevelVOList);
            LOGGER.info("supplementControlOrderDetailData|controlOrderLimitDTOMap:{}.", controlOrderLimitDTOMap);
            Optional<PriceModeResult> priceModeResultOptional = marketingExtService.getPriceModeByOrgLevelVO(goodsNo, orgLevelVOList);
            Optional<BigDecimal> priceModeOptional = basePriceOrderService.getPriceMode(goodsNo, priceTypeCode, orgLevelVOList, priceModeResultOptional, channelIdList);
            detailEditV2 = getControlOrderDetailEditV2FromManagePriceDTO(detailEditV2, priceTypeCode, controlOrderLimitDTOMap);
            detailEditV2.setPriceTypeCode(priceTypeCode);
            if (priceModeOptional.isPresent()) {
                detailEditV2.setOriginalPrice(BigDecimal.ZERO.compareTo(priceModeOptional.get()) == 0 ? null : priceModeOptional.get());
            }
            detailEditV2.setGmtUpdate(now);
            int dataFull=DataFullEnum.NOT_FULL.getCode();
            if (Optional.ofNullable(supplementDataVO.getDataFull()).orElse(Boolean.FALSE)) {
                dataFull = DataFullEnum.HAS_FULL.getCode();
            }
            if (priceModeResultOptional.isPresent()) {
                detailEditV2.setExtend1Str(ControlOrderExtendDTO.getJSONFormatStr(priceModeResultOptional.get().getModePriceCost(), ControlWayEnum.REAL_PRICE.getCode(), ControlSetWayEnum.FIXED_NUMBER.getCode(),
                    DataCompletionEnum.HAS_COMPLETED.getCode(),dataFull , detailEditV2.getParentUpperLimit(), detailEditV2.getParentLowerLimit()));
            } else {
                detailEditV2.setExtend1Str(ControlOrderExtendDTO.getJSONFormatStr(ControlWayEnum.REAL_PRICE.getCode(), ControlSetWayEnum.FIXED_NUMBER.getCode(), DataCompletionEnum.HAS_COMPLETED.getCode(), dataFull, detailEditV2.getParentUpperLimit(), detailEditV2.getParentLowerLimit()));
            }
            //todo @海峰 看下是否需要触发补全商品信息
            priceManageControlOrderDetailExtMapper.updateByAdjustCode(detailEditV2);
        }
    }

    /**
     * 通过管控单号查询组织详情
     * @param controlCode
     * @return
     */
    protected List<OrgLevelVO> listOrgLevelVOListByControlCode(String controlCode) {
        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        orgDetailExample.createCriteria().andControlOrderCodeEqualTo(controlCode);
        return priceManageControlOrgDetailMapper.selectByExample(orgDetailExample).stream()
            .map(orgDetail -> new OrgLevelVO(orgDetail.getOrgId(), orgDetail.getOrgLevel()))
            .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditBackUpdateStatusByCode(OaSource oaSource) {
        if (Objects.isNull(oaSource) || StringUtils.isBlank(oaSource.getOrderId()) || Objects.isNull(oaSource.getOrderStatus().getCode()) || Objects.isNull(oaSource.getOperateTime())) {
            LOGGER.warn("审核参数有误");
            return;
        }

        try {

            String controlOrderCode = oaSource.getOrderId();
            Integer auditStatus = oaSource.getOrderStatus().getCode();
            Date operateTime = oaSource.getOperateTime();
            LocalDateTime auditTime = operateTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            PriceManageControlOrderExample controlOrderExample = new PriceManageControlOrderExample();
            controlOrderExample.setLimit(1);
            controlOrderExample.createCriteria().andControlOrderIdEqualTo(controlOrderCode).andAuditStatusEqualTo(AuditStatusEnum.UN_AUDIT.getCode());
            List<PriceManageControlOrder> priceManageControlOrderList = priceManageControlOrderMapper.selectByExample(controlOrderExample);
            if (CollectionUtils.isEmpty(priceManageControlOrderList)) {
                LOGGER.warn("auditBackUpdateStatusByCode|审核已处理过|controlOrderCode：{}。auditStatus:{}.", controlOrderCode, auditStatus);
                return;
            }

            PriceManageControlOrder unPriceManageControlOrder = priceManageControlOrderList.stream().findFirst().get();
            if (Objects.isNull(unPriceManageControlOrder)) {
                LOGGER.warn("auditBackUpdateStatusByCode|管控单为空|controlOrderCode：{}。auditStatus:{}.", controlOrderCode, auditStatus);
                return;
            }

            PriceManageControlOrder priceManageControlOrder = new PriceManageControlOrder();
            priceManageControlOrder.setAuditStatus(auditStatus);
            priceManageControlOrder.setAuditBy(oaSource.getOperatorUserId());
            priceManageControlOrder.setAuditByName(oaSource.getOperatorUserName());
            priceManageControlOrder.setAuditDate(auditTime);
            if (Objects.equals(AuditStatusEnum.AUDIT_REJECTED.getCode(), auditStatus)) {
                priceManageControlOrder.setAuditReason(oaSource.getRejectReason());
            }

            boolean updateControlOrderDetail = false;
            LocalDateTime controlOrderAuditTime = null;
            if (ControlOrderTypeEnum.CONTROL_ORDER.getCode() == unPriceManageControlOrder.getControlOrderType()
                && Objects.equals(AuditStatusEnum.AUDIT_PASS.getCode(), auditStatus)) {
                controlOrderAuditTime = auditTime;
                LocalDateTime scheduleTime = unPriceManageControlOrder.getScheduledTime();
                Duration duration = Duration.between(auditTime, scheduleTime);
                //如果审核时间 < 预生效时间,则将预生效时间+30天为生效时间;否则生效时间+30天为生效时间
                //如果审核时间比执行时间小于等于3天 则将实际审核时间+30天
                if (duration.toDays() <= DEFAULT_GAP_DURATION_) {
                    priceManageControlOrder.setEffectTime(auditTime.plusDays(defaultAuditDuration));
                    String extend = priceManageControlOrder.getExtend();
                    JSONObject jsonObject1 = new JSONObject();
                    if (StringUtils.isNotBlank(extend)) {
                        jsonObject1 = JSONObject.parseObject(extend);
                    }
                    String auditTimeStr = DateUtils.dateToString(operateTime);
                    jsonObject1.put("auditDate", auditTimeStr);
                    jsonObject1.put("remark", "审核时间和执行时间中间差" + duration.toDays() + ",需要更新effect_time");
                    updateControlOrderDetail = true;
                } else {
                    priceManageControlOrder.setEffectTime(unPriceManageControlOrder.getScheduledTime().plusDays(defaultAuditDuration));
                }
            }

            priceManageControlOrderMapper.updateByExampleSelective(priceManageControlOrder, controlOrderExample);

            //更新审核时间到明细，后面计算会用到
            if (ControlOrderTypeEnum.CONTROL_ORDER.getCode() == unPriceManageControlOrder.getControlOrderType() && Objects.nonNull(controlOrderAuditTime)) {
                priceManageControlOrderDetailMapper.updateControlOrderDetailAuditDate(controlOrderCode, controlOrderAuditTime);
            }

            if (ControlOrderTypeEnum.CONTROL_ORDER.getCode() == unPriceManageControlOrder.getControlOrderType() && updateControlOrderDetail) {
                priceManageControlOrderDetailService.updateControlOrderDetailDxsDate(controlOrderCode,
                    priceManageControlOrder.getEffectTime());
            }

            //管控单审核通过之后生成管控通知门店明细
            if (ControlOrderTypeEnum.CONTROL_ORDER.getCode() == unPriceManageControlOrder.getControlOrderType()
                && Objects.equals(AuditStatusEnum.AUDIT_PASS.getCode(), auditStatus)) {
                // 管控单审核通过之后:
                // 1.生成管控通知(price_control_notice);
                // 2.生成管控门店商品明细(price_control_manage_store_detail);
                this.generateControlStoreDetail(controlOrderCode);
            }

            dealUnControlAuditResult(unPriceManageControlOrder);
        } catch (Exception e) {
            LOGGER.warn("auditBackUpdateStatusByCode|审核异常", e);
            throw e;
        }
    }


    /**
     * 不执行管控审核处理逻辑
     * @param unPriceManageControlOrder
     */
    private void dealUnControlAuditResult(PriceManageControlOrder unPriceManageControlOrder) {
        if (ControlOrderTypeEnum.UNCONTROL_ORDER.getCode() != unPriceManageControlOrder.getControlOrderType()) {
            LOGGER.info("dealUnControlAuditResult|非 不执行管控逻辑，不处理");
            return;
        }

        if(StringUtils.isBlank(unPriceManageControlOrder.getExtend())){
            LOGGER.info("dealUnControlAuditResult|该订单没有扩展信息："+unPriceManageControlOrder.getControlOrderId());
            return;
        }

        Optional<ControlExtendVO> optionalControlExtendVO = ControlExtendVO.getInstance(unPriceManageControlOrder.getExtend());
        if(!optionalControlExtendVO.isPresent()){
            LOGGER.info("dealUnControlAuditResult|该订单没有扩展信息："+unPriceManageControlOrder.getControlOrderId());
            return;
        }
        ControlExtendVO extendVO = optionalControlExtendVO.get();
        String controlOrderId = extendVO.getControlOrderCode();
        PriceManageControlOrder realControlOrder = priceManageControlOrderExtMapper.selectByControlOrderCode(controlOrderId);
        List<OptionDto> optionDtoList = permissionExtService.getGoodsStoreList(getControlOrgInfoByCode(controlOrderId));
        List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(realControlOrder.getChannel());
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(realControlOrder.getPriceType());
        List<String> goodsNoList = priceManageControlOrderDetailExtMapper.getGoodsNoListbyControlCode(controlOrderId);

        optionDtoList.stream().forEach(optionDto -> {
            Long storeId = Long.parseLong(optionDto.getValue());
            List<PriceStoreDetail> storePriceDetails = priceStoreDetailReadService.getPriceStoreDetailByParam(storeId, channelIdList, goodsNoList, priceTypeCodeList);
            if (CollectionUtils.isEmpty(storePriceDetails)) {
                return;
            }
            PriceStoreDetail priceStoreDetail = storePriceDetails.get(0);
            String extend1 = priceStoreDetail.getExtend1();
            if (org.apache.commons.lang.StringUtils.isNotBlank(extend1)) {
                JSONObject json = JSONObject.parseObject(extend1);
                //此商品是否存在不执行管控单id
                String noControlOrderId = json.getString("notExecControlOrderId");
                if (org.apache.commons.lang.StringUtils.isNotBlank(noControlOrderId)) {
                    return;
                }
            }

            JSONObject jsonObject1;
            if (org.apache.commons.lang.StringUtils.isEmpty(extend1)) {
                jsonObject1 = new JSONObject();
            } else {
                jsonObject1 = JSONObject.parseObject(extend1);
            }

            jsonObject1.put("notExecControlOrderId", unPriceManageControlOrder.getId());
            priceStoreDetail.setExtend1(jsonObject1.toJSONString());
            priceStoreDetailMapper.updateByPrimaryKey(priceStoreDetail);
        });


    }

    /**
     * 获取单据里的组织信息通过编码
     * @param controlOrderId
     * @return
     */
    protected List<OrgLevelVO> getControlOrgInfoByCode(String controlOrderId){
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderExtMapper.selectByControlOrderCode(controlOrderId);
        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        orgDetailExample.createCriteria().andControlOrderCodeEqualTo(priceManageControlOrder.getControlOrderId());
        List<PriceManageControlOrgDetail> orgDetailList = controlOrgDetailMapper.selectByExample(orgDetailExample);
        if(CollectionUtils.isEmpty(orgDetailList)){
            return com.google.common.collect.Lists.newArrayList();
        }

        return orgDetailList.stream().map(v->{
            OrgLevelVO orgLevelVO = new OrgLevelVO();
            orgLevelVO.setOrgId(v.getOrgId());
            orgLevelVO.setOrgLevel(v.getOrgLevel());
            orgLevelVO.setOrgName(v.getOrgName());
            orgLevelVO.setName(v.getOrgName());
            return orgLevelVO;
        }).collect(Collectors.toList());
    }

    @Override
    public void editNotControlOrder(ControlNotOrderEditParam param) {
        PriceManageControlOrderExample orderExample = new PriceManageControlOrderExample();
        orderExample.createCriteria().andControlOrderIdEqualTo(param.getControlNoOrderCode())
            .andControlOrderTypeEqualTo(ControlOrderTypeEnum.UNCONTROL_ORDER.getCode())
            .andAuditStatusEqualTo(AuditStatusEnum.AUDIT_REJECTED.getCode());

        PriceManageControlOrder priceManageControlOrder = new PriceManageControlOrder();
        priceManageControlOrder.setControlNoExecReason(param.getControlNoExecReason());
        priceManageControlOrderMapper.updateByExampleSelective(priceManageControlOrder, orderExample);
    }

    /**
     * 根据管控单价格DTO获取调价单编辑对象
     * @param detailEditV2
     * @param controlOrderLimitDTOMap
     * @return
     */
    private ControlOrderDetailEditV2 getControlOrderDetailEditV2FromManagePriceDTO(ControlOrderDetailEditV2 detailEditV2, String priceTypeCode, Map<String, ControlOrderLimitDTO> controlOrderLimitDTOMap) {

        String uniqueGoodsNo = new StringBuilder().append(detailEditV2.getGoodsNo()).append(Constants.COMMA).append(priceTypeCode).toString();
        ControlOrderLimitDTO controlOrderLimitDTO = controlOrderLimitDTOMap.get(uniqueGoodsNo);
        if (controlOrderLimitDTO != null) {
            detailEditV2.setParentUpperLimit(controlOrderLimitDTO.getUpperLimit());
            detailEditV2.setParentLowerLimit(controlOrderLimitDTO.getLowerLimit());
        }
        return detailEditV2;
    }

    /**
     * 获取最高最低限价
     * @param supplementDataVO
     * @param channelIdList
     * @param orgLevelVOList
     * @return
     */
    private Map<String, ControlOrderLimitDTO> getControlOrderManagePriceDTO(List<ControlOrderDetailSupplementDataVO> supplementDataVOList, List<Integer> channelIdList, List<OrgLevelVO> orgLevelVOList) {
        List<ControlOrderQueryParam> queryParamList = Lists.newLinkedList();
        for (ControlOrderDetailSupplementDataVO supplementDataVO : supplementDataVOList) {
        	for (OrgLevelVO orgDetail : orgLevelVOList) {
                ControlOrderQueryParam controlOrderQueryParam = new ControlOrderQueryParam();
                controlOrderQueryParam.setGoodsNo(supplementDataVO.getGoodsNo());
                controlOrderQueryParam.setPriceTypeCode(supplementDataVO.getPriceTypeCode());
                controlOrderQueryParam.setOrgId(orgDetail.getOrgId());
                controlOrderQueryParam.setChannelIdList(channelIdList);
                queryParamList.add(controlOrderQueryParam);
            }
		}
        List<ControlOrderLimitDTO> currentControlLimitOfGoodsNo = priceManageControlOrderReadService.findCurrentControlLimitOfGoodsNo(queryParamList);

        if (CollectionUtils.isEmpty(currentControlLimitOfGoodsNo)) {
            return new HashMap<>();
        }
        return currentControlLimitOfGoodsNo.stream().collect(Collectors.toMap(ControlOrderLimitDTO::getUniqueGoodsNo, Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 根据ES的商品信息查询结果获取调价单编辑对象
     * @param controlOrderCode
     * @param goodsInfo
     * @return
     */
    private ControlOrderDetailEditV2 getControlOrderDetailEditV2FromGoodInfo(String controlOrderCode, SpuListVO goodsInfo) {
        ControlOrderDetailEditV2 detailEditV2 = new ControlOrderDetailEditV2();
        detailEditV2.setControlOrderCode(controlOrderCode);
        detailEditV2.setSpuId(goodsInfo.getId());
        detailEditV2.setGoodsNo(goodsInfo.getGoodsNo());
        detailEditV2.setCurName(goodsInfo.getCurName());
        detailEditV2.setManufacturer(goodsInfo.getFactoryid());
        detailEditV2.setJhiSpecification(goodsInfo.getJhiSpecification());
        detailEditV2.setDosage(goodsInfo.getDosageForms());
        detailEditV2.setProdarea(goodsInfo.getProdarea());
        detailEditV2.setGoodsName(goodsInfo.getName());
        detailEditV2.setGoodsUnit(goodsInfo.getGoodsunit());
        return detailEditV2;
    }

    private void deleteByControlCodeAndGoodsNoAndPriceType(String controlOrderCode, String goodsNo, String priceTypeCode) {
        if (StringUtils.isBlank(controlOrderCode) || StringUtils.isBlank(goodsNo) || StringUtils.isBlank(priceTypeCode)) {
            throw new AmisBusinessException(ReturnCodeEnum.PARAM_ERROR);
        }
        priceManageControlOrderDetailExtMapper.deleteByControlCodeAndGoodsNoAndPriceType(controlOrderCode, goodsNo, priceTypeCode);
    }

    @Override
    public void autoBatchOrSingleAddAdjustPriceOrder(List<PriceControlNotice> param) {
        LOGGER.info("autoBatchOrSingleAddAdjustPriceOrder|接收参数{}：", param.size());
        if (CollectionUtils.isEmpty(param)) {
            LOGGER.info("autoBatchOrSingleAddAdjustPriceOrder|接收参数：通知单明细为空!");
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_DETAIL_NULL);
        }
        List<PriceControlNotice> collect = param.stream().filter(PriceControlNotice -> PriceControlNotice.getPrice().compareTo(PriceControlNotice.getLowerLimit()) <= 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            LOGGER.info("autoBatchOrSingleAddAdjustPriceOrder|接收参数：没有符合条件的数据，无法生成调价单!");
        }

        Map<String, List<PriceControlNotice>> baseInfoMap = collect.stream().collect(Collectors.groupingBy(v -> new StringBuilder().append(v.getPriceTypeCode()).append(Constants.LINE).append(v.getChannelId()).append(Constants.LINE).append(v.getDxsDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).toString()));
        boolean isPortal = RequestHeaderContextUtils.checkPortalWeb();
        List<Long> idList = Lists.newArrayList();
        baseInfoMap.forEach((baseKey, notices) -> {

        	//把相同商品编码的门店id汇总 设置调价单明细机构
    		Map<String, List<Long>> goodsNoMapperStoreIdsMap = notices.stream().collect(Collectors.
            		groupingBy(PriceControlNotice::getGoodsNo,Collectors.mapping(PriceControlNotice::getStoreId, Collectors.toList())));

    		//把相同商品编码的门店id汇总 设置调价单明细机构
    		Map<String, List<BigDecimal>> goodsNoMapperLowerLimitMap = notices.stream().collect(Collectors.
            		groupingBy(PriceControlNotice::getGoodsNo,Collectors.mapping(PriceControlNotice::getLowerLimit, Collectors.toList())));

            List<Long> storeIdList = notices.stream().map(v -> v.getStoreId()).collect(Collectors.toList());
            Long goodsCount = notices.stream().map(v -> v.getGoodsNo()).count();
            List<String> goodsNoList = notices.stream().filter(v -> StringUtils.isNotBlank(v.getGoodsNo())).map(v -> v.getGoodsNo()).distinct().collect(Collectors.toList());
            //信息合并
            PriceControlNotice controlNotice = notices.stream().findFirst().get();
            controlNotice.setPriceTypeCodeJoin(controlNotice.getPriceTypeCode());
            idList.addAll(notices.stream().map(v -> v.getId()).collect(Collectors.toList()));
            TokenUserDTO tokenUserDTO = new TokenUserDTO();

            AdjustPriceOrderV2Param adjustPriceOrderV2ParamByControlOrderDetail = createAdjustPriceOrderV2ParamByControlOrderDetail(controlNotice,storeIdList, tokenUserDTO, goodsCount);

            long adjustPriceOrderId = adjustPriceOrderV2Service.addAdjustPriceOrder(adjustPriceOrderV2ParamByControlOrderDetail, tokenUserDTO);

            AdjustPriceOrderDetailAddV2Param adjustPriceOrderDetailAddV2Param = new AdjustPriceOrderDetailAddV2Param();
            adjustPriceOrderDetailAddV2Param.setAdjustDetailReason(Constants.AUTO_ADJUST_REASON);//原因必填
            adjustPriceOrderDetailAddV2Param.setAdjustPriceOrderId(adjustPriceOrderId);
            adjustPriceOrderDetailAddV2Param.setGoodsNoList(goodsNoList);
            adjustPriceOrderDetailAddV2Param.setOrgTabType(PriceOrderTabTypeEnum.STORE_LIST.getType());
            adjustPriceOrderDetailAddV2Param.setOrgIdList(permissionExtService.listOrgIdByOutId(storeIdList, OrgTypeEnum.STORE.getCode()));
            adjustPriceOrderDetailAddV2Param.setPrice(controlNotice.getLowerLimit());
            adjustPriceOrderDetailAddV2Param.setGoodsNoMapperStoreIdsMap(goodsNoMapperStoreIdsMap);
            adjustPriceOrderDetailAddV2Param.setGoodsNoMapperLowerLimitMap(goodsNoMapperLowerLimitMap);
            adjustPriceOrderDetailV2Service.addAdjustPriceOrderDetails(adjustPriceOrderDetailAddV2Param, tokenUserDTO, CreateAdjustDataSourceEnum.CONTROL_NOTICE_ADD.getCode());

            //根据调价单id 明细表dataFull=1、limitStatus=0
            AdjustPriceOrder order = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
            if(null==order) {
            	throw new AmisBadRequestException(ReturnCodeEnum.SYSTEM_ERROR);
            }
            adjustPriceOrderDetailV2Service.updateAdjustPriceOrderDetailLimitStatus(order.getAdjustCode(), GoodsLimitStatusEnum.NONE.getCode(),DataFullEnum.HAS_FULL.getCode());

            updateOnlyPriceNoticeStatus(idList);

            adjustPriceOrderV2Service.submitAdjustPriceOrderToAuditById(adjustPriceOrderId,isPortal, null, null, tokenUserDTO);
        });
    }
    /**
     * 组织调价单表头参数
     * @return
     */
    protected AdjustPriceOrderV2Param createAdjustPriceOrderV2ParamByControlOrderDetail(PriceControlNotice priceControlNotice, List<Long> storeIdList,TokenUserDTO tokenUserDTO,Long goodsCount) {

        AdjustPriceOrderV2Param adjustPriceOrderV2Param = new AdjustPriceOrderV2Param();

        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderExtMapper.selectByControlOrderCode(priceControlNotice.getControlOrderId());
        tokenUserDTO.setUserId(priceManageControlOrder.getCreatedBy());
        tokenUserDTO.setName(priceManageControlOrder.getCreatedByName());
         adjustPriceOrderV2Param.setAdjustName(getIncrNum(null,tokenUserDTO));
        List<OrgLevelVO> orgLevelVOS = listOrgLevelVOListByControlCode(priceControlNotice.getControlOrderId());
        if (CollectionUtils.isNotEmpty(orgLevelVOS)){
            adjustPriceOrderV2Param.setUserOrgId(orgLevelVOS.get(0).getOrgId());
        }
		List<String> priceTypeCodeList = Arrays.asList(priceControlNotice.getPriceTypeCodeJoin().split(",")).stream().map(s -> s.trim()).collect(Collectors.toList());

        adjustPriceOrderV2Param.setOrgIdList(permissionExtService.listOrgIdByOutId(storeIdList, OrgTypeEnum.STORE.getCode()));
        adjustPriceOrderV2Param.setPriceTypeCodeList(priceTypeCodeList);
        adjustPriceOrderV2Param.setChannelIdList(com.google.common.collect.Lists.newArrayList(priceControlNotice.getChannelId()));
        adjustPriceOrderV2Param.setChannelIds(Objects.nonNull(priceControlNotice.getChannelId())?priceControlNotice.getChannelId().toString():"");
        adjustPriceOrderV2Param.setPriceTypeCodes(priceControlNotice.getPriceTypeCode());
        adjustPriceOrderV2Param.setGoodsScope(GoodsScopeEnum.ALL_GOODS_SCOPE.getCode());
        adjustPriceOrderV2Param.setAdjustType(AdjustTypeEnum.SUPER_CONTROL.getCode());
        adjustPriceOrderV2Param.setAdjustReason(Constants.AUTO_ADJUST_REASON);
        adjustPriceOrderV2Param.setScheduledTime(Objects.isNull(priceControlNotice.getDxsDate())?null:Date.from(priceControlNotice.getDxsDate().atZone(ZoneId.systemDefault()).toInstant()));
        adjustPriceOrderV2Param.setScheduledTimeSecondLong(Objects.isNull(priceControlNotice.getDxsDate())?"":String.valueOf(priceControlNotice.getDxsDate().toEpochSecond(ZoneOffset.of("+8"))));
        adjustPriceOrderV2Param.setStep(PricePageStepEnum.STEP_12.getStep());
        adjustPriceOrderV2Param.setExecStatus(ExecEnum.EXEC_PLAN.getCode());
        adjustPriceOrderV2Param.setOrgTabType(PriceOrderTabTypeEnum.STORE_LIST.getType());
        adjustPriceOrderV2Param.setTabTypeValList(com.beust.jcommander.internal.Lists.newArrayList(1));
        adjustPriceOrderV2Param.setAuditStatus((byte)AuditStatusEnum.UN_SUBMITTED.getCode());
        adjustPriceOrderV2Param.setGoodsCount(goodsCount.intValue());
        adjustPriceOrderV2Param.setIsAutoCreatePriceAdjust(1);
        return adjustPriceOrderV2Param;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importControlOrderDetails(ControlOrderImportV2Param param, TokenUserDTO userDTO) {
    	String controlOrderCode = "";
        try {
        	//校验
            checkImportAdjustPriceOrderDetails(param);
            PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getControlOrderId());
            if(null==priceManageControlOrder) {
            	throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
            }
            checkGoodsNo(param);
            controlOrderCode = priceManageControlOrder.getControlOrderId();
            List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(priceManageControlOrder.getPriceType());
            checkImportPriceTypeCode(priceTypeCodeList,param);
            clearControlOrderImportResultRedisKey(priceManageControlOrder.getControlOrderId());
            basePriceOrderService.checkControlOrderIsNullAndCanChange(priceManageControlOrder);
        	checkParentUpperLower(param);
            transactionTemplate.execute(ts -> {
            	deleteExistedOrderDetailRecord(param, priceManageControlOrder);
                List<PriceManageControlOrderDetail> manageControlOrderDetails = batchAddControlOrderDetailsForImport(param, userDTO, priceManageControlOrder);
                batchInsertAndAsyncCompleteData(manageControlOrderDetails);
    			return null;
    		});
            sendImportDataResult(param, priceManageControlOrder);
        } catch (Exception e) {
        	LOGGER.error("importControlOrderDetails|管控单导入商品明细失败|", e);
        	String controlOrderImportResultRedisKey = getControlOrderImportResultRedisKey(controlOrderCode);
			importDataResultService.sendImportResult(controlOrderImportResultRedisKey, 0, 0, 0, ImportStatusEnum.IMPORT_FAIL.getCode());
			throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CHECK_IMPORT);
        }
    }

    /**
     * 校验调价单明细导入参数，有问题，抛出异常
     * @param param
     */
    private void checkImportAdjustPriceOrderDetails(ControlOrderImportV2Param param) {
        LOGGER.info("checkImportAdjustPriceOrderDetails {}",param.getControlOrderId());
        if (param == null|| null==param.getControlOrderId()) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }else {
            if (CollectionUtils.isEmpty(param.getControlOrderDetailDTOList())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "导入商品数不能为空");
            }
            if (param.getControlOrderDetailDTOList().size() > maxExcelImportSize) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "导入商品数不能超过" + maxExcelImportSize);
            }
            param.getControlOrderDetailDTOList().stream().forEach(v->{
                if (StringUtils.isBlank(v.getGoodsNo())){
                    v.setResult(Boolean.FALSE);
                    v.setMessage("商品编码不能为空");
                }
            });
            //检测重复商品编码(导入取最后一条)并打标其他条为导入失败
            List<String> duplicateGoodsNos = param.getControlOrderDetailDTOList().stream().filter(v -> !Boolean.FALSE.equals(v.getResult())).collect(Collectors.groupingBy(ControlOrderDetailDTO::getGoodsNo, Collectors.counting()))
                .entrySet().stream().filter(e -> e.getValue() > 1)
                .map(Map.Entry::getKey).collect(Collectors.toList());
            List<ControlOrderDetailDTO> importAdjustPriceOrderDetails = param.getControlOrderDetailDTOList().stream().filter(v -> duplicateGoodsNos.contains(v.getGoodsNo())).sorted(Comparator.comparing(ControlOrderDetailDTO::getLineNo).reversed()).collect(Collectors.toList());
            Set<String> checkedGoodsNos=new HashSet<>();
            for (ControlOrderDetailDTO detailDTO : importAdjustPriceOrderDetails) {
                if (!checkedGoodsNos.contains(detailDTO.getGoodsNo())){
                    checkedGoodsNos.add(detailDTO.getGoodsNo());
                }else{
                    detailDTO.setResult(Boolean.FALSE);
                    detailDTO.setMessage("重复的商品编码");
                }
            }
        }
    }

    /**
     * 新导入数据覆盖 已存在历史记录
     * @param controlOrderImportV2Param
     * @param priceManageControlOrder
     */
    private void deleteExistedOrderDetailRecord(ControlOrderImportV2Param controlOrderImportV2Param, PriceManageControlOrder priceManageControlOrder) {
        List<String> hasAddGoodsNoList = getGoodsNoListbyControlCode(priceManageControlOrder.getControlOrderId());
        List<String> removeGoodsNoList = controlOrderImportV2Param.getControlOrderDetailDTOList().stream()
            .filter(v->!Boolean.FALSE.equals(v.getResult())&& hasAddGoodsNoList.contains(v.getGoodsNo()))
            .map(ControlOrderDetailDTO::getGoodsNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(removeGoodsNoList)){
            LOGGER.info("清除历史数据removeGoodsNoList={}",removeGoodsNoList);
            Lists.partition(removeGoodsNoList,200).forEach(v ->{
                PriceManageControlOrderDetailExample example=new PriceManageControlOrderDetailExample();
                example.createCriteria().andControlOrderCodeEqualTo(priceManageControlOrder.getControlOrderId()).andGoodsNoIn(v);
                priceManageControlOrderDetailMapper.deleteByExample(example);
            });

        }
    }

    /**
     * 批量导入管控单明细
     * @param importControlOrder
     * @param userDTO
     * @param priceManageControlOrder
     */
    private  List<PriceManageControlOrderDetail>  batchAddControlOrderDetailsForImport(ControlOrderImportV2Param importControlOrder, TokenUserDTO userDTO,PriceManageControlOrder priceManageControlOrder ) {
        List<String> toAddGoodsNoList = importControlOrder.getControlOrderDetailDTOList().stream()
            .filter(v -> !Boolean.FALSE.equals(v.getResult())).map(ControlOrderDetailDTO::getGoodsNo).collect(Collectors.toList());
        ControlOrderGoodsParam param=new ControlOrderGoodsParam();
        param.setGoodsCodeList(toAddGoodsNoList);
        param.setControlOrderCode(priceManageControlOrder.getControlOrderId());
        param.setId(importControlOrder.getControlOrderId());
        List<PriceManageControlOrderDetail> priceManageControlOrderDetails = batchControlOrderGoodsAdd(param, userDTO);
        setWriteableDataForInsert(priceManageControlOrderDetails,importControlOrder);
        return priceManageControlOrderDetails;
    }

    /**
     *
     * @Title: batchInsertAndAsyncCompleteData
     * @Description: 保存数据并异步不全数据
     * @param: @param controlOrderDetailList
     * @return: void
     * @throws
     */
    private void batchInsertAndAsyncCompleteData(List<PriceManageControlOrderDetail> controlOrderDetailList) {
        for (List<PriceManageControlOrderDetail> controlOrderDetails : Lists.partition(controlOrderDetailList, 500)) {
            priceManageControlOrderDetailExtMapper.batchInsert(controlOrderDetails);
        }
        //异步补全
        controlOrderDetailList.stream().map(controlOrderDetail -> {
            ControlOrderDetailSupplementDataVO supplementDataVO = new ControlOrderDetailSupplementDataVO();
            supplementDataVO.setControlOrderCode(controlOrderDetail.getControlOrderCode());
            supplementDataVO.setPriceTypeCode(controlOrderDetail.getPriceTypeCode());
            supplementDataVO.setGoodsNo(controlOrderDetail.getGoodsNo());
            if(Optional.ofNullable(controlOrderDetail.getGuidePrice()).isPresent()
                &&Optional.ofNullable(controlOrderDetail.getUpperLimit()).isPresent()
                &&Optional.ofNullable(controlOrderDetail.getLowerLimit()).isPresent()){
                supplementDataVO.setDataFull(Boolean.TRUE);
            };
            return supplementDataVO;
        }).distinct().forEach(supplementDataVO -> supplementDataProducer.sendMq(supplementDataVO));
    }

    /**
     * 导入数据 可编辑字段赋值
     * @param priceManageControlOrderDetails
     * @param importControlOrder
     */
    private void setWriteableDataForInsert(List<PriceManageControlOrderDetail> priceManageControlOrderDetails, ControlOrderImportV2Param importControlOrder) {
        if (CollectionUtils.isNotEmpty(priceManageControlOrderDetails)){
            Map<String, ControlOrderDetailDTO> detailDTOMap = importControlOrder.getControlOrderDetailDTOList().stream()
                .filter(v -> !Boolean.FALSE.equals(v.getResult())).collect(Collectors.toMap(ControlOrderDetailDTO::getGoodsNo, Function.identity(), (key1, key2) -> key2));
            priceManageControlOrderDetails.stream().forEach(v->{
                ControlOrderDetailDTO detailDTO = detailDTOMap.get(v.getGoodsNo());
                if (Objects.nonNull(detailDTO)&&CollectionUtils.isNotEmpty(detailDTO.getControlOrderDetailOptionsDTOList())){
                    detailDTO.getControlOrderDetailOptionsDTOList().forEach(t->{
                        if (t.getPriceType().equals(v.getPriceTypeCode())){
                            v.setGuidePrice(t.getGuidePrice());
                            v.setUpperLimit(t.getUpperLimit());
                            v.setLowerLimit(t.getLowerLimit());
                            // 前台静态毛利率（*后期建设）、实价(默认)
                            v.setExtend1(ControlOrderExtendDTO.getJSONFormatStr(ControlWayEnum.REAL_PRICE.getCode(), ControlSetWayEnum.FIXED_NUMBER.getCode(),DataCompletionEnum.NOT_COMPLETE.getCode(), DataFullEnum.HAS_FULL.getCode()));
                        }
                    });
                }
            });
        }
    }

    /**
     * 校验导入数: 是否缺失价格类型和最高/低限价
     * @param priceTypeCodeList
     * @param importControlOrder
     */
    private void checkImportPriceTypeCode(List<String> priceTypeCodeList, ControlOrderImportV2Param importControlOrder) {
        importControlOrder.getControlOrderDetailDTOList().stream().filter(v->!Boolean.FALSE.equals(v.getResult())).forEach(v->{
            if(CollectionUtils.isEmpty(v.getControlOrderDetailOptionsDTOList())){
                v.setResult(Boolean.FALSE);
                v.setMessage("价格信息不能为空");
            }
        });
    }

    /**
     *
     * @Title: clearControlOrderImportResultRedisKey
     * @Description: 清理历史导入结果
     * @param: @param adjustCode
     * @return: void
     * @throws
     */
    private void clearControlOrderImportResultRedisKey(String controlOrder) {
    	String controlOrderImportResultRedisKey = getControlOrderImportResultRedisKey(controlOrder);
    	RBucket<AdjustImportResultDTO> bucket = redissonClient.getBucket(controlOrderImportResultRedisKey);
    	if(bucket.isExists()) {
    		bucket.delete();
    	}
    }

    /**
    *
    * @Title: sendImportDataResult
    * @Description: 发送导入结果
    * @param: @param importAdjustPriceOrder
    * @param: @param adjustPriceOrder
    * @return: void
    * @throws
    */
   private void sendImportDataResult(ControlOrderImportV2Param param,PriceManageControlOrder controlOrder) {
	   	List<ControlOrderDetailDTO> importList = param.getControlOrderDetailDTOList();
	   	List<ControlOrderDetailDTO> importFailList = importList.stream().filter(v -> Boolean.FALSE.equals(v.getResult())).collect(Collectors.toList());
	   	if(CollectionUtils.isNotEmpty(importFailList)) {
	   		List<PriceManageControlOrderDetail> controlOrderDetailList = createControlOrderDetailList(controlOrder, importFailList);
	   		LinkedHashMap<String, String> fieldMap = priceManageControlOrderReadService.getControlOrderDetailDownloadFieldMap(controlOrder.getPriceType());
	   		fieldMap.put("errorMsg", "错误原因");
	   		List<Map<String, Object>> listData = getControlOrderDetailListResults(controlOrder, controlOrderDetailList, false);
	   		//组装异常数据
	        String fileName = "管控单入异常_"+controlOrder.getControlOrderId();
	        //结果
			String dataKey = getControlOrderImportErrorDataRedisKey(controlOrder.getControlOrderId());
			//结构
			String structureKey = getControlOrderImportStructureRedisKey(controlOrder.getControlOrderId());
			//上传路径
			String uploadUrl = AsyncExportActionEnum.PRICE_CONTROL_ORDER_DETAIL.getUploadUrl();
			ImportErrorDataDTO importErrorData = new ImportErrorDataDTO(fileName,dataKey,structureKey,uploadUrl,fieldMap,listData);
	   		importDataResultService.sendImportErrorData(importErrorData);
	   	}
	   	int total = importList.size();
	   	int failCount = importFailList.size();
	   	int successCount = total - failCount;
	   	String controlOrderImportResultRedisKey = getControlOrderImportResultRedisKey(controlOrder.getControlOrderId());
	   	importDataResultService.sendImportResult(controlOrderImportResultRedisKey, total, successCount, failCount, ImportStatusEnum.IMPORT_FINISH.getCode());
   }

    /**
     *
     * @Title: getControlOrderImportResultRedisKey
     * @Description: 获取管控单导入结果key
     * @param: @param controlOrderCode
     * @param: @return
     * @return: String
     * @throws
     */
    private String getControlOrderImportResultRedisKey(String controlOrderCode) {
    	return RedisKeysConstant.CONTROL_ORDER_IMPORT_RESULT+controlOrderCode;
    }

    /**
     *
     * @Title: getControlOrderImportErrorDataRedisKey
     * @Description: 获取管控单导入异常数据key
     * @param: @param controlOrderCode
     * @param: @return
     * @return: String
     * @throws
     */
    private String getControlOrderImportErrorDataRedisKey(String controlOrderCode) {
    	return RedisKeysConstant.CONTROL_ORDER_IMPORT_EXCEPTION_RESULT_+controlOrderCode;
    }

    /**
     *
     * @Title: getControlOrderImportResultRedisKey
     * @Description: 获取管控单导入数据结构key
     * @param: @param controlOrderCode
     * @param: @return
     * @return: String
     * @throws
     */
    private String getControlOrderImportStructureRedisKey(String controlOrderCode) {
    	return RedisKeysConstant.CONTROL_ORDER_IMPORT_RESULT_STRUCTURE_+controlOrderCode;
    }

    /**
     *
     * @Title: createControlOrderDetailList
     * @Description: 封装导出数据
     * @param: @param controlOrder
     * @param: @param importFailList
     * @param: @return
     * @return: List<PriceManageControlOrderDetail>
     * @throws
     */
    private List<PriceManageControlOrderDetail> createControlOrderDetailList(PriceManageControlOrder controlOrder,List<ControlOrderDetailDTO> importFailList){
    	List<PriceManageControlOrderDetail> controlOrderDetailList = Lists.newArrayList();
    	PriceManageControlOrderDetail controlOrderDetail = null;
    	for (ControlOrderDetailDTO detailDto : importFailList) {
    		List<ControlOrderDetailOptionsDTO> detailOptionsList = detailDto.getControlOrderDetailOptionsDTOList();
    		for (ControlOrderDetailOptionsDTO detailOptionsDto : detailOptionsList) {
    			controlOrderDetail = new PriceManageControlOrderDetail();
    			controlOrderDetail.setControlOrderCode(controlOrder.getControlOrderId());
        		controlOrderDetail.setGoodsNo(detailDto.getGoodsNo());
        		controlOrderDetail.setGoodsName(detailDto.getGoodsName());
        		controlOrderDetail.setCurName(detailDto.getCurName());
        		controlOrderDetail.setManufacturer(detailDto.getManufacturer());
        		controlOrderDetail.setJhiSpecification(detailDto.getJhiSpecification());
        		controlOrderDetail.setProdarea(detailDto.getProdarea());
        		controlOrderDetail.setGoodsUnit(detailDto.getGoodsUnit());
        		controlOrderDetail.setPriceTypeCode(detailOptionsDto.getPriceType());
        		controlOrderDetail.setGuidePrice(detailOptionsDto.getGuidePrice());
        		controlOrderDetail.setUpperLimit(detailOptionsDto.getUpperLimit());
        		controlOrderDetail.setLowerLimit(detailOptionsDto.getLowerLimit());
        		JSONObject json = new JSONObject();
        		json.put("errorMsg", detailDto.getMessage());
        		json.put("controlWay", ControlWayEnum.REAL_PRICE.getCode());
        		json.put("controlSetWay", ControlSetWayEnum.FIXED_NUMBER.getCode());
        		controlOrderDetail.setExtend1(json.toString());
        		controlOrderDetailList.add(controlOrderDetail);
			}
		}
    	return controlOrderDetailList;
    }

	@Override
	public AdjustImportResultDTO getAdjustImportResult(Long controlId) {
		PriceManageControlOrder controlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlId);
		if (controlOrder == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
		String controlOrderImportResultRedisKey = getControlOrderImportResultRedisKey(controlOrder.getControlOrderId());
		return importDataResultService.getAdjustImportResult(controlOrderImportResultRedisKey);
	}

	@Override
	public ExportFileCubeVO<Map<String, Object>> asyncExportErrorControlOrderDetailsFile(Long controlId, Integer page,
			Integer pageSize) {
		PriceManageControlOrder controlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlId);
		if (controlOrder == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
		String dataKey = getControlOrderImportErrorDataRedisKey(controlOrder.getControlOrderId());
		String structureKey = getControlOrderImportStructureRedisKey(controlOrder.getControlOrderId());
		return importDataResultService.selectErrorData(dataKey, structureKey, page, pageSize);
	}

    @Override
    public List<Long> batchOrSingleAddAdjustPriceOrderByIdList(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO) {
        List<AutoPriceAdjustOrderDetailDTO> autoPriceAdjustOrderDetailList = null;
        AdjustPriceOrderDefaultDTO orderDefaultDTO = getAdjustPriceOrderDefault();
        //医保限价预警
        if(CommonEnums.BizTypeEnum.PRICE_LIMIT_CONTROL_WARN.getCode().equals(param.getBizTypeCode())){
            orderDefaultDTO = getAdjustPriceOrderDefault();
            autoPriceAdjustOrderDetailList = convertPriceLimitWarnToAutoPriceAdjustOrderDetail(param);
        }
        //验证是否存在 门店id、商品编码、价格类型、渠道相同的数据 并且把相同的门店id+商品编码+价格类型返回出来，组装成字符串 提醒给用户
        String duplicateWarning  = validateDuplicateData(autoPriceAdjustOrderDetailList);
        if(StringUtils.isNotBlank(duplicateWarning)){
            throw new AmisBadRequestException(duplicateWarning);
        }
        List<Long> userStoreIdList =getOutIdByUser(userDTO);
        String unauthorizedStoreNames = validateStorePermission(autoPriceAdjustOrderDetailList, userStoreIdList);
        if(StringUtils.isNotBlank(unauthorizedStoreNames)){
            throw new AmisBadRequestException(unauthorizedStoreNames);
        }
        List<Long> adjustOrderIdList = new ArrayList<>();
        //按照价格类型、渠道进行分组 生成不同的调价单
        Map<String, List<AutoPriceAdjustOrderDetailDTO>> groupedData =
            generatePriceAdjustOrdersByGroup(autoPriceAdjustOrderDetailList);
        for (Map.Entry<String, List<AutoPriceAdjustOrderDetailDTO>> entry : groupedData.entrySet()) {
            List<AutoPriceAdjustOrderDetailDTO> groupItems = entry.getValue();
            Map<AutoPriceAdjustOrderDetailGroupKeyDTO, List<Long>> groupItemMap = groupByFourFields(groupItems);
            Long adjustOrderId = autoCreateAdjustPriceOrderCommon(orderDefaultDTO,groupItemMap,userDTO,param.getUserOrgId(),param);
            adjustOrderIdList.add(adjustOrderId);
        }
        return adjustOrderIdList;
    }

    // 验证门店权限并返回提醒信息
    public String validateStorePermission(List<AutoPriceAdjustOrderDetailDTO> autoPriceAdjustOrderDetailList,
                                          List<Long> userStoreIdList) {

        // 找出用户没有权限的门店
        List<String> unauthorizedStoreNames = autoPriceAdjustOrderDetailList.stream()
            .filter(detail -> !userStoreIdList.contains(detail.getStoreId()))
            .map(AutoPriceAdjustOrderDetailDTO::getStoreName)
            .distinct() // 去重，避免重复的门店名称
            .collect(Collectors.toList());

        // 如果有无权限的门店，返回提醒信息
        if (!unauthorizedStoreNames.isEmpty()) {
            String storeNamesStr = String.join("、", unauthorizedStoreNames);
            return "如下门店没有权限：" + storeNamesStr;
        }

        // 如果所有门店都有权限，返回空字符串或null
        return null;
    }

    /**
     * 获取当前登录人有权限的门店id
     * @param userDTO
     * @return
     */
    private List<Long> getOutIdByUser(TokenUserDTO userDTO) {
        if (userDTO == null || userDTO.getUserId() == null) {
            throw new AmisBadRequestException("用户信息不能为空");
        }
        Set<Integer> orgTypes = Collections.singleton(OrgTypeEnum.STORE.getCode());
        List<OrgTreeDTO> orgTreeList = permissionExtService.getOrgTreeByUser(
            userDTO.getUserId(),
            RequestHeaderContextUtils.getResourceId(),
            orgTypes
        );
        if (CollectionUtils.isEmpty(orgTreeList)) {
            throw new AmisBadRequestException("用户[" + userDTO.getUserId() + "]没有对应的组织信息");
        }
        return orgTreeList.stream()
            .map(OrgTreeDTO::getOutId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * 获取调价单主表默认值
     * @return
     */
    private AdjustPriceOrderDefaultDTO getAdjustPriceOrderDefault(){
        AdjustPriceOrderDefaultDTO orderDefaultDTO = new AdjustPriceOrderDefaultDTO();
        orderDefaultDTO.setAdjustName(AdjustTypeEnum.MEDICAL_INSURANCE_PRICE_LIMIT_CONTROL.getMessage());
        orderDefaultDTO.setAdjustType(AdjustTypeEnum.MEDICAL_INSURANCE_PRICE_LIMIT_CONTROL.getCode());
        orderDefaultDTO.setAdjustReason(AdjustTypeEnum.MEDICAL_INSURANCE_PRICE_LIMIT_CONTROL.getMessage());
        orderDefaultDTO.setExecStatus(ExecEnum.EXEC_NOW.getCode());
        orderDefaultDTO.setAuditStatus((byte)AuditStatusEnum.UN_SUBMITTED.getCode());
        orderDefaultDTO.setGoodsScope(GoodsScopeEnum.ALL_GOODS_SCOPE.getCode());
        return orderDefaultDTO;
    }

    private Long autoCreateAdjustPriceOrderCommon(AdjustPriceOrderDefaultDTO orderDefault,Map<AutoPriceAdjustOrderDetailGroupKeyDTO, List<Long>> groupItemMap,TokenUserDTO userDTO,
                                                  Long userOrgId,ControlNoticeAddAdjustParam param){
        String channelIds = groupItemMap.keySet().stream().map(key -> String.valueOf(key.getChannelId())).distinct().collect(Collectors.joining(","));
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(channelIds);
        String channelNames = priceChannelMap.values().stream().map(PriceChannel::getChannelName).distinct().collect(Collectors.joining(","));
        String priceTypeCodes = groupItemMap.keySet().stream().map(AutoPriceAdjustOrderDetailGroupKeyDTO::getPriceTypeCode).distinct().collect(Collectors.joining(","));
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(priceTypeCodes);
        LocalDateTime operateTime = LocalDateTime.now();
        Long resourceId = RequestHeaderContextUtils.getResourceId();
        List<Long> orgIdList = groupItemMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        AutoPriceAdjustOrderDetailGroupKeyDTO groupKey = groupItemMap.keySet().iterator().next();
        String channelId = groupKey.getChannelId().toString();
        String adjustPriceType = groupKey.getPriceTypeCode();
        String adjustCode = noSequenceService.priceNoSequence(NoSequenceTypeEnum.PRICECENTER_AJUST);
        //生成调价单头信息
        orderDefault.setChannelId(channelId);
        orderDefault.setAdjustPriceType(adjustPriceType);
        orderDefault.setGoodsCount(groupItemMap.size());
        orderDefault.setAdjustCode(adjustCode);
        List<OrgLevelVO> orgLevelVOList = adjustPriceOrderV2Service.getUserActualSelectOrgDTOList(userDTO.getUserId(), resourceId, orgIdList);
        AdjustPriceOrder adjustPriceOrder = autoBuildInsertAdjustPriceOrder(orderDefault, userDTO, userOrgId);
        adjustPriceOrderV2Service.insertAdjustOrgDetailList(adjustCode, orgLevelVOList, userDTO, operateTime);
        adjustPriceOrderMapper.insertSelective(adjustPriceOrder);
        long adjustPriceOrderId = adjustPriceOrder.getId();
            basePriceOrderService.recordNewAuditLog(adjustCode, PriceOrderTypeEnum.ADJUST, AdjustTypeEnum.getEnum(adjustPriceOrder.getAdjustType()), AuditStatusEnum.UN_SUBMITTED, userDTO, new Date());
        //生成调价单明细信息
        List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = autoBuildInsertAdjustPriceOrderDetail(adjustPriceOrder, orderDefault, groupItemMap, orgLevelVOList, priceChannelMap, priceTypeMap, userDTO);
        Lists.partition(adjustPriceOrderDetailList, 200)
            .forEach(insertPartitionAdjustPriceOrderDetailList -> adjustPriceOrderDetailExMapper.batchInsert(insertPartitionAdjustPriceOrderDetailList));
        //补充商品信息
        sendAdjustOrderDetailThreadExecutor.execute(() -> {
            if(CommonEnums.BizTypeEnum.PRICE_LIMIT_CONTROL_WARN.getCode().equals(param.getBizTypeCode())){
                //修改医保限价预警状态
                erpBizSupportService.updatePriceLimitControlWarnAdjustStatusByIds(param.getIdList());
            }
            adjustPriceOrderDetailV2Service.sendToSupplementDataMsgs(Boolean.FALSE, adjustPriceOrderDetailList, Boolean.TRUE);
        });

        return adjustPriceOrderId;
    }

    private List<AdjustPriceOrderDetail> autoBuildInsertAdjustPriceOrderDetail(AdjustPriceOrder adjustPriceOrder,AdjustPriceOrderDefaultDTO orderDefault,
                                                                               Map<AutoPriceAdjustOrderDetailGroupKeyDTO, List<Long>> groupItemMap,List<OrgLevelVO> orgLevelVOList,
                                                                               Map<Integer, PriceChannel> priceChannelMap,
                                                                               Map<String, PriceType> priceTypeMap,
                                                                               TokenUserDTO userDTO){
        Map<Long, OrgLevelVO> orgLevelMap = orgLevelVOList.stream().collect(Collectors.toMap(OrgLevelVO::getOrgId, orgLevelVO -> orgLevelVO));
        long [] adjustDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.ADJUST_PRICE_ORDER_DETAIL.getBiz(), groupItemMap.size());
        List<AdjustPriceOrderDetail> detailList = new ArrayList<>();
        Date currentDate = new Date();
        int adjustDetailIdIndex  = 0;
        for (Map.Entry<AutoPriceAdjustOrderDetailGroupKeyDTO, List<Long>> entry : groupItemMap.entrySet()) {
            AutoPriceAdjustOrderDetailGroupKeyDTO groupKey = entry.getKey();
            List<Long> storeOrgIdList = entry.getValue();
            // 提取组织信息
            GoodsNoOrgIdsDTO orgInfo = extractOrgInfo(storeOrgIdList, orgLevelMap);
            // 获取渠道和价格类型
            PriceChannel priceChannel = priceChannelMap.get(groupKey.getChannelId());
            PriceType priceType = priceTypeMap.get(groupKey.getPriceTypeCode());
            // 创建并填充 AdjustPriceOrderDetail
            AdjustPriceOrderDetail adjustPriceOrderDetail = buildAdjustPriceOrderDetail(
                adjustPriceOrder, orderDefault, userDTO, groupKey, orgInfo, priceChannel,
                priceType, adjustDetailIds[adjustDetailIdIndex++], currentDate);
            detailList.add(adjustPriceOrderDetail);
        }
        return detailList;
    }

    /**
     * 创建并填充 AdjustPriceOrderDetail
     */
    private AdjustPriceOrderDetail buildAdjustPriceOrderDetail(AdjustPriceOrder adjustPriceOrder, AdjustPriceOrderDefaultDTO orderDefault, TokenUserDTO userDTO, AutoPriceAdjustOrderDetailGroupKeyDTO groupKey,
        GoodsNoOrgIdsDTO orgInfo, PriceChannel priceChannel, PriceType priceType, long adjustDetailId, Date currentDate) {

        AdjustPriceOrderDetail detail = new AdjustPriceOrderDetail();
        detail.setAdjustCode(adjustPriceOrder.getAdjustCode());
        detail.setAuthOrgId(adjustPriceOrder.getOrgId());
        detail.setAuthOrgName(adjustPriceOrder.getOrgName());
        detail.setSpuId(0L);
        detail.setSkuId(0L);
        detail.setGoodsNo(groupKey.getGoodsNo());
        detail.setOrgIds(orgInfo.getOrgIds());
        detail.setOrgNames(orgInfo.getOrgNames());
        detail.setOrgLevels(orgInfo.getOrgLevels());
        detail.setPriceTypeCode(groupKey.getPriceTypeCode());
        detail.setPriceTypeId(priceType.getId());
        detail.setPriceTypeName(priceType.getName());
        detail.setPrice(PriceUtil.getFenFromYuan(groupKey.getSuggestedPrice()));
        detail.setAdjustType((byte) TargetTypeEnum.ORG.getCode());
        detail.setStatus((byte) StatusEnum.NORMAL.getCode());
        detail.setGmtCreate(currentDate);
        detail.setGmtUpdate(currentDate);
        detail.setVersion(1);
        detail.setCreatedBy(userDTO.getUserId());
        detail.setUpdatedBy(userDTO.getUserId());
        detail.setUpdatedByName(userDTO.getName());
        detail.setPriceFlag(0);
        detail.setChannelId(groupKey.getChannelId());
        detail.setChannelEnCode(priceChannel.getChannelEnCode());
        detail.setChannelOutCode(priceChannel.getOutChannelCode());
        detail.setAdjustDetailId(String.valueOf(adjustDetailId));
        detail.setAdjustPriceVersion(AdjustPriceVersionEnum.VERSION_2_0.getVersion());
        detail.setMedicalPriceInterception(0);
        detail.setReason(orderDefault.getAdjustReason());
        detail.setExtend1(AdjustPriceOrderDetailExtend1.getJSONFormatStr(DataCompletionEnum.NOT_COMPLETE.getCode(),
            DataFullEnum.NOT_FULL.getCode(), 1, "", YNEnum.YES.getType()));
        return detail;
    }

    /**
     * 提取组织信息
     */
    private GoodsNoOrgIdsDTO extractOrgInfo(List<Long> storeOrgIdList, Map<Long, OrgLevelVO> orgLevelMap) {
        List<Long> orgIdList = new ArrayList<>();
        List<String> orgNameList = new ArrayList<>();
        List<Integer> orgLevelList = new ArrayList<>();

        for (Long storeOrgId : storeOrgIdList) {
            OrgLevelVO orgLevelVO = orgLevelMap.get(storeOrgId);
            if (orgLevelVO != null) {
                orgIdList.add(orgLevelVO.getOrgId());
                orgNameList.add(orgLevelVO.getOrgName());
                orgLevelList.add(orgLevelVO.getOrgLevel());
            }
        }
        GoodsNoOrgIdsDTO goodsNoOrgIdsDTO = new GoodsNoOrgIdsDTO();
        goodsNoOrgIdsDTO.setOrgIds(String.join(",", orgIdList.stream().map(String::valueOf).collect(Collectors.toList())));
        goodsNoOrgIdsDTO.setOrgNames(String.join(",", orgNameList));
        goodsNoOrgIdsDTO.setOrgLevels(String.join(",", orgLevelList.stream().map(String::valueOf).collect(Collectors.toList())));
        return goodsNoOrgIdsDTO;
    }



    public static Map<AutoPriceAdjustOrderDetailGroupKeyDTO, List<Long>> groupByFourFields(List<AutoPriceAdjustOrderDetailDTO> groupItems) {
        return groupItems.stream()
            .collect(Collectors.groupingBy(
                item -> new AutoPriceAdjustOrderDetailGroupKeyDTO(
                    item.getGoodsNo(),
                    item.getPriceTypeCode(),
                    item.getChannelId(),
                    item.getSuggestedPrice()
                ),
                Collectors.mapping(
                    AutoPriceAdjustOrderDetailDTO::getStoreOrgId,
                    Collectors.toList()
                )
            ));
    }

    @Override
    public AdjustPriceOrder autoBuildInsertAdjustPriceOrder(AdjustPriceOrderDefaultDTO orderDefault, TokenUserDTO userDTO, Long userOrgId) {
        //AutoPriceAdjustOrderDetailGroupKeyDTO groupKey = groupItemMap.keySet().iterator().next();
        //String channelId = groupKey.getChannelId().toString();
        //String adjustPriceType = groupKey.getPriceTypeCode();
        AdjustPriceOrder adjustPriceOrder = new AdjustPriceOrder();
        adjustPriceOrder.setAdjustCode(orderDefault.getAdjustCode());
        adjustPriceOrder.setAdjustType(orderDefault.getAdjustType().byteValue());
        OrgLevelVO orgLevelVO = permissionExtService.getOrgLevelVOByOrgId(userOrgId);
        adjustPriceOrder.setOrgId(orgLevelVO.getOrgId());
        adjustPriceOrder.setOrgName(orgLevelVO.getOrgName());
        adjustPriceOrder.setAuditStatus(orderDefault.getAuditStatus());
        adjustPriceOrder.setReason(orderDefault.getAdjustReason());
        adjustPriceOrder.setExecStatus(orderDefault.getExecStatus().byteValue());
        adjustPriceOrder.setEffectStatus(EffectStatusEnum.NO_EFFECT.getCode().byteValue());
        adjustPriceOrder.setScheduledTime(new Date());
        adjustPriceOrder.setStatus((byte)StatusEnum.NORMAL.getCode());
        adjustPriceOrder.setGmtCreate(new Date());
        adjustPriceOrder.setGmtUpdate(new Date());
        adjustPriceOrder.setCreatedBy(userDTO.getUserId());
        adjustPriceOrder.setCreatedByName(userDTO.getName());
        adjustPriceOrder.setUpdatedBy(userDTO.getUserId());
        adjustPriceOrder.setUpdatedByName(userDTO.getName());
        adjustPriceOrder.setChannel(orderDefault.getChannelId());
        adjustPriceOrder.setGoodsScope(orderDefault.getGoodsScope());
        adjustPriceOrder.setAdjustPriceType(orderDefault.getAdjustPriceType());
        adjustPriceOrder.setAdjustPriceVersion(AdjustPriceVersionEnum.VERSION_2_0.getVersion());
        adjustPriceOrder.setAdjustName(adjustPriceOrder.getAdjustCode());
        adjustPriceOrder.setAdjustGoodsCount(orderDefault.getGoodsCount());
        adjustPriceOrder.setAdjustOrgTabType(PriceOrderTabTypeEnum.STORE_LIST.getType());
        adjustPriceOrder.setVersion(1);
        //扩展字段增加生效方式用于回显
        String extend = adjustPriceOrder.getExtend();
        Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(extend);
        AdjustPriceOrderExtend adjustPriceOrderExtend;
        if(instance.isPresent()) {
            adjustPriceOrderExtend = instance.get();
        }else {
            adjustPriceOrderExtend = AdjustPriceOrderExtend.getInstance();
        }
        adjustPriceOrderExtend.setEffectMode(ExecEnum.EXEC_NOW.getCode());
        if(StringUtils.isNotBlank(orderDefault.getSource())){
            adjustPriceOrderExtend.setSource(orderDefault.getSource());
        }
        if(null!=orderDefault.getStoreId()){
            adjustPriceOrderExtend.setStoreId(orderDefault.getStoreId());
        }
        adjustPriceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(adjustPriceOrderExtend));
        return adjustPriceOrder;
    }

    /**
     * 根据价格类型、渠道分组生成调价单
     * @param autoPriceAdjustOrderDetailList 原始数据列表
     * @return 分组后的调价单Map，key为分组标识，value为该组的详情列表
     */
    public Map<String, List<AutoPriceAdjustOrderDetailDTO>> generatePriceAdjustOrdersByGroup(
        List<AutoPriceAdjustOrderDetailDTO> autoPriceAdjustOrderDetailList) {

        return autoPriceAdjustOrderDetailList.stream()
            .collect(Collectors.groupingBy(
                item -> generateGroupKey(item.getPriceTypeCode(), item.getChannelId()),
                LinkedHashMap::new,  // 保持插入顺序
                Collectors.toList()
            ));
    }

    /**
     * 生成分组键
     * @param priceTypeCode 价格类型
     * @param channelId 渠道id
     * @return 分组键
     */
    private String generateGroupKey(String priceTypeCode,Integer channelId) {
        return String.format("%s_%s", priceTypeCode, channelId);
    }

    // 验证重复数据的方法
    public String validateDuplicateData(List<AutoPriceAdjustOrderDetailDTO> autoPriceAdjustOrderDetailList) {
        // 创建一个Map来统计每个组合的出现次数
        Map<String, Long> duplicateCount = autoPriceAdjustOrderDetailList.stream()
            .collect(Collectors.groupingBy(
                item -> item.getStoreId() + "_" + item.getGoodsNo() + "_" + item.getPriceTypeCode() + "_" + item.getChannelId(),
                Collectors.counting()
            ));

        // 找出重复的数据（出现次数大于1的）
        List<String> duplicateKeys = duplicateCount.entrySet().stream()
            .filter(entry -> entry.getValue() > 1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        if (duplicateKeys.isEmpty()) {
            return ""; // 没有重复数据
        }

        // 组装提醒字符串
        StringBuilder warningMessage = new StringBuilder();
        warningMessage.append("存在重复的数据组合：\n");

        for (String key : duplicateKeys) {
            String[] parts = key.split("_");
            String storeId = parts[0];
            String goodsNo = parts[1];
            String priceTypeCode = parts[2];
            String channel = parts[3];
            long count = duplicateCount.get(key);

            warningMessage.append(String.format("门店ID：%s，商品编码：%s，价格类型：%s，渠道：%s（重复%d次）\n",
                storeId, goodsNo, priceTypeCode, channel, count));
        }

        return warningMessage.toString();
    }

    /**
     * 根据id查询限价管控预警数据
     * @param warnIdList
     * @return
     */
    private List<PriceLimitControlWarnDTO> getPriceLimitControlWarnList(List<Long> warnIdList) {
        List<PriceLimitControlWarnDTO> warnList = Lists.newArrayList();
        List<List<Long>> partition = Lists.partition(warnIdList, Constants.TWO_HUNDRED);
        for (List<Long> ruleIdList : partition) {
            if (CollectionUtils.isEmpty(ruleIdList)) {
                continue;
            }
            Map<String, Object> paramMap = new HashMap<>();
            String idFilter = ruleIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
            paramMap.put("filter", "id,in," + idFilter);
            paramMap.put("status", 0);
            ResponseEntity<CommonRes<PageResult<PriceLimitControlWarnDTO>>> response;
            try {
                response = nyuwaErpService.getPriceLimitControlWarnList(paramMap);
            } catch (Exception e) {
                LOGGER.error("PriceLimitControlWarnServiceImpl|调用限价管控预警接口异常", e);
                continue;
            }
            if(null==response || null==response.getBody() || null==response.getBody().getData() || CollectionUtils.isEmpty(response.getBody().getData().getRows())){
                LOGGER.warn("PriceLimitControlWarnServiceImpl|调用限价管控预警接口返回结果为空, paramMap: {}", JSON.toJSONString(paramMap));
                continue;
            }
            warnList.addAll(response.getBody().getData().getRows());
        }
        return warnList;
    }

    /**
	 *
	 * @Title: checkParentUpperLower
	 * @Description: 验证 设定的最高限价不能高于上级最高限价 设定的最低限价不能低于上级最低限价
	 * @param: @param param
	 * @return: void
	 * @throws
	 */
	private void checkParentUpperLower(ControlOrderImportV2Param param) {
		List<ControlOrderDetailDTO> importList = param.getControlOrderDetailDTOList();
	   	List<ControlOrderDetailDTO> importQualifiedList = importList.stream().filter(v -> Boolean.TRUE.equals(v.getResult())).collect(Collectors.toList());
	   	if(CollectionUtils.isEmpty(importQualifiedList)) {
	   		return;
	   	}
	   	PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getControlOrderId());
        List<OrgLevelVO> orgLevelVOList = listOrgLevelVOListByControlCode(priceManageControlOrder.getControlOrderId());
        List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(priceManageControlOrder.getChannel());
	   	List<List<ControlOrderDetailDTO>> allList = Lists.partition(importQualifiedList, 100);
	   	allList.forEach(subList -> {
	   		List<ControlOrderDetailSupplementDataVO> supplementDataVOList = Lists.newArrayList();
	   		ControlOrderDetailSupplementDataVO supplementDataVO = null;
	   		for (ControlOrderDetailDTO controlOrderDetailDTO : subList) {
	   			List<ControlOrderDetailOptionsDTO> detailOptionsDTOList = controlOrderDetailDTO.getControlOrderDetailOptionsDTOList();
	   			for (ControlOrderDetailOptionsDTO detailOptions : detailOptionsDTOList) {
	   				supplementDataVO = new ControlOrderDetailSupplementDataVO();
					supplementDataVO.setControlOrderCode(priceManageControlOrder.getControlOrderId());
					supplementDataVO.setGoodsNo(controlOrderDetailDTO.getGoodsNo());
					supplementDataVO.setPriceTypeCode(detailOptions.getPriceType());
					supplementDataVOList.add(supplementDataVO);
				}
			}
   	        Map<String, ControlOrderLimitDTO> controlOrderLimitDTOMap = getControlOrderManagePriceDTO(supplementDataVOList, channelIdList, orgLevelVOList);
	   		//验证限价
	   		subList.forEach(item -> {
	   			StringBuffer sb = new StringBuffer();
	   			String goodsNo = item.getGoodsNo();
	   			List<ControlOrderDetailOptionsDTO> detailOptionsDTOList = item.getControlOrderDetailOptionsDTOList();
	   			for (ControlOrderDetailOptionsDTO detailOptionsDTO : detailOptionsDTOList) {
	   				String priceTypeCode = detailOptionsDTO.getPriceType();
	   				String combinationKey = goodsNo+","+priceTypeCode;
	   				if(controlOrderLimitDTOMap.containsKey(combinationKey)) {
	   					ControlOrderLimitDTO controlOrderLimitDTO = controlOrderLimitDTOMap.get(combinationKey);
	   					boolean upperLimitFlag = checkUpperLimitNotGreaterThan(detailOptionsDTO.getUpperLimit(), controlOrderLimitDTO.getUpperLimit());
	   					if(!upperLimitFlag) {
	   						sb.append(priceTypeCode+"设定最高限价大于上级设定最高限价("+controlOrderLimitDTO.getUpperLimit().divide(new BigDecimal(100))+"),");
	   					}
	   					boolean lowerLimitFlag = checkLowerLimitNotGreaterThan(detailOptionsDTO.getLowerLimit(), controlOrderLimitDTO.getLowerLimit());
	   					if(!lowerLimitFlag) {
	   						sb.append(priceTypeCode+"设定最低限价小于上级设定最低限价("+controlOrderLimitDTO.getLowerLimit().divide(new BigDecimal(100))+"),");
	   					}
	   				}
				}
	   			if(StringUtils.isNotBlank(sb.toString())) {
	   				item.setResult(Boolean.FALSE);
	   				item.setMessage(sb.toString());
	            }
	   		});
	   	});
	}
	/**
	 *
	 * @Title: checkGoodsNo
	 * @Description: 验证商品编码是否存在
	 * @param: @param param
	 * @return: void
	 * @throws
	 */
	private void checkGoodsNo(ControlOrderImportV2Param param) {
		List<ControlOrderDetailDTO> importList = param.getControlOrderDetailDTOList();
		List<String> goodsNoList = importList.stream().filter(v -> StringUtils.isNotBlank(v.getGoodsNo())).map(ControlOrderDetailDTO::getGoodsNo).distinct().collect(Collectors.toList());
	   	if(CollectionUtils.isEmpty(goodsNoList)) {
	   		return;
	   	}
	   	Map<String, SpuListVO> allGoodsMap = new HashMap<String, SpuListVO>();
	   	List<List<String>> allList = Lists.partition(goodsNoList, 50);
	   	allList.forEach(subList -> {
	   		//根据商品编码查询商品库
	   		allGoodsMap.putAll(searchExtService.getSpuVOMap(subList));
	   		//验证导入的商品是否在商品库存在
	   	});
	   	List<ControlOrderDetailDTO> dataList = importList.stream().filter(v -> StringUtils.isNotBlank(v.getGoodsNo())).collect(Collectors.toList());
	   	dataList.forEach(item -> {
	   		if(!allGoodsMap.containsKey(item.getGoodsNo())) {
	   			item.setResult(Boolean.FALSE);
	   			item.setMessage(item.getMessage()+"商品编码不存在,");
	   		}
	   	});
	}
	/**
	 *
	 * @Title: checkUpperLimitNotGreaterThan
	 * @Description: 验证设定最高限价是否不大于上级最高限价
	 * @param: @param upperLimit
	 * @param: @param parentUpperLimit
	 * @param: @return
	 * @return: boolean
	 * @throws
	 */
    private static boolean checkUpperLimitNotGreaterThan(BigDecimal upperLimit, BigDecimal parentUpperLimit) {
    	if(null==parentUpperLimit) {
    		return true;
    	}
        if(upperLimit.compareTo(parentUpperLimit)<=0) {
    		return true;
    	}
    	return false;
    }
    /**
     *
     * @Title: checkLowerLimitNotGreaterThan
     * @Description: 验证设定最低限价是否不小于上级最低限价
     * @param: @param lowerLimit
     * @param: @param parentLowerLimit
     * @param: @return
     * @return: boolean
     * @throws
     */
    private static boolean checkLowerLimitNotGreaterThan(BigDecimal lowerLimit, BigDecimal parentLowerLimit) {
        if(null==parentLowerLimit) {
        	return true;
        }
    	if(lowerLimit.compareTo(parentLowerLimit)>=0) {
    		return true;
    	}
    	return false;
    }
}

