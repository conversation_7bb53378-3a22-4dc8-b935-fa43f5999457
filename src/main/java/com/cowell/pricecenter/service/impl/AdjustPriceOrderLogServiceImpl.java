package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.AdjustPriceOrderLog;
import com.cowell.pricecenter.entity.AdjustPriceOrderLogExample;
import com.cowell.pricecenter.mapper.AdjustPriceOrderLogMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderExMapper;
import com.cowell.pricecenter.service.IAdjustPriceOrderLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/4/21 16:20
 */
@Service
public class AdjustPriceOrderLogServiceImpl implements IAdjustPriceOrderLogService {

    @Resource
    private AdjustPriceOrderLogMapper adjustPriceOrderLogMapper;

    @Resource
    private AdjustPriceOrderMapper adjustPriceOrderMapper;

    @Override
    public int recordLogByAdjustPriceOrder(AdjustPriceOrder adjustPriceOrder) {

        if (Objects.nonNull(adjustPriceOrder)) {
            AdjustPriceOrderLog adjustPriceOrderLog = new AdjustPriceOrderLog();
            BeanUtils.copyProperties(adjustPriceOrder, adjustPriceOrderLog);
            adjustPriceOrderLog.setGmtCreate(new Date());
            return adjustPriceOrderLogMapper.insert(adjustPriceOrderLog);
        }

        return 0;
    }

    @Override
    public int recordLogByAdjustPriceOrder(String adjustCode) {
        if (StringUtils.isBlank(adjustCode)) {
            return 0;
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
        return recordLogByAdjustPriceOrder(adjustPriceOrder);
    }

    @Override
    public void deleteHistoryLog(int beforeDays) {
        AdjustPriceOrderLogExample example = new AdjustPriceOrderLogExample();
        AdjustPriceOrderLogExample.Criteria criteria = example.createCriteria();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -beforeDays);
        criteria.andGmtCreateLessThanOrEqualTo(calendar.getTime());

        adjustPriceOrderLogMapper.deleteByExample(example);

    }
}
