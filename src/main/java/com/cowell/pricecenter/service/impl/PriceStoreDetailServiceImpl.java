package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.common.thread.ThreadUtils;
import com.cowell.framework.utils.IdUtils;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.pricecenter.config.AdjustPriceConfig;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.PriceDetailHistoryMapper;
import com.cowell.pricecenter.mapper.PriceOrgGoodsMapper;
import com.cowell.pricecenter.mapper.PriceStoreDetailMapper;
import com.cowell.pricecenter.mapper.UnPriceStoreDetailMapper;
import com.cowell.pricecenter.mapper.extension.PriceStoreDetailExMapper;
import com.cowell.pricecenter.mq.producer.PricePushCubeProducer;
import com.cowell.pricecenter.mq.producer.PricePushYJSPriceTypeToHLProducer;
import com.cowell.pricecenter.mq.producer.PriceSyncTaskProducer;
import com.cowell.pricecenter.mq.vo.PricePushYJSVo;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskGoodsVO;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskVO;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.security.SecurityUtils;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.apollo.SyncPriceChannelConfigProperties;
import com.cowell.pricecenter.service.dto.PriceDictConverter;
import com.cowell.pricecenter.service.dto.PriceWarningRecordDTO;
import com.cowell.pricecenter.service.dto.StoreBriefDTO;
import com.cowell.pricecenter.service.dto.StoreCostDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.service.feign.facade.ItemSearchEngineFacadeService;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;
import com.cowell.pricecenter.service.vo.GoodsOfJointVo;
import com.cowell.pricecenter.utils.PriceTypeUtil;
import com.cowell.pricecenter.utils.StateUtils;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.errors.BusinessException;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.cowell.pricecenter.web.rest.util.ExcelUtils;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import com.cowell.pricecenter.web.rest.vo.PriceSaveOrUpdateVO;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class PriceStoreDetailServiceImpl extends BasePermissionService implements IPriceStoreDetailService {

    private final Logger logger = LoggerFactory.getLogger(PriceStoreDetailServiceImpl.class);

    @Autowired
    private PriceOrgGoodsMapper priceOrgGoodsMapper;
    @Autowired
    private PriceDictionaryService priceDictionaryService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;

    @Autowired
    private SearchService searchService;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PriceStoreDetailExMapper priceStoreDetailExMapper;

    @Autowired
    private PriceDetailHistoryMapper priceDetailHistoryMapper;

    @Autowired
    private UnPriceStoreDetailMapper unPriceStoreDetailMapper;

    @Autowired
    private PricePushCubeProducer pricePushCubeProducer;

    @Autowired
    private PriceSyncBaseService priceSyncBaseService;

    @Autowired
    private PriceSyncConfigService priceSyncConfigService;

    @Autowired
    private PriceWarnRecordService priceWarnRecordService;

    @Autowired
    private PriceSyncTaskService priceSyncTaskService;

//    @Autowired
//    private PriceSyncCenterStoreProducer priceSyncCenterStoreProducer;

    @Autowired
    private PricePushYJSPriceTypeToHLProducer pricePushYJSPriceTypeToHLProducer;

    @Autowired
    private SyncPriceChannelConfigProperties syncPriceChannelConfigProperties;

    @Autowired
    private PriceSyncTaskProducer priceSyncTaskProducer;

    @Autowired
    private PriceStoreDetailReadService detailReadService;

    @Autowired
    @Qualifier("taskExecutorTrace2")
    private AsyncTaskExecutor asyncTaskExecutor;


    @Value("${excel.import.detail.size}")
    private int excelImportSize;

    @Value("${price.type.transformation.businessId:99999}")
    private String transformationBusinessId;

    @ApolloJsonValue("${price.hmoPriceTypeList:[]}")
    private List<String> hmoPriceTypeList;

    @Autowired
    private AdjustPriceConfig adjustPriceConfig;

    @Autowired
    private IPriceService priceService;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    @Autowired
    private FeignStoreService feignStoreService;

    @Value("${price.queryLimitCount:50}")
    private int queryLimitCount;

    @Autowired
    private ItemSearchEngineFacadeService itemSearchEngineFacadeService;


    private static final String PRICE_STORE_DETAIL_INFO = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.PRICE_STORE_DETAIL_SYNC_KEY;
    private static final String PRICED_STORE_KEY = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.PRICED_STORE_KEY;
    private static final String PRICE_TYPE_GJHIS_O2O = "GJHIS-O2O";
    private static final String PRICE_TYPE_GJHIS_B2C = "GJHIS-B2C";
    private static final String PRICE_TYPE_ALL = "ALL";

    @Override
    public String priceListImport(MultipartFile file) throws Exception {

        Long loginUserId = SecurityUtils.getCurrentUserToken().getUserId();
        String loginUserName = SecurityUtils.getCurrentUserToken().getName();
        logger.info("当前登录人ID:{}", loginUserId);
        logger.info("当前登录人NAME:{}", loginUserName);

        // 解析excel
          /* 换新的excel了 此方法作废
        List<ImportPriceStoreDetailDTO> goodsList = ExcelUtils.excelToList(file.getInputStream(), ImportPriceStoreDetailDTO.propertyToMap(),
            ImportPriceStoreDetailDTO.class, file.getOriginalFilename(), 0, "");
        logger.info("importPriceStoreDetail|goodsList,{}.", goodsList);
        if (CollectionUtils.isEmpty(goodsList)) {
            throw new BusinessErrorException(ReturnCodeEnum.EXCEL_NO_DATE);
        }
        if (goodsList.size() > excelImportSize) {
            throw new BusinessErrorException(ReturnCodeEnum.EXCEL_TOO_MANY_DATA);
        }
   */

        List<ImportPriceStoreDetailDTO> goodsList = ExcelUtils.excelToList(file, excelImportSize);
        logger.info("priceListImport goodsList:{}", goodsList);
        int random = new Random().nextInt(90000) + 10000;
        String key = "PRICE_STORE_DETAIL_CODE_" + random;
        RMapCache<String, ImportPriceStoreDetailResponse> priceRMapCache = redissonClient.getMapCache(PRICE_STORE_DETAIL_INFO + key);

        Integer totalSize = goodsList.size();
        asyncTaskExecutor.execute(() -> {
            //给本批次调价的商品生成一个调价单号
            String adjustCode = PriceSourceEnum.EXCEL.getCode() + "EXCEL" + DateUtils.getTodayYYYYMMDDHHmmss() + IdUtils.getNumByLength(4);

            ImportPriceStoreDetailResponse result = new ImportPriceStoreDetailResponse();
            List<ImportPriceStoreDetailErrorDTO> errorList = Lists.newArrayList();
            Set<Long> businessIdSet = new HashSet<>();
            Map<String, Object> preMap = batchPreCheck(goodsList, errorList);
            if (MapUtils.isEmpty(preMap)) {
                errorList.forEach(error -> error.setStatus("机构ID为空或不存在"));
                result.setResult(1);
                result.setTotalSize(totalSize);
                result.setRightSize(0);
                result.setErrorSize(totalSize);
                result.setErrorMessageDTOList(errorList);
                priceRMapCache.put(key, result, 1, TimeUnit.HOURS);
                return;
            }

            Integer errorSize = Integer.parseInt(preMap.get("errorSize").toString());
            List<String> rightList = (List<String>) preMap.get("rightList");
            Map<String, ImportPriceStoreDetailDTO> storeDetailCache = (Map<String, ImportPriceStoreDetailDTO>) preMap.get("storeDetailCache");
            Map<String, PriceOrgGoods> orgGoodsCache = (Map<String, PriceOrgGoods>) preMap.get("orgGoodsCache");
            Map<String, PriceType> priceTypeMap = (Map<String, PriceType>) preMap.get("priceTypeMap");
            Map<Long, OrgDTO> orgMap = (Map<Long, OrgDTO>) preMap.get("orgMap");
            insertPriceStoreDetail(rightList, storeDetailCache, orgGoodsCache, priceTypeMap, orgMap, errorSize, errorList, adjustCode, businessIdSet, loginUserId, loginUserName);

            result.setResult(1);
            result.setTotalSize(totalSize);
            result.setRightSize(totalSize - errorSize);
            result.setErrorSize(errorSize);
            result.setErrorMessageDTOList(errorList);
            priceRMapCache.put(key, result, 1, TimeUnit.HOURS);

            //本地调价通过，开始给英克海典同步价格
            pushPriceToYkHd(businessIdSet, adjustCode, PriceSourceEnum.EXCEL.getCode());
        });

        return key;
    }

    @Override
    public ImportPriceStoreDetailResponse getPriceListImportStatus(String key) throws Exception {
        logger.info("获取批量导入价格的状态,参数:{}", key);
        RMapCache<String, ImportPriceStoreDetailResponse> priceRMapCache = redissonClient.getMapCache(PRICE_STORE_DETAIL_INFO + key);
        ImportPriceStoreDetailResponse response = new ImportPriceStoreDetailResponse();
        if (priceRMapCache.size() != 0) {
            response = priceRMapCache.get(key);
        }
        logger.info("获取批量导入价格的状态,结果:{}", response);
        return response;
    }

    @Override
    public PriceQueryNewDTO getPricedCount(PriceStoreDetailParam param) {

        PriceQueryNewDTO newDTO = new PriceQueryNewDTO();

        UnPriceStoreDetailExample example = new UnPriceStoreDetailExample();
        UnPriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(param.getBusinessId());
        if (param.getStoreId() != null) {
            criteria.andStoreIdEqualTo(param.getStoreId());
        }
        Long unPricedCount = unPriceStoreDetailMapper.countByExample(example);
        int pricedCount = 0;

        Long key;
        if (param.getStoreId() != null) {
            key = param.getStoreId();
        } else {
            key = param.getBusinessId();
        }
        RMapCache<Long, Integer> mapCache = redissonClient.getMapCache(PRICED_STORE_KEY + key);
        if (mapCache.size() != 0) {
            pricedCount += mapCache.get(key);
        }

        newDTO.setUnPricedCount(unPricedCount.intValue());
        newDTO.setPricedCount(pricedCount);

        return newDTO;
    }

    @Override
    public Map<String, Object> getPricedStoreDetailList(PriceStoreDetailParam param) {

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .businessId(param.getBusinessId())
            .build();

        if (param.getStoreId() != null) {
            query.setStoreId(param.getStoreId());
        }
        if (StringUtils.isNotEmpty(param.getKeyWord())) {
            query.setExtendLike("%" + param.getKeyWord() + "%");
        }
        if (param.getLevel() != null && StringUtils.isNotEmpty(param.getLevel().toString())) {
            query.setLevel(param.getLevel());
        }
        if (StringUtils.isNotEmpty(param.getPriceTypeCode())) {
            query.setPriceTypeCode(param.getPriceTypeCode());
        }
        if (CollectionUtils.isNotEmpty(param.getChannelIdList())) {
            query.setChannelIdList(param.getChannelIdList());
        }
        Long count = priceStoreDetailReadService.count(query);
        int page = param.getPage();
        int pageSize = param.getPageSize();
        if (param.getPage() == null || param.getPage() <= 0) {
            page = 1;
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            pageSize = 10;
        }
        query.setPage(page);
        query.setPageSize(pageSize);
        List<PriceStoreDetail> pricedList = priceStoreDetailReadService.query(query);
        logger.info("pricedList:{}", pricedList);
        //规格 剂型 生产厂家，需要从priceOrgGoods取
        List<PriceStoreDetailDTO> dtoList = transDetailDTO(pricedList, param.getBusinessId());

        Map<String, Object> resultMap = new HashMap<>(16);
        resultMap.put("count", count);
        resultMap.put("list", dtoList);
        return resultMap;
    }


    @Override
    public Map<String, Object> getUnPricedStoreDetailList(PriceStoreDetailParam param) {

        UnPriceStoreDetailExample example = new UnPriceStoreDetailExample();
        UnPriceStoreDetailExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(param.getKeyWord())) {
            criteria.andExtendLike("%" + param.getKeyWord() + "%");
        }
        criteria.andBusinessIdEqualTo(param.getBusinessId());
        if (param.getStoreId() != null) {
            criteria.andStoreIdEqualTo(param.getStoreId());
        }
        Long count = unPriceStoreDetailMapper.countByExample(example);
        int page = param.getPage();
        int pageSize = param.getPageSize();
        if (param.getPage() == null || param.getPage() <= 0) {
            page = 1;
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            pageSize = 10;
        }
        example.setLimit(pageSize);
        example.setOffset((page - 1) * pageSize);
        List<UnPriceStoreDetail> list = unPriceStoreDetailMapper.selectByExample(example);
        logger.info("unpricedList:{}", list);

        Map<String, Object> resultMap = new HashMap<>(16);
        resultMap.put("count", count);
        resultMap.put("list", list);
        return resultMap;
    }

    @Override
    public List<UnPriceStoreDetail> downloadUnPricedDetailList(HttpServletRequest request) {

        String businessId = request.getParameter("businessId");
        String storeId = request.getParameter("storeId");
        String keyWord = request.getParameter("keyWord");
        logger.info("下载未定价商品参数,连锁ID:{},门店ID:{},关键字:{}", businessId, storeId, keyWord);

        UnPriceStoreDetailExample example = new UnPriceStoreDetailExample();
        UnPriceStoreDetailExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(keyWord)) {
            criteria.andExtendLike("%" + keyWord + "%");
        }
        criteria.andBusinessIdEqualTo(Long.parseLong(businessId));
        if (storeId != null && StringUtils.isNotEmpty(storeId) && !"0".equals(storeId)) {
            criteria.andStoreIdEqualTo(Long.parseLong(storeId));
        }
        List<UnPriceStoreDetail> list = unPriceStoreDetailMapper.selectByExample(example);
        logger.info("下载未定价商品结果:{}", list);

        return list;
    }

    @Override
    public List<PriceQueryGoodsNoDTO> getPriceByType(PriceQueryParam param) {

        Long storeId = param.getStoreId();

        String type = StringUtils.isEmpty(param.getType()) ? PTypeEnum.LSJ.getCode() : param.getType();

        // 这个价格类型不参与变更逻辑(判断是否查询会员价)
        String paramType = type;

        List<String> goodsNoList = param.getGoodsNoList()
            .stream()
            .distinct()
            .collect(Collectors.toList());

        if (goodsNoList.size() > queryLimitCount) {
            throw new BusinessException(ReturnCodeEnum.QUERY_PRICE_GOODS_COUNT_EXCEED_LIMIT);
        }
        List<String> goodsNoList401 = goodsNoList.stream().filter(item -> item.startsWith("401")).collect(Collectors.toList());
        //是否邦建 如果是邦建需要换 GJYJS 变为 LSJ
        boolean isPriceTypeTransformation = type.equals(PTypeEnum.GJYJS.getCode()) && isHasPriceTypeTransformation(storeId);
        if (isPriceTypeTransformation) {
            if (CollectionUtils.isNotEmpty(goodsNoList401)) {
                type = param.getType();
            } else {
                type = PTypeEnum.LSJ.getCode();
            }
        }

        //当前真实的 价格类型
        String curType = type;

        type = (PRICE_TYPE_GJHIS_B2C.equals(curType) || PRICE_TYPE_GJHIS_O2O.equals(curType)) ? PTypeEnum.GJHIS.getCode() : type;

        // 判断是否是 b2c 或者 高济互医的价格类型 如果是 需要做搂底 查高济药急送价格
        boolean isB2CPriceType = curType.equals(PTypeEnum.B2C.getCode()) || PRICE_TYPE_GJHIS_B2C.equals(curType) || PRICE_TYPE_GJHIS_O2O.equals(curType);

        List<PriceQueryGoodsNoDTO> dtoList = new ArrayList<>();

        List<Integer> channelIds = Lists.newArrayList();
        if (Objects.nonNull(param.getChannelId())) {
            channelIds.add(param.getChannelId());
        }

        if (Objects.isNull(param.getSkuId()) && CollectionUtils.isEmpty(param.getSkuIdList())) {
            param.setSkuId(0L);
        }
        if (PRICE_TYPE_ALL.equals(type)) {
            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(storeId)
                .channelIdList(channelIds)
                .goodsNoList(goodsNoList)
                .priceTypeCodeList(Arrays.asList(PTypeEnum.GJYJS.getCode(), PTypeEnum.B2C.getCode(),
                    PTypeEnum.GJHIS.getCode(), PTypeEnum.LSJ.getCode(), PTypeEnum.HYJ.getCode()))
                .build();
            if (CollectionUtils.isNotEmpty(param.getSkuIdList())) {
                query.setSkuIdList(param.getSkuIdList());
            } else {
                query.setSkuId(param.getSkuId());
            }
            List<PriceStoreDetail> priceList = detailReadService.query(query);

            if (CollectionUtils.isEmpty(priceList)) {
                return dtoList;
            }

            //通过商品编码分组
            Map<String, List<PriceStoreDetail>> goodsNoToPriceMap = priceList.stream().collect(Collectors.groupingBy(PriceStoreDetail::getGoodsNo));

            goodsNoToPriceMap.forEach((k, v) -> {
                PriceQueryGoodsNoDTO dto = new PriceQueryGoodsNoDTO();
                dto.setGoodsNo(k);
                v.forEach(item -> {
                    PTypeEnum pTypeEnum = PTypeEnum.getPTypeEnum(item.getPriceTypeCode());
                    if (pTypeEnum != null) {
                        dto.setSkuId(item.getSkuId());
                        switch (pTypeEnum) {
                            case GJYJS:
                                dto.setPrice(item.getPrice());
                                if (StateUtils.hasState(item.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                                    dto.setIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
                                } else {
                                    dto.setIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
                                }
                                break;
                            case B2C:
                                dto.setB2cPrice(item.getPrice());
                                if (StateUtils.hasState(item.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                                    dto.setB2cPriceIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
                                } else {
                                    dto.setB2cPriceIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
                                }
                                break;
                            case GJHIS:
                                dto.setHisPrice(item.getPrice());
                                if (StateUtils.hasState(item.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                                    dto.setHisPriceIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
                                } else {
                                    dto.setHisPriceIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
                                }
                                break;
                            case LSJ:
                                dto.setLsPrice(item.getPrice());
                                if (StateUtils.hasState(item.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                                    dto.setLsPriceIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
                                } else {
                                    dto.setLsPriceIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
                                }
                                break;
                            case HYJ:
                                dto.setMemberPrice(item.getPrice());
                                if (StateUtils.hasState(item.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                                    dto.setMemberPriceIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
                                } else {
                                    dto.setMemberPriceIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
                                }
                                break;
                            default:
                                break;
                        }

                    }
                });
                dtoList.add(dto);
            });
            return dtoList;
        }


        List<String> priceTypeCodeList = Lists.newArrayList();
        priceTypeCodeList.add(type);

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .channelIdList(channelIds)
            .goodsNoList(goodsNoList)
            .priceTypeCodeList(priceTypeCodeList)
//            .skuId(param.getSkuId())
//            .skuIdList(param.getSkuIdList())
            .build();
        if (CollectionUtils.isNotEmpty(param.getSkuIdList())) {
            query.setSkuIdList(param.getSkuIdList());
        } else {
            query.setSkuId(param.getSkuId());
        }

        if (Objects.nonNull(param.getChannelStoreId())) {
            query.setChannelStoreId(param.getChannelStoreId());
        }
        if (StringUtils.isNotBlank(param.getPriceGroup())){
            query.setPriceGroup(param.getPriceGroup());
        }

        List<PriceStoreDetail> priceList = detailReadService.query(query);

        boolean lack = CollectionUtils.isEmpty(priceList) || priceList.size() != goodsNoList.size();
        logger.info("判断是否是互医:{} 结果是否缺失:{} 是否查询到价格:{}", isB2CPriceType, lack, priceList.size());
        if (isB2CPriceType && CollectionUtils.isEmpty(priceList)) {
            priceTypeCodeList.clear();
            if (PRICE_TYPE_GJHIS_B2C.equals(curType)) {
                priceTypeCodeList.add(PTypeEnum.B2C.getCode());
            } else if (PRICE_TYPE_GJHIS_O2O.equals(curType)) {
                priceTypeCodeList.add(PTypeEnum.GJYJS.getCode());
            } else if (curType.equals(PTypeEnum.B2C.getCode())) {
                priceTypeCodeList.add(PTypeEnum.GJYJS.getCode());
            }
            query.setPriceTypeCodeList(priceTypeCodeList);
            priceList = detailReadService.query(query);
        } else if ((type.equals(PTypeEnum.GJYJS.getCode()) || hmoPriceTypeList.contains(type)) && lack) {
            // 高济药急送，HMO渠道,额外查询零售价兜底
            if (CollectionUtils.isNotEmpty(priceList)) {
                goodsNoList.removeAll(priceList.stream().map(PriceStoreDetail::getGoodsNo).collect(Collectors.toList()));
            }
            priceTypeCodeList.clear();
            priceTypeCodeList.add(PTypeEnum.LSJ.getCode());
            query.setPriceTypeCodeList(priceTypeCodeList);
            priceList.addAll(detailReadService.query(query));
        }

        if (CollectionUtils.isEmpty(priceList)) {
            return new ArrayList<>();
        }

        for (PriceStoreDetail detail : priceList) {
            if ((detail.getPrice() == null || detail.getPrice().equals(new BigDecimal(0))) && !isB2CPriceType) {
                //发送mq 触发补充价格的方法
                PriceSyncTaskVO vo = new PriceSyncTaskVO();
                vo.setGoodsNo(detail.getGoodsNo());
                vo.setStoreId(detail.getStoreId());
                vo.setBusinessId(detail.getBusinessId());
                vo.setPriceTypeCode(type);
                vo.setType(PriceSyncTaskTypeEnum.SELECT_PRICE_NULL.getType());
                priceSyncTaskProducer.sendPriceSyncTask(vo);
                continue;
            }

            PriceQueryGoodsNoDTO dto = new PriceQueryGoodsNoDTO();
            dto.setGoodsNo(detail.getGoodsNo());
            dto.setPrice(detail.getPrice());
            dto.setSkuId(detail.getSkuId());
            dto.setExtend(detail.getExtend());
            if (Objects.nonNull(detail.getStatus())) {
                dto.setStatus(Integer.valueOf(detail.getStatus() + ""));
            }

            if (StateUtils.hasState(detail.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                dto.setIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
            } else {
                dto.setIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
            }
            dtoList.add(dto);
        }
        // 如果是药急送，需要获取会员价
        if (Objects.equals(paramType, PTypeEnum.GJYJS.getCode())) {
            setMemberPrice(dtoList, query);
        }
        return dtoList;
    }

    private void setMemberPrice(List<PriceQueryGoodsNoDTO> list, PriceStoreDetailQuery query) {
        logger.info("PriceStoreDetailServiceImpl|setMemberPrice|获取会员价");
        PriceStoreDetailQuery memberPriceQuery = new PriceStoreDetailQuery();
        BeanUtils.copyProperties(query, memberPriceQuery);
        memberPriceQuery.setPriceTypeCodeList(Lists.newArrayList());
        memberPriceQuery.setPriceTypeCode(PTypeEnum.HYJ.getCode());
        List<PriceStoreDetail> memberPriceList = detailReadService.query(memberPriceQuery);
        if (CollectionUtils.isEmpty(memberPriceList)) {
            logger.warn("PriceStoreDetailServiceImpl|setMemberPrice|未能获取到会员价|{}", JSON.toJSON(memberPriceQuery));
            return;
        }
        Map<String, PriceStoreDetail> priceMap = memberPriceList.stream()
            .collect(Collectors.toMap(PriceStoreDetail::getGoodsNo, price -> price, (k1, k2) -> k1));
        for (PriceQueryGoodsNoDTO priceQueryGoodsNoDTO : list) {
            PriceStoreDetail priceStoreDetail = priceMap.get(priceQueryGoodsNoDTO.getGoodsNo());
            if (priceStoreDetail == null) {
                continue;
            }
            priceQueryGoodsNoDTO.setMemberPrice(priceStoreDetail.getPrice());
        }
    }

    @Override
    public List<PriceCompareDTO> getPriceAndMemberPrice(PriceQueryParam param) {
        //logger.info("根据商品编码获取商品的会员价和零售价 请求参数:{}", param.toString());
        Long storeId = param.getStoreId();
        List<String> goodsNoList = param.getGoodsNoList();
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();

        criteria.andStoreIdEqualTo(storeId);
        criteria.andGoodsNoIn(goodsNoList);
        criteria.andChannelIdEqualTo(param.getChannelId());
        criteria.andPriceTypeCodeIn(Arrays.asList(PTypeEnum.LSJ.getCode(), PTypeEnum.HYJ.getCode(), PTypeEnum.CLJ.getCode(), PTypeEnum.CHYJ.getCode()));
        if (Objects.isNull(param.getSkuId()) && CollectionUtils.isEmpty(param.getSkuIdList())) {
            criteria.andSkuIdEqualTo(0L);
        }
        if (Objects.nonNull(param.getSkuId())) {
            criteria.andSkuIdEqualTo(param.getSkuId());
        }
        if (CollectionUtils.isNotEmpty(param.getSkuIdList())) {
            criteria.andSkuIdIn(param.getSkuIdList());
        }
        List<PriceStoreDetailPriceAndMemberPriceDTO> priceList = priceStoreDetailExMapper.queryPriceStoreDetailPriceAndMemberPriceList(example);
        if (CollectionUtils.isEmpty(priceList)) {
            return new ArrayList<>();
        }
        Map<String, PriceStoreDetailPriceAndMemberPriceDTO> map = priceList.stream().collect(Collectors.toMap(item -> item.getGoodsNo() + "_" + item.getPriceTypeCode(), Function.identity()));
        List<PriceCompareDTO> dtoList;
        Map<String, PriceCompareDTO> priceQueryGoodsNoDTOMap = new HashMap<>();
        List<String> hasPriceGoodsNoList = priceList.stream().map(PriceStoreDetailPriceAndMemberPriceDTO::getGoodsNo).distinct().collect(Collectors.toList());
        for (String goodsNo : hasPriceGoodsNoList) {
            PriceCompareDTO dto = new PriceCompareDTO();

            PriceStoreDetailPriceAndMemberPriceDTO priceDTO = map.get(goodsNo + "_" + PTypeEnum.LSJ.getCode());

            dto.setGoodsNo(goodsNo);
            dto.setPrice(priceDTO == null ? new BigDecimal(0) : priceDTO.getPrice());
            dto.setIsHasPrice(priceDTO == null ? 0 : 1);
            if (Objects.nonNull(priceDTO) && StateUtils.hasState(priceDTO.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                dto.setIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
            } else {
                dto.setIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
            }

            PriceStoreDetailPriceAndMemberPriceDTO piecePriceDTO = map.get(goodsNo + "_" + PTypeEnum.CLJ.getCode());
            dto.setPiecePrice(piecePriceDTO == null ? new BigDecimal(0) : piecePriceDTO.getPrice());
            dto.setIsHasPiecePrice(piecePriceDTO == null ? 0 : 1);
            if (Objects.nonNull(piecePriceDTO) && StateUtils.hasState(piecePriceDTO.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                dto.setIsPieceSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
            } else {
                dto.setIsPieceSpecial(IsSpecialEnum.not_SPECIAL.getCode());
            }

            PriceStoreDetailPriceAndMemberPriceDTO memberPriceDTO = map.get(goodsNo + "_" + PTypeEnum.HYJ.getCode());
            dto.setMemberPrice(memberPriceDTO == null ? new BigDecimal(0) : memberPriceDTO.getPrice());
            dto.setIsHasMemberPrice(memberPriceDTO == null ? 0 : 1);
            if (Objects.nonNull(memberPriceDTO) && StateUtils.hasState(memberPriceDTO.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                dto.setMemberPriceIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
            } else {
                dto.setMemberPriceIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
            }

            PriceStoreDetailPriceAndMemberPriceDTO memberPiecePriceDTO = map.get(goodsNo + "_" + PTypeEnum.CHYJ.getCode());
            dto.setMemberPiecePrice(memberPiecePriceDTO == null ? new BigDecimal(0) : memberPiecePriceDTO.getPrice());
            dto.setIsHasMemberPiecePrice(memberPiecePriceDTO == null ? 0 : 1);
            if (Objects.nonNull(memberPiecePriceDTO) && StateUtils.hasState(memberPiecePriceDTO.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos())) {
                dto.setMemberPiecePriceIsSpecial(IsSpecialEnum.IS_SPECIAL.getCode());
            } else {
                dto.setMemberPiecePriceIsSpecial(IsSpecialEnum.not_SPECIAL.getCode());
            }

            priceQueryGoodsNoDTOMap.put(goodsNo, dto);
        }
        dtoList = new ArrayList<>(priceQueryGoodsNoDTOMap.values());
        return dtoList;
    }

    @Override
    public List<PriceQueryGoodsNoDTO> getAllPOSPricePage(PriceQueryParam param) {
        Long storeId = param.getStoreId();
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();

        criteria.andStoreIdEqualTo(storeId);
        criteria.andChannelIdEqualTo(param.getChannelId());
        if (Objects.isNull(param.getSkuId()) && CollectionUtils.isEmpty(param.getSkuIdList())) {
            criteria.andSkuIdEqualTo(0L);
        }
        if (Objects.nonNull(param.getSkuId())) {
            criteria.andSkuIdEqualTo(param.getSkuId());
        }
        if (CollectionUtils.isNotEmpty(param.getSkuIdList())) {
            criteria.andSkuIdIn(param.getSkuIdList());
        }
        criteria.andPriceTypeCodeIn(Arrays.asList(PTypeEnum.LSJ.getCode(), PTypeEnum.HYJ.getCode()));
        example.setOrderByClause("id");
        example.setLimit(param.getPageSize());
        example.setOffset(param.getPage() * param.getPageSize());
        List<PriceStoreDetailPriceAndMemberPriceDTO> priceList = priceStoreDetailExMapper.queryPriceStoreDetailPriceAndMemberPriceList(example);
        if (CollectionUtils.isEmpty(priceList)) {
            return new ArrayList<>();
        }
        Map<String, PriceStoreDetailPriceAndMemberPriceDTO> map = priceList.stream().collect(Collectors.toMap(item -> item.getGoodsNo() + "_" + item.getPriceTypeCode(), Function.identity()));
        List<PriceQueryGoodsNoDTO> dtoList = new ArrayList<>();
        for (PriceStoreDetailPriceAndMemberPriceDTO detail : priceList) {
            PriceQueryGoodsNoDTO dto = new PriceQueryGoodsNoDTO();

            PriceStoreDetailPriceAndMemberPriceDTO priceDTO = map.get(detail.getGoodsNo() + "_" + PTypeEnum.LSJ.getCode());
            PriceStoreDetailPriceAndMemberPriceDTO memberPriceDTO = map.get(detail.getGoodsNo() + "_" + PTypeEnum.HYJ.getCode());

            dto.setGoodsNo(detail.getGoodsNo());
            dto.setPrice(priceDTO == null ? new BigDecimal(0) : priceDTO.getPrice());
            dto.setIsHasPrice(priceDTO == null ? 0 : 1);
            dto.setMemberPrice(memberPriceDTO == null ? new BigDecimal(0) : memberPriceDTO.getPrice());
            dto.setIsHasMemberPrice(memberPriceDTO == null ? 0 : 1);
            dtoList.add(dto);
        }
        return dtoList;
    }

    private List<PriceStoreDetailDTO> transDetailDTO(List<PriceStoreDetail> pricedList, Long businessId) {

        List<String> goodsNoList = pricedList.stream().map(goods -> goods.getGoodsNo()).collect(Collectors.toList());

        SpuNewParamVo paramVo = new SpuNewParamVo();
        paramVo.setBusinessId(businessId);
        paramVo.setGoodsNoList(goodsNoList);
        logger.info("ES搜索连锁下商品列表信息，参数:{}", paramVo);
        List<SpuNewVo> spuList;
        try {
            spuList = itemSearchEngineFacadeService.getNewSpuList4Post(paramVo);
//            spuList = searchService.getNewSpulistBusiness(paramVo);
        } catch (Exception e) {
            logger.warn("ES搜索连锁下商品列表信息异常,", e);
            throw new BusinessException(ReturnCodeEnum.GET_ES_RESULT_ERROR);
        }
        logger.info("ES搜索连锁下商品列表信息，查询结果:{}", spuList);
        Map<String, SpuNewVo> spuMap = spuList.stream().collect(Collectors.toMap(o -> o.getGoodsNo(), item -> item, (i, j) -> i));

        List<PriceStoreDetailDTO> dtoList = new ArrayList<>();
        for (PriceStoreDetail detail : pricedList) {
            SpuNewVo spuVo = spuMap.get(detail.getGoodsNo());
            logger.info("spuVO:{}", spuVo);
            PriceStoreDetailDTO dto = new PriceStoreDetailDTO();
            BeanUtils.copyProperties(detail, dto);
            dto.setOrgLevelName(OrgLevelEnum.getName(dto.getLevel()));
            if (spuVo != null) {
                dto.setDosage(spuVo.getDosageformsid());
                dto.setJhiSpecification(spuVo.getJhiSpecification());
                dto.setManufacturer(spuVo.getFactoryid());
            }
            dtoList.add(dto);
        }

        return dtoList;
    }


    private Map batchPreCheck(List<ImportPriceStoreDetailDTO> goodsList, List<ImportPriceStoreDetailErrorDTO> errorList) {

        // 缓存的数据map
        Map<String, ImportPriceStoreDetailDTO> storeDetailCache = new ConcurrentHashMap<>(16);
        // 缓存定调价目录map
        Map<String, PriceOrgGoods> orgGoodsCache = new ConcurrentHashMap<>(16);

        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = getPriceTypeMap();
        logger.info("priceListImport batchPreCheck priceTypeMap:{}", priceTypeMap);
        //获取所有的机构
        Map<String, Object> resultMap = new HashMap<>(16);
        List<Long> orgIdList = goodsList.stream().filter(detail -> detail.getOrgId() != null).map(detail -> detail.getOrgId()).distinct().collect(Collectors.toList());
        Map<Long, OrgDTO> orgMap;
        if (CollectionUtils.isEmpty(orgIdList)) {
            return null;
        }
        orgMap = getOrgMap(orgIdList);
        if (MapUtils.isEmpty(orgMap)) {
            return null;
        }
        // goodsList集合,返回的集合是条件都符合的记录,参数错误的在errorList
        List<String> rightList = new ArrayList<>();
        checkDataBase(rightList, goodsList, errorList, storeDetailCache, orgGoodsCache, priceTypeMap, orgMap);

        logger.info("priceListImport batchPreCheck 导入数据前错误列表:{}", errorList);
        resultMap.put("rightList", rightList);
        resultMap.put("storeDetailCache", storeDetailCache);
        resultMap.put("orgGoodsCache", orgGoodsCache);
        resultMap.put("priceTypeMap", priceTypeMap);
        resultMap.put("orgMap", orgMap);

        resultMap.put("errorSize", errorList.size());
        resultMap.put("errorList", errorList);
        return resultMap;

    }


    /**
     * 校验录入数据重复性
     *
     * @param list             Excel导入原始数据
     * @param errorList        错误数据列表,用于展现在前端
     * @param storeDetailCache 正确数据过滤后缓存
     * @param orgGoodsCache    正确数据过滤后,得到定调价目录缓存
     * @param priceTypeMap     所有价格类型缓存
     * @param orgMap           正确数据过滤后,得到机构缓存
     * @return 正确的Excel数据, 用于后续插入或新增
     */
    private List<String> checkDataBase(List<String> rightList,
                                       List<ImportPriceStoreDetailDTO> list,
                                       List<ImportPriceStoreDetailErrorDTO> errorList,
                                       Map<String, ImportPriceStoreDetailDTO> storeDetailCache,
                                       Map<String, PriceOrgGoods> orgGoodsCache,
                                       Map<String, PriceType> priceTypeMap,
                                       Map<Long, OrgDTO> orgMap) {
        //1.校验价格类型是否正确
        //2.校验价格类型名称是否不存在/停用
        //3.部门ID是否存在
        //4.商品编码是否存在
        //5.3+4判断是否有调价权限
        //6.判断价格是否在价格区间


        List<List<ImportPriceStoreDetailDTO>> subList = Lists.partition(list, 100);
        Callable[] callables = new Callable[subList.size()];
        for (int i = 0; i < subList.size(); i++) {
            List<ImportPriceStoreDetailDTO> tempList = subList.get(i);
            callables[i] = () -> {
                for (ImportPriceStoreDetailDTO item : tempList) {
                    String goodsNo = item.getGoodsNo();
                    Long orgId = item.getOrgId();
                    String priceTypeCode = item.getPriceTypeCode();
                    BigDecimal price = new BigDecimal(item.getPrice());

                    ImportPriceStoreDetailErrorDTO message = new ImportPriceStoreDetailErrorDTO();
                    message.setGoodsNo(goodsNo);
                    message.setOrgId(orgId);
                    message.setPriceTypeCode(priceTypeCode);
                    message.setPrice(price);

                    //校验价格是否为数字
                    String reg = "\\d+|\\d+\\.\\d+";
                    if (goodsNo == null || "null".equals(goodsNo)
                        || orgId == null || "null".equals(goodsNo)
                        || priceTypeCode == null || "null".equals(priceTypeCode)
                        || price == null) {
                        StringBuilder errorMessage = new StringBuilder();
                        if (goodsNo == null || "null".equals(goodsNo)) {
                            errorMessage.append("商品编码不能为空,");
                        }
                        if (orgId == null || "null".equals(goodsNo)) {
                            errorMessage.append("调价部门ID不能为空,");
                        }
                        if (priceTypeCode == null || "null".equals(priceTypeCode)) {
                            errorMessage.append("价格类型唯一标识不能为空,");
                        }
                        if (price == null) {
                            errorMessage.append("价格不能为空,");
                        }
                        int length = errorMessage.toString().length();
                        message.setMessage(errorMessage.substring(0, length - 1));
                        errorList.add(message);
                        continue;
                    }
                    //1.校验价格类型是否正确
                    if (!price.toString().matches(reg)) {
                        message.setMessage(ReturnCodeEnum.PRICE_FORMAT_ERROR.getMessage());
                        errorList.add(message);
                        continue;
                    }
                    //2.校验价格类型名称是否不存在/停用
                    if (!priceTypeMap.containsKey(priceTypeCode)) {
                        message.setMessage("价格类型不存在或停用");
                        errorList.add(message);
                        continue;
                    }
                    //3.部门ID是否存在
                    if (orgMap == null || !orgMap.containsKey(orgId)) {
                        message.setMessage("机构ID不存在");
                        errorList.add(message);
                        continue;
                    }
                    //5. 判断是否有调价权限
                    PriceOrgGoodsExample example = new PriceOrgGoodsExample();
                    example.createCriteria()
                        .andGoodsNoEqualTo(goodsNo)
                        .andOrgIdEqualTo(orgId)
                        .andStatusEqualTo((byte) StatusEnum.NORMAL.getCode());
                    List<PriceOrgGoods> priceOrgGoodsList = priceOrgGoodsMapper.selectByExample(example);
                    //判断该商品是否有调价权限
                    if (CollectionUtils.isEmpty(priceOrgGoodsList)) {
                        message.setMessage("商品编码不存在或该商品无调价权限");
                        errorList.add(message);
                        continue;
                    }
                    PriceOrgGoods goods = priceOrgGoodsList.get(0);
                    //6.判断价格是否在调价区间
                    BigDecimal basePrice = goods.getBasePrice();
                    BigDecimal upAdjustPrice = goods.getUpAdjustRange();
                    BigDecimal downAdjustPrice = goods.getDownAdjustRange();
                    if (basePrice != null && upAdjustPrice != null && downAdjustPrice != null) {
                        BigDecimal basic = new BigDecimal("100");
                        BigDecimal comParePrice = new BigDecimal(item.getPrice()).multiply(basic);
                        BigDecimal upAdjustRange = basePrice.add(basePrice.multiply(upAdjustPrice.divide(basic)));
                        BigDecimal downAdjustRange = basePrice.subtract(basePrice.multiply(downAdjustPrice).divide(basic));
                        if (comParePrice.compareTo(downAdjustRange) == -1 || comParePrice.compareTo(upAdjustRange) == 1) {
                            message.setUpperLimit(upAdjustRange);
                            message.setLowerLimit(downAdjustRange);
                            message.setGoodsName(goods.getCurName());
                            message.setOrgName(goods.getOrgName());
                            message.setMessage("商品价格不在调价区间");
                            errorList.add(message);
                            continue;
                        }
                    }
                    //把上述条件都排除掉的放到缓存中供后续使用
                    orgGoodsCache.put(orgId + "_" + goodsNo, goods);
                    storeDetailCache.put(orgId + "_" + goodsNo + "_" + priceTypeCode, item);
                    rightList.add(orgId + "_" + goodsNo + "_" + priceTypeCode);
                }
                return rightList;
            };
        }
        return ThreadUtils.concurrentExecute(callables);
    }

    private Map<String, PriceType> getPriceTypeMap() {
        List<PriceDictionaryDTO> dictionaryDTOS = priceDictionaryService.getChildrenByCode(DictCodeEnum.PRICE_TYPE.getCode());
        if (CollectionUtils.isEmpty(dictionaryDTOS)) {
            throw new BusinessErrorException(ReturnCodeEnum.PRICE_TYPE_NOT_EXIST);
        }
        Map<String, PriceType> priceTypeMap = new HashMap<>(16);
        for (PriceDictionaryDTO dictionaryDTO : dictionaryDTOS) {
            priceTypeMap.put(dictionaryDTO.getDictCode(), PriceDictConverter.dictToPriceType(dictionaryDTO));
        }
        return priceTypeMap;
    }


    private Map<Long, OrgDTO> getOrgMap(List<Long> orgIdList) {
        logger.info("根据id集合批量获取组织机构信息,参数:{}", orgIdList);
        ResponseEntity<List<OrgDTO>> responseEntity;
        try {
            responseEntity = permissionService.listOrgInfoById(orgIdList, false);
        } catch (Exception e) {
            logger.error("根据id集合批量获取组织机构信息异常:", e);
            throw new BusinessException(ReturnCodeEnum.GET_AUTH_ERROR);
        }
        logger.info("根据id集合批量获取组织机构信息结束,查询结果:{}", responseEntity.getBody());
        List<OrgDTO> list = responseEntity.getBody();
        Map<Long, OrgDTO> map = new HashMap<>(16);
        for (OrgDTO dto : list) {
            map.put(dto.getId(), dto);
        }
        return map;
    }


    private void insertPriceStoreDetail(List<String> rightList,
                                        Map<String, ImportPriceStoreDetailDTO> detailCache,
                                        Map<String, PriceOrgGoods> orgGoodsCache,
                                        Map<String, PriceType> priceTypeMap,
                                        Map<Long, OrgDTO> orgMap,
                                        Integer errorSize,
                                        List<ImportPriceStoreDetailErrorDTO> errorList,
                                        String adjustCode,
                                        Set<Long> businessIdSet,
                                        Long loginUserId,
                                        String loginUserName) {
        //将过滤后正常列表的所有orgId获取到
        List<Long> orgIdList = rightList.stream().map(right -> Long.parseLong(right.split("_")[0])).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orgIdList)) {
            return;
        }
        //根据orgIdList，批量查询组织机构节点下指定类型组成的阉割版的树
        Map<Long, List<OrgTreeDTO>> treeMap = listOrgTreesByRootOrgIdAndTypesBatch(orgIdList);
        //上一步的机构树处理，拼接成必要的门店列表
        List<StoreDTO> storeDTOList = new ArrayList<>();
        Iterator<Map.Entry<Long, List<OrgTreeDTO>>> iterator = treeMap.entrySet().iterator();
        Map.Entry<Long, List<OrgTreeDTO>> entry;
        while (iterator.hasNext()) {
            entry = iterator.next();
            List<OrgTreeDTO> orgDTOList = entry.getValue();
            addStoreDTO(orgDTOList, storeDTOList);
        }
        logger.info("查询出所有的storeDTOList:{}", storeDTOList);
        if (CollectionUtils.isEmpty(storeDTOList)) {
            return;
        }
        List<Long> storeOrgIdList = storeDTOList.stream().map(storeOrg -> storeOrg.getOrgId()).collect(Collectors.toList());

        Map<String, OrgDTO> orgParentDTOMap = getDirectParentOrgList(storeOrgIdList);

        List<List<String>> subList = Lists.partition(rightList, 100);
        Callable[] callables = new Callable[subList.size()];

        for (int i = 0; i < subList.size(); i++) {
            List<String> stringList = subList.get(i);
            callables[i] = () -> {
                List<ImportPriceStoreDetailErrorDTO> partErrorList = new ArrayList<>();
                for (String complexId : stringList) {
                    ImportPriceStoreDetailDTO detailDTO = detailCache.get(complexId);
                    if (detailDTO == null) {
                        continue;
                    }
                    Boolean errorFlag = insert(detailDTO, storeDTOList, orgGoodsCache, orgParentDTOMap, priceTypeMap, orgMap, adjustCode, businessIdSet, loginUserId, loginUserName);
                    //如果更新或新增异常,将该条记录增加到错误列表中，错误数量加1
                    if (!errorFlag) {
                        ImportPriceStoreDetailErrorDTO detailErrorDTO = new ImportPriceStoreDetailErrorDTO();
                        BeanUtils.copyProperties(detailDTO, detailErrorDTO);
                        partErrorList.add(detailErrorDTO);
                    }
                }
                return partErrorList;
            };
        }
        List<List<ImportPriceStoreDetailErrorDTO>> list = ThreadUtils.concurrentExecute(callables);
        for (List<ImportPriceStoreDetailErrorDTO> errorDTOList : list) {
            errorList.addAll(errorDTOList);
            errorSize += errorDTOList.size();
        }
        logger.info("批量导入商品价格完成");
    }

    @Override
    public Boolean insertSync(PriceSyncInsertlParam priceSyncInsertlParam,
                              SpuNewVo spuNewVo, boolean isWarning
    ) {
        Boolean errorFlag = true;
        PriceStoreDetail priceStoreDetail;
        Long businessId = priceSyncInsertlParam.getBusinessId();
        Long storeId = priceSyncInsertlParam.getStoreId();
        String goodsNo = priceSyncInsertlParam.getGoodsNo();
        String priceTypeCode = "";

        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (null == priceTypeMap) {
            logger.info("海典同步处理 获取可用的价格列表异常null");
            return false;
        }
        if (priceSyncInsertlParam.getPriceType() == HDPriceTypeEnum.LSJ.getCode()) {
            priceTypeCode = PTypeEnum.LSJ.getCode();
        }
        if (priceSyncInsertlParam.getPriceType() == HDPriceTypeEnum.HYJ.getCode()) {
            priceTypeCode = PTypeEnum.HYJ.getCode();
        }
        if (priceSyncInsertlParam.getPriceType() == HDPriceTypeEnum.CLJ.getCode()) {
            priceTypeCode = PTypeEnum.CLJ.getCode();
        }
        if (priceSyncInsertlParam.getPriceType() == HDPriceTypeEnum.CHYJ.getCode()) {
            priceTypeCode = PTypeEnum.CHYJ.getCode();
        }
        PriceType priceType = priceTypeMap.get(priceTypeCode);
        if (null == priceType) {
            logger.info("海典同步处理 获取可用的价格列表后跟海典的值不匹配{}", priceSyncInsertlParam.getPriceType());
            return false;
        }


        BigDecimal price = new BigDecimal(priceSyncInsertlParam.getPrice()).setScale(PriceConstant.PRICE_SCALE, BigDecimal.ROUND_FLOOR).multiply(new BigDecimal("100"));

        //price = price.multiply(new BigDecimal("100"));

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .channelId(adjustPriceConfig.getDefaultChannelId())
            .priceTypeCode(priceTypeCode)
            .goodsNo(goodsNo)
            .build();
        List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(query);
        logger.info("海典同步处理 查询库中是否存在返回:{}", priceStoreDetailDbList);
        if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
            PriceStoreDetail priceStoreDetaildb = priceStoreDetailDbList.get(0);
            //进行价格对比时再判断价格是否一样
            if (priceSyncInsertlParam.getIsPriceComparison().equals(PriceComparisonEnum.COMPARE_PRICES.getCode())) {
                boolean isSpecial = StateUtils.hasState(priceStoreDetaildb.getPriceSign(), PriceSignEnum.IS_SPECIAL.getBitPos());
                int priceSpecial = 0;
                if (isSpecial) {
                    priceSpecial = 1;
                }
                if (price.compareTo(priceStoreDetaildb.getPrice()) == 0 && priceSyncInsertlParam.getIsSpecial() == priceSpecial) {
                    logger.info("海典同步处理 库里价格和传入的一样，不处理,priceSyncInsertlParam:{}", priceSyncInsertlParam);
                    return true;
                }
            }
//            if (null != priceStoreDetaildb.getSyncDate()
//                && priceStoreDetaildb.getSyncDate() > Long.parseLong(priceSyncInsertlParam.getSyncDate())) {
//                //时间戳效验 库里的比传入的大 证明此数据比较老，不处理
//                logger.info("海典同步处理 时间戳效验 库里的比传入的大 证明此数据比较老，不处理,priceSyncInsertlParam:{}", priceSyncInsertlParam);
//                return true;
//            }
        }

        priceStoreDetail = new PriceStoreDetail();
        //priceStoreDetail.setItemId(priceSyncInsertlParam.getItemId());
        priceStoreDetail.setPrice(price);
        priceStoreDetail.setOrgId(0L);
        priceStoreDetail.setOrgName("");
        priceStoreDetail.setLevel((byte) OrgLevelEnum.BUSINESS.getCode());//写死连锁
        priceStoreDetail.setAdjustCode("0");
        priceStoreDetail.setAdjustDetailId(11L);

        priceStoreDetail.setAuthOrgId(0L);
        priceStoreDetail.setAuthOrgName("0");
        priceStoreDetail.setAuthOrgLevel((byte) OrgLevelEnum.BUSINESS.getCode());//写死连锁
        priceStoreDetail.setOrgGoodsId(0L);
        priceStoreDetail.setCurName(spuNewVo.getCurName());
        priceStoreDetail.setBarCode(spuNewVo.getBarCode());
        priceStoreDetail.setOpCode(spuNewVo.getOpCode());
        priceStoreDetail.setExtend(spuNewVo.getCurName() + "_" + spuNewVo.getBarCode() + "_" + spuNewVo.getOpCode() + "_" + spuNewVo.getGoodsNo());
        priceStoreDetail.setSpuId(spuNewVo.getId() == null ? 0L : spuNewVo.getId());

        priceStoreDetail.setStoreId(storeId);
        //priceStoreDetail.setStoreName(priceSyncInsertlParam.getStoreName());
        priceStoreDetail.setGoodsNo(goodsNo);
        priceStoreDetail.setPriceTypeCode(priceType.getCode());
        priceStoreDetail.setPriceTypeId(priceType.getId());
        priceStoreDetail.setPriceTypeName(priceType.getName());

        priceStoreDetail.setBusinessId(businessId);
        //priceStoreDetail.setBusinessName(priceSyncInsertlParam.getBusinessName());
        priceStoreDetail.setSyncDate(Long.parseLong(priceSyncInsertlParam.getSyncDate()));
        priceStoreDetail.setUpdatedBy(0L);
        priceStoreDetail.setUpdatedByName("sync_" + priceSyncInsertlParam.getPriceSyncType());
        priceStoreDetail.setItemType(priceSyncInsertlParam.getItemType());
        priceStoreDetail.setStatus(DeleteStatusEnum.NORMAL.getCode().byteValue());

        Long changeSign = 0L;
        if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
            PriceStoreDetail priceStoreDetail1 = priceStoreDetailDbList.get(0);
            Long isSpecial = priceStoreDetail1.getPriceSign();
            changeSign = getPriceSign(priceSyncInsertlParam, isSpecial);
        } else {
            changeSign = getPriceSign(priceSyncInsertlParam, Long.valueOf(priceSyncInsertlParam.getIsSpecial()));
        }
        //保存特价状态
        priceStoreDetail.setPriceSign(changeSign);
        priceStoreDetail.setChannelId(adjustPriceConfig.getDefaultChannelId());

        try {
            priceService.savePrice(priceStoreDetail);
        } catch (Exception e) {
            errorFlag = false;
            logger.error("海典同步处理 保存价格异常异常", e);
        }

//        Integer syncType = null;
//
//        PriceSyncConfigDTO priceSyncConfigDTO = null;
//
//        if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
//            try {
//                PriceStoreDetail priceStoreDetaildb = priceStoreDetailDbList.get(0);
//                //价格浮动预警
//                if (isWarning) {
//                    priceSyncConfigDTO = priceSyncConfigService.getPriceConfigByBusinessId(businessId);
//                    logger.info("海典同步处理 该连锁设置价格预警返回：{},{}，{}", businessId, priceSyncInsertlParam.getBusinessName(), priceSyncConfigDTO);
//                    if (null == priceSyncConfigDTO) {
//                        logger.info("海典同步处理 该连锁没有设置价格预警：{},{}", businessId, priceSyncInsertlParam.getBusinessName());
//                        return false;
//                    }
//                    syncType = priceSyncConfigDTO.getSyncType();
//                    if (null != syncType && PriceSyncTypeEnum.NO_SYNC_HD.getCode() != syncType && priceSyncConfigDTO.isWarningSwitch()) {
//                        Integer warningFloatValue = priceSyncConfigDTO.getWarningFloatValue();
//                        if (null == warningFloatValue) {
//                            logger.info("海典同步处理 该连锁设置价格预警值为空：{},{}", businessId, priceSyncInsertlParam.getBusinessName());
//                            return false;
//                        }
//                        pushWarnRecord = syncWarningCompute(price, warningFloatValue, priceStoreDetaildb, priceStoreDetail);
//                    }
//                }
//                //乐观锁机制 取的是啥 修改的时候把这个条件带上 避免并发
//                criteria.andPriceEqualTo(priceStoreDetaildb.getPrice());
//                insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
//                logger.info("海典同步处理 更新priceStoreDetail表 goodsNo:{},priceType:{},price:{} 结果:{}",
//                    goodsNo, priceTypeCode, price, insertUpdateResult);
//
//                if (insertUpdateResult == 1 && pushWarnRecord) {
//                    try {
//                        //推预警清单 王璐
//                        pushWarnRecord(priceStoreDetaildb, price, priceSyncInsertlParam.getBusNo(),
//                            priceSyncInsertlParam.getCompId(), PSourceEnum.HDPOS.getCode(), PriceWarnTypeEnun.TJ.getCode());
//                    } catch (Exception e) {
//                        //推预警清单异常应该由priceWarnRecordService提供者保证方法执行完，这里先这么写着，等priceWarnRecordService的人改
//                        logger.error("海典同步处理  推预警清单异常异常", e);
//                    }
//                }
//            } catch (Exception e) {
//                errorFlag = false;
//                logger.error("海典同步处理 .updateByPrimaryKeySelective异常", e);
//            }
//        } else {
//            Long primaryKey = getPriceStoreDetailPk(storeId);
//            priceStoreDetail.setId(primaryKey);
//            priceStoreDetail.setCreatedBy(0L);
//            priceStoreDetail.setVersion(priceSyncInsertlParam.getVersion());
//            priceStoreDetail.setItemId(priceSyncInsertlParam.getItemId());
//            try {
//                insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
//                logger.info("海典同步处理 新增priceStoreDetail表.insertSelective,goodsNo:{},businessId:{}, storeId:{}, priceType:{},price:{},result:{}",
//                    goodsNo, priceStoreDetail.getBusinessId(), priceStoreDetail.getStoreId(),
//                    priceTypeCode, price, insertUpdateResult);
//            } catch (Exception e) {
//                errorFlag = false;
//                logger.error("海典同步处理 新增priceStoreDetail表 异常", e);
//            }
//        }
//        //更新或新增,将价格记录到变更历史中
//        logger.info("海典同步处理 将价格记录到变更历史中:{}", priceStoreDetail);
//        if (insertUpdateResult == 1 && errorFlag) {
//            insertPriceDetailHistory(priceStoreDetail);
//            pricePushCubeProducer.sendMq(priceStoreDetail);
//
//            //保存海典零售价到该连锁对应的渠道价格类型的库中，然后同步至线上渠道
//            if (priceSyncInsertlParam.getPriceType() == HDPriceTypeEnum.LSJ.getCode()
//                && !pushWarnRecord) {
//                syncHdLSJtoOnlineChannel(syncType, priceTypeMap, price, isWarning, priceSyncConfigDTO, priceStoreDetail, priceSyncInsertlParam);
//                //海典零售价到药急送中心店(连锁对应的虚拟门店)
//                priceSyncCenterStoreProducer.sendMq(priceTypeMap, isWarning, priceSyncConfigDTO, priceStoreDetail, priceSyncInsertlParam);
//            }
//        }


        return errorFlag;
    }

    private Long getPriceSign(PriceSyncInsertlParam priceSyncInsertlParam, Long isSpecial) {
        Long changeSign;
        if (priceSyncInsertlParam.getIsSpecial() != null && priceSyncInsertlParam.getIsSpecial() == 1) {
            changeSign = StateUtils.changeState(isSpecial, PriceSignEnum.IS_SPECIAL.getBitPos());
        } else {
            changeSign = StateUtils.changeState(isSpecial, PriceSignEnum.IS_SPECIAL.getBitPos(), false);
        }
        return changeSign;
    }

    @Override
    public boolean syncHdLSJtoGJYJSStep1(Map<String, PriceType> priceTypeMap, PriceSyncConfigDTO priceSyncConfigDTO,
                                         boolean isWarning, String priceTypeCode, PriceStoreDetail priceStoreDetail, PriceSyncInsertlParam priceSyncInsertlParam,
                                         long storeId) {

        PriceType priceType = JSON.parseObject(JSON.toJSONString(priceTypeMap.get(priceTypeCode)), PriceType.class);
        logger.info("价格同步到中心店 获取可用的价格列表的值{}", priceType);
        if (null == priceType) {
            logger.info("价格同步到中心店 获取可用的价格列表跟海典的值不匹配{}", priceSyncInsertlParam.getPriceType());
            return false;
        }
        //重置价格属性
        priceStoreDetail.setPriceTypeId(priceType.getId());
        priceStoreDetail.setPriceTypeName(priceType.getName());
        priceStoreDetail.setPriceTypeCode(priceTypeCode);

        BigDecimal price = new BigDecimal(priceSyncInsertlParam.getPrice());
        boolean errorFlag = true;
        boolean pushWarnRecord = false;
        int insertUpdateResult;


        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId);
        criteria.andGoodsNoEqualTo(priceStoreDetail.getGoodsNo());
        criteria.andPriceTypeCodeEqualTo(priceTypeCode);
        criteria.andChannelIdEqualTo(adjustPriceConfig.getDefaultChannelId());

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .goodsNo(priceStoreDetail.getGoodsNo())
            .priceTypeCode(priceTypeCode)
            .channelId(adjustPriceConfig.getDefaultChannelId())
            .build();

        if (Objects.nonNull(priceStoreDetail.getSkuId())) {
            criteria.andSkuIdEqualTo(priceStoreDetail.getSkuId());
            query.setSkuId(priceStoreDetail.getSkuId());
        }
        List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(query);
        if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
            try {
                PriceStoreDetail priceStoreDetaildb = priceStoreDetailDbList.get(0);
//                if (null != priceStoreDetaildb.getSyncDate()
//                    && priceStoreDetaildb.getSyncDate() > Long.parseLong(priceSyncInsertlParam.getSyncDate())) {
//                    //时间戳效验 库里的比传入的大 证明此数据比较老，不处理
//                    logger.info("价格同步到中心店{} 时间戳效验 库里的比传入的大 证明此数据比较老，不处理,priceSyncInsertlParam:{}", priceTypeCode, priceSyncInsertlParam);
//                    return true;
//                }
                //价格浮动预警
                logger.info("价格同步到中心店 isWarning:{}", isWarning);
                if (isWarning) {
                    logger.info("价格同步到中心店 isWarningSwitch:{}", priceSyncConfigDTO.isWarningSwitch());
                    if (priceSyncConfigDTO.isWarningSwitch()) {
                        Integer warningFloatValue = priceSyncConfigDTO.getWarningFloatValue();
                        logger.info("价格同步到中心店 warningFloatValue:{}", warningFloatValue);
                        pushWarnRecord = syncWarningCompute(price, warningFloatValue, priceStoreDetaildb, priceStoreDetail);
                        logger.info("价格同步到中心店 pushWarnRecord:{}", pushWarnRecord);
                    }
                }
                //乐观锁机制 取的是啥 修改的时候把这个条件带上 避免并发
                criteria.andPriceEqualTo(priceStoreDetaildb.getPrice());
                logger.info("价格同步到中心店  构造修改条件参数：{}：{}：{}：{}", storeId, priceStoreDetail.getGoodsNo(), priceTypeCode, priceStoreDetaildb.getPrice());
                priceStoreDetail.setId(null);
                priceStoreDetail.setSkuId(null);
                insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
                logger.info("价格同步到中心店{}  更新priceStoreDetail表 goodsNo:{},priceType:{},price:{} 结果:{}",
                    priceTypeCode, priceStoreDetail.getGoodsNo(), priceTypeCode, price, insertUpdateResult);
                if (insertUpdateResult == 1 && pushWarnRecord) {
                    try {
                        //推预警清单 王璐
                        pushWarnRecord(priceStoreDetaildb, price, priceSyncInsertlParam.getBusNo(),
                            priceSyncInsertlParam.getCompId(), PSourceEnum.HDPOS.getCode(), PriceWarnTypeEnun.TJ.getCode());
                    } catch (Exception e) {
                        //推预警清单异常应该由priceWarnRecordService提供者保证方法执行完，这里先这么写着，等priceWarnRecordService的人改
                        logger.error("价格同步到中心店{} .推预警清单异常异常", priceTypeCode, e);
                    }
                }
            } catch (Exception e) {
                errorFlag = false;
                logger.error("价格同步到中心店{}.updateByPrimaryKeySelective异常", priceTypeCode, e);
            }
        } else {
            Long primaryKey = getPriceStoreDetailPk(priceStoreDetail.getStoreId());
            priceStoreDetail.setId(primaryKey);
            priceStoreDetail.setCreatedBy(0L);
            priceStoreDetail.setVersion(priceSyncInsertlParam.getVersion());
            priceStoreDetail.setItemId(priceSyncInsertlParam.getItemId());
            try {
                insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
                logger.info("价格同步到中心店{} 新增priceStoreDetail表.insertSelective,goodsNo:{},priceType:{},price:{},result:{}",
                    priceTypeCode, priceStoreDetail.getGoodsNo(), priceTypeCode, price, insertUpdateResult);
            } catch (Exception e) {
                errorFlag = false;
                logger.error("价格同步到中心店{}  新增priceStoreDetail表 异常", priceTypeCode, e);
            }
        }
        //更新或新增,将价格记录到变更历史中
        //logger.info("价格同步到中心店{} 将价格记录到变更历史中:{}", priceStoreDetail);
        if (errorFlag) {
            insertPriceDetailHistory(priceStoreDetail);
            pricePushCubeProducer.sendMq(priceStoreDetail);
        }
        return errorFlag;
    }

    boolean syncHdLSJtoOnlineChannelStep1(Map<String, PriceType> priceTypeMap, BigDecimal price, PriceSyncConfigDTO priceSyncConfigDTO, boolean isWarning, String priceTypeCode, PriceStoreDetail priceStoreDetail, PriceSyncInsertlParam priceSyncInsertlParam) {

        PriceType priceType = priceTypeMap.get(priceTypeCode);
        if (null == priceType) {
            logger.info("获取可用的价格列表后跟海典的值不匹配{}", priceSyncInsertlParam.getPriceType());
            return false;
        }
        priceStoreDetail.setPriceTypeId(priceType.getId());
        priceStoreDetail.setPriceTypeName(priceType.getName());
        priceStoreDetail.setPriceTypeCode(priceTypeCode);

        long businessId = priceStoreDetail.getBusinessId();
        boolean errorFlag = true;
        boolean pushWarnRecord = false;
        int insertUpdateResult;
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(priceStoreDetail.getStoreId());
        criteria.andGoodsNoEqualTo(priceStoreDetail.getGoodsNo());
        criteria.andPriceTypeCodeEqualTo(priceTypeCode);
        criteria.andChannelIdEqualTo(adjustPriceConfig.getDefaultChannelId());

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(priceStoreDetail.getStoreId())
            .goodsNo(priceStoreDetail.getGoodsNo())
            .priceTypeCode(priceTypeCode)
            .channelId(adjustPriceConfig.getDefaultChannelId())
            .build();

        Long skuId = priceStoreDetail.getSkuId();
        if (Objects.nonNull(skuId)) {
            criteria.andSkuIdEqualTo(skuId);
            query.setSkuId(skuId);
        }

        List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(query);
        if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
            try {
                PriceStoreDetail priceStoreDetaildb = priceStoreDetailDbList.get(0);
//                if (null != priceStoreDetaildb.getSyncDate()
//                    && priceStoreDetaildb.getSyncDate() > Long.parseLong(priceSyncInsertlParam.getSyncDate())) {
//                    //时间戳效验 库里的比传入的大 证明此数据比较老，不处理
//                    logger.info("海典零售价到对应的渠道价{} 时间戳效验 库里的比传入的大 证明此数据比较老，不处理,priceSyncInsertlParam:{}", priceTypeCode, priceSyncInsertlParam);
//                    return true;
//                }
                //价格浮动预警
                if (isWarning) {
                    if (null == priceSyncConfigDTO) {
                        logger.info("海典零售价到对应的渠道价{} 该连锁没有设置价格预警：{},{}", priceTypeCode, businessId, priceSyncInsertlParam.getBusinessName());
                        return false;
                    }
                    if (priceSyncConfigDTO.isWarningSwitch()) {
                        Integer warningFloatValue = priceSyncConfigDTO.getWarningFloatValue();
                        if (null == warningFloatValue) {
                            logger.info("海典零售价到对应的渠道价{}  该连锁设置价格预警值为空：{},{}", priceTypeCode, businessId, priceSyncInsertlParam.getBusinessName());
                            return false;
                        }
                        pushWarnRecord = syncWarningCompute(price, warningFloatValue, priceStoreDetaildb, priceStoreDetail);
                    }
                }
                //乐观锁机制 取的是啥 修改的时候把这个条件带上 避免并发
                criteria.andPriceEqualTo(priceStoreDetaildb.getPrice());
                insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
                logger.info("海典零售价到对应的渠道价{}  更新priceStoreDetail表 goodsNo:{},priceType:{},price:{} 结果:{}",
                    priceTypeCode, priceStoreDetail.getGoodsNo(), priceTypeCode, price, insertUpdateResult);
                if (insertUpdateResult == 1 && pushWarnRecord) {
                    try {
                        //推预警清单 王璐
                        pushWarnRecord(priceStoreDetaildb, price, priceSyncInsertlParam.getBusNo(),
                            priceSyncInsertlParam.getCompId(), PSourceEnum.HDPOS.getCode(), PriceWarnTypeEnun.TJ.getCode());
                    } catch (Exception e) {
                        //推预警清单异常应该由priceWarnRecordService提供者保证方法执行完，这里先这么写着，等priceWarnRecordService的人改
                        logger.error("海典零售价到对应的渠道价{} .推预警清单异常异常", priceTypeCode, e);
                    }
                }
            } catch (Exception e) {
                errorFlag = false;
                logger.error("海典零售价到对应的渠道价{}.updateByPrimaryKeySelective异常", priceTypeCode, e);
            }
        } else {
            Long primaryKey = getPriceStoreDetailPk(priceStoreDetail.getStoreId());
            priceStoreDetail.setId(primaryKey);
            priceStoreDetail.setCreatedBy(0L);
            priceStoreDetail.setVersion(priceSyncInsertlParam.getVersion());


            priceStoreDetail.setItemId(priceSyncInsertlParam.getItemId());
            try {
                insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
                logger.info("海典零售价到对应的渠道价{} 新增priceStoreDetail表.insertSelective,goodsNo:{},priceType:{},price:{},result:{}",
                    priceTypeCode, priceStoreDetail.getGoodsNo(), priceTypeCode, price, insertUpdateResult);
            } catch (Exception e) {
                errorFlag = false;
                logger.error("海典零售价到对应的渠道价{}  新增priceStoreDetail表 异常", priceTypeCode, e);
            }
        }
        //更新或新增,将价格记录到变更历史中
        // logger.info("海典零售价到对应的渠道价{} 将价格记录到变更历史中:{}", priceStoreDetail);
        if (errorFlag) {
            insertPriceDetailHistory(priceStoreDetail);
            pricePushCubeProducer.sendMq(priceStoreDetail);
        }
        return errorFlag;
    }

    //保存海典零售价到该连锁对应的渠道价格类型的库中，然后同步至线上渠道
    void syncHdLSJtoOnlineChannel(Integer syncType, Map<String, PriceType> priceTypeMap, BigDecimal price, boolean isWarning, PriceSyncConfigDTO priceSyncConfigDTO,
                                  PriceStoreDetail priceStoreDetail, PriceSyncInsertlParam priceSyncInsertlParam) {
        //有第三方渠道
        if (StringUtils.isNotBlank(priceSyncInsertlParam.getChannelList())) {

            Long businessId = priceStoreDetail.getBusinessId();
            Long storeId = priceStoreDetail.getStoreId();

            String str = priceSyncInsertlParam.getChannelList();
            String[] channelList = str.split("#");
            for (String ss : channelList) {
                String priceTypeCode = "";
                if (OmsThirdChannelEnum.ORDER_CHANNEL_JD.getType().intValue() == Integer.parseInt(ss)) {
                    priceTypeCode = PTypeEnum.JDDJ.getCode();
                }
                if (OmsThirdChannelEnum.ORDER_CHANNEL_MT.getType().intValue() == Integer.parseInt(ss)) {
                    priceTypeCode = PTypeEnum.MTWM.getCode();
                }
                if (OmsThirdChannelEnum.ORDER_CHANNEL_EM.getType().intValue() == Integer.parseInt(ss)) {
                    priceTypeCode = PTypeEnum.ELM.getCode();
                }
                if (OmsThirdChannelEnum.ORDER_CHANNEL_MP.getType().intValue() == Integer.parseInt(ss)) {
                    priceTypeCode = PTypeEnum.GJYJS.getCode();
                    //临时方案药急送的类型给海龙推一下
                    PricePushYJSVo pricePushYJSVo = new PricePushYJSVo();
                    pricePushYJSVo.setPrice(price);
                    pricePushYJSVo.setBusinessId(businessId);
                    pricePushYJSVo.setStoreId(storeId);
                    pricePushYJSVo.setGoodsNo(priceStoreDetail.getGoodsNo());
                    pricePushYJSVo.setPriceType(priceTypeCode);
                    pricePushYJSPriceTypeToHLProducer.sendMq(pricePushYJSVo);
                }
                //第一步 保存海典零售价到该连锁对应的渠道价格类型的库中
                boolean rs = syncHdLSJtoOnlineChannelStep1(priceTypeMap, price, priceSyncConfigDTO, isWarning, priceTypeCode, priceStoreDetail, priceSyncInsertlParam);
                if (!rs) {
                    continue;
                }

                // 同步到线上渠道
                if (null != syncType && PriceSyncTypeEnum.SYNC_HD.getCode() == syncType && syncPriceChannelConfigProperties.isSyncPrice(priceStoreDetail.getBusinessId(), priceStoreDetail.getStoreId())) {
                    PriceSyncTaskGoodsVO vo = new PriceSyncTaskGoodsVO();
                    vo.setGoodsNo(priceStoreDetail.getGoodsNo());
                    vo.setPrice(price.longValue());
                    priceSyncTaskService.syncPriceToPlatform(businessId, storeId, Arrays.asList(vo), PTypeEnum.LSJ.getCode());
                }

            }
        }
    }


    @Override
    public void pushWarnRecord(PriceStoreDetail priceStoreDetaildb, BigDecimal newPrice, String busNo, String compId, Byte priceSource, Byte priceWarnType) {
        Date date = new Date();
        List<PriceWarningRecordDTO> recordDTOList = new ArrayList<>();
        PriceWarningRecordDTO priceWarningRecordDTO = new PriceWarningRecordDTO();
        priceWarningRecordDTO.setBusinessId(priceStoreDetaildb.getBusinessId());
        priceWarningRecordDTO.setStoreId(priceStoreDetaildb.getStoreId());
        priceWarningRecordDTO.setStoreName(priceStoreDetaildb.getStoreName());
        priceWarningRecordDTO.setGoodsNo(priceStoreDetaildb.getGoodsNo());
        priceWarningRecordDTO.setSkuName(priceStoreDetaildb.getCurName());
        priceWarningRecordDTO.setPriceType(PTypeEnum.getTypeByCode(priceStoreDetaildb.getPriceTypeCode()));
        priceWarningRecordDTO.setSrcPrice(priceStoreDetaildb.getPrice().longValue());
        priceWarningRecordDTO.setNewPrice(newPrice.longValue());
        priceWarningRecordDTO.setPriceSource(priceSource);
        priceWarningRecordDTO.setPriceWarnType(priceWarnType);
        priceWarningRecordDTO.setLastUpdateTime(date);
        priceWarningRecordDTO.setCreatedBy("-1");
        priceWarningRecordDTO.setGmtCreate(date);
        priceWarningRecordDTO.setUpdatedBy("-1");
        priceWarningRecordDTO.setGmtUpdate(date);
        priceWarningRecordDTO.setStatus((byte) 0);
        priceWarningRecordDTO.setVersion(0);
        priceWarningRecordDTO.setBusNO(busNo);
        priceWarningRecordDTO.setCompId(compId);
        recordDTOList.add(priceWarningRecordDTO);
        priceWarnRecordService.batchInsertAndUpdateStatus(recordDTOList);
    }

    private Boolean insert(ImportPriceStoreDetailDTO detailDTO,
                           List<StoreDTO> storeDTOList,
                           Map<String, PriceOrgGoods> orgGoodsCache,
                           Map<String, OrgDTO> orgParentDTOMap,
                           Map<String, PriceType> priceTypeMap,
                           Map<Long, OrgDTO> orgMap,
                           String adjustCode,
                           Set<Long> businessIdSet,
                           Long loginUserId,
                           String loginUserName) {
        int insertUpdateResult;
        Boolean errorFlag = true;

        PriceStoreDetail priceStoreDetail;
        Long businessId;
        for (StoreDTO storeDTO : storeDTOList) {
            //orgId表示调价权限机构ID，应该是Excel里的机构ID
            Long storeId = storeDTO.getId();
            if (storeId == null) {
                continue;
            }
            Long orgId = detailDTO.getOrgId();
            if (orgId == null) {
                continue;
            }
            Long storeOrgId = storeDTO.getOrgId();
            String goodsNo = detailDTO.getGoodsNo();
            String priceTypeCode = detailDTO.getPriceTypeCode();
            BigDecimal price = new BigDecimal(detailDTO.getPrice());
            price = price.multiply(new BigDecimal("100"));
            PriceOrgGoods goods = orgGoodsCache.get(detailDTO.getOrgId() + "_" + goodsNo);
            PriceType priceType = priceTypeMap.get(priceTypeCode);

            priceStoreDetail = new PriceStoreDetail();
            priceStoreDetail.setPrice(price);
            priceStoreDetail.setOrgId(orgId);
            priceStoreDetail.setOrgName(orgMap.get(orgId).getShortName());
            priceStoreDetail.setLevel(goods.getOrgLevel());
            priceStoreDetail.setAdjustCode(adjustCode);
            priceStoreDetail.setAdjustDetailId(11L);

            priceStoreDetail.setAuthOrgId((goods.getParentOrgId() != null && goods.getParentOrgId() == 0) ? goods.getOrgId() : goods.getParentOrgId());
            priceStoreDetail.setAuthOrgName((goods.getParentOrgId() != null && goods.getParentOrgId() == 0) ? goods.getOrgName() : goods.getParentOrgName());
            priceStoreDetail.setAuthOrgLevel(goods.getAdjustLevel());
            priceStoreDetail.setOrgGoodsId(goods.getId());
            priceStoreDetail.setCurName(goods.getCurName());
            priceStoreDetail.setBarCode(goods.getBarCode());
            priceStoreDetail.setOpCode(goods.getOpCode());
            priceStoreDetail.setExtend(goods.getCurName() + "_" + goods.getBarCode() + "_" + goods.getOpCode() + "_" + goods.getGoodsNo());
            priceStoreDetail.setSpuId(goods.getSpuId());

            priceStoreDetail.setStoreId(storeId);
            priceStoreDetail.setStoreName(storeDTO.getShortName());
            priceStoreDetail.setGoodsNo(goodsNo);
            priceStoreDetail.setPriceTypeCode(detailDTO.getPriceTypeCode());
            priceStoreDetail.setPriceTypeId(priceType == null ? 123L : priceType.getId());
            priceStoreDetail.setPriceTypeName(priceType == null ? "价格类型名称" : priceType.getName());
            OrgDTO businessOrgDTO = orgParentDTOMap.get("" + storeOrgId + OrgTypeEnum.BUSINESS.getCode());
            OrgDTO platformOrgDTO = orgParentDTOMap.get("" + storeOrgId + OrgTypeEnum.REGIONAL_PLATFORM.getCode());
            if (businessOrgDTO != null) {
                businessId = businessOrgDTO.getOutId();
                businessIdSet.add(businessId);
                priceStoreDetail.setBusinessId(businessId);
                priceStoreDetail.setBusinessName(businessOrgDTO.getShortName());
            }
            if (platformOrgDTO != null) {
                priceStoreDetail.setPlatformId(platformOrgDTO.getOutId());
                priceStoreDetail.setPlatformName(platformOrgDTO.getShortName());
            }
            priceStoreDetail.setUpdatedBy(null == loginUserId ? 0L : loginUserId);
            priceStoreDetail.setUpdatedByName(null == loginUserName ? "" : loginUserName);
            priceStoreDetail.setStatus(DeleteStatusEnum.NORMAL.getCode().byteValue());
            PriceStoreDetailExample example = new PriceStoreDetailExample();
            PriceStoreDetailExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(storeId);
            criteria.andGoodsNoEqualTo(goodsNo);
            criteria.andPriceTypeCodeEqualTo(priceTypeCode);

            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(storeId)
                .goodsNo(goodsNo)
                .priceTypeCode(priceTypeCode)
                .build();

            List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(query);

            //判断是否是第三方渠道价格类型
            boolean pushWarnRecord = false;
            boolean isThirdChannelPriceType = false;
            boolean warningSwitch = false;
            Integer syncType = null;
            Integer warningFloatValue = null;
            List cashChannelCodeList = PriceTypeUtil.thirdChannelPriceType;
            if (cashChannelCodeList.contains(priceTypeCode)) {
                isThirdChannelPriceType = true;
                PriceSyncConfigDTO priceSyncConfigDTO = priceSyncConfigService.getPriceConfigByBusinessId(priceStoreDetail.getBusinessId());
                logger.info("该连锁设置价格预警返回：{},{}，{}", priceStoreDetail.getBusinessId(), priceStoreDetail.getBusinessName(), priceSyncConfigDTO);
                if (null == priceSyncConfigDTO) {
                    logger.error("该连锁没有设置价格预警：{},{}", priceStoreDetail.getBusinessId(), priceStoreDetail.getBusinessName());
                    return false;
                }
                syncType = priceSyncConfigDTO.getSyncType();
                if (null != syncType && PriceSyncTypeEnum.NO_SYNC_HD.getCode() != syncType) {
                    warningSwitch = priceSyncConfigDTO.isWarningSwitch();
                    warningFloatValue = priceSyncConfigDTO.getWarningFloatValue();
                }

            }

            if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
                try {
                    PriceStoreDetail priceStoreDetaildb = priceStoreDetailDbList.get(0);
                    if (isThirdChannelPriceType) {
                        if (warningSwitch) {
                            if (null == warningFloatValue) {
                                logger.info("该连锁设置价格预警值为空：{},{}", priceStoreDetail.getBusinessId(), priceStoreDetail.getBusinessName());
                                return false;
                            }
                            pushWarnRecord = syncWarningCompute(price, warningFloatValue, priceStoreDetaildb, priceStoreDetail);
                        }
                    }
                    //乐观锁机制 取的是啥 修改的时候把这个条件带上 避免并发
                    criteria.andPriceEqualTo(priceStoreDetaildb.getPrice());
                    insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
                    logger.info("PriceStoreDetailServiceImpl.updateByExampleSelective,goodsNo:{},priceType:{},price:{},result:{}",
                        goodsNo, priceTypeCode, price, insertUpdateResult);
                    if (insertUpdateResult == 1 && pushWarnRecord) {
                        try {
                            //推预警清单 王璐
                            pushWarnRecord(priceStoreDetaildb, price, null,
                                null, PSourceEnum.PRICECENTER.getCode(), PriceWarnTypeEnun.TJ.getCode());
                        } catch (Exception e) {
                            //推预警清单异常应该由priceWarnRecordService提供者保证方法执行完，这里先这么写着，等priceWarnRecordService的人改
                            logger.error("PriceStoreDetailServiceImpl.推预警清单异常异常", e);
                        }
                    }
                } catch (Exception e) {
                    errorFlag = false;
                    logger.error("PriceStoreDetailServiceImpl.updateByExampleSelective", e);
                }
            } else {
                Long primaryKey = getPriceStoreDetailPk(storeId);
                priceStoreDetail.setId(primaryKey);
                priceStoreDetail.setCreatedBy(null == loginUserId ? 0L : loginUserId);
                priceStoreDetail.setVersion(PriceStoreDetailVersionEnum.SELF_CREATE.getCode());
                try {
                    insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
                    logger.info("PriceStoreDetailServiceImpl.insertSelective,goodsNo:{},priceType:{},price:{},result:{}",
                        goodsNo, priceTypeCode, price, insertUpdateResult);
                } catch (Exception e) {
                    errorFlag = false;
                    logger.error("PriceStoreDetailServiceImpl.insertSelective异常", e);
                }
            }

            //同步价格中台电商渠道价格至线上渠道
            if (errorFlag && !pushWarnRecord && null != syncType && PriceSyncTypeEnum.SYNC_CENTER.getCode() == syncType) {
                List<PriceSyncTaskGoodsVO> goodsVOS = new ArrayList<>();
                PriceSyncTaskGoodsVO priceSyncTaskGoodsVO = new PriceSyncTaskGoodsVO();
                priceSyncTaskGoodsVO.setGoodsNo(goodsNo);
                priceSyncTaskGoodsVO.setPrice(priceStoreDetail.getPrice().longValue());
                goodsVOS.add(priceSyncTaskGoodsVO);
                priceSyncTaskService.syncPriceToPlatform(priceStoreDetail.getBusinessId(), priceStoreDetail.getStoreId(), goodsVOS, priceTypeCode);
            }


            //临时方案药急送的类型给海龙推一下
            if (PTypeEnum.GJYJS.getCode().equals(priceTypeCode)
                || PTypeEnum.GJHIS.getCode().equals(priceTypeCode)
                || PTypeEnum.B2C.getCode().equals(priceTypeCode)) {

                PricePushYJSVo pricePushYJSVo = new PricePushYJSVo();
                pricePushYJSVo.setPrice(price);
                pricePushYJSVo.setPriceType(priceTypeCode);
                pricePushYJSVo.setBusinessId(priceStoreDetail.getBusinessId());
                pricePushYJSVo.setStoreId(storeId);
                pricePushYJSVo.setGoodsNo(priceStoreDetail.getGoodsNo());
                pricePushYJSPriceTypeToHLProducer.sendMq(pricePushYJSVo);
            }

            //更新或新增,将价格记录到变更历史中
            logger.info("priceStoreDetail:{}", priceStoreDetail);
            if (errorFlag) {
                insertPriceDetailHistory(priceStoreDetail);
                if (!pushWarnRecord) {
                    pricePushCubeProducer.sendMq(priceStoreDetail);
                }
            }
        }
        return errorFlag;
    }

    //王璐那边调用的，预警确认
    @Override
    public boolean confirmSync(PriceSyncInsertlParam priceSyncInsertlParam) {
        logger.info("{}价格同步确认 入参：{}", priceSyncInsertlParam.getGoodsNo(), priceSyncInsertlParam);
        try {

            PriceStoreDetailExample example = new PriceStoreDetailExample();
            example.createCriteria()
                .andStoreIdEqualTo(priceSyncInsertlParam.getStoreId())
                .andGoodsNoEqualTo(priceSyncInsertlParam.getGoodsNo())
                .andPriceTypeCodeEqualTo(priceSyncInsertlParam.getPriceTypeCode());

            List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(priceSyncInsertlParam.getStoreId(),
                priceSyncInsertlParam.getPriceTypeCode(),
                priceSyncInsertlParam.getGoodsNo());
            if (CollectionUtils.isEmpty(priceStoreDetailDbList)) {
                logger.error("{}价格同步确认 异常 库里没有对应记录", priceSyncInsertlParam.getGoodsNo());
                return false;
            }

            PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
            PriceStoreDetail priceStoreDetaildb = priceStoreDetailDbList.get(0);
            logger.info("{}价格同步确认 priceStoreDetaildb:{}", priceSyncInsertlParam.getGoodsNo(), priceStoreDetaildb);
            priceStoreDetail.setPrice(priceStoreDetaildb.getDsxPrice());

            int insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
            logger.info("{}价格同步确认 更新priceStoreDetail表  结果:{}", priceSyncInsertlParam.getGoodsNo(), insertUpdateResult);
            if (insertUpdateResult != 1) {
                logger.error("{}价格同步确认 异常 更新priceStoreDetail表失败  结果:{}", priceSyncInsertlParam.getGoodsNo(), insertUpdateResult);
                return false;
            }
            //判断是否是第三方渠道价格类型
            int syncType = 0;
            List cashChannelCodeList = PriceTypeUtil.thirdChannelPriceType;
            if (cashChannelCodeList.contains(priceSyncInsertlParam.getPriceTypeCode())) {
//            PriceSyncConfigDTO priceSyncConfigDTO = priceSyncConfigService.getPriceConfigByBusinessId(priceStoreDetaildb.getBusinessId());
//            logger.info("{}价格同步确认 该连锁设置价格预警返回：,{}，{}", priceSyncInsertlParam.getGoodsNo(), priceStoreDetaildb.getBusinessName(), priceSyncConfigDTO);
//            if (null == priceSyncConfigDTO) {
//                logger.error("{}价格同步确认 异常 该连锁没有设置价格预警：{}", priceSyncInsertlParam.getGoodsNo(), priceStoreDetaildb.getBusinessName());
//                return false;
//            }
//            syncType = priceSyncConfigDTO.getSyncType();
                //同步价格中台电商渠道价格至线上渠道
                logger.info("{}价格同步确认 电商渠道价格至线上渠道  priceTypeCode:{}", priceSyncInsertlParam.getGoodsNo(), priceSyncInsertlParam.getPriceTypeCode());
                List<PriceSyncTaskGoodsVO> goodsVOS = new ArrayList<>();
                PriceSyncTaskGoodsVO priceSyncTaskGoodsVO = new PriceSyncTaskGoodsVO();
                priceSyncTaskGoodsVO.setGoodsNo(priceSyncInsertlParam.getGoodsNo());
                priceSyncTaskGoodsVO.setPrice(priceStoreDetail.getPrice().longValue());
                goodsVOS.add(priceSyncTaskGoodsVO);
                priceSyncTaskService.syncPriceToPlatformByManual(priceStoreDetaildb.getBusinessId(), priceStoreDetaildb.getStoreId(), goodsVOS, priceSyncInsertlParam.getPriceTypeCode());

                logger.info("{}价格同步确认 临时方案药急送的类型给海龙推一下 判断条件:{}，{}，{}",
                    priceSyncInsertlParam.getGoodsNo(), PTypeEnum.GJYJS.getCode(), priceSyncInsertlParam.getPriceTypeCode(), PTypeEnum.GJYJS.getCode().equals(priceSyncInsertlParam.getPriceTypeCode()));
                //临时方案药急送的类型给海龙推一下
                PricePushYJSVo pricePushYJSVo = new PricePushYJSVo();
                pricePushYJSVo.setPrice(priceStoreDetail.getPrice());
                pricePushYJSVo.setBusinessId(priceStoreDetaildb.getBusinessId());
                pricePushYJSVo.setStoreId(priceStoreDetaildb.getStoreId());
                pricePushYJSVo.setGoodsNo(priceSyncInsertlParam.getGoodsNo());


                logger.info("{}价格同步确认 临时方案药急送的类型给海龙推一下  入参:{}", priceSyncInsertlParam.getGoodsNo(), pricePushYJSVo);
                if (PTypeEnum.GJYJS.getCode().equals(priceSyncInsertlParam.getPriceTypeCode())
                    || PTypeEnum.B2C.getCode().equals(priceSyncInsertlParam.getPriceTypeCode())
                    || PTypeEnum.GJHIS.getCode().equals(priceSyncInsertlParam.getPriceTypeCode())) {

                    pricePushYJSVo.setPriceType(priceSyncInsertlParam.getPriceTypeCode());
                    pricePushYJSPriceTypeToHLProducer.sendMq(pricePushYJSVo);
                }
            }

            priceStoreDetaildb.setPrice(priceStoreDetail.getPrice());
            pricePushCubeProducer.sendMq(priceStoreDetaildb);

        } catch (Exception e) {
            logger.error("{}价格同步确认 Exception:", priceSyncInsertlParam.getGoodsNo(), e);
        }
        logger.info("{}价格同步确认完成", priceSyncInsertlParam.getGoodsNo());
        return true;
    }

    @Override
    public int delPriceStoreDetailBuessIdIsNull() {
        int i = priceStoreDetailExMapper.delPriceStoreDetailBuessIdIsNull();
        logger.info("delPriceStoreDetailBuessIdIsNull,结果:{}", i);
        return i;
    }

    @Override
    public CommonResponse saveOrUpdateForHL(PriceSaveOrUpdateVO param) {
        logger.info("处理海龙推送过来的数据，入参:{}", param);
        Long businessId = param.getBusinessId();
        Long storeId = param.getStoreId();
        Long itemId = param.getItemId();
        String price = param.getPrice();
        String goodsNo = param.getGoodsNo();
        if (null == businessId || null == storeId || null == itemId
            || StringUtils.isBlank(price) || StringUtils.isBlank(goodsNo)) {
            return new CommonResponse(ReturnCodeEnum.PARAM_ERROR);
        }

        //取连锁名称
        ResponseEntity<List<OrgDTO>> responseEntity = permissionService.listOrgByOutId(OrgTypeEnum.BUSINESS.getCode(), Arrays.asList(businessId));
        logger.info("处理海龙推送过来的数据 {}调permission取连锁名称返回： {}", businessId, responseEntity);
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
            logger.error("处理海龙推送过来的数据 {}调权限系统取连锁异常： {}", businessId, responseEntity);
            return new CommonResponse(ReturnCodeEnum.CALL_PERMISSION_ERROR);
        }
        List<OrgDTO> list = responseEntity.getBody();
        if (CollectionUtils.isEmpty(list)) {
            logger.error("处理海龙推送过来的数据 {}调权限系统取连锁异常 list： {}", businessId, list);
            return new CommonResponse(ReturnCodeEnum.CALL_PERMISSION_ERROR);
        }
        OrgDTO orgDTO = list.get(0);
        String businessName = orgDTO.getShortName();
        //取门店名称
        responseEntity = permissionService.listOrgByOutId(OrgTypeEnum.STORE.getCode(), Arrays.asList(storeId));
        logger.info("处理海龙推送过来的数据 {}调permission取门店名称返回： {}", storeId, responseEntity);
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
            logger.error("处理海龙推送过来的数据 {}调权限系统取门店异常 ： {}", businessId, responseEntity);
            return new CommonResponse(ReturnCodeEnum.CALL_PERMISSION_ERROR);
        }
        list = responseEntity.getBody();
        if (CollectionUtils.isEmpty(list)) {
            logger.error("处理海龙推送过来的数据 {}调权限系统取门店异常 list： {}", businessId, list);
            return new CommonResponse(ReturnCodeEnum.CALL_PERMISSION_ERROR);
        }
        orgDTO = list.get(0);
        String storeName = orgDTO.getShortName();

        PriceSyncInsertlParam priceSyncInsertlParam = new PriceSyncInsertlParam();
        priceSyncInsertlParam.setBusinessId(businessId);
        priceSyncInsertlParam.setBusinessName(businessName);
        priceSyncInsertlParam.setStoreId(storeId);
        priceSyncInsertlParam.setStoreName(storeName);
        priceSyncInsertlParam.setGoodsNo(goodsNo);
        priceSyncInsertlParam.setPrice(price);
        priceSyncInsertlParam.setPriceType(HDPriceTypeEnum.LSJ.getCode());//写死，只有零售价过来
        priceSyncInsertlParam.setSyncDate(String.valueOf(System.currentTimeMillis()));
        priceSyncInsertlParam.setPriceSyncType(PriceSyncSelfTagEnum.HL_SAVE_UPDATE.getCode());
        priceSyncInsertlParam.setItemType(ItemTypeEnum.DEFAULT.getCode());
        priceSyncInsertlParam.setItemId(itemId);
        priceSyncInsertlParam.setVersion(PriceStoreDetailVersionEnum.HL_CREATE.getCode());
        List<Long> goodsNos = new ArrayList<>();
        goodsNos.add(Long.parseLong(goodsNo));
        SpuNewVo spuNewVo;
        try {
            logger.info("处理海龙推送过来的数据 {}调search入参： {},{}", goodsNo, businessId, goodsNos);
//            ResponseEntity<List<SpuNewVo>> rep = searchService.getNewSpuList(businessId, goodsNos);
            PageResult<SpuNewVo> rep = itemSearchEngineFacadeService.getNewSpuList(businessId, goodsNos);
            logger.info("处理海龙推送过来的数据 {}调search返回： {}", goodsNo, rep);
//            if (null == rep || CollectionUtils.isEmpty(rep.getBody()) || null == rep.getBody().get(0)) {
//                logger.error("处理海龙推送过来的数据 }调{}search异常了：{}", businessId, goodsNo);
//                return new CommonResponse(ReturnCodeEnum.CALL_SEARCH_ERROR);
//            }
            if (null == rep || CollectionUtils.isEmpty(rep.getRows()) || null == rep.getRows().get(0)) {
                logger.error("处理海龙推送过来的数据 }调{}search异常了：{}", businessId, goodsNo);
                return new CommonResponse(ReturnCodeEnum.CALL_SEARCH_ERROR);
            }
            spuNewVo = rep.getRows().get(0);
            Boolean rs = this.insertSync(priceSyncInsertlParam, spuNewVo, false);
            if (rs) {
                return new CommonResponse(rs);
            }
            logger.info("处理海龙推送过来的数据 {},{},处理结果：{}", goodsNo, businessId, rs);
        } catch (Exception e) {
            logger.error("处理海龙推送过来的数据 {}调{}search异常了 Exception：", businessId, goodsNo, e);
        }

        return new CommonResponse(false);
    }

    @Override
    public CommonResponse saveOrUpdateForHLByType(PriceSaveOrUpdateVO param) {

        logger.info("根据参数新增商品价格:{}", param);

        Long businessId = param.getBusinessId();
        Long storeId = param.getStoreId();
        Long itemId = param.getItemId();
        String price = param.getPrice();
        String goodsNo = param.getGoodsNo();
        BigDecimal priceDB = new BigDecimal(param.getPrice()).setScale(2, BigDecimal.ROUND_FLOOR).multiply(new BigDecimal("100"));
        if (null == businessId || null == storeId || null == itemId
            || StringUtils.isBlank(price) || StringUtils.isBlank(goodsNo)) {
            return new CommonResponse(ReturnCodeEnum.PARAM_ERROR);
        }


        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (null == priceTypeMap) {
            logger.info("海典同步处理 获取可用的价格列表异常{}", priceTypeMap);
            return new CommonResponse(ReturnCodeEnum.FAIL.getCode(), "价格列表异常");
        }
        PriceType priceType = priceTypeMap.get(param.getType());

        if (priceType == null) {
            return new CommonResponse(ReturnCodeEnum.FAIL.getCode(), "价格类型不存在");
        }

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .goodsNo(goodsNo)
            .priceTypeCode(param.getType())
            .channelId(adjustPriceConfig.getPushItemCenterChannelId())
            .build();

        List<PriceStoreDetail> priceStoreDetailList = priceStoreDetailReadService.query(query);
        if (CollectionUtils.isNotEmpty(priceStoreDetailList)) {
            //查询出来的价格信息
            PriceStoreDetail priceStoreDetail = priceStoreDetailList.get(0);

            //需要修改的
            PriceStoreDetail priceStoreDetailNew = new PriceStoreDetail();
            priceStoreDetailNew.setPrice(priceDB);

            PriceStoreDetailExample exampleNew = new PriceStoreDetailExample();
            exampleNew.createCriteria()
                .andStoreIdEqualTo(storeId)
                .andGoodsNoEqualTo(goodsNo)
                .andPriceTypeCodeEqualTo(priceStoreDetail.getPriceTypeCode())
                .andChannelIdEqualTo(adjustPriceConfig.getPushItemCenterChannelId());

            int result = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetailNew, exampleNew);

            logger.info("已经存在了,修改结果:{}", result);

            return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK);

        }

        SpuNewVo spuNewVo = new SpuNewVo();
//        List<Long> goodsNos = new ArrayList<>();
//        goodsNos.add(Long.parseLong(goodsNo));
        try {

//            ResponseEntity<List<SpuNewVo>> rep = searchService.getNewSpuList(businessId, goodsNos);
            spuNewVo = getSpuInfo(businessId, goodsNo);
            logger.info("调search入参 :{}, :{} :{} :{}", goodsNo, businessId, goodsNo, spuNewVo);
        } catch (Exception e) {
            logger.error("处理海龙推送过来的数据 {}调{}search异常了 Exception：", businessId, goodsNo, e);
        }

        PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
        priceStoreDetail.setItemId(param.getItemId());
        priceStoreDetail.setPrice(priceDB);
        priceStoreDetail.setOrgId(0L);
        priceStoreDetail.setOrgName("");
        priceStoreDetail.setLevel((byte) OrgLevelEnum.BUSINESS.getCode());
        priceStoreDetail.setAdjustCode("0");
        priceStoreDetail.setAdjustDetailId(11L);

        priceStoreDetail.setAuthOrgId(0L);
        priceStoreDetail.setAuthOrgName("0");
        priceStoreDetail.setAuthOrgLevel((byte) OrgLevelEnum.BUSINESS.getCode());
        priceStoreDetail.setOrgGoodsId(0L);
        priceStoreDetail.setCurName(spuNewVo.getCurName());
        priceStoreDetail.setBarCode(spuNewVo.getBarCode());
        priceStoreDetail.setOpCode(spuNewVo.getOpCode());
        priceStoreDetail.setExtend(spuNewVo.getCurName() + "_" + spuNewVo.getBarCode() + "_" + spuNewVo.getOpCode() + "_" + spuNewVo.getGoodsNo());
        priceStoreDetail.setSpuId(spuNewVo.getId() == null ? 0L : spuNewVo.getId());

        priceStoreDetail.setStoreId(storeId);
        priceStoreDetail.setGoodsNo(goodsNo);
        priceStoreDetail.setPriceTypeCode(priceType.getCode());
        priceStoreDetail.setPriceTypeId(priceType.getId());
        priceStoreDetail.setPriceTypeName(priceType.getName());

        priceStoreDetail.setBusinessId(businessId);
        priceStoreDetail.setSyncDate(System.currentTimeMillis());
        priceStoreDetail.setUpdatedBy(0L);
        priceStoreDetail.setUpdatedByName("sync_HL_" + param.getType());
        priceStoreDetail.setItemType(ItemTypeEnum.DEFAULT.getCode());
        priceStoreDetail.setStatus(DeleteStatusEnum.NORMAL.getCode().byteValue());
        priceStoreDetail.setCreatedBy(0L);


        int result = priceStoreDetailMapper.insertSelective(priceStoreDetail);
        logger.info("同步海龙成功结果:{}", result);

        PriceSyncTaskVO priceSyncTaskVO = new PriceSyncTaskVO();
        priceSyncTaskVO.setBusinessId(businessId);
        priceSyncTaskVO.setStoreId(storeId);
        priceSyncTaskVO.setGoodsNo(goodsNo);
        priceSyncTaskVO.setType(PriceSyncTaskTypeEnum.SYNC_TASK_SUPPLEMENT_INFO.getType());
        priceSyncTaskVO.setPriceTypeCode(priceType.getCode());
        priceSyncTaskProducer.sendPriceSyncTask(priceSyncTaskVO);

        return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK);
    }

    /**
     * 是否有价格转换
     *
     * @param storeId
     * @return
     */
    private boolean isHasPriceTypeTransformation(Long storeId) {
        logger.info("根据门店ID做价格类型转换:{} Apollo配置项:{}", storeId, transformationBusinessId);

        if (storeId == null || org.apache.commons.lang3.StringUtils.isBlank(transformationBusinessId)) {
            return false;
        }

        String storeIdLast5 = "";
        List<String> businessIdLast5List = new ArrayList<>();
        try {

            storeIdLast5 = IdUtils.getLastNumber(storeId + "", 5);
            String[] businessIdArr = transformationBusinessId.split(",");

            for (String str : businessIdArr) {
                if (str == null) {
                    continue;
                }
                businessIdLast5List.add(IdUtils.getLastNumber(str + "", 5));
            }
        } catch (Exception e) {
            logger.info("转换需要查询的门店后五位或者连锁配置异常:", e);
            return false;
        }


        if (org.apache.commons.lang3.StringUtils.isBlank(storeIdLast5) || CollectionUtils.isEmpty(businessIdLast5List)) {
            logger.info("查询的门店后五位或者连锁配置为空");
            return false;
        }
        String finalStoreIdLast = storeIdLast5;
        boolean match = businessIdLast5List.stream().anyMatch(item -> item.equals(finalStoreIdLast));
        if (match) {
            StoreBriefDTO storeDTOByStoreId = feignStoreService.getStoreDTOByStoreId(storeId);
            String realBusinessIdLast = IdUtils.getLastNumber(storeDTOByStoreId.getBusinessId() + "", 5);
            match = businessIdLast5List.stream().anyMatch(item -> item.equals(realBusinessIdLast));
        }
        return match;

    }

    /**
     * 保存组合商品信息
     *
     * @params: * @param null
     * @Author: fwyang
     * @Description:
     * @version: 1.0
     * @Date: 17:25 2020/5/7
     */
    @Override
    public void saveGoodsOfJointInfo(GoodsOfJointVo vo) {
        BigDecimal price = new BigDecimal(BigDecimalUtils.convertFenByYuan(vo.getPrice()));

        Date date = new Date();
        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = getPriceTypeMap();
        PriceType priceType = priceTypeMap.get(PTypeEnum.GJYJS.getCode());

        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(vo.getStoreId());
        criteria.andGoodsNoEqualTo(vo.getGoodsNo());
        //criteria.andSpuIdEqualTo(vo.getSpuId());
        criteria.andPriceTypeCodeEqualTo(PTypeEnum.GJYJS.getCode());

        List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(vo.getStoreId(), PTypeEnum.GJYJS.getCode(), vo.getGoodsNo());

        if (CollectionUtils.isEmpty(priceStoreDetailDbList)) {
            PriceStoreDetail priceStoreDetail = new PriceStoreDetail();

            Long primaryKey = getPriceStoreDetailPk(vo.getStoreId());
            priceStoreDetail.setId(primaryKey);
            priceStoreDetail.setStoreId(vo.getStoreId());
            priceStoreDetail.setStoreName(vo.getStoreName());
            priceStoreDetail.setSpuId(vo.getSpuId());
            priceStoreDetail.setGoodsNo(vo.getGoodsNo());
            priceStoreDetail.setCurName(vo.getItemName());
            priceStoreDetail.setPriceTypeCode(priceType.getCode());
            priceStoreDetail.setPriceTypeId(priceType.getId());
            priceStoreDetail.setPriceTypeName(priceType.getName());
            priceStoreDetail.setPrice(price);
            priceStoreDetail.setAuthOrgId(0L);
            priceStoreDetail.setAuthOrgName("0");
            priceStoreDetail.setAuthOrgLevel((byte) OrgLevelEnum.BUSINESS.getCode());//写死连锁
            priceStoreDetail.setOrgId(0L);
            priceStoreDetail.setOrgName("");
            priceStoreDetail.setLevel((byte) OrgLevelEnum.BUSINESS.getCode());//写死连锁
            priceStoreDetail.setBusinessId(vo.getBusinessId());
            priceStoreDetail.setBusinessName(vo.getBusinessName());
            priceStoreDetail.setAdjustCode("0");
            priceStoreDetail.setAdjustDetailId(11L);
            priceStoreDetail.setOrgGoodsId(0L);
            priceStoreDetail.setStatus(DeleteStatusEnum.NORMAL.getCode().byteValue());
            priceStoreDetail.setGmtCreate(date);
            priceStoreDetail.setGmtUpdate(date);

            priceStoreDetail.setItemType(ItemTypeEnum.DEFAULT.getCode());
            priceStoreDetail.setItemId(vo.getItemId());
            priceStoreDetail.setVersion(PriceStoreDetailVersionEnum.HL_CREATE.getCode());

            priceStoreDetail.setCreatedBy(0L);
            ;
            priceStoreDetail.setUpdatedBy(0L);

            try {
                int insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
                logger.info("组合商品处理 新增priceStoreDetail表.insertSelective,goodsNo:{},priceType:{},price:{},result:{}",
                    priceStoreDetail.getGoodsNo(), priceStoreDetail.getPriceTypeCode()
                    , price, insertUpdateResult);

                //发送 mq 同步 cube
                pricePushCubeProducer.sendMq(priceStoreDetail);

            } catch (Exception e) {
                logger.error("组合商品处理 新增priceStoreDetail表 异常", e);
            }
        } else {
            PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
            priceStoreDetail.setId(priceStoreDetailDbList.get(0).getId());
            priceStoreDetail.setPrice(price);
            priceStoreDetail.setStoreId(vo.getStoreId());
            priceStoreDetail.setBusinessId(vo.getBusinessId());
            priceStoreDetail.setCurName(vo.getItemName());
            priceStoreDetail.setItemId(vo.getItemId());
            priceStoreDetail.setGmtUpdate(date);
            try {
                int insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
                logger.info("组合商品处理 更新priceStoreDetail表.insertSelective,goodsNo:{},priceType:{},price:{},result:{}",
                    priceStoreDetail.getGoodsNo(), priceStoreDetail.getPriceTypeCode()
                    , price, insertUpdateResult);

                //发送 mq 同步 cube
                pricePushCubeProducer.sendMq(priceStoreDetail);

            } catch (Exception e) {
                logger.error("组合商品处理 更新priceStoreDetail表 异常", e);
            }
        }

    }

    /**
     * 成本价转移数据
     *
     * @params: * @param null
     * @Author: fwyang
     * @Description:
     * @version: 1.0
     * @Date: 17:25 2020/5/7
     */
    @Override
    public void addDetailForStoreCost(StoreCostDTO vo, SpuNewVo spuNewVo) {

        Date date = new Date();

        int insertUpdateResult = 0;
        Boolean errorFlag = true;
        boolean pushWarnRecord = false;
        PriceStoreDetail priceStoreDetail;
        Long businessId = vo.getBusinessId();
        Long storeId = vo.getStoreId();
        String goodsNo = vo.getItemCode();
        String priceTypeCode = PTypeEnum.CBJ.getCode();

        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (null == priceTypeMap) {
            logger.info("成本价转移数据 获取可用的价格列表异常null");
            return;
        }
        PriceType priceType = priceTypeMap.get(priceTypeCode);
        if (null == priceType) {
            logger.info("成本价转移数据 获取可用的价格列表后跟海典的值不匹配{}", PTypeEnum.CBJ.getCode());
            return;
        }

        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId);
        criteria.andGoodsNoEqualTo(goodsNo);
        criteria.andPriceTypeCodeEqualTo(priceTypeCode);
        List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(storeId, priceTypeCode, goodsNo);
        logger.info("成本价转移数据 查询库中是否存在返回:{}", priceStoreDetailDbList);

        priceStoreDetail = new PriceStoreDetail();
        priceStoreDetail.setPrice(vo.getAveragePrice());
        priceStoreDetail.setOrgId(0L);
        priceStoreDetail.setOrgName("");
        //写死连锁
        priceStoreDetail.setLevel((byte) OrgLevelEnum.BUSINESS.getCode());
        priceStoreDetail.setAdjustCode("0");
        priceStoreDetail.setAdjustDetailId(11L);

        priceStoreDetail.setAuthOrgId(0L);
        priceStoreDetail.setAuthOrgName("0");
        //写死连锁
        priceStoreDetail.setAuthOrgLevel((byte) OrgLevelEnum.BUSINESS.getCode());
        priceStoreDetail.setOrgGoodsId(0L);
        priceStoreDetail.setCurName(spuNewVo.getCurName());
        priceStoreDetail.setBarCode(spuNewVo.getBarCode());
        priceStoreDetail.setOpCode(spuNewVo.getOpCode());
        priceStoreDetail.setExtend(spuNewVo.getCurName() + "_" + spuNewVo.getBarCode() + "_" + spuNewVo.getOpCode() + "_" + spuNewVo.getGoodsNo());
        priceStoreDetail.setSpuId(spuNewVo.getId());

        priceStoreDetail.setStoreId(storeId);
        priceStoreDetail.setStoreName(vo.getStoreName());
        priceStoreDetail.setGoodsNo(goodsNo);
        priceStoreDetail.setPriceTypeCode(priceType.getCode());
        priceStoreDetail.setPriceTypeId(priceType.getId());
        priceStoreDetail.setPriceTypeName(priceType.getName());

        priceStoreDetail.setBusinessId(businessId);
        priceStoreDetail.setUpdatedBy(0L);
        priceStoreDetail.setItemId(0L);
        priceStoreDetail.setItemType(ItemTypeEnum.DEFAULT.getCode());
        priceStoreDetail.setStatus(DeleteStatusEnum.NORMAL.getCode().byteValue());
        priceStoreDetail.setBusinessName(vo.getBusinessName());

        Integer syncType = null;

        PriceSyncConfigDTO priceSyncConfigDTO = null;

        if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
            try {
                PriceStoreDetail priceStoreDetaildb = priceStoreDetailDbList.get(0);

                //乐观锁机制 取的是啥 修改的时候把这个条件带上 避免并发
                criteria.andPriceEqualTo(priceStoreDetaildb.getPrice());
                insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
                logger.info("成本价转移数据 更新priceStoreDetail表 goodsNo:{},priceType:{},price:{} 结果:{}",
                    goodsNo, priceTypeCode, vo.getAveragePrice(), insertUpdateResult);

            } catch (Exception e) {
                errorFlag = false;
                logger.error("成本价转移数据 .updateByPrimaryKeySelective异常", e);
            }
        } else {
            Long primaryKey = getPriceStoreDetailPk(storeId);
            priceStoreDetail.setId(primaryKey);
            priceStoreDetail.setCreatedBy(0L);
            priceStoreDetail.setVersion(PriceStoreDetailVersionEnum.HL_CREATE.getCode());
            priceStoreDetail.setItemId(vo.getItemId());
            try {
                insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
                logger.info("成本价转移数据 新增priceStoreDetail表.insertSelective,goodsNo:{},priceType:{},price:{},result:{}",
                    goodsNo, priceTypeCode, vo.getAveragePrice(), insertUpdateResult);
            } catch (Exception e) {
                errorFlag = false;
                logger.error("成本价转移数据 新增priceStoreDetail表 异常", e);
            }
        }

    }

    /**
     * 同步成本价消息
     * @param vo
     */
    @Override
    public void addItemCostPrice(StoreCostDTO vo) {
        String priceTypeCode = vo.getPriceType().getCode();
        Long priceTypeId = vo.getPriceType().getId();
        String priceTypeName = vo.getPriceType().getName();
        List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(vo.getStoreId(), priceTypeCode, vo.getItemCode());
        logger.info("同步成本价消息 查询库中是否存在返回:{}", priceStoreDetailDbList.size());

        PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
        //关键必填项
        priceStoreDetail.setBusinessId(vo.getBusinessId());
        priceStoreDetail.setStoreId(vo.getStoreId());
        priceStoreDetail.setGoodsNo(vo.getItemCode());
        priceStoreDetail.setPriceTypeCode(priceTypeCode);
        priceStoreDetail.setPriceTypeId(priceTypeId);
        priceStoreDetail.setPriceTypeName(priceTypeName);
        priceStoreDetail.setPrice(vo.getAveragePrice());

        //补全其他必填项 默认值
        priceStoreDetail.setSpuId(0L);
        priceStoreDetail.setAuthOrgId(0L);
        priceStoreDetail.setAuthOrgName("def");
        priceStoreDetail.setAuthOrgLevel((byte) 0);
        priceStoreDetail.setOrgId(0L);
        priceStoreDetail.setOrgName("def");
        priceStoreDetail.setLevel((byte) 0);
        priceStoreDetail.setAdjustCode("0");
        priceStoreDetail.setAdjustDetailId(0L);
        priceStoreDetail.setOrgGoodsId(0L);
        priceStoreDetail.setStatus(DeleteStatusEnum.NORMAL.getCode().byteValue());
        priceStoreDetail.setVersion(0);
        priceStoreDetail.setCreatedBy(0L);

        Integer insertUpdateResult;

        if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
            PriceStoreDetailExample example = new PriceStoreDetailExample();
            PriceStoreDetailExample.Criteria criteria = example.createCriteria();
            criteria.andStoreIdEqualTo(vo.getStoreId());
            criteria.andGoodsNoEqualTo(vo.getItemCode());
            criteria.andPriceTypeCodeEqualTo(priceTypeCode);
            PriceStoreDetail existData = priceStoreDetailDbList.get(0);
            //乐观锁机制 取的是啥 修改的时候把这个条件带上 避免并发
            criteria.andPriceEqualTo(existData.getPrice());
            insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
            logger.info("同步成本价消息 更新priceStoreDetail表 goodsNo:{},store:{},price:{} 结果:{}",
                vo.getItemCode(), vo.getStoreId(), vo.getAveragePrice().toString(), insertUpdateResult);

        } else {
            Long primaryKey = getPriceStoreDetailPk(vo.getStoreId());
            priceStoreDetail.setId(primaryKey);
            insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
            logger.info("同步成本价消息 新增priceStoreDetail表 goodsNo:{},store:{},price:{} 结果:{}",
                vo.getItemCode(), vo.getStoreId(), vo.getAveragePrice().toString(), insertUpdateResult);

        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePriceStoreDetail(PriceStoreDetail priceStoreDetail) {
        priceStoreDetailMapper.updateByPrimaryKey(priceStoreDetail);
        String extend1 = priceStoreDetail.getExtend1();
        if (StringUtils.isNotBlank(extend1)) {
            PriceDetailHistory priceDetailHistory = new PriceDetailHistory();
            BeanUtils.copyProperties(priceStoreDetail, priceDetailHistory);
            priceDetailHistory.setStatus(new Byte(StatusEnum.NORMAL.getCode() + ""));
            priceDetailHistory.setPrice(null);
            priceDetailHistory.setGmtCreate(new Date());
            priceDetailHistory.setGmtUpdate(new Date());
            priceDetailHistory.setId(null);
            priceDetailHistory.setExtend(extend1);
            priceDetailHistory.setUpdatedByName("管控单将门店价格清空");
            priceDetailHistoryMapper.insert(priceDetailHistory);
        }

    }

    @Override
    public Optional<BigDecimal> getPriceByGoodsAndStoreAndPriceType(String goodsNo, Long storeId, String priceTypeCode,
                                                                    List<Integer> channelIdList) {
        logger.info("<===[PriceStoreDetailServiceImpl.getPriceByGoodsAndStoreAndPriceType] goodsNo: {}，storeId：{},  priceTypeCode: {}",
            goodsNo, storeId, priceTypeCode);
        Optional<BigDecimal> result = Optional.empty();
        if (goodsNo == null || storeId == null || priceTypeCode == null || CollectionUtils.isEmpty(channelIdList)) {
            return result;
        }

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .goodsNo(goodsNo)
            .storeId(storeId)
            .channelIdList(channelIdList)
            .priceTypeCode(priceTypeCode)
            .status(StatusEnum.NORMAL.getCode())
            .priceIsNotNull(true)
            .build();
        ;
        List<PriceStoreDetail> priceStoreDetailList = priceStoreDetailReadService.query(query);
        if (CollectionUtils.isNotEmpty(priceStoreDetailList) && priceStoreDetailList.get(0).getPrice() != null) {
            result = priceStoreDetailList.stream().map(PriceStoreDetail::getPrice).min(BigDecimal::compareTo);
        }
        logger.info("<===[PriceStoreDetailServiceImpl.getPriceByGoodsAndStoreAndPriceType] result: {}", result);
        return result;
    }

    @Override
    public List<PriceQueryDTO> getPriceByStoreIdAndRelateNo(PriceQueryParam param) {
        List<PriceQueryDTO> allPriceQueryDTO = new ArrayList<>();

        if (param == null || (param.getStoreId() == null && param.getBusinessId() == null) || param.getQueryNoList() == null) {
            return allPriceQueryDTO;
        }

        List<PriceQueryDetailParam> queryDetailParamList = param.getQueryNoList();

        List<String> goodsNoList = queryDetailParamList.stream().map(query -> query.getGoodsNo()).collect(Collectors.toList());
        List<PriceQueryDetailParam> PriceQueryDetailParamNotSpuId = queryDetailParamList.stream().filter(query -> query.getSpuId() != null).collect(Collectors.toList());
        List<Long> spuIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(PriceQueryDetailParamNotSpuId)) {
            spuIdList = queryDetailParamList.stream().map(query -> query.getSpuId()).collect(Collectors.toList());
        }

        Long storeId = param.getStoreId();
        Long businessId = param.getBusinessId();

        PriceStoreDetailQuery queryByGoodsNo = PriceStoreDetailQuery.builder().build();
        PriceStoreDetailQuery queryBySpuId = PriceStoreDetailQuery.builder().build();

        if (businessId != null && businessId != 0) {
            queryByGoodsNo.setBusinessId(businessId);
            queryBySpuId.setBusinessId(businessId);
        }
        if (storeId != null && storeId != 0) {
            queryByGoodsNo.setStoreId(storeId);
            queryBySpuId.setStoreId(storeId);
        }
        List<PriceStoreDetail> goodsNoDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            queryByGoodsNo.setGoodsNoList(goodsNoList);
            queryByGoodsNo.setChannelId(param.getChannelId());
            goodsNoDetailList = priceStoreDetailReadService.query(queryByGoodsNo);
        }
        List<PriceStoreDetail> spuIdDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spuIdList)) {
            queryBySpuId.setSpuIdList(spuIdList);
            queryBySpuId.setChannelId(param.getChannelId());
            spuIdDetailList = priceStoreDetailReadService.query(queryBySpuId);
        }

        for (PriceQueryDetailParam detailParam : queryDetailParamList) {
            PriceQueryDTO queryDTO = new PriceQueryDTO();
            List<PriceDTO> priceDTOList = new ArrayList<>();

            Long spuId = detailParam.getSpuId();
            String goodsNo = detailParam.getGoodsNo();
            PriceStoreDetail tempDetail = new PriceStoreDetail();
            if (StringUtils.isNotBlank(goodsNo)) {
                for (PriceStoreDetail detail : goodsNoDetailList) {
                    if (goodsNo.equals(detail.getGoodsNo())) {
                        tempDetail = detail;
                        PriceDTO priceDTO = new PriceDTO();
                        BeanUtils.copyProperties(detail, priceDTO);
                        priceDTO.setCode(detail.getPriceTypeCode());
                        queryDTO.setIsMaintain(detail.getIsMaintain());
                        priceDTO.setGoodsNo(goodsNo);
                        priceDTOList.add(priceDTO);
                    }
                }
            } else if (StringUtils.isBlank(goodsNo) && spuId != null) {
                for (PriceStoreDetail detail : spuIdDetailList) {
                    if (spuId.equals(detail.getSpuId())) {
                        tempDetail = detail;
                        PriceDTO priceDTO = new PriceDTO();
                        BeanUtils.copyProperties(detail, priceDTO);
                        priceDTO.setCode(detail.getPriceTypeCode());
                        queryDTO.setIsMaintain(detail.getIsMaintain());
                        priceDTOList.add(priceDTO);
                    }
                }
            }
            storeId = tempDetail.getStoreId() == null ? 0L : tempDetail.getStoreId();
            spuId = tempDetail.getSpuId() == null ? 0L : tempDetail.getSpuId();
            goodsNo = StringUtils.isBlank(tempDetail.getGoodsNo()) ? "0" : tempDetail.getGoodsNo();

            queryDTO.setKey(storeId + "_" + spuId + "_" + goodsNo);
            queryDTO.setGoodsNo(goodsNo);
            queryDTO.setPriceDTOList(priceDTOList);
            allPriceQueryDTO.add(queryDTO);
            logger.info("获取价格查询结果:{}", allPriceQueryDTO);
        }
        return allPriceQueryDTO;
    }
}
