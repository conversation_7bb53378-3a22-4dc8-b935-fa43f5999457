package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.vo.OrgTreeVO;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mq.producer.StoreGroupChangeProducer;
import com.cowell.pricecenter.service.PriceMdmDataService;
import com.cowell.pricecenter.service.StoreGroupService;
import com.cowell.pricecenter.service.dto.OrgInfoDTO;
import com.cowell.pricecenter.service.dto.StoreGroupDetailDTO;
import com.cowell.pricecenter.service.dto.StoreGroupImportDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.cowell.pricecenter.service.feign.TagFeignService;
import com.cowell.pricecenter.service.feign.facade.TagFeignFacadeService;
import com.cowell.pricecenter.utils.HutoolExcelUtils;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StoreGroupServiceImpl implements StoreGroupService {

    private final Logger logger = LoggerFactory.getLogger(StoreGroupServiceImpl.class);

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private TagFeignService tagFeignService;

    @Autowired
    private TagFeignFacadeService tagFeignFacadeService;

    @Autowired
    private StoreGroupChangeProducer storeGroupChangeProducer;

    @Override
    public PageResponse<List<StoreGroupDTO>> getStoreGroupList(Long userId, StoreGroupQueryParam param) throws Exception {
        try {
            Optional.ofNullable(param).orElseThrow(() -> new BusinessErrorException("查询条件不能为空"));
            PageResponse<List<StoreGroupDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPage(param.getPage());
            pageResponse.setPageSize(param.getPageSize());
            pageResponse.setCode(HttpStatus.OK.value());
            pageResponse.setResult(new ArrayList<>());
            if(Objects.isNull(param.getPage()) || Objects.isNull(param.getPageSize())){
                param.setPage(0);
                param.setPageSize(10);
            }
            if(param.getPage() < 0 || param.getPageSize() <= 0){
                param.setPage(0);
                param.setPageSize(10);
            }
            if (PriceMdmDataService.SYS_USER_MDM_SENDER.equals(param.getCreatedName())) {
                userId=PriceMdmDataService.SYS_USER_MDM_SENDER_USER_ID;
            }
            Long finalUserId = userId;
            List<StoreGroupDTO> groupDTOS = new ArrayList<>();
             if (Objects.nonNull(param.getOrgId())) {
                TagListQueryParam entityCodeParam = new TagListQueryParam();
                entityCodeParam.setEntityCode(param.getOrgId().toString());
                entityCodeParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
                entityCodeParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
                entityCodeParam.setEntityType(EntityTypeEnum.STORE.getType());
                for (int i = 1 ;; i++) {
                    entityCodeParam.setPage(Long.valueOf(i));
                    entityCodeParam.setPrePage(Long.valueOf(Constants.FEIGN_ONCE_QUERY_MAX));
                    logger.info("tagFeignService.tagListQueryByEntityCode param:{}", entityCodeParam);
                    ResponseEntity<CommonRes<PageResult<TagListQueryDTO>>> commonRes = tagFeignService.tagListQueryByEntityCode(entityCodeParam);
                    if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
                        || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())){
                        throw new BusinessErrorException("根据实体反查标签列表异常");
                    }
                    PageResult<TagListQueryDTO> pageResult = commonRes.getBody().getData();
                    groupDTOS.addAll(pageResult.getRows().stream().map(v -> {
                        if (StringUtils.isNotBlank(param.getCreatedName()) && !param.getCreatedName().trim().equals(v.getCreatorName())) {
                            return null;
                        }
                        if (StringUtils.isNotBlank(param.getGroupName()) && !param.getGroupName().trim().equals(v.getTagTitle())) {
                            return null;
                        }
                        if (StringUtils.isBlank(v.getExtend())) {
                            return null;
                        }
                        StoreGroupDTO storeGroupDTO = JSONObject.parseObject(v.getExtend(), StoreGroupDTO.class);
                        storeGroupDTO.setId(v.getTagId());
                        storeGroupDTO.setGmtCreate(v.getGmtCreate());
                        storeGroupDTO.setCreatedName(v.getCreatorName());
                        storeGroupDTO.setCreatedBy(v.getCreatorId());
                        storeGroupDTO.setGroupName(v.getTagTitle());
                        storeGroupDTO.setGroupCode(v.getTagId().toString());
                        storeGroupDTO.setGroupStatusDesc(StoreGroupStatusEnum.getNameByCode(storeGroupDTO.getGroupStatus()));
                        storeGroupDTO.setGroupTypeDesc(StoreGroupTypeEnum.getNameByCode(storeGroupDTO.getGroupType()));
                        storeGroupDTO.setGroupCount(Objects.isNull(v.getEntityCount()) ? 0 : v.getEntityCount().intValue());
                        return storeGroupDTO;
                    }).filter(Objects::nonNull).collect(Collectors.toList()));
                    if (pageResult.getTotal() <= Long.valueOf(entityCodeParam.getPage() * entityCodeParam.getPrePage())) {
                        break;
                    }
                }
            } else {
                TagPageParam tagParam = new TagPageParam();
                tagParam.setBizType(TagBizTypeEnum.MARKETING.getCode());
                tagParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
                tagParam.setEntityType(EntityTypeEnum.STORE.getType());
                // 是否返回实体数量,0:不返回，1：返回
                tagParam.setReturnEntityCountFlag(1);

                if (StringUtils.isNotBlank(param.getCreatedName())) {
                    tagParam.setCreator(param.getCreatedName().trim());
                }
                if (StringUtils.isNotBlank(param.getGroupName())) {
                    tagParam.setTitle(param.getGroupName().trim());
                }
                for (int i = 1; ; i++) {
                    tagParam.setPage(Long.valueOf(i));
                    tagParam.setPageSize(Constants.FEIGN_ONCE_QUERY_MAX);
                    logger.info("tagFeignService.queryByPage param:{}", tagParam);
                    ResponseEntity<CommonRes<PageResult<TagDTO>>> commonRes = tagFeignService.queryByPage(tagParam);
                    if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
                        || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())) {
                        throw new BusinessErrorException("查询门店组异常");
                    }
                    PageResult<TagDTO> pageResult = commonRes.getBody().getData();
                    groupDTOS.addAll(pageResult.getRows().stream().map(v -> {
                        if (StringUtils.isBlank(v.getExtend())) {
                            return null;
                        }
                        StoreGroupDTO storeGroupDTO = JSONObject.parseObject(v.getExtend(), StoreGroupDTO.class);
                        storeGroupDTO.setId(v.getId());
                        storeGroupDTO.setGmtCreate(v.getGmtCreate());
                        storeGroupDTO.setCreatedName(v.getCreator());
                        storeGroupDTO.setCreatedBy(v.getCreateId());
                        storeGroupDTO.setGroupName(v.getTitle());
                        storeGroupDTO.setGroupCode(v.getId().toString());
                        storeGroupDTO.setGroupStatusDesc(StoreGroupStatusEnum.getNameByCode(storeGroupDTO.getGroupStatus()));
                        storeGroupDTO.setGroupTypeDesc(StoreGroupTypeEnum.getNameByCode(storeGroupDTO.getGroupType()));
                        storeGroupDTO.setGroupCount(Objects.isNull(v.getEntityCount()) ? 0 : v.getEntityCount().intValue());
                        return storeGroupDTO;
                    }).filter(Objects::nonNull).collect(Collectors.toList()));
                    pageResponse.setTotalSize(pageResult.getTotal());
                    if (pageResult.getTotal() <= Long.valueOf(tagParam.getPage() * tagParam.getPageSize())) {
                        break;
                    }
                }
            }
            if (Objects.nonNull(param.getGroupStatus())) {
                groupDTOS = groupDTOS.stream().filter(v -> {
                    return v.getGroupStatus().byteValue() == param.getGroupStatus();
                }).collect(Collectors.toList());
            }
            if (Objects.nonNull(param.getGroupType())) {
                StoreGroupTypeEnum groupTypeEnum = Optional.ofNullable(StoreGroupTypeEnum.getEnumByCode(param.getGroupType())).orElseThrow(() -> new BusinessErrorException("未知的类型"));
                if (StoreGroupTypeEnum.PUBLIC.equals(groupTypeEnum)) {
                    groupDTOS = groupDTOS.stream().filter(v -> StoreGroupTypeEnum.PUBLIC.getCode() == v.getGroupType()).collect(Collectors.toList());
                }
                if (StoreGroupTypeEnum.PRIVATE.equals(groupTypeEnum)){
                    groupDTOS = groupDTOS.stream().filter(v -> StoreGroupTypeEnum.PRIVATE.getCode() == v.getGroupType() && v.getCreatedBy().equals(finalUserId)).collect(Collectors.toList());
                }
            } else {
                // 自己创建私有的+公开的

                groupDTOS = groupDTOS.stream().filter(v -> StoreGroupTypeEnum.PUBLIC.getCode() == v.getGroupType()
                    || (StoreGroupTypeEnum.PRIVATE.getCode() == v.getGroupType() && v.getCreatedBy().equals(finalUserId))).collect(Collectors.toList());
            }
            if (param.getShowStopAble()) {
                Iterator<StoreGroupDTO> iterator = groupDTOS.iterator();
                while (iterator.hasNext()) {
                    StoreGroupDTO next = iterator.next();
                    if (next.getGroupStatus().byteValue() == StoreGroupStatusEnum.STOP.getCode()) {
                        iterator.remove();
                    }
                }
            }
            pageResponse.setTotalSize(groupDTOS.stream().count());
            pageResponse.setResult(groupDTOS.stream().map(v -> {
                v.setEditAble(v.getCreatedBy().equals(finalUserId));
                return v;
            })/*.sorted(Comparator.comparing(StoreGroupDTO::getGmtCreate).reversed())*/
                .skip((long) param.getPage() * param.getPageSize())
                .limit(param.getPageSize()).collect(Collectors.toList()));
            return pageResponse;
        } catch (Exception e) {
            logger.error("获取门店组列表异常:", e);
            throw e;
        }
    }

    @Override
    public PageResponse<List<StoreGroupDetailDTO>> getStoreGroupDetail(Long userId, String groupCode, Integer page, Integer pageSize, HttpServletRequest request) throws Exception {
        try {
            if (StringUtils.isBlank(groupCode)) {
                throw new BusinessErrorException("门店组编码不能为空");
            }
            if(Objects.isNull(page) || Objects.isNull(pageSize)){
                page = 0;
                pageSize = 10;
            }
            if(page < 0 || pageSize <= 0){
                page = 0;
                pageSize = 10;
            }
            TagEntityQueryServerParam queryServerParam = new TagEntityQueryServerParam();
            queryServerParam.setTagId(Long.parseLong(groupCode));
            queryServerParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
            queryServerParam.setEntityType(EntityTypeEnum.STORE.getType());
            queryServerParam.setPage(page + 1);
            queryServerParam.setPrePage(pageSize);
            queryServerParam.setResultType(ResultTypeEnums.ENTITYINFO.getType());
            queryServerParam.setManagerFlag(YNEnum.YES.getType());
            logger.info("tagFeignService.manageTagEntityQuery param:{}", queryServerParam);
            ResponseEntity<CommonRes<PageResult<TagEntityQueryDTO>>> resResponseEntity = tagFeignService.manageTagEntityQuery(queryServerParam);
            if (Objects.isNull(resResponseEntity) || Objects.isNull(resResponseEntity.getBody())
                || Objects.isNull(resResponseEntity.getBody().getData()) || Objects.isNull(resResponseEntity.getBody().getData().getRows())){
                throw new BusinessErrorException("查询门店组异常");
            }
            PageResult<TagEntityQueryDTO> data = resResponseEntity.getBody().getData();
            PageResponse<List<StoreGroupDetailDTO>> pageResponse = new PageResponse<>();
            pageResponse.setPageSize(pageSize);
            pageResponse.setPage(page);
            pageResponse.setTotalSize(data.getTotal());
            if (CollectionUtils.isEmpty(data.getRows())) {
                pageResponse.setResult(new ArrayList<>());
                return pageResponse;
            }
            ResponseEntity<List<OrgDTO>> responseEntity = permissionService.listOrgInfoById(data.getRows().stream().map(v -> Long.valueOf(v.getEntityCode())).collect(Collectors.toList()), false);
            if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("权限系统异常");
            }
            Map<Long, OrgDTO> orgMap = responseEntity.getBody().stream().collect(Collectors.toMap(OrgDTO::getId, Function.identity(), (k1, k2) -> k1));
            pageResponse.setResult(data.getRows().stream().map(v -> {
                StoreGroupDetailDTO dto = new StoreGroupDetailDTO();
                OrgDTO orgDTO = orgMap.get(Long.valueOf(v.getEntityCode()));
                if (Objects.isNull(orgDTO)) {
                    return null;
                }
                dto.setStoreId(orgDTO.getOutId());
                dto.setOrgId(orgDTO.getId());
                dto.setStoreCode(orgDTO.getSapcode());
                dto.setStoreName(orgDTO.getShortName());
                return dto;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
            return pageResponse;
        } catch (Exception e) {
            logger.error("根据单号获取门店组明细异常:", e);
            throw e;
        }
    }

    @Override
    public CommonResponse<Object> editStoreGroup(Long userId, String userName, StoreGroupEditParam param, HttpServletRequest request) throws Exception {
        try {
            Optional.ofNullable(param).orElseThrow(() -> new BusinessErrorException("请选择待保存数据"));
            //只是维护会员日(不更其他) 不走权限那套校验
            if(Objects.nonNull(param.getGroupId()) &&param.getMemberDay()) {
            	return editStoreGroupMemberDay(userId, userName, param);
            }
            Optional.ofNullable(param.getResourceId()).orElseThrow(() -> new BusinessErrorException("资源ID不能为空,请联系管理员处理"));
            if (StringUtils.isBlank(param.getGroupName())) {
                throw new BusinessErrorException("请输入门店组名称");
            }
            StoreGroupTypeEnum groupTypeEnum = Optional.ofNullable(StoreGroupTypeEnum.getEnumByCode(param.getGroupType())).orElseThrow(() -> new BusinessErrorException("请选择正确的门店组类型"));
            StoreGroupStatusEnum groupStatusEnum = Optional.ofNullable(StoreGroupStatusEnum.getEnumByCode(param.getGroupStatus())).orElseThrow(() -> new BusinessErrorException("请选择正确的门店组状态"));
            if (CollectionUtils.isEmpty(param.getStoreOrgIds())) {
                throw new BusinessErrorException("请选择门店");
            }
            if (Objects.isNull(param.getReplaceStoreAble())) {
                param.setReplaceStoreAble(true);
            }
            // 获取用户数据权限的门店集合
            ResponseEntity<List<OrgTreeVO>> responseEntity = permissionService.listUserDataScopeTreesByTypes(userId, param.getResourceId(), Lists.newArrayList(OrgTypeEnum.STORE.getCode()), OrgTypeEnum.STORE.getCode());
            if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("权限系统异常");
            }
            List<OrgTreeVO> dataScopeStores = responseEntity.getBody().stream().filter(v -> Objects.nonNull(v.getId()) && param.getStoreOrgIds().contains(v.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dataScopeStores) || param.getStoreOrgIds().stream().distinct().collect(Collectors.toList()).size() != dataScopeStores.size()) {
                throw new BusinessErrorException("选择的门店超出数据权限范围");
            }
            List<String> orgList = dataScopeStores.stream()
                .map(OrgTreeVO::getId)
                .map(String::valueOf)
                .collect(Collectors.toList());
            // Extend字段转换成 StoreGroupDTO
            StoreGroupDTO storeGroupDTO=new StoreGroupDTO();
            if(Objects.nonNull(param.getGroupId())){
                TagDTO tagDTO = tagFeignFacadeService.getTagDTOByStoreGroupId(param.getGroupId());
                Optional.ofNullable(tagDTO).orElseThrow(() -> new BusinessErrorException("未查询到门店组信息"));
                storeGroupDTO = JSONObject.parseObject(tagDTO.getExtend(), StoreGroupDTO.class);
                //编辑门店组时，检查门店组是否“维护会员日”，如果维护了会员日，则进行唯一性校验 且 更新基本信息时保留原有会员日信息
                List<TagOverlapInfo> tagOverlapInfoList =  checkStoreGroupDuplicate(tagDTO,orgList);
                if(CollectionUtils.isNotEmpty(tagOverlapInfoList)){
                    return new CommonResponse<>(ReturnCodeEnum.ADJUST_ORDER_PARAM_CHECK_ERROR, tagOverlapInfoList);
                }
            }
            storeGroupDTO.setGroupType(groupTypeEnum.getCode());
            storeGroupDTO.setGroupStatus(groupStatusEnum.getCode());
            storeGroupDTO.setGroupCount(dataScopeStores.size());
            List<String> oldOrgList =new ArrayList<>();
            TagSaveParam saveParam = new TagSaveParam();
            saveParam.setId(param.getGroupId());
            saveParam.setTitle(param.getGroupName().trim());
            saveParam.setTagDesc(param.getGroupName().trim());
            saveParam.setBizType(TagBizTypeEnum.MARKETING.getCode());// 2 商家中心
            saveParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
            saveParam.setTagLevel(TagLevelEnum.LEVEL_1.getCode());//中台约定1
            saveParam.setTagProperty(TagPropertyEnum.BASE.getType());
            saveParam.setCreatorId(userId);
            saveParam.setCreatorName(userName);
            saveParam.setModifierId(userId);
            saveParam.setModifierName(userName);
            saveParam.setEntityType(EntityTypeEnum.STORE.getType());
            saveParam.setExtend(JSON.toJSONString(storeGroupDTO));
            logger.info("tagFeignService.save param:{}", saveParam);
            ResponseEntity<Object> saveRes = tagFeignService.save(saveParam);
            if (saveRes == null
                || !(saveRes.getStatusCode().equals(HttpStatus.OK) || saveRes.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new BusinessErrorException(saveRes.getBody().toString());
            }
            Long id;
            try {
                id = Long.valueOf(saveRes.getBody().toString());
            } catch (Exception e) {
                throw new BusinessErrorException("调用标签编辑门店组异常");
            }
            TagMarkParentParam tagMarkParentParam = new TagMarkParentParam();
            tagMarkParentParam.setUserId(userId);
            tagMarkParentParam.setUserName(userName);
            TagMarkParam tagMarkParam = new TagMarkParam();
            tagMarkParam.setTagsId(Sets.newHashSet(Objects.isNull(param.getGroupId()) ? id : param.getGroupId()));
            List<StoreGroupDetailDTO> detailDTOS = genGroupDetailDTOS(dataScopeStores);
            tagMarkParam.setEntityMarkParams(detailDTOS.stream().map(v -> {
                EntityMarkParam entityMarkParam = new EntityMarkParam();
                entityMarkParam.setEntityTitle(v.getStoreName());
                entityMarkParam.setEntityCode(v.getOrgId().toString());
//                entityMarkParam.setExtend(JSON.toJSONString(v));
                return entityMarkParam;
            }).collect(Collectors.toSet()));

            tagMarkParentParam.setTagMarkParamList(Lists.newArrayList(tagMarkParam));
            tagMarkParentParam.setEntityType(EntityTypeEnum.STORE.getType());
            tagMarkParentParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
            tagMarkParentParam.setMarkStatus(YNEnum.YES.getType());//1打标 0去标
            tagMarkParentParam.setNeedMarkBaseTag(YNEnum.NO.getType());
            if (Objects.nonNull(param.getGroupId())) {
                // id不为空 则先把老标签去除在打新标
                if (param.getReplaceStoreAble()) {
                    List<TagEntityQueryDTO> entityQueryDTOS = new ArrayList<>();
                    for (int i = 1 ;; i++) {
                        TagEntityQueryServerParam queryServerParam = new TagEntityQueryServerParam();
                        queryServerParam.setTagId(param.getGroupId());
                        queryServerParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
                        queryServerParam.setEntityType(EntityTypeEnum.STORE.getType());
                        queryServerParam.setPage(i);
                        queryServerParam.setPrePage(Constants.FEIGN_ONCE_QUERY_MAX);
                        queryServerParam.setResultType(ResultTypeEnums.ENTITYINFO.getType());
                        queryServerParam.setManagerFlag(YNEnum.YES.getType());
                        logger.info("tagFeignService.manageTagEntityQuery param:{}", queryServerParam);
                        ResponseEntity<CommonRes<PageResult<TagEntityQueryDTO>>> resResponseEntity = tagFeignService.manageTagEntityQuery(queryServerParam);
                        if (Objects.isNull(resResponseEntity) || Objects.isNull(resResponseEntity.getBody())
                            || Objects.isNull(resResponseEntity.getBody().getData()) || Objects.isNull(resResponseEntity.getBody().getData().getRows())) {
                            throw new BusinessErrorException("查询门店组异常");
                        }
                        PageResult<TagEntityQueryDTO> pageResult = resResponseEntity.getBody().getData();
                        entityQueryDTOS.addAll(pageResult.getRows());
                        if (pageResult.getTotal() <= queryServerParam.getPage() * queryServerParam.getPrePage()) {
                            break;
                        }
                    }
                    if (CollectionUtils.isNotEmpty(entityQueryDTOS)) {

                        TagMarkParentParam removeTagMarkParentParam = new TagMarkParentParam();
                        removeTagMarkParentParam.setUserId(userId);
                        removeTagMarkParentParam.setUserName(userName);
                        TagMarkParam removeTagMarkParam = new TagMarkParam();
                        removeTagMarkParam.setTagsId(Sets.newHashSet(param.getGroupId()));
                        removeTagMarkParam.setEntityMarkParams(entityQueryDTOS.stream().map(v -> {
                            EntityMarkParam entityMarkParam = new EntityMarkParam();
                            entityMarkParam.setEntityCode(v.getEntityCode());
                            entityMarkParam.setEntityTitle(v.getEntityName());
                            return entityMarkParam;
                        }).collect(Collectors.toSet()));
                        removeTagMarkParentParam.setTagMarkParamList(Lists.newArrayList(removeTagMarkParam));
                        removeTagMarkParentParam.setEntityType(EntityTypeEnum.STORE.getType());
                        removeTagMarkParentParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
                        removeTagMarkParentParam.setMarkStatus(YNEnum.NO.getType());//1打标 0去标
                        removeTagMarkParentParam.setNeedMarkBaseTag(YNEnum.NO.getType());
                        logger.info("tagFeignService.tagMark remove param:{}", removeTagMarkParentParam);
                        tagFeignService.tagMark(removeTagMarkParentParam);
                        oldOrgList = entityQueryDTOS.stream().map(TagEntityQueryDTO::getEntityCode).distinct().collect(Collectors.toList());
                    }
                }
            }
            logger.info("tagFeignService.tagMark param:{}", tagMarkParentParam);
            ResponseEntity<CommonRes> markRes = tagFeignService.tagMark(tagMarkParentParam);
            // 计算本次新增的orgId集合
            List<String> newOrgList = new ArrayList<>(orgList);
            newOrgList.removeAll(oldOrgList);
            // 计算本次需要删除的orgId集合
            List<String> deleteOrgList = new ArrayList<>(oldOrgList);
            deleteOrgList.removeAll(orgList);
            //修改门店组时，(目前不存在 禁用下编辑都是启用下编辑)发送消息通知,消费者自行处理更新
            if(Objects.nonNull(param.getGroupId())){
                storeGroupChangeProducer.sendMessage(param.getGroupId(),CommonEnums.StoreGroupMemberDayOpTypeEnum.STORE_GROUP_BASE_OP_EDIT.getCode(),newOrgList,deleteOrgList);
            }
            return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK.getCode(), Objects.isNull(param.getGroupId()) ? "新增成功" : "修改成功");
        } catch (Exception e) {
            logger.error("编辑门店组异常:", e);
            throw e;
        }
    }

    @Override
    public CommonResponse<List<Long>> storeGroupImport(MultipartFile file, Long resourceId, Long userId) throws Exception {
        try {
            if (Objects.isNull(resourceId)) {
                return new CommonResponse<>(ReturnCodeEnum.ADJUST_ORDER_PARAM_CHECK_ERROR.getCode(), "资源ID不能为空,请联系管理员处理");
            }
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("MDM编码", "sapCode");
            List<StoreGroupImportDTO> importList = HutoolExcelUtils.excelToBean(file.getInputStream(), StoreGroupImportDTO.class, headerMap);
            if (CollectionUtils.isEmpty(importList)) {
                throw new BusinessErrorException("导入文件为空");
            }
            List<String> importSapcodes = importList.stream().map(StoreGroupImportDTO::getSapCode).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(importSapcodes)) {
                throw new BusinessErrorException("导入文件为空");
            }
            // 获取用户数据权限的门店集合
            ResponseEntity<List<OrgTreeVO>> responseEntity = permissionService.listUserDataScopeTreesByTypes(userId, resourceId, Lists.newArrayList(Integer.valueOf(OrgTypeEnum.STORE.getCode())), OrgTypeEnum.STORE.getCode());
            if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))
                || CollectionUtils.isEmpty(responseEntity.getBody())) {
                throw new BusinessErrorException("权限系统异常");
            }
            List<OrgTreeVO> dataScopeStores = responseEntity.getBody().stream().filter(v -> StringUtils.isNotBlank(v.getSapcode()) && importSapcodes.contains(v.getSapcode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(dataScopeStores)) {
                throw new BusinessErrorException("导入的门店超出数据权限范围");
            }
            return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK, dataScopeStores.stream().map(OrgTreeVO::getId).distinct().collect(Collectors.toList()));
        } catch (Exception e) {
            logger.error("导入门店组异常:", e);
            throw e;
        }
    }

    @Override
    public CommonResponse<Object> storeGroupUpdateStatus(Long groupId, Byte groupStatus, Long userId, String userName) throws Exception {
        try {
            if (Objects.isNull(groupId)) {
                throw new BusinessErrorException("请选择门店组进行状态变更");
            }
            StoreGroupStatusEnum groupStatusEnum = StoreGroupStatusEnum.getEnumByCode(groupStatus);
            if (Objects.isNull(groupStatusEnum)) {
                throw new BusinessErrorException("请选择门店组进行状态变更");
            }
            TagDTO tagDTO = tagFeignFacadeService.getTagDTOByStoreGroupId(groupId);
            StoreGroupDTO storeGroupDTO = JSONObject.parseObject(tagDTO.getExtend(), StoreGroupDTO.class);
            if(StoreGroupStatusEnum.STARTUP.equals(groupStatusEnum)) {
                List<TagOverlapInfo> tagOverlapInfos = checkStoreGroupDuplicate(tagDTO);
                if(CollectionUtils.isNotEmpty(tagOverlapInfos)){
                    return new CommonResponse<>(ReturnCodeEnum.ADJUST_ORDER_PARAM_CHECK_ERROR, tagOverlapInfos);
                }
            }
            TagSaveParam saveParam = new TagSaveParam();
            saveParam.setId(tagDTO.getId());
            saveParam.setTitle(tagDTO.getTitle());
            saveParam.setTagDesc(tagDTO.getTagDesc());
            saveParam.setBizType(TagBizTypeEnum.MARKETING.getCode());// 2 商家中心
            saveParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
            saveParam.setTagLevel(TagLevelEnum.LEVEL_1.getCode());//中台约定1
            saveParam.setTagProperty(TagPropertyEnum.BASE.getType());
            saveParam.setCreatorId(tagDTO.getCreateId());
            saveParam.setCreatorName(tagDTO.getCreator());
            saveParam.setModifierId(userId);
            saveParam.setModifierName(userName);
            saveParam.setEntityType(EntityTypeEnum.STORE.getType());
            storeGroupDTO.setGroupStatus(groupStatusEnum.getCode());
            saveParam.setExtend(JSON.toJSONString(storeGroupDTO));
            logger.info("tagFeignService.save param:{}", saveParam);
            ResponseEntity<Object> saveRes = tagFeignService.save(saveParam);
            if (saveRes == null|| !(saveRes.getStatusCode().equals(HttpStatus.OK) || saveRes.getStatusCode().equals(HttpStatus.CREATED))) {
                throw new BusinessErrorException(saveRes.getBody().toString());
            }
            //启用/停用门店组时，发送消息通知,消费者自行处理更新
            storeGroupChangeProducer.sendMessage(tagDTO.getId(),
                StoreGroupStatusEnum.STARTUP.equals(groupStatusEnum)?
                    CommonEnums.StoreGroupMemberDayOpTypeEnum.STORE_GROUP_MEMBER_DAY_OP_ABLE.getCode():
                    CommonEnums.StoreGroupMemberDayOpTypeEnum.STORE_GROUP_MEMBER_DAY_OP_DISABLE.getCode());

            return new CommonResponse<>(ReturnCodeEnum.HTTP_STATUS_OK);
        } catch (Exception e) {
            logger.error("变更门店组状态异常:", e);
            throw e;
        }
    }


    private List<StoreGroupDetailDTO> genGroupDetailDTOS(List<OrgTreeVO> dataScopeStores) {
        return dataScopeStores.stream().map(v -> {
            StoreGroupDetailDTO dto = new StoreGroupDetailDTO();
            dto.setStoreId(v.getOutId());
            dto.setOrgId(v.getId());
            dto.setStoreCode(v.getSapcode());
            dto.setStoreName(v.getShortName());
            return dto;
        }).collect(Collectors.toList());
    }
	@Override
	public List<StoreGroupDTO> getStoreGroupByStoreGroupIdList(Long userId,Integer tagType,List<Long> storeGroupIdList) {
		List<StoreGroupDTO> storeGroupList = Lists.newArrayList();
		TagPageParam tagParam = new TagPageParam();
        tagParam.setBizType(TagBizTypeEnum.MARKETING.getCode());
        tagParam.setTagType(tagType);
        tagParam.setEntityType(EntityTypeEnum.STORE.getType());
        // 是否返回实体数量,0:不返回，1：返回
        tagParam.setReturnEntityCountFlag(1);
        tagParam.setTagIdList(storeGroupIdList);
        for (int i = 1; ; i++) {
            tagParam.setPage((long) i);
            tagParam.setPageSize(Constants.FEIGN_ONCE_QUERY_MAX);
            logger.info("tagFeignService.queryByPage param:{}", JSON.toJSONString(tagParam));
            ResponseEntity<CommonRes<PageResult<TagDTO>>> commonRes = tagFeignService.queryByPage(tagParam);
            if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
                || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())) {
                throw new BusinessErrorException("查询门店组异常");
            }
            PageResult<TagDTO> pageResult = commonRes.getBody().getData();

            storeGroupList.addAll(pageResult.getRows().stream().map(v -> {
                if (StringUtils.isBlank(v.getExtend()) && tagType!=TagTypeEnums.STORE.getType()) {
                    return null;
                }
                StoreGroupDTO storeGroupDTO = JSONObject.parseObject(v.getExtend(), StoreGroupDTO.class);
                if(null==storeGroupDTO) {
                	storeGroupDTO = new StoreGroupDTO();
                }
                storeGroupDTO.setId(v.getId());
                storeGroupDTO.setGmtCreate(v.getGmtCreate());
                storeGroupDTO.setCreatedName(v.getCreator());
                storeGroupDTO.setCreatedBy(v.getCreateId());
                storeGroupDTO.setGroupName(v.getTitle());
                storeGroupDTO.setGroupCode(v.getId().toString());
                if(tagType!=TagTypeEnums.STORE.getType()) {
                	storeGroupDTO.setGroupStatusDesc(StoreGroupStatusEnum.getNameByCode(storeGroupDTO.getGroupStatus()));
                    storeGroupDTO.setGroupTypeDesc(StoreGroupTypeEnum.getNameByCode(storeGroupDTO.getGroupType()));
                }
                storeGroupDTO.setGroupCount(Objects.isNull(v.getEntityCount()) ? 0 : v.getEntityCount().intValue());
                return storeGroupDTO;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
            if (pageResult.getTotal() <= Long.valueOf(tagParam.getPage() * tagParam.getPageSize())) {
                break;
            }
        }
        if(!TagTypeEnums.STORE.getType().equals(tagType)) {
        	storeGroupList = storeGroupList.stream().filter(v -> {
                return v.getGroupStatus().equals(StoreGroupStatusEnum.STARTUP.getCode());
            }).collect(Collectors.toList());
        	// 自己创建私有的+公开的
            storeGroupList = storeGroupList.stream().filter(v -> StoreGroupTypeEnum.PUBLIC.getCode() == v.getGroupType()
                || (StoreGroupTypeEnum.PRIVATE.getCode() == v.getGroupType() && v.getCreatedBy().equals(userId))).collect(Collectors.toList());
            storeGroupList.removeIf(next -> next.getGroupStatus().equals(StoreGroupStatusEnum.STOP.getCode()));
        }
		return storeGroupList;
	}

    /**
     * 根据门店组id查询门店orgId列表
     * @param tagId
     * @return
     */
	@Override
	public List<String> tagEntityCodeListQuery(Long tagId) {
		List<String> storeIdList = Lists.newArrayList();
		TagEntityQueryServerParam queryServerParam = new TagEntityQueryServerParam();
        queryServerParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
        queryServerParam.setManagerFlag(YNEnum.YES.getType());
        queryServerParam.setResultType(ResultTypeEnums.ENTITYCODE.getType());
        queryServerParam.setTagBizType(TagBizTypeEnum.MARKETING.getCode());
        queryServerParam.setTagId(tagId);
        queryServerParam.setEntityType(EntityTypeEnum.STORE.getType());
        for (int i = 1; ; i++) {
            queryServerParam.setPage(i);
            queryServerParam.setPrePage(Constants.FEIGN_ONCE_QUERY_MAX);
            logger.info("标签查询实体Code tagFeignService.tagEntityCodeListQuery param:{}", queryServerParam);
            ResponseEntity<CommonRes<PageResult<String>>> commonRes = tagFeignService.tagEntityCodeListQuery(queryServerParam);
            logger.info("标签查询实体Code tagFeignService.tagEntityCodeListQuery 结果:{}", commonRes);
            if (Objects.isNull(commonRes) || Objects.isNull(commonRes.getBody())
                || Objects.isNull(commonRes.getBody().getData()) || Objects.isNull(commonRes.getBody().getData().getRows())) {
            	return storeIdList;
            }
            PageResult<String> data = commonRes.getBody().getData();
            storeIdList.addAll(data.getRows());
            if (data.getTotal() <= (long) queryServerParam.getPage() * queryServerParam.getPrePage() || CollectionUtils.isEmpty(data.getRows())) {
                logger.info("标签查询实体Code 完成");
                break;
            }
        }
		return storeIdList;
	}

	/**
	 * 检查当前门店组 是否存在 一个门店存在于其他维护了会员日的门店组
	 * @param tagDTO
	 */
	private List<TagOverlapInfo> checkStoreGroupDuplicate(TagDTO tagDTO) {
       return checkStoreGroupDuplicate(tagDTO, null);
    }

    /**
     * 编辑门店时需要传入本次操作的门店列表, 其他情况查询tag下的门店实体列表即可
     * 检查当前门店组 是否存在 一个门店存在于其他维护了会员日的门店组
     * @param tagDTO
     */
    private List<TagOverlapInfo> checkStoreGroupDuplicate(TagDTO tagDTO, List<String> entityCodeList) {
        Optional.ofNullable(tagDTO).orElseThrow(() -> new BusinessErrorException("请选择待保存数据"));
        if (StringUtils.isNotBlank(tagDTO.getExtend())) {
            StoreGroupDTO storeGroupDTOOld = JSONObject.parseObject(tagDTO.getExtend(), StoreGroupDTO.class);
            if(null!=storeGroupDTOOld.getCycleType() && CollectionUtils.isNotEmpty(storeGroupDTOOld.getNums())) {
                return storeGroupDuplicate(tagDTO.getId(), entityCodeList);
            }
        }
        return Lists.newArrayList();
    }

    /**
     * 只是维护会员日(保留原来标签基本信息和门店)
     * @param userId
     * @param userName
     * @param param
     * @return
     * @throws Exception
     */
	private CommonResponse<Object> editStoreGroupMemberDay(Long userId, String userName, StoreGroupEditParam param)
			throws Exception {
		try {
        	Optional.ofNullable(param).orElseThrow(() -> new BusinessErrorException("请选择待保存数据"));
            Optional.ofNullable(param.getGroupId()).orElseThrow(() -> new BusinessErrorException("门店组ID不能为空"));
            TagDTO tagDTO = tagFeignFacadeService.getTagDTOByStoreGroupId(param.getGroupId());
            Optional.ofNullable(tagDTO).orElseThrow(() -> new BusinessErrorException("未查询到门店组信息"));
            StoreGroupDTO storeGroupDTO = JSONObject.parseObject(tagDTO.getExtend(), StoreGroupDTO.class);
            //验证历史前门店组是否维护会员日
            boolean isHistoryMemberDay = false;
            if(null != storeGroupDTO.getCycleType() && CollectionUtils.isNotEmpty(storeGroupDTO.getNums())) {
            	isHistoryMemberDay = true;
            }
            //判断当前门店组是维护会员日 还是清空会员日
            boolean isCurrentMemberDay = false;
            Integer operationType;
            if(null!=param.getCycleType() && CollectionUtils.isNotEmpty(param.getNums())) {
                //维护会员日
                storeGroupDTO.setCycleType(param.getCycleType());
                storeGroupDTO.setNums(param.getNums());
                operationType =CommonEnums.StoreGroupMemberDayOpTypeEnum.STORE_GROUP_MEMBER_DAY_OP_EDIT.getCode();
                isCurrentMemberDay = true;
            }else {
                //清空会员日
                storeGroupDTO.setCycleType(null);
                storeGroupDTO.setNums(null);
                operationType =CommonEnums.StoreGroupMemberDayOpTypeEnum.STORE_GROUP_MEMBER_DAY_OP_DELETE.getCode();
            }
            /**
             * 1、历史门店组未维护会员日、当前门店组未维护会员日 不需要对缓存处理 防止误清理其他维护会员日的门店和当前门店组相同门店的缓存
             * 2、历史门店组未维护会员日、当前门店组维护会员日 需要对缓存处理
             * 3、历史门店组维护会员日、当前门店组未维护会员 需要对缓存处理
             * 4、历史门店组维护会员日、当前门店组维护会员日 需要对缓存处理
             */
            if(!isHistoryMemberDay && !isCurrentMemberDay) {
                return new CommonResponse<>(ReturnCodeEnum.HTTP_STATUS_OK);
            }
            //检查门店组内是否存在已经维护了会员日的门店
            tagDTO.setExtend(JSON.toJSONString(storeGroupDTO));
            List<TagOverlapInfo> tagOverlapInfoList = checkStoreGroupDuplicate(tagDTO);
            if(CollectionUtils.isNotEmpty(tagOverlapInfoList)){
                return new CommonResponse<>(ReturnCodeEnum.ADJUST_ORDER_PARAM_CHECK_ERROR, tagOverlapInfoList);
            }
            TagSaveParam saveParam = new TagSaveParam();
            saveParam.setId(tagDTO.getId());
            saveParam.setTitle(tagDTO.getTitle());
            saveParam.setTagDesc(tagDTO.getTagDesc());
            saveParam.setBizType(TagBizTypeEnum.MARKETING.getCode());// 2 商家中心
            saveParam.setTagType(TagTypeEnums.STORE_GROUP.getType());
            saveParam.setTagLevel(TagLevelEnum.LEVEL_1.getCode());//中台约定1
            saveParam.setTagProperty(TagPropertyEnum.BASE.getType());
            saveParam.setCreatorId(tagDTO.getCreateId());
            saveParam.setCreatorName(tagDTO.getCreator());
            saveParam.setModifierId(userId);
            saveParam.setModifierName(userName);
            saveParam.setEntityType(EntityTypeEnum.STORE.getType());
            saveParam.setExtend(JSON.toJSONString(storeGroupDTO));
            logger.info("tagFeignService.save param:{}", saveParam);
            tagFeignService.save(saveParam);
            //通知促销中台同步缓存
            if(Objects.nonNull(param.getGroupId()) && Byte.valueOf(StoreGroupStatusEnum.STARTUP.getCode()).equals(storeGroupDTO.getGroupStatus())){
                storeGroupChangeProducer.sendMessage(param.getGroupId(),operationType);
            }
            return new CommonResponse<>(ReturnCodeEnum.HTTP_STATUS_OK);
        } catch (Exception e) {
            logger.error("设置会员日异常:", e);
            throw e;
        }
	}



	/**
	 * 验证当前维护会员日门店组内门店是否在其他会员日门店组内存在重复
	 * @return
	 */
	private List<TagOverlapInfo> storeGroupDuplicate(Long groupId,List<String> entityCodeList){
        if(CollectionUtils.isEmpty(entityCodeList)){
            entityCodeList = tagEntityCodeListQuery(groupId);
        }
        if (CollectionUtils.isEmpty(entityCodeList)) {
            return Lists.newArrayList();
        }

        // 结果集: 标签ID -> 重叠信息
        List<TagOverlapInfo> tagOverlapStats = new ArrayList<>();

        // 分批处理实体查询
        Lists.partition(entityCodeList, Constants.FEIGN_ONCE_QUERY_MAX)
            .forEach(subList -> {
                // 查询实体对应的标签
                Map<String, List<Long>> entityTagMap = tagFeignFacadeService.findEntityTagList(
                    subList,
                    EntityTypeEnum.STORE,
                    TagBizTypeEnum.MARKETING
                );

                // 收集所有标签ID
                Set<Long> allTagIds = entityTagMap.values()
                    .stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());

                if (CollectionUtils.isEmpty(allTagIds)) {
                    return;
                }

                // 查询标签详情
                List<TagDTO> tagDTOList = tagFeignFacadeService.getTagDTOListByStoreGroupId(
                    new ArrayList<>(allTagIds)
                );

                // 过滤并收集有效的标签信息
                Map<Long, TagDTO> validTagMap = tagDTOList.stream()
                    .filter(tagDTO -> {
                        StoreGroupDTO storeGroupDTO = JSONObject.parseObject(
                            tagDTO.getExtend(),
                            StoreGroupDTO.class
                        );
                        return !tagDTO.getId().equals(groupId) && // 不是自己
                            Byte.valueOf(StoreGroupStatusEnum.STARTUP.getCode()).equals(storeGroupDTO.getGroupStatus()) && // 进行中
                            storeGroupDTO.getCycleType() != null && // 设置了cycleType
                            CollectionUtils.isNotEmpty(storeGroupDTO.getNums()); // nums不为空
                    })
                    .collect(Collectors.toMap(
                        TagDTO::getId,
                        tag -> tag
                    ));

                if (validTagMap.isEmpty()) {
                    return;
                }

                // 按标签ID分组，统计每个标签下重叠的门店
                Map<Long, Set<String>> tagStoreMap = new HashMap<>();

                entityTagMap.forEach((storeId, tagIds) -> {
                    tagIds.stream()
                        .filter(validTagMap::containsKey)
                        .forEach(tagId ->
                            tagStoreMap.computeIfAbsent(tagId, k -> new HashSet<>())
                                .add(storeId)
                        );
                });

                // 查询门店信息
                Set<Long> allStoreIds = tagStoreMap.values().stream()
                    .flatMap(Set::stream)
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());

                Map<Long, OrgInfoDTO> storeInfoMap = permissionService.listOrgInfoByIdWithoutType(
                    new ArrayList<>(allStoreIds)
                ).stream().collect(Collectors.toMap(
                    OrgInfoDTO::getId,
                    org -> org
                ));

                // 构建最终结果
                tagStoreMap.forEach((tagId, storeIds) -> {
                    TagDTO tagDTO = validTagMap.get(tagId);
                    Set<String> storeNames = storeIds.stream()
                        .map(Long::valueOf)
                        .map(storeInfoMap::get)
                        .filter(Objects::nonNull)
                        .map(OrgInfoDTO::getShortName)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
                    TagOverlapInfo overlapInfo = TagOverlapInfo.builder()
                        .tagId(tagId)
                        .tagName(tagDTO.getTitle())
                        .creator(tagDTO.getCreator())
                        .overlapStoreCount(storeNames.size())
                        .overlapStoreNames(formatStoreNames(storeNames, 3))
                        .build();

                    tagOverlapStats.add(overlapInfo);
                });
            });
        return tagOverlapStats;
	}

    // 处理店铺名称的工具方法
    private String formatStoreNames(Set<String> storeNames, int limit) {
        if (CollectionUtils.isEmpty(storeNames)) {
            return "";
        }

        List<String> nameList = new ArrayList<>(storeNames);
        if (storeNames.size() <= limit) {
            return String.join("、", nameList);
        } else {
            return String.join("、", nameList.subList(0, limit)) + "...";
        }
    }


}
