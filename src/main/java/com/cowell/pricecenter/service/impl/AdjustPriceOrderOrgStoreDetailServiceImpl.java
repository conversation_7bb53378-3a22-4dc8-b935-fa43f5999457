/**
* @mbg.generated
* generator on Wed Apr 20 17:49:51 CST 2022
*/
package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.pricecenter.cache.IDataCacheManager;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetailExample.Criteria;
import com.cowell.pricecenter.enums.AdjustPriceOrderOrgStoreDetailEnum;
import com.cowell.pricecenter.enums.AdjustPriceVersionEnum;
import com.cowell.pricecenter.enums.DealPriceStepEnum;
import com.cowell.pricecenter.enums.DealStepStatusEnum;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderOrgStoreDetailMapper;
import com.cowell.pricecenter.mapper.PricePushTaskMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderExMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgStoreDetailExtMapper;
import com.cowell.pricecenter.mapper.extension.PriceDetailHistoryExtMapper;
import com.cowell.pricecenter.mq.producer.AdjustPriceOrderOrgStoreDetailActualProducer;
import com.cowell.pricecenter.param.OrgStoreDetailSplitDTO;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.AdjustPriceOrderOrgStoreDetailService;
import com.cowell.pricecenter.service.FeignStoreService;
import com.cowell.pricecenter.service.IAdjustPriceOrderDetailService;
import com.cowell.pricecenter.service.PricePushHistoryService;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderOrgStoreDetailExtend;
import com.cowell.pricecenter.service.dto.MdmStoreBaseDTO;
import com.cowell.pricecenter.service.dto.PriceDetailHistoryExtend1;
import com.cowell.pricecenter.service.dto.request.AdjustPriceBusinessIdPriceParam;
import com.cowell.pricecenter.service.dto.request.AmisPageParam;
import com.cowell.pricecenter.service.dto.response.AdjustPriceBusinessIdPriceDTO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.query.AdjustPriceOrderOrgStoreDetailQuery;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.cowell.pricecenter.web.rest.vo.PricePushResultVO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class AdjustPriceOrderOrgStoreDetailServiceImpl implements AdjustPriceOrderOrgStoreDetailService {

    private static final Logger logger = LoggerFactory.getLogger(AdjustPriceOrderOrgStoreDetailServiceImpl.class);

    @Autowired
    private AdjustPriceOrderOrgStoreDetailMapper adjustPriceOrderOrgStoreDetailMapper;

    @Autowired
    private AdjustPriceOrderOrgStoreDetailExtMapper adjustPriceOrderOrgStoreDetailExtMapper;

    @Autowired
    private AdjustPriceOrderMapper adjustPriceOrderMapper;

    @Autowired
    private PricePushTaskMapper pricePushTaskMapper;

    @Autowired
    private PricePushHistoryService pricePushHistoryService;

    @Resource
    private FeignStoreService feignStoreService;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private AdjustPriceOrderOrgStoreDetailActualProducer adjustPriceOrderOrgStoreDetailActualProducer;

    @Autowired
    private IAdjustPriceOrderDetailService adjustPriceOrderDetailService;


    @Autowired
    private AdjustPriceOrderExMapper adjustPriceOrderExMapper;

    @Autowired
    private PriceDetailHistoryExtMapper priceDetailHistoryExtMapper;


    private static final int SUCCESS = 1;

    /**
     * 调价单count
     * key=OHC_ADJUST_COUNT_LOCAL_CACHE_KEY_ADJUSTCode
     */
    private static final String OHC_ADJUST_COUNT_LOCAL_CACHE_KEY = "OHC_ADJUST_COUNT_LOCAL_CACHE_KEY_%s";


    /**
     * countByExample
     *
     * @param example example
     * @return long long
     */
    @Override
    public long countByExample(AdjustPriceOrderOrgStoreDetailExample example) {
        return adjustPriceOrderOrgStoreDetailMapper.countByExample(example);
    }

    /**
     * deleteByExample
     *
     * @param example example
     * @return int int
     */
    @Override
    public int deleteByExample(AdjustPriceOrderOrgStoreDetailExample example) {
        return adjustPriceOrderOrgStoreDetailMapper.deleteByExample(example);
    }

    /**
     * deleteByPrimaryKey
     *
     * @param id id
     * @return int int
     */
    @Override
    public int deleteByPrimaryKey(Long id) {
        return adjustPriceOrderOrgStoreDetailMapper.deleteByPrimaryKey(id);
    }

    /**
     * insert
     *
     * @param row row
     * @return int int
     */
    @Override
    public int insert(AdjustPriceOrderOrgStoreDetail row) {
        return adjustPriceOrderOrgStoreDetailMapper.insert(row);
    }

    /**
     * insertSelective
     *
     * @param row row
     * @return int int
     */
    @Override
    public int insertSelective(AdjustPriceOrderOrgStoreDetail row) {
        return adjustPriceOrderOrgStoreDetailMapper.insertSelective(row);
    }

    /**
     * selectByExample
     *
     * @param example example
     * @return List<AdjustPriceOrderOrgStoreDetail> List<AdjustPriceOrderOrgStoreDetail>
     */
    @Override
    public List<AdjustPriceOrderOrgStoreDetail> selectByExample(AdjustPriceOrderOrgStoreDetailExample example) {
        return adjustPriceOrderOrgStoreDetailMapper.selectByExample(example);
    }

    /**
     * selectByPrimaryKey
     *
     * @param id id
     * @return AdjustPriceOrderOrgStoreDetail AdjustPriceOrderOrgStoreDetail
     */
    @Override
    public AdjustPriceOrderOrgStoreDetail selectByPrimaryKey(Long id) {
        return adjustPriceOrderOrgStoreDetailMapper.selectByPrimaryKey(id);
    }

    /**
     * updateByExampleSelective
     *
     * @param row     row
     * @param example example
     * @return int int
     */
    @Override
    public int updateByExampleSelective(AdjustPriceOrderOrgStoreDetail row, AdjustPriceOrderOrgStoreDetailExample example) {
        return adjustPriceOrderOrgStoreDetailMapper.updateByExampleSelective(row, example);
    }

    /**
     * updateByExample
     *
     * @param row     row
     * @param example example
     * @return int int
     */
    @Override
    public int updateByExample(AdjustPriceOrderOrgStoreDetail row, AdjustPriceOrderOrgStoreDetailExample example) {
        return adjustPriceOrderOrgStoreDetailMapper.updateByExample(row, example);
    }

    /**
     * updateByPrimaryKeySelective
     *
     * @param row row
     * @return int int
     */
    @Override
    public int updateByPrimaryKeySelective(AdjustPriceOrderOrgStoreDetail row) {
        return adjustPriceOrderOrgStoreDetailMapper.updateByPrimaryKeySelective(row);
    }

    /**
     * updateByPrimaryKey
     *
     * @param row row
     * @return int int
     */
    @Override
    public int updateByPrimaryKey(AdjustPriceOrderOrgStoreDetail row) {
        return adjustPriceOrderOrgStoreDetailMapper.updateByPrimaryKey(row);
    }

    @Override
    public List<AdjustPriceOrderOrgStoreDetail> query(AdjustPriceOrderOrgStoreDetailQuery query) {
        AdjustPriceOrderOrgStoreDetailExample queryExample = buildExample(query);
        return adjustPriceOrderOrgStoreDetailMapper.selectByExample(queryExample);
    }

    @Override
    public List<AdjustPriceOrderOrgStoreDetail> page(AdjustPriceOrderOrgStoreDetailQuery query, int page, int pageSize) {
        AdjustPriceOrderOrgStoreDetailExample queryExample = buildExample(query);
        queryExample.setLimit(pageSize);
        queryExample.setOffset(((long) page * pageSize));
        return adjustPriceOrderOrgStoreDetailMapper.selectByExample(queryExample);
    }

    @Override
    public List<AdjustPriceOrderOrgStoreDetail> distinctStorePage(AdjustPriceOrderOrgStoreDetailQuery query, int page, int pageSize) {
        AdjustPriceOrderOrgStoreDetailExample queryExample = buildExample(query);
        queryExample.setLimit(pageSize);
        queryExample.setOffset(((long) page * pageSize));
        return adjustPriceOrderOrgStoreDetailExtMapper.distinctStorePage(queryExample);
    }

    private AdjustPriceOrderOrgStoreDetailExample buildExample(AdjustPriceOrderOrgStoreDetailQuery query) {
        AdjustPriceOrderOrgStoreDetailExample example = new AdjustPriceOrderOrgStoreDetailExample();
        AdjustPriceOrderOrgStoreDetailExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(query.getAdjustCode())) {
            criteria.andAdjustCodeEqualTo(query.getAdjustCode());
        }
        if (Objects.nonNull(query.getStoreId())) {
            criteria.andStoreIdEqualTo(query.getStoreId());
        }
        if (Objects.nonNull(query.getBusinessId())) {
            criteria.andBusinessIdEqualTo(query.getBusinessId());
        }

        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus());
        }

        return example;
    }

    @Override
    public int getAdjustPriceLevel(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId) {
        if (StringUtils.isBlank(adjustCode)) {
            return -1;
        }
        AdjustPriceOrderOrgStoreDetail adjustPriceLevelDetail = getAdjustPriceLevelDetail(adjustCode, storeId, goodsNo, priceTypeCode, channelId, skuId);
        if (Objects.nonNull(adjustPriceLevelDetail)) {
            return adjustPriceLevelDetail.getAdjustPriceLevel();
        }
        return -1;
    }

    @Override
    public AdjustPriceOrderOrgStoreDetail getAdjustPriceLevelDetail(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId) {
        if (StringUtils.isBlank(adjustCode)) {
            return AdjustPriceOrderOrgStoreDetail.builder().adjustPriceLevel(-1).build();
        }
        if (Objects.isNull(skuId)) {
            skuId = 0L;
        }
        // 构建缓存key
        String cacheKey = buildAdjustPriceLevelDetailCacheKey(adjustCode, storeId, goodsNo, priceTypeCode, channelId, skuId);
        try {
            IDataCacheManager<String, Object> cacheManager = new OHCDataCacheManagerImpl();
            // 先从缓存获取
            AdjustPriceOrderOrgStoreDetail result = cacheManager.getCache(cacheKey,AdjustPriceOrderOrgStoreDetail.class);
            if (Objects.isNull(result)){
                result = queryAdjustPriceLevelDetailFromDB(adjustCode, storeId, goodsNo, priceTypeCode, channelId, skuId);
                cacheManager.setCache(cacheKey, result, 600000L);
            }else {
                return result;
            }
            return result;
        } catch (Exception e) {
            logger.error("getAdjustPriceLevelDetail异常 adjustCode={}, cacheKey={}", adjustCode, cacheKey, e);
            // 异常情况下直接查询数据库，不使用缓存
            return queryAdjustPriceLevelDetailFromDB(adjustCode, storeId, goodsNo, priceTypeCode, channelId, skuId);
        }
    }

    /**
     * 构建调价级别详情缓存key
     */
    private String buildAdjustPriceLevelDetailCacheKey(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId) {
        return RedisKeysConstant.ADJUST_PRICE_LEVEL_DETAIL_KEY +
               adjustCode + "_" + storeId + "_" + goodsNo + "_" + priceTypeCode + "_" + channelId + "_" + skuId;
    }

    /**
     * 从数据库查询调价级别详情
     */
    private AdjustPriceOrderOrgStoreDetail queryAdjustPriceLevelDetailFromDB(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId) {
        try {
            if (adjustCodeIsNotVersion1_0(adjustCode)) {
                // 存在调价单且调价单版本不为1.0
                AdjustPriceOrderOrgStoreDetailExample example = new AdjustPriceOrderOrgStoreDetailExample();
                AdjustPriceOrderOrgStoreDetailExample.Criteria criteria = example.createCriteria();
                criteria.andAdjustCodeEqualTo(adjustCode);
                criteria.andStoreIdEqualTo(storeId);
                criteria.andGoodsNoEqualTo(goodsNo);
                criteria.andPriceTypeCodeEqualTo(priceTypeCode);
                criteria.andChannelIdEqualTo(channelId);
                criteria.andSkuIdEqualTo(skuId);
                example.setLimit(1);
                List<AdjustPriceOrderOrgStoreDetail> list = adjustPriceOrderOrgStoreDetailMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(list)) {
                    return list.get(0);
                }
                // 兼容历史调价单
                example = new AdjustPriceOrderOrgStoreDetailExample();
                example.createCriteria()
                    .andAdjustCodeEqualTo(adjustCode)
                    .andStoreIdEqualTo(storeId)
                    .andGoodsNoEqualTo("0")
                    .andPriceTypeCodeEqualTo("-1")
                    .andChannelIdEqualTo(0)
                    .andSkuIdEqualTo(0L);
                example.setLimit(1);
                list = adjustPriceOrderOrgStoreDetailMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(list)) {
                    return list.get(0);
                }
            }
        } catch (Exception e) {
            logger.error("queryAdjustPriceLevelDetailFromDB异常 adjustCode={}", adjustCode, e);
        }
        // 无查询结果 层级默认为-1
        return AdjustPriceOrderOrgStoreDetail.builder().adjustPriceLevel(-1).build();
    }
    /**
     * 判断是否为非1.0版本
     */
    public boolean adjustCodeIsNotVersion1_0(String adjustCode) {
        IDataCacheManager<String, Object> cacheManager = new OHCDataCacheManagerImpl();
        String key = String.format(OHC_ADJUST_COUNT_LOCAL_CACHE_KEY,adjustCode);
        Boolean versionMoreThan1_0 = cacheManager.getCache(key,Boolean.class);
        if (Objects.isNull(versionMoreThan1_0)){
            AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
            versionMoreThan1_0  =  Objects.nonNull(adjustPriceOrder) && !AdjustPriceVersionEnum.VERSION_1_0.getVersion().equals(adjustPriceOrder.getAdjustPriceVersion());
            cacheManager.setCache(key, versionMoreThan1_0, 600000L);
        }else {
            logger.info("adjustCodeIsNotVersion1_0|命中缓存获取调价单版本信息|adjustCode:{}",adjustCode);
        }
        return versionMoreThan1_0;
    }

    @Override
    public void deleteByAdjustCode(String adjustCode) {
        if (adjustCode == null) {
            return;
        }
        AdjustPriceOrderOrgStoreDetailExample example = new AdjustPriceOrderOrgStoreDetailExample();
        example.createCriteria().andAdjustCodeEqualTo(adjustCode);
        adjustPriceOrderOrgStoreDetailMapper.deleteByExample(example);
    }


    @Override
    public List<AdjustPriceOrderOrgStoreDetail> listByAdjustCodeAndBusinessIds(String adjustCode, List<Long> businessIds, Integer page, Integer pageSize) {
        if (adjustCode == null || CollectionUtils.isEmpty(businessIds)) {
            return Collections.emptyList();
        }
        AmisPageParam pageParam = AmisPageParam.createInstance(page, pageSize);
        AdjustPriceOrderOrgStoreDetailExample example = new AdjustPriceOrderOrgStoreDetailExample();
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode)
            .andBusinessIdIn(businessIds)
        ;
        example.setOffset(pageParam.getOffset());
        example.setLimit(pageParam.getSize());
        List<AdjustPriceOrderOrgStoreDetail> storeDetailList = adjustPriceOrderOrgStoreDetailMapper.selectByExample(example);
        return storeDetailList == null ? Collections.emptyList() : storeDetailList;
    }

    @Override
    public List<AdjustPriceOrderOrgStoreDetail> listByAdjustCodeAndStoreOrgIdsAndGoodnoes(String adjustCode, List<Long> storeOrgIds,
        List<String> goodsnoList, int page, int pageSize) {
        if (adjustCode == null || CollectionUtils.isEmpty(storeOrgIds)) {
            return Collections.emptyList();
        }
        AmisPageParam pageParam = AmisPageParam.createInstance(page, pageSize);
        List<Long> storeDetailIdList = adjustPriceOrderOrgStoreDetailExtMapper.listIdByAdjustCodeAndStoreOrgIdsAndGoodnoes(adjustCode, storeOrgIds, goodsnoList,null,false, pageParam.getSize(), pageParam.getOffset());
        if(CollectionUtils.isEmpty(storeDetailIdList)) {
        	return Collections.emptyList();
        }
        AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
        orgStoreDetailExample.createCriteria().andIdIn(storeDetailIdList).andAdjustCodeEqualTo(adjustCode);
        return adjustPriceOrderOrgStoreDetailMapper.selectByExample(orgStoreDetailExample);
    }

    @Override
    public void batchInsert(List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList) {
        adjustPriceOrderOrgStoreDetailExtMapper.batchInsert(orderOrgStoreDetailList);
    }

    @Override
    public int updateStatus(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId,
                            AdjustPriceOrderOrgStoreDetailEnum.Status status, String result) {
        AdjustPriceOrderOrgStoreDetail orgStoreDetail = AdjustPriceOrderOrgStoreDetail.builder()
            .status((byte)status.getCode())
            .result(result)
            .build();
        AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
        AdjustPriceOrderOrgStoreDetailExample.Criteria criteria = orgStoreDetailExample.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
            .andAdjustCodeEqualTo(adjustCode)
            .andGoodsNoEqualTo(goodsNo)
            .andPriceTypeCodeEqualTo(priceTypeCode)
            .andChannelIdEqualTo(channelId);
        if (Objects.nonNull(skuId)) {
            criteria.andSkuIdEqualTo(skuId);
        }
        if (PriceConstant.UPDATE_PRICE_STORE_DETAIL_FAIL_TIPS.equals(result)) {
            criteria.andResultIsNull();
        }
        return this.updateByExampleSelective(orgStoreDetail, orgStoreDetailExample);
    }

    @Override
    public int recordResult(String adjustCode, Long storeId, String goodsNo, String priceTypeCode, Integer channelId, Long skuId, String result) {
        AdjustPriceOrderOrgStoreDetail orgStoreDetail = AdjustPriceOrderOrgStoreDetail.builder()
            .result(result)
            .build();
        AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
        AdjustPriceOrderOrgStoreDetailExample.Criteria criteria = orgStoreDetailExample.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
            .andAdjustCodeEqualTo(adjustCode)
            .andGoodsNoEqualTo(goodsNo)
            .andPriceTypeCodeEqualTo(priceTypeCode)
            .andChannelIdEqualTo(channelId);
        if (Objects.nonNull(skuId)) {
            criteria.andSkuIdEqualTo(skuId);
        }
        return this.updateByExampleSelective(orgStoreDetail, orgStoreDetailExample);
    }

    @Override
	public void supplementExtendData(OrgStoreDetailSplitDTO splitParam) {
		logger.info("AdjustPriceOrderOrgStoreDetailServiceImpl|supplementExtendData|splitParam:{}",JSON.toJSONString(splitParam));
		AdjustPriceOrder adjustPriceOrder = adjustPriceOrderExMapper.selectByAdjustCode(splitParam.getAdjustCode());
		if(null==adjustPriceOrder) {
			return;
		}
		AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
		orgStoreDetailExample.createCriteria().
		andAdjustCodeEqualTo(splitParam.getAdjustCode()).andStoreIdEqualTo(splitParam.getStoreId()).
		andPriceTypeCodeEqualTo(splitParam.getPriceTypeCode()).
		andChannelIdEqualTo(splitParam.getChannelId());
		List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList = adjustPriceOrderOrgStoreDetailMapper.selectByExample(orgStoreDetailExample);
		if(CollectionUtils.isEmpty(orgStoreDetailList)) {
			return;
		}
		String lastTime = DateUtils.dateToString(adjustPriceOrder.getGmtCreate());
		List<AdjustPriceOrderOrgStoreDetail> changeData = Lists.newArrayList();
		for (AdjustPriceOrderOrgStoreDetail orgStoreDetail : orgStoreDetailList) {
			PriceDetailHistory priceDetailHistory = priceDetailHistoryExtMapper.findPreviousPriceByStoreIdAndGoodsNoAndPriceType(splitParam.getStoreId(), orgStoreDetail.getGoodsNo(), splitParam.getPriceTypeCode(),splitParam.getChannelId(),lastTime);
			if(null==priceDetailHistory) {
				continue;
			}

			String extend = orgStoreDetail.getExtend();
			Optional<AdjustPriceOrderOrgStoreDetailExtend> currInstance = AdjustPriceOrderOrgStoreDetailExtend.getInstance(extend);
			AdjustPriceOrderOrgStoreDetailExtend currExtend = currInstance.get();

			if(null!=priceDetailHistory.getPrice() && priceDetailHistory.getPrice().compareTo(BigDecimal.ZERO)>0) {
				currExtend.setAdjustBeforePrice(priceDetailHistory.getPrice());
			}

			Optional<PriceDetailHistoryExtend1> hisExtend1Instance = PriceDetailHistoryExtend1.getInstance(priceDetailHistory.getExtend1());
			if(hisExtend1Instance.isPresent()) {
				PriceDetailHistoryExtend1 hisExtend1 = hisExtend1Instance.get();
				currExtend.setBeforeGoodsTypeName(hisExtend1.getBeforeGoodsTypeName());
				currExtend.setBeforeActivityName(hisExtend1.getBeforeActivityName());
			}

			orgStoreDetail.setExtend(AdjustPriceOrderOrgStoreDetailExtend.toJSONFormatStr(currExtend));
			changeData.add(orgStoreDetail);
		}
		List<List<AdjustPriceOrderOrgStoreDetail>> partition = Lists.partition(changeData, PriceConstant.BATCH_COMMIT_SIZE);
		for (List<AdjustPriceOrderOrgStoreDetail> subList : partition) {
			adjustPriceOrderOrgStoreDetailExtMapper.batchUpdateExtend(adjustPriceOrder.getAdjustCode(),subList);
		}
	}

	@Override
	public int updateOrgStoreExtend(PricePushResultVO taskResult) {
		String pushCode = taskResult.getPushCode();
        Integer batch = taskResult.getBatch();
        int updateResult = 0;
        String key = RedisKeysConstant.PRICE_ADJUST_POS_ORG_STORE_CALLBACK + pushCode + "_" + batch;
        RLock lock = redisson.getLock(key);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(2, 5, TimeUnit.SECONDS);
            if (!isLocked) {
            	logger.info("未获取到锁|pushCode：{}",pushCode);
                return updateResult;
            }
            PricePushTaskExample pricePushTaskExample = new PricePushTaskExample();
            pricePushTaskExample.createCriteria()
                .andPushCodeEqualTo(pushCode)
                .andBatchEqualTo(batch);
            List<PricePushTask> pricePushTasks = pricePushTaskMapper.selectByExample(pricePushTaskExample);
            if (CollectionUtils.isEmpty(pricePushTasks)) {
            	return updateResult;
            }
            //根据pushCode、batch查询price_push_history
            PricePushHistoryExample pricePushHistoryExample=new PricePushHistoryExample();
            pricePushHistoryExample.createCriteria().andPushCodeEqualTo(pushCode).andBatchEqualTo(batch);
            List<PricePushHistory> pushHistoryList = pricePushHistoryService.selectByExample(pricePushHistoryExample);
            if(CollectionUtils.isEmpty(pushHistoryList)) {
            	return updateResult;
            }
            //常规处理
            saveOrgStoreExtendAormal(taskResult, pushHistoryList);
            //执价异常商品处理
            saveOrgStoreExtendFail(taskResult, pricePushTasks);
            updateResult = SUCCESS;
        } catch (InterruptedException e) {
            throw new BusinessErrorException("系统异常");
        } finally {
            if (isLocked && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return updateResult;
	}

	/**
	 *
	 * @Title: saveOrgStoreExtendAormal
	 * @Description: 常规处理 - 优化为批量更新
	 * @param: @param taskResult
	 * @param: @param pushHistoryList
	 * @return: void
	 * @throws
	 */
	private void saveOrgStoreExtendAormal(PricePushResultVO taskResult,List<PricePushHistory> pushHistoryList) {
		if (CollectionUtils.isEmpty(pushHistoryList)) {
			return;
		}

		// 批量查询所有需要更新的记录
		List<AdjustPriceOrderOrgStoreDetail> batchUpdateList = new ArrayList<>();
		String adjustCode = null;

		for (PricePushHistory pricePushHistory : pushHistoryList) {
			if (adjustCode == null) {
				adjustCode = pricePushHistory.getAdjustCode();
			}

			// 查询对应的门店详情记录
			AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
			orgStoreDetailExample.createCriteria()
				.andAdjustCodeEqualTo(pricePushHistory.getAdjustCode())
				.andStoreIdEqualTo(pricePushHistory.getStoreId())
				.andChannelIdEqualTo(pricePushHistory.getChannelId())
				.andGoodsNoEqualTo(pricePushHistory.getGoodsNo())
				.andPriceTypeCodeEqualTo(pricePushHistory.getPriceTypeCode());
            orgStoreDetailExample.setLimit(1);
			List<AdjustPriceOrderOrgStoreDetail> storeDetailList = adjustPriceOrderOrgStoreDetailMapper.selectByExample(orgStoreDetailExample);
			if (CollectionUtils.isEmpty(storeDetailList)) {
				continue;
			}

			AdjustPriceOrderOrgStoreDetail storeDetail = storeDetailList.get(0);
			//设置pricePushHistoryId
			setPricePushHistoryId(storeDetail, pricePushHistory);

			String extend = storeDetail.getExtend();
			Optional<AdjustPriceOrderOrgStoreDetailExtend> instance = AdjustPriceOrderOrgStoreDetailExtend.getInstance(extend);
			AdjustPriceOrderOrgStoreDetailExtend orgStoreDetailExtend = instance.get();

			// 根据任务结果类型更新状态
			updateExtendStatusByTaskResult(orgStoreDetailExtend, taskResult);

			// 设置推送码和更新时间
			orgStoreDetailExtend.setPushCode(pricePushHistory.getPushCode());
			storeDetail.setExtend(AdjustPriceOrderOrgStoreDetailExtend.toJSONFormatStr(orgStoreDetailExtend));
			storeDetail.setStatus(null);
			storeDetail.setGmtUpdate(new Date());

			batchUpdateList.add(storeDetail);
		}

		// 执行批量更新
		if (!batchUpdateList.isEmpty()) {
			// 使用现有的批量更新方法，按批次处理
			List<List<AdjustPriceOrderOrgStoreDetail>> partition = Lists.partition(batchUpdateList, PriceConstant.BATCH_COMMIT_SIZE);
			for (List<AdjustPriceOrderOrgStoreDetail> subList : partition) {
				adjustPriceOrderOrgStoreDetailExtMapper.batchUpdateExtend(adjustCode, subList);
			}
			logger.info("saveOrgStoreExtendAormal批量更新完成，adjustCode={}, 更新记录数={}", adjustCode, batchUpdateList.size());
		}
	}

	/**
	 * 根据任务结果更新扩展状态
	 */
	private void updateExtendStatusByTaskResult(AdjustPriceOrderOrgStoreDetailExtend orgStoreDetailExtend, PricePushResultVO taskResult) {
		if(taskResult.getType()==DealPriceStepEnum.RECEIVED.getCode()) {
			if(taskResult.getCode()==DealStepStatusEnum.EXE_SUCCESS.getCode()) {
				orgStoreDetailExtend.setSendStatus(DealStepStatusEnum.EXE_SUCCESS.getCode());
				orgStoreDetailExtend.setSendMsg(taskResult.getMsg());
			}else {
				orgStoreDetailExtend.setSendStatus(DealStepStatusEnum.EXE_FAIL.getCode());
				orgStoreDetailExtend.setSendMsg(taskResult.getMsg());
				orgStoreDetailExtend.setReceiveStatus(DealStepStatusEnum.EXE_FAIL.getCode());
				orgStoreDetailExtend.setReceiveMsg("");
				orgStoreDetailExtend.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
				orgStoreDetailExtend.setEffectMsg("");
			}
		} else if(taskResult.getType()==DealPriceStepEnum.UPDATED.getCode()) {
			if(taskResult.getCode()==DealStepStatusEnum.EXE_SUCCESS.getCode()) {
				orgStoreDetailExtend.setReceiveStatus(DealStepStatusEnum.EXE_SUCCESS.getCode());
				orgStoreDetailExtend.setReceiveMsg(taskResult.getMsg());
			}else {
				orgStoreDetailExtend.setReceiveStatus(DealStepStatusEnum.EXE_FAIL.getCode());
				orgStoreDetailExtend.setReceiveMsg(taskResult.getMsg());
				orgStoreDetailExtend.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
				orgStoreDetailExtend.setEffectMsg("");
			}
		} else if(taskResult.getType()==DealPriceStepEnum.EFFECTED.getCode()) {
			if(taskResult.getCode()==DealStepStatusEnum.EXE_SUCCESS.getCode()) {
				orgStoreDetailExtend.setEffectStatus(DealStepStatusEnum.EXE_SUCCESS.getCode());
				orgStoreDetailExtend.setEffectMsg(taskResult.getMsg());
				orgStoreDetailExtend.setReceiveStatus(DealStepStatusEnum.EXE_SUCCESS.getCode());
				orgStoreDetailExtend.setReceiveMsg(DealStepStatusEnum.EXE_SUCCESS.getMessage());
				orgStoreDetailExtend.setSendStatus(DealStepStatusEnum.EXE_SUCCESS.getCode());
				orgStoreDetailExtend.setSendMsg(DealStepStatusEnum.EXE_SUCCESS.getMessage());
			}else {
				orgStoreDetailExtend.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
				orgStoreDetailExtend.setEffectMsg(taskResult.getMsg());
			}
		}
	}

	private void setPricePushHistoryId(AdjustPriceOrderOrgStoreDetail storeDetail,PricePushHistory pricePushHistory) {
		String extend = storeDetail.getExtend();
		Optional<AdjustPriceOrderOrgStoreDetailExtend> instance = AdjustPriceOrderOrgStoreDetailExtend.getInstance(extend);
		if(instance.isPresent()) {
			AdjustPriceOrderOrgStoreDetailExtend adjustPriceOrderOrgStoreDetailExtend = instance.get();
			adjustPriceOrderOrgStoreDetailExtend.setPricePushHistoryId(pricePushHistory.getId());
			storeDetail.setExtend(AdjustPriceOrderOrgStoreDetailExtend.toJSONFormatStr(adjustPriceOrderOrgStoreDetailExtend));
		}else {
			String jsonFormatStr = AdjustPriceOrderOrgStoreDetailExtend.getJSONFormatStr(pricePushHistory.getId());
			storeDetail.setExtend(jsonFormatStr);
		}
	}
	/**
	 *
	 * @Title: saveOrgStoreExtendFail
	 * @Description: 执价异常商品处理 - 优化为批量更新
	 * @param: @param taskResult
	 * @param: @param pricePushTasks
	 * @return: void
	 * @throws
	 */
	private void saveOrgStoreExtendFail(PricePushResultVO taskResult,List<PricePushTask> pricePushTasks) {
		// 如果priceList不为空，说明有价格在线下POS执行失败，这里需要处理失败的数据
        List<PricePushResultVO.Detail> priceList = taskResult.getPriceList();
    	if(CollectionUtils.isEmpty(priceList)) {
    		return;
    	}
        Date now = new Date();
    	PricePushTask pricePushTask = pricePushTasks.get(0);
        String adjustCode = pricePushTask.getAdjustCode();
    	String storeNo = priceList.get(0).getStoreId();
    	MdmStoreBaseDTO mdmStoreBase = feignStoreService.getMdmStoreBase(storeNo);
    	// 批量处理失败的商品
    	List<AdjustPriceOrderOrgStoreDetail> batchUpdateList = new ArrayList<>();
    	for (PricePushResultVO.Detail detail : priceList) {
    		AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
    		orgStoreDetailExample.createCriteria()
    			.andAdjustCodeEqualTo(adjustCode)
    			.andStoreIdEqualTo(mdmStoreBase.getStoreId())
    			.andChannelIdEqualTo(0)
        		.andGoodsNoEqualTo(detail.getGoodsNo())
        		.andPriceTypeCodeEqualTo(detail.getPriceTypeCode());
            orgStoreDetailExample.setLimit(1);
    		List<AdjustPriceOrderOrgStoreDetail> detailList = adjustPriceOrderOrgStoreDetailMapper.selectByExample(orgStoreDetailExample);
        	if(CollectionUtils.isNotEmpty(detailList)) {
        		AdjustPriceOrderOrgStoreDetail orgStoreDetail = detailList.get(0);
        		String extend = orgStoreDetail.getExtend();
        		Optional<AdjustPriceOrderOrgStoreDetailExtend> instance = AdjustPriceOrderOrgStoreDetailExtend.getInstance(extend);
        		AdjustPriceOrderOrgStoreDetailExtend orgStoreDetailExtend = instance.get();
        		// 根据任务类型设置失败状态
        		updateExtendFailStatusByTaskType(orgStoreDetailExtend, taskResult, detail);
        		orgStoreDetail.setExtend(AdjustPriceOrderOrgStoreDetailExtend.toJSONFormatStr(orgStoreDetailExtend));
        		orgStoreDetail.setGmtUpdate(now);
        		orgStoreDetail.setStatus(null);
        		batchUpdateList.add(orgStoreDetail);
        	}
		}
		// 执行批量更新
		if (!batchUpdateList.isEmpty()) {
			// 使用现有的批量更新方法，按批次处理
			List<List<AdjustPriceOrderOrgStoreDetail>> partition = Lists.partition(batchUpdateList, PriceConstant.BATCH_COMMIT_SIZE);
			for (List<AdjustPriceOrderOrgStoreDetail> subList : partition) {
				adjustPriceOrderOrgStoreDetailExtMapper.batchUpdateExtend(adjustCode, subList);
			}
			logger.info("saveOrgStoreExtendFail批量更新完成，adjustCode={}, 更新记录数={}", adjustCode, batchUpdateList.size());
		}
	}

	/**
	 * 根据任务类型更新失败状态
	 */
	private void updateExtendFailStatusByTaskType(AdjustPriceOrderOrgStoreDetailExtend orgStoreDetailExtend,
			PricePushResultVO taskResult, PricePushResultVO.Detail detail) {
		if(taskResult.getType()==DealPriceStepEnum.RECEIVED.getCode()) {
			orgStoreDetailExtend.setSendStatus(DealStepStatusEnum.EXE_FAIL.getCode());
			orgStoreDetailExtend.setSendMsg(detail.getMsg());
			orgStoreDetailExtend.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
			orgStoreDetailExtend.setEffectMsg(detail.getMsg());
		} else if(taskResult.getType()==DealPriceStepEnum.UPDATED.getCode()) {
			orgStoreDetailExtend.setReceiveStatus(DealStepStatusEnum.EXE_FAIL.getCode());
			orgStoreDetailExtend.setReceiveMsg(detail.getMsg());
			orgStoreDetailExtend.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
			orgStoreDetailExtend.setEffectMsg(detail.getMsg());
		} else if(taskResult.getType()==DealPriceStepEnum.EFFECTED.getCode()) {
			orgStoreDetailExtend.setEffectStatus(DealStepStatusEnum.EXE_FAIL.getCode());
			orgStoreDetailExtend.setEffectMsg(detail.getMsg());
		}
	}

	@Override
	public void supplementSplitExtendData(String adjustCode) {

		//预生效调价单
        adjustPriceOrderDetailService.distributePreEffectAdjustPriceOrder(adjustCode);

        //补调价前价格
		List<OrgStoreDetailSplitDTO> orgStoreSpiltList = adjustPriceOrderOrgStoreDetailExtMapper.selectOrgStoreDetailSplit(adjustCode);

		if(CollectionUtils.isEmpty(orgStoreSpiltList)) {
			return;
		}

		orgStoreSpiltList.forEach(orgStoreDetail -> adjustPriceOrderOrgStoreDetailActualProducer.sendMq(orgStoreDetail));
	}

	@Override
	public PageResult<AdjustPriceBusinessIdPriceDTO> listAdjustBusinessGoods(AdjustPriceBusinessIdPriceParam query) {
		PageResult<AdjustPriceBusinessIdPriceDTO> pageResult = new PageResult<>();
		 AdjustPriceOrderOrgStoreDetailExample queryExample = new AdjustPriceOrderOrgStoreDetailExample();
		 Criteria criteria = queryExample.createCriteria();
		 if (StringUtils.isNotBlank(query.getAdjustCode())) {
			 criteria.andAdjustCodeEqualTo(query.getAdjustCode());
         }else{
             logger.info("listAdjustBusinessGoods|入参调价单号为空");
             return pageResult;
         }
         if (Objects.nonNull(query.getPriceTypeCode())) {
            criteria.andPriceTypeCodeEqualTo(query.getPriceTypeCode());
         }
	     queryExample.setLimit(query.getPageSize());
	     queryExample.setOffset(((long) (query.getPage()-1) * query.getPageSize()));
	     List<AdjustPriceOrderOrgStoreDetail> detailList = adjustPriceOrderOrgStoreDetailExtMapper.distinctBusinessPage(queryExample);
	     List<AdjustPriceBusinessIdPriceDTO> businessPriceList = detailList.stream().map(v -> {
	    	 AdjustPriceBusinessIdPriceDTO record = new AdjustPriceBusinessIdPriceDTO();
	         BeanUtils.copyProperties(v, record);
	         return record;
	     }).collect(Collectors.toList());
	     pageResult.setRows(businessPriceList);
	     return pageResult;
	}

}

