package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.PriceManageControlOrderDetailMapper;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrderDetailExtMapper;
import com.cowell.pricecenter.mq.producer.ControlOrderDetailSupplementDataProducer;
import com.cowell.pricecenter.mq.vo.ControlOrderDetailSupplementDataVO;
import com.cowell.pricecenter.service.IBasePriceOrderService;
import com.cowell.pricecenter.service.ITocExtService;
import com.cowell.pricecenter.service.PriceManageControlOrderDetailReadService;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderManagePriceDTO;
import com.cowell.pricecenter.service.dto.ControlOrderDetailQueryDTO;
import com.cowell.pricecenter.service.dto.PriceOrderPriceTypeAndChannelChange;
import com.cowell.pricecenter.service.dto.request.ControlOrderDetailListV2Param;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.controlOrder.ControlOrderExtendDTO;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.cowell.pricecenter.web.rest.errors.AmisBusinessException;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/4/29 10:38
 */
@Service
public class PriceManageControlOrderDetailReadServiceImpl implements PriceManageControlOrderDetailReadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceManageControlOrderDetailReadServiceImpl.class);


    @Resource
    private IBasePriceOrderService basePriceOrderService;

    @Resource
    private PriceManageControlOrderDetailMapper priceManageControlOrderDetailMapper;

    @Autowired
    private PriceManageControlOrderDetailExtMapper priceManageControlOrderDetailExtMapper;

    @Resource
    private ITocExtService tocExtService;

    @Resource
    private ControlOrderDetailSupplementDataProducer supplementDataProducer;

    @Override
    public long countByExample(PriceManageControlOrderDetailExample example) {
        return priceManageControlOrderDetailMapper.countByExample(example);
    }

    @Override
    public List<PriceManageControlOrderDetail> selectByExample(PriceManageControlOrderDetailExample example) {
        return priceManageControlOrderDetailMapper.selectByExample(example);
    }

    @Override
    public PriceManageControlOrderDetail selectByPrimaryKey(Long id) {
        return priceManageControlOrderDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<PriceManageControlOrderDetail> getControlOrderDetailList(ControlOrderDetailQueryDTO queryDTO) {

        List<String> controlOrderIds = null;
        if (StringUtils.isNotBlank(queryDTO.getControlOrderCode())) {
            controlOrderIds = Lists.newArrayList(queryDTO.getControlOrderCode());
        }

        if (CollectionUtils.isEmpty(queryDTO.getControlOrderCodes())) {
            queryDTO.setControlOrderCodes(Lists.newArrayList());
        }

        if (CollectionUtils.isNotEmpty(controlOrderIds)) {
            queryDTO.getControlOrderCodes().addAll(controlOrderIds);
        }
        PriceManageControlOrderDetailExample orderDetailExample = new PriceManageControlOrderDetailExample();
        PriceManageControlOrderDetailExample.Criteria criteria = orderDetailExample.createCriteria();
        if (CollectionUtils.isNotEmpty(queryDTO.getControlOrderCodes())) {
            criteria.andControlOrderCodeIn(queryDTO.getControlOrderCodes());
        }
        return priceManageControlOrderDetailMapper.selectByExample(orderDetailExample);
    }

    @Override
    public long getControlOrderDetailCount(ControlOrderDetailQueryDTO queryDTO) {
        List<String> controlOrderCodes = null;
        if (StringUtils.isNotBlank(queryDTO.getControlOrderCode())) {
            controlOrderCodes = Lists.newArrayList(queryDTO.getControlOrderCode());
        }

        if (CollectionUtils.isEmpty(queryDTO.getControlOrderCodes())) {
            queryDTO.setControlOrderCodes(Lists.newArrayList());
        }

        if (CollectionUtils.isNotEmpty(controlOrderCodes)) {
            queryDTO.getControlOrderCodes().addAll(controlOrderCodes);
        }

        PriceManageControlOrderDetailExample orderDetailExample = new PriceManageControlOrderDetailExample();
        PriceManageControlOrderDetailExample.Criteria criteria = orderDetailExample.createCriteria();
        if (CollectionUtils.isNotEmpty(queryDTO.getControlOrderCodes())) {
            criteria.andControlOrderCodeIn(queryDTO.getControlOrderCodes());
        }
        return priceManageControlOrderDetailMapper.countByExample(orderDetailExample);
    }

    @Override
    public Optional<AdjustPriceOrderManagePriceDTO> getNewestAdjustPriceOrderManagePriceDTO(String goodsNo, List<Long> orgIdList, String priceTypeCode, Integer channelId, Date effectTime) {
        AdjustPriceOrderManagePriceDTO adjustPriceOrderManagePriceDTO =
            priceManageControlOrderDetailExtMapper.getNewestAdjustPriceOrderManagePriceDTO(goodsNo, orgIdList, priceTypeCode, channelId, effectTime);
        return adjustPriceOrderManagePriceDTO == null ? Optional.empty() : Optional.of(adjustPriceOrderManagePriceDTO);
    }

    @Override
    public void resetOrderDetailsByChannelsAndPriceTypes(PriceManageControlOrder priceManageControlEditOrder, PriceOrderPriceTypeAndChannelChange orderPriceTypeAndChannelChange,
                                                         TokenUserDTO userDTO, Date operateTimeDate) {
        if (priceManageControlEditOrder == null || priceManageControlEditOrder.getControlOrderId() == null || orderPriceTypeAndChannelChange == null) {
            LOGGER.error("<===[PriceManageControlStoreDetailServiceImpl.resetOrderDetailsByChannelsAndPriceTypes]  参数都不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        String controlOrderCode = priceManageControlEditOrder.getControlOrderId();
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getDeleteChannelIdList())) {
            deleteByControlOrderCodeAndChannelIds(controlOrderCode, orderPriceTypeAndChannelChange.getDeleteChannelIdList());
        }
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getDeletePriceTypeCodeList())) {
            deleteByControlOrderPriceTypeCodes(controlOrderCode, orderPriceTypeAndChannelChange.getDeletePriceTypeCodeList());
        }
        //有新增渠道和有旧的价格类型保留
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getAddChannelIdList()) &&
            CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getLeavePriceTypeCodeList())) {
            copyOrderDetailByChannelIdsAndPriceTypeCodes(controlOrderCode, orderPriceTypeAndChannelChange.getAddChannelIdList(),
                orderPriceTypeAndChannelChange.getLeavePriceTypeCodeList(), operateTimeDate);
        }
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getChangeAfterChannelIdList()) &&
            CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getAddPriceTypeCodeList())
        ) {
            addOrderDetailByChannelIdsAndPriceTypeCodes(priceManageControlEditOrder, orderPriceTypeAndChannelChange.getChangeAfterChannelIdList(),
                orderPriceTypeAndChannelChange.getAddPriceTypeCodeList(), userDTO, operateTimeDate);
        }
    }

    /**
     * 通过渠道删除明细
     * @param controlOrderCode
     * @param deleteChannelIdList
     */
    private void deleteByControlOrderCodeAndChannelIds(String controlOrderCode, List<Integer> deleteChannelIdList) {
        PriceManageControlOrderDetailExample deleteNotInChannelIdsExample = new PriceManageControlOrderDetailExample();
        deleteNotInChannelIdsExample.createCriteria().andControlOrderCodeEqualTo(controlOrderCode).andChannelIdIn(deleteChannelIdList);
        priceManageControlOrderDetailMapper.deleteByExample(deleteNotInChannelIdsExample);
    }

    /**
     * 通过价格类型编码删除明细
     * @param controlOrderCode
     * @param deletePriceTypeCodeList
     */
    private void deleteByControlOrderPriceTypeCodes(String controlOrderCode, List<String> deletePriceTypeCodeList) {
        PriceManageControlOrderDetailExample deleteNotInPriceTypeIdsExample = new PriceManageControlOrderDetailExample();
        deleteNotInPriceTypeIdsExample.createCriteria().andControlOrderCodeEqualTo(controlOrderCode).andPriceTypeCodeIn(deletePriceTypeCodeList);
        priceManageControlOrderDetailMapper.deleteByExample(deleteNotInPriceTypeIdsExample);
    }

    /**
     * 根据渠道类型拷贝遗留下来的价格类型数据
     * @param controlOrderCode
     * @param addChannelIdList
     * @param leavePriceTypeCodeList
     */
    private void copyOrderDetailByChannelIdsAndPriceTypeCodes(String controlOrderCode, List<Integer> addChannelIdList,
                                                              List<String> leavePriceTypeCodeList, Date operateTime) {
        List<PriceManageControlOrderDetail> adjustPriceOrderDetails = selectOrderDetailV2ListGroupByGoodAndPriceType(controlOrderCode, leavePriceTypeCodeList);
        int size = addChannelIdList.size() * adjustPriceOrderDetails.size();
        List<PriceManageControlOrderDetail> toInsertOrderDetais = new ArrayList<>(size);
        long [] adjustDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.PRICE_MANAGE_CONTROL_ORDER_DETAIL.getBiz(), size);
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(addChannelIdList);
        int idIndex = 0;
        for (PriceChannel priceChannel : priceChannelMap.values()) {
            for (PriceManageControlOrderDetail adjustPriceOrderDetail : adjustPriceOrderDetails) {
                PriceManageControlOrderDetail toInsertOrderDetail = copyFromPriceTypeOrderDetail(adjustPriceOrderDetail, operateTime);
                toInsertOrderDetail.setChannelId(priceChannel.getChannelId());
                toInsertOrderDetail.setChannelOutCode(priceChannel.getOutChannelCode());
                toInsertOrderDetail.setChannelEnCode(priceChannel.getChannelEnCode());
                toInsertOrderDetail.setControlOrderDetailId(String.valueOf(adjustDetailIds[idIndex++]));
                toInsertOrderDetail.setOrderType((byte)1);
                toInsertOrderDetais.add(toInsertOrderDetail);
            }
        }
        com.google.common.collect.Lists.partition(toInsertOrderDetais, 200)
            .forEach(toInsertPartitionOrderDetais -> priceManageControlOrderDetailExtMapper.batchInsert(toInsertPartitionOrderDetais));
    }

    private PriceManageControlOrderDetail copyFromPriceTypeOrderDetail(PriceManageControlOrderDetail adjustPriceOrderDetail, Date operateTime) {
        PriceManageControlOrderDetail result = new PriceManageControlOrderDetail();
        result.setControlOrderCode(adjustPriceOrderDetail.getControlOrderCode());
        result.setAuthOrgId(adjustPriceOrderDetail.getAuthOrgId());
        result.setAuthOrgName(adjustPriceOrderDetail.getAuthOrgName());
        result.setAuthLevel(adjustPriceOrderDetail.getAuthLevel());
        result.setSpuId(adjustPriceOrderDetail.getSpuId());
        result.setGoodsNo(adjustPriceOrderDetail.getGoodsNo());
        result.setGoodsName(adjustPriceOrderDetail.getGoodsName());
        result.setGoodsUnit(adjustPriceOrderDetail.getGoodsUnit());
        result.setProdarea(adjustPriceOrderDetail.getProdarea());
        result.setPriceTypeId(adjustPriceOrderDetail.getPriceTypeId());
        result.setPriceTypeCode(adjustPriceOrderDetail.getPriceTypeCode());
        result.setPriceTypeName(adjustPriceOrderDetail.getPriceTypeName());
        result.setPrice(adjustPriceOrderDetail.getPrice());
        result.setOriginalPrice(adjustPriceOrderDetail.getOriginalPrice());
        result.setUpperLimit(adjustPriceOrderDetail.getUpperLimit());
        result.setLowerLimit(adjustPriceOrderDetail.getLowerLimit());
        result.setCurName(adjustPriceOrderDetail.getCurName());
        result.setManufacturer(adjustPriceOrderDetail.getManufacturer());
        result.setJhiSpecification(adjustPriceOrderDetail.getJhiSpecification());
        result.setDosage(adjustPriceOrderDetail.getDosage());
        result.setStatus(adjustPriceOrderDetail.getStatus());
        result.setCreatedBy(adjustPriceOrderDetail.getCreatedBy());
        result.setExtend1(adjustPriceOrderDetail.getExtend1());
        result.setVersion(1);
        result.setGoodsName(adjustPriceOrderDetail.getGoodsName());
        result.setGuidePrice(adjustPriceOrderDetail.getGuidePrice());
        result.setUpdatedBy(adjustPriceOrderDetail.getUpdatedBy());
        result.setUpdatedByName(adjustPriceOrderDetail.getUpdatedByName());
        result.setPrice(adjustPriceOrderDetail.getPrice());
        return result;
    }

    /**
     * 按照商品和价格类型
     * @param controlOrderCode
     * @param leavePriceTypeCodeList
     * @return
     */
    protected List<PriceManageControlOrderDetail> selectOrderDetailV2ListGroupByGoodAndPriceType(String controlOrderCode, List<String> leavePriceTypeCodeList) {
        ControlOrderDetailListV2Param param = new ControlOrderDetailListV2Param();
        param.setControlOrderCode(controlOrderCode);
        param.setToPage(false);
        param.setPriceTypeCodeList(leavePriceTypeCodeList);
        List<PriceManageControlOrderDetail> adjustPriceOrderDetailList = priceManageControlOrderDetailExtMapper.selectOrderDetailV2ListPageGroupByGoodAndPriceType(param);
        return adjustPriceOrderDetailList == null ? Collections.emptyList() : adjustPriceOrderDetailList;
    }

    protected void addOrderDetailByChannelIdsAndPriceTypeCodes(PriceManageControlOrder priceManageControlOrder, List<Integer> addChannelIdList, List<String> addPriceTypeCodeList,
                                                               TokenUserDTO userDTO, Date operateTime) {
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(addPriceTypeCodeList);
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(addChannelIdList);
        List<String> goodsNoList = getGoodsNoListbyControlOrderCode(priceManageControlOrder.getControlOrderId());
        List<PriceManageControlOrderDetail> insertAdjustPriceOrderDetailList = getInsertControlOrderDetailList(priceManageControlOrder, goodsNoList, priceChannelMap, priceTypeMap,
            addChannelIdList, addPriceTypeCodeList, userDTO, operateTime);
        com.google.common.collect.Lists.partition(insertAdjustPriceOrderDetailList, 200)
            .forEach(toInsertGoodsNoList -> priceManageControlOrderDetailExtMapper.batchInsert(toInsertGoodsNoList));
        //异步补全
        insertAdjustPriceOrderDetailList.stream().map(controlOrderDetail -> {
            ControlOrderDetailSupplementDataVO supplementDataVO = new ControlOrderDetailSupplementDataVO();
            supplementDataVO.setControlOrderCode(controlOrderDetail.getControlOrderCode());
            supplementDataVO.setPriceTypeCode(controlOrderDetail.getPriceTypeCode());
            supplementDataVO.setGoodsNo(controlOrderDetail.getGoodsNo());
            return supplementDataVO;
        }).distinct().forEach(supplementDataVO -> supplementDataProducer.sendMq(supplementDataVO));

    }

    private List<PriceManageControlOrderDetail> getInsertControlOrderDetailList(PriceManageControlOrder manageControlOrder,
                                                                                List<String> toAddGoodsNoList,
                                                                                Map<Integer, PriceChannel> priceChannelMap,
                                                                                Map<String, PriceType> priceTypeMap,
                                                                                List<Integer> channelList,
                                                                                List<String> priceTypeList,
                                                                                TokenUserDTO userDTO, Date operateTime) {
        int size = channelList.size() * priceTypeList.size() * toAddGoodsNoList.size();
        LOGGER.info("controlOrderGoodsAdd|size:{}.",size);
        if(size<=0){
            LOGGER.warn("添加明细商品失败");
            return Lists.newArrayList();
        }
        List<PriceManageControlOrderDetail> controlOrderDetailList = new ArrayList<>(size);
        long [] adjustDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.PRICE_MANAGE_CONTROL_ORDER_DETAIL.getBiz(), size);
        LOGGER.info("controlOrderGoodsAdd|adjustDetailIds:{}.",adjustDetailIds.length);
        int idIndex = 0;
        for (String goodsCode : toAddGoodsNoList) {
            for (String priceTypeCode : priceTypeList) {

                PriceType priceType = priceTypeMap.get(priceTypeCode);
                if (priceType == null) {
                    LOGGER.error("<===[controlOrderGoodsAdd]  priceTypeCode: {} 不存在", priceTypeCode);
                    throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR);
                }

                for (Integer channel : channelList) {

                    PriceChannel priceChannel = priceChannelMap.get(channel);
                    if (priceChannel == null) {
                        LOGGER.error("<===[controlOrderGoodsAdd]  channelId: {} 不存在", channel);
                        throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR);
                    }

                    PriceManageControlOrderDetail controlOrderDetail = new PriceManageControlOrderDetail();
                    controlOrderDetail.setControlOrderDetailId(String.valueOf(adjustDetailIds[idIndex++]));
                    controlOrderDetail.setControlOrderCode(manageControlOrder.getControlOrderId());
                    controlOrderDetail.setAuthOrgId(manageControlOrder.getOrgId());
                    controlOrderDetail.setAuthOrgName(manageControlOrder.getOrgName());
                    controlOrderDetail.setAuthLevel(manageControlOrder.getOrderLevel());
                    controlOrderDetail.setOrgGoodsId(null);
                    controlOrderDetail.setGoodsNo(goodsCode);
                    controlOrderDetail.setPriceGroupNames("");
                    controlOrderDetail.setPriceTypeId(priceType.getId());
                    controlOrderDetail.setPriceTypeCode(priceTypeCode);
                    controlOrderDetail.setPriceTypeName(priceType.getName());
                    controlOrderDetail.setPriceTypeOutCode(null);//取不到直接为空
                    controlOrderDetail.setPrice(BigDecimal.ZERO);
                    controlOrderDetail.setOriginalPrice(BigDecimal.ZERO);
                    controlOrderDetail.setUpperLimit(null);
                    controlOrderDetail.setLowerLimit(null);
                    controlOrderDetail.setStatus(StatusEnum.NORMAL.getCode());
                    controlOrderDetail.setEffectStatus(EffectStatusEnum.NO_EFFECT.getCode());
                    controlOrderDetail.setDxsDate(manageControlOrder.getScheduledTime());
                    controlOrderDetail.setGmtCreate(LocalDateTime.now());
                    controlOrderDetail.setGmtUpdate(LocalDateTime.now());
                    controlOrderDetail.setExtend1(ControlOrderExtendDTO.getJSONFormatStr(DataCompletionEnum.NOT_COMPLETE.getCode(), DataFullEnum.NOT_FULL.getCode()));
                    controlOrderDetail.setExtend("");
                    controlOrderDetail.setVersion(0);
                    controlOrderDetail.setCreatedBy(userDTO.getUserId());
                    controlOrderDetail.setUpdatedBy(userDTO.getUserId());
                    controlOrderDetail.setUpdatedByName(userDTO.getUserName());
                    controlOrderDetail.setChannelId(channel);
                    controlOrderDetail.setChannelOutCode(priceChannel.getOutChannelCode());
                    controlOrderDetail.setChannelEnCode(priceChannel.getChannelEnCode());
                    controlOrderDetail.setGuidePrice(null);
                    controlOrderDetail.setOrderType((byte)1);
                    controlOrderDetailList.add(controlOrderDetail);
                }

            }
        }
        return controlOrderDetailList;
    }

    private List<String> getGoodsNoListbyControlOrderCode(String controlOrderCode) {
        if (org.apache.commons.lang.StringUtils.isEmpty(controlOrderCode)) {
            LOGGER.error("<===[AdjustPriceOrderV2ServiceImpl.getGoodsNoListbyControlOrderCode]  参数都不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<String> hasAddGoodsNoList = priceManageControlOrderDetailExtMapper.getGoodsNoListbyControlCode(controlOrderCode);
        if (hasAddGoodsNoList == null) {
            hasAddGoodsNoList = Collections.emptyList();
        }
        return hasAddGoodsNoList;
    }
}
