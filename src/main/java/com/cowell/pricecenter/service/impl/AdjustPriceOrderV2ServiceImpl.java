package com.cowell.pricecenter.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.pricecenter.builder.dto.StoreChannelMappingDTO;
import com.cowell.pricecenter.cache.CacheVar;
import com.cowell.pricecenter.common.wechat.WeChatMessage;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.constant.PriceSyncConstant;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.entity.AdjustPriceOrderExample.Criteria;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.enums.CommonEnums.MqDelayExecBizEnum;
import com.cowell.pricecenter.mapper.AdjustPriceOrderDetailMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderOrgDetailMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderDetailExMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderExMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgStoreDetailExtMapper;
import com.cowell.pricecenter.mapper.extension.PricePushTaskExtMapper;
import com.cowell.pricecenter.mq.producer.AdjustOrderChangeStatusProducer;
import com.cowell.pricecenter.mq.producer.AdjustPriceOrderOrgStoreDetailExtendProducer;
import com.cowell.pricecenter.mq.producer.MakePriceSuggestTaskResultProducer;
import com.cowell.pricecenter.param.PricePushHistoryParam;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.dto.response.amis.ColumnVO;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.feign.*;
import com.cowell.pricecenter.service.feign.facade.ItemSearchEngineFacadeService;
import com.cowell.pricecenter.service.feign.vo.SpuListVO;
import com.cowell.pricecenter.service.feign.vo.SpuQueryParamVO;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.service.vo.OrgParam;
import com.cowell.pricecenter.utils.AmisTimeUtil;
import com.cowell.pricecenter.utils.CommonUtil;
import com.cowell.pricecenter.utils.Md5Utils;
import com.cowell.pricecenter.utils.RequestHeaderContextUtils;
import com.cowell.pricecenter.web.rest.AdjustPriceOrderResource;
import com.cowell.pricecenter.web.rest.errors.*;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import com.cowell.pricecenter.web.rest.util.CheckAdjustPriceOrderUtil;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.maihaoche.starter.mq.enums.DelayTimeLevel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: pricecenter
 * @description: 调价单管理2.0版本的服务实现类
 * @author: jmlu
 * @create: 2022-03-18 17:17
 **/

@Service
public class AdjustPriceOrderV2ServiceImpl implements IAdjustPriceOrderV2Service {

    private final Logger logger = LoggerFactory.getLogger(AdjustPriceOrderV2ServiceImpl.class);

    @Resource
    private AdjustPriceOrderMapper adjustPriceOrderMapper;

    @Resource
    private AdjustPriceOrderExMapper adjustPriceOrderExMapper;

    @Resource
    private IAdjustPriceOrderDetailV2Service adjustPriceOrderDetailV2Service;

    @Resource
    private AdjustPriceOrderOrgDetailService adjustPriceOrderOrgDetailService;

    @Resource
    private PriceOrderTabTypeDetailService priceOrderTabTypeDetailService;

    @Resource
    private IPermissionExtService permissionExtService;

    @Resource
    private NoSequenceService noSequenceService;

    @Resource
    private ISearchExtService searchExtService;

    @Resource
    private IBasePriceOrderService basePriceOrderService;

    @Resource
    private IPriceTypeService priceTypeService;

    @Resource
    private PriceChannelService priceChannelService;

    @Resource
    private ITocExtService tocExtService;

    @Resource
    private IAdjustPriceOrderDetailService adjustPriceOrderDetailService;

    @Resource
    private AdjustPriceOrderOrgStoreDetailService adjustPriceOrderOrgStoreDetailService;

    @Autowired
    private AdjustPriceOrderDetailExMapper adjustPriceOrderDetailExMapper;

    @Autowired
    private PriceDictionaryService priceDictionaryService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AdjustPriceOrderDetailV2ReadServiceImpl detailV2ReadService;

    @Autowired
    private AdjustPriceOrderOrgStoreDetailExtendProducer adjustPriceOrderOrgStoreDetailExtendProducer;

    @Autowired
    private IAdjustPriceOrderV2Service priceOrderV2Service;

    @Autowired
    private ItemSearchEngineFacadeService itemSearchEngineFacadeService;

    @Autowired
    private IAdjustPriceOrderService adjustPriceOrderService;

    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Autowired
    private AdjustOrderChangeStatusProducer adjustOrderChangeStatusProducer;

    @Autowired
    private ErpBizSupportService erpBizSupportService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private ItemCenterCpservice itemCenterCpservice;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private AdjustPriceOrderOrgDetailMapper adjustPriceOrderOrgDetailMapper;

    @Autowired
    private PriceManageControlOrderReadService priceManageControlOrderReadService;

    @Autowired
    private MakePriceSuggestTaskResultProducer makePriceSuggestTaskResultProducer;

    @Autowired
    @Qualifier("checkAdjustPriceRebateThreadExecutor")
    private AsyncTaskExecutor checkAdjustPriceRebateThreadExecutor;

    @Autowired
    private AdjustPriceOrderDetailMapper adjustPriceOrderDetailMapper;

    @Autowired
    @Qualifier("initNewStorePriceThreadExecutor")
    private AsyncTaskExecutor initNewStorePriceThreadExecutor;
    @Autowired
    private IPriceManageControlOrderService priceManageControlOrderService;
    @Autowired
    private AdjustPriceOrderOrgStoreDetailExtMapper adjustPriceOrderOrgStoreDetailExtMapper;
    @Autowired
    private PricePushTaskExtMapper pricePushTaskExtMapper;

    /**
     * 调价单名字的最大字符长度
     */

    @Value("${adjust.name.max.length:20}")
    public int ADJUST_NAME_MAX_LENGTH;

    /**
     * 调价单原因的最大字符长度
     */
    @Value("${adjust.reason.max.length:50}")
    public int ADJUST_REASON_MAX_LENGTH;
    /**
     * 最后1次采购价类型编码
     */
    @Value("${last.purchase.price.type}")
    public String lastPurchasePriceType;
    /**
     * 定价建议类型编码
     */
    @Value("${suggest.price.type}")
    public String suggestPriceType;

    /**
     * 当前登录人连锁包含配置中 不显示执价结果按钮
     */
    @Value("${display.effect.result.button:}")
    private String displayEffectResultButton;

    /**
     * 小于当前配置时间时 不显示执价结果按钮
     */
    @Value("${display.effect.result.button.date:2022-08-16 00:00:00}")
    private String displayEffectResultButtonDate;

    /**
     * 调价单添加或导入验证门店组、门店标签包含的门店不能大于1000
     */
    @Value("${adjust.storegroup.storemaxcount:1000}")
    public Integer storemaxcount;

    @Value("${adjust.price.error.wechat.url}")
    private String orgStoreErrorWechatUrl;

    @Value("${adjust.price.lawful.switch:false}")
    private Boolean lawfulSwitch;

    @ApolloJsonValue("${adjusttype.goods.whitelist.businessIds.switch:[]}")
    private List<Long> goodsWhitelistBusinessIdSwitch;

    /**
     * 已配置的调价类型，无需提交OA审核，默认自动审核通过
     */
    @ApolloJsonValue("${price.adjust.adjustType.autoaudit:[]}")
    private List<Integer> adjustTypeAutoAudits;

    // 查询es最大请求数量
    @Value("${whiteList.query.page.max:50}")
    private Integer queryPageMax;

    @Value("${allowAdjustType:20}")
    private String allowAdjustType;

    @ApolloJsonValue("${adjust.price.businessIds.rebate.warn.switch:[]}")
    private List<Long> businessIdsRebateWarnSwitch;

    @Value("${price.floor.remind.switch:false}")
    private boolean priceFloorRemindSwitch;

    /**
     * B2C渠道 价格
     */
    @ApolloJsonValue("${b2c.channel:{}}")
    public Map<String, List<String>> b2cChannelsMap;

    /**
     * 调价单提交类型映射
     */
    @ApolloJsonValue("${adjust.audit.adjust.type.mapper:{}}")
    public Map<String, Integer> adjustTypeMapperMap;

    /**
     * 生成门店维度调价单明细 重试次数
     */
    @Value("${org.store.retry.count:3}")
    private Integer orgStoreReTryCount;
    private static final int DEFAULT_PAGE_SIZE = 500;
    private static final int BATCH_SAVE_THRESHOLD = Constants.BATCH_SAVE_LIMIT_SIZE.intValue();
    private static final int CACHE_TTL_MINUTES = 30;



    public List<String> getB2CPriceTypeConfig(Integer b2cChannelId) {
        if(MapUtils.isEmpty(b2cChannelsMap) || Objects.isNull(b2cChannelId)){
            return null;
        }
        return b2cChannelsMap.get(String.valueOf(b2cChannelId));
    }

    public List<String> getB2CChannelIdList() {
        if(MapUtils.isEmpty(b2cChannelsMap)){
            return Lists.newArrayList();
        }
        List<String> keyList = new ArrayList<>(b2cChannelsMap.keySet());
        return keyList;
    }

    /**
     * 等于-1 调价类型列表不删除 超管控范围自动调价类型
     */
    private static final int  NO_REMOVE_SUPER_CONTROL_TYPE= -1;

    private static final Integer TIMETOLIVE = 10;

    private static final String NEWSTORE_ADJUSTPRICE_REASON = "新店价格初始化";

    @Override
    public PageResult<AdjustPriceOrderV2VO> searchAdjustPriceOrderList(AdjustPriceOrderListV2Param param, TokenUserDTO userDTO) {
        addStoreOrgToHDQuery(param,userDTO);
        if (StringUtils.isNotEmpty(param.getGoodsNo())) {
            param.setAdjustCodeList(searchExtService.getAdjustCodeListByGoodsNo(param.getGoodsNo()));
        }
        param = basePriceOrderService.dealDateRanges(param);
        param = basePriceOrderService.dealOrgIdList(param, userDTO);
        param.setOffsetByPageAndSize();
        param.setChannelId(param.getChannel());
        int noChannelAuth = -99;
        //选择全部渠道查询时，查询用户权限范围内渠道
        if(null == param.getChannelId()){
            List<OptionDto> optionDtoList = null;
            ControlOrderStatusParam conParam = new ControlOrderStatusParam();
            conParam.setApiCode(PriceManageStatusApiEnum.PRICE_CHANNEL.getCode());
            optionDtoList = priceManageControlOrderReadService.controlSelectUnifyList(conParam);
            if(CollectionUtils.isEmpty(optionDtoList)){
                param.setChannelId(noChannelAuth);
            }else{
                param.setChannelIdList(optionDtoList.stream().map(o -> Integer.valueOf(o.getValue())).collect(Collectors.toList()));
            }
        }

        List<AdjustPriceOrderV2VO> adjustPriceOrderV2VOList = Collections.emptyList();
        long count =  adjustPriceOrderExMapper.searchAdjustPriceOrderV2Count(param);
        if (count > 0) {
            List<AdjustPriceOrder> adjustPriceOrderList = adjustPriceOrderExMapper.searchAdjustPriceOrderV2List(param);
            adjustPriceOrderV2VOList = getFromAdjustPriceOrderList(adjustPriceOrderList, userDTO);
            adjustPriceOrderV2VOList.forEach(order -> {
            	Date displayDate = DateUtils.stringToDate(displayEffectResultButtonDate);
            	Date effectTime = DateUtils.stringToDate(order.getEffectTime(),DateUtils.DATE_FORMAT_YYYYMMDD);
            	if(null!=effectTime && displayDate.getTime()<effectTime.getTime()) {
            		if(null!=userDTO && null!=userDTO.getBusinessId() && !displayEffectResultButton.contains(userDTO.getBusinessId().toString()) &&
                			order.getEffectStatus().intValue()==EffectStatusEnum.EFFECTED.getCode().intValue()) {
                		order.setDisplayEffectResult(true);
                	}
            	}else {
            		order.setDisplayEffectResult(false);
            	}
            	if(order.getEffectStatus().intValue()!=EffectStatusEnum.EFFECTED.getCode().intValue()) {
            		order.setOfflineEffectStatusName("");
            	}
            	boolean isAfreshAdjustPrice = false;//是否已重复执价
            	Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(order.getExtend());
            	if(instance.isPresent()) {
            		AdjustPriceOrderExtend adjustPriceOrderExtend = instance.get();
            		if(null!=adjustPriceOrderExtend.getAfreshAdjustPriceStatus() && adjustPriceOrderExtend.getAfreshAdjustPriceStatus()) {
            			isAfreshAdjustPrice = true;
            		}
            	}
            	if(!isAfreshAdjustPrice) {
            		if((AuditStatusEnum.AUDIT_PASS.getCode() == order.getAuditStatus() &&
                			EffectStatusEnum.NO_EFFECT.getCode().intValue()==order.getEffectStatus().intValue()) ||
                			(EffectStatusEnum.EFFECTED.getCode().intValue()==order.getEffectStatus().intValue() &&
                			AdjustPriceOrderOfflineEffectStatusEnum.EFFECT_FAIL.getCode()==order.getOfflineEffectStatus())) {
                		order.setShowAfreshAdjustPriceButton(true);
                	}
            	}
            });
        }
        setProgressBarValue(adjustPriceOrderV2VOList);
        return new PageResult(count, adjustPriceOrderV2VOList);
    }

    /**
     * 海典查询补充门店机构信息 作为查询条件
     * @param param
     */
    private void addStoreOrgToHDQuery(AdjustPriceOrderListV2Param param,TokenUserDTO userDTO){
        if(StringUtils.isBlank(param.getSapCode())){
            return;
        }
        OrgToRedisDTO orgToRedisDTO = getOrgToRedisDTO(param.getSapCode(),userDTO);
        if(null!=orgToRedisDTO){
            param.setOrgIdList(Lists.newArrayList(orgToRedisDTO.getId()));
        }
    }

    /**
     * 设置进度条值
     * 根据调价订单列表中的调价状态来更新进度条值
     *
     * @param adjustPriceOrderV2VOList 调价订单列表，包含多个调价订单信息
     */
    private void setProgressBarValue(List<AdjustPriceOrderV2VO> adjustPriceOrderV2VOList){
        if (CollectionUtils.isEmpty(adjustPriceOrderV2VOList)) {
            return;
        }
        for (AdjustPriceOrderV2VO order : adjustPriceOrderV2VOList){
            Integer offlineEffectStatus = order.getOfflineEffectStatus();
            if (offlineEffectStatus == null) {
                continue;
            }
            // 获取调价代码
            String adjustCode = order.getAdjustCode();
            // 跳过这些状态：待生效、成功、失败
            if (isSkippableStatus(offlineEffectStatus)) {
                continue;
            }
            // 执行中
            if(null!=order.getOfflineEffectStatus() && AdjustPriceOrderOfflineEffectStatusEnum.EFFECT_PROCESSING.getCode() == order.getOfflineEffectStatus()){
                PricePushHistoryStatisticsDTO taskStatistics = pricePushTaskExtMapper.getTaskStatistics(adjustCode);
                int progress = Constants.MIN_PROGRESS; // 默认值
                if (taskStatistics != null && taskStatistics.getTotal() > 0) {
                    int calculatedProgress = taskStatistics.getProgressCount() * 100 / taskStatistics.getTotal();
                    progress = Math.max(calculatedProgress, Constants.MIN_PROGRESS);
                }
                //执行中状态如果计算出100%，则设置为95%
                if(progress==100){
                    progress = Constants.MAX_PROGRESS_95;
                }
                order.setProgressBar(progress);
            }
            // 部分成功
            if(null!=order.getOfflineEffectStatus() && AdjustPriceOrderOfflineEffectStatusEnum.EFFECT_PARTIAL_SUCCESS.getCode() == order.getOfflineEffectStatus()){
                PricePushHistoryParam param = new PricePushHistoryParam();
                param.setAdjustCode(adjustCode);
                if (order.getEffectStatus() != null) {
                    param.setEffectStatus(order.getEffectStatus().intValue());
                }
                PricePushHistoryStatisticsDTO statistics = adjustPriceOrderOrgStoreDetailExtMapper.selectPricePushHistoryStatistics(param);
                int progress = Constants.MIN_PROGRESS; // 默认值
                if(null!=statistics){
                    order.setSuccessPriceCount(statistics.getSuccessCount());
                    order.setTotalDetailCount(statistics.getTotal());
                    if (statistics.getTotal() > 0) {
                        int calculatedProgress = statistics.getSuccessCount() * 100 / statistics.getTotal();
                        progress = Math.max(calculatedProgress, Constants.MIN_PROGRESS);
                    }
                }
                order.setProgressBar(progress);
            }
        }
    }

    /**
     * 判断是否为需要跳过的状态
     */
    private boolean isSkippableStatus(Integer status) {
        return AdjustPriceOrderOfflineEffectStatusEnum.EFFECT_WAIT.getCode() == status ||
            AdjustPriceOrderOfflineEffectStatusEnum.EFFECT_SUCCESS.getCode() == status ||
            AdjustPriceOrderOfflineEffectStatusEnum.EFFECT_FAIL.getCode() == status;
    }

    @Override
    public PageResult<AdjustPriceOrderV2VO> searchControlAdjustPriceOrderList(AdjustPriceOrderListV2Param param, TokenUserDTO userDTO) {
        param.setOffsetByPageAndSize();
        List<AdjustPriceOrderV2VO> adjustPriceOrderV2VOList = Collections.emptyList();
        long count =  adjustPriceOrderExMapper.searchAdjustPriceOrderV2Count(param);
        if (count > 0) {
            List<AdjustPriceOrder> adjustPriceOrderList = adjustPriceOrderExMapper.searchAdjustPriceOrderV2List(param);
            adjustPriceOrderV2VOList = getFromAdjustPriceOrderList(adjustPriceOrderList, userDTO);
        }
        List<ColumnVO> columnVOList = ControlAdjustPriceOrderListColumnEnum.getColumns();
        return new PageResult(count, columnVOList, adjustPriceOrderV2VOList);
    }


    /**
     * 根据调价单列表获取
     * @param adjustPriceOrderList
     * @return
     */
    private List<AdjustPriceOrderV2VO> getFromAdjustPriceOrderList(List<AdjustPriceOrder> adjustPriceOrderList, TokenUserDTO userDTO) {
        return adjustPriceOrderList.stream().map(adjustPriceOrder -> getVOFromAdjustPriceOrderAndOrgCount(adjustPriceOrder, userDTO, false)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public long addAdjustPriceOrder(AdjustPriceOrderV2Param param, TokenUserDTO userDTO) {
    	if(null!=userDTO && goodsWhitelistBusinessIdSwitch.contains(userDTO.getBusinessId())) {
    		checkAdjustTypeWhitelist(param.getGoodsScope(), param.getAdjustType());
		}
        LocalDateTime operateTime = LocalDateTime.now();
        Date operateTimeDate = Date.from(operateTime.atZone(ZoneId.systemDefault()).toInstant());
        param = trimAdjustPriceOrderV2Param(param);
        checkEditAdjustPriceOrderV2Param(Optional.empty(), param, userDTO, true);

        param = parseAmisCheckValues(param);
        checkStoreGroupScopeStoreMaxCount(param.getOrgTabType(),param.getOrgIdList());
        String adjustCode = noSequenceService.priceNoSequence(NoSequenceTypeEnum.PRICECENTER_AJUST);
        List<OrgLevelVO> orgLevelVOList = Lists.newArrayList();
        if(getB2CChannelIdList().contains(param.getChannelIds())) {
        	if(null!=param.getEffectMode() && param.getEffectMode()==1) {
        		throw new AmisBadRequestException(ReturnCodeEnum.B2C_CHANNEL_EFFECTMODE_ERROR);
        	}
        	ResponseEntity<List<OrgDTO>> responseEntity = permissionService.listOrgInfoById(param.getOrgIdList(), false);
            if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new AmisBadRequestException(ReturnCodeEnum.CALL_PERMISSION_ERROR);
            }
            List<OrgDTO> orgDtoList = responseEntity.getBody();
            if(CollectionUtils.isEmpty(orgDtoList)) {
            	throw new AmisBadRequestException(ReturnCodeEnum.CALL_PERMISSION_ERROR);
            }
            Map<Long, OrgDTO> orgDtoMap = orgDtoList.stream().collect(Collectors.toMap(OrgDTO::getId, Function.identity(), (v1, v2) -> v1));
        	List<Long> storeIdList = orgDtoList.stream().map(OrgDTO::getOutId).collect(Collectors.toList());
        	Map<Long,StoreChannelMappingDTO> storeChannelMapperMap = null;
        	ResponseEntity<List<StoreChannelMappingDTO>> response = storeService.list(null, storeIdList,null,null);
			if (Objects.isNull(response) || !HttpStatus.OK.equals(response.getStatusCode()) || Objects.isNull(response.getBody())) {
				storeChannelMapperMap = new HashMap<Long, StoreChannelMappingDTO>();
            }else {
            	storeChannelMapperMap = response.getBody().stream().collect(Collectors.toMap(StoreChannelMappingDTO::getChannelMappingStoreId, Function.identity(), (v1, v2) -> v1));
            }
        	OrgLevelVO orgVo = null;
        	for (Long orgId : param.getOrgIdList()) {
        		orgVo = new OrgLevelVO();
        		orgVo.setOrgId(orgId);
        		orgVo.setOrgLevel(OrgLevelTypeEnum.STORE.getCode());
        		orgVo.setOrgName("");
        		String extend = storeChannelMapperMap.get(orgDtoMap.get(orgId).getOutId()).getExtend();
        		Optional<StoreChannelMappingDTOExtend> instance = StoreChannelMappingDTOExtend.getInstance(extend);
        		if(instance.isPresent()) {
        			StoreChannelMappingDTOExtend storeChannelMappingDTOExtend = instance.get();
        			orgVo.setOrgName(storeChannelMappingDTOExtend.getChannelMappingStoreName());
        		}
        		orgLevelVOList.add(orgVo);
			}
        }else {
        	orgLevelVOList = getUserActualSelectOrgDTOList(userDTO.getUserId(), param.getResourceId(), param.getOrgIdList());
            insertAdjustOrgTabTypeDetailList(adjustCode, param.getOrgTabType(), param.getTabTypeValList(), userDTO, operateTime);
        }
        insertAdjustOrgDetailList(adjustCode, orgLevelVOList, userDTO, operateTime);
        long adjustPriceOrderId = insertAdjustPriceOrder(adjustCode, param, userDTO, operateTimeDate);
        basePriceOrderService.recordNewAuditLog(adjustCode, PriceOrderTypeEnum.ADJUST, AdjustTypeEnum.getEnum(param.getAdjustType()), AuditStatusEnum.IN_PREPARATION, userDTO, operateTimeDate);
        return adjustPriceOrderId;
    }

    private void checkAdjustTypeWhitelist(Integer goodsScope,Integer adjustType) {
    	if(null==goodsScope || null==adjustType) {
    		return;
    	}
    	if(GoodsScopeEnum.ALL_GOODS_SCOPE.getCode()==goodsScope && AdjustTypeEnum.PUTONG.getCode()==adjustType) {
    		throw new AmisBadRequestException(ReturnCodeEnum.ADJUST_TYPE_WHITE_GOODS_SCOPE);
    	}
    }

    private void checkStoreGroupScopeStoreMaxCount(Integer orgTabType,List<Long> orgIdList) {
    	if(null!=orgTabType && (orgTabType==PriceOrderTabTypeEnum.STORE_GROUP.getType() || orgTabType==PriceOrderTabTypeEnum.STORE_FLAG.getType())) {
			if(orgIdList.size()>storemaxcount) {
				throw new AmisBadRequestException(ReturnCodeEnum.ERROR_STOREGROUP_STOREMAXCOUNT,ReturnCodeEnum.ERROR_STOREGROUP_STOREMAXCOUNT.getMessage()+storemaxcount);
			}
		}
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAdjustPriceOrder(AdjustPriceOrderV2Param param, TokenUserDTO userDTO) {
    	if(null!=userDTO && goodsWhitelistBusinessIdSwitch.contains(userDTO.getBusinessId())) {
    		checkAdjustTypeWhitelist(param.getGoodsScope(), param.getAdjustType());
		}
        LocalDateTime operateTime = LocalDateTime.now();
        Date operateTimeDate = Date.from(operateTime.atZone(ZoneId.systemDefault()).toInstant());
        if (param.getId() == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        param.setExecStatus(param.getEffectMode());
        Long adjustPriceOrderId = param.getId();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);
        checkAdjustTypeChange(adjustPriceOrder, param);
        checkEditAdjustPriceOrderV2Param(Optional.of(adjustPriceOrder), param, userDTO, false);
        //扩展字段增加生效方式用于回显
        String extend = adjustPriceOrder.getExtend();
        Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(extend);
        if(instance.isPresent()) {
        	AdjustPriceOrderExtend adjustPriceOrderExtend = instance.get();
        	adjustPriceOrderExtend.setEffectMode(param.getEffectMode());
        	adjustPriceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(adjustPriceOrderExtend));
        }else {
        	adjustPriceOrder.setExtend(AdjustPriceOrderExtend.getJSONFormatStr(param.getEffectMode()));
        }

        //是否新增页面提交过来, 新建则保存制单中审核记录
        boolean isAdd = adjustPriceOrder.getExecStatus() == null;
        String adjustCode = adjustPriceOrder.getAdjustCode();
        List<OrgLevelVO> orgLevelVOList = Lists.newArrayList();
        if (param.getStep() == PricePageStepEnum.STEP_1.getStep()) {
            param = parseAmisCheckValues(param);
            checkStoreGroupScopeStoreMaxCount(param.getOrgTabType(),param.getOrgIdList());
            if(!getB2CChannelIdList().contains(param.getChannelIds())) {
            	orgLevelVOList = getUserActualSelectOrgDTOList(userDTO.getUserId(), param.getResourceId(), param.getOrgIdList());
            }
            boolean isToDeleteAllDetail = isToDeleteAllDetail(adjustPriceOrder, orgLevelVOList, param);
            updateAdjustOrgTabTypeDetailList(adjustCode, param.getOrgTabType(), param.getTabTypeValList(), userDTO, operateTime);
            if(getB2CChannelIdList().contains(param.getChannelIds())) {
            	if(null!=param.getEffectMode() && param.getEffectMode()==1) {
            		throw new AmisBadRequestException(ReturnCodeEnum.B2C_CHANNEL_EFFECTMODE_ERROR);
            	}
            	orgLevelVOList.clear();
            	ResponseEntity<List<OrgDTO>> responseEntity = permissionService.listOrgInfoById(param.getOrgIdList(), false);
                if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
                    throw new AmisBadRequestException(ReturnCodeEnum.CALL_PERMISSION_ERROR);
                }
                List<OrgDTO> orgDtoList = responseEntity.getBody();
                if(CollectionUtils.isEmpty(orgDtoList)) {
                	throw new AmisBadRequestException(ReturnCodeEnum.CALL_PERMISSION_ERROR);
                }
                Map<Long, OrgDTO> orgDtoMap = orgDtoList.stream().collect(Collectors.toMap(OrgDTO::getId, Function.identity(), (v1, v2) -> v1));
            	List<Long> storeIdList = orgDtoList.stream().map(OrgDTO::getOutId).collect(Collectors.toList());
            	Map<Long,StoreChannelMappingDTO> storeChannelMapperMap = null;
            	ResponseEntity<List<StoreChannelMappingDTO>> response = storeService.list(null, storeIdList,null,null);
    			if (Objects.isNull(response) || !HttpStatus.OK.equals(response.getStatusCode()) || Objects.isNull(response.getBody())) {
    				storeChannelMapperMap = new HashMap<Long, StoreChannelMappingDTO>();
                }else {
                	storeChannelMapperMap = response.getBody().stream().collect(Collectors.toMap(StoreChannelMappingDTO::getChannelMappingStoreId, Function.identity(), (v1, v2) -> v1));
                }
            	OrgLevelVO orgVo = null;
            	for (Long orgId : param.getOrgIdList()) {
            		orgVo = new OrgLevelVO();
            		orgVo.setOrgId(orgId);
            		orgVo.setOrgLevel(OrgLevelTypeEnum.STORE.getCode());
            		orgVo.setOrgName("");
            		String storeChannelExtend = storeChannelMapperMap.get(orgDtoMap.get(orgId).getOutId()).getExtend();
            		Optional<StoreChannelMappingDTOExtend> storeChannelinstance = StoreChannelMappingDTOExtend.getInstance(storeChannelExtend);
            		if(storeChannelinstance.isPresent()) {
            			StoreChannelMappingDTOExtend storeChannelMappingDTOExtend = storeChannelinstance.get();
            			orgVo.setOrgName(storeChannelMappingDTOExtend.getChannelMappingStoreName());
            		}
            		orgLevelVOList.add(orgVo);
    			}
            }
            updateAdjustOrgDetailList(adjustCode, orgLevelVOList, userDTO, operateTime);
            if (isToDeleteAllDetail) {
                List<Long> detailIdList = adjustPriceOrderDetailV2Service.selectAdjustPriceOrderDetailIdByAdjustCode(adjustCode, null, null);
                adjustPriceOrderDetailV2Service.deleteAdjustPriceOrderDetails(adjustPriceOrderId, userDTO);
                adjustPriceOrderDetailV2Service.deleteAdjustPriceOrderDetailStoreIdCache(adjustCode,detailIdList);
            } else {
                PriceOrderPriceTypeAndChannelChange orderPriceTypeAndChannelChange = basePriceOrderService.getChangeChannelAndPriceType(adjustPriceOrder.getChannel(), adjustPriceOrder.getAdjustPriceType(),
                    param.getChannelIdList(), param.getPriceTypeCodeList());
                adjustPriceOrderDetailV2Service.resetOrderDetailsByChannelsAndPriceTypes(adjustPriceOrder, orderPriceTypeAndChannelChange, userDTO, operateTimeDate);
            }

        }
        updateAdjustPriceOrder(adjustPriceOrder, param, userDTO, operateTimeDate);
        setAdjustPriceOrderInPreparationById(adjustPriceOrderId, userDTO);
    }

    private boolean checkAdjustTypeChange(AdjustPriceOrder adjustPriceOrder,AdjustPriceOrderV2Param param) {
    	if(null == param.getGoodsScope()) {
    		return false;
    	}
    	boolean status = true;
    	if(adjustPriceOrder.getGoodsScope().equals(param.getGoodsScope()) ||
    			(adjustPriceOrder.getGoodsScope().intValue()!=GoodsScopeEnum.SENSITIVE_GOODS.getCode() &&
    			param.getGoodsScope().intValue()!=GoodsScopeEnum.SENSITIVE_GOODS.getCode())){
    		return false;
    	}
    	//商品范围发生变化 明细权限验证会变，此处删除明细
    	AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
    	example.createCriteria().andAdjustCodeEqualTo(adjustPriceOrder.getAdjustCode());
    	adjustPriceOrderDetailMapper.deleteByExample(example);
    	return status;
    }

    /**
     * 修改商品范围则删除所有商品明细
     * @param adjustPriceOrder
     * @param orgLevelVOList
     * @param param
     * @return
     */
    private boolean isToDeleteAllDetail(AdjustPriceOrder adjustPriceOrder, List<OrgLevelVO> orgLevelVOList, AdjustPriceOrderV2Param param) {
        boolean result = false;
        if (adjustPriceOrder.getGoodsScope() != null && param.getGoodsScope() != null &&
            adjustPriceOrder.getGoodsScope() != param.getGoodsScope()) {
            result = true;
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAdjustPriceOrderById(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);
        String adjustCode = adjustPriceOrder.getAdjustCode();
        deleteAdjustPriceOrder(adjustPriceOrder, userDTO, operateTime);
        //删除调价单明细门店缓存
        List<Long> detailIdList = adjustPriceOrderDetailV2Service.selectAdjustPriceOrderDetailIdByAdjustCode(adjustPriceOrder.getAdjustCode(), null, null);
        if(CollectionUtils.isNotEmpty(detailIdList)){
            for (List<Long> detailIds : Lists.partition(detailIdList, Constants.THREE_HUNDRED)) {
                adjustPriceOrderDetailV2Service.deleteAdjustPriceOrderDetailStoreIdCache(adjustPriceOrder.getAdjustCode(), detailIds);
            }
        }
        adjustPriceOrderDetailV2Service.nonPhysicalDeleteByAdjustCode(adjustCode, userDTO, operateTime);
    }


    @Override
    public AdjustPriceOrderV2VO getAdjustPriceOrderById(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);

        AdjustPriceOrderV2VO adjustPriceOrderV2VO =  getVOFromAdjustPriceOrderAndOrgCount(adjustPriceOrder, userDTO, true);
        return adjustPriceOrderV2VO;
    }

    @Override
    public AdjustPriceOrderV2VO getAdjustPriceOrderByCode(String adjustCode, TokenUserDTO userDTO) {
        if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);

        AdjustPriceOrderV2VO adjustPriceOrderV2VO =  getVOFromAdjustPriceOrderAndOrgCount(adjustPriceOrder, userDTO, true);
        return adjustPriceOrderV2VO;
    }

    /**
     * 校验调价单明细
     * @param userDTO
     */
    private void checkDataFullCompleted(AdjustPriceOrder adjustPriceOrder,Boolean isPortal, TokenUserDTO userDTO) {
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        String adjustCode = adjustPriceOrder.getAdjustCode();

        List<String> dataFullAndLimitErrors = getDataFullAndLimitErrors(adjustCode);
        if (CollectionUtils.isNotEmpty(dataFullAndLimitErrors)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, Joiner.on("\n").join(dataFullAndLimitErrors));
        }
        boolean hasGoodDetail = adjustPriceOrderDetailV2Service.hasGoodDetail(adjustCode, userDTO);
        if (!hasGoodDetail) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "调价单的商品明细不能为空");
        }
        Long storeId = adjustPriceOrderDetailV2Service.getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null==isPortal || !isPortal || null == storeId) {
        	boolean reasonIsNullCount = adjustPriceOrderDetailV2Service.countAdjustPriceOrderDetailReasonIsNull(adjustCode);
            if(reasonIsNullCount) {
            	throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "请检查商品行中的调价原因是否填写完整");
            }
        }
        int priceIsNullCount = adjustPriceOrderDetailExMapper.countAdjustPriceOrderDetailPriceIsNull(adjustCode);
        if(priceIsNullCount>0) {
        	throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "商品明细价格不能为空,请检查。");
        }
        boolean isSupplementCompleted = adjustPriceOrderDetailV2Service.isAdjustPriceOrderDetailImportCompletedByCode(adjustCode, userDTO);
        if (!isSupplementCompleted) {
        	//发送异步补充相关数据
        	List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList = adjustPriceOrderDetailV2Service.selectAdjustPriceOrderDetailSupplementNotCompleted(adjustCode);
        	adjustPriceOrderDetailV2Service.sendToSupplementDataMsgs(false,insertAdjustPriceOrderDetailList,Boolean.TRUE);
        	Set<String> notCompletedLine = new HashSet<String>();
        	insertAdjustPriceOrderDetailList.forEach(item -> {
        		notCompletedLine.add(item.getGoodsNo()+"_"+item.getOrgIds());
        	});
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "存在"+notCompletedLine.size()+"个商品信息补充不完整，请稍等几分钟重新保存。");
        }
    }

    private List<String> getDataFullAndLimitErrors(String adjustCode) {
        List<AdjustOrderDetailGoodsExtend1> detailGoodsExtend1List = detailV2ReadService.listGoodsExtend1ByAdjustCode(adjustCode);
        Map<String, String> errMsgMap = Maps.newHashMap();
        Set<String> goodsLawfulErrorSet = Sets.newHashSet();
        Set<String> goodsNoPriceCodeSet = Sets.newHashSet();
        for (AdjustOrderDetailGoodsExtend1 goodsExtend1 : detailGoodsExtend1List) {
            StringBuilder errMsgsBuilder = new StringBuilder();
            boolean isGoodsErr = false;
            if (errMsgMap.containsKey(goodsExtend1.getGoodsNo())) {
                errMsgsBuilder.append(errMsgMap.get(goodsExtend1.getGoodsNo()));
                isGoodsErr = true;
            }
            String goodsNoPriceCode = goodsExtend1.getGoodsNo() + "_" + goodsExtend1.getPriceTypeCode();
            if (!goodsNoPriceCodeSet.contains(goodsNoPriceCode)) {
                Optional<AdjustPriceOrderDetailExtend1> extendOptional = AdjustPriceOrderDetailExtend1.getInstance(goodsExtend1.getExtend1());
                if (extendOptional.isPresent()) {
                    AdjustPriceOrderDetailExtend1 extend = extendOptional.get();
                    if (extend.getLimitStatus() != null && extend.getLimitStatus() != GoodsLimitStatusEnum.NONE.getCode()) {
                        if (!isGoodsErr) {
                            errMsgsBuilder.append(goodsExtend1.getGoodsNo());
                            errMsgsBuilder.append(":");
                        }
                        errMsgsBuilder.append("此商品");
                        errMsgsBuilder.append(goodsExtend1.getPriceTypeName());
                        errMsgsBuilder.append(GoodsLimitStatusEnum.getName(extend.getLimitStatus()));
                        errMsgsBuilder.append("，请核实后修改;");
                        isGoodsErr = true;
                    }
                    if (extend.getDataFull() == null || extend.getDataFull() == DataFullEnum.NOT_FULL.getCode()) {
                        if (!isGoodsErr) {
                            errMsgsBuilder.append(goodsExtend1.getGoodsNo());
                            errMsgsBuilder.append(":");
                        }
                        errMsgsBuilder.append("[");
                        errMsgsBuilder.append(goodsExtend1.getPriceTypeName());
                        errMsgsBuilder.append("]");
                        errMsgsBuilder.append("类型数据填写不完整;");
                    }
                    if(Boolean.valueOf(lawfulSwitch)) {
                    	if (extend.getLawful() != null && !extend.getLawful() && !goodsLawfulErrorSet.contains(goodsExtend1.getGoodsNo())) {
                            if (!isGoodsErr) {
                                errMsgsBuilder.append(goodsExtend1.getGoodsNo());
                                errMsgsBuilder.append(":");
                            }
                            errMsgsBuilder.append("未完成“首营”，无法进行定调价，请核实并修改;");
                            goodsLawfulErrorSet.add(goodsExtend1.getGoodsNo());
                        }
                    }
                }
            }
            String errMsgs = errMsgsBuilder.toString();
            if (StringUtils.isNotEmpty(errMsgs)) {
                errMsgMap.put(goodsExtend1.getGoodsNo(), errMsgs);
            }
        }
        return errMsgMap.values().stream().collect(Collectors.toList());
    }

    /**
     * 获取调价单明细保存不完整信息
     * @param notFullOrderDetailList
     */
    private String getNotFullDetailErrorMessage(List<AdjustPriceOrderDetail> notFullOrderDetailList) {
        StringBuilder errorMessage = new StringBuilder();
        errorMessage.append("\"新价格\"字段没有全部填写或者填完没有点击表格上面的保存按钮。\n");
        errorMessage.append("没有填写的数据如下：\n");
        for (AdjustPriceOrderDetail orderDetail : notFullOrderDetailList) {
            errorMessage.append("商品编码: ");
            errorMessage.append(orderDetail.getGoodsNo());
            errorMessage.append(", 价格类型:");
            errorMessage.append(orderDetail.getPriceTypeName());
            errorMessage.append(";\n");
        }
        return errorMessage.toString();
    }

    @Override
    public void saveAdjustPriceOrder(Long adjustPriceOrderId, Boolean isPortal, Boolean cancel, TokenUserDTO userDTO) {
        Date operateTime = new Date();
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        //删除调价单和缓存
        if(null!=cancel && cancel){
            deleteAdjustPriceOrderById(adjustPriceOrderId,userDTO);
            delPriceAdjustidForHd(adjustPriceOrder,userDTO);
            return;
        }
        Long storeId = adjustPriceOrderDetailV2Service.getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null!=storeId){
            validatePricesForHd(adjustPriceOrder);
        }
        List<String> goodsList = adjustPriceOrderDetailExMapper.goodsOfNeedSpecialAuditOrderDetail(adjustPriceOrder.getAdjustCode());
        if(CollectionUtils.isNotEmpty(goodsList)) {
        	String goods = String.join(",", goodsList);
        	throw new AmisBadRequestException(ReturnCodeEnum.GOODS_NEED_SPECIAL_AUDIT, "下列商品填写的新价格超过上级设置的最高/最低限价范围，请检查！商品编码："+goods);
        }
        checkDataFullCompleted(adjustPriceOrder, isPortal, userDTO);
        setAdjustPriceOrderAuditStatusById(adjustPriceOrderId, AuditStatusEnum.UN_SUBMITTED, userDTO, operateTime);
        asyncTaskExecutor.execute(() -> {
            //医保限价强制拦截
            boolean medicalPriceInterception = adjustPriceOrderDetailV2Service.interceptMedicarePrice(adjustPriceOrder);
            if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
        		checkGrossPureMargin(adjustPriceOrder,medicalPriceInterception);
        	}else {
        		checkWisdomPrice(adjustPriceOrder,medicalPriceInterception,userDTO);
        	}
        });
    }

    /**
     * 三个价格至少填写一个
     * @param adjustPriceOrder
     */
    private void validatePricesForHd(AdjustPriceOrder adjustPriceOrder) {
        List<String> goodsNoList = adjustPriceOrderDetailExMapper.selectPricesForHd(adjustPriceOrder.getAdjustCode());
        if(CollectionUtils.isNotEmpty(goodsNoList)){
            String goodsNoStr = String.join(",", goodsNoList);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "以下商品价格信息填写不完整：" + goodsNoStr);
        }
    }

    private void checkGrossPureMargin(AdjustPriceOrder adjustPriceOrder,boolean medicalPriceInterception) {
    	AdjustImportResultDTO importResult = new AdjustImportResultDTO();
    	String cacheKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.GOODS_WARN+adjustPriceOrder.getId();
		redissonClient.getBucket(cacheKey).delete();
		try {
			List<String> goodsNoList = adjustPriceOrderDetailExMapper.grossPureMarginLessThanZero(adjustPriceOrder.getAdjustCode());
			//与集团标牌价对比价格低的商品
	    	if(CollectionUtils.isNotEmpty(goodsNoList) || medicalPriceInterception) {
	    		importResult.setStatus(ImportStatusEnum.IMPORT_FAIL.getCode());
	    		importResult.setMsg(StringUtils.join(goodsNoList,","));
                if(medicalPriceInterception){
                    importResult.setMsg4("医保限价强制拦截");
                }
				redissonClient.getBucket(cacheKey).set(importResult,2,TimeUnit.MINUTES);
	    	}else {
	    		importResult.setStatus(ImportStatusEnum.IMPORT_FINISH.getCode());
				redissonClient.getBucket(cacheKey).set(importResult,2,TimeUnit.MINUTES);
	    	}
		} catch (Exception e) {
			logger.error("checkGrossPureMargin|校验按当前填写的新价格对应的预估净毛利率是否为负失败,调价单号:{}", adjustPriceOrder.getAdjustCode(),e);
			importResult.setStatus(ImportStatusEnum.IMPORT_FAIL.getCode());
    		importResult.setMsg("校验按当前填写的新价格对应的预估净毛利率是否为负异常，请重试！");
			redissonClient.getBucket(cacheKey).set(importResult,2,TimeUnit.MINUTES);
		}
    }

    private void checkWisdomPrice(AdjustPriceOrder adjustPriceOrder,boolean medicalPriceInterception,TokenUserDTO userDTO) {
    	AdjustImportResultDTO importResult = new AdjustImportResultDTO();
    	String cacheKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.GOODS_WARN+adjustPriceOrder.getId();
    	List<String> adjustPriceTypeList = Arrays.asList(adjustPriceOrder.getAdjustPriceType().split(","));
		if(!adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode())) {
			importResult.setStatus(ImportStatusEnum.IMPORT_FINISH.getCode());
			redissonClient.getBucket(cacheKey).set(importResult,2,TimeUnit.MINUTES);
			return;
		}
		redissonClient.getBucket(cacheKey).delete();
    	Set<String> goodsWarnSet = new HashSet<String>();
		try {
			List<AdjustPriceOrderDetailPrice> detaiPriceList = adjustPriceOrderDetailExMapper.getAdjustPriceOrderDetailPrice(adjustPriceOrder.getAdjustCode(), PriceTypeModeEnum.LSJ.getPriceTypeCode());
			if(CollectionUtils.isNotEmpty(detaiPriceList)) {
				List<Long> businessIdList = Lists.newArrayList();
				List<String> goodsNoList = detaiPriceList.stream().map(AdjustPriceOrderDetailPrice::getGoodsNo).distinct().collect(Collectors.toList());
				Optional<AdjustPriceOrderDetailExtend1> instance = null;
				for (AdjustPriceOrderDetailPrice detail : detaiPriceList) {
					instance = AdjustPriceOrderDetailExtend1.getInstance(detail.getExtend1());
					if(instance.isPresent()) {
						if(StringUtils.isNotBlank(instance.get().getBusinessIds())) {
							List<Long> businessIds = Arrays.asList(instance.get().getBusinessIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
							businessIdList.addAll(businessIds);
							detail.setBusinessIdList(businessIds);
						}
					}
				}
				List<List<String>> partition = Lists.partition(goodsNoList, 80);
				ResponseEntity<List<MakePriceSuggestResultDTO>> response = null;
				MakePriceSuggestResultDTO result = null;
				AdjustPriceOrderDetailPrice updateExtend1 = null;
				AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = null;
				List<AdjustPriceOrderDetailPrice> updateExtend1List = Lists.newArrayList();
				List<BigDecimal> wisdomPriceList  = Lists.newArrayList();
				Map<String, MakePriceSuggestResultDTO> suggestMap = new HashMap<String, MakePriceSuggestResultDTO>();
				for (List<String> goodsNoSubList : partition) {
					response = erpBizSupportService.selectConfirmPriceByBusinessGoodsNo(businessIdList.stream().distinct().collect(Collectors.toList()), goodsNoSubList);
					if(null!=response && CollectionUtils.isNotEmpty(response.getBody())) {
						suggestMap.clear();
			    		suggestMap = response.getBody().stream().collect(Collectors.toMap(k -> k.getBusinessId()+"_"+k.getGoodsNo(), part -> part ,(v1,v2)->v2));
			    		updateExtend1List.clear();
			    		for (AdjustPriceOrderDetailPrice detail : detaiPriceList) {
			    			if(goodsWarnSet.contains(detail.getGoodsNo())) {
			    				continue;
			    			}
			    			if(StringUtils.isBlank(detail.getGoodsNo()) || CollectionUtils.isEmpty(detail.getBusinessIdList())) {
			    				continue;
			    			}
			    			wisdomPriceList.clear();
			    			for (Long businessId : detail.getBusinessIdList()) {
			    				result = suggestMap.get(businessId+"_"+detail.getGoodsNo());
								if(null!=result && null!=result.getConfirmMakepriceSuggestPrice()) {
									wisdomPriceList.add(result.getConfirmMakepriceSuggestPrice());
									if(result.getConfirmMakepriceSuggestPrice().compareTo(detail.getPrice())==1) {
										goodsWarnSet.add(detail.getGoodsNo());
									}
								}
							}
			    			if(CollectionUtils.isNotEmpty(wisdomPriceList)) {
			    				updateExtend1 = new AdjustPriceOrderDetailPrice();
			    				BeanUtils.copyProperties(detail, updateExtend1);
			    				Optional<AdjustPriceOrderDetailExtend1> instance2 = AdjustPriceOrderDetailExtend1.getInstance(updateExtend1.getExtend1());
				    			if(instance2.isPresent()) {
				    				adjustPriceOrderDetailExtend1 = instance2.get();
				    				adjustPriceOrderDetailExtend1.setWisdomPrice(wisdomPriceList.stream().min(BigDecimal::compareTo).get());
				    				updateExtend1.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
				    			}
				    			updateExtend1List.add(updateExtend1);
			    			}
						}
			    		adjustPriceOrderDetailExMapper.batchUpdateExtend1(adjustPriceOrder.getAdjustCode(),updateExtend1List);
			    	}
				}
			}
			Set<String> lessThanHyjGoodsNoList = new HashSet<String>();
			//是否线下pos、价格类型是否存在零售价和会员价
	        if(businessIdsRebateWarnSwitch.contains(userDTO.getBusinessId()) && adjustPriceOrder.getChannel().equals(Constants.DEFAULT_CHANNEL) &&
	            adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode()) && !adjustPriceTypeList.contains(PriceTypeModeEnum.HYJ.getPriceTypeCode())){
	        	lessThanHyjGoodsNoList = checkIsExistRebateHyj(adjustPriceOrder.getAdjustCode(),detaiPriceList);
	        }
	        List<String> priceFloorRemindGoodsNoList = new ArrayList<String>();
	        if(priceFloorRemindSwitch && adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode())){
	        	priceFloorRemindGoodsNoList = adjustPriceOrderDetailExMapper.selectPriceFloorRemindGoodsNo(adjustPriceOrder.getAdjustCode());
	        }
	        String msg = "*部分商品当前填写的零售价小于集团建议标牌价，请确认是否继续使用此价格?";
			String msg2 = "*部分商品的零售价发生变化，请确认是否同时调整会员价，如需调整请重新选择会员价类型后进行调整?";
			String msg3 = "*部分商品设置的零售价低于商品数据中维护的最低限价，请检查?";
			String txStr = "具体商品如下:";
			//与集团标牌价对比价格低的商品
	    	if(CollectionUtils.isNotEmpty(goodsWarnSet) || CollectionUtils.isNotEmpty(lessThanHyjGoodsNoList) || CollectionUtils.isNotEmpty(priceFloorRemindGoodsNoList) || medicalPriceInterception) {
	    		importResult.setStatus(ImportStatusEnum.IMPORT_FAIL.getCode());
	    		if(CollectionUtils.isNotEmpty(goodsWarnSet)) {
	    			importResult.setMsg(msg+txStr+StringUtils.join(goodsWarnSet,","));
	    		}
	    		if(CollectionUtils.isNotEmpty(lessThanHyjGoodsNoList)) {
	    			importResult.setMsg2(msg2+txStr+StringUtils.join(lessThanHyjGoodsNoList,","));
	    		}
	    		if(CollectionUtils.isNotEmpty(priceFloorRemindGoodsNoList)) {
	    			importResult.setMsg3(msg3+txStr+StringUtils.join(priceFloorRemindGoodsNoList,","));
	    		}
                if(medicalPriceInterception){
                    importResult.setMsg4("医保限价强制拦截");
                }
				redissonClient.getBucket(cacheKey).set(importResult,2,TimeUnit.MINUTES);
	    	}else {
	    		importResult.setStatus(ImportStatusEnum.IMPORT_FINISH.getCode());
				redissonClient.getBucket(cacheKey).set(importResult,2,TimeUnit.MINUTES);
	    	}
		} catch (Exception e) {
			logger.error("checkWisdomPrice|定调价明细与标牌价价格对比失败,调价单号:{}", adjustPriceOrder.getAdjustCode(),e);
			importResult.setStatus(ImportStatusEnum.IMPORT_FAIL.getCode());
    		importResult.setMsg("当前填写的零售价小于集团建议标牌价对比异常，请重试！");
			redissonClient.getBucket(cacheKey).set(importResult,2,TimeUnit.MINUTES);
		}

    }

    private Set<String> checkIsExistRebateHyj(String adjustCode, List<AdjustPriceOrderDetailPrice> detaiPriceList){
    	Date operateTime = new Date();
    	Set<String> lessThanHyjGoodsNoList = new HashSet<String>();
    	CountDownLatch countDownLatch = new CountDownLatch(detaiPriceList.size());
    	detaiPriceList.forEach(detail -> {
    		checkAdjustPriceRebateThreadExecutor.execute(() -> {
				try {
					List<AdjustPriceOrderOrgStoreDetail> resultStoreDetails = Lists.newArrayList();
			    	List<PriceStoreDetailVo> storePriceDetailList = Lists.newArrayList();
		    		List<Long> orgIdList = Arrays.asList(detail.getOrgIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		    		List<Integer> orgLevelList = Arrays.asList(detail.getOrgLevels().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
		    		List<OrgLevelVO> orgLevelBeanList = Lists.newArrayList();
		    		OrgLevelVO orgLevelVO = null;
		    		for (int i = 0; i < orgIdList.size(); i++) {
		    			orgLevelVO = new OrgLevelVO();
		    			orgLevelVO.setOrgId(orgIdList.get(i));
		    			orgLevelVO.setOrgLevel(orgLevelList.get(i));
		    			orgLevelBeanList.add(orgLevelVO);
		    		}
		    		Map<Integer, List<OrgLevelVO>> groupOrgLevelVOMap = orgLevelBeanList.stream().collect(Collectors.groupingBy(OrgLevelVO::getOrgLevel));
		            //获取所有门店基本信息
		            for (Integer orgLevel : groupOrgLevelVOMap.keySet()) {
		                resultStoreDetails.addAll(getStoreDetailListByOneLevelOrgs(adjustCode, orgLevel, groupOrgLevelVOMap.get(orgLevel), OrgLevelTypeEnum.getByCode(orgLevel), operateTime));
		            }
		            if(CollectionUtils.isNotEmpty(resultStoreDetails)) {
		            	List<Long> storeIdList = resultStoreDetails.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).distinct().collect(Collectors.toList());
		            	List<List<Long>> partition = Lists.partition(storeIdList, Constants.BATCH_QUERY_SIZE_50);
		        		for (List<Long> subStoreIdList : partition) {
		        			storePriceDetailList.addAll(searchExtService.getByGoodsNoAndStoreIdListAndPriceTypeCode(subStoreIdList, detail.getGoodsNo(), PriceTypeModeEnum.HYJ.getPriceTypeCode()));
		        		}
		            }
		            if(CollectionUtils.isNotEmpty(storePriceDetailList)) {
		            	BigDecimal rebateHyj = null;
		            	for (PriceStoreDetailVo priceStore : storePriceDetailList) {
		            		if(!CommonUtil.isJsonString(priceStore.getExtend())) {
		            			continue;
		            		}
		            		rebateHyj = CommonUtil.getExtendValue(priceStore.getExtend(), "rebateHyj", BigDecimal.class);
		            		if(null!=rebateHyj && rebateHyj.compareTo(BigDecimal.ZERO)>0) {
		            			lessThanHyjGoodsNoList.add(priceStore.getGoodsNo());
		            			break;
		            		}
		            	}
		            }
				}catch (Exception e){
		            logger.error("AdjustPriceOrderDetailV2ServiceImpl|checkIsExistRebateHyj|检查调价单折扣数据异常", e);
				}finally {
                    countDownLatch.countDown();
                }
            });
    	});
    	try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("AdjustPriceOrderDetailV2ServiceImpl|checkIsExistRebateHyj|checkIsExistRebateHyj异常", e);
        }
    	return lessThanHyjGoodsNoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setAdjustPriceOrderInPreparationById(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        Date operateTime = new Date();
        setAdjustPriceOrderAuditStatusById(adjustPriceOrderId, AuditStatusEnum.IN_PREPARATION, userDTO, operateTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitAdjustPriceOrderToAuditById(Long adjustPriceOrderId, Boolean isPortal, Integer effectMode,String scheduledTimeSecondLong, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        boolean priceIsNull = adjustPriceOrderDetailV2Service.checkAdjustPriceIsNull(adjustPriceOrder.getAdjustCode());
        if(priceIsNull) {
        	throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "商品明细价格不能为空,请检查。");
        }
        List<String> dataFullAndLimitErrors = getDataFullAndLimitErrors(adjustPriceOrder.getAdjustCode());
        if (CollectionUtils.isNotEmpty(dataFullAndLimitErrors)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, Joiner.on("\n").join(dataFullAndLimitErrors));
        }
        int ybLimitCount = adjustPriceOrderDetailExMapper.countMedicalPriceInterception(adjustPriceOrder.getAdjustCode());
        if(ybLimitCount>0){
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "存在医保限价拦截数据,不能提交。");
        }
        String lockKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.ADJUST_ORDER_SUBMIT_LOCK_KEY + adjustPriceOrderId;
        RLock lock = redissonClient.getLock(lockKey);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(0, 10, TimeUnit.MINUTES);
            if (!isLocked) {
                logger.info("submitAdjustPriceOrderToAuditById|adjustCode: {} 获取提交审核锁失败", adjustPriceOrder.getAdjustCode());
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "当前调价单正在提交审核，不用重复提交。");
            }
            logger.info("submitAdjustPriceOrderToAuditById|adjustCode: {} 提交审核", adjustPriceOrder.getAdjustCode());
            checkAdjustPriceOrderIsCanSubmit(adjustPriceOrder,isPortal);
            boolean isNeedSpecialAudit = basePriceOrderService.isNeedSpecialAuditByAdjustPriceOrderId(adjustPriceOrderId);
            PriceOrderTypeEnum priceOrderTypeEnum = isNeedSpecialAudit ? PriceOrderTypeEnum.ADJUST_OVERRUN :PriceOrderTypeEnum.ADJUST;
            Integer auditStatusOld = null;
            Integer auditStatusNew = DetailAuditStatusEnum.AUDIT_PASS.getCode();
            String operatorUserName = "";
            if(CollectionUtils.isNotEmpty(adjustTypeAutoAudits) && adjustTypeAutoAudits.contains(adjustPriceOrder.getAdjustType().intValue())) {
            	adjustPriceOrder.setAuditStatus((byte)AuditStatusEnum.AUDIT_PASS.getCode());
            	adjustPriceOrder.setAuditDate(new Date());
        	}else {
        		JmParamDTO jmDTO = new JmParamDTO();
				try {
					jmDTO = findJmParam(adjustPriceOrder, userDTO.getUserId());
				} catch (Exception e) {
					logger.error("AdjustPriceOrderV2ServiceImpl|submitAdjustPriceOrderToAuditById|异常",e);
				}
				int adjustType = adjustPriceOrder.getAdjustType().intValue();
				if(MapUtils.isNotEmpty(adjustTypeMapperMap)) {
					if(adjustTypeMapperMap.containsKey(adjustPriceOrder.getAdjustType().toString())) {
						adjustType = adjustTypeMapperMap.get(adjustPriceOrder.getAdjustType().toString());
					}
				}
        		basePriceOrderService.submitOAAduit(adjustPriceOrder.getAdjustCode(), adjustPriceOrder.getAdjustName(), adjustPriceOrder.getScheduledTime(), adjustPriceOrder.getReason(),
                        priceOrderTypeEnum, AdjustTypeEnum.getEnum(adjustType), AuditStatusEnum.UN_AUDIT, OaJumpUrlKeyEnum.ADJUST, userDTO,
                        operateTime,jmDTO.getBlankType(),jmDTO.getOrgType(),jmDTO.getJmBusiness());
                adjustPriceOrder.setAuditStatus((byte)AuditStatusEnum.UN_AUDIT.getCode());
        	}
            resetExecStatus(adjustPriceOrder,effectMode,scheduledTimeSecondLong);
            adjustPriceOrderMapper.updateByPrimaryKey(adjustPriceOrder);
            delPriceAdjustidForHd(adjustPriceOrder,userDTO);
            //审批通过状态
            if(adjustPriceOrder.getAuditStatus() == (byte)AuditStatusEnum.AUDIT_PASS.getCode()) {
            	//异步生成门店机构数据
            	AdjustOrderChangeStatusDTO statusDTO = new AdjustOrderChangeStatusDTO();
                statusDTO.setAdjustOrderCode(adjustPriceOrder.getAdjustCode());
                statusDTO.setAuditStatus(AuditStatusEnum.AUDIT_PASS.getCode());
                statusDTO.setAdjustPriceVersion(AdjustPriceVersionEnum.VERSION_2_0.getVersion());
                statusDTO.setOperatorId(adjustPriceOrder.getCreatedBy());
                statusDTO.setOperatorName(adjustPriceOrder.getCreatedByName());
                try {
                    adjustOrderChangeStatusProducer.sendMessage(statusDTO);
                } catch (Exception e) {
                	logger.error("submitAdjustPriceOrderToAuditById|调价单审核|adjustCode:{},发送MQ异常", adjustPriceOrder.getAdjustCode(), e);
                    asyncTaskExecutor.execute(() -> priceOrderV2Service.saveOrgStoreDetail(adjustPriceOrder.getAdjustCode()));
                }
            }
            asyncTaskExecutor.execute(() -> adjustPriceOrderDetailV2Service.updateAdjustPriceOrderDetailAuditStatus(adjustPriceOrder.getAdjustCode(),auditStatusNew,auditStatusOld,operatorUserName,true));
        } catch (Exception e) {
        	logger.error("AdjustPriceOrderV2ServiceImpl|submitAdjustPriceOrderToAuditById异常",e);
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, e.getMessage());
        } finally {
            if (isLocked && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理单体加盟店缓存
     * @param adjustPriceOrder
     * @param userDTO
     */
    private void delPriceAdjustidForHd(AdjustPriceOrder adjustPriceOrder, TokenUserDTO userDTO) {
        // 基本参数校验
        if (adjustPriceOrder == null || userDTO == null) {
            return;
        }
        Long storeId = adjustPriceOrderDetailV2Service.getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null == storeId){
            return;
        }
        try {
            // 获取门店信息
            ResponseEntity<MdmStoreBaseDTO> responseEntity = storeService.findByStoreId(storeId);
            if (responseEntity == null || !HttpStatus.OK.equals(responseEntity.getStatusCode()) || responseEntity.getBody() == null) {
                logger.warn("获取门店信息失败: storeId={}", storeId);
                return;
            }
            String sapCode = responseEntity.getBody().getStoreNo();
            if (StringUtils.isBlank(sapCode)) {
                logger.warn("门店SAP编码为空: storeId={}", storeId);
                return;
            }
            // 删除Redis缓存
            String redisKey = RedisKeysConstant.getPriceAdjustidForHdKey(userDTO.getUserId(), sapCode);
            redissonClient.getBucket(redisKey).deleteAsync();
            logger.debug("删除价格调整缓存: userId={}, sapCode={}", userDTO.getUserId(), sapCode);
        } catch (Exception e) {
            logger.error("删除价格调整缓存异常: storeId={}, userId={}", storeId, userDTO.getUserId(), e);
        }
    }

    /**
     * 单体加盟提交 重新设置生效方式 并且状态修改为正常
     * @param adjustPriceOrder
     * @param effectMode
     * @param scheduledTimeSecondLong
     */
    private void resetExecStatus(AdjustPriceOrder adjustPriceOrder,Integer effectMode,String scheduledTimeSecondLong){
        if(null==effectMode){
            return;
        }
        if(ExecEnum.EXEC_PLAN.getCode() == effectMode.intValue() && StringUtils.isBlank(scheduledTimeSecondLong)){
            throw new BusinessErrorException(ReturnCodeEnum.ERROR_SCHEDULED_TIME_NULL);
        }
        if (effectMode.intValue() == ExecEnum.EXEC_NOW.getCode()) {
            adjustPriceOrder.setExecStatus((byte)ExecEnum.EXEC_NOW.getCode());
            adjustPriceOrder.setScheduledTime(new Date());
        } else {
            adjustPriceOrder.setExecStatus((byte)ExecEnum.EXEC_PLAN.getCode());
            Date date = AmisTimeUtil.getDateFromDateTime(scheduledTimeSecondLong);
            Date now = new Date(); // 获取当前时间
            // 验证日期不能小于当前时间
            if (date != null && date.before(now)) {
                throw new BusinessErrorException("计划时间不能早于当前时间");
            }
            adjustPriceOrder.setScheduledTime(date);
        }
        adjustPriceOrder.setExecStatus(effectMode.byteValue());
        //扩展字段增加生效方式用于回显
        String extend = adjustPriceOrder.getExtend();
        Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(extend);
        if(instance.isPresent()) {
            AdjustPriceOrderExtend adjustPriceOrderExtend = instance.get();
            adjustPriceOrderExtend.setEffectMode(adjustPriceOrder.getExecStatus().intValue());
            adjustPriceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(adjustPriceOrderExtend));
        }else {
            adjustPriceOrder.setExtend(AdjustPriceOrderExtend.getJSONFormatStr(adjustPriceOrder.getExecStatus().intValue()));
        }
        adjustPriceOrder.setStatus(YNEnum.NO.getType().byteValue());
    }

    private JmParamDTO findJmParam(AdjustPriceOrder adjustPriceOrder,Long userId) {
    	JmParamDTO jmDto = new JmParamDTO();
    	MdmStoreBaseDTO storeBase = new MdmStoreBaseDTO();
		Integer auditBlankType = AuditBlankTypeEnum.NO.getType();
        String jmBusiness = null;
		Integer auditOrgType = checkOrgType(adjustPriceOrder.getAdjustCode(),userId,storeBase);
        if(!auditOrgType.equals(AuditOrgTypeEnum.DIRECTSALE.getType())){
            auditBlankType= checkBlankType(adjustPriceOrder.getAdjustCode());
            jmBusiness = getJmBusiness(auditOrgType, storeBase);
        }
		jmDto.setBlankType(auditBlankType);
		jmDto.setOrgType(auditOrgType);
		jmDto.setJmBusiness(jmBusiness);
		Optional<AdjustPriceOrderExtend> orderExtend = AdjustPriceOrderExtend.getInstance(adjustPriceOrder.getExtend());
		if(orderExtend.isPresent()) {
			AdjustPriceOrderExtend extend = orderExtend.get();
			extend.setBlankType(jmDto.getBlankType());
			extend.setOrgType(jmDto.getOrgType());
			extend.setJmBusiness(jmDto.getJmBusiness());
			adjustPriceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(extend));
		}else {
			AdjustPriceOrderExtend instance = AdjustPriceOrderExtend.getInstance();
			instance.setBlankType(jmDto.getBlankType());
			instance.setOrgType(jmDto.getOrgType());
			instance.setJmBusiness(jmDto.getJmBusiness());
			adjustPriceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(instance));
		}
		return jmDto;
    }

    /**
     * 获取所属连锁编码
     * @param auditOrgType
     * @param storeBase
     * @return
     */
    private String getJmBusiness(Integer auditOrgType,MdmStoreBaseDTO storeBase) {
    	String jmBusiness = null;
    	if(StringUtils.isBlank(storeBase.getExtend())) {
    		return jmBusiness;
    	}
        JSONObject jsonObject = JSON.parseObject(storeBase.getExtend());
        if(null != jsonObject && jsonObject.containsKey(PriceSyncConstant.GJ_CODE)){
            jmBusiness = jsonObject.getString(PriceSyncConstant.GJ_CODE);
        }
		return jmBusiness;
    }

    private Integer checkBlankType(String adjustCode) {
        Set<Integer> blankSpaceSet = new HashSet<Integer>();
    	List<AdjustOrgIdLevelDTO> list = adjustPriceOrderDetailExMapper.selectAdjustOrderDetailExtend1(adjustCode);
    	if(CollectionUtils.isEmpty(list)) {
    		return AuditBlankTypeEnum.NO.getType();
    	}
    	Set<Long> businesSet = new HashSet<Long>();
        for (AdjustOrgIdLevelDTO detail : list) {
        	Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(detail.getExtend1());
        	if(!instance.isPresent()) {
        		continue;
        	}
        	AdjustPriceOrderDetailExtend1 extend1 = instance.get();
    		if(StringUtils.isBlank(extend1.getBusinessIds())){
                continue;
            }
            businesSet.addAll(Arrays.stream(extend1.getBusinessIds().split(","))
                .map(Long::valueOf)
                .collect(Collectors.toSet()));
        }
        blankSpaceSet = getBlankSpaceSet(Lists.newArrayList(businesSet));
        if(CollectionUtils.isEmpty(blankSpaceSet)){
        	return AuditBlankTypeEnum.NO.getType();
        }
        if(blankSpaceSet.contains(AuditBlankTypeEnum.NO.getPermType())){
            return AuditBlankTypeEnum.NO.getType();
        }else {
        	return AuditBlankTypeEnum.YES.getType();
        }
    }

    private Set<Integer> getBlankSpaceSet(List<Long> businessIdList){
    	Set<Integer> blankSpaceSet = new HashSet<Integer>();
    	ResponseEntity<List<MdmBusinessBaseDTO>> response = storeService.findMdmBusinessBaseByBusinessIds(businessIdList);
		if(response.getBody() == null || CollectionUtils.isEmpty(response.getBody())){
			return blankSpaceSet;
		}
		//extend字段 示例值：{"blankSpace":"1"} blankSpace 空白区域 int 1-是 2-否
		List<MdmBusinessBaseDTO> businessBaseList = response.getBody();
        for (MdmBusinessBaseDTO businessBase : businessBaseList) {
            JSONObject jsonObject = JSON.parseObject(businessBase.getExtend());
            if(null == jsonObject || !jsonObject.containsKey("blankSpace")){
                continue;
            }
            blankSpaceSet.add(jsonObject.getInteger("blankSpace"));
        }
		return blankSpaceSet;
    }

    private Integer checkOrgType(String adjustCode,Long userId,MdmStoreBaseDTO storeBase) {
    	List<AdjustOrgIdLevelDTO> list = adjustPriceOrderDetailExMapper.selectAdjustOrderDetailOrgIdLevel(adjustCode);
    	if(CollectionUtils.isEmpty(list)) {
    		return AuditOrgTypeEnum.DIRECTSALE.getType();
    	}
    	Set<Long> storeIdSet = getStoreIdSet(list, userId);
		if(CollectionUtils.isEmpty(storeIdSet)){
			return AuditOrgTypeEnum.DIRECTSALE.getType();
		}
		Set<String> storeAttrSet = getStoreAttrSet(Lists.newArrayList(storeIdSet),storeBase);
 		if(CollectionUtils.isEmpty(storeAttrSet)) {
 			return AuditOrgTypeEnum.DIRECTSALE.getType();
 		}
 		boolean direct = StoreAttrTypeEnum.checkIsDirect(Lists.newArrayList(storeAttrSet));
 		if(direct) {
 			return AuditOrgTypeEnum.DIRECTSALE.getType();
 		}
 		boolean joinSingle = StoreAttrTypeEnum.checkIsJoinSingle(Lists.newArrayList(storeAttrSet));
 		if(joinSingle) {
 			return AuditOrgTypeEnum.JOIN_SINGLE.getType();
 		}
 		boolean jJoinChain = StoreAttrTypeEnum.checkIsJoinChain(Lists.newArrayList(storeAttrSet));
 		if(jJoinChain) {
 			return AuditOrgTypeEnum.JOIN_CHAIN.getType();
 		}
    	return AuditOrgTypeEnum.DIRECTSALE.getType();
    }

    private Set<String> getStoreAttrSet(List<Long> storeIdList,MdmStoreBaseDTO storeBase){
    	Set<String> storeAttrSet = new HashSet<String>();
    	ResponseEntity<List<MdmStoreBaseDTO>> response = null;
    	List<MdmStoreBaseDTO> storeBaseList = null;
    	List<List<Long>> partition = Lists.partition(storeIdList, 300);
    	for (List<Long> list : partition) {
    		response = storeService.findStoreByStoreIdsAndExtend(list,false);
     		if(response.getBody() == null || CollectionUtils.isEmpty(response.getBody())){
     			continue;
     		}
     		storeBaseList = response.getBody().stream()
     			    .filter(dto -> dto.getStoreAttr() != null && !"null".equals(dto.getStoreAttr()) && !dto.getStoreAttr().isEmpty())
     			    .collect(Collectors.toList());
     		if(CollectionUtils.isEmpty(storeBaseList)) {
     			continue;
     		}
     		if(StringUtils.isBlank(storeBase.getExtend())) {
     			storeBase.setExtend(getBelongTo(storeBaseList)==null?null:getBelongTo(storeBaseList).getExtend());
     		}
     		storeAttrSet.addAll(storeBaseList.stream().map(MdmStoreBaseDTO::getStoreAttr).collect(Collectors.toSet()));
		}
    	if(CollectionUtils.isEmpty(storeAttrSet)) {
 			storeAttrSet.add(StoreAttrTypeEnum.DIRECT_SELFBUILD.getMessage());
 		}
    	return storeAttrSet;
    }

    private MdmStoreBaseDTO getBelongTo(List<MdmStoreBaseDTO> storeBaseList) {
    	for (MdmStoreBaseDTO storeBase : storeBaseList) {
    		JSONObject jsonObject = JSON.parseObject(storeBase.getExtend());
            if(null != jsonObject && jsonObject.containsKey(PriceSyncConstant.GJ_CODE) && StringUtils.isNotBlank(jsonObject.getString(PriceSyncConstant.GJ_CODE))){
            	return storeBase;
            }
		}
    	return null;
    }

    private Set<Long> getStoreIdSet(List<AdjustOrgIdLevelDTO> list,Long userId){
    	Set<Long> storeIdSet = new HashSet<Long>();
    	List<Long> orgIdList = Lists.newArrayList();
		List<Integer> orgLevelList = Lists.newArrayList();
		Set<Long> storeOrgIdSet = Sets.newLinkedHashSet();
		Set<Long> otherOrgIdSet = Sets.newLinkedHashSet();
    	for (AdjustOrgIdLevelDTO detail : list) {
			if(StringUtils.isEmpty(detail.getOrgIds()) || StringUtils.isEmpty(detail.getOrgLevels())) {
				continue;
			}
			orgIdList.clear();orgLevelList.clear();
			orgIdList = Arrays.stream(detail.getOrgIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
			orgLevelList = Arrays.stream(detail.getOrgLevels().split(",")).map(Integer::valueOf).collect(Collectors.toList());
			if(orgIdList.size() != orgLevelList.size()) {
				continue;
			}
			for (int i = 0; i < orgLevelList.size(); i++) {
				if(orgLevelList.get(i) == OrgLevelEnum.STORE.getCode()) {
					storeOrgIdSet.add(orgIdList.get(i));
				}else {
					otherOrgIdSet.add(orgIdList.get(i));
				}
			}
		}
    	if(CollectionUtils.isNotEmpty(storeOrgIdSet)) {
    		storeIdSet.addAll(orgIdConvertStoreId(Lists.newArrayList(storeOrgIdSet)));
    	}
    	if(CollectionUtils.isNotEmpty(otherOrgIdSet)){
    		List<OrgDTO> orgList = permissionService.getUserDataScopeStoreBatch(userId, RequestHeaderContextUtils.getResourceId(), Lists.newArrayList(otherOrgIdSet), Constants.ORGTYPE_ORGID);
    		if(CollectionUtils.isNotEmpty(orgList)) {
    			storeIdSet.addAll(orgList.stream().map(OrgDTO::getOutId).collect(Collectors.toSet()));
    		}
    	}
    	return storeIdSet;
    }

    private List<Long> orgIdConvertStoreId(List<Long> orgIdList){
    	return CacheVar.listStoreIdsByStoreOrgIds(orgIdList);
    }

    private void sendSupplementExtend(AdjustPriceOrder order) {
    	adjustPriceOrderOrgStoreDetailExtendProducer.sendMq(order);
    }


    /**
     * 把调价单的组织机构拆分到拥有的所有门店，先删除已经存在组织门店数据（因为存在审核退回重新编辑提交）
     * @param adjustPriceOrder
     */
    private void splitAdjustOrgToStore(AdjustPriceOrder adjustPriceOrder,
                                       List<AdjustPriceOrderDetail> copyOrderDetailList,
                                       Date operateTime) {
        // 参数验证
        if (adjustPriceOrder == null || adjustPriceOrder.getAdjustCode() == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }

        String adjustCode = adjustPriceOrder.getAdjustCode();

        // 清理旧数据
        adjustPriceOrderOrgStoreDetailService.deleteByAdjustCode(adjustCode);

        // 解析渠道和价格类型
        List<Integer> channelList = Arrays.stream(adjustPriceOrder.getChannel().split(","))
            .map(String::trim)
            .map(Integer::parseInt)
            .collect(Collectors.toList());

        List<String> adjustPriceTypeList = Arrays.stream(adjustPriceOrder.getAdjustPriceType().split(","))
            .map(String::trim)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(channelList) || CollectionUtils.isEmpty(adjustPriceTypeList)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "调价单主表数据不完整");
        }

        // 补充复制订单详情中的渠道和价格类型
        if (CollectionUtils.isNotEmpty(copyOrderDetailList)) {
            for (AdjustPriceOrderDetail detail : copyOrderDetailList) {
                if (!adjustPriceTypeList.contains(detail.getPriceTypeCode())) {
                    adjustPriceTypeList.add(detail.getPriceTypeCode());
                }
                if (!channelList.contains(detail.getChannelId())) {
                    channelList.add(detail.getChannelId());
                }
            }
        }

        // 分页处理订单详情
        List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailAllList = new ArrayList<>();
        int page = 0;

        while (true) {
            int offset = page * DEFAULT_PAGE_SIZE;
            List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailExMapper
                .listAdjustPriceOrderDetailPageByCode(adjustCode, offset, DEFAULT_PAGE_SIZE);

            if (CollectionUtils.isEmpty(orderDetailList)) {
                break;
            }

            // 处理当前页的订单详情
            for (AdjustPriceOrderDetail adjustPriceOrderDetail : orderDetailList) {
                processOrderDetail(adjustPriceOrderDetail, adjustCode, channelList,
                    adjustPriceTypeList, operateTime, orgStoreDetailAllList);

                // 批量保存避免内存溢出
                if (orgStoreDetailAllList.size() >= BATCH_SAVE_THRESHOLD) {
                    batchSaveOrgStoreDetail(adjustPriceOrder, orgStoreDetailAllList);
                    orgStoreDetailAllList.clear();
                }
            }
            page++;
        }

        // 保存剩余数据
        if (CollectionUtils.isNotEmpty(orgStoreDetailAllList)) {
            batchSaveOrgStoreDetail(adjustPriceOrder, orgStoreDetailAllList);
        }
    }

    /**
     * 处理单个订单详情
     */
    private void processOrderDetail(AdjustPriceOrderDetail adjustPriceOrderDetail,
                                    String adjustCode,
                                    List<Integer> channelList,
                                    List<String> adjustPriceTypeList,
                                    Date operateTime,
                                    List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailAllList) {

        // 获取组织门店信息（带缓存）
        List<AdjustPriceOrderOrgStoreDetail> orgStoreList = getOrgStoreListWithCache(
            adjustCode, adjustPriceOrderDetail, operateTime);
        if (CollectionUtils.isEmpty(orgStoreList)) {
            logger.error("AdjustPriceOrderV2ServiceImpl|splitAdjustOrgToStore|adjustCode:{},门店信息不完整", adjustCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "门店信息不完整");
        }
        // 处理覆盖下级配置
        Optional<AdjustPriceOrderDetailExtend1> detailExtent1 =
            AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
        if (detailExtent1.isPresent()) {
            AdjustPriceOrderDetailExtend1 extent1 = detailExtent1.get();
            if (extent1.getOverrideLowerLevel() != null && extent1.getOverrideLowerLevel() == 1) {
                for (AdjustPriceOrderOrgStoreDetail orgStoreDetail : orgStoreList) {
                    orgStoreDetail.setAdjustPriceLevel(OrgLevelTypeEnum.STORE.getNewCode());
                }
            }
        }
        // 获取订单详情映射
        List<Long> orgIdList = Arrays.stream(adjustPriceOrderDetail.getOrgIds().split(","))
            .map(String::trim)
            .map(Long::parseLong)
            .collect(Collectors.toList());

        List<AdjustPriceOrderDetail> detailList = adjustPriceOrderDetailExMapper
            .selectOrderDetailByGoodsNoOrgList(adjustCode, adjustPriceOrderDetail.getGoodsNo(),
                orgIdList, adjustPriceOrderDetail.getSkuId());

        Map<String, AdjustPriceOrderDetail> detailMap = detailList.stream()
            .collect(Collectors.toMap(
                k -> k.getGoodsNo() + "_" + k.getSkuId() + "_" + k.getPriceTypeCode(),
                Function.identity(),
                (v1, v2) -> v2));

        // 创建扩展信息
        AdjustPriceOrderOrgStoreDetailExtend orgStoreDetailExtend = createOrgStoreDetailExtend(adjustPriceOrderDetail);
        BigDecimal minusOne = new BigDecimal(Constants.INVALID_INDEX);
        // 生成组织门店详情 - 优化嵌套循环
        for (Integer channelId : channelList) {
            for (String adjustPriceType : adjustPriceTypeList) {
                String detailKey = adjustPriceOrderDetail.getGoodsNo() + "_" +
                    adjustPriceOrderDetail.getSkuId() + "_" + adjustPriceType;
                AdjustPriceOrderDetail orderDetail = detailMap.get(detailKey);

                if (orderDetail == null || orderDetail.getPrice() == null || orderDetail.getPrice().compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }

                // 获取返利信息
                BigDecimal rebateHyj = null;
                Optional<AdjustPriceOrderDetailExtend1> instance =
                    AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
                if (instance.isPresent()) {
                    rebateHyj = instance.get().getRebate();
                }

                // 为每个门店创建记录
                for (AdjustPriceOrderOrgStoreDetail orgStore : orgStoreList) {
                    AdjustPriceOrderOrgStoreDetail newOrgStore = new AdjustPriceOrderOrgStoreDetail();
                    BeanUtils.copyProperties(orgStore, newOrgStore);

                    newOrgStore.setGoodsNo(adjustPriceOrderDetail.getGoodsNo());
                    newOrgStore.setSkuId(adjustPriceOrderDetail.getSkuId());
                    newOrgStore.setPriceTypeCode(adjustPriceType);
                    newOrgStore.setPrice(orderDetail.getPrice());
                    newOrgStore.setChannelId(channelId);

                    orgStoreDetailExtend.setAdjustDetailId(orderDetail.getId());
                    orgStoreDetailExtend.setRebateHyj(rebateHyj);
                    newOrgStore.setExtend(AdjustPriceOrderOrgStoreDetailExtend.toJSONFormatStr(orgStoreDetailExtend));

                    orgStoreDetailAllList.add(newOrgStore);
                }
            }
        }
    }

    /**
     * 获取组织门店列表（带缓存）
     */
    private List<AdjustPriceOrderOrgStoreDetail> getOrgStoreListWithCache(String adjustCode,
                                                                          AdjustPriceOrderDetail adjustPriceOrderDetail,
                                                                          Date operateTime) {
        // 构建缓存key
        String orgStoreDetailUniqueKey = adjustCode + "_" + adjustPriceOrderDetail.getOrgIds();
        String cacheKey = Md5Utils.MD5Encode(getAdjustOrgStoreDetailRedisKey(orgStoreDetailUniqueKey));

        // 尝试从缓存获取
        RBucket<List<AdjustPriceOrderOrgStoreDetail>> bucket = redissonClient.getBucket(cacheKey);
        if (bucket.isExists()) {
            return bucket.get();
        }

        // 缓存不存在，重新构建
        List<Long> orgIdList = Arrays.stream(adjustPriceOrderDetail.getOrgIds().split(","))
            .map(String::trim)
            .map(Long::parseLong)
            .collect(Collectors.toList());

        List<Integer> orgLevelList = Arrays.stream(adjustPriceOrderDetail.getOrgLevels().split(","))
            .map(String::trim)
            .map(Integer::parseInt)
            .collect(Collectors.toList());

        // 构建组织层级信息
        List<OrgLevelVO> orgLevelBeanList = new ArrayList<>();
        for (int i = 0; i < orgIdList.size(); i++) {
            OrgLevelVO orgLevelVO = new OrgLevelVO();
            orgLevelVO.setOrgId(orgIdList.get(i));
            orgLevelVO.setOrgLevel(orgLevelList.get(i));
            orgLevelBeanList.add(orgLevelVO);
        }

        List<AdjustPriceOrderOrgStoreDetail> orgStoreList = getAdjustStoreDetailsFromOrgs(
            adjustCode, orgLevelBeanList, operateTime);
        // 设置缓存
        bucket.set(orgStoreList, CACHE_TTL_MINUTES, TimeUnit.MINUTES);

        return orgStoreList;
    }

    /**
     * 批量保存组织门店详情
     */
    private void batchSaveOrgStoreDetail(AdjustPriceOrder adjustPriceOrder,
                                         List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailAllList) {
        logger.info("AdjustPriceOrderV2ServiceImpl|splitAdjustOrgToStore|adjustCode:{},开始获取分布式ID,数量:{}",
            adjustPriceOrder.getAdjustCode(), orgStoreDetailAllList.size());

        long[] orgStoreDetailIds = tocExtService.getDistributedIDList(
            DistributedIDTypeEnum.ADJUST_PRICE_ORG_STORE_DETAIL.getBiz(),
            orgStoreDetailAllList.size());

        for (int i = 0; i < orgStoreDetailAllList.size(); i++) {
            AdjustPriceOrderOrgStoreDetail orgStoreDetail = orgStoreDetailAllList.get(i);
            orgStoreDetail.setOrgStoreDetailId(String.valueOf(orgStoreDetailIds[i]));
            if (orgStoreDetail.getSkuId() == null) {
                orgStoreDetail.setSkuId(0L);
            }
        }

        logger.info("AdjustPriceOrderV2ServiceImpl|splitAdjustOrgToStore|adjustCode:{},开始执行保存操作",
            adjustPriceOrder.getAdjustCode());

        adjustPriceOrderOrgStoreDetailService.batchInsert(orgStoreDetailAllList);
    }

    /**
     *
     * @Title: createOrgStoreDetailExtend
     * @Description: orgStoreDetail:extend封装
     * @param: @param adjustPriceOrderDetail
     * @param: @return
     * @return: String
     * @throws
     */
    private AdjustPriceOrderOrgStoreDetailExtend createOrgStoreDetailExtend(AdjustPriceOrderDetail adjustPriceOrderDetail) {
    	String goodsTypeName = adjustPriceOrderDetail.getComment()==null?"":adjustPriceOrderDetail.getComment();
    	String activityTypeName = adjustPriceOrderDetail.getLabel()==null?"":adjustPriceOrderDetail.getLabel();
    	Integer priceFlag = adjustPriceOrderDetail.getPriceFlag()==null?PriceFlagEnum.NO.getCode():adjustPriceOrderDetail.getPriceFlag();
    	return AdjustPriceOrderOrgStoreDetailExtend.getInstance(goodsTypeName, activityTypeName, priceFlag, null,
    			DealStepStatusEnum.EXE_DEFAULT.getCode(),"",
    			DealStepStatusEnum.EXE_DEFAULT.getCode(),"",
    			DealStepStatusEnum.EXE_DEFAULT.getCode(),"",
    			null);
    }
    /**
     *
     * @Title: getAdjustOrgStoreDetailRedisKey
     * @Description: 转换MD5设置为rediskey
     * @param: @param orgStoreDetailUniqueKey
     * @param: @return
     * @return: String
     * @throws
     */
   private String getAdjustOrgStoreDetailRedisKey(String orgStoreDetailUniqueKey) {
   	return RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ADJUST_ORG_STORE_DETAIL_+orgStoreDetailUniqueKey;
   }
    /**
     * 对组织列表按照组织层级进行分组并取出所有门店
     * @param orgLevelVOList
     * @return
     */
    @Override
    public List<AdjustPriceOrderOrgStoreDetail> getAdjustStoreDetailsFromOrgs(String adjustCode, List<OrgLevelVO> orgLevelVOList, Date operateTime) {
        List<AdjustPriceOrderOrgStoreDetail> resultStoreDetails = Lists.newArrayList();
        Map<Integer, List<OrgLevelVO>> groupOrgLevelVOMap = orgLevelVOList.stream().collect(Collectors.groupingBy(OrgLevelVO::getOrgLevel));
        //先获取所有门店基本信息
        for (Integer orgLevel : groupOrgLevelVOMap.keySet()) {
            resultStoreDetails.addAll(getStoreDetailListByOneLevelOrgs(adjustCode, orgLevel, groupOrgLevelVOMap.get(orgLevel), OrgLevelTypeEnum.getByCode(orgLevel), operateTime));
        }
        if (resultStoreDetails.size() > 0) {
            //long [] orgStoreDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.ADJUST_PRICE_ORG_STORE_DETAIL.getBiz(), resultStoreDetails.size());
            List<Long> storeIdList = resultStoreDetails.stream().map(AdjustPriceOrderOrgStoreDetail::getOrgId).collect(Collectors.toList());
            resultStoreDetails = supplementBusinessAndPlatformInfo(storeIdList, resultStoreDetails);
        }
        return resultStoreDetails;
    }

    /**
     * 补充门店数据的公司和平台信息
     * @param storeDetailList
     * @return
     */
    private List<AdjustPriceOrderOrgStoreDetail> supplementBusinessAndPlatformInfo(List<Long> storeIdList,
        List<AdjustPriceOrderOrgStoreDetail> storeDetailList) {
        List<List<Long>> partitionStoreIdsList = Lists.partition(storeIdList, 1000);
        Map<Long, List<OrgDTO>> ancestorsOrgMap = new HashMap<>();
        for (List<Long> partitionStoreIds : partitionStoreIdsList) {
            ancestorsOrgMap.putAll(permissionExtService.getAncestorsOrgList(partitionStoreIds));
        }
        List<AdjustPriceOrderOrgStoreDetail> storeDetailsResult = new ArrayList<>(storeDetailList.size());
        for (AdjustPriceOrderOrgStoreDetail storeDetail : storeDetailList) {
            List<OrgDTO> ancestorsOrgList = ancestorsOrgMap.get(storeDetail.getOrgId());
            if (ancestorsOrgList != null) {
                OrgSummaryDTO orgSummaryDTO = permissionExtService.getOrgSummaryDTO(ancestorsOrgList);
                OrgDTO platformOrgDTO = orgSummaryDTO.getPlatformDTO();
                OrgDTO businessOrgDTO = orgSummaryDTO.getBusinessDTO();
                if (platformOrgDTO != null) {
                    storeDetail.setPlatformId(platformOrgDTO.getOutId());
                    storeDetail.setPlatformName(platformOrgDTO.getShortName());
                }
                if (businessOrgDTO != null) {
                    storeDetail.setBusinessId(businessOrgDTO.getOutId());
                    storeDetail.setBusinessName(businessOrgDTO.getShortName());
                }
                if (storeDetail.getBusinessId() != null) {
                    storeDetailsResult.add(storeDetail);
                }
            }
        }
        return storeDetailsResult;
    }

    /**
     * 获取同一层级的组织所有门店详情
     * @param adjustCode
     * @param oneLevelOrgVOList
     * @return
     */
    private List<AdjustPriceOrderOrgStoreDetail> getStoreDetailListByOneLevelOrgs(String adjustCode, Integer orgLevel, List<OrgLevelVO> oneLevelOrgVOList,
        Optional<OrgLevelTypeEnum> orgLevelTypeEnumOptional, Date operateTime) {
        if (orgLevelTypeEnumOptional.isPresent()) {
            List<Long> oneLevelOrgIdList = oneLevelOrgVOList.stream().map(OrgLevelVO::getOrgId).collect(Collectors.toList());
            OrgLevelTypeEnum orgLevelTypeEnum = orgLevelTypeEnumOptional.get();
            List<OrgLevelVO> orgLevelVOList = Collections.emptyList();
            if (orgLevel == OrgLevelTypeEnum.STORE.getCode()) {
                List<Long> OrgIdList = oneLevelOrgVOList.stream().map(OrgLevelVO::getOrgId).collect(Collectors.toList());
                orgLevelVOList = permissionExtService.listPermissionByOrgIds(OrgIdList).values().stream().collect(Collectors.toList());
            } else {
                orgLevelVOList = permissionExtService.listStoreInfosByOrgIds(oneLevelOrgIdList);
            }
            return orgLevelVOList.stream().map(orgLevelVO -> {
                AdjustPriceOrderOrgStoreDetail orgStoreDetail = new AdjustPriceOrderOrgStoreDetail();
                orgStoreDetail.setOrgId(orgLevelVO.getOrgId());
                orgStoreDetail.setOrgName(orgLevelVO.getOrgName());
                orgStoreDetail.setStoreId(orgLevelVO.getOutId());
                orgStoreDetail.setStoreName(orgLevelVO.getOrgName());
                orgStoreDetail.setAdjustPriceLevel(orgLevelTypeEnum.getNewCode());
                orgStoreDetail.setAdjustCode(adjustCode);
                orgStoreDetail.setStatus((byte)StatusEnum.NORMAL.getCode());
                orgStoreDetail.setGmtCreate(operateTime);
                return orgStoreDetail;
            }).filter(orgLevelDetail -> orgLevelDetail.getStoreId() != null).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public AdjustPriceOrderOrgDetailVO listAdjustPriceOrderOrgDetailList(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);;
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        List<AdjustPriceOrderOrgDetail> orgDetailList = adjustPriceOrderOrgDetailService.listByAdjustCode(adjustPriceOrder.getAdjustCode());
        List<Long> orgIdList = orgDetailList.stream().map(AdjustPriceOrderOrgDetail::getOrgId).collect(
            Collectors.toList());
        String orgNames = orgDetailList.stream().map(AdjustPriceOrderOrgDetail::getOrgName).collect(Collectors.joining(PriceConstant.NAME_SEPARATOR));
        List<String> tabTypeValList = priceOrderTabTypeDetailService.getAdjustPriceOrderTabTypeValues(adjustPriceOrder.getAdjustCode());
        return new AdjustPriceOrderOrgDetailVO(adjustPriceOrder.getAdjustOrgTabType(), tabTypeValList, orgIdList, orgNames);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditSuccessOrFailure(String adjustCode, AuditStatusEnum statusEnum) {
        if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndLastAudit(adjustPriceOrder, statusEnum);
        if (statusEnum == AuditStatusEnum.AUDIT_REJECTED) {
            adjustPriceOrderDetailV2Service.deleteAllDataNotInAdjustPriceTypeCodes(adjustCode, adjustPriceOrder.getAdjustPriceType());
        }
        adjustPriceOrder.setAuditStatus((byte)statusEnum.getCode());
        adjustPriceOrder.setAuditDate(operateTime);
        adjustPriceOrder.setGmtUpdate(operateTime);
        adjustPriceOrderMapper.updateByPrimaryKey(adjustPriceOrder);

    }

    @Override
    public List<OptionDto> listBusinessInfosByOrderId(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        List<OrgLevelVO> orgLevelVOList = basePriceOrderService.listOrgListByAdjustPriceOrderId(adjustPriceOrderId);
        return permissionExtService.getGoodsBusinessList(orgLevelVOList);
    }

    @Override
    public List<OptionDto> listStoreInfosByByOrderId(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        List<OrgLevelVO> orgLevelVOList = basePriceOrderService.listOrgListByAdjustPriceOrderId(adjustPriceOrderId);
        return permissionExtService.getGoodsStoreList(orgLevelVOList);
    }

    @Override
    public List<AdjustPriceOrder> listEffectAdjustOrdersByEffectTime(Integer adjustType,LocalDateTime startTime, LocalDateTime endTime, Integer page, Integer pageSize) {
        AmisPageParam pageParam = AmisPageParam.createInstance(page, pageSize);
        AdjustPriceOrderExample example = new AdjustPriceOrderExample();
        Date startDateTime = Date.from(startTime.atZone(ZoneId.systemDefault()).toInstant());
        Date endDateTime = Date.from(endTime.atZone(ZoneId.systemDefault()).toInstant());
        Criteria createCriteria = example.createCriteria();
        createCriteria.andEffectTimeBetween(startDateTime, endDateTime)
            .andEffectStatusEqualTo(EffectStatusEnum.EFFECTED.getCode().byteValue())
            .andAdjustPriceVersionEqualTo(AdjustPriceVersionEnum.VERSION_2_0.getVersion()).andAdjustCodeLike("1AJUST%");
        if(null!=adjustType) {
        	createCriteria.andAdjustTypeEqualTo(adjustType.byteValue());
        }
        example.setOffset((int)pageParam.getOffset());
        example.setLimit(pageParam.getSize());
        example.setOrderByClause("effect_time desc");

        List<AdjustPriceOrder> adjustOrderList = adjustPriceOrderMapper.selectByExample(example);
        return adjustOrderList == null ? Collections.emptyList() : adjustOrderList;
    }

    @Override
    public boolean isAdjustPriceOrderToSubmit(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);;
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        return adjustPriceOrder.getAuditStatus() == AuditStatusEnum.UN_SUBMITTED.getCode();
    }

    @Override
    public boolean isAdjustPriceOrderToSave(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        boolean result = adjustPriceOrderDetailV2Service.isAdjustPriceOrderDetailDataFull(adjustPriceOrderId, userDTO);
        return result;
    }

    @Override
    public void immediatelyExecutePriceById(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);;
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        if (!isCanImmediatelyExecute(adjustPriceOrder)) {
            logger.info("<===[AdjustPriceOrderV2ServiceImpl.immediatelyExecutePriceById] adjustPriceOrderId: {} 不能立即值价。", adjustPriceOrderId);;
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        setAdjustPriceOrderExecStatus(adjustPriceOrder, ExecEnum.EXEC_NOW, userDTO, operateTime);
        asyncTaskExecutor.execute(() -> {
        	//立即值价
            adjustPriceOrderDetailService.distributePreEffectAdjustPriceOrder(adjustPriceOrder.getAdjustCode());
        });
    }

    /**
     * 检查调价单是否能提交
     * @param adjustPriceOrder
     */
    private void checkAdjustPriceOrderIsCanSubmit(AdjustPriceOrder adjustPriceOrder,Boolean isPortal) {
    	if (adjustPriceOrder.getAuditStatus() == AuditStatusEnum.UN_AUDIT.getCode() ||
    			adjustPriceOrder.getAuditStatus() == AuditStatusEnum.AUDIT_PASS.getCode()) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "当前调价单已提交审核，不用重复提交");
        }
        if (adjustPriceOrder.getAuditStatus() != AuditStatusEnum.UN_SUBMITTED.getCode()) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "需要先保存调价单才能提交审核");
        }
        Long storeId = adjustPriceOrderDetailV2Service.getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);

        if(null == storeId || null==isPortal || !isPortal) {
        	boolean reasonIsNullCount = adjustPriceOrderDetailV2Service.countAdjustPriceOrderDetailReasonIsNull(adjustPriceOrder.getAdjustCode());
            if(reasonIsNullCount) {
            	throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "请检查商品行中的调价原因是否填写完整");
            }
        }
    }

    /**
     * 保存用户的执行状态，只有当前调价单的拥有者才能更新
     * @param adjustPriceOrder
     * @param execEnum
     * @param userDTO
     * @param operateTime NOT NULL
     */
    private void setAdjustPriceOrderExecStatus(AdjustPriceOrder adjustPriceOrder, ExecEnum execEnum, TokenUserDTO userDTO, Date operateTime) {
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        adjustPriceOrder.setExecStatus((byte)execEnum.getCode());
        if (userDTO != null) {
            adjustPriceOrder.setUpdatedBy(userDTO.getUserId());
            adjustPriceOrder.setUpdatedByName(userDTO.getName());
        }
        adjustPriceOrder.setGmtUpdate(operateTime);
        adjustPriceOrderMapper.updateByPrimaryKey(adjustPriceOrder);
    }

    /**
     * 保存用户的审核状态，只有当前调价单的拥有者才能更新
     * @param adjustPriceOrderId
     * @param statusEnum
     * @param userDTO
     * @param OperateTime NOT NULL
     */
    private void setAdjustPriceOrderAuditStatusById(Long adjustPriceOrderId, AuditStatusEnum statusEnum, TokenUserDTO userDTO, Date OperateTime) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        adjustPriceOrder.setAuditStatus((byte)statusEnum.getCode());
        adjustPriceOrder.setUpdatedBy(userDTO.getUserId());
        adjustPriceOrder.setUpdatedByName(userDTO.getName());
        adjustPriceOrder.setGmtUpdate(OperateTime);
        adjustPriceOrderMapper.updateByPrimaryKey(adjustPriceOrder);
    }

    /**
     * 插入调价单标签/门店组
     * @param adjustCode
     * @param orderTabType
     * @param tabTypeValList
     * @param userDTO
     * @param operateTime
     */
    private void insertAdjustOrgTabTypeDetailList(String adjustCode, int orderTabType, List<Long> tabTypeValList, TokenUserDTO userDTO, LocalDateTime operateTime) {
        if (PriceOrderTabTypeEnum.STORE_GROUP.getType() == orderTabType || PriceOrderTabTypeEnum.STORE_FLAG.getType() == orderTabType) {
            if (CollectionUtils.isEmpty(tabTypeValList)) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
            }
            PriceOrderTabTypeDetail priceOrderTabTypeDetail = new PriceOrderTabTypeDetail();
            priceOrderTabTypeDetail.setAdjustOrderCode(adjustCode);
            priceOrderTabTypeDetail.setOrgTabType(orderTabType);
            priceOrderTabTypeDetail.setTabTypeValue(Joiner.on(",").join(tabTypeValList));
            priceOrderTabTypeDetail.setGmtCreate(operateTime);
            priceOrderTabTypeDetail.setGmtUpdate(operateTime);
            priceOrderTabTypeDetailService.insert(priceOrderTabTypeDetail);
        }
    }

    /**
     * 插入调价单组织机构明细
     * @param adjustCode
     * @param orgLevelVOList
     * @param userDTO
     * @param operateTime
     */
    @Override
    public void insertAdjustOrgDetailList(String adjustCode, List<OrgLevelVO> orgLevelVOList, TokenUserDTO userDTO, LocalDateTime operateTime) {
        List<AdjustPriceOrderOrgDetail> orgDetailList = orgLevelVOList.stream().map(orgLevelVO -> {
            AdjustPriceOrderOrgDetail orderOrgDetail = new AdjustPriceOrderOrgDetail();
            orderOrgDetail.setAdjustCode(adjustCode);
            orderOrgDetail.setOrgId(orgLevelVO.getOrgId());
            orderOrgDetail.setOrgLevel(orgLevelVO.getOrgLevel());
            orderOrgDetail.setOrgName(orgLevelVO.getOrgName());
            orderOrgDetail.setGmtCreate(operateTime);
            orderOrgDetail.setGmtUpdate(operateTime);
            return orderOrgDetail;
        }).collect(Collectors.toList());
        Lists.partition(orgDetailList, 200).forEach(orgDetailPartitonList -> adjustPriceOrderOrgDetailService.batchInsert(orgDetailPartitonList));
    }

    /**
     * 插入调价单信息
     * @param adjustCode
     * @param param
     * @param userDTO
     * @param operateTime
     */
    private long insertAdjustPriceOrder(String adjustCode, AdjustPriceOrderV2Param param, TokenUserDTO userDTO, Date operateTime) {
        AdjustPriceOrder adjustPriceOrder = new AdjustPriceOrder();
        adjustPriceOrder.setAdjustCode(adjustCode);
        Long userOrgId = param.getUserOrgId();
        OrgLevelVO orgLevelVO = permissionExtService.getOrgLevelVOByOrgId(userOrgId);
        adjustPriceOrder.setOrgId(orgLevelVO.getOrgId());
        adjustPriceOrder.setOrgName(orgLevelVO.getOrgName());
        adjustPriceOrder.setGmtCreate(operateTime);
        adjustPriceOrder.setGmtUpdate(operateTime);
        adjustPriceOrder.setCreatedBy(userDTO.getUserId());
        adjustPriceOrder.setCreatedByName(userDTO.getName());
        adjustPriceOrder.setUpdatedBy(userDTO.getUserId());
        adjustPriceOrder.setUpdatedByName(userDTO.getName());
        adjustPriceOrder.setStatus((byte)StatusEnum.NORMAL.getCode());
        adjustPriceOrder.setAuditStatus((byte)AuditStatusEnum.IN_PREPARATION.getCode());
        adjustPriceOrder.setEffectStatus(EffectStatusEnum.NO_EFFECT.getCode().byteValue());
        adjustPriceOrder.setAdjustPriceVersion(AdjustPriceVersionEnum.VERSION_2_0.getVersion());
        adjustPriceOrder.setVersion(1);
        if (param.getGoodsCount() != null) {
            adjustPriceOrder.setAdjustGoodsCount(param.getGoodsCount());
        }
        if (null != param.getAuditStatus()) {
            adjustPriceOrder.setAuditStatus(param.getAuditStatus());
        }
        if (null != param.getIsAutoCreatePriceAdjust()) {
            com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
            jsonObject.put("isAutoCreatePriceAdjust", param.getIsAutoCreatePriceAdjust());
            adjustPriceOrder.setExtend(jsonObject.toJSONString());
        }
        adjustPriceOrder = getFromAdjustPriceOrderV2Param(adjustPriceOrder, param, operateTime);
        adjustPriceOrderMapper.insertSelective(adjustPriceOrder);
        return adjustPriceOrder.getId();
    }

    /**
     *
     * @param adjustCode
     * @param orgLevelVOList
     * @param userDTO
     * @param operateTime
     */
    private void updateAdjustOrgDetailList(String adjustCode, List<OrgLevelVO> orgLevelVOList, TokenUserDTO userDTO, LocalDateTime operateTime) {
        adjustPriceOrderOrgDetailService.deleteByAdjustCode(adjustCode);
        insertAdjustOrgDetailList(adjustCode, orgLevelVOList, userDTO, operateTime);
    }

    /**
     * 更新调价单标签/门店组
     * @param adjustCode
     * @param orderTabType
     * @param tabTypeValList
     * @param userDTO
     * @param operateTime
     */
    private void updateAdjustOrgTabTypeDetailList(String adjustCode, int orderTabType, List<Long> tabTypeValList, TokenUserDTO userDTO, LocalDateTime operateTime) {
        priceOrderTabTypeDetailService.deleteByAdjustCode(adjustCode);
        insertAdjustOrgTabTypeDetailList(adjustCode, orderTabType, tabTypeValList, userDTO, operateTime);
    }

    /**
     * 更新调价单信息
     * @param adjustPriceOrder
     * @param param
     * @param userDTO
     * @param operateTime
     */
    private void updateAdjustPriceOrder(AdjustPriceOrder adjustPriceOrder, AdjustPriceOrderV2Param param, TokenUserDTO userDTO, Date operateTime) {
        adjustPriceOrder.setGmtUpdate(operateTime);
        adjustPriceOrder.setUpdatedBy(userDTO.getUserId());
        adjustPriceOrder.setUpdatedByName(userDTO.getUserName());
        adjustPriceOrder.setAuditStatus((byte)AuditStatusEnum.IN_PREPARATION.getCode());
        adjustPriceOrder = getFromAdjustPriceOrderV2Param(adjustPriceOrder, param, operateTime);
        adjustPriceOrderMapper.updateByPrimaryKey(adjustPriceOrder);
    }

    /**
     *  非物理删除调价单明细，把状态打成删除状态
     * @param adjustCode
     * @param userDTO
     * @param operateTime
     */
    private void deleteAdjustPriceOrderDetailsByAdjustCode(String adjustCode, TokenUserDTO userDTO, Date operateTime) {
        adjustPriceOrderDetailV2Service.nonPhysicalDeleteByAdjustCode(adjustCode, userDTO, operateTime);
    }

    /**
     *  非物理删除调价单，把状态打成删除状态
     * @param adjustPriceOrder
     * @param userDTO
     * @param operateTime
     */
    private void deleteAdjustPriceOrder(AdjustPriceOrder adjustPriceOrder, TokenUserDTO userDTO, Date operateTime) {
        adjustPriceOrder.setUpdatedBy(userDTO.getUserId());
        adjustPriceOrder.setUpdatedByName(userDTO.getName());
        adjustPriceOrder.setGmtUpdate(operateTime);
        adjustPriceOrder.setStatus((byte)StatusEnum.DELETE.getCode());
        adjustPriceOrderMapper.updateByPrimaryKey(adjustPriceOrder);
    }

    /**
     * 检查调价单新增/编辑的提交参数
     * @param param
     * @param userDTO
     */
    private void checkEditAdjustPriceOrderV2Param(Optional<AdjustPriceOrder> adjustPriceOrderOptional ,AdjustPriceOrderV2Param param, TokenUserDTO userDTO, boolean isAdd) {
        if (isAdd && param.getId() != null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        } else if (!isAdd && param.getId() == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        if (param.getStep() == PricePageStepEnum.STEP_1.getStep() || param.getStep() == PricePageStepEnum.STEP_12.getStep()) {
            if (StringUtils.isBlank(param.getAdjustName()) ||
                param.getAdjustName().length() > ADJUST_NAME_MAX_LENGTH ||
                param.getChannelIds() == null ||
                param.getPriceTypeCodes() == null ||
                param.getGoodsScope() == null ||
                param.getAdjustType() == null ||
                param.getUserOrgId() == null ||
                (param.getAdjustReason() != null && param.getAdjustReason().length() > ADJUST_REASON_MAX_LENGTH)
            ) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
            }
            if (CollectionUtils.isEmpty(param.getOrgIdList())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "请选择组织机构");
            } else if (param.getOrgTabType() == null){
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
            }
            checkAdjustName(adjustPriceOrderOptional, param.getAdjustName());
            if(!getB2CChannelIdList().contains(param.getChannelIds())) {
            	permissionExtService.checkUserOrgPermissions(userDTO.getUserId(), param.getResourceId(), param.getOrgIdList());
                basePriceOrderService.checkUserPriceDimensiones(userDTO.getUserId(), param.getResourceId(), param.getPriceTypeCodeList(), param.getChannelIdList(), param.getGoodsScope());
            }
        }
        if (param.getStep() == PricePageStepEnum.STEP_2.getStep()  || param.getStep() == PricePageStepEnum.STEP_12.getStep()) {
            if (param.getExecStatus() == null || (param.getScheduledTimeSecondLong() == null && param.getExecStatus() == ExecEnum.EXEC_PLAN.getCode())) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
            }
            Long scheduledTimeSecondLong = StringUtils.isBlank(param.getScheduledTimeSecondLong()) ? null : Long.parseLong(param.getScheduledTimeSecondLong());
            if (param.getExecStatus() == ExecEnum.EXEC_PLAN.getCode() && !basePriceOrderService.isAfterAndEqualToday(scheduledTimeSecondLong)) {
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "计划生效日期必须大于等于今天");
            }
        }
        if (!PricePageStepEnum.getEnumByStep(param.getStep()).isPresent()) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
    }

    /**
     * 根据调价单单入参返回调价单对象
     * @param adjustPriceOrder
     * @param param
     * @return
     */
    private  AdjustPriceOrder getFromAdjustPriceOrderV2Param(AdjustPriceOrder adjustPriceOrder, AdjustPriceOrderV2Param param, Date operateTime) {
        if (param.getStep() == PricePageStepEnum.STEP_1.getStep() || param.getStep() == PricePageStepEnum.STEP_12.getStep()) {
            adjustPriceOrder.setAdjustName(param.getAdjustName());
            adjustPriceOrder.setChannel(Joiner.on(",").join(param.getChannelIdList()));
            adjustPriceOrder.setAdjustPriceType(Joiner.on(",").join(param.getPriceTypeCodeList()));
            adjustPriceOrder.setGoodsScope(param.getGoodsScope());
            adjustPriceOrder.setAdjustType(param.getAdjustType().byteValue());
            adjustPriceOrder.setReason(param.getAdjustReason());
            adjustPriceOrder.setAdjustOrgTabType(param.getOrgTabType());
        }
        if (param.getStep() == PricePageStepEnum.STEP_2.getStep() || param.getStep() == PricePageStepEnum.STEP_12.getStep())  {
            adjustPriceOrder.setExecStatus(param.getExecStatus() != null ?
                param.getExecStatus().byteValue() : (byte)ExecEnum.EXEC_PLAN.getCode());
            if (param.getExecStatus() != null) {
                if (param.getExecStatus().intValue() == ExecEnum.EXEC_NOW.getCode()) {
                    adjustPriceOrder.setExecStatus((byte)ExecEnum.EXEC_NOW.getCode());
                    adjustPriceOrder.setScheduledTime(operateTime);
                } else {
                    adjustPriceOrder.setExecStatus((byte)ExecEnum.EXEC_PLAN.getCode());
                    adjustPriceOrder.setScheduledTime(AmisTimeUtil.getDateFromDateTime(param.getScheduledTimeSecondLong()));
                }
                //扩展字段增加生效方式用于回显
                String extend = adjustPriceOrder.getExtend();
                Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(extend);
                if(instance.isPresent()) {
                    AdjustPriceOrderExtend adjustPriceOrderExtend = instance.get();
                    adjustPriceOrderExtend.setEffectMode(adjustPriceOrder.getExecStatus().intValue());
                    adjustPriceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(adjustPriceOrderExtend));
                }else {
                    adjustPriceOrder.setExtend(AdjustPriceOrderExtend.getJSONFormatStr(adjustPriceOrder.getExecStatus().intValue()));
                }
            }
        }
        return adjustPriceOrder;
    }

    /**
     * 根据用户选择的组织机构及其权限判断用户实际所选组织
     * ex：假如用户拥有价格中台的西南平台权限，则直接返回西南平台组织ID；假如没有西南平台权限，且选择西南平台，则把西南平台转化为西南平台下所拥有的子节点ID列表
     * @param userId
     * @param resourceId
     * @param orgIdList
     * @return
     */
    @Override
    public List<OrgLevelVO> getUserActualSelectOrgDTOList(long userId, long resourceId, List<Long> orgIdList) {
        Set<Long> resultOrgIdSet = new HashSet<>(orgIdList.size());
        List<Long> userOrgIdList = permissionExtService.findIsFullScopeSonOrgIdList(userId, orgIdList);
        for (Long orgId : orgIdList) {
            //假如拥有权限，就直接添加，不拥有就添加拥有权限的子节点
            if (userOrgIdList.contains(orgId)) {
                resultOrgIdSet.add(orgId);
            } else {
                List<Long> childUserOrgIdList = permissionExtService.findIsFullScopeSonOrgIdList(userId, Lists.newArrayList(orgId));
                resultOrgIdSet.addAll(childUserOrgIdList);
            }
        }
        if (resultOrgIdSet.isEmpty()) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.getUserActualSelectOrgDTOList] orgIdList: {} 选择的组织及其子组织都没有权限。",
                StringUtils.join(orgIdList, ","));;
            throw new AmisBadRequestException(ReturnCodeEnum.SYSTEM_ERROR, "选择的组织及其子组织都没有权限。");
        }
        return listOrgInfoByIds(resultOrgIdSet);
    }

    /**
     * 根据组织ID列表获取组织信息列表
     * @param orgIdSet
     * @return
     */
    private List<OrgLevelVO> listOrgInfoByIds(Set<Long> orgIdSet) {
        Map<Long, OrgLevelVO> orgLevelVOMap = Collections.emptyMap();
        try {
            orgLevelVOMap = permissionExtService.listPermissionByOrgIds(Lists.newArrayList(orgIdSet));
        } catch (BusinessException e) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.listOrgInfoByIds] error: {}", e.getErrorMessage());
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        if (orgLevelVOMap.size() != orgIdSet.size()) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.listOrgInfoByIds] : {}");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        return Lists.newArrayList(orgLevelVOMap.values());
    }

    /**
     * 根据AdjustPriceOrder获取VO，把渠道列表，价格类型都转成List
     * @param adjustPriceOrder
     * @return
     */
    private AdjustPriceOrderV2VO getVOFromAdjustPriceOrderAndOrgCount(AdjustPriceOrder adjustPriceOrder, TokenUserDTO userDTO, boolean isSetOrgIdList) {
        Long orgCount = adjustPriceOrderOrgDetailService.countByAdjustCode(adjustPriceOrder.getAdjustCode());

        AdjustPriceOrderOrgDetailVO adjustPriceOrderOrgDetailVO = listAdjustPriceOrderOrgDetailList(adjustPriceOrder.getId(), userDTO);

        AdjustPriceOrderV2VO adjustPriceOrderV2VO = new AdjustPriceOrderV2VO();
        if (adjustPriceOrder.getChannel() != null) {
            List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(adjustPriceOrder.getChannel());
            adjustPriceOrderV2VO.setChannelNames(getChannelNames(channelIdList));
            adjustPriceOrderV2VO.setChannelIds(adjustPriceOrder.getChannel());
        }
        if (adjustPriceOrder.getAdjustPriceType() != null) {
            List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());
            adjustPriceOrderV2VO.setPriceTypeNames(getPriceTypeNames(priceTypeCodeList));
            adjustPriceOrderV2VO.setPriceTypeCodes(adjustPriceOrder.getAdjustPriceType());
        }
        adjustPriceOrderV2VO.setId(adjustPriceOrder.getId());
        adjustPriceOrderV2VO.setAdjustCode(adjustPriceOrder.getAdjustCode());
        adjustPriceOrderV2VO.setAdjustName(adjustPriceOrder.getAdjustName());
        adjustPriceOrderV2VO.setGoodsScope(adjustPriceOrder.getGoodsScope());
        if (adjustPriceOrder.getGoodsScope() != null) {
            PriceDictionaryDTO dictionaryDTO = priceDictionaryService.getByCode(DictCodeEnum.GOODS_SCOPE.getCode(), adjustPriceOrder.getGoodsScope()+"");
            adjustPriceOrderV2VO.setGoodsScopeName(Optional.ofNullable(dictionaryDTO).map(PriceDictionaryDTO::getDictName).orElse(""));
        }
        if (adjustPriceOrder.getAuditStatus() != null) {
            int auditStatus = adjustPriceOrder.getAuditStatus().intValue();
            adjustPriceOrderV2VO.setAuditStatus(auditStatus);
            adjustPriceOrderV2VO.setAuditStatusName(AuditStatusEnum.getName(auditStatus));
        }
        if (adjustPriceOrder.getAdjustType() != null) {
            adjustPriceOrderV2VO.setAdjustType(adjustPriceOrder.getAdjustType().intValue());
            adjustPriceOrderV2VO.setAdjustTypeName(AdjustTypeEnum.getNameV2(adjustPriceOrder.getAdjustType()));
        }
        adjustPriceOrderV2VO.setAdjustReason(adjustPriceOrder.getReason());
        if (adjustPriceOrder.getScheduledTime() != null) {
            adjustPriceOrderV2VO.setScheduledTime(DateUtils.dateToString(adjustPriceOrder.getScheduledTime()));
        }
        if (adjustPriceOrder.getExecStatus() != null) {
            int execStatus = adjustPriceOrder.getExecStatus().intValue();
            adjustPriceOrderV2VO.setExecStatus(execStatus);
            adjustPriceOrderV2VO.setExecStatusName(ExecEnum.getName(execStatus));
        }
        adjustPriceOrderV2VO.setOrgTabType(adjustPriceOrder.getAdjustOrgTabType());
        adjustPriceOrderV2VO.setOrgCount(orgCount);
        if (isSetOrgIdList) {
            adjustPriceOrderV2VO.setOrgIdList(adjustPriceOrderOrgDetailVO.getOrgIdList());
            adjustPriceOrderV2VO.setTabTypeValList(adjustPriceOrderOrgDetailVO.getTabTypeValList());
            adjustPriceOrderV2VO.setOrgNames(adjustPriceOrderOrgDetailVO.getOrgNames());
        }

        if (adjustPriceOrder.getGmtCreate() != null) {
            adjustPriceOrderV2VO.setCreateTime(DateUtils.dateToString(adjustPriceOrder.getGmtCreate()));
        }
        adjustPriceOrderV2VO.setCreateUserName(adjustPriceOrder.getCreatedByName());
        if (adjustPriceOrder.getScheduledTime() != null) {
            adjustPriceOrderV2VO.setScheduledTime(DateUtils.dateToString(adjustPriceOrder.getScheduledTime(), DateUtils.DATETIME_FORMAT));
            adjustPriceOrderV2VO.setScheduledTimeSecondLong(AmisTimeUtil.getSecondFromDate(adjustPriceOrder.getScheduledTime()));
        }
        if (adjustPriceOrder.getEffectTime() != null) {
            adjustPriceOrderV2VO.setEffectTime(DateUtils.dateToString(adjustPriceOrder.getEffectTime(), DateUtils.DATETIME_FORMAT));
        }
        if (userDTO != null && null != userDTO.getUserId()) {
            adjustPriceOrderV2VO.setIsOwner(userDTO.getUserId().equals(adjustPriceOrder.getCreatedBy()));
        }
        adjustPriceOrderV2VO.setEffectStatus(adjustPriceOrder.getEffectStatus());
        adjustPriceOrderV2VO.setEffectStatusName(getEffectStatusName(adjustPriceOrder.getEffectStatus()));
        adjustPriceOrderV2VO.setIsCanImmediatelyExecute(isCanImmediatelyExecute(adjustPriceOrder));
        adjustPriceOrderV2VO.setAdjustGoodsCount(adjustPriceOrder.getAdjustGoodsCount());

        if (adjustPriceOrder.getOfflineEffectStatus() != null) {
            int offlineEffectStatus = adjustPriceOrder.getOfflineEffectStatus().intValue();
            adjustPriceOrderV2VO.setOfflineEffectStatus(offlineEffectStatus);
            adjustPriceOrderV2VO.setOfflineEffectStatusName(AdjustPriceOrderOfflineEffectStatusEnum.getName(offlineEffectStatus));
            if(offlineEffectStatus==AdjustPriceOrderOfflineEffectStatusEnum.EFFECT_WAIT.getCode()) {
            	adjustPriceOrderV2VO.setOfflineEffectStatusName("");
            }
        }
        Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(adjustPriceOrder.getExtend());
        if(instance.isPresent()) {
        	AdjustPriceOrderExtend adjustPriceOrderExtend = instance.get();
        	if(null==adjustPriceOrderExtend.getEffectMode()) {
        		adjustPriceOrderV2VO.setEffectMode(ExecEnum.EXEC_PLAN.getCode());
            	adjustPriceOrderV2VO.setEffectModeName(ExecEnum.EXEC_PLAN.getMessage());
        	}else {
        		adjustPriceOrderV2VO.setEffectMode(adjustPriceOrderExtend.getEffectMode());
            	adjustPriceOrderV2VO.setEffectModeName(ExecEnum.getName(adjustPriceOrderV2VO.getEffectMode()));
        	}
        }else {
        	adjustPriceOrderV2VO.setEffectMode(ExecEnum.EXEC_PLAN.getCode());
        	adjustPriceOrderV2VO.setEffectModeName(ExecEnum.EXEC_PLAN.getMessage());
        }
        adjustPriceOrderV2VO.setExtend(adjustPriceOrder.getExtend());
        int detailCount = adjustPriceOrderDetailExMapper.countOfGoodsByAdjustCode(adjustPriceOrder.getAdjustCode());
        adjustPriceOrderV2VO.setIsChangeOrg(detailCount>0?true:false);
        return adjustPriceOrderV2VO;
    }

    /**
     * 前端展示未生效状态：对应中台未生效、预生效执行中、预生效状态。
     * 前端展示已生效状态：对应中台已生效。
     * @param effectStatus
     * @return
     */
    private String getEffectStatusName(Byte effectStatus) {
    	String effectStatusName = EffectStatusEnum.getName(effectStatus);
    	if(StringUtils.isNotBlank(effectStatusName)) {
    		return effectStatusName;
    	}
    	return "未知状态";
    }

    /**
     * 调价单是否能立即值价 审核通过且审核通过时间大于等于预计生效时间
     * @param adjustPriceOrder
     * @return
     */
    private Boolean isCanImmediatelyExecute(AdjustPriceOrder adjustPriceOrder) {
        Boolean result = false;
        if (adjustPriceOrder.getAuditStatus() == AuditStatusEnum.AUDIT_PASS.getCode() &&
            adjustPriceOrder.getEffectStatus().intValue() == EffectStatusEnum.PRE_EFFECTED.getCode() &&
            adjustPriceOrder.getExecStatus() == ExecEnum.EXEC_PLAN.getCode() &&
            (   adjustPriceOrder.getAuditDate() != null  &&
                adjustPriceOrder.getScheduledTime() != null &&
                adjustPriceOrder.getAuditDate().compareTo(adjustPriceOrder.getScheduledTime()) >=0
            )
        ) {
            result = true;
        }
        return result;
    }

    /**
     * 根据价格类型Code列表获取价格类型名称列表
     * @param priceTypeCodeList
     * @return
     */
    private String getPriceTypeNames(List<String> priceTypeCodeList) {
        return priceTypeService.getPriceTypeListByCodes(priceTypeCodeList).stream().
            map(PriceType::getName).collect(Collectors.joining(PriceConstant.NAME_SEPARATOR));
    }

    /**
     * 根据channelId列表获取channelName列表
     * @param channelIdList
     * @return
     */
    private String getChannelNames(List<Integer> channelIdList) {
        return priceChannelService.getPriceChannelListByChannelIds(channelIdList).stream().
            map(PriceChannel::getChannelName).collect(Collectors.joining(PriceConstant.NAME_SEPARATOR));
    }


    /**
     * 查看调价单名字是否已经存在,不包含自己本身，存在则抛出异常
     * @param adjustName
     */
    private void checkAdjustName(Optional<AdjustPriceOrder> adjustPriceOrderOptional, String adjustName) {
        AdjustPriceOrderExample adjustPriceOrderExample = new AdjustPriceOrderExample();
        adjustPriceOrderExample.setLimit(1);
        adjustPriceOrderExample.createCriteria().andAdjustNameEqualTo(adjustName)
            .andStatusEqualTo((byte)StatusEnum.NORMAL.getCode());
        List<AdjustPriceOrder> adjustPriceOrderList = adjustPriceOrderMapper.selectByExample(adjustPriceOrderExample);
        if (CollectionUtils.isNotEmpty(adjustPriceOrderList)) {
            AdjustPriceOrder adjustPriceOrder = adjustPriceOrderList.get(0);
            if (adjustName.equals(adjustPriceOrder.getAdjustName())) {
                if (adjustPriceOrderOptional.isPresent()) {
                    if (!adjustPriceOrderOptional.get().getId().equals(adjustPriceOrder.getId())) {
                        throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "调价单名称已经存在");
                    }
                } else {
                    throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "调价单名称已经存在");
                }
            }
        }
    }

    /**
     * 过滤需要trim的字段
     * @param param
     * @return
     */
    private AdjustPriceOrderV2Param trimAdjustPriceOrderV2Param(AdjustPriceOrderV2Param param) {
        param.setAdjustName(StringUtils.trimToNull(param.getAdjustName()));
        param.setAdjustReason(StringUtils.trimToNull(param.getAdjustReason()));
        return param;
    }

    /**
     * 把Amis的check的值（逗号分隔换成List对象）
     * @param param
     * @return
     */
    private AdjustPriceOrderV2Param parseAmisCheckValues(AdjustPriceOrderV2Param param) {
        param.setChannelIdList(Splitter.on(",").splitToList(param.getChannelIds()).stream().map(Integer::valueOf).collect(Collectors.toList()));
        param.setPriceTypeCodeList(Splitter.on(",").splitToList(param.getPriceTypeCodes()));
        return param;
    }

    @Override
	public List<OptionDto> getAdjustTypeList(Long adjustPriceOrderId) {
		List<OptionDto> optionDtoList = Arrays.stream(AdjustTypeEnum.values())
	            .map(typeEnum -> new OptionDto(typeEnum.getMessageV2(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
		Integer adjustType = null;
		boolean isRemove = true;
		if(null!=adjustPriceOrderId) {
			if(adjustPriceOrderId == NO_REMOVE_SUPER_CONTROL_TYPE) {
				isRemove = false;
			}else {
				AdjustPriceOrder priceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
				if(null!=priceOrder) {
					adjustType = Integer.valueOf(priceOrder.getAdjustType());
					if(adjustType==AdjustTypeEnum.SUPER_CONTROL.getCode()) {
						isRemove = false;
					}
				}
			}
		}
		if(isRemove && CollectionUtils.isNotEmpty(optionDtoList)) {
			Iterator<OptionDto> iterator = optionDtoList.iterator();
			while(iterator.hasNext()) {
				OptionDto optionDto = iterator.next();
				Integer superControl = Integer.valueOf(optionDto.getValue());
				if(superControl==AdjustTypeEnum.SUPER_CONTROL.getCode()) {
					iterator.remove();
				}
			}
		}
		return optionDtoList;
	}

	@Override
	//@Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
	public void saveOrgStoreDetail(String adjustCode) {
		AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
        if (Objects.isNull(adjustPriceOrder)) {
        	logger.error("AdjustPriceOrderV2ServiceImpl|saveOrgStoreDetail|adjustCode:{},未查到调价单主表信息",adjustCode);
        	throw new BusinessErrorException("参数异常");
        }
        Date operateTime = new Date();
        //根据价格类型和渠道的配置复制数据
        List<AdjustPriceOrderDetail> copyOrderDetailList = adjustPriceOrderDetailV2Service.copyByAdjustPriceTypeAndChannelCopyList(adjustPriceOrder.getId());
        // 构建重试key
        String retryKey = buildRetryKey(adjustCode);
        RAtomicLong retryCounter = redissonClient.getAtomicLong(retryKey);
        try {
            // 执行主业务逻辑
            splitAdjustOrgToStore(adjustPriceOrder, copyOrderDetailList, operateTime);
            // 发送补充消息
            sendSupplementMessages(adjustPriceOrder);
        } catch (Exception e) {
            handleRetryLogic(adjustPriceOrder, retryCounter, e);
        }
	}

	private String buildRetryKey(String adjustCode) {
        return RedisKeysConstant.PROJECT_NAME +
               RedisKeysConstant.ORG_STORE_RETRY_COUNT_KEY +
               ":" + adjustCode;
    }

	private void sendSupplementMessages(AdjustPriceOrder adjustPriceOrder) {
        adjustPriceOrder.setDelayTimeLevel(DelayTimeLevel.SECOND_10.getLevel());
        adjustPriceOrder.setDelayExecBiz(MqDelayExecBizEnum.SUPPLEMENT_ORG_STORE_EXTEND.getCode());
        sendSupplementExtend(adjustPriceOrder);
        sendMakePriceSuggestTask(adjustPriceOrder);
    }

	private void handleRetryLogic(AdjustPriceOrder adjustPriceOrder,RAtomicLong retryCounter, Exception e) {
	        logger.error("调价单组织门店明细处理异常: adjustCode={}", adjustPriceOrder.getAdjustCode(), e);
	        long retryCount = retryCounter.incrementAndGet();
	        retryCounter.expire(Constants.EXPIRE_MINUTE_30, TimeUnit.MINUTES);
	        // 重试逻辑
	        if (retryCount <= orgStoreReTryCount) {
	            adjustPriceOrder.setDelayTimeLevel(DelayTimeLevel.MINUTE_2.getLevel());
	            adjustPriceOrder.setDelayExecBiz(MqDelayExecBizEnum.CREATE_ORG_STORE_DETAIL.getCode());
	            adjustPriceOrderOrgStoreDetailExtendProducer.sendMq(adjustPriceOrder);
	            logger.info("AdjustPriceOrderV2ServiceImpl|handleRetryLogic|adjustCode:{},异常重试第:{}次",adjustPriceOrder.getAdjustCode(),retryCount);
	        } else {
	            // 发送告警消息
	            sendAlarmMessage(adjustPriceOrder.getAdjustCode(),retryCount);
	        }
	    }
	private void sendAlarmMessage(String adjustCode,long retryCount) {
        String errorMsg = "调价单号:" + adjustCode + ",生成调价单组织门店明细数据异常";
        String message = WeChatMessage.createMarkdownMessage(createOrgStoreErrorMsgBody(errorMsg));
        HttpUtil.post(orgStoreErrorWechatUrl, message);
        logger.info("AdjustPriceOrderV2ServiceImpl|sendAlarmMessage|adjustCode:{},异常重试第:{}次发送企业微信告警",adjustCode,retryCount);
    }

	private void sendMakePriceSuggestTask(AdjustPriceOrder adjustPriceOrder) {
		makePriceSuggestTaskResultProducer.sendMq(adjustPriceOrder);
	}

    private String createOrgStoreErrorMsgBody(String description) {
		String content = "【生成调价单组织门店明细数据异常通知】\n" +
				">集合名称：<font color=\\\"info\\\">价格中台</font>\n " +
                ">告警名称：<font color=\\\"info\\\">生成调价单组织门店明细数据异常统计</font>\n " +
                ">时间：<font color=\\\"info\\\">" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()) + "</font>\n" +
                ">异常描述：<font color=\\\"warning\\\">" + description +"</font>\n ";
		return content;
	}

	@Override
	public AdjustPriceOrder selectByAdjustCode(String adjustCode) {
		return adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
	}

	@Override
	public int updateByExampleSelective(AdjustPriceOrder record, AdjustPriceOrderExample example) {
		return adjustPriceOrderMapper.updateByExampleSelective(record, example);
	}

	@Override
	public void afreshEffectAdjustPrice(String adjustCode) {
		//设置重试执价状态
		AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustCode);
		if(null==adjustPriceOrder) {
			return;
		}
		AdjustPriceOrder priceOrder = new AdjustPriceOrder();
		Optional<AdjustPriceOrderExtend> orderExtend = AdjustPriceOrderExtend.getInstance(adjustPriceOrder.getExtend());
		AdjustPriceOrderExtend adjustPriceOrderExtend = null;
		if(orderExtend.isPresent()) {
			adjustPriceOrderExtend = orderExtend.get();
			adjustPriceOrderExtend.setAfreshAdjustPriceStatus(true);
		}else {
			adjustPriceOrderExtend = new AdjustPriceOrderExtend();
			adjustPriceOrderExtend.setAfreshAdjustPriceStatus(true);
		}
		priceOrder.setExtend(AdjustPriceOrderExtend.toJSONFormatStr(adjustPriceOrderExtend));
		priceOrder.setId(adjustPriceOrder.getId());
		adjustPriceOrderMapper.updateByPrimaryKeySelective(priceOrder);

		AdjustPriceOrderOrgStoreDetailExample example = new AdjustPriceOrderOrgStoreDetailExample();
		example.createCriteria().andAdjustCodeEqualTo(adjustCode);
		long count = adjustPriceOrderOrgStoreDetailService.countByExample(example);
		if(count>0) {
			adjustPriceOrderDetailService.distributePreEffectAdjustPriceOrder(adjustCode);
		}else {
			priceOrderV2Service.saveOrgStoreDetail(adjustCode);
		}
	}

	@Override
	public void batchCacheInitNewStorePrice(InitNewStorePriceHeadDTO head) {
		try {
			String cacheKey = RedisKeysConstant.getInitNewStoreAdjustPriceImportResultKey(head.getDownloadCode(),head.getStoreNo(),head.getPage());
			RBucket<List<InitNewStorePriceDTO>> initNewStoreCache = redissonClient.getBucket(cacheKey);
			initNewStoreCache.set(head.getInitDataList(), 30, TimeUnit.MINUTES);
		} catch (Exception e) {
			saveImportResult(head.getDownloadCode(), ImportStatusEnum.IMPORT_FAIL.getCode(), 0, 0, 0, "");
			logger.error("AdjustPriceOrderV2ServiceImpl|batchCacheInitNewStorePrice|异常",e);
			throw new BadRequestAlertException(ReturnCodeEnum.SYSTEM_ERROR.getMessage(), AdjustPriceOrderResource.ENTITY_NAME, ReturnCodeEnum.SYSTEM_ERROR.getCode().toString());
		}
	}

	@Override
	public void initNewStorePriceForCache(List<DownloadParamDTO> downloadCodePageList) {
		String downloadCode = downloadCodePageList.get(0).getDownloadCode();
		Long userId = downloadCodePageList.get(0).getUserId();
		String userName = downloadCodePageList.get(0).getUserName();
		String storeNo = downloadCodePageList.get(0).getStoreNo();
		TokenUserDTO tokenUserDTO = new TokenUserDTO();
		tokenUserDTO.setUserId(userId);
		tokenUserDTO.setUserName(userName);
		List<InitNewStorePriceDTO>  initNewStorePriceAll = Lists.newArrayList();
		Integer count = 0;
		try {
			for (DownloadParamDTO param : downloadCodePageList) {
				String cacheKey = RedisKeysConstant.getInitNewStoreAdjustPriceImportResultKey(downloadCode,storeNo,param.getPage());
				RBucket<List<InitNewStorePriceDTO>> initNewStoreCache = redissonClient.getBucket(cacheKey);
				if(!initNewStoreCache.isExists()) {
					logger.info("AdjustPriceOrderV2ServiceImpl|initNewStorePriceForCache|cacheKey:{},没有在redis获取到数据",cacheKey);
					continue;
				}
			    initNewStorePriceAll.addAll(initNewStoreCache.get());
			    //验证完每一页之后 删除缓存数据
			    initNewStoreCache.delete();
			}
			count = initNewStorePriceAll.size();
			logger.info("自动创建调价单......key:{},storeNo:{}",downloadCode,storeNo);
			initNewStorePriceAll = initNewStorePriceAll.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(tc -> tc.getGoodsNo()))), ArrayList::new));
			SaveAdjustPriceOrderDTO buildAdjustPriceOrder = buildAdjustPriceOrder(initNewStorePriceAll,tokenUserDTO);
			initNewStorePriceToDb(buildAdjustPriceOrder,tokenUserDTO);
			processStoreData(downloadCode, storeNo, count, true);
		} catch (Exception e) {
			processStoreData(downloadCode, storeNo, count, false);
			//saveImportResult(downloadCode, ImportStatusEnum.IMPORT_FAIL.getCode(), 0, 0, 0);
			logger.error("AdjustPriceOrderV2ServiceImpl|initNewStorePriceForCache|异常",e);
		}
	}

	private void saveImportResult(String downloadCode,int status, int total,int successCount,int failCount,String msg) {
		AdjustImportResultDTO importResult = new AdjustImportResultDTO();
		importResult.setTotal(total);
		importResult.setSuccessCount(successCount);
		importResult.setFailCount(failCount);
		importResult.setStatus(status);
		importResult.setUuid(downloadCode);
		importResult.setMsg(msg);
		updateImportResult(importResult);
	}

	private Map<String, SpuListVO> getSpuList(List<String> goodsNoList){
		try {
			Thread.sleep(20L);
		} catch (InterruptedException e) {
			logger.error("休眠失败",e);
		}
		SpuQueryParamVO paramVo = new SpuQueryParamVO();
        paramVo.setGoodsNoList(goodsNoList);
        paramVo.setPage(1);
        paramVo.setPageSize(goodsNoList.size());
        com.cowell.pricecenter.service.dto.response.PageResult<SpuListVO> pageResponse = itemSearchEngineFacadeService.getSpuList(paramVo);
	    if(null!=pageResponse && CollectionUtils.isNotEmpty(pageResponse.getRows())) {
	    	List<SpuListVO> result = pageResponse.getRows();
    		return result.stream().collect(Collectors.toMap(SpuListVO::getGoodsNo, Function.identity(), (v1, v2) -> v1));
	    }
	    return Maps.newHashMap();
	}

	/**
	 * 封装调价单数据
	 * @param
	 * @return
	 */
	private SaveAdjustPriceOrderDTO buildAdjustPriceOrder(List<InitNewStorePriceDTO> importList,TokenUserDTO tokenUserDTO) {
		List<String> priceTypeList = Lists.newArrayList(PriceTypeModeEnum.LSJ.getPriceTypeCode(),PriceTypeModeEnum.HYJ.getPriceTypeCode(),PriceTypeModeEnum.CLJ.getPriceTypeCode());
		SaveAdjustPriceOrderDTO priceOrder = new SaveAdjustPriceOrderDTO();
		priceOrder.setOrgId(importList.get(0).getStoreOrgId());
		priceOrder.setChannel(Constants.DEFAULT_CHANNEL);
		priceOrder.setAdjustName("价格初始化"+DateUtils.dateToString(new Date(), DateUtils.TIME_FORMAT));
		priceOrder.setAdjustPriceType(StringUtils.join(priceTypeList, ","));
		priceOrder.setUserName(tokenUserDTO.getUserName());
		priceOrder.setUserId(tokenUserDTO.getUserId());
		priceOrder.setExecStatus((byte)ExecEnum.EXEC_NOW.getCode());
		priceOrder.setScheduledTime(DateUtils.getTodayYYYYMMDD_HHMMSS());
		priceOrder.setAdjustType((byte)AdjustTypeEnum.INIT_NEWSTORE_PRICE.getCode());
		priceOrder.setReason("价格初始化");
		priceOrder.setNoSequenceType(NoSequenceTypeEnum.INIT_NEWSTORE_PRICE_ADJUST.getKey());
		List<SaveAdjustPriceOrderDetailDTO> saveDetailList = new ArrayList<SaveAdjustPriceOrderDetailDTO>();
		SaveAdjustPriceOrderDetailDTO orderDetail = null;
		for (InitNewStorePriceDTO detail : importList) {
			orderDetail = new SaveAdjustPriceOrderDetailDTO();
			BeanUtils.copyProperties(detail, orderDetail);
			orderDetail.setOrgIds(detail.getStoreOrgId().toString());
            orderDetail.setOrgNames(detail.getOrgNames());
            orderDetail.setOrgLevels(detail.getOrgLevels());
			orderDetail.setPrice(BigDecimalUtils.convertFenByYuan(detail.getLsjPrice()));
			orderDetail.setPriceTypeCode(PriceTypeModeEnum.LSJ.getPriceTypeCode());
			orderDetail.setSkuId(0L);
			orderDetail.setSpuId(0L);
			orderDetail.setReason(NEWSTORE_ADJUSTPRICE_REASON);
			saveDetailList.add(orderDetail);

			orderDetail = new SaveAdjustPriceOrderDetailDTO();
			BeanUtils.copyProperties(detail, orderDetail);
			orderDetail.setOrgIds(detail.getStoreOrgId().toString());
            orderDetail.setOrgNames(detail.getOrgNames());
            orderDetail.setOrgLevels(detail.getOrgLevels());
			orderDetail.setPrice(BigDecimalUtils.convertFenByYuan(detail.getHyjPrice()));
			orderDetail.setPriceTypeCode(PriceTypeModeEnum.HYJ.getPriceTypeCode());
			orderDetail.setSkuId(0L);
			orderDetail.setSpuId(0L);
			orderDetail.setReason(NEWSTORE_ADJUSTPRICE_REASON);
			saveDetailList.add(orderDetail);

			orderDetail = new SaveAdjustPriceOrderDetailDTO();
			BeanUtils.copyProperties(detail, orderDetail);
			orderDetail.setOrgIds(detail.getStoreOrgId().toString());
            orderDetail.setOrgNames(detail.getOrgNames());
            orderDetail.setOrgLevels(detail.getOrgLevels());
			orderDetail.setPrice(BigDecimalUtils.convertFenByYuan(detail.getClsjPrice()));
			orderDetail.setPriceTypeCode(PriceTypeModeEnum.CLJ.getPriceTypeCode());
			orderDetail.setSkuId(0L);
			orderDetail.setSpuId(0L);
			orderDetail.setReason(NEWSTORE_ADJUSTPRICE_REASON);
			saveDetailList.add(orderDetail);
		}
		priceOrder.setDetailList(saveDetailList);
		return priceOrder;
	}

	@Override
	public void initNewStorePriceToDb(SaveAdjustPriceOrderDTO param,TokenUserDTO tokenUserDTO) {
		CheckAdjustPriceOrderUtil.checkSaveAdjustPriceOrder(param);
        CheckAdjustPriceOrderUtil.checkSaveAdjustPriceOrderType(param.getAdjustType(), allowAdjustType);
		try {
			adjustPriceOrderService.saveAdjustPriceOrderNoTrn(param);
			/**
			AuditAdjustPriceOrderDTO auditOrder = new AuditAdjustPriceOrderDTO();
			auditOrder.setAuditStatus((byte)AuditStatusEnum.AUDIT_PASS.getCode());
			auditOrder.setAdjustCode(adjustCode);
			auditOrder.setUserName(tokenUserDTO.getUserName());
			auditOrder.setUserId(tokenUserDTO.getUserId());
			CheckAdjustPriceOrderUtil.checkAuditAdjustPriceOrder(auditOrder);
			ReturnCodeEnum audit = adjustPriceOrderService.audit(auditOrder);
			if(!audit.getCode().equals(ReturnCodeEnum.SUCCESS.getCode())) {
	        	logger.info("initNewStorePriceToDb|自动创建调价单审批流程异常|audit:{}",JSON.toJSONString(audit));
	        	throw new RuntimeException("新店初始化价格自动创建调价单流程异常");
	        }
	        */
		} catch (Exception e) {
			//手动回滚数据
			String adjustCode = param.getAdjustCode();
			if(StringUtils.isNotBlank(adjustCode)) {
				AdjustPriceOrderExample example = new AdjustPriceOrderExample();
				example.createCriteria().andAdjustCodeEqualTo(adjustCode);
				int delCount = adjustPriceOrderMapper.deleteByExample(example);
				if(delCount>0) {
					adjustPriceOrderOrgDetailService.deleteByAdjustCode(adjustCode);
					//循环删除调价单明细
					while (true){
			            int count = adjustPriceOrderDetailExMapper.deleteBatch(Constants.BATCH_DEL_SIZE, adjustCode);
			            if(count <= 0){
			               break;
			            }
			        }
				}
			}
			logger.info("initNewStorePriceToDb|新店初始化价格自动创建调价单异常",e);
			throw new RuntimeException("新店初始化价格自动创建调价单异常");
		}
	}

	@Override
	public AdjustImportResultDTO getAdjustGoodsWarnList(Long adjustId) {
    	AdjustImportResultDTO importResult = new AdjustImportResultDTO();
    	String cacheKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.GOODS_WARN+adjustId;
		RBucket<AdjustImportResultDTO> bucket = redissonClient.getBucket(cacheKey);
		if(bucket.isExists()) {
    		importResult = bucket.get();
    	}
		return importResult;
	}

	@Override
	public List<OrgTreeDTO> getAbsScopeOrgTrees(OrgParam param,TokenUserDTO userDTO) {
		List<OrgTreeDTO> orgList = Lists.newArrayList();
		if(param.getOrgBizCode()==OrgBizCodeEnum.GENERAL_ORG_LIST.getCode() && CollectionUtils.isNotEmpty(param.getTypes())){
			Set<Integer> typeSet = new HashSet<Integer>(param.getTypes());
			return permissionExtService.getOrgTreeByUser(userDTO.getUserId(),RequestHeaderContextUtils.getResourceId(), typeSet);
		}
		if(null == param.getChannelId()) {
			throw new BusinessErrorException("渠道id不能为空");
		}
		List<OrgDTO> userDataScopeOrgList = permissionExtService.getUserDataScopeOrgByType(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), OrgTypeEnum.BUSINESS.getCode(), null);
        if (CollectionUtils.isEmpty(userDataScopeOrgList)) {
        	logger.warn("对不起,您没有门店数据权限");
        	throw new BusinessErrorException("对不起,您没有门店数据权限");
        }
        if(null!=param.getBusinessId()) {
        	OrgDTO org = userDataScopeOrgList.stream().filter(orgDTO -> orgDTO.getOutId().equals(param.getBusinessId())).findFirst().orElse(null);
        	userDataScopeOrgList.clear();
        	userDataScopeOrgList.add(org);
        }
        List<Long> detailOrgIdList = null;
        if(null != param.getAdjustId()) {
        	AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustId());
        	if(null != adjustPriceOrder) {
        		AdjustPriceOrderOrgDetailExample orgDetailExample = new AdjustPriceOrderOrgDetailExample();
            	orgDetailExample.createCriteria().andAdjustCodeEqualTo(adjustPriceOrder.getAdjustCode());
            	List<AdjustPriceOrderOrgDetail> orgDetailList = adjustPriceOrderOrgDetailMapper.selectByExample(orgDetailExample);
            	if(CollectionUtils.isNotEmpty(orgDetailList)) {
            		detailOrgIdList = orgDetailList.stream().map(AdjustPriceOrderOrgDetail::getOrgId).collect(Collectors.toList());
            	}
        	}
        }
        List<Long> businessIdList = Lists.newArrayList();
        List<Integer> channelIdList = Lists.newArrayList();
        List<StoreChannelMappingDTO> storeChannelMappList = Lists.newArrayList();
        Map<Long, List<StoreChannelMappingDTO>> storeChannelMappMap = Maps.newHashMap();
        ResponseEntity<List<StoreChannelMappingDTO>> responseEntity = null;
        OrgTreeDTO orgVo = null;
        List<OrgTreeDTO> channelStoreList = null;
        OrgTreeDTO b2cChannelStore = null;
        List<StoreChannelMappingDTO> subStoreChannelMappList = Lists.newArrayList();
        List<List<OrgDTO>> partition = Lists.partition(userDataScopeOrgList, 10);
        for (List<OrgDTO> subList : partition) {
        	businessIdList.clear();
        	channelIdList.clear();
        	businessIdList = subList.stream().map(OrgDTO::getOutId).collect(Collectors.toList());
        	channelIdList.add(param.getChannelId());
        	responseEntity = storeService.getAllStoreChannelMappingByBusinessIdList(businessIdList, channelIdList);
        	if (Objects.isNull(responseEntity) || !HttpStatus.OK.equals(responseEntity.getStatusCode()) || Objects.isNull(responseEntity.getBody())) {
                continue;
            }
        	storeChannelMappList.clear();
        	storeChannelMappList = responseEntity.getBody();
        	if(CollectionUtils.isEmpty(storeChannelMappList)) {
        		continue;
        	}
        	storeChannelMappList = storeChannelMappList.stream().filter(obj -> null!=obj.getChannelMappingStoreId()).collect(Collectors.toList());
        	if(CollectionUtils.isEmpty(storeChannelMappList)) {
        		continue;
        	}
        	storeChannelMappMap.clear();
        	storeChannelMappMap = storeChannelMappList.stream().collect(Collectors.groupingBy(e -> e.getBusinessId()));
        	for (OrgDTO orgDto : subList) {
        		if(!storeChannelMappMap.containsKey(orgDto.getOutId())) {
        			continue;
        		}
        		orgVo = new OrgTreeDTO();
        		orgVo.setId(orgDto.getId());
        		orgVo.setOutId(orgDto.getOutId());
            	orgVo.setName(orgDto.getShortName());
            	orgVo.setLevel(OrgLevelTypeEnum.BUSINESS.getNewCode());
            	subStoreChannelMappList.clear();
            	subStoreChannelMappList = storeChannelMappMap.get(orgDto.getOutId());
            	channelStoreList = Lists.newArrayList();
            	List<Long> channelMappingStoreIdList = subStoreChannelMappList.stream().map(StoreChannelMappingDTO::getChannelMappingStoreId).collect(Collectors.toList());
            	if(CollectionUtils.isEmpty(channelMappingStoreIdList)) {
            		continue;
            	}
            	List<OrgDTO> orgDtoList = permissionExtService.listOrgByOutId(channelMappingStoreIdList, OrgTypeEnum.STORE.getCode());
            	if(CollectionUtils.isEmpty(orgDtoList)) {
            		continue;
            	}
            	List<Long> orgIdList = orgDtoList.stream().map(OrgDTO::getId).collect(Collectors.toList());
            	List<OrgDTO> scopeOrgDtoList = permissionExtService.getUserDataScopeChildOrgByOrgId(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), orgIdList, 1);
            	if(CollectionUtils.isEmpty(scopeOrgDtoList)) {
            		continue;
            	}
            	Map<Long, OrgDTO> channelMappingStoreIdOrgIdMap = scopeOrgDtoList.stream().collect(Collectors.toMap(OrgDTO::getOutId, Function.identity(), (v1, v2) -> v1));
            	for (StoreChannelMappingDTO storeChannelMapp : subStoreChannelMappList) {
            		if(null==storeChannelMapp.getChannelMappingStoreId()) {
            			continue;
            		}
            		OrgDTO orgDTO = channelMappingStoreIdOrgIdMap.get(storeChannelMapp.getChannelMappingStoreId());
            		if(null == orgDTO) {
            			continue;
            		}
            		if(CollectionUtils.isNotEmpty(detailOrgIdList) && !detailOrgIdList.contains(orgDTO.getId())) {
            			continue;
            		}
            		b2cChannelStore = new OrgTreeDTO();
            		b2cChannelStore.setId(orgDTO.getId());
            		b2cChannelStore.setOutId(storeChannelMapp.getChannelMappingStoreId());
            		b2cChannelStore.setName("未设置名称");
            		b2cChannelStore.setLevel(OrgLevelTypeEnum.STORE.getNewCode());
            		if(StringUtils.isNotBlank(storeChannelMapp.getExtend())) {
            			Optional<StoreChannelMappingDTOExtend> instance = StoreChannelMappingDTOExtend.getInstance(storeChannelMapp.getExtend());
                		if(instance.isPresent()) {
                			StoreChannelMappingDTOExtend extendIns = instance.get();
                			if(null!=extendIns && StringUtils.isNotBlank(extendIns.getChannelMappingStoreName())) {
                				b2cChannelStore.setName(extendIns.getChannelMappingStoreName());
                			}
                		}
            		}
            		channelStoreList.add(b2cChannelStore);
				}
            	if(CollectionUtils.isEmpty(channelStoreList)) {
            		continue;
            	}
            	orgVo.setChildren(channelStoreList);
            	orgList.add(orgVo);
			}
		}
		return orgList;
	}

	@Override
	public PageResult<CommonNewSpuVo> getItemSkuList(NewSpuQueryParam dto) {
		List<CommonNewSpuVo> result = Lists.newArrayList();
		if(dto.getOrgBizCode()==OrgBizCodeEnum.GENERAL_ORG_LIST.getCode() ){
			Map<String, Object> paramMap = JSON.parseObject(JSON.toJSONString(dto),Map.class);
			List<CommonNewSpuVo> spuList = marketingService.getCommonNewSpuVo(paramMap);
			if(CollectionUtils.isEmpty(spuList)) {
				return new PageResult<>(0L, new ArrayList<>());
			}
			return new PageResult<>(Long.valueOf(spuList.size()),spuList);
		}
		if(null == dto.getBusinessId()) {
			throw new BusinessErrorException("连锁id不能为空");
		}
		if(CollectionUtils.isEmpty(dto.getStoreIdList())) {
			throw new BusinessErrorException("门店id不能为空");
		}
		ItemSkuQueryApiDTO itemSkuQuery = new ItemSkuQueryApiDTO();
		BeanUtils.copyProperties(dto, itemSkuQuery);
		ResponseEntity<PageResult<ItemSkuVo>> responseEntity = itemCenterCpservice.query(itemSkuQuery);
		if (Objects.isNull(responseEntity) || !HttpStatus.OK.equals(responseEntity.getStatusCode()) || Objects.isNull(responseEntity.getBody())) {
			return new PageResult<>(0L, new ArrayList<>());
        }
		PageResult<ItemSkuVo> itemList = responseEntity.getBody();
		List<ItemSkuVo> rows = itemList.getRows();
		if(CollectionUtils.isNotEmpty(rows)) {
			ResponseEntity<List<StoreChannelMappingDTO>> response = storeService.list(dto.getBusinessId(), dto.getStoreIdList(),null,null);
			if (Objects.isNull(response) || !HttpStatus.OK.equals(response.getStatusCode()) || Objects.isNull(response.getBody())) {
				return new PageResult<>(0L, new ArrayList<>());
            }
			Map<Long, StoreChannelMappingDTO> storeChannelMap = response.getBody().stream().collect(Collectors.toMap(StoreChannelMappingDTO::getChannelMappingStoreId, Function.identity(), (v1, v2) -> v1));
			StoreChannelMappingDTO storeChannelMapp = null;
			for (ItemSkuVo itemSkuVo : rows) {
				if(storeChannelMap.containsKey(itemSkuVo.getStoreId())) {
					storeChannelMapp = storeChannelMap.get(itemSkuVo.getStoreId());
					if(StringUtils.isNotBlank(storeChannelMapp.getExtend())) {
            			Optional<StoreChannelMappingDTOExtend> instance = StoreChannelMappingDTOExtend.getInstance(storeChannelMapp.getExtend());
                		if(instance.isPresent()) {
                			StoreChannelMappingDTOExtend extendIns = instance.get();
                			if(null!=extendIns && StringUtils.isNotBlank(extendIns.getChannelMappingStoreName())) {
                				itemSkuVo.setStoreName(extendIns.getChannelMappingStoreName());
                			}
                		}
            		}
				}
			}
			CommonNewSpuVo spuVo = null;
			for (ItemSkuVo itemSkuVo : rows) {
				spuVo = new CommonNewSpuVo();
				BeanUtils.copyProperties(itemSkuVo, spuVo);
				result.add(spuVo);
			}
		}
		return new PageResult<>(itemList.getTotal(), result);
	}

	@Override
	public void updateImportResult(AdjustImportResultDTO param) {
		String cacheResultKey = RedisKeysConstant.getInitNewStoreAdjustPriceImportResultKey(param.getUuid());
        redissonClient.getBucket(cacheResultKey).set(JSON.toJSONString(param),30,TimeUnit.MINUTES);
	}

	@Override
	public void saveImportErrorData(List<InitNewStorePriceDTO> param) {
		String uuid = param.get(0).getUuid();
	    Integer index = param.get(0).getIndex();
	    String cacheKey = RedisKeysConstant.getInitNewStoreAdjustPriceImportErrorDataKey(uuid,index);
		redissonClient.getBucket(cacheKey).set(JSON.toJSONString(param),30,TimeUnit.MINUTES);
	}

	@Override
	public void initializeImportTask(ImportInitStorePriceTaskStatusDTO status) {
	    // 转换为JSON字符串
	    String jsonStatus = JSON.toJSONString(status);
	    String key = RedisKeysConstant.getImportInitStorePriceTaskStatusKey(status.getUuid());
	    redissonClient.getBucket(key).set(jsonStatus,30,TimeUnit.MINUTES);
	}

    @Override
    public Long getAdjustIdForHdFromCache(String sapCode, TokenUserDTO userDTO) {
        OrgToRedisDTO orgToRedisDTO = getOrgToRedisDTO(sapCode,userDTO);
        String key = RedisKeysConstant.getPriceAdjustidForHdKey(userDTO.getUserId(), sapCode);
        RBucket<String> bucket = redissonClient.getBucket(key);
        // 尝试从缓存中获取值
        String priceAdjustmentId = bucket.get();
        if (priceAdjustmentId != null) {
            // 如果取到值，直接返回
            return Long.valueOf(priceAdjustmentId);
        } else {
            OrgDTO orgDTO = permissionService.getOrgBelongBusiness(orgToRedisDTO.getId());
            Long adjustId = autoCreateAdjustPriceOrder(orgToRedisDTO, orgDTO.getId(), userDTO);
            bucket.set(adjustId.toString());
            return adjustId;
        }
    }

    /**
     * 获取组织Redis信息
     */
    private OrgToRedisDTO getOrgToRedisDTO(String sapCode, TokenUserDTO userDTO) {
        OrgToRedisDTO orgToRedisDTO = CacheVar.storeSapCodeCacheMap.get(sapCode);
        if(null == orgToRedisDTO){
            Map<String, OrgLevelVO> orgMap = permissionExtService.queryOrgInfoBySapCodes(Lists.newArrayList(sapCode));
            if(null!=orgMap && orgMap.containsKey(sapCode)){
                OrgLevelVO orgLevelVO = orgMap.get(sapCode);
                orgToRedisDTO = new OrgToRedisDTO();
                orgToRedisDTO.setId(orgLevelVO.getOrgId());
                orgToRedisDTO.setOutId(orgLevelVO.getOutId());
            }
        }
        if(null == orgToRedisDTO){
            throw new BusinessErrorException("sapCode编码不存在");
        }
        //权限验证
        Boolean result = permissionExtService.checkDataScopes(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), orgToRedisDTO.getId().toString());
        if(!result){
            throw new BusinessErrorException("用户没有该门店权限");
        }
        return orgToRedisDTO;
    }

    private Long autoCreateAdjustPriceOrder(OrgToRedisDTO orgToRedisDTO, Long userOrgId, TokenUserDTO userDTO) {
        LocalDateTime operateTime = LocalDateTime.now();
        Long resourceId = RequestHeaderContextUtils.getResourceId();
        String adjustCode = noSequenceService.priceNoSequence(NoSequenceTypeEnum.PRICECENTER_AJUST);
        AdjustPriceOrderDefaultDTO orderDefault = buildOrderDefault(orgToRedisDTO, userDTO);
        orderDefault.setAdjustCode(adjustCode);
        List<OrgLevelVO> orgLevelVOList = getUserActualSelectOrgDTOList(userDTO.getUserId(), resourceId, Lists.newArrayList(orgToRedisDTO.getId()));
        AdjustPriceOrder adjustPriceOrder = priceManageControlOrderService.autoBuildInsertAdjustPriceOrder(orderDefault, userDTO, userOrgId);
        adjustPriceOrder.setStatus(YNEnum.YES.getType().byteValue());//设置删除状态 没有提交可不看到
        insertAdjustOrgDetailList(adjustCode, orgLevelVOList, userDTO, operateTime);
        adjustPriceOrderMapper.insertSelective(adjustPriceOrder);
        return adjustPriceOrder.getId();
    }

    private AdjustPriceOrderDefaultDTO buildOrderDefault(OrgToRedisDTO orgToRedisDTO, TokenUserDTO userDTO) {
        AdjustPriceOrderDefaultDTO orderDefaultDTO = new AdjustPriceOrderDefaultDTO();
        orderDefaultDTO.setChannelId(Constants.DEFAULT_CHANNEL);
        orderDefaultDTO.setAdjustPriceType(PriceTypeModeEnum.LSJ.getPriceTypeCode()+","+PriceTypeModeEnum.HYJ.getPriceTypeCode()+","+PriceTypeModeEnum.CLJ.getPriceTypeCode());
        orderDefaultDTO.setGoodsScope(GoodsScopeEnum.ALL_GOODS_SCOPE.getCode());
        orderDefaultDTO.setAdjustType(AdjustTypeEnum.JM_SHICHANG_TYPE.getCode());
        orderDefaultDTO.setAdjustReason(AdjustTypeEnum.JM_SHICHANG_TYPE.getMessage());
        orderDefaultDTO.setExecStatus(ExecEnum.EXEC_NOW.getCode());
        orderDefaultDTO.setAuditStatus((byte)AuditStatusEnum.IN_PREPARATION.getCode());
        orderDefaultDTO.setSource(CommonEnums.SourceEnum.HD.getCode());
        orderDefaultDTO.setStoreId(orgToRedisDTO.getOutId());
        return orderDefaultDTO;
    }

    /**
	 * 处理单个门店数据的结果记录
	 * @param uuid 导入任务的唯一标识符
	 * @param success 是否处理成功
	 */
	private void processStoreData(String uuid, String storeNo, int goodsNoPriceCount, boolean success) {
	    String key = RedisKeysConstant.getImportInitStorePriceTaskStatusKey(uuid);
	    String lockKey = RedisKeysConstant.getImportInitStorePriceTaskStatusLock(uuid);
	    RLock lock = redissonClient.getLock(lockKey);
	    boolean locked = false;
	    try {
	    	// 尝试获取锁，等待3秒，10秒后自动释放
	        locked = lock.tryLock(10, 30, TimeUnit.SECONDS);
	        if (!locked) {
	            logger.warn("获取锁超时，门店[{}]数据处理结果可能未正确记录", storeNo);
	            return;
	        }
	        RBucket<String> bucket = redissonClient.getBucket(key);
	        String jsonStatus = bucket.get();
	        ImportInitStorePriceTaskStatusDTO status;
	        if (jsonStatus == null || jsonStatus.isEmpty()) {
	            status = new ImportInitStorePriceTaskStatusDTO();
	            status.setUuid(uuid);
	        } else {
	            status = JSON.parseObject(jsonStatus, ImportInitStorePriceTaskStatusDTO.class); // 使用FastJSON
	        }
	        // 根据处理结果更新状态
	        if (success) {
	            status.getSuccessStores().put(storeNo, goodsNoPriceCount);
	        } else {
	            status.getFailedStores().put(storeNo, goodsNoPriceCount);
	        }
	        String updatedJsonStatus = JSON.toJSONString(status); // 使用FastJSON
	        bucket.set(updatedJsonStatus);
	        // 检查是否所有门店都已处理完成
	        if (status.isCompleted()) {
	            // 所有门店都已处理完成，触发完成事件
	        	StringBuffer msg = new StringBuffer();
	        	if(StringUtils.isNotBlank(status.getSuccessStoreIdsAsString())) {
	        		msg.append("以下门店价格初始化成功").append(status.getSuccessStoreIdsAsString());
	        	}
	        	if(StringUtils.isNotBlank(status.getFailedStoreIdsAsString())) {
	        		msg.append(",");
	        		msg.append("以下门店生成调价单失败，请重试以下门店").append(status.getFailedStoreIdsAsString());
	        	}
                logger.info("门店价格初始化完成，结果：{}", msg.toString());
	        	saveImportResult(uuid, ImportStatusEnum.IMPORT_FINISH.getCode(), status.getItemCount(), status.getTotalSuccessPriceCount(), 0, msg.toString());
	        }
	    } catch (Exception e) {
	        logger.error("处理门店数据结果时发生错误", e);
	    } finally {
	    	// 只有成功获取到锁的情况下才需要解锁
	        if (locked && lock.isHeldByCurrentThread()) {
	            lock.unlock();
	        }
	    }
	}
}
