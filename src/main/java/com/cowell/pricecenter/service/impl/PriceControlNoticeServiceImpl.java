/**
* @mbg.generated
* generator on Fri Mar 18 13:56:06 CST 2022
*/
package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.PriceControlNotice;
import com.cowell.pricecenter.entity.PriceControlNoticeExample;
import com.cowell.pricecenter.mapper.PriceControlNoticeMapper;
import com.cowell.pricecenter.service.PriceControlNoticeService;
import com.cowell.pricecenter.service.dto.PriceControlNoticeQueryDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class PriceControlNoticeServiceImpl implements PriceControlNoticeService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceControlNoticeServiceImpl.class);

    @Autowired
    private PriceControlNoticeMapper priceControlNoticeMapper;

    /**
     * deleteByPrimaryKey
     * @param id id
     * @return int int
     */
    @Override
    public int deleteByPrimaryKey(Long id) {
        return priceControlNoticeMapper.deleteByPrimaryKey(id);
    }

    /**
     * insert
     * @param row row
     * @return int int
     */
    @Override
    public int insert(PriceControlNotice row) {
        return priceControlNoticeMapper.insert(row);
    }

    /**
     * insertSelective
     * @param row row
     * @return int int
     */
    @Override
    public int insertSelective(PriceControlNotice row) {
        return priceControlNoticeMapper.insertSelective(row);
    }

    /**
     * selectByPrimaryKey
     * @param id id
     * @return PriceControlNotice PriceControlNotice
     */
    @Override
    public PriceControlNotice selectByPrimaryKey(Long id) {
        return priceControlNoticeMapper.selectByPrimaryKey(id);
    }

    /**
     * updateByPrimaryKeySelective
     * @param row row
     * @return int int
     */
    @Override
    public int updateByPrimaryKeySelective(PriceControlNotice row) {
        return priceControlNoticeMapper.updateByPrimaryKeySelective(row);
    }

    /**
     * updateByPrimaryKey
     * @param row row
     * @return int int
     */
    @Override
    public int updateByPrimaryKey(PriceControlNotice row) {
        return priceControlNoticeMapper.updateByPrimaryKey(row);
    }

    @Override
    public long countByExample(PriceControlNoticeExample example) {
        return priceControlNoticeMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PriceControlNoticeExample example) {
        return priceControlNoticeMapper.deleteByExample(example);
    }

    @Override
    public int insertOrUpdate(PriceControlNotice record) {
        return priceControlNoticeMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(PriceControlNotice record) {
        return priceControlNoticeMapper.insertOrUpdateSelective(record);
    }

    @Override
    public List<PriceControlNotice> selectByExample(PriceControlNoticeExample example) {
        return priceControlNoticeMapper.selectByExample(example);
    }

    @Override
    public int updateByExampleSelective(PriceControlNotice record, PriceControlNoticeExample example) {
        return priceControlNoticeMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceControlNotice record, PriceControlNoticeExample example) {
        return priceControlNoticeMapper.updateByExample(record, example);
    }

    @Override
    public int updateBatch(List<PriceControlNotice> list) {
        return priceControlNoticeMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<PriceControlNotice> list) {
        return priceControlNoticeMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<PriceControlNotice> list) {
        return priceControlNoticeMapper.batchInsert(list);
    }

    @Override
    public int saveControlNotice(List<PriceControlNotice> list) {
        if (CollectionUtils.isEmpty(list)) {
            LOGGER.info("PriceControlNoticeServiceImpl|saveControlNotice|管控通知明细为空");
            return 0;
        }

        List<PriceControlNotice> insertList = Lists.newArrayList();
        list.forEach(priceNotice -> {
            PriceControlNoticeQueryDTO queryDTO = new PriceControlNoticeQueryDTO();
            queryDTO.setStoreId(priceNotice.getStoreId());
            queryDTO.setChannelId(priceNotice.getChannelId());
            queryDTO.setGoodsNo(priceNotice.getGoodsNo());
            queryDTO.setPriceTypeCode(priceNotice.getPriceTypeCode());
            queryDTO.setControlOrderCode(priceNotice.getControlOrderId());
            List<PriceControlNotice> existList = getPriceControlNoticeListByParam(queryDTO);
            if (CollectionUtils.isEmpty(existList) || Objects.isNull(existList.get(0))) {
                insertList.add(priceNotice);
            } else {
                PriceControlNotice existNotice = existList.get(0);
                existNotice.setControlOrderId(priceNotice.getControlOrderId());
                priceControlNoticeMapper.updateByPrimaryKey(existNotice);
            }

        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            return priceControlNoticeMapper.batchInsert(insertList);
        }

        return 0;
    }

    @Override
    public long countPriceControlNotice(PriceControlNoticeQueryDTO queryDTO) {
        PriceControlNoticeExample priceControlNoticeExample = getPriceControlNoticeExample(queryDTO);
        return priceControlNoticeMapper.countByExample(priceControlNoticeExample);
    }

    @Override
    public List<PriceControlNotice> getPriceControlNoticeListByParam(PriceControlNoticeQueryDTO queryDTO) {
        PriceControlNoticeExample priceControlNoticeExample = getPriceControlNoticeExample(queryDTO);
        return priceControlNoticeMapper.selectByExample(priceControlNoticeExample);
    }

    private PriceControlNoticeExample getPriceControlNoticeExample(PriceControlNoticeQueryDTO queryDTO) {
        PriceControlNoticeExample priceControlNoticeExample = new PriceControlNoticeExample();
        PriceControlNoticeExample.Criteria criteria = priceControlNoticeExample.createCriteria();
        if (Objects.nonNull(queryDTO.getChannelId())) {
            criteria.andChannelIdEqualTo(queryDTO.getChannelId());
        }

        if (StringUtils.isNotBlank(queryDTO.getGoodsNo())) {
            criteria.andGoodsNoEqualTo(queryDTO.getGoodsNo());
        }

        if (Objects.nonNull(queryDTO.getStoreId())) {
            criteria.andStoreIdEqualTo(queryDTO.getStoreId());
        }

        if (StringUtils.isNotBlank(queryDTO.getPriceTypeCode())) {
            criteria.andPriceTypeCodeEqualTo(queryDTO.getPriceTypeCode());
        }

        if (org.apache.commons.lang3.StringUtils.isNotBlank(queryDTO.getControlOrderCode())) {
            criteria.andControlOrderIdEqualTo(queryDTO.getControlOrderCode());
        }
        return priceControlNoticeExample;
    }
}





