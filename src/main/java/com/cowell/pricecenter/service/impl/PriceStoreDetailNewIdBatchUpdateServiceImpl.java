package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.enums.DistributedIDTypeEnum;
import com.cowell.pricecenter.service.IPriceStoreDetailNewIdBatchUpdateService;
import com.cowell.pricecenter.service.ITocExtService;
import com.cowell.pricecenter.web.rest.errors.BusinessException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Price Store Detail批量更新new_id字段服务实现
 * 支持512张表(128*4个数据源)的批量更新
 *
 * <AUTHOR>
 */
@Service
public class PriceStoreDetailNewIdBatchUpdateServiceImpl implements IPriceStoreDetailNewIdBatchUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(PriceStoreDetailNewIdBatchUpdateServiceImpl.class);

    private static final int TABLES_PER_DS = 128;
    private static final int DS_COUNT = 4;

    @Autowired
    @Qualifier("ds_0")
    @Lazy
    private DataSource dataSource0;

    @Autowired
    @Qualifier("ds_1")
    @Lazy
    private DataSource dataSource1;

    @Autowired
    @Qualifier("ds_2")
    @Lazy
    private DataSource dataSource2;

    @Autowired
    @Qualifier("ds_3")
    @Lazy
    private DataSource dataSource3;

    @Autowired
    private ITocExtService tocExtService;

    private JdbcTemplate[] jdbcTemplates;

    private DataSource[] dataSources;

    // 任务停止控制开关
    private final AtomicBoolean stopFlag = new AtomicBoolean(false);

    private final AtomicBoolean taskRunning = new AtomicBoolean(false);

    @Value("${price.batch.peakHourBatchSize:200}")
    private int peakHourBatchSize;

    @Value("${price.batch.offPeakMaxBatchSize:800}")
    private int offPeakMaxBatchSize;

    @Value("${price.batch.peakHourSleepMs:500}")
    private long peakHourSleepMs;

    @Value("${price.batch.offPeakSleepMs:100}")
    private long offPeakSleepMs;

    //高峰期 6-20点
    private final static  int peakStartHour = 6;
    private final static  int peakEndHour = 20;

    @PostConstruct
    public void init() {
        dataSources = new DataSource[]{dataSource0, dataSource1, dataSource2, dataSource3};
        jdbcTemplates = new JdbcTemplate[DS_COUNT];
        for (int i = 0; i < DS_COUNT; i++) {
            jdbcTemplates[i] = new JdbcTemplate(dataSources[i]);
        }
    }
    @Override
    public void batchUpdateAllTables(int dsIndex) {
        if (!taskRunning.compareAndSet(false, true)) {
            logger.warn("批量更新任务已在运行中，跳过本次执行");
            return;
        }
        stopFlag.set(false);
        logger.info("开始批量更新所有price_store_detail表的new_id字段");
        try {
            for (int tableIndex = 0; tableIndex < TABLES_PER_DS && !stopFlag.get(); tableIndex++) {
                String tableName = "price_store_detail_" + tableIndex;
                try {
                    int updatedCount = batchUpdateTable(dsIndex, tableName, null);
                    logger.info("数据源{} 表{} 更新完成，共更新{}条记录",dsIndex, tableName, updatedCount);
                    // 检查停止标志
                    if (stopFlag.get()) {
                        logger.info("接收到停止信号，终止批量更新任务");
                        break;
                    }
                    // 根据时间段控制消费速度
                    long sleepTime = getCurrentSleepTime();
                    Thread.sleep(sleepTime);
                } catch (Exception e) {
                    logger.error("更新数据源{} 表{} 失败", dsIndex, tableName, e);
                    if (e instanceof InterruptedException) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            if (stopFlag.get()) {
                logger.info("批量更新任务已被停止");
            } else {
                logger.info("所有表批量更新完成");
            }

        } finally {
            taskRunning.set(false);
        }
    }

    @Override
    public int batchUpdateTable(int dsIndex, String tableName, String storeId) {
        if (dsIndex < 0 || dsIndex >= DS_COUNT) {
            throw new RuntimeException("数据源索引超出范围: " + dsIndex);
        }
        JdbcTemplate jdbcTemplate = jdbcTemplates[dsIndex];
        AtomicLong maxId = new AtomicLong(0);
        int totalUpdated = 0;
        logger.info("开始更新数据源{} 表{} {}", dsIndex, tableName,storeId != null ? "门店ID:" + storeId : "所有门店");
        try {
            while (!stopFlag.get()) {
                // 根据当前时间和数据库性能动态调整批次大小
                int batchSize = getCurrentBatchSize(jdbcTemplate, tableName);
                // 查询需要更新的记录ID列表
                List<Long> idsToUpdate = queryIdsForUpdate(jdbcTemplate, tableName, maxId.get(), batchSize, storeId);
                if (idsToUpdate.isEmpty()) {
                    logger.info("表 {} {} 没有更多需要更新的记录", tableName, storeId != null ? "门店" + storeId : "");
                    break;
                }
                // 批量获取分布式ID
                List<Long> newIds = getDistributedIds(idsToUpdate.size());
                // 批量更新
                int updated = batchUpdateNewIds(jdbcTemplate, tableName, idsToUpdate, newIds);
                totalUpdated += updated;
                // 更新maxId为本批次最大的ID
                maxId.set(idsToUpdate.get(idsToUpdate.size() - 1));

                logger.info("表 {} {} 本批次更新 {} 条记录，当前maxId: {}",
                           tableName, storeId != null ? "门店" + storeId : "", updated, maxId.get());

                // 检查停止标志
                if (stopFlag.get()) {
                    logger.info("接收到停止信号，终止表 {} 的更新", tableName);
                    break;
                }
                // 根据时间段控制消费速度
                long sleepTime = getCurrentSleepTime();
                Thread.sleep(sleepTime);
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.info("表 {} 更新被中断", tableName);
        } catch (Exception e) {
            logger.error("更新表 {} 失败", tableName, e);
            throw new BusinessException("更新表失败: " + e.getMessage());
        }
        return totalUpdated;
    }

    /**
     * 根据当前时间和数据库性能动态获取批次大小
     */
    private int getCurrentBatchSize(JdbcTemplate jdbcTemplate, String tableName) {
        boolean isPeakHour = isPeakHour();
        if (isPeakHour) {
            return peakHourBatchSize;
        } else {
            return offPeakMaxBatchSize;
        }
    }

    /**
     * 获取当前休眠时间
     */
    private long getCurrentSleepTime() {
        return isPeakHour() ? peakHourSleepMs : offPeakSleepMs;
    }

    /**
     * 判断是否为高峰期
     */
    private boolean isPeakHour() {
        int currentHour = LocalTime.now().getHour();
        return currentHour >= peakStartHour && currentHour < peakEndHour;
    }

    /**
     * 查询需要更新的记录ID列表
     */
    private List<Long> queryIdsForUpdate(JdbcTemplate jdbcTemplate, String tableName,
                                       long lastMaxId, int batchSize, String storeId) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT id FROM ").append(tableName)
           .append(" WHERE new_id = 0 AND id > ?");

        if (storeId != null) {
            sql.append(" AND store_id = ?");
        }
        sql.append(" ORDER BY id LIMIT ?");
        try {
            if (storeId != null) {
                return jdbcTemplate.queryForList(sql.toString(), Long.class, lastMaxId, storeId, batchSize);
            } else {
                return jdbcTemplate.queryForList(sql.toString(), Long.class, lastMaxId, batchSize);
            }
        } catch (Exception e) {
            logger.error("查询需要更新的记录ID失败，表: {}, maxId: {}", tableName, lastMaxId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 批量获取分布式ID
     */
    private List<Long> getDistributedIds(int count) {
        try {
            long[] ids = tocExtService.getDistributedIDList(DistributedIDTypeEnum.PRICE_STORE_DETAIL.getBiz(), count);
            List<Long> result = new ArrayList<>();
            for (long id : ids) {
                result.add(id);
            }
            return result;
        } catch (Exception e) {
            logger.error("获取分布式ID失败，count: {}", count, e);
            throw new BusinessException("获取分布式ID失败: " + e.getMessage());
        }
    }

    /**
     * 智能批量更新new_id字段
     */
    private int batchUpdateNewIds(JdbcTemplate jdbcTemplate, String tableName,
                                List<Long> ids, List<Long> newIds) {
        if (ids.isEmpty() || ids.size() != newIds.size()) {
            throw new IllegalArgumentException("ID列表和新ID列表长度不匹配或为空");
        }
        return batchUpdateWithJdbcBatch(jdbcTemplate, tableName, ids, newIds);
    }

    /**
     * 方案1: JDBC批处理更新 - 推荐中小批量
     */
    private int batchUpdateWithJdbcBatch(JdbcTemplate jdbcTemplate, String tableName,
                                       List<Long> ids, List<Long> newIds) {
        String sql = "UPDATE " + tableName + " SET new_id = ? WHERE id = ?";

        int[] results = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                ps.setLong(1, newIds.get(i));
                ps.setLong(2, ids.get(i));
            }

            @Override
            public int getBatchSize() {
                return ids.size();
            }
        });

        return results.length;
    }


    @Override
    public void showProgress(int dsIndex, String tableName, String storeId) {
        if (dsIndex < 0 || dsIndex >= DS_COUNT) {
            logger.error("数据源索引超出范围: {}", dsIndex);
            return;
        }
        JdbcTemplate jdbcTemplate = jdbcTemplates[dsIndex];
        StringBuilder totalSql = new StringBuilder("SELECT COUNT(*) FROM ").append(tableName);
        StringBuilder updatedSql = new StringBuilder("SELECT COUNT(*) FROM ").append(tableName).append(" WHERE new_id != 0");
        StringBuilder pendingSql = new StringBuilder("SELECT COUNT(*) FROM ").append(tableName).append(" WHERE new_id = 0");

        if (storeId != null) {
            String storeCondition = " AND store_id = " + storeId;
            totalSql.append(" WHERE store_id = ").append(storeId);
            updatedSql.append(storeCondition);
            pendingSql.append(storeCondition);
        }
        try {
            int total = jdbcTemplate.queryForObject(totalSql.toString(), Integer.class);
            int updated = jdbcTemplate.queryForObject(updatedSql.toString(), Integer.class);
            int pending = jdbcTemplate.queryForObject(pendingSql.toString(), Integer.class);
            double progress = total > 0 ? (updated * 100.0 / total) : 0;
            String scope = storeId != null ? "门店" + storeId : "全部门店";
            logger.info("数据源{} 表{} {} 进度统计:", dsIndex, tableName, scope);
            logger.info("  总记录数: {}", total);
            logger.info("  已更新: {}", updated);
            logger.info("  待更新: {}", pending);
            logger.info("  完成进度: {} %", progress);

        } catch (Exception e) {
            logger.error("查询进度失败，数据源: {}, 表: {}", dsIndex, tableName, e);
        }
    }

    @Override
    public void stopCurrentTask() {
        stopFlag.set(true);
        logger.info("已设置任务停止标志，正在执行的任务将在当前批次完成后停止");
    }

    @Override
    public boolean isTaskRunning() {
        return taskRunning.get();
    }
}
