package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Maps;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.entity.AdjustPriceOrderDetail;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.service.AdjustLimitConfigService;
import com.cowell.pricecenter.service.ErpBizSupportExtService;
import com.cowell.pricecenter.service.IAdjustPriceOrderV2Service;
import com.cowell.pricecenter.service.ISearchExtService;
import com.cowell.pricecenter.service.dto.GoodsClassification;
import com.cowell.pricecenter.service.dto.response.PriceStoreDetailVo;
import com.cowell.pricecenter.service.feign.vo.AdjustLimitConfigRow;
import com.cowell.pricecenter.service.feign.vo.PriceManageWhiteListVO;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @program: pricecenter
 * @description: 调价管控服务，例如限购限制和防疫限制
 * @author: jmlu
 * @create: 2022-12-10 20:14
 **/

@Service
public class AdjustLimitConfigServiceImpl implements AdjustLimitConfigService {
	private static final Logger logger = LoggerFactory.getLogger(AdjustLimitConfigServiceImpl.class);
    @Resource
    private ISearchExtService searchExtService;

    @Resource
    private ErpBizSupportExtService erpBizSupportExtService;

    @Autowired
    private IAdjustPriceOrderV2Service adjustPriceOrderV2Service;

    @ApolloJsonValue("${adjust.not.check.goodsname.classone:[11, 12, 13]}")
    private List<String> notCheckGoodsNameClassOneList;

    @Override
    public GoodsLimitStatusEnum checkGoodsClassificationLimit(GoodsClassification goodsClassification, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,
    		AdjustPriceOrderDetail orderDetail,Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap) {
        if (goodsClassification == null || CollectionUtils.isEmpty(businessIdList)) {
            return null;
        }
        GoodsLimitStatusEnum limitStatus = null;
        for (Long businessId : businessIdList) {
        	limitStatus = checkClassification(goodsClassification, businessId, storeDetailList, orderDetail,limitLevel,limitConfigRowMap);
        	//管控条件没有匹配 用于是否中止后续管控验证
        	if(null==limitStatus) {
        		continue;
        	}
        	//管控条件已匹配 不满足限制要求中止后续管控验证
            if (GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()!=limitStatus.getCode()) {
                break;
            }
        }
        if (null!=limitStatus && GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()==limitStatus.getCode()) {
            return GoodsLimitStatusEnum.NONE;
        }
        return limitStatus;
    }

    @Override
    public GoodsLimitStatusEnum checkGoodsNoLimit(String goodsNo, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
    		Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap) {
        if (StringUtils.isEmpty(goodsNo) || CollectionUtils.isEmpty(businessIdList)) {
            return null;
        }
        GoodsLimitStatusEnum limitStatus = null;
        for (Long businessId : businessIdList) {
        	limitStatus = checkGoodsno(goodsNo, businessId, storeDetailList, orderDetail,limitLevel,limitConfigRowMap);
        	//管控条件没有匹配 用于是否中止后续管控验证
        	if(null==limitStatus) {
        		continue;
        	}
        	//管控条件已匹配 不满足限制要求中止后续管控验证
            if (GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()!=limitStatus.getCode()) {
                break;
            }
        }
        if (null!=limitStatus && GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()==limitStatus.getCode()) {
            return GoodsLimitStatusEnum.NONE;
        }
        return limitStatus;
    }

    /**
     * 检查是否商品编码受限
     * @param goodsNo
     * @param businessId
     * @return
     */
    private GoodsLimitStatusEnum checkGoodsno(String goodsNo, Long businessId,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
    		Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap) {
    	//List<AdjustLimitConfigRow> configRows = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.GOODS_NO_LIMIT, businessId,goodsNo);
    	List<AdjustLimitConfigRow> configRows = null;
    	if(limitLevel==AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()) {
    		List<AdjustLimitConfigRow> configRowList = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.GOODS_NO_LIMIT, businessId,goodsNo);
    		Map<Long, List<AdjustLimitConfigRow>> businessLimitConfigs = configRowList.stream().collect(Collectors.groupingBy(e -> e.getBusinessId()));
    		configRows = businessLimitConfigs.get(Long.valueOf(AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()));
    		limitConfigRowMap.put(businessId+"_"+AdjustLimitConfigEnum.GOODS_NO_LIMIT.getCode(), businessLimitConfigs.get(businessId));
        }else {
        	configRows = limitConfigRowMap.get(businessId+"_"+AdjustLimitConfigEnum.GOODS_NO_LIMIT.getCode());
        }
    	if(CollectionUtils.isEmpty(configRows)) {
    		return null;
    	}
        AdjustLimitConfigRow limitObj = configRows.stream().filter(configRow -> configRow.getBizCode().equals(goodsNo)).findFirst().orElse(null);
        if(null==limitObj) {
        	return null;
        }
        Integer bizOption = limitObj.getBizOption();
    	return checkLimitStatus(storeDetailList, orderDetail,bizOption);
    }

    /**
     * 检查是否分类受限
     * @param goodsClassification
     * @param businessId
     * @return
     */
    private GoodsLimitStatusEnum checkClassification(GoodsClassification goodsClassification, Long businessId,List<PriceStoreDetailVo> storeDetailList,
    		AdjustPriceOrderDetail orderDetail,Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap) {
    	List<AdjustLimitConfigRow> configRows = null;
    	if(limitLevel==AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()) {
    		List<AdjustLimitConfigRow> configRowList = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.ClASSIFICATION_LIMIT, businessId,null);
    		Map<Long, List<AdjustLimitConfigRow>> businessLimitConfigs = configRowList.stream().collect(Collectors.groupingBy(e -> e.getBusinessId()));
    		configRows = businessLimitConfigs.get(Long.valueOf(AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()));
    		limitConfigRowMap.put(businessId+"_"+AdjustLimitConfigEnum.ClASSIFICATION_LIMIT.getCode(), businessLimitConfigs.get(businessId));
        }else {
        	configRows = limitConfigRowMap.get(businessId+"_"+AdjustLimitConfigEnum.ClASSIFICATION_LIMIT.getCode());
        }
    	if(CollectionUtils.isEmpty(configRows)) {
    		return null;
    	}
        AdjustLimitConfigRow limitObj = configRows.stream().filter(configRow -> configRow.getBizCode().equals(goodsClassification.getClassOne()) ||
        		configRow.getBizCode().equals(goodsClassification.getClassTwo()) ||
        		configRow.getBizCode().equals(goodsClassification.getClassThree()) ||
        		configRow.getBizCode().equals(goodsClassification.getClassFour())
        ).findFirst().orElse(null);
        if(null==limitObj) {
        	return null;
        }
        Integer bizOption = limitObj.getBizOption();
    	return checkLimitStatus(storeDetailList, orderDetail,bizOption);
    }

    @Override
    public GoodsLimitStatusEnum checkGoodsNameLimit(String goodsName, GoodsClassification goodsClassification, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,
    		AdjustPriceOrderDetail orderDetail,Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap) {
        if (StringUtils.isEmpty(goodsName) || goodsClassification == null || CollectionUtils.isEmpty(businessIdList)) {
            return null;
        }
        String classOne = goodsClassification.getClassOne();
        if (!notCheckGoodsNameClassOneList.contains(classOne)) {
        	GoodsLimitStatusEnum limitStatus = null;
            for (Long businessId : businessIdList) {
            	limitStatus = checkGoodsNameLimit(goodsName, businessId, storeDetailList, orderDetail,limitLevel,limitConfigRowMap);
            	//管控条件没有匹配 用于是否中止后续管控验证
            	if(null==limitStatus) {
            		continue;
            	}
            	//管控条件已匹配 不满足限制要求中止后续管控验证
                if (GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()!=limitStatus.getCode()) {
                    break;
                }
            }
            if (null!=limitStatus && GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()==limitStatus.getCode()) {
                return GoodsLimitStatusEnum.NONE;
            }
            return limitStatus;
        }
        return null;
    }

    private GoodsLimitStatusEnum checkGoodsNameLimit(String goodsName, Long businessId,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
    		Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap) {
    	//List<AdjustLimitConfigRow> configRows = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.GOODS_NAME_LIMIT, businessId,null);
    	List<AdjustLimitConfigRow> configRows = null;
    	if(limitLevel==AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()) {
    		List<AdjustLimitConfigRow> configRowList = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.GOODS_NAME_LIMIT, businessId,null);
    		Map<Long, List<AdjustLimitConfigRow>> businessLimitConfigs = configRowList.stream().collect(Collectors.groupingBy(e -> e.getBusinessId()));
    		configRows = businessLimitConfigs.get(Long.valueOf(AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()));
    		limitConfigRowMap.put(businessId+"_"+AdjustLimitConfigEnum.GOODS_NAME_LIMIT.getCode(), businessLimitConfigs.get(businessId));
        }else {
        	configRows = limitConfigRowMap.get(businessId+"_"+AdjustLimitConfigEnum.GOODS_NAME_LIMIT.getCode());
        }
    	if(CollectionUtils.isEmpty(configRows)) {
    		return null;
    	}
        AdjustLimitConfigRow limitObj = configRows.stream().filter(configRow -> goodsName.contains(configRow.getBizCode())).findFirst().orElse(null);
        if(null==limitObj) {
        	return null;
        }
        Integer bizOption = limitObj.getBizOption();
    	return checkLimitStatus(storeDetailList, orderDetail,bizOption);
    }

    private GoodsLimitStatusEnum checkLimitStatus(List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,Integer bizOption) {
    	for (PriceStoreDetailVo storeDetail : storeDetailList) {
    		if(GoodsLimitStatusEnum.UPPER_LIMIT.getCode()==bizOption && orderDetail.getPrice().compareTo(storeDetail.getPrice())<=0) {
    			return GoodsLimitStatusEnum.UPPER_LIMIT;
    		}
    		if(GoodsLimitStatusEnum.LOWER_LIMIT.getCode()==bizOption  && orderDetail.getPrice().compareTo(storeDetail.getPrice())>=0) {
    			return GoodsLimitStatusEnum.LOWER_LIMIT;
    		}
    		if(GoodsLimitStatusEnum.NOT_ALLOW_ADJUST.getCode()==bizOption) {
    			return GoodsLimitStatusEnum.NOT_ALLOW_ADJUST;
    		}
		}
    	return GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST;
    }

    @Override
    public GoodsLimitStatusEnum checkGoodsLimit(String goodsNo, String goodsName, String categoryId, String covidtype, Map<Long, String> pushLevelsMap, List<OrgLevelVO> orgLevelVOList, AdjustPriceOrderDetail orderDetail, Long userId) {
    	GoodsLimitStatusEnum defaultControlStatusEnum = GoodsLimitStatusEnum.NONE;
    	logger.info("AdjustLimitConfigServiceImpl|checkGoodsLimit|orderDetail:{}", JSON.toJSONString(orderDetail));

    	//验证userId是否在白名单，存在白名单中则直接返回不管控
    	List<PriceManageWhiteListVO> managerUserIdList = erpBizSupportExtService.queryPriceManageWhiteList(PriceManageWhiteListEnum.USERID_WHITE_TYPE, null, userId);

    	if(CollectionUtils.isNotEmpty(managerUserIdList)) {
    		return defaultControlStatusEnum;
    	}
    	if((!orderDetail.getPriceTypeCode().equals(PriceTypeModeEnum.LSJ.getPriceTypeCode()) &&
    			!orderDetail.getPriceTypeCode().equals(PriceTypeModeEnum.HYJ.getPriceTypeCode())) || null==orderDetail.getPrice()) {
    		return defaultControlStatusEnum;
    	}
    	List<AdjustPriceOrderOrgStoreDetail> orgStoreList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs("", orgLevelVOList, null);

    	if(CollectionUtils.isEmpty(orgStoreList)) {
    		return defaultControlStatusEnum;
    	}
		List<Long> businessList = orgStoreList.stream().map(AdjustPriceOrderOrgStoreDetail::getBusinessId).distinct().collect(Collectors.toList());
		//验证businessIdList 是否为机构白名单配置的组织，如果是则不执行管控(选择组织机构为多个连锁时，需所有连锁都是机构白名单配置的机构，才不执行管控)
		List<PriceManageWhiteListVO> manageWhiteList = erpBizSupportExtService.queryPriceManageWhiteList(PriceManageWhiteListEnum.BUSINESS_WHITE_TYPE, businessList, null);
		List<Long> whiteBusinessIdList = manageWhiteList.stream().map(PriceManageWhiteListVO::getBusinessId).distinct().collect(Collectors.toList());
		boolean allMatch = businessList.stream().allMatch(whiteBusinessIdList::contains);
		if(allMatch) {
			return defaultControlStatusEnum;
		}
		List<Long> storeIdList = orgStoreList.stream().filter(store -> !whiteBusinessIdList.contains(store.getBusinessId())).map(AdjustPriceOrderOrgStoreDetail::getStoreId).distinct().collect(Collectors.toList());
		if(CollectionUtils.isEmpty(storeIdList)) {
			return defaultControlStatusEnum;
		}
		//排除白名单内的连锁id
		List<Long> businessIdList = businessList.stream().filter(bId -> !whiteBusinessIdList.contains(bId)).sorted().collect(Collectors.toList());
		if(CollectionUtils.isEmpty(businessIdList)) {
			return defaultControlStatusEnum;
		}
		List<List<Long>> partition = Lists.partition(storeIdList, Constants.BATCH_QUERY_SIZE_50);
		List<PriceStoreDetailVo> storeDetailList = Lists.newArrayList();
		for (List<Long> subStoreIdList : partition) {
			List<PriceStoreDetailVo> subStoreDetailList = searchExtService.getByGoodsNoAndStoreIdListAndPriceTypeCode(subStoreIdList, goodsNo, orderDetail.getPriceTypeCode());
			if(CollectionUtils.isNotEmpty(subStoreDetailList)) {
				storeDetailList.addAll(subStoreDetailList);
			}
		}
    	GoodsClassification goodsClassification = GoodsClassification.fromCategoryId(categoryId);
        //新品（商品在公司没有做过价格）不做商品限制判断
        if (CollectionUtils.isEmpty(storeDetailList)) {
        	return defaultControlStatusEnum;
        }

        /**
         * 获取商品分类、商品编码、关键字、标签时 同时把集团和连锁信息返回过来，
         * 依次验证商品分类、商品编码、关键字、标签时(集团)。
         * 依次验证商品分类、商品编码、关键字、标签时(连锁)。
         * 遇到匹配不论是否能拦截 都不往下执行验证逻辑
         */
        //key:连锁Id_AdjustLimitConfigEnum.code。value:List<AdjustLimitConfigRow>
        Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap = Maps.newHashMap();
        List<Integer> limitLevelList = AdjustLimitLevelEnum.getAdjustLimitLevel();
        for (Integer limitLevel : limitLevelList) {
        	GoodsLimitStatusEnum goodsClassificationLimit = checkGoodsClassificationLimit(goodsClassification, businessIdList, storeDetailList, orderDetail,limitLevel,limitConfigRowMap);
            if(null!=goodsClassificationLimit) {
            	return goodsClassificationLimit;
            }
            GoodsLimitStatusEnum goodsNoLimit = checkGoodsNoLimit(goodsNo, businessIdList, storeDetailList, orderDetail,limitLevel,limitConfigRowMap);
            if(null!=goodsNoLimit) {
            	return goodsNoLimit;
            }
            GoodsLimitStatusEnum goodsNameLimit = checkGoodsNameLimit(goodsName, goodsClassification, businessIdList, storeDetailList, orderDetail,limitLevel,limitConfigRowMap);
            if(null!=goodsNameLimit) {
            	return goodsNameLimit;
            }
            GoodsLimitStatusEnum covidtypeLimit = checkCovidTypeLimit(covidtype, businessIdList, storeDetailList, orderDetail,limitLevel,limitConfigRowMap,ControlGoodsPropertyEnum.GOODS_TAG);
            if(null!=covidtypeLimit) {
            	return covidtypeLimit;
            }
            GoodsLimitStatusEnum goodsPropertyLimit = checkGoodsPropertyLimit(pushLevelsMap, businessIdList, storeDetailList, orderDetail,limitLevel,limitConfigRowMap,ControlGoodsPropertyEnum.SALES_PROPERTY);
            if(null!=goodsPropertyLimit) {
            	return goodsPropertyLimit;
            }
		}
        return defaultControlStatusEnum;
    }

	@Override
	public GoodsLimitStatusEnum checkCovidTypeLimit(String covidtype, List<Long> businessIdList,
			List<PriceStoreDetailVo> storeDetailList, AdjustPriceOrderDetail orderDetail,Integer limitLevel,Map<String,
			List<AdjustLimitConfigRow>> limitConfigRowMap, ControlGoodsPropertyEnum propertyEnum) {
		if (StringUtils.isEmpty(covidtype) || CollectionUtils.isEmpty(businessIdList)) {
            return null;
        }
		GoodsLimitStatusEnum limitStatus = null;
        for (Long businessId : businessIdList) {
        	limitStatus = checkCovidType(covidtype, businessId, storeDetailList, orderDetail, limitLevel, limitConfigRowMap, propertyEnum);
        	//管控条件没有匹配 用于是否中止后续管控验证
        	if(null==limitStatus) {
        		continue;
        	}
        	//管控条件已匹配 不满足限制要求中止后续管控验证
            if (GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()!=limitStatus.getCode()) {
                break;
            }
        }
        if (null!=limitStatus && GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()==limitStatus.getCode()) {
            return GoodsLimitStatusEnum.NONE;
        }
        return limitStatus;
	}

	@Override
	public GoodsLimitStatusEnum checkGoodsPropertyLimit(Map<Long, String> pushLevelsMap, List<Long> businessIdList,
			List<PriceStoreDetailVo> storeDetailList, AdjustPriceOrderDetail orderDetail,Integer limitLevel,
			Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap, ControlGoodsPropertyEnum propertyEnum) {
		if (MapUtils.isEmpty(pushLevelsMap) || CollectionUtils.isEmpty(businessIdList)) {
            return null;
        }
		GoodsLimitStatusEnum limitStatus = null;
        for (Long businessId : businessIdList) {
        	limitStatus = checkGoodsProperty(pushLevelsMap, businessId, storeDetailList, orderDetail, limitLevel, limitConfigRowMap, propertyEnum);
        	//管控条件没有匹配 用于是否中止后续管控验证
        	if(null==limitStatus) {
        		continue;
        	}
        	//管控条件已匹配 不满足限制要求中止后续管控验证
            if (GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()!=limitStatus.getCode()) {
                break;
            }
        }
        if (null!=limitStatus && GoodsLimitStatusEnum.CONDITION_SATISFY_ADJUST.getCode()==limitStatus.getCode()) {
            return GoodsLimitStatusEnum.NONE;
        }
        return limitStatus;
	}

	/**
     * 检查是否商品属性受限
     * @param goodsNo
     * @param businessId
     * @return
     */
    private GoodsLimitStatusEnum checkCovidType(String covidtype, Long businessId,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
    		Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap, ControlGoodsPropertyEnum propertyEnum) {
    	//List<AdjustLimitConfigRow> configRows = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.COVID_TYPE_LIMIT, businessId,covidtype);
    	List<AdjustLimitConfigRow> configRows = null;
    	if(limitLevel==AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()) {
    		List<AdjustLimitConfigRow> configRowList = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.GOODS_TAG_PROPERTY_LIMIT, businessId,covidtype);
    		Map<Long, List<AdjustLimitConfigRow>> businessLimitConfigs = configRowList.stream().collect(Collectors.groupingBy(e -> e.getBusinessId()));
    		configRows = businessLimitConfigs.get(Long.valueOf(AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()));
    		limitConfigRowMap.put(businessId+"_"+AdjustLimitConfigEnum.GOODS_TAG_PROPERTY_LIMIT.getCode()+"_"+propertyEnum.getCode(), businessLimitConfigs.get(businessId));
        }else {
        	configRows = limitConfigRowMap.get(businessId+"_"+AdjustLimitConfigEnum.GOODS_TAG_PROPERTY_LIMIT.getCode()+"_"+propertyEnum.getCode());
        }
    	if(CollectionUtils.isEmpty(configRows)) {
    		return null;
    	}
        AdjustLimitConfigRow limitObj = configRows.stream().filter(configRow -> configRow.getBizCode().contains(covidtype)).findFirst().orElse(null);
        if(null==limitObj) {
        	return null;
        }
        Integer bizOption = limitObj.getBizOption();
    	return checkLimitStatus(storeDetailList, orderDetail,bizOption);
    }

	/**
     * 检查是否商品属性受限
     * @param goodsNo
     * @param businessId
     * @return
     */
    private GoodsLimitStatusEnum checkGoodsProperty(Map<Long, String> pushLevelsMap, Long businessId,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
    		Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap,ControlGoodsPropertyEnum propertyEnum) {
    	//List<AdjustLimitConfigRow> configRows = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.COVID_TYPE_LIMIT, businessId,covidtype);
    	String goodsProperty = pushLevelsMap.get(businessId);
    	if(StringUtils.isBlank(goodsProperty)) {
			return null;
		}
		List<AdjustLimitConfigRow> configRows = null;
    	if(limitLevel==AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()) {
    		List<AdjustLimitConfigRow> configRowList = erpBizSupportExtService.queryAdjustLimitConfig(AdjustLimitConfigEnum.GOODS_TAG_PROPERTY_LIMIT, businessId,goodsProperty);
    		Map<Long, List<AdjustLimitConfigRow>> businessLimitConfigs = configRowList.stream().collect(Collectors.groupingBy(e -> e.getBusinessId()));
    		configRows = businessLimitConfigs.get(Long.valueOf(AdjustLimitLevelEnum.LIMITLEVEL_GROUP.getCode()));
    		limitConfigRowMap.put(businessId+"_"+AdjustLimitConfigEnum.GOODS_TAG_PROPERTY_LIMIT.getCode()+"_"+propertyEnum.getCode(), businessLimitConfigs.get(businessId));
        }else {
        	configRows = limitConfigRowMap.get(businessId+"_"+AdjustLimitConfigEnum.GOODS_TAG_PROPERTY_LIMIT.getCode()+"_"+propertyEnum.getCode());
        }
    	if(CollectionUtils.isEmpty(configRows)) {
    		return null;
    	}
        AdjustLimitConfigRow limitObj = configRows.stream().filter(configRow -> configRow.getBizCode().contains(goodsProperty)).findFirst().orElse(null);
        if(null==limitObj) {
        	return null;
        }
        Integer bizOption = limitObj.getBizOption();
    	return checkLimitStatus(storeDetailList, orderDetail,bizOption);
    }

}
