package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.cowell.pricecenter.cache.CacheVar;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.AdjustPriceOrderDetail;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail;
import com.cowell.pricecenter.entity.PriceChannel;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.AdjustPriceOrderDetailBaseColumnEnum;
import com.cowell.pricecenter.enums.AdjustPriceOrderOfflineEffectStatusEnum;
import com.cowell.pricecenter.enums.AdjustTypeEnum;
import com.cowell.pricecenter.enums.DealStepStatusEnum;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.PriceFlagEnum;
import com.cowell.pricecenter.enums.PriceManageStatusApiEnum;
import com.cowell.pricecenter.enums.PriceTypeModeEnum;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderDetailExMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgStoreDetailExtMapper;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.AdjustPriceOrderOrgStoreDetailService;
import com.cowell.pricecenter.service.IAdjustOrderPriceHistoryService;
import com.cowell.pricecenter.service.IAdjustPriceOrderDetailV2ReadService;
import com.cowell.pricecenter.service.IAdjustPriceOrderDetailV2Service;
import com.cowell.pricecenter.service.IAdjustPriceOrderV2Service;
import com.cowell.pricecenter.service.IDownloadFromRedisCacheService;
import com.cowell.pricecenter.service.IPriceTypeService;
import com.cowell.pricecenter.service.PriceChannelService;
import com.cowell.pricecenter.service.PriceManageControlOrderReadService;
import com.cowell.pricecenter.service.PriceQueryService;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderDetailExtend1;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderOrgStoreDetailExtend;
import com.cowell.pricecenter.service.dto.MdmStoreBaseDTO;
import com.cowell.pricecenter.service.dto.OrgToRedisDTO;
import com.cowell.pricecenter.service.dto.request.AdjustOrderPriceHistoryExportParam;
import com.cowell.pricecenter.service.dto.request.AdjustPriceHistoryRecordDTO;
import com.cowell.pricecenter.service.dto.request.AmisPageParam;
import com.cowell.pricecenter.service.dto.request.ControlOrderStatusParam;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.AdjustOrderPriceHistoryDTO;
import com.cowell.pricecenter.service.dto.response.AdjustPriceOrderDetailIdDTO;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.feign.ErpBizSupportService;
import com.cowell.pricecenter.utils.PriceUtil;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RBuckets;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Service;

/**
 * @program: pricecenter
 * @description: 调价单调价历史服务
 * @author: jmlu
 * @create: 2022-09-19 17:08
 **/

@Service
public class AdjustOrderPriceHistoryServiceImpl implements IAdjustOrderPriceHistoryService {

    private final Logger logger = LoggerFactory.getLogger(AdjustOrderPriceHistoryServiceImpl.class);

    @Autowired
    private IAdjustPriceOrderV2Service adjustPriceOrderV2Service;
    @Autowired
    private AdjustPriceOrderOrgStoreDetailService orgStoreDetailService;
    @Autowired
    private PriceChannelService priceChannelService;
    @Autowired
    private IPriceTypeService priceTypeService;
    @Autowired
    private IAdjustPriceOrderDetailV2ReadService adjustPriceOrderDetailV2ReadService;

    @Autowired
    private IDownloadFromRedisCacheService downloadFromRedisCacheService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private PriceQueryService priceQueryService;
    @Autowired
    private ErpBizSupportService erpBizSupportService;
    @Autowired
    private AdjustPriceOrderOrgStoreDetailExtMapper adjustPriceOrderOrgStoreDetailExtMapper;
    @Autowired
    private PriceManageControlOrderReadService priceManageControlOrderReadService;
    @Autowired
    private AdjustPriceOrderDetailExMapper adjustPriceOrderDetailExMapper;
    @Autowired
    private IAdjustPriceOrderDetailV2Service adjustPriceOrderDetailV2Service;

    private static final int ADJUST_ORDER_STORE_DETAIL_PAGE_SIZE = 100;

    private static final int ADJUST_PRICE_ORDER_PAGE_SIZE = 10;

    @Override
    @NewSpan
    public void initAdjustOrderPriceHistoryCache(AdjustOrderPriceHistoryExportParam param, TokenUserDTO userDTO) {
        if (StringUtils.isEmpty(param.getStartAndEndTime()) || CollectionUtils.isEmpty(param.getStoreOrgIds())
            || StringUtils.isEmpty(param.getDownloadCode())
        ) {
            logger.info("AdjustOrderPriceHistoryServiceImpl|cacheAdjustOrderPriceHistory| 参数错误, downloadCode: {}", param.getDownloadCode());
            return;
        }
        initAdjustOrderPriceHistoryExportParam(param);
        Map<String, PriceType> userPriceTypeMap = priceTypeService.getUserPriceTypeMap(userDTO.getUserId());
        Map<Integer, PriceChannel> userPriceChannelMap = priceChannelService.getUserChannelMap(userDTO.getUserId());

        if (CollectionUtils.isEmpty(param.getStoreOrgIds())) {
            logger.info("AdjustOrderPriceHistoryServiceImpl|cacheAdjustOrderPriceHistory| 参数错误, 门店个数不能为空");
            return;
        }
        //此处过滤虚拟门店即sapcode为空的
        Iterator<Long> iterator = param.getStoreOrgIds().iterator();
        while(iterator.hasNext()) {
        	Long storeOrgId = iterator.next();
        	Long storeId = CacheVar.storeOrgIdAndOutIdMapping.get(storeOrgId);
        	if(null==storeId) {
        		continue;
        	}
        	OrgToRedisDTO orgToRedisDTO = CacheVar.storeCacheMap.get(storeId);
        	if(null!=orgToRedisDTO && StringUtils.isBlank(orgToRedisDTO.getSapCode())) {
        		iterator.remove();
        	}
        }
        if(CollectionUtils.isEmpty(param.getStoreOrgIds())) {
            logger.info("AdjustOrderPriceHistoryServiceImpl|initAdjustOrderPriceHistoryCache|过滤虚拟门店后 门店id为空 不查询数据");
            return;
        }
        //所有数据分页的当前使用页
        int curTotalPage = 1;
        int adjustOrderPage = 1;
        try {
			List<AdjustPriceOrder> adjustOrderList = listEffectAdjustOrdersByEffectTime(param.getAdjustType(), param.getStartTime(), param.getEndTime(), adjustOrderPage, ADJUST_PRICE_ORDER_PAGE_SIZE);
			while(CollectionUtils.isNotEmpty(adjustOrderList)) {
			    for (AdjustPriceOrder adjustPriceOrder : adjustOrderList) {
			        curTotalPage = cacheOneAdjustOrderPriceHistory(curTotalPage, param.getDownloadCode(), adjustPriceOrder, param.getStoreOrgIds(), param.getGoodsnoList(), userPriceTypeMap, userPriceChannelMap);
			    }
			    adjustOrderList = listEffectAdjustOrdersByEffectTime(param.getAdjustType(), param.getStartTime(), param.getEndTime(), ++adjustOrderPage, ADJUST_PRICE_ORDER_PAGE_SIZE);
			}
		} catch (Exception e) {
			logger.error("initAdjustOrderPriceHistoryCache|异常",e);
		}
        downloadFromRedisCacheService.cacheDownloadPageCount(RedisKeysConstant.ADJUST_PRICE_STORE_HISTORY, param.getDownloadCode(), --curTotalPage);
        logger.info("AdjustOrderPriceHistoryServiceImpl|cacheAdjustOrderPriceHistory| downloadCode:{}, 总页数: {}", param.getDownloadCode(), curTotalPage);
    }

    /**
     * 拆解查询的开始结束时间，拆解商品编码列表
     * @param param
     */
    private void initAdjustOrderPriceHistoryExportParam(AdjustOrderPriceHistoryExportParam param) {
        if (StringUtils.isNotBlank(param.getStartAndEndTime())) {
            String[] times = param.getStartAndEndTime().split(",");
            if (StringUtils.isNotBlank(times[0])) {
                LocalDate startDate = LocalDate.parse(times[0], DateTimeFormatter.ISO_DATE);
                param.setStartTime(LocalDateTime.of(startDate, LocalTime.MIN));
            }
            if (StringUtils.isNotBlank(times[1])) {
                LocalDate endDate = LocalDate.parse(times[1], DateTimeFormatter.ISO_DATE);
                param.setEndTime(LocalDateTime.of(endDate, LocalTime.MAX));
            }
        }
        if (StringUtils.isNotBlank(param.getGoodsnoes())) {
            param.setGoodsnoList(Lists.newArrayList(StringUtils.split(param.getGoodsnoes(),",")));
        }
    }

    @Override
    public PageResult<AdjustOrderPriceHistoryDTO> listAdjustOrderPriceHistoryFromCache(String downloadCode, Integer page) {
        PageResult<AdjustOrderPriceHistoryDTO> pageResult = new PageResult<>();

        String cacheKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.ADJUST_PRICE_STORE_HISTORY + downloadCode + "_" + page;
        RBucket<List<AdjustOrderPriceHistoryDTO>> priceHistoryDTOCache = redissonClient.getBucket(cacheKey);
        List<AdjustOrderPriceHistoryDTO>  priceHistoryDTOS = priceHistoryDTOCache.get();

        if (CollectionUtils.isNotEmpty(priceHistoryDTOS)) {
            pageResult.setRows(priceHistoryDTOS);
            pageResult.setPagePrepared(true);
            priceHistoryDTOCache.deleteAsync();
        } else {
            pageResult.setPagePrepared(false);
        }
        Integer pageCount = downloadFromRedisCacheService.getDownloadPageCount(RedisKeysConstant.ADJUST_PRICE_STORE_HISTORY, downloadCode);
        if (pageCount != null && page >= pageCount) {
            pageResult.setFinished(true);
        }
        return pageResult;
    }

    /**
     * 缓存每个调价单的门店调价数据,返回下一个页数
     * @param curTotalPage
     * @param downloadCode
     * @param adjustPriceOrder
     * @param storeOrgIds
     * @param userPriceTypeMap
     * @param userPriceChannelMap
     */
    private int cacheOneAdjustOrderPriceHistory(int curTotalPage, String downloadCode, AdjustPriceOrder adjustPriceOrder, List<Long> storeOrgIds,
        List<String> goodsnoList, Map<String, PriceType> userPriceTypeMap, Map<Integer, PriceChannel> userPriceChannelMap) {
        int orderStoreDetailPage = 1;
        List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList = orgStoreDetailService.listByAdjustCodeAndStoreOrgIdsAndGoodnoes(
            adjustPriceOrder.getAdjustCode(), storeOrgIds, goodsnoList, orderStoreDetailPage++, ADJUST_ORDER_STORE_DETAIL_PAGE_SIZE);
        while(CollectionUtils.isNotEmpty(orderOrgStoreDetailList)) {
            List<Long> orderDetailIdList = getAdjustOrderDetailIdList(orderOrgStoreDetailList);
            Map<Long, AdjustPriceOrderDetail> adjustPriceOrderDetailMap = adjustPriceOrderDetailV2ReadService.getMapByAdjustCodeAndDetailIds(
                adjustPriceOrder.getAdjustCode(), orderDetailIdList);
            boolean cacheResult = cacheOneAdjustOrderPriceHistoryPage(curTotalPage, downloadCode, orderOrgStoreDetailList, adjustPriceOrderDetailMap, adjustPriceOrder,
               userPriceTypeMap, userPriceChannelMap);
            if (cacheResult) {
                curTotalPage++;
            }
            orderOrgStoreDetailList = orgStoreDetailService.listByAdjustCodeAndStoreOrgIdsAndGoodnoes(
                adjustPriceOrder.getAdjustCode(), storeOrgIds, goodsnoList, orderStoreDetailPage++, ADJUST_ORDER_STORE_DETAIL_PAGE_SIZE);
        }
        return curTotalPage;
    }

    /**
     * 获取去重后调价单明细的Id列表
     * @param orderOrgStoreDetailList
     * @return
     */
    private List<Long> getAdjustOrderDetailIdList(List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList) {
        return orderOrgStoreDetailList.stream()
            .map(orderOrgStoreDetail -> {
                    Long adjustDetailId = null;
                    Optional<AdjustPriceOrderOrgStoreDetailExtend> detailExtendOptional = AdjustPriceOrderOrgStoreDetailExtend.getInstance(orderOrgStoreDetail.getExtend());
                    if (detailExtendOptional.isPresent()) {
                        AdjustPriceOrderOrgStoreDetailExtend detailExtend = detailExtendOptional.get();
                        adjustDetailId = detailExtend.getAdjustDetailId();
                    }
                    return adjustDetailId;
            })
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    }

    /**
     * 分页缓存每个调价单的门店调价数据
     * @param curTotalPage
     * @param downloadCode
     * @param orderOrgStoreDetailList
     * @param adjustPriceOrderDetailMap
     * @param adjustPriceOrder
     * @param userPriceTypeMap
     * @param userPriceChannelMap
     */
    private boolean cacheOneAdjustOrderPriceHistoryPage(int curTotalPage, String downloadCode, List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList,
        Map<Long, AdjustPriceOrderDetail> adjustPriceOrderDetailMap, AdjustPriceOrder adjustPriceOrder,
        Map<String, PriceType> userPriceTypeMap, Map<Integer, PriceChannel> userPriceChannelMap
    ) {
    	cacheStoreStatus(orderOrgStoreDetailList);
        List<AdjustOrderPriceHistoryDTO> adjustOrderPriceHistoryDTOS = orderOrgStoreDetailList.stream()
            .map(orderOrgStoreDetail ->
                toAdjustOrderPriceHistoryDTO(orderOrgStoreDetail, adjustPriceOrderDetailMap, adjustPriceOrder, userPriceTypeMap, userPriceChannelMap)
            )
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        logger.info("AdjustOrderPriceHistoryServiceImpl|cacheOneAdjustOrderPriceHistoryPage| downloadCode:{}, 第{}页, 数量: {}",
            downloadCode, curTotalPage, adjustOrderPriceHistoryDTOS.size());
        boolean cacheResult = false;
        if (adjustOrderPriceHistoryDTOS.size() > 0) {
            String cacheKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.ADJUST_PRICE_STORE_HISTORY + downloadCode + "_" + curTotalPage;
            RBucket<List<AdjustOrderPriceHistoryDTO>> priceHistoryDTOCache = redissonClient.getBucket(cacheKey);
            priceHistoryDTOCache.set( adjustOrderPriceHistoryDTOS, 10, TimeUnit.MINUTES);
            cacheResult = true;
        }
        return cacheResult;
    }
    
    /**
     * 缓存经营状态
     * @param cacheStoreStatus
     */
    private void cacheStoreStatus(List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList) {
		String[] storeIdArray = orderOrgStoreDetailList.stream().map(x -> RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.STORE_STATUS_KEY+x.getStoreId()).toArray(String[]::new);
	    RBuckets buckets = redissonClient.getBuckets();
	    Map<String,MdmStoreBaseDTO> redisMap = buckets.get(storeIdArray);
	    List<AdjustPriceOrderOrgStoreDetail> hisList = orderOrgStoreDetailList;
	    if(MapUtils.isNotEmpty(redisMap)) {
	    	List<Long> cacheStoreIdList = redisMap.values().stream().map(MdmStoreBaseDTO::getStoreId).collect(Collectors.toList());
		    hisList = orderOrgStoreDetailList.stream().filter(x -> !cacheStoreIdList.contains(x.getStoreId())).collect(Collectors.toList());
		    if(CollectionUtils.isEmpty(hisList)) {
		    	return;
		    }
	    }
		List<Long> noCacheStoreIdList = hisList.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).distinct().collect(Collectors.toList());
	    Map<Long, MdmStoreBaseDTO> mdmStoreMap = priceQueryService.getStoreInfo(noCacheStoreIdList);
	    mdmStoreMap.forEach((key, value) -> {
	    	redissonClient.getBucket(RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.STORE_STATUS_KEY+key).set(value,10,TimeUnit.MINUTES);
	    });
    }

    private AdjustOrderPriceHistoryDTO toAdjustOrderPriceHistoryDTO(AdjustPriceOrderOrgStoreDetail orderOrgStoreDetail,
        Map<Long, AdjustPriceOrderDetail> adjustPriceOrderDetailMap, AdjustPriceOrder adjustPriceOrder,
        Map<String, PriceType> userPriceTypeMap, Map<Integer, PriceChannel> userPriceChannelMap
    ) {
        PriceType priceType = userPriceTypeMap.get(orderOrgStoreDetail.getPriceTypeCode());
        PriceChannel priceChannel = userPriceChannelMap.get(orderOrgStoreDetail.getChannelId());
        if (priceChannel == null || priceType == null) {
            return null;
        }

        Optional<AdjustPriceOrderOrgStoreDetailExtend> orgStoreDetailExtendOptional = AdjustPriceOrderOrgStoreDetailExtend.getInstance(orderOrgStoreDetail.getExtend());
        AdjustOrderPriceHistoryDTO adjustOrderPriceHistoryDTO = null;
        if (orgStoreDetailExtendOptional.isPresent()) {
            AdjustPriceOrderOrgStoreDetailExtend orgStoreDetailExtend = orgStoreDetailExtendOptional.get();
            Long adjustDetailId = orgStoreDetailExtend.getAdjustDetailId();
            if (adjustDetailId != null) {
                adjustOrderPriceHistoryDTO = new AdjustOrderPriceHistoryDTO();
                AdjustPriceOrderDetail adjustPriceOrderDetail = adjustPriceOrderDetailMap.get(adjustDetailId);
                adjustOrderPriceHistoryDTO.setGoodsNo(adjustPriceOrderDetail.getGoodsNo());
                adjustOrderPriceHistoryDTO.setGoodsName(adjustPriceOrderDetail.getGoodsName());
                adjustOrderPriceHistoryDTO.setPriceSignName(PriceFlagEnum.getByCode(adjustPriceOrderDetail.getPriceFlag()).getName());
                BigDecimal newPrice = PriceUtil.getYuanFromFenWithNull(adjustPriceOrderDetail.getPrice());
                adjustOrderPriceHistoryDTO.setNewPrice(newPrice == null ? "无" : newPrice.toPlainString());
                adjustOrderPriceHistoryDTO.setAdjustReason(adjustPriceOrderDetail.getReason());
                BigDecimal oldPrice = PriceUtil.getYuanFromFenWithNull(orgStoreDetailExtend.getAdjustBeforePrice());
                adjustOrderPriceHistoryDTO.setOldPrice(oldPrice == null ? "无" : oldPrice.toPlainString());

                adjustOrderPriceHistoryDTO.setChannelReceiveStatus(orgStoreDetailExtend.getSendStatus());
                String receiveMsg = getChannelMsg(orgStoreDetailExtend.getSendStatus(), orgStoreDetailExtend.getSendMsg());
                adjustOrderPriceHistoryDTO.setChannelReceiveMsg(receiveMsg);
                adjustOrderPriceHistoryDTO.setChannelUpdateStatus(orgStoreDetailExtend.getReceiveStatus());
                String updateMsg = getChannelMsg(orgStoreDetailExtend.getReceiveStatus(), orgStoreDetailExtend.getReceiveMsg());
                adjustOrderPriceHistoryDTO.setChannelUpdateMsg(updateMsg);
                adjustOrderPriceHistoryDTO.setChannelEffectStatus(orgStoreDetailExtend.getEffectStatus());
                String effectMsg = getChannelMsg(orgStoreDetailExtend.getEffectStatus(), orgStoreDetailExtend.getEffectMsg());
                adjustOrderPriceHistoryDTO.setChannelEffectMsg(effectMsg);

                adjustOrderPriceHistoryDTO.setChannelEffectTime(orgStoreDetailExtend.getEffectTime());

                //OrgToRedisDTO businessCache = CacheVar.businessCacheMap.get(orderOrgStoreDetail.getBusinessId());
                //OrgToRedisDTO storeCache = CacheVar.storeCacheMap.get(orderOrgStoreDetail.getStoreId());
                Optional<AdjustTypeEnum> adjustTypeEnumOptional = AdjustTypeEnum.getByCode(adjustPriceOrder.getAdjustType());

                adjustOrderPriceHistoryDTO.setBusinessCode(CacheVar.businessCacheMap.get(orderOrgStoreDetail.getBusinessId()) != null ? StringUtils.stripToEmpty(CacheVar.businessCacheMap.get(orderOrgStoreDetail.getBusinessId()).getSapCode()) : "");
                adjustOrderPriceHistoryDTO.setBusinessName(CacheVar.businessCacheMap.get(orderOrgStoreDetail.getBusinessId()) != null ? StringUtils.stripToEmpty(CacheVar.businessCacheMap.get(orderOrgStoreDetail.getBusinessId()).getShortName()) :
                    orderOrgStoreDetail.getBusinessName());
                adjustOrderPriceHistoryDTO.setStoreNo(CacheVar.storeCacheMap.get(orderOrgStoreDetail.getStoreId()) != null ? StringUtils.stripToEmpty(CacheVar.storeCacheMap.get(orderOrgStoreDetail.getStoreId()).getSapCode()) : "");
                adjustOrderPriceHistoryDTO.setStoreName(CacheVar.storeCacheMap.get(orderOrgStoreDetail.getStoreId()) != null ? StringUtils.stripToEmpty(CacheVar.storeCacheMap.get(orderOrgStoreDetail.getStoreId()).getShortName()) :
                    orderOrgStoreDetail.getStoreName());
                adjustOrderPriceHistoryDTO.setChannelName(priceChannel.getChannelName());
                adjustOrderPriceHistoryDTO.setPriceTypeName(priceType.getName());
                adjustOrderPriceHistoryDTO.setAdjustCode(adjustPriceOrder.getAdjustCode());
                adjustOrderPriceHistoryDTO.setAdjustName(adjustPriceOrder.getAdjustName());
                if (adjustTypeEnumOptional.isPresent()) {
                    adjustOrderPriceHistoryDTO.setAdjustTypeName(adjustTypeEnumOptional.get().getMessageV2());
                }
                if (adjustPriceOrder.getEffectTime() != null) {
                    adjustOrderPriceHistoryDTO.setEffectTime(DateUtils.dateToString(adjustPriceOrder.getEffectTime()));
                }
                if (adjustPriceOrder.getScheduledTime() != null) {
                    adjustOrderPriceHistoryDTO.setScheduledTime(DateUtils.dateToString(adjustPriceOrder.getScheduledTime(), DateUtils.DATE_FORMAT_YYYYMMDD));
                }
                int offlineEffectStatus = adjustPriceOrder.getOfflineEffectStatus() == null ? -1 : adjustPriceOrder.getOfflineEffectStatus();
                adjustOrderPriceHistoryDTO.setOfflineEffectStatusName(AdjustPriceOrderOfflineEffectStatusEnum.getName(offlineEffectStatus));
                adjustOrderPriceHistoryDTO.setCreateUserName(adjustPriceOrder.getCreatedByName());
                String cacheKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.STORE_STATUS_KEY + orderOrgStoreDetail.getStoreId();
                RBucket<MdmStoreBaseDTO> bucket = redissonClient.getBucket(cacheKey);
                if(bucket.isExists()) {
                	MdmStoreBaseDTO mdmStoreBase = bucket.get();
                	adjustOrderPriceHistoryDTO.setStoreStatus(null==mdmStoreBase?"":mdmStoreBase.getStoreStatus());
                }
            }
        }


        return adjustOrderPriceHistoryDTO;
    }

    private String getChannelMsg(Integer status, String msg) {
        String resultMsg = "";
        if (StringUtils.isNotEmpty(msg)) {
            resultMsg = msg;
        } else if (StringUtils.isEmpty(msg) && status != null) {
            resultMsg = DealStepStatusEnum.getName(status);
        }
        return resultMsg;
    }

    private List<AdjustPriceOrder> listEffectAdjustOrdersByEffectTime(Integer adjustType,LocalDateTime startTime, LocalDateTime endTime, int page, int pageSize) {
        return adjustPriceOrderV2Service.listEffectAdjustOrdersByEffectTime(adjustType,startTime, endTime, page, pageSize);
    }

	@Override
	public void priceHistorySummaryData(String startTimeStr,String endTimeStr) {
		ControlOrderStatusParam param = new ControlOrderStatusParam();
		param.setApiCode(PriceManageStatusApiEnum.ALL_PRICE_TYPE.getCode());
		List<OptionDto> priceTypeCodeList = priceManageControlOrderReadService.controlSelectUnifyList(param);
		Map<String, OptionDto> priceTypeCodeMap = priceTypeCodeList.stream().collect(Collectors.toMap(OptionDto::getValue, Function.identity(), (v1, v2) -> v1));
		LocalDateTime startTime = null;
		LocalDateTime endTime = null;
		if(StringUtils.isNotBlank(startTimeStr) && StringUtils.isNotBlank(endTimeStr)) {
			LocalDate startDate = LocalDate.parse(startTimeStr, DateTimeFormatter.ISO_DATE);
            startTime = LocalDateTime.of(startDate, LocalTime.MIN);
			LocalDate endDate = LocalDate.parse(endTimeStr, DateTimeFormatter.ISO_DATE);
            endTime = LocalDateTime.of(endDate, LocalTime.MAX);
		}else {
			startTime = LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MIN);
			endTime = LocalDateTime.of(LocalDate.now().plusDays(-1), LocalTime.MAX);
		}
		logger.info("priceHistorySummaryData|价格调整记录汇总|开始时间:{},结束时间:{}",startTime,endTime);
		List<AdjustPriceOrder> priceOrderList = Lists.newArrayList();
		int pageSize = 100;
		for (int i = 1; ; i++) {
			priceOrderList.clear();
			//查询符合条件的调价单号
			priceOrderList = listEffectAdjustOrdersByEffectTime(null, startTime, endTime, i, pageSize);
			if (CollectionUtils.isEmpty(priceOrderList)) {
                logger.info("第{}次查询返回为空, 结束", i);
                break;
            }
			//根据单号统计明细
			List<AdjustPriceHistoryRecordDTO> recordList = Lists.newArrayList();
			for (AdjustPriceOrder adjustPriceOrder : priceOrderList) {
				recordList.clear();
				erpBizSupportService.deleBatchByAdjustCode(adjustPriceOrder.getAdjustCode());
				recordList = createAdjustPriceHistoryRecordDTO(adjustPriceOrder,priceTypeCodeMap);
				if(CollectionUtils.isEmpty(recordList)) {
					continue;
				}
				logger.info("价格调整汇总记录数据量|adjustCode:{},recordSize:{}",adjustPriceOrder.getAdjustCode(),recordList.size());
				com.google.common.collect.Lists.partition(recordList, 200).forEach(subList -> erpBizSupportService.saveAdjustPriceHistoryRecord(subList));
			}
		}
	}
	
	private List<AdjustPriceHistoryRecordDTO> createAdjustPriceHistoryRecordDTO(AdjustPriceOrder order,Map<String, OptionDto> priceTypeCodeMap){
		List<AdjustPriceHistoryRecordDTO> recordList = Lists.newArrayList();
		List<String> filterPriceTypeList = Lists.newArrayList(PriceTypeModeEnum.CLJ.getPriceTypeCode(),PriceTypeModeEnum.CHYJ.getPriceTypeCode(),PTypeEnum.GJYJS.getCode());
		AmisPageParam pageParam = null;
		int pageSize = 200;
		for (int i = 1; ; i++) {
			pageParam = AmisPageParam.createInstance(i, pageSize);
			List<Long> orgStoreDetailIdList = adjustPriceOrderOrgStoreDetailExtMapper.listIdByAdjustCodeAndStoreOrgIdsAndGoodnoes(order.getAdjustCode(), null, null,filterPriceTypeList,true, pageParam.getSize(), pageParam.getOffset());
	        if(CollectionUtils.isEmpty(orgStoreDetailIdList)) {
	        	break;
	        }
	        recordList.addAll(adjustPriceOrderOrgStoreDetailExtMapper.priceHistorySummaryData(order.getAdjustCode(), orgStoreDetailIdList));
		}
		for (AdjustPriceHistoryRecordDTO record : recordList) {
			record.setAdjustType(order.getAdjustType());
			record.setAdjustTypeName(AdjustTypeEnum.getNameV2(order.getAdjustType()));
			record.setEffectTime(order.getEffectTime());
			record.setPriceTypeName(null==priceTypeCodeMap.get(record.getPriceType())?"":priceTypeCodeMap.get(record.getPriceType()).getLabel());
			record.setAdjustName(order.getAdjustName());
		}
		//补充机构编码 连锁级别或者门店级别 
		List<Long> adjustDetailIdList = recordList.stream().map(AdjustPriceHistoryRecordDTO::getAdjustDetailId).distinct().collect(Collectors.toList());
		if(CollectionUtils.isEmpty(adjustDetailIdList)) {
			return Lists.newArrayList();
		}
		List<AdjustPriceOrderDetailIdDTO> orderDetailIdList = adjustPriceOrderDetailExMapper.selectAdjustPriceOrderDetailIdById(order.getAdjustCode(), adjustDetailIdList);
		if(CollectionUtils.isEmpty(orderDetailIdList)) {
			return Lists.newArrayList();
		}
		Map<String, Object> itemMap = new HashMap<String, Object>();
		for (AdjustPriceOrderDetailIdDTO orderDetail : orderDetailIdList) {
			Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
          	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = detailExtend1Optional.get();
          	itemMap.clear();
			itemMap.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_14.getName(), adjustPriceOrderDetailExtend1.getGoodsOrgTabType());
			itemMap.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_23.getName(), orderDetail.getExtend1());
			itemMap.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName(), orderDetail.getOrgIds());
			itemMap.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_17.getName(), orderDetail.getOrgLevels());
			adjustPriceOrderDetailV2Service.fillOrgIdsSapCode(itemMap);
			for (AdjustPriceHistoryRecordDTO record : recordList) {
				if(!record.getAdjustDetailId().equals(orderDetail.getId())) {
					continue;
				}
				if(null==itemMap.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName())) {
					logger.info("没有获取到orgId编码 adjustCode:{},detailId:{}",orderDetail.getAdjustCode(),orderDetail.getId());
					itemMap.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName(), "");
				}
				record.setComment(orderDetail.getReason());
				record.setGmtCreate(new Date());
				record.setGoodsOrgSapcode(itemMap.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName()).toString());
				record.setSkuName(orderDetail.getGoodsName()==null?"":orderDetail.getGoodsName());
				record.setSpecifications(orderDetail.getSpecifications());
				record.setManufacturer(orderDetail.getManufacturer());
				record.setUnit(orderDetail.getUnit());
				record.setCreatedBy(order.getCreatedByName());
			}
		}
		orderDetailIdList.clear();
		return recordList;
	}
}
