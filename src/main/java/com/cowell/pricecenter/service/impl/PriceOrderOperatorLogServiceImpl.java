package com.cowell.pricecenter.service.impl;

import com.cowell.permission.dto.EmployeeDetailDTO;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.entity.PriceOrderOperatorLog;
import com.cowell.pricecenter.entity.PriceOrderOperatorLogExample;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.PriceOrderOperatorLogMapper;
import com.cowell.pricecenter.service.IPermissionExtService;
import com.cowell.pricecenter.service.PriceOrderOperatorLogService;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.request.RequestBodyDTO;
import com.cowell.pricecenter.service.dto.response.OperatorLogDTO;
import com.cowell.pricecenter.service.feign.ErpSaasService;
import com.cowell.pricecenter.utils.JacksonUtil;
import com.cowell.pricecenter.utils.RequestHeaderContextUtils;
import com.cowell.pricecenter.utils.StreamUtils;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:06
 */

@Service
public class PriceOrderOperatorLogServiceImpl implements PriceOrderOperatorLogService {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private PriceOrderOperatorLogMapper priceOrderOperatorLogMapper;

    @Autowired
    private IPermissionExtService permissionExtService;

    @Autowired
    private ErpSaasService erpSaasService;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public long countByExample(PriceOrderOperatorLogExample example) {
        return priceOrderOperatorLogMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PriceOrderOperatorLogExample example) {
        return priceOrderOperatorLogMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return priceOrderOperatorLogMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PriceOrderOperatorLog record) {
        return priceOrderOperatorLogMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceOrderOperatorLog record) {
        return priceOrderOperatorLogMapper.insertSelective(record);
    }

    @Override
    public List<PriceOrderOperatorLog> selectByExample(PriceOrderOperatorLogExample example) {
        return priceOrderOperatorLogMapper.selectByExample(example);
    }

    @Override
    public PriceOrderOperatorLog selectByPrimaryKey(Integer id) {
        return priceOrderOperatorLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(PriceOrderOperatorLog record, PriceOrderOperatorLogExample example) {
        return priceOrderOperatorLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceOrderOperatorLog record, PriceOrderOperatorLogExample example) {
        return priceOrderOperatorLogMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceOrderOperatorLog record) {
        return priceOrderOperatorLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceOrderOperatorLog record) {
        return priceOrderOperatorLogMapper.updateByPrimaryKey(record);
    }


    @Override
    public List<OperatorLogDTO> getLogList(Integer orderType, String orderId) {
        PriceOrderOperatorLogExample example = new PriceOrderOperatorLogExample();
        example.createCriteria()
//            .andPriceOrderTypeEqualTo(orderType)
            .andOrderIdEqualTo(orderId);
        example.setOrderByClause("id desc");
        try {
            List<PriceOrderOperatorLog> logs = priceOrderOperatorLogMapper.selectByExample(example);
            return logs.stream().map(v -> {
                OperatorLogDTO operatorLogDTO = new OperatorLogDTO();
                BeanUtils.copyProperties(v, operatorLogDTO);
                String extend = v.getExtend();
                if (StringUtils.isNotBlank(extend)) {
                    OperatorLog operatorLog;
                    try {
                        operatorLog = JacksonUtil.getObjectMapper().readValue(extend, OperatorLog.class);
                    } catch (IOException e) {
                        logger.error("解析扩展字段异常", e);
                        return operatorLogDTO;
                    }
                    BeanUtils.copyProperties(operatorLog, operatorLogDTO);
                }
                operatorLogDTO.setOrderType(v.getPriceOrderType());
                operatorLogDTO.setRejectReason(v.getRejectReasion());
                operatorLogDTO.setOperateTime(Optional.ofNullable(operatorLogDTO.getOperateTime()).orElse(v.getGmtCreate()));
                return operatorLogDTO;
            }).collect(Collectors.toList())
                .stream().filter(StreamUtils.distinctByKey(v -> v.getNodeType() + "|" + v.getOperateTime())).collect(Collectors.toList());
//                .stream().sorted(Comparator.comparing(OperatorLogDTO::getNodeType, Comparator.reverseOrder()).thenComparing(OperatorLogDTO::getOperateTime, Comparator.reverseOrder())).collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("获取操作记录异常", e);
            throw e;
        }
    }

    @Override
    public boolean saveLog(OperatorLog operatorLog) {
        return saveLog(operatorLog, true);
    }

    private boolean saveLog(OperatorLog operatorLog, boolean isCheck) {
        try {
            if (isCheck) {
                checkOperatorLog(operatorLog);
            }

            if (Objects.equals(operatorLog.getNodeType(), OperateLogNodeEnum.NEW.getCode())) {
                PriceOrderOperatorLogExample example = new PriceOrderOperatorLogExample();
                example.createCriteria().andOrderIdEqualTo(operatorLog.getOrderId()).andPriceOrderTypeEqualTo(operatorLog.getOrderType());
                long count = priceOrderOperatorLogMapper.countByExample(example);
                if (count > 0) {
                    operatorLog.setNodeType(OperateLogNodeEnum.REJECTED_UPDATE.getCode());
                    operatorLog.setNodeName(OperateLogNodeEnum.REJECTED_UPDATE.getMessage());
                }
            }

            Date now = new Date();
            PriceOrderOperatorLog priceOrderOperatorLog = new PriceOrderOperatorLog();
            BeanUtils.copyProperties(operatorLog, priceOrderOperatorLog);
            priceOrderOperatorLog.setPriceOrderType(operatorLog.getOrderType());
            priceOrderOperatorLog.setOrderStatus(operatorLog.getOrderStatus().getCode());
            priceOrderOperatorLog.setRejectReasion(operatorLog.getRejectReason());
            priceOrderOperatorLog.setGmtCreate(now);
            priceOrderOperatorLog.setGmtUpdate(now);
            priceOrderOperatorLog.setExtend(JacksonUtil.getObjectMapper().writeValueAsString(operatorLog));
            priceOrderOperatorLogMapper.insertSelective(priceOrderOperatorLog);
        } catch (Exception e) {
            logger.error("saveLog|异常 <== {}", operatorLog, e);
            return false;
        }
        return true;
    }

    @Override
    public boolean pushToOA(OaTarget oaTarget) {
        try {
            checkOaTarget(oaTarget);

            OperatorLog operatorLog = new OperatorLog();
            BeanUtils.copyProperties(oaTarget, operatorLog);

            saveLog(operatorLog);

            OrderItemTypeEnum orderItemTypeEnum = PriceOrderTypeEnum.getOrderItemTypeEnum(oaTarget.getOrderItemType());

            oaTarget.setOrderItemTypeCode(null == orderItemTypeEnum ? null : orderItemTypeEnum.getCode());
            StaffNumDTO staffNumDTO = permissionExtService.getStaffNumByUserId(oaTarget.getOperatorUserId());
            oaTarget.setOperatorUserName(Optional.ofNullable(staffNumDTO).map(StaffNumDTO::getStaffNum).orElse(operatorLog.getOperatorUserName()));
            pushToErpSaas(oaTarget);
        } catch (BusinessErrorException e) {
            logger.error("pushToOA|异常 <== {}", oaTarget, e);
            throw e;
        } catch (Exception e) {
            logger.error("pushToOA|异常 <== {}", oaTarget, e);
            return false;
        }
        return true;
    }

    @Override
    public boolean acceptOA(OaSource oaSource) {
        // 流程创建成功: 不处理
        // 流程创建失败: 单据更新成审核驳回
        // 审批中: 不更新单据
        // 审核通过 && 审核驳回: 更新单据...
        Integer oaStatus = oaSource.getOaStatus();
        try {
            PriceOrderTypeEnum priceOrderTypeEnum = PriceOrderTypeEnum.getEnumByCode(oaSource.getOrderType());
            if (null == priceOrderTypeEnum) {
                logger.info("acceptOA|orderType={}不存在", oaSource.getOrderType());
                return false;
            }
            OaStatusEnum oaStatusEnum = OaStatusEnum.getEnum(oaStatus);
            if (null == oaStatusEnum) {
                logger.info("acceptOA|oaStatus={}不存在", oaStatus);
                return false;
            }
            if (oaStatusEnum.equals(OaStatusEnum.PROCESS_SUCCESS)) {
                logger.info("acceptOA|流程创建成功无需处理");
                return false;
            }

            OrderStatus orderStatus = PriceOrderTypeEnum.getOrderStatus(priceOrderTypeEnum, oaStatusEnum);
            if (null == orderStatus) {
                logger.info("acceptOA|没找到状态映射 <== oaStatus={}", oaStatusEnum);
                return false;
            }

            OperateLogNodeEnum nodeEnum = PriceOrderTypeEnum.getOperateLogNodeEnum(oaStatusEnum);

            oaSource.setOrderStatus(orderStatus);

            // 流程创建失败转换成审核驳回
            if (Objects.equals(oaSource.getOaStatus(), OaStatusEnum.PROCESS_FAIL.getCode())) {
                oaSource.setOrderStatus(PriceOrderTypeEnum.getOrderStatus(priceOrderTypeEnum, OaStatusEnum.AUDIT_REJECTED));
                oaSource.setOperateTime(new Date());
                oaSource.setOperatorUserId(Constants.DEFAULT_USER_ID);
                oaSource.setOperatorUserName(Constants.DEFAULT_USER_NAME);
                oaSource.setRejectReason(StringUtils.isNotBlank(oaSource.getProcessFailMessage())
                    ? oaSource.getProcessFailMessage() : OaStatusEnum.PROCESS_FAIL.getMessage());
            }

            OperatorLog operatorLog = new OperatorLog();
            BeanUtils.copyProperties(oaSource, operatorLog);
            operatorLog.setOaStatus(oaSource.getOaStatus());
            operatorLog.setOrderStatus(orderStatus);
            operatorLog.setOrderStatusCode(orderStatus.getCode());
            operatorLog.setOrderType(priceOrderTypeEnum.getCode());
            operatorLog.setOrderTypeEnum(priceOrderTypeEnum);
            operatorLog.setNodeType(nodeEnum.getCode());
            operatorLog.setNodeName(nodeEnum.getMessage());

            // 设置当前审批人
            List<OaAuditor> currAuditors = oaSource.getCurrAuditors();
            if (CollectionUtils.isNotEmpty(currAuditors)) {
                // 根据时间降序, 取第一条作为当前审批人
                currAuditors = currAuditors.stream().sorted(Comparator.comparing(OaAuditor::getAuditTime).reversed()).collect(Collectors.toList());
                OaAuditor currAuditor = currAuditors.get(0);

                Date auditTime = new Date();
                boolean auditTimeIsError = true;
                if (currAuditor.getAuditTime() != null) {
                    try {
                        auditTime = DateUtils.parseDate(currAuditor.getAuditTime(), "yyyy-MM-dd HH:mm:ss");
                        auditTimeIsError = false;
                    } catch (Exception e) {
                        //不做任何处理
                    }
                }
                if (auditTimeIsError) {
                    logger.info("acceptOA|审核时间数据有问题。auditTime: {}", currAuditor.getAuditTime());
                }

                operatorLog.setOperateTime(auditTime);
                operatorLog.setOperatorUserId(Constants.DEFAULT_USER_ID);
                operatorLog.setOperatorUserName(currAuditor.getAuditorName());
                operatorLog.setRejectReason(currAuditor.getAuditOpinion());

                oaSource.setOperateTime(auditTime);
                oaSource.setOperatorUserId(Constants.DEFAULT_USER_ID);
                oaSource.setOperatorUserName(currAuditor.getAuditorName());
                oaSource.setRejectReason(currAuditor.getAuditOpinion());
            }

            // 加锁
            RLock lock = redissonClient.getLock("PRICE-SAVE-OPERATOR-LOG-LOCK-" + oaSource.getOrderId());
            try {
                boolean isLocked = lock.tryLock(0, 10, TimeUnit.SECONDS);
                if (!isLocked) {
                    logger.debug("acceptOA|获取锁失败:{}", lock.getName());
                    return false;
                }
                // 幂等处理
                if (!idempotent(operatorLog)) {
                    logger.info("acceptOA|已存在相同数据,无需处理 <== orderId={}", oaSource.getOrderId());
                    return false;
                }
                saveLog(operatorLog, false);
            } finally {
                if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            logger.error("acceptOA|异常 <== {}", oaSource, e);
            return false;
        }
        return Objects.equals(oaStatus, OaStatusEnum.AUDIT_PASS.getCode())
            || Objects.equals(oaStatus, OaStatusEnum.AUDIT_REJECTED.getCode())
            || Objects.equals(oaStatus, OaStatusEnum.PROCESS_FAIL.getCode());
    }

    /**
     * 幂等处理(单号 + 节点 + 操作人 + 操作时间)
     *
     * @param operatorLog
     * @return
     */
    private boolean idempotent(OperatorLog operatorLog) {
        try {
            PriceOrderOperatorLogExample example = new PriceOrderOperatorLogExample();
            example.createCriteria().andOrderIdEqualTo(operatorLog.getOrderId());
            List<PriceOrderOperatorLog> orderOperatorLogs = priceOrderOperatorLogMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(orderOperatorLogs)) {
                for (PriceOrderOperatorLog orderOperatorLog : orderOperatorLogs) {
                    String extend = orderOperatorLog.getExtend();
                    if (StringUtils.isNotBlank(extend)) {
                        OperatorLog existLog = JacksonUtil.getObjectMapper().readValue(extend, OperatorLog.class);
                        if ((operatorLog.getUniqueKey()).equals(existLog.getUniqueKey())) {
                            logger.warn("idempotent|相同操作记录已存在 <== {}", existLog.getUniqueKey());
                            return false;
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("idempotent|error!", e);
        }
        return true;
    }

    private void checkOaTarget(OaTarget oaTarget) {
        if (null == oaTarget.getOrderTypeEnum()) {
            throw new BusinessErrorException("请传入单据类型");
        }
        if (null == oaTarget.getOrderItemType()) {
            throw new BusinessErrorException("请传入管理类型");
        }
        if (null == oaTarget.getOrderId()) {
            throw new BusinessErrorException("请传入单据编码");
        }
        if (StringUtils.isBlank(oaTarget.getOrderName())) {
            throw new BusinessErrorException("请传入单据名称");
        }
        if (null == oaTarget.getOperatorUserId()) {
            throw new BusinessErrorException("请传入操作人Id");
        }
        if (StringUtils.isBlank(oaTarget.getPcUrl())) {
            throw new BusinessErrorException("请传入pcUrl");
        }
        if (StringUtils.isBlank(oaTarget.getAppUrl())) {
            throw new BusinessErrorException("请传入appUrl");
        }
        if (null == oaTarget.getOperateTime()) {
            oaTarget.setOperateTime(new Date());
        }

        oaTarget.setOrderType(oaTarget.getOrderTypeEnum().getCode());
        oaTarget.setOrderItemTypeCode(oaTarget.getOrderItemType().getCode());
    }

    private void checkOperatorLog(OperatorLog operatorLog) {
        PriceOrderTypeEnum priceOrderTypeEnum = operatorLog.getOrderTypeEnum();
        if (null == priceOrderTypeEnum) {
            throw new BusinessErrorException("请传入单据类型");
        }
        if (null == operatorLog.getOrderStatus()) {
            throw new BusinessErrorException("请传入单据状态");
        }
        if (null == operatorLog.getOrderItemType()) {
            throw new BusinessErrorException("请传入管理类型");
        }
        if (null == operatorLog.getOrderId()) {
            throw new BusinessErrorException("请传入单据编码");
        }
        if (null == operatorLog.getOperatorUserId()) {
            throw new BusinessErrorException("请传入操作人Id");
        }
        if (StringUtils.isBlank(operatorLog.getOperatorUserName())) {
            throw new BusinessErrorException("请传入操作人名称");
        }
        if (null == operatorLog.getOperateTime()) {
            operatorLog.setOperateTime(new Date());
        }

        OperateLogNodeEnum nodeEnum = PriceOrderTypeEnum.getOperateLogNodeEnum(operatorLog.getOrderStatus());

        operatorLog.setOrderType(priceOrderTypeEnum.getCode());
        operatorLog.setOrderItemTypeCode(operatorLog.getOrderItemType().getCode());
        operatorLog.setOrderStatusCode(operatorLog.getOrderStatus().getCode());
        operatorLog.setNodeType(nodeEnum.getCode());
        operatorLog.setNodeName(nodeEnum.getMessage());
    }

    private EmployeeDetailDTO getEmpInfo(Long userId) {
        try {
            return permissionExtService.getEmployeeDetailByUserIdAndResourceId(userId, RequestHeaderContextUtils.getResourceId());
        } catch (Exception e) {
            logger.error("根据用户Id获取用户详细信息异常,userId={}", userId, e);
        }
        return null;
    }

    /**
     * 推送 erpSaas
     *
     * @param oaTarget
     */
    private void pushToErpSaas(OaTarget oaTarget) {
        RequestBodyDTO requestBodyDTO = null;
        String requestBody = null;
        try {
            String jsonData = JacksonUtil.getObjectMapper().writeValueAsString(oaTarget);

            requestBodyDTO = new RequestBodyDTO();
            requestBodyDTO.setAction("pushOrderToOA");
            requestBodyDTO.setAppName(Constants.APP_NAME.toLowerCase());
            requestBodyDTO.setBusinessId("99999");
            requestBodyDTO.setBdata(jsonData);
            requestBody = JacksonUtil.getObjectMapper().writeValueAsString(requestBodyDTO);
        } catch (JsonProcessingException e) {
            logger.error("pushToErpSaas|解析发送数据异常", e);
        }
        try {
            //获取发送给SAP的报文
            logger.info("pushToErpSaas|发送SAP报文给OA,requestBody:{}", requestBody);
            //发送sap报文
            ResponseEntity<String> responseEntity = erpSaasService.accept(requestBodyDTO);
            logger.info("pushToErpSaas|发送SAP报文给OA,statusCode:{}", responseEntity.getStatusCode());
        } catch (Exception e) {
            logger.error("pushToErpSaas|error!", e);
        }
    }

}
