package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.builder.dto.StoreChannelMappingDTO;
import com.cowell.pricecenter.config.AdjustPriceConfig;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.AdjustPriceOrderDetailMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.PriceStoreDetailMapper;
import com.cowell.pricecenter.mapper.extension.PriceStoreDetailExMapper;
import com.cowell.pricecenter.mq.producer.*;
import com.cowell.pricecenter.mq.vo.InitialzePriceMqVo;
import com.cowell.pricecenter.mq.vo.PricePushYJSVo;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskVO;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.apollo.SyncPriceChannelConfigProperties;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.response.PriceStoreDetailComparisonDTO;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import com.cowell.pricecenter.service.feign.BamFeignService;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.Assert;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cowell.pricecenter.platformapi.HttpRequestUrl.EB_SYNC_PRICE_GOODS_LIMIT;


@Service
public class PriceToolsServiceImpl extends BasePermissionService implements IPriceToolsService{

    private final Logger logger = LoggerFactory.getLogger(PriceToolsServiceImpl.class);
    @Autowired
    private AdjustPriceOrderMapper adjustPriceOrderMapper;
    @Autowired
    private AdjustPriceOrderDetailMapper adjustPriceOrderDetailMapper;
    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;
    @Autowired
    private PriceStoreDetailExMapper priceStoreDetailExMapper;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private PriceSyncBaseService priceSyncBaseService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private InitializePriceProducer initializePriceProducer;
    @Autowired
    private PricePushYJSPriceTypeToHLProducer pricePushYJSPriceTypeToHLProducer;
    @Autowired
    private BamFeignService bamFeignService;
    @Autowired
    private PriceSyncTaskProducer priceSyncTaskProducer;
    @Autowired
    private SyncPriceChannelConfigProperties syncPriceChannelConfigProperties;
    @Autowired
    private PricePushCubeProducer pricePushCubeProducer;

    @Autowired
    private AdjustPriceConfig adjustPriceConfig;

    @Autowired
    @Qualifier("taskExecutorTrace2")
    private AsyncTaskExecutor asyncTaskExecutor2;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    @Resource
    private FeignStoreService feignStoreService;

    @Resource
    private PriceChangeProducer priceChangeProducer;

    @Autowired
    private IPriceService priceService;

    @Override
    public boolean priceStoreDetailAndpriceDetailHistoryDiff(Long storeId) {
        String ptype = PTypeEnum.LSJ.getCode();

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .priceTypeCode(ptype)
            .adjustCodeLike("%2EXCEL%")
            .gmtUpdateGreaterThan(DateUtils.stringToDate("2019-04-01 00:00:00"))
            .build();

        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);
        logger.info("调价单上传数据结果源:{}",priceList);
        List<Map> rs = new ArrayList<>();
        for (PriceStoreDetail priceStoreDetail : priceList) {
            String adjustCode = priceStoreDetail.getAdjustCode();
            String goodsno = priceStoreDetail.getGoodsNo();
            String price = priceStoreDetail.getPrice().toString();
            Map<String,String> map = new HashMap();
            map.put("adjustCode",adjustCode);
            map.put("goodsno",goodsno);
            map.put("price",price);
            map.put("storeId",storeId+"");
            rs.add(map);
        }
        logger.info("调价单上传数据结果 :{}", JSON.toJSONString(rs));
        return true;
    }


    @Override
    public boolean priceStoreDetailAndAdjustPriceDiff(Long storeId) {
        String ptype = PTypeEnum.LSJ.getCode();

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .priceTypeCode(ptype)
            .build();

        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        for (PriceStoreDetail priceStoreDetail : priceList) {
            String adjustCode = priceStoreDetail.getAdjustCode();
            String goodsno = priceStoreDetail.getGoodsNo();
            AdjustPriceOrderDetailExample ee = new AdjustPriceOrderDetailExample();
            ee.createCriteria()
                .andAdjustCodeEqualTo(adjustCode)
                .andGoodsNoEqualTo(goodsno)
                .andPriceTypeCodeEqualTo(ptype);
            List<AdjustPriceOrderDetail> adjustPriceOrderDetaillist = adjustPriceOrderDetailMapper.selectByExample(ee);
            if (CollectionUtils.isNotEmpty(adjustPriceOrderDetaillist)) {
                for (AdjustPriceOrderDetail adjustPriceOrderDetail : adjustPriceOrderDetaillist) {
                    if (priceStoreDetail.getPrice().compareTo(adjustPriceOrderDetail.getPrice()) != 0) {
                        logger.info("调价单价格比对不一样的数据 priceStoreDetail:{},adjustPriceOrderDetail:{}", priceStoreDetail, adjustPriceOrderDetail);
                    }
                }

            }
        }
        return true;
    }

    @Override
    public CommonResponse comparisonPriceTypeDifference(Long businessId, String storeIds, String sourcePriceTypeCode, String targetPriceTypeCode) {

        //uuid 该任务的 唯一主键
        String uuid = UUID.randomUUID().toString();

        List<String> storeIdStr = Lists.newArrayList();

        if (org.apache.commons.lang3.StringUtils.isNotBlank(storeIds) ){
            String[] storeIdArr = storeIds.split(",");
            for (String storeId : storeIdArr) {
                InitialzePriceMqVo initialzePriceMqVo = new InitialzePriceMqVo();
                initialzePriceMqVo.setStoreId(Long.parseLong(storeId));
                initialzePriceMqVo.setSourcePriceTypeCode(sourcePriceTypeCode);
                initialzePriceMqVo.setTargetPriceTypeCode(targetPriceTypeCode);
                initialzePriceMqVo.setType(2);
                initialzePriceMqVo.setTaskId(uuid);
                initializePriceProducer.sendMq(initialzePriceMqVo);

                storeIdStr.add(storeId);
            }
        }else{
            //按连锁下的所有门店配置多个（一个门店配置一个）
            List<CrmStoreBusinessDTO> storeList = storeService.getCrmStoreByBusinessIds(new Long[]{businessId});

            List<CrmStoreDTO> crmStoreDTOList = storeList.get(0).getCrmStoreDTOList();

            for (CrmStoreDTO crmStoreDto : crmStoreDTOList) {

                InitialzePriceMqVo initialzePriceMqVo = new InitialzePriceMqVo();
                initialzePriceMqVo.setStoreId(crmStoreDto.getId());
                initialzePriceMqVo.setSourcePriceTypeCode(sourcePriceTypeCode);
                initialzePriceMqVo.setTargetPriceTypeCode(targetPriceTypeCode);
                initialzePriceMqVo.setType(2);
                initialzePriceMqVo.setTaskId(uuid);
                initializePriceProducer.sendMq(initialzePriceMqVo);
                storeIdStr.add(crmStoreDto.getId()+"");
            }
        }

        CommonResponse response = new CommonResponse();

        Map<String,Object> map = Maps.newHashMap();
        map.put("taskId",uuid);
        map.put("storeNumber",storeIdStr.size()+"");
        map.put("executeNumber",0);
        response.setResult(map);


        //放需要执行的门店ID 缓存
        RBucket<String> rBucketResponse = redissonClient.getBucket("COMPARISON_PRICE_DIFFERENCE_STORE_LIST_" +uuid );
        rBucketResponse.set(JSONObject.toJSONString(storeIdStr));
        rBucketResponse.expire(1L, TimeUnit.DAYS);

        return response;
    }

    @Override
    public CommonResponse comparisonPriceTypeTaskProgress(String taskId) {

        CommonResponse response = new CommonResponse();

        Map<String,Object> map = Maps.newHashMap();
        map.put("taskId",taskId);

        //放需要执行的门店ID 缓存
        RBucket<String> rBucketResponse = redissonClient.getBucket("COMPARISON_PRICE_DIFFERENCE_STORE_LIST_" +taskId );
        List<String> storeLists = JSONObject.parseArray(rBucketResponse.get(),String.class);
        map.put("storeNumber",storeLists.size()+"");

        //将门店执行完成数量 + 1
        RAtomicLong rAtomicLongAgain = redissonClient.getAtomicLong("COMPARISON_PRICE_DIFFERENCE_EXECUTE_NUMBER_"+taskId);
        map.put("executeNumber",rAtomicLongAgain.get());

        //放需要执行的门店ID 缓存
        RBucket<String> rBucketFileUrl = redissonClient.getBucket("COMPARISON_PRICE_DIFFERENCE_FILE_URL_" +taskId );
        map.put("downloadFileUrl",rBucketFileUrl.get());

        response.setResult(map);

        return response;
    }


    @Override
    public void doComparisonPriceTypeDifference(String taskId,Long storeId,String sourcePriceTypeCode, String targetPriceTypeCode){
        logger.info("执行门店价格对比 门店:{} 源价格:{} 目标价格:{}",storeId,sourcePriceTypeCode,targetPriceTypeCode);

        PriceStoreDetailComparisonDTO dto = new PriceStoreDetailComparisonDTO();
        dto.setStoreId(storeId);
        dto.setSourcePriceTypeCode(sourcePriceTypeCode);
        dto.setTargetPriceTypeCode(targetPriceTypeCode);

        List<PriceStoreDetailComparisonDTO>  dtoList = priceStoreDetailExMapper.comparisonPriceTypeDifference(dto);

        List<PriceStoreDetailComparisonDTO>  dtoListNew = Lists.newArrayList();
        for (PriceStoreDetailComparisonDTO item: dtoList) {

            if (item.getTargetPrice() == null
                || !item.getTargetPrice().equals(item.getSourcePrice())){
                dtoListNew.add(item);
            }
        }
        logger.info("单个门店价格对比完成,价格不一致的数量:{} 门店ID:{}",dtoListNew.size(),storeId);

        //将门店执行完成数量 + 1
        RAtomicLong rAtomicLongAgain = redissonClient.getAtomicLong("COMPARISON_PRICE_DIFFERENCE_EXECUTE_NUMBER_"+taskId);
        rAtomicLongAgain.expire(1L, TimeUnit.DAYS);
        rAtomicLongAgain.addAndGet(1L);

        //放执行的门店 价格异常的 缓存
        RBucket<String> rBucketResponse = redissonClient.getBucket("COMPARISON_PRICE_DIFFERENCE_GOODS_LIST_" +taskId+"_"+storeId );
        rBucketResponse.set(JSONObject.toJSONString(dtoListNew));
        rBucketResponse.expire(1L, TimeUnit.DAYS);

        //放执行的门店 价格异常的 缓存
        RBucket<String> rBucketGoodsTotal = redissonClient.getBucket("COMPARISON_PRICE_DIFFERENCE_GOODS_TOTAL_" +taskId+"_"+storeId );
        rBucketGoodsTotal.set(dtoList.size()+"");
        rBucketGoodsTotal.expire(1L, TimeUnit.DAYS);

        logger.info("对比完成！执行操作门店价格对比 门店:{} 源价格:{} 目标价格:{} ",storeId,sourcePriceTypeCode,targetPriceTypeCode);
    }

    @Override
    public boolean correctPriceStoreDetailAndAdjustPriceDiff(Long storeId,String priceTypeCode) {

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .priceTypeCode(priceTypeCode)
            .build();

        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);
        for (PriceStoreDetail priceStoreDetail : priceList) {
            String adjustCode = priceStoreDetail.getAdjustCode();
            String goodsno = priceStoreDetail.getGoodsNo();
            AdjustPriceOrderDetailExample ee = new AdjustPriceOrderDetailExample();
            ee.createCriteria()
                .andAdjustCodeEqualTo(adjustCode)
                .andGoodsNoEqualTo(goodsno)
                .andPriceTypeCodeEqualTo(priceTypeCode);
            List<AdjustPriceOrderDetail> adjustPriceOrderDetaillist = adjustPriceOrderDetailMapper.selectByExample(ee);
            if (CollectionUtils.isNotEmpty(adjustPriceOrderDetaillist)) {
                for (AdjustPriceOrderDetail adjustPriceOrderDetail : adjustPriceOrderDetaillist) {
                    if (priceStoreDetail.getPrice().compareTo(adjustPriceOrderDetail.getPrice()) != 0) {
                        PriceStoreDetail priceStoreDetailNew = new PriceStoreDetail();
                        priceStoreDetailNew.setPrice(adjustPriceOrderDetail.getPrice());
                        priceStoreDetailNew.setGmtUpdate(new Date());
                        priceStoreDetailNew.setVersion(PriceStoreDetailVersionEnum.ADJUST_CORRECT.getCode());

                        PriceStoreDetailExample example2 = new PriceStoreDetailExample();
                        PriceStoreDetailExample.Criteria criteria2 = example2.createCriteria();
                        criteria2.andStoreIdEqualTo(storeId);
                        criteria2.andPriceTypeCodeEqualTo(priceTypeCode);
                        criteria2.andGoodsNoEqualTo(goodsno);

                        int i = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetailNew, example2);
                        logger.info("调价单价格比对不一样的数据矫正 goodsNo:{},结果:{}", goodsno, i);
                    }
                }

            }
        }
        return true;
    }


    @Override
    public boolean changeAdjustPriceOrderStatus(String adjustCode, int auditStatus) {
        AdjustPriceOrder record = new AdjustPriceOrder();
        record.setAuditStatus((byte) auditStatus);
        AdjustPriceOrderExample example = new AdjustPriceOrderExample();
        AdjustPriceOrderExample.Criteria criteria = example.createCriteria();
        criteria.andAdjustCodeEqualTo(adjustCode);
        int i = adjustPriceOrderMapper.updateByExampleSelective(record, example);
        return i == 1;
    }


    @Override
    public CommonResponse initializePrice(Long businessId, String sourcePriceTypeCode, String targetPriceTypeCode) {
        try {
            logger.info("连锁价格初始化 {}", businessId);
            Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
            PriceType priceType = priceTypeMap.get(sourcePriceTypeCode);
            if (null == priceType) {
                return new CommonResponse(-1, sourcePriceTypeCode + "入参sourcePriceTypeCode对应的价格类型没找到");
            }
            InitialzePriceMqVo initialzePriceMqVo = new InitialzePriceMqVo();
            initialzePriceMqVo.setSourcePriceTypeCode(sourcePriceTypeCode);
            initialzePriceMqVo.setTargetPriceTypeCode(targetPriceTypeCode);

            priceType = priceTypeMap.get(targetPriceTypeCode);
            if (null == priceType) {
                return new CommonResponse(-1, targetPriceTypeCode + "入参targetPriceTypeCode对应的价格类型没找到");
            }
            initialzePriceMqVo.setTargetPriceType(priceType);

            //按连锁下的所有门店配置多个（一个门店配置一个）
            List<CrmStoreBusinessDTO> storeList = storeService.getCrmStoreByBusinessIds(new Long[]{businessId});
            logger.info("连锁价格初始化 获取对应的多个门店信息返回门店数量为{}", storeList.size());
            for (CrmStoreBusinessDTO dto : storeList) {
                List<CrmStoreDTO> crmStoreDTOList = dto.getCrmStoreDTOList();
                logger.info("连锁价格初始化 通过多个连锁ID，获取对应的多个门店信息crmStoreDTOList返回数量{}", crmStoreDTOList.size());
                for (CrmStoreDTO crmStoreDTO : crmStoreDTOList) {
                    long storeId = crmStoreDTO.getId();
                    initialzePriceMqVo.setStoreId(storeId);
                    initializePriceProducer.sendMq(initialzePriceMqVo);
                }
            }
        } catch (Exception e) {
            logger.info("连锁价格初始化 Exception", e);
        }
        logger.info("连锁价格初始化完成 {}", businessId);
        return new CommonResponse();
    }

    @Override
    public CommonResponse initializePriceByStoreId(Long businessId, Long storeId, String sourcePriceTypeCode, String targetPriceTypeCode) {
        logger.info("连锁指定门店价格类型初始化为目标价格类型,门店:{} 原类型:{} 目标类型:{}", storeId, sourcePriceTypeCode, targetPriceTypeCode);

        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();

        PriceType priceType = priceTypeMap.get(sourcePriceTypeCode);
        if (null == priceType) {
            return new CommonResponse(-1, sourcePriceTypeCode + "入参sourcePriceTypeCode对应的价格类型没找到");
        }

        priceType = priceTypeMap.get(targetPriceTypeCode);
        if (null == priceType) {
            return new CommonResponse(-1, targetPriceTypeCode + "入参targetPriceTypeCode对应的价格类型没找到");
        }

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .businessId(businessId)
            .priceTypeCode(sourcePriceTypeCode)
            .build();

        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        logger.info("连锁指定门店价格类型初始化为目标价格类型,门店原价格类型数量:{}", priceList.size());

        //进行价格 复制
        this.initializePriceByStoreId(priceList, targetPriceTypeCode);

        return new CommonResponse();
    }


    @Override
    public boolean initializeCenterStorePrice(Long businessId,Long storeId,Long channelStoreId,String priceTypeCode, Integer channelId) {
        try {

            StoreChannelMappingDTO dto = new StoreChannelMappingDTO();
            dto.setChannel(13);
            dto.setBindStatus(0);
            dto.setBusinessId(businessId);
            if (channelStoreId != null){
                dto.setChannelStoreId(String.valueOf(channelStoreId));
            }
            dto.setStoreId(storeId);

            ResponseEntity<List<StoreChannelMappingDTO>> rsp = storeService.getStoreChannelMappingByConditions(dto);
            if (null == rsp || HttpStatus.OK != rsp.getStatusCode()) {
                return false;
            }

            logger.info("中心店价格初始化 {}取中心店数据返回{}", storeId, rsp);
            Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
            PriceType priceType = priceTypeMap.get(priceTypeCode);

            List<StoreChannelMappingDTO> list = rsp.getBody();
            if (CollectionUtils.isNotEmpty(list)) {
                for (StoreChannelMappingDTO storeChannelMappingDTO : list) {

                    //根据channelStoreid 判断是否 需要同步
                    String chanelStoreId = storeChannelMappingDTO.getChannelStoreId();
                    if (syncPriceChannelConfigProperties.isNoSyncPriceByChannelStoreId(chanelStoreId)){
                        continue;
                    }

                    StoreBriefDTO storeBriefDTO = feignStoreService.getStoreDTOByStoreId(Long.valueOf(chanelStoreId));

//                    PriceStoreDetailExample example = new PriceStoreDetailExample();
//                    PriceStoreDetailExample.Criteria criteria = example.createCriteria();
//                    criteria.andStoreIdEqualTo(storeId);
//                    criteria.andPriceTypeCodeEqualTo(PTypeEnum.LSJ.getCode());
//                    List<PriceStoreDetail> priceList = priceStoreDetailMapper.selectByExample(example);

                    List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(PriceStoreDetailQuery.builder()
                        .storeId(storeId)
                        .priceTypeCode(PTypeEnum.LSJ.getCode())
                        .channelId(channelId)
                        .build());
                    if (CollectionUtils.isEmpty(priceList)) {
                        continue;
                    } else {
                        for (PriceStoreDetail priceStoreDetail : priceList) {
                            try {
                                priceStoreDetail.setPriceTypeCode(priceTypeCode);
                                priceStoreDetail.setPriceTypeId(priceType.getId());
                                priceStoreDetail.setPriceTypeName(priceType.getName());
                                priceStoreDetail.setStoreId(Long.parseLong(storeChannelMappingDTO.getChannelStoreId()));
                                priceStoreDetail.setStoreName(storeChannelMappingDTO.getChannelStoreName());
                                priceStoreDetail.setBusinessId(storeBriefDTO.getBusinessId());
                                priceStoreDetail.setGmtCreate(new Date());
                                priceStoreDetail.setId(null);
                                int i = priceStoreDetailMapper.insertSelective(priceStoreDetail);
                                logger.info("中心店价格初始化 {}生成药急送价格结果{}", storeId, i);

                                //通知商品中心
                                this.syncItemCenterNewPrice(priceStoreDetail);

                            } catch (Exception e) {
                                logger.info("中心店价格初始化 Exception1", e);
                            }
                        }

                    }
                }

            }
            logger.info("中心店价格初始化完成 {}", storeId);
        } catch (Exception e) {
            logger.info("中心店价格初始化 Exception", e);
        }
        return false;
    }

    @Override
    public boolean comparisonPriceByTargetStoreIdAndStoreId(Long targetStoreId, Long storeId, String goodsNo, String priceTypeCode, Integer channelId) {

        logger.info("根据门店对比目标门店价格 :{} :{} :{} :{}",targetStoreId,storeId,goodsNo,priceTypeCode);
        boolean isHasGoodsNo = org.apache.commons.lang3.StringUtils.isNotBlank(goodsNo);
        List<String> goodsNoList = Lists.newArrayList();
        if (isHasGoodsNo){
            goodsNoList = Arrays.asList(goodsNo.split("[,，]"));
        }

        //根据门店编码 和 商品编码 查询价格中心价格
        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(targetStoreId)
            .channelId(channelId)
            .priceTypeCode(priceTypeCode)
            .build();
        if (isHasGoodsNo){
            query.setGoodsNoList(goodsNoList);
        }
        List<PriceStoreDetail> priceList;

        int page = 1, pageSize = 100;
        while (true) {
            query.setPageSize(pageSize);
            query.setPage(page);
            priceList = priceStoreDetailReadService.query(query);
            if (CollectionUtils.isNotEmpty(priceList)){
                priceList.forEach(item->{
                    PriceSyncTaskVO priceSyncTaskVO = new PriceSyncTaskVO();
                    priceSyncTaskVO.setStoreId(storeId);
                    priceSyncTaskVO.setType(PriceSyncTaskTypeEnum.COMPARISON_PRICE_STOREID_STOREID.getType());
                    priceSyncTaskVO.setDetail(item);
                    priceSyncTaskProducer.sendPriceSyncTask(priceSyncTaskVO);
                });
            } else {
                break;
            }
            page++;
            logger.info("根据门店对比目标门店价格 page:{}, size:{} ",page, priceList.size());
        }

        return false;
    }



    @Override
    public boolean comparisonPriceByTargetStoreIdPriceCodeAndStoreIdPriceCode(Long targetStoreId, String targetPriceTypeCode, Long storeId, String storeName,String priceTypeCode, String goodsNo, Integer channelId) {

        logger.info("根据门店对比目标门店价格 targetStoreId:{} targetPriceTypeCode:{} storeId:{}  priceTypeCode:{} goodsNo:{} channelId:{}",
            targetStoreId,targetPriceTypeCode,storeId,priceTypeCode,goodsNo,channelId);
        boolean isHasGoodsNo = org.apache.commons.lang3.StringUtils.isNotBlank(goodsNo);
        List<String> goodsNoList = Lists.newArrayList();
        if (isHasGoodsNo){
            goodsNoList = Arrays.asList(goodsNo.split("[,，]"));
        }

        //根据门店编码 和 商品编码 查询价格中心价格
        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(targetStoreId)
            .channelId(channelId)
            .priceTypeCode(targetPriceTypeCode)
            .build();
        if (isHasGoodsNo){
            query.setGoodsNoList(goodsNoList);
        }
        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        if (CollectionUtils.isNotEmpty(priceList)){

            priceList.forEach(item->{
                PriceSyncTaskVO priceSyncTaskVO = new PriceSyncTaskVO();
                priceSyncTaskVO.setStoreId(storeId);
                priceSyncTaskVO.setStoreName(StringUtils.isEmpty(storeName) ? item.getStoreName() : storeName);
                priceSyncTaskVO.setPriceTypeCode(priceTypeCode);
                priceSyncTaskVO.setType(PriceSyncTaskTypeEnum.COMPARISON_PRICE_STOREID_STOREID_DIFF_PAY_CODE.getType());
                priceSyncTaskVO.setDetail(item);
                priceSyncTaskProducer.sendPriceSyncTask(priceSyncTaskVO);
            });
        }
        logger.info("根据门店对比目标门店价格完成 :{} ",priceList.size());
        return false;
    }



    @Override
    public void executeComparisonPriceByTargetStoreIdAndStoreId(Long storeId, PriceStoreDetail priceStoreDetail) {
        logger.info("执行对比价格请求参数:{} :{}",storeId,priceStoreDetail);

        String goodsNo = priceStoreDetail.getGoodsNo();
        String priceTypeCode = priceStoreDetail.getPriceTypeCode();
        Integer channelId = priceStoreDetail.getChannelId();

        //根据门店编码 和 商品编码 查询价格中心价格
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
            .andGoodsNoEqualTo(goodsNo)
            .andChannelIdEqualTo(channelId)
            .andPriceTypeCodeEqualTo(priceTypeCode);

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .goodsNo(goodsNo)
            .channelId(channelId)
            .priceTypeCode(priceTypeCode)
            .build();
        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        if (CollectionUtils.isEmpty(priceList)){
            //插入价格
            PriceStoreDetail priceStoreDetailNew = new PriceStoreDetail();
            BeanUtils.copyProperties(priceStoreDetail,priceStoreDetailNew);
            priceStoreDetailNew.setId(null);
            priceStoreDetailNew.setStoreId(storeId);
            priceStoreDetailNew.setCreatedBy(0L);
            priceStoreDetailNew.setGmtCreate(new Date());
            priceStoreDetailNew.setGmtUpdate(new Date());

            try {
                int result = priceStoreDetailMapper.insertSelective(priceStoreDetailNew);
                logger.info("补充门店不存在的价格:{} :{} :{}",result,storeId,goodsNo);

                priceService.afterSavePrice(priceStoreDetailNew);
            }catch (Exception e){
                logger.warn("补充门店不存在的价格 异常:",e);
            }

        }else {
            //对比价格
            BigDecimal targetPrice = priceStoreDetail.getPrice();
            PriceStoreDetail priceStoreDetailOld = priceList.get(0);

            boolean isEqually = targetPrice.equals(priceStoreDetailOld.getPrice());
            if (!isEqually){

                PriceStoreDetail record = new PriceStoreDetail();
                record.setPrice(targetPrice);
                record.setGmtUpdate(new Date());

                try {
                    int result = priceStoreDetailMapper.updateByExampleSelective(record, example);
                    logger.info("补充门店价格 不一致:{} :{} :{}",result,storeId,goodsNo);
                    List<PriceStoreDetail> priceStoreDetails = priceStoreDetailMapper.selectByExample(example);
                    priceStoreDetails.forEach(d -> priceService.afterSavePrice(d));
                }catch (Exception e){
                    logger.warn("补充门店不存在的价格 异常:",e);
                }
            }

            logger.info("补充门店价格 不一致 对比结果 :{} :{} :{}",isEqually,storeId,goodsNo);
        }

    }

    @Override
    public void executeComparisonPriceByTargetStoreIdPriceCodeAndStoreIdPriceCode(Long storeId, String storeName, String priceTypeCode, PriceStoreDetail priceStoreDetail) {

        String goodsNo = priceStoreDetail.getGoodsNo();

        //根据门店编码 和 商品编码 查询价格中心价格
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId)
            .andGoodsNoEqualTo(goodsNo)
            .andChannelIdEqualTo(priceStoreDetail.getChannelId())
            .andPriceTypeCodeEqualTo(priceTypeCode);

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .goodsNo(goodsNo)
            .channelId(priceStoreDetail.getChannelId())
            .priceTypeCode(priceTypeCode)
            .build();

        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        if (CollectionUtils.isEmpty(priceList)){

            Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
            PriceType priceType = priceTypeMap.get(priceTypeCode);

            StoreBriefDTO storeBriefDTO = feignStoreService.getStoreDTOByStoreId(storeId);

            //插入价格
            PriceStoreDetail priceStoreDetailNew = new PriceStoreDetail();
            BeanUtils.copyProperties(priceStoreDetail,priceStoreDetailNew);
            priceStoreDetailNew.setId(null);
            priceStoreDetailNew.setStoreId(storeId);
            priceStoreDetailNew.setBusinessId(storeBriefDTO.getBusinessId());
            priceStoreDetailNew.setStoreName(StringUtils.isEmpty(storeName) ? priceStoreDetail.getStoreName() : storeName);
            priceStoreDetailNew.setPriceTypeCode(priceType.getCode());
            priceStoreDetailNew.setPriceTypeName(priceType.getName());
            priceStoreDetailNew.setPriceTypeId(priceType.getId());
            priceStoreDetailNew.setAdjustCode("0");
            priceStoreDetailNew.setAdjustDetailId(0L);
            priceStoreDetailNew.setCreatedBy(0L);
            priceStoreDetailNew.setGmtCreate(new Date());
            priceStoreDetailNew.setGmtUpdate(new Date());

            try {
                int result = priceStoreDetailMapper.insertSelective(priceStoreDetailNew);
                logger.info("补充门店不存在的价格:{} :{} :{}",result,storeId,goodsNo);
                priceService.afterSavePrice(priceStoreDetailNew);
            }catch (Exception e){
                logger.warn("补充门店不存在的价格 异常:",e);
            }

        }else {
            //对比价格
            BigDecimal targetPrice = priceStoreDetail.getPrice();
            PriceStoreDetail priceStoreDetailOld = priceList.get(0);

            boolean isEqually = targetPrice.equals(priceStoreDetailOld.getPrice());
            if (!isEqually){


                PriceStoreDetail record = new PriceStoreDetail();
                record.setPrice(targetPrice);
                record.setGmtUpdate(new Date());

                try {
                    int result = priceStoreDetailMapper.updateByExampleSelective(record, example);
                    logger.info("补充门店价格 不一致:{} :{} :{}",result,storeId,goodsNo);
                    List<PriceStoreDetail> priceStoreDetails = priceStoreDetailMapper.selectByExample(example);
                    priceStoreDetails.forEach(d -> priceService.afterSavePrice(d));
                }catch (Exception e){
                    logger.warn("补充门店不存在的价格 异常:",e);
                }
            }

            logger.info("补充门店价格 不一致 对比结果 :{} :{} :{}",isEqually,storeId,goodsNo);
        }

    }

    @Override
    public boolean delPriceStoreDetailSpu0() {
        logger.info("delPriceStoreDetailSpu0");
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andSpuIdNotEqualTo(0L);
        int i = priceStoreDetailMapper.deleteByExample(example);
        logger.info("delPriceStoreDetailSpu0结果{}", i);
        return false;
    }


    @Override
    public void initializePriceByStoreId(List<PriceStoreDetail> priceStoreDetails, String targetPriceTypeCode) {
        logger.info("价格初始化,原价格列表数量:{} 目标价格类型:{}", priceStoreDetails.size(), targetPriceTypeCode);

        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        PriceType priceType = priceTypeMap.get(targetPriceTypeCode);
        if (null == priceType) {
            logger.info("入参targetPriceTypeCode对应的价格类型没找到");
            return;
        }

        if (CollectionUtils.isEmpty(priceStoreDetails)) {
            return;
        }

        //将商品列表进行分组
        List<List<PriceStoreDetail>> priceLists = Lists.partition(priceStoreDetails, EB_SYNC_PRICE_GOODS_LIMIT);

        priceLists.forEach(item -> {
            //进行组装新的价格数据
            List<PriceStoreDetail> priceStoreDetailList = item.stream().map(itemT -> toTargetPriceStoreDetail(itemT, targetPriceTypeCode, priceType)).collect(Collectors.toList());
            List<List<PriceStoreDetail>> priceLists2 = Lists.partition(priceStoreDetailList, 20);
            priceLists2.forEach(item2 -> {
                asyncTaskExecutor2.execute(() -> {
                    try {
                        int result = priceStoreDetailExMapper.batchInsert(item2);
                        logger.info("连锁指定门店价格类型初始化为目标价格类型,批量插入成功:{}", result);
                        //补充门店连锁信息
                        item2.forEach(i -> {
                            syncItemCenterNewPrice(i);
                            priceService.afterSavePrice(i);
                        });
                    } catch (Exception e) {
                        logger.info("连锁指定门店价格类型初始化为目标价格类型,批量插入异常:", e);
                        item2.forEach(item3 -> {
                            try {
                                int result = priceStoreDetailExMapper.batchInsert(Collections.singletonList(item3));
                                logger.info("连锁指定门店价格类型初始化为目标价格类型,批量插入异常后,单个保存重试 结果:{} :{}", result, item3.getGoodsNo());
                                //补充信息
                                this.syncItemCenterNewPrice(item3);
                                priceService.afterSavePrice(item3);
                            } catch (Exception e1) {
                                logger.warn("连锁指定门店价格类型初始化为目标价格类型,批量保存异常后,单个保存重试异常 :{} :", item3.getGoodsNo(), e1);
                            }
                        });
                    }
                });
            });
        });
    }


    @Override
    public void compareItemCenterPriceToUpdate(Long businessId, String storeIds, String priceTypeCode, Integer channelId) {
        Assert.isTrue(StringUtils.isNotEmpty(storeIds), "请求门店参数不可以为空");
        Assert.notNull(businessId, "请求连锁参数不可以为空");

        String[] storeIdArr = storeIds.split(",");

        for (String storeId : storeIdArr) {
            long storeIdL = Long.parseLong(storeId);
            asyncTaskExecutor2.execute(() -> {
                //需要推送的商品价格
                List<PriceStoreDetail> priceStoreDetailList = Lists.newArrayList();
                try {
                    logger.info("根据连锁和门店查询已经上架的商品价格, 进行价格对比 :{} :{}", businessId, storeIdL);

                    //根据门店id 查询门店已经上架的商品编码
                    ResponseEntity<List<ItemPriceSync>> responseEntity = null;//itemFeignService.getStoreItemPrice(businessId,storeIdL);
                    List<ItemPriceSync> itemPriceSyncList = responseEntity.getBody();
                    if (CollectionUtils.isEmpty(itemPriceSyncList)) {
                        logger.info("根据连锁和门店ID查询上架的商品为空。");
                        return;
                    }
                    logger.info("根据连锁和门店查询已经上架的商品价格, 列表数量:{}", itemPriceSyncList.size());
                    List<List<ItemPriceSync>> itemPriceSyncLists = Lists.partition(itemPriceSyncList, 500);

                    for (List<ItemPriceSync> itemPriceSyncList1 : itemPriceSyncLists) {

                        //需要进行循环 和 查询价格中台价格的 商品编码列表
                        List<String> goodsNoList = Lists.newArrayList();
                        //商家中心的 编码对应价格的 Map
                        Map<String, ItemPriceSync> goodsNoToItemPriceSync = Maps.newHashMap();
                        for (ItemPriceSync itemPriceSync : itemPriceSyncList1) {
                            goodsNoList.add(itemPriceSync.getGoodsNo());
                            goodsNoToItemPriceSync.put(itemPriceSync.getGoodsNo(), itemPriceSync);
                        }

                        //根据门店编码 和 商品编码 查询价格中心价格
                        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                            .storeId(storeIdL)
                            .goodsNoList(goodsNoList)
                            .channelId(channelId)
                            .priceTypeCode(priceTypeCode)
                            .build();

                        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);
                        if (CollectionUtils.isEmpty(priceList)) {
                            logger.info("根据商品编码和门店查询的价格列表为空, 不做对比重推。");
                            continue;
                        }

                        Map<String, PriceStoreDetail> goodsNoToPriceMap = priceList.stream().collect(Collectors.toMap(PriceStoreDetail::getGoodsNo, Function.identity()));

                        for (String goodsNo : goodsNoList) {

                            //价格中台的价格信息
                            PriceStoreDetail priceStoreDetail = goodsNoToPriceMap.get(goodsNo);
                            BigDecimal priceCenter = priceStoreDetail.getPrice();

                            //商品中心的价格信息
                            ItemPriceSync itemPriceSync = goodsNoToItemPriceSync.get(goodsNo);
                            BigDecimal itemPrice = new BigDecimal(itemPriceSync.getPrice());

                            //如果两个价格一样， 直接跳过
                            if (itemPrice.compareTo(priceCenter) == 0) {
                                logger.info("根据连锁和门店查询已经上架的商品价格, 进行价格对比, 两个价格一样跳过 中台:{} 商品:{} 编码:{}。", priceCenter, itemPrice, goodsNo);
                                continue;
                            }
                            priceStoreDetailList.add(priceStoreDetail);
                        }

                        // 对比价格不一致的 发送mq 通知商品中心修改 价格
                        if (CollectionUtils.isEmpty(priceStoreDetailList)) {
                            logger.info("根据连锁和门店查询已经上架的商品价格, 进行价格对比, 需要重推的列表为空。");
                            continue;
                        }
                        priceStoreDetailList.forEach(this::syncItemCenterNewPrice);
                        logger.info("根据连锁和门店查询已经上架的商品价格, 进行价格对比完成。门店:{} 数量:{}", storeIdL, priceStoreDetailList.size());
                    }
                } catch (Exception e) {
                    logger.warn("根据连锁和门店查询已经上架的商品价格, 进行价格对比 异常:", e);
                }
            });
        }
    }

    @Override
    public void syncPriceToItemCenterByStore(Long storeId, String priceTypeCode, Integer channelId) {

        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        PriceType priceType = priceTypeMap.get(priceTypeCode);
        if (null == priceType) {
            logger.info("入参priceTypeCode对应的价格类型没找到");
            return;
        }

        pushPriceToItemCenter(storeId, priceTypeCode, null, channelId);


    }

    @Override
    public void syncPriceToItemCenterByStore(Long businessId, Long storeId, String priceTypeCode, List<String> goodsNos, Integer channelId) {
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        PriceType priceType = priceTypeMap.get(priceTypeCode);
        if (null == priceType) {
            logger.info("入参priceTypeCode对应的价格类型没找到");
            return;
        }
        List<Long> storeIdList = new ArrayList<>();
        if (Objects.isNull(storeId)) {
            List<MdmStoreBaseToRedisDTO> mdmStoreByBusinessId = feignStoreService.findMdmStoreByBusinessId(businessId);
            if (CollectionUtils.isEmpty(mdmStoreByBusinessId)) {
                return;
            }
            storeIdList.addAll(mdmStoreByBusinessId.stream().map(MdmStoreBaseToRedisDTO::getStoreId).collect(Collectors.toList()));
        } else {
            storeIdList.add(storeId);
        }

        storeIdList.forEach(id -> pushPriceToItemCenter(id, priceTypeCode, goodsNos, channelId));
    }

    private void pushPriceToItemCenter(Long storeId, String priceTypeCode, List<String> goodsNos, Integer channelId) {
        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .channelId(channelId)
            .priceTypeCode(priceTypeCode)
            .build();

        if (CollectionUtils.isNotEmpty(goodsNos)) {
            query.setGoodsNoList(goodsNos);
        }
        query.setPageSize(1000);
        List<PriceStoreDetail> priceList;
        int page = 0;
        do {
            query.setPage(page);
            priceList = priceStoreDetailReadService.query(query);
            logger.info("指定门店价格类型同步到商品中心,门店价格数量:{}", priceList.size());

            if (CollectionUtils.isEmpty(priceList)) {
                break;
            }

            List<List<PriceStoreDetail>> priceLists2 = Lists.partition(priceList, 5000);
            priceLists2.forEach(item2 -> {
                asyncTaskExecutor2.execute(() -> {
                    try {
                        //发送消息到商品中心
                        item2.forEach(this::syncItemCenterNewPrice);
                    } catch (Exception e) {
                        logger.info("指定门店价格类型同步到商品中心,异常:", e);
                    }
                });
            });
            page++;
        } while (CollectionUtils.isNotEmpty(priceList));



    }

    @Override
    public void updatePriceByPriceType(Long businessId, String storeIds, String sourcePriceTypeCode, String targetPriceTypeCode, Integer channelId) {

        if (StringUtils.isNotBlank(storeIds)) {
            String[] storeIdArr = storeIds.split(",");
            for (String storeId : storeIdArr) {
                this.doUpdatePriceByPriceType(Long.parseLong(storeId), sourcePriceTypeCode, targetPriceTypeCode, channelId);
            }
        } else {
            //按连锁下的所有门店配置多个（一个门店配置一个）
            List<CrmStoreBusinessDTO> storeList = storeService.getCrmStoreByBusinessIds(new Long[]{businessId});
            logger.info("连锁价格初始化 获取对应的多个门店信息返回门店数量为{}", storeList.size());
            if (CollectionUtils.isEmpty(storeList)) {
                logger.info("根据指定价格类型的价格修改到目标价格类型的价格, 根据连锁查询门店列表为空不做修改");
                return;
            }

            List<CrmStoreDTO> crmStoreDTOList = storeList.get(0).getCrmStoreDTOList();
            logger.info("根据指定价格类型的价格修改到目标价格类型的价格, 获取对应的多个门店信息crmStoreDTOList返回数量{}", crmStoreDTOList.size());

            if (CollectionUtils.isEmpty(crmStoreDTOList)) {
                logger.info("根据指定价格类型的价格修改到目标价格类型的价格, 根据连锁查询对应门店列表为空不做修改");
                return;
            }
            for (CrmStoreDTO crmStoreDto : crmStoreDTOList) {
                long storeIdCur = crmStoreDto.getId();
                this.doUpdatePriceByPriceType(storeIdCur, sourcePriceTypeCode, targetPriceTypeCode, channelId);
            }
        }
    }

    @Override
    public void selectPriceNullOrZero(Long businessId, Long storeId, String priceTypeCode, String goodsNo) {
        logger.info("查询价格为空或者为零, 补充操作:{} :{} :{}", storeId, priceTypeCode, goodsNo);

        try {
            //如果价格查询为空/零 需要通知bam 重新对比价格
            bamFeignService.queryBJStoreGoodsPriceInternal(businessId, storeId, Arrays.asList(goodsNo));
        } catch (Exception e) {
            logger.warn("查询价格为空或者为零, 调用Bam对比价格:{}", businessId + "-" + storeId + "-" + goodsNo);
        }


        //查询价格对比
        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .goodsNo(goodsNo)
            .priceTypeCodeList(Arrays.asList("LSJ", priceTypeCode))
            .build();

        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        if (CollectionUtils.isEmpty(priceList)) {
            logger.info("查询价格类型为LSJ/" + priceTypeCode + "价格数据为空,不做操作");
            return;
        }

        Map<String, PriceStoreDetail> priceTypeMap = priceList.stream().distinct().collect(Collectors.toMap(PriceStoreDetail::getPriceTypeCode, item -> item, (k1, k2) -> k1));

        PriceStoreDetail priceStoreDetail = priceTypeMap.get("LSJ");
        if (priceStoreDetail == null || Objects.isNull(priceStoreDetail.getPrice())) {
            logger.info("查询价格类型为LSJ价格数据为空,没有参考标注不做操作");
            return;
        }

        //如果查询的价格类型为空 需要新增
        PriceStoreDetail priceStoreDetailCur = priceTypeMap.get(priceTypeCode);
        if (priceStoreDetailCur == null) {
            PriceStoreDetail priceStoreDetailNew = new PriceStoreDetail();
            BeanUtils.copyProperties(priceStoreDetail, priceStoreDetailNew);
            priceStoreDetailNew.setGmtUpdate(new Date());
            int result = priceStoreDetailMapper.insertSelective(priceStoreDetailNew);
            logger.info("查询价格类型为|" + priceTypeCode + "|价格数据为空,复制LSJ成功条数:{}", result);
            priceService.afterSavePrice(priceStoreDetailNew);
        } else {

            //当前参数中需要查询的价格类型数据
            if (priceStoreDetail.getPrice().equals(priceStoreDetailCur.getPrice())) {
                logger.info("查询价格类型为LSJ/" + priceTypeCode + "价格数据,对比后价格一样不做操作");
                return;
            }

            PriceStoreDetailExample example2 = new PriceStoreDetailExample();
            PriceStoreDetailExample.Criteria criteria2 = example2.createCriteria();
            criteria2.andStoreIdEqualTo(storeId);
            criteria2.andIdEqualTo(priceStoreDetailCur.getId());
            criteria2.andGoodsNoEqualTo(goodsNo);

            PriceStoreDetail priceStoreDetailNew = new PriceStoreDetail();
            priceStoreDetailNew.setPrice(priceStoreDetail.getPrice());
            priceStoreDetailNew.setGmtUpdate(new Date());
            int result = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetailNew, example2);
            logger.info("查询价格类型为|" + priceTypeCode + "|价格数据为0,修改为LSJ价价格成功条数:{}", result);
            List<PriceStoreDetail> priceStoreDetails = priceStoreDetailMapper.selectByExample(example2);
            priceStoreDetails.forEach(d -> priceService.afterSavePrice(d));
        }
    }

    @Override
    public void channelPriceCompareLSJ(Long businessId, Long storeId, String priceTypeCode) {
        logger.info("根据渠道价对比零售价 门店:{} 渠道价编码:{}", storeId, priceTypeCode);
        int pageSize = 2000;
        int pageCur = 1;


        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        PriceType priceType = priceTypeMap.get(priceTypeCode);
        if (null == priceType) {
            logger.info("根据渠道价对比零售价, 入参priceTypeCode对应的价格类型没找到 :{}", priceTypeCode);
            return;
        }


        for (; ; ) {
            logger.info("根据渠道价对比零售价,查询价格列表,连锁:{} 门店:{} 当前页数:{}", businessId, storeId, pageCur);
            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .businessId(businessId)
                .storeId(storeId)
                .priceTypeCodeList(Arrays.asList("LSJ", priceTypeCode))
                .page(pageCur)
                .pageSize(pageSize)
                .orderBy("goods_no asc")
                .build();

            List<PriceStoreDetail> list = priceStoreDetailReadService.query(query);
            if (org.springframework.util.CollectionUtils.isEmpty(list) || list.size() <= 0) {
                logger.info("根据渠道价对比零售价|对比结束| 连锁:{} 门店:{} 总页数:{}", businessId, storeId, pageCur);
                break;
            }

            //获取 LSJ 的 商品List
            List<PriceStoreDetail> listLJS = list.stream().filter(itemLSJ -> "LSJ".equals(itemLSJ.getPriceTypeCode())).collect(Collectors.toList());
            //获取 channel price 的 商品mq
            Map<String, PriceStoreDetail> listChannelPriceMap = list.stream().filter(itemLSJ -> itemLSJ.getPriceTypeCode().equals(priceTypeCode)).collect(Collectors.toMap(PriceStoreDetail::getGoodsNo, itemLSJ -> itemLSJ, (k1, k2) -> k1));

            //需要初始化的价格数据
            List<PriceStoreDetail> initializePriceList = Lists.newArrayList();

            for (PriceStoreDetail itemLSJCur : listLJS) {
                //零售价的 价格 和商品编码
                String goodsNo = itemLSJCur.getGoodsNo();
                BigDecimal priceLSJ = itemLSJCur.getPrice();

                PriceStoreDetail priceStoreDetailChannel = listChannelPriceMap.get(goodsNo);

                //如果 priceStoreDetailGJYJS 为空， 说明没有 渠道价
                if (priceStoreDetailChannel == null) {
                    initializePriceList.add(itemLSJCur);
                    logger.info("根据渠道价对比零售价, 渠道价不存在,之后会执行批量插入 :{}", goodsNo);
                    continue;
                }

                //渠道价格
                BigDecimal channelPrice = priceStoreDetailChannel.getPrice();

                if (!priceLSJ.equals(channelPrice)) {
                    PriceStoreDetailExample example2 = new PriceStoreDetailExample();
                    example2.createCriteria()
                        .andStoreIdEqualTo(storeId).andIdEqualTo(priceStoreDetailChannel.getId());


                    PriceStoreDetail priceStoreDetailChannelNew = new PriceStoreDetail();
                    priceStoreDetailChannelNew.setPrice(priceLSJ);
                    priceStoreDetailChannelNew.setGmtUpdate(new Date());
                    int result = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetailChannelNew, example2);
                    logger.info("根据渠道价对比零售价, 渠道价和零售价不一致,修改为零售价结果 " +
                        "门店:{} 商品:{} 原渠道价格:{} 零售价:{} 修改结果:{}", storeId, goodsNo, channelPrice, priceLSJ, result);

                    List<PriceStoreDetail> priceStoreDetails = priceStoreDetailMapper.selectByExample(example2);
                    priceStoreDetails.forEach(d -> priceService.afterSavePrice(d));


//                    //通知商品中心修改价格
//                    PriceStoreDetail priceStoreDetailItemCenter = new PriceStoreDetail();
//                    priceStoreDetailItemCenter.setBusinessId(priceStoreDetailChannel.getBusinessId());
//                    priceStoreDetailItemCenter.setStoreId(priceStoreDetailChannel.getStoreId());
//                    priceStoreDetailItemCenter.setGoodsNo(priceStoreDetailChannel.getGoodsNo());
//                    priceStoreDetailItemCenter.setPrice(priceLSJ);
//                    priceStoreDetailItemCenter.setPriceTypeCode(priceTypeCode);
//                    this.syncItemCenterNewPrice(priceStoreDetailItemCenter);
                }

            }

            //进行批量 复制 价格
            this.initializePriceByStoreId(initializePriceList, priceTypeCode);

            pageCur++;
        }
    }

    @Override
    public void importPriceUpdate(List<ImportPriceUpdateDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)){
            logger.info("价格导入修改参数为空, 跳过");
            return;
        }
        int resultTotal = 0;
        for (ImportPriceUpdateDTO item: dtos) {

            String storeId = item.getStoreId();
            String goodsNo = item.getGoodsNo();
            String priceTypeCode = item.getPriceTypeCode();
            String price = item.getPrice();
            Integer channelId = item.getChannelId();

            if (StringUtils.isBlank(storeId) || StringUtils.isBlank(goodsNo)
                || StringUtils.isBlank(priceTypeCode) || StringUtils.isBlank(price)){
                logger.info("价格导入修改参数门店/商品/价格类型/价格为空, 跳过");
                continue;
            }

            try {
                PriceStoreDetail record = new PriceStoreDetail();
                record.setPrice(new BigDecimal(price));

                PriceStoreDetailExample example = new PriceStoreDetailExample();
                example.createCriteria()
                    .andStoreIdEqualTo(Long.parseLong(storeId))
                    .andGoodsNoEqualTo(goodsNo)
                    .andChannelIdEqualTo(channelId)
                    .andPriceTypeCodeEqualTo(priceTypeCode);
                int result = priceStoreDetailMapper.updateByExampleSelective(record,example);
                logger.info("修改价格类型, 门店:{} 商品:{} 成功条数:{} ",storeId,goodsNo,result);
                resultTotal = resultTotal + result;


                //通知商品中心修改价格
                if ("LSJ".equals(item.getPriceTypeCode())){
                    PriceStoreDetail priceStoreDetailItemCenter = new PriceStoreDetail();
                    priceStoreDetailItemCenter.setBusinessId(item.getBusinessId());
                    priceStoreDetailItemCenter.setStoreId(Long.parseLong(storeId));
                    priceStoreDetailItemCenter.setGoodsNo(goodsNo);
                    priceStoreDetailItemCenter.setPrice(new BigDecimal(price));
                    priceStoreDetailItemCenter.setPriceTypeCode(item.getPriceTypeCode());
                    this.syncItemCenterNewPrice(priceStoreDetailItemCenter);
                }
            }catch (Exception e){
                logger.warn("修改价格类型, 异常:",e);
            }
        }
        logger.info("修改价格类型, 总-成功条数:{}",resultTotal);
    }

    @Override
    public void importPriceUpdateNew(List<ImportPriceUpdateDTO> dtos) {
        if (CollectionUtils.isEmpty(dtos)){
            logger.info("价格导入修改参数为空, 跳过");
            return;
        }
        int resultTotal = 0;
        for (ImportPriceUpdateDTO item: dtos) {

            String storeId = item.getStoreId();
            String goodsNo = item.getGoodsNo();
            String priceTypeCode = item.getPriceTypeCode();
            Integer channelId = item.getChannelId();
            BigDecimal price = new BigDecimal(item.getPrice()).multiply(new BigDecimal(100));

            try {

                SpuNewVo spuNewVo = new SpuNewVo();

                PriceStoreDetail record = new PriceStoreDetail();
                record.setStoreId(Long.parseLong(storeId));
                record.setGoodsNo(goodsNo);
                record.setPriceTypeCode(priceTypeCode);

                Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();

                PriceType priceType = priceTypeMap.get(priceTypeCode);

                record.setPriceTypeName(priceType.getName());
                record.setPriceTypeId(priceType.getId());
                record.setPriceTypeCode(priceType.getCode());
                record.setPrice(price);

                record.setOrgId(Long.parseLong(item.getOrgId()));
                record.setOrgName(item.getOrgName());
                record.setLevel((byte) OrgLevelEnum.BUSINESS.getCode());//写死连锁
                record.setAdjustCode("0");
                record.setAdjustDetailId(11L);

                record.setAuthOrgId(0L);
                record.setAuthOrgName("0");
                record.setAuthOrgLevel((byte) OrgLevelEnum.BUSINESS.getCode());//写死连锁
                record.setOrgGoodsId(0L);
                record.setCurName(spuNewVo.getCurName());
                record.setBarCode(spuNewVo.getBarCode());
                record.setOpCode(spuNewVo.getOpCode());
                record.setExtend(spuNewVo.getCurName() + "_" + spuNewVo.getBarCode() + "_" + spuNewVo.getOpCode() + "_" + spuNewVo.getGoodsNo());
                record.setSpuId(spuNewVo.getId() == null ? 0L : spuNewVo.getId());
                record.setVersion(21);
                record.setCreatedBy(0L);
                record.setChannelId(channelId);


                PriceStoreDetail recordGJYJS = new PriceStoreDetail();
                BeanUtils.copyProperties(record,recordGJYJS);

                PriceType priceType1 = priceTypeMap.get("GJYJS");
                recordGJYJS.setPriceTypeCode(priceType1.getCode());

                recordGJYJS.setPriceTypeName(priceType.getName());
                recordGJYJS.setPriceTypeId(priceType.getId());
                recordGJYJS.setPriceTypeCode(priceType.getCode());

                try {
                    int result = priceStoreDetailMapper.insertSelective(record);

                    //通知cube
                    pricePushCubeProducer.sendMq(record);

                    if (record.getPriceTypeCode().equals("LSJ")){

                        int resultGjyjs =  priceStoreDetailMapper.insertSelective(recordGJYJS);
                        //通知商品中心修改价格

                        PriceStoreDetail priceStoreDetailItemCenter = new PriceStoreDetail();
                        priceStoreDetailItemCenter.setBusinessId(item.getBusinessId());
                        priceStoreDetailItemCenter.setStoreId(Long.parseLong(storeId));
                        priceStoreDetailItemCenter.setGoodsNo(goodsNo);
                        priceStoreDetailItemCenter.setPrice(price);
                        priceStoreDetailItemCenter.setPriceTypeCode("GJYJS");
                        if (channelId == adjustPriceConfig.getDefaultChannelId()
                            || channelId == adjustPriceConfig.getPushItemCenterChannelId()) {
                            this.syncItemCenterNewPrice(priceStoreDetailItemCenter);
                        }
                    }
                }catch (DuplicateKeyException e1) {
                    logger.warn("[{}] doInsert Duplicate entry:", e1);
                }catch (Exception e){
                    logger.warn("保存异常:",e);
                }
            }catch (Exception e){
                logger.warn("修改价格类型, 异常:",e);
            }
        }
        logger.info("修改价格类型, 总-成功条数:{}",resultTotal);
    }

    @Override
    public CommonResponse queryGoodsPriceCount(String queryTaskId,Long businessId, String storeIds,String priceTypeCode) {

        boolean isQueryResult = org.apache.commons.lang3.StringUtils.isNotEmpty(queryTaskId);
        CommonResponse response = new CommonResponse();
        Map<String,Object> map = Maps.newHashMap();
        if (isQueryResult){
            logger.info("查询门店价格数量:{}",queryTaskId);
            map.put("queryTaskId",queryTaskId);

            //放需要执行的门店ID 缓存
            RBucket<String> rBucketResponse = redissonClient.getBucket("QUERY_GOODS_PRICE_STORE_TOTAL_NUMBER_" +queryTaskId );
            List<String> storeList = JSONObject.parseArray(rBucketResponse.get(),String.class);
            map.put("queryStoreTotalNumber",storeList.size()+"");

            //将门店执行完成数量 + 1
            RAtomicLong rAtomicLongAgain = redissonClient.getAtomicLong("QUERY_GOODS_PRICE_STORE_EXECUTE_NUMBER_"+queryTaskId);
            map.put("queryStoreExecuteNumber",rAtomicLongAgain.get());

            long priceCount = 0L;

            for (String storeIdStr : storeList){
                //已经执行的门店ID 缓存
                RBucket<String> rBucketStorePriceCount = redissonClient.getBucket("QUERY_GOODS_PRICE_STORE_EXECUTE_RESULT_" +queryTaskId + storeIdStr );
                String count = rBucketStorePriceCount.get();
                if (org.apache.commons.lang3.StringUtils.isNotBlank(count)){
                    priceCount = priceCount + Long.parseLong(count);
                }
                map.put("queryPriceTotalNumber_"+storeIdStr,count);
            }
            map.put("queryPriceTotalNumber",priceCount);
            response.setResult(map);
            logger.info("查询门店价格数量结果:{}",JSONObject.toJSONString(response));
            return response;

        }else{
            logger.info("进行查询门店价格数量的统计 :{} :{} :{}",businessId,storeIds,priceTypeCode);
            //uuid 该任务的 唯一主键
            String queryTaskIdNew = UUID.randomUUID().toString().replace("-","");
            List<String> storeIdList = Lists.newArrayList();

            map.put("queryTaskId",queryTaskIdNew);
            boolean isSelectByStoreId = StringUtils.isNotBlank(storeIds);
            if (isSelectByStoreId){
                String[] storeIdArr = storeIds.split(",");
                for (String storeId : storeIdArr) {

                    InitialzePriceMqVo initialzePriceMqVo = new InitialzePriceMqVo();
                    initialzePriceMqVo.setStoreId(Long.parseLong(storeId));
                    initialzePriceMqVo.setTargetPriceTypeCode(priceTypeCode);
                    initialzePriceMqVo.setType(3);
                    initialzePriceMqVo.setTaskId(queryTaskIdNew);
                    initializePriceProducer.sendMq(initialzePriceMqVo);

                    storeIdList.add(storeId);
                }
            }else {
                //按连锁下的所有门店配置多个（一个门店配置一个）
                List<CrmStoreBusinessDTO> storeList = storeService.getCrmStoreByBusinessIds(new Long[]{businessId});
                logger.info("查询门店价格数量 根据连锁获取门店信息，返回门店数量为{}", storeList.size());
                if (CollectionUtils.isEmpty(storeList)){
                    map.put("queryStoreTotalNumber","0");
                    map.put("queryStoreExecuteNumber","0");
                    map.put("queryPriceTotalNumber","0");
                    response.setResult(map);
                    return response;
                }

                List<CrmStoreDTO> crmStoreDTOList = storeList.get(0).getCrmStoreDTOList();
                for (CrmStoreDTO crmStoreDto : crmStoreDTOList) {
                    long storeIdCur = crmStoreDto.getId();

                    InitialzePriceMqVo initialzePriceMqVo = new InitialzePriceMqVo();
                    initialzePriceMqVo.setStoreId(storeIdCur);
                    initialzePriceMqVo.setTargetPriceTypeCode(priceTypeCode);
                    initialzePriceMqVo.setType(3);
                    initialzePriceMqVo.setTaskId(queryTaskIdNew);
                    initializePriceProducer.sendMq(initialzePriceMqVo);

                    storeIdList.add(storeIdCur+"");
                }
            }

            //放需要执行的门店ID 缓存
            RBucket<String> rBucketResponse = redissonClient.getBucket("QUERY_GOODS_PRICE_STORE_TOTAL_NUMBER_" +queryTaskIdNew );
            rBucketResponse.set(JSONObject.toJSONString(storeIdList));
            rBucketResponse.expire(1,TimeUnit.DAYS);

            map.put("queryStoreTotalNumber",storeIdList.size()+"");
            response.setResult(map);
            return response;
        }
    }

    @Override
    public  Long doSelectGoodsPriceCount(String queryTaskIdNew,Long storeId,String priceTypeCode){

        PriceStoreDetailExample example = new PriceStoreDetailExample();
        example.createCriteria().andStoreIdEqualTo(storeId).andPriceTypeCodeEqualTo(priceTypeCode);
        long count = priceStoreDetailMapper.countByExample(example);
        logger.info("查询门店价格数量, 查询任务ID:{} 门店:{} 结果:{} ",queryTaskIdNew,storeId,count);

        //将门店执行完成数量 + 1
        RAtomicLong rAtomicLongAgain = redissonClient.getAtomicLong("QUERY_GOODS_PRICE_STORE_EXECUTE_NUMBER_"+queryTaskIdNew);
        rAtomicLongAgain.addAndGet(1L);
        rAtomicLongAgain.expire(1L, TimeUnit.DAYS);

        //已经执行的门店ID 缓存
        RBucket<String> rBucketStorePriceCount = redissonClient.getBucket("QUERY_GOODS_PRICE_STORE_EXECUTE_RESULT_" +queryTaskIdNew + storeId );
        rBucketStorePriceCount.set(count+"");
        rBucketStorePriceCount.expire(1L, TimeUnit.DAYS);

        return count;
    }

    @Override
    public Long doSelectGoodsPriceCount(String taskId, Long storeId, String targetPriceTypeCode, Integer channelId) {
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        example.createCriteria().andStoreIdEqualTo(storeId).andChannelIdEqualTo(channelId).andPriceTypeCodeEqualTo(targetPriceTypeCode);
        long count = priceStoreDetailMapper.countByExample(example);
        logger.info("查询门店价格数量, 查询任务ID:{} 门店:{} 渠道:{} 结果:{} ",taskId,storeId,channelId,count);
        return count;
    }

    private void doUpdatePriceByPriceType(Long storeId, String sourcePriceTypeCode, String targetPriceTypeCode, Integer channelId) {
        asyncTaskExecutor2.execute(() -> {
            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(storeId)
                .channelId(channelId)
                .priceTypeCode(sourcePriceTypeCode)
                .build();

            List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);
            logger.warn("根据指定价格类型的价格修改到目标价格类型的价格,需要修改的数量:{}", priceList.size());
            if (CollectionUtils.isNotEmpty(priceList)) {

                for (PriceStoreDetail priceStoreDetail : priceList) {
                    try {

                        PriceStoreDetail record = new PriceStoreDetail();
                        record.setPrice(priceStoreDetail.getPrice());
                        record.setGmtUpdate(new Date());

                        PriceStoreDetailExample example2 = new PriceStoreDetailExample();
                        PriceStoreDetailExample.Criteria criteria2 = example2.createCriteria();
                        criteria2.andStoreIdEqualTo(storeId);
                        criteria2.andChannelIdEqualTo(channelId);
                        criteria2.andPriceTypeCodeEqualTo(targetPriceTypeCode);
                        criteria2.andGoodsNoEqualTo(priceStoreDetail.getGoodsNo());

                        int result = priceStoreDetailMapper.updateByExampleSelective(record, example2);
                        logger.info("根据指定价格类型的价格修改到目标价格类型的价格完成, 门店:{} 商品:{} 价格:{} 成功条数:{}", storeId,
                            priceStoreDetail.getGoodsNo(), priceStoreDetail.getPrice(), result);
                    } catch (Exception e) {
                        logger.warn("根据指定价格类型的价格修改到目标价格类型的价格异常:", e);
                    }
                }

                logger.info("根据指定价格类型的价格修改到目标价格类型的价格,修改完成 门店:{} ", storeId);
            }
        });
    }

    /**
     * 同步商品中心
     *
     * @param priceStoreDetail
     */
    private void syncItemCenterNewPrice(PriceStoreDetail priceStoreDetail) {
        logger.warn("修改价格完成并同步商品中心 门店:{} 商品:{} 价格类型:{} 渠道：{}", priceStoreDetail.getStoreId(),
            priceStoreDetail.getGoodsNo(), priceStoreDetail.getPriceTypeCode(), priceStoreDetail.getChannelId());
        PricePushYJSVo pricePushYJSVo = new PricePushYJSVo();
        pricePushYJSVo.setPrice(priceStoreDetail.getPrice());
        pricePushYJSVo.setBusinessId(priceStoreDetail.getBusinessId());
        pricePushYJSVo.setStoreId(priceStoreDetail.getStoreId());
        pricePushYJSVo.setGoodsNo(priceStoreDetail.getGoodsNo());
        pricePushYJSVo.setPriceType(priceStoreDetail.getPriceTypeCode());

        pricePushYJSPriceTypeToHLProducer.sendMq(pricePushYJSVo);
        logger.warn("修改价格完成并同步商品中心 商品:{}", priceStoreDetail.getGoodsNo());
    }


    private PriceStoreDetail toTargetPriceStoreDetail(PriceStoreDetail sourcePriceDetail, String targetPriceTypeCode, PriceType priceType) {
        PriceStoreDetail newPrice = new PriceStoreDetail();
        BeanUtils.copyProperties(sourcePriceDetail, newPrice);
        newPrice.setPriceTypeCode(targetPriceTypeCode);
        newPrice.setPriceTypeId(priceType.getId());
        newPrice.setPriceTypeName(priceType.getName());
        newPrice.setId(null);
        newPrice.setGmtUpdate(new Date());
        newPrice.setChannelId(adjustPriceConfig.getDefaultChannelId());
        return newPrice;
    }

    @Override
    public void updateBusinessId(Long storeId, Long newBusinessId) {

        PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
        priceStoreDetail.setBusinessId(newBusinessId);
        //取连锁名称
        String businessName = priceSyncBaseService.getBusinessName(newBusinessId);

        priceStoreDetail.setBusinessName(businessName);
        priceStoreDetail.setChannelId(null);


        PriceStoreDetailExample example = new PriceStoreDetailExample();
        example.createCriteria().andStoreIdEqualTo(storeId);

        priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail,example);
    }
    @Override
    public void copyPrice(Long businessId, String storeIds,String goodsNo, String sourcePriceTypeCode, String targetPriceTypeCode, Integer channelId) {


        if (org.apache.commons.lang3.StringUtils.isEmpty(storeIds)){
            throw new BusinessErrorException("请求参数异常");
        }

        String[] storeIdArr = storeIds.split(",");

        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (null == priceTypeMap) {
            logger.info("海典同步处理 获取可用的价格列表异常{}", priceTypeMap);
            return;
        }
        PriceType priceType = priceTypeMap.get(targetPriceTypeCode);

        for (String storeId : storeIdArr){

            if (org.apache.commons.lang3.StringUtils.isEmpty(storeId)){
                continue;
            }

            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(Long.parseLong(storeId))
                .priceTypeCode(sourcePriceTypeCode)
                .channelId(channelId)
                .build();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(goodsNo)){
                query.setGoodsNo(goodsNo);
            }
            List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);
            logger.warn("根据指定价格类型的价格修改到目标价格类型的价格,需要修改的数量:{}", priceList.size());
            if (CollectionUtils.isNotEmpty(priceList)) {

                for (PriceStoreDetail priceStoreDetail:priceList) {

                    PriceStoreDetail record = new PriceStoreDetail();
                    BeanUtils.copyProperties(priceStoreDetail,record);
                    record.setPrice(priceStoreDetail.getPrice());
                    record.setGmtUpdate(new Date());
                    record.setPriceTypeCode(targetPriceTypeCode);
                    record.setPriceTypeId(priceType.getId());
                    record.setPriceTypeName(priceType.getName());
                    record.setId(null);

                    try {
                        int result = priceStoreDetailMapper.insertSelective(record);
                        logger.warn("复制价格结果:{}",result);

                        if (channelId == adjustPriceConfig.getDefaultChannelId()
                            || channelId == adjustPriceConfig.getPushItemCenterChannelId()) {
                            //临时方案药急送的类型给海龙推一下
                            PricePushYJSVo pricePushYJSVo = new PricePushYJSVo();
                            pricePushYJSVo.setPrice(priceStoreDetail.getPrice());
                            pricePushYJSVo.setBusinessId(businessId);
                            pricePushYJSVo.setStoreId(priceStoreDetail.getStoreId());
                            pricePushYJSVo.setGoodsNo(priceStoreDetail.getGoodsNo());
                            pricePushYJSVo.setPriceType(targetPriceTypeCode);
                            pricePushYJSPriceTypeToHLProducer.sendMq(pricePushYJSVo);
                            logger.warn("复制价格成功，并通知商品中心完成:{}",goodsNo);
                        }
                    }catch (Exception e){
                        logger.warn("复制价格异常:",e);
                    }
                }
            }

        }
    }

    @Override
    public void syncGJYJSPrice(Long businessId, String storeIds, String goodsNos) {
        List<Long> storeIdList;
        if (StringUtils.isNotBlank(storeIds)) {
            String[] storeIdStrArray = storeIds.split(",");
            storeIdList = Arrays.stream(storeIdStrArray).map(Long::valueOf).collect(Collectors.toList());
        } else {
            List<MdmStoreBaseToRedisDTO> mdmStoreByBusinessId = feignStoreService.findMdmStoreByBusinessId(businessId);
            storeIdList = mdmStoreByBusinessId.stream().map(MdmStoreBaseToRedisDTO::getStoreId).collect(Collectors.toList());
        }

        storeIdList.forEach(storeId -> asyncTaskExecutor2.execute(() -> {
            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(storeId)
                .channelId(adjustPriceConfig.getPushPosChannelId())
                .priceTypeCode(PTypeEnum.LSJ.getCode())
                .pageSize(100)
                .build();
            if (StringUtils.isNotBlank(goodsNos)) {
                String[] goodsNoArray = goodsNos.split(",");
                List<String> goodsNoList = Arrays.asList(goodsNoArray);
                query.setGoodsNoList(goodsNoList);
            }
            List<PriceStoreDetail> detailList;
            int page = 1;
            do {
                query.setPage(page);
                detailList = priceStoreDetailReadService.query(query);
                if (CollectionUtils.isEmpty(detailList)) {
                    break;
                }
                detailList.forEach(detail -> {
                    priceService.sendPriceChange(detail, PriceChangeEventEnum.INTERFACE_TRIGGER_CHANGE);
//                    priceChangeProducer.sendMq(priceService.buildPriceChange(detail));
                });
                page++;
            } while (CollectionUtils.isNotEmpty(detailList));
        }));
    }

    @Override
    public void syncItemCenter(Long businessId, String storeIds, String priceTypeCode, int channel) {
        List<Long> storeIdList;
        if (StringUtils.isNotBlank(storeIds)) {
            String[] storeIdStrArray = storeIds.split(",");
            storeIdList = Arrays.stream(storeIdStrArray).map(Long::valueOf).collect(Collectors.toList());
        } else {
            List<MdmStoreBaseToRedisDTO> mdmStoreByBusinessId = feignStoreService.findMdmStoreByBusinessId(businessId);
            storeIdList = mdmStoreByBusinessId.stream().map(MdmStoreBaseToRedisDTO::getStoreId).collect(Collectors.toList());
        }

        storeIdList.forEach(storeId -> {
            PriceSyncTaskVO priceSyncTaskVO = new PriceSyncTaskVO();
            priceSyncTaskVO.setStoreId(storeId);
            priceSyncTaskVO.setType(PriceSyncTaskTypeEnum.SYNC_TO_ITEM_CENTER.getType());
            priceSyncTaskVO.setPriceTypeCode(priceTypeCode);
            priceSyncTaskVO.setChannel(channel);
            priceSyncTaskProducer.sendPriceSyncTask(priceSyncTaskVO);
        });
    }
}
