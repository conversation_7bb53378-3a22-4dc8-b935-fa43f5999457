package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.UserRoleRelateDTO;
import com.cowell.pricecenter.builder.dto.StoreChannelMappingDTO;
import com.cowell.pricecenter.cache.CacheVar;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.entity.AdjustPriceOrderDetailExample.Criteria;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.AdjustPriceOrderDetailMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderOrgStoreDetailMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderDetailExMapper;
import com.cowell.pricecenter.mq.producer.AdjustPriceOrderDetailLimitStatusProducer;
import com.cowell.pricecenter.mq.producer.AdjustPriceOrderDetailSupplementDataProducer;
import com.cowell.pricecenter.mq.vo.AdjustPriceOrderDetailSupplementDataVO;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.dto.response.amis.AmisCommonResponse;
import com.cowell.pricecenter.service.dto.response.amis.ColumnVO;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.feign.*;
import com.cowell.pricecenter.service.feign.facade.ItemSearchEngineFacadeService;
import com.cowell.pricecenter.service.feign.vo.*;
import com.cowell.pricecenter.service.tag.StoreAttrTagService;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.service.vo.ShortNameVO;
import com.cowell.pricecenter.utils.*;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.cowell.pricecenter.web.rest.errors.AmisBusinessException;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.cowell.rule.engine.sdk.RuleEngineClient;
import com.cowell.rule.engine.sdk.enums.FunctionResultTypeEnum;
import com.cowell.rule.engine.sdk.model.CommonResponse;
import com.cowell.rule.engine.sdk.model.FunctionParam;
import com.cowell.rule.engine.sdk.model.FunctionResult;
import com.cowell.rule.engine.sdk.model.RuleFunctionMain;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * @program: pricecenter
 * @description: 调价单明细2.0版本的服务实现
 * @author: jmlu
 * @create: 2022-03-24 10:51
 **/

@Service
public class AdjustPriceOrderDetailV2ServiceImpl implements IAdjustPriceOrderDetailV2Service {

    private final Logger logger = LoggerFactory.getLogger(AdjustPriceOrderDetailV2ServiceImpl.class);

    @Resource
    private AdjustPriceOrderDetailExMapper adjustPriceOrderDetailExMapper;

    @Resource
    private AdjustPriceOrderDetailMapper adjustPriceOrderDetailMapper;

    @Resource
    private AdjustPriceOrderMapper adjustPriceOrderMapper;

    @Resource
    private AdjustPriceOrderOrgDetailService adjustPriceOrderOrgDetailService;

    @Resource
    private PriceManageControlOrderDetailReadService priceManageControlOrderDetailReadService;

    @Resource
    private IPermissionExtService permissionExtService;

    @Resource
    private AdjustPriceOrderDetailSupplementDataProducer supplementDataProducer;

    @Resource
    private IBasePriceOrderService basePriceOrderService;

    @Resource
    private ITocExtService tocExtService;

    @Resource
    private IMarketingExtService marketingExtService;

    @Resource
    private ISearchExtService searchExtService;

    @Resource
    private BizGoodsWhiteListService bizGoodsWhiteListService;

    @Autowired
	private IAdjustPriceOrderV2Service adjustPriceOrderV2Service;

    @Autowired
	private TransactionTemplate transactionTemplate;

    @Autowired
    private PriceOrderTabTypeDetailService priceOrderTabTypeDetailService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PriceBusinessDetailHistoryService priceBusinessDetailHistoryService;

    @Autowired
    private StoreGroupService storeGroupService;

    @Autowired
    private ErpBizSupportService erpBizSupportService;

    @Autowired
    @Qualifier("adjustOrderDetailImportThreadExecutor")
    private AsyncTaskExecutor adjustOrderDetailImportThreadExecutor;

    @Autowired
    @Qualifier("adjustOrderDetailSearchThreadExecutor")
    private AsyncTaskExecutor adjustOrderDetailSearchThreadExecutor;

    @Autowired
    private AdjustLimitConfigService adjustControlService;

    @Autowired
    @Qualifier("getMatchPriceManageControlOrderPricesThreadExecutor")
    private AsyncTaskExecutor getPriceManageControlOrderPricesMaxThreadExecutor;

    @ApolloJsonValue("${adjust.price.type.channel.copy:[]}")
    public List<AdjustPriceTypeAndChannelCopy> adjustPriceTypeAndChannelCopyList;

    @Autowired
    private FeignForestService feignForestService;

    @Autowired
    private PriceDictionaryService priceDictionaryService;

    @Autowired
    private AdjustPriceOrderDetailLimitStatusProducer limitStatusProducer;

    @Resource
    private SearchService searchService;

    @Autowired
    private AdjustPriceOrderOrgStoreDetailMapper adjustPriceOrderOrgStoreDetailMapper;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private ItemSearchEngineFacadeService itemSearchEngineFacadeService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private NyuwaErpService nyuwaErpService;

    @Autowired
    private ItemCenterCpservice itemCenterCpservice;

    @Autowired
    private StockcenterService stockcenterService;

    @Autowired
    private ItemCenterCpExtService itemCenterCpExtService;

    @Autowired
    @Qualifier("adjustOrderDetailExportThreadExecutor")
    private AsyncTaskExecutor adjustOrderDetailExportThreadExecutor;

    @Autowired
    @Qualifier("sendAdjustOrderDetailThreadExecutor")
    private AsyncTaskExecutor sendAdjustOrderDetailThreadExecutor;

    @Autowired
    private PriceMdmDataService priceMdmDataService;

    @Autowired
    private StoreAttrTagService storeAttrTagService;

    @Autowired
    private RuleEngineClient ruleEngineClient;

    /**
     * 最后1次采购价类型编码
     */
    @Value("${last.purchase.price.type}")
    public String lastPurchasePriceType;
    /**
     * 定价建议类型编码
     */
    @Value("${suggest.price.type}")
    public String suggestPriceType;

    /**
     * 调价单添加或导入验证门店组、门店标签包含的门店不能大于1000
     */
    @Value("${adjust.storegroup.storemaxcount:1000}")
    public Integer storemaxcount;

    /**
     * 当前登录人连锁包含配置中 不显示执价结果按钮 并且不显示最后一次采购价
     */
    @Value("${display.effect.result.button:}")
    private String displayEffectResultButton;

    /**
     * 集团参考零售价:refretailprice,公司参考零售价:comretailprice,参考进价:refpurchprice,最低限价:floorprice
     */
    @Value("${price.type.reference:}")
    private String priceTypeReference;

    @ApolloJsonValue("${show.beforeadjustgoodstype:[]}")
    private List<Long> showBeforeAdjustgoodstypeBusinessIdList;

    @ApolloJsonValue("${price.adjust.jmd.roles:[]}")
    private List<String> priceAdjustJmdRoles;

    private static final Integer TIMETOLIVEHOURS = 1;

    // 查询es最大请求数量
    @Value("${whiteList.query.page.max:50}")
    private Integer whiteListQueryPageMax;

    /**
     * B2C渠道 价格
     */
    @ApolloJsonValue("${b2c.channel:{}}")
    public Map<String, List<String>> b2cChannelsMap;

    /**
     * 调价单明细成本价
     */
    @Value("${adjust.order.detail.cbj}")
    public String detailCbj;

    // portal业务类型
    @Value("${portal.biztype:41}")
    private Integer portalBizType;

    @ApolloJsonValue("${business.tagbiztype.mapper:[]}")
    public List<BusinessTagBizTypeDto> businessTagBizTypeList;

    /**
     * 规则引擎 医保限价管控 规则编号
     */
    @Value("${ruleengine.ruleno:pqNrZoFPZmOntF5Z-DuNu}")
    private String ruleNo;


    public List<String> getB2CPriceTypeConfig(Integer b2cChannelId) {
        if(MapUtils.isEmpty(b2cChannelsMap) || Objects.isNull(b2cChannelId)){
            return null;
        }
        return b2cChannelsMap.get(String.valueOf(b2cChannelId));
    }

    public List<String> getB2CChannelIdList() {
        if(MapUtils.isEmpty(b2cChannelsMap)){
            return Lists.newArrayList();
        }
        List<String> keyList = new ArrayList<>(b2cChannelsMap.keySet());
        return keyList;
    }

    @Override
    public PageResult<Map<String, Object>> listAdjustPriceOrderDetailsPageById(AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO, boolean isEdit, boolean isAudit) {
        if (param.getAdjustPriceOrderId() == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = basePriceOrderService.getAdjustPriceOrderById(param.getAdjustPriceOrderId());
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        PageResult<Map<String, Object>> pageResult = listAdjustPriceOrderDetailsPage(adjustPriceOrder, param, userDTO, isEdit, isAudit);
        List<Map<String, Object>> rows = pageResult.getRows();
        if(adjustPriceOrder.getAuditStatus()==AuditStatusEnum.IN_PREPARATION.getCode() ||
        		adjustPriceOrder.getAuditStatus()==AuditStatusEnum.UN_SUBMITTED.getCode() ||
        		adjustPriceOrder.getAuditStatus()==AuditStatusEnum.UN_AUDIT.getCode()) {
    		for (Map<String, Object> map : rows) {
				map.put("detailAuditStatus", "");
				map.put("detailAuditMessage", "");
			}
    	}
        return pageResult;
    }

    @Override
    public PageResult<Map<String, Object>> listAdjustPriceOrderDetailsPageByCode(AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO, boolean isEdit, boolean isAudit) {
        if (param.getAdjustCode() == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = basePriceOrderService.getAdjustPriceOrderByAdjustCode(param.getAdjustCode());
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        PageResult<Map<String, Object>> pageResult = listAdjustPriceOrderDetailsPage(adjustPriceOrder, param, userDTO, isEdit, isAudit);
        List<Map<String, Object>> rows = pageResult.getRows();
        rows.forEach(item -> {
    		item.put("useAudit", true);
        	String extend1 = item.get("extend1").toString();
        	Optional<AdjustPriceOrderDetailExtend1> optionalDetailExtend1 = AdjustPriceOrderDetailExtend1.getInstance(extend1);
        	if(optionalDetailExtend1.isPresent()) {
        		AdjustPriceOrderDetailExtend1 detailExtend1 = optionalDetailExtend1.get();
        		if(null!=detailExtend1.getDetailAuditStatus() && DetailAuditStatusEnum.AUDIT_PASS.getCode()==detailExtend1.getDetailAuditStatus()) {
        			item.put("detailAuditStatus",isAudit==true?DetailAuditStatusEnum.AUDIT_PASS.isStatus():DetailAuditStatusEnum.AUDIT_PASS.getMessage());
        		}else{
        			item.put("detailAuditStatus", isAudit==true?DetailAuditStatusEnum.AUDIT_REFUSE.isStatus():DetailAuditStatusEnum.AUDIT_REFUSE.getMessage());
        			if(null!=detailExtend1.getDetailAuditStatus() && DetailAuditStatusEnum.AUDIT_REFUSE.getCode()==detailExtend1.getDetailAuditStatus()) {
        				item.put("useAudit", false);
        			}
        		}
        	}
        	List<Long> nextAuditors = getNextAuditors(adjustPriceOrder);
        	if(CollectionUtils.isNotEmpty(nextAuditors)) {
        		if(!nextAuditors.contains(param.getWorkcode())) {
    				item.put("useAudit", false);
    			}
        	}else {
        		item.put("useAudit", false);
        	}
        	if(null!=adjustPriceOrder.getAuditStatus() && (adjustPriceOrder.getAuditStatus()==AuditStatusEnum.AUDIT_PASS.getCode() ||
        			adjustPriceOrder.getAuditStatus()==AuditStatusEnum.AUDIT_REJECTED.getCode())) {
        		item.put("useAudit", false);
        	}
        });
        return pageResult;
    }

    /**
     *
     * @Title: getNextAuditors
     * @Description: 获取当前审批人   8开头8位员工编号
     * @param: @param adjustPriceOrder
     * @param: @return
     * @return: List<String>
     * @throws
     */
    private List<Long> getNextAuditors(AdjustPriceOrder adjustPriceOrder){
    	String orderExtend = adjustPriceOrder.getExtend();
    	Optional<AdjustPriceOrderExtend> orderExtendInstance = AdjustPriceOrderExtend.getInstance(orderExtend);
    	if(orderExtendInstance.isPresent()) {
    		AdjustPriceOrderExtend adjustPriceOrderExtend = orderExtendInstance.get();
    		if(StringUtils.isNotBlank(adjustPriceOrderExtend.getNextAuditors())) {
    			List<Long> nextAuditorsList = Arrays.asList(adjustPriceOrderExtend.getNextAuditors().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    			return nextAuditorsList;
    		}
    	}
    	return null;
    }

    @Override
    public PageResult<OAMobileAdjustPriceOrderDetailVO> listMobileAdjustPriceOrderDetailsPageById(AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO) {
        if (param.getAdjustPriceOrderId() == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = basePriceOrderService.getAdjustPriceOrderById(param.getAdjustPriceOrderId());
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        PageResult<OAMobileAdjustPriceOrderDetailVO> pageResult = listMobileAdjustPriceOrderDetailsPage(adjustPriceOrder, param, userDTO);
        return pageResult;
    }

    @Override
    public PageResult<OAMobileAdjustPriceOrderDetailVO> listMobileAdjustPriceOrderDetailsPageByCode(
        AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO) {
        if (param.getAdjustCode() == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = basePriceOrderService.getAdjustPriceOrderByAdjustCode(param.getAdjustCode());
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        PageResult<OAMobileAdjustPriceOrderDetailVO> pageResult = listMobileAdjustPriceOrderDetailsPage(adjustPriceOrder, param, userDTO);
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void addAdjustPriceOrderDetails(AdjustPriceOrderDetailAddV2Param param, TokenUserDTO userDTO, Integer createDataSource) {
    	clearCacheRedisMsg(param.getAdjustPriceOrderId());
    	AdjustPriceOrderExample orderExample = new AdjustPriceOrderExample();
    	orderExample.createCriteria().andIdEqualTo(param.getAdjustPriceOrderId());
    	AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustPriceOrderId());
    	if(null == adjustPriceOrder) {
    		throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
    	}
    	if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())){
    		saveB2cAdjustPriceOrderDetail(param, userDTO);
    	}else {
	        checkAdjustPriceOrderDetailAddV2Param(param);
	        //验证 并组装条件 商品、机构是否存在交集数据
	        if(null==createDataSource || createDataSource!=CreateAdjustDataSourceEnum.CONTROL_NOTICE_ADD.getCode()) {
	        	if(GoodsScopeEnum.SENSITIVE_GOODS.getCode()==adjustPriceOrder.getGoodsScope()) {
	        		checkSensitiveGoods(adjustPriceOrder, param, userDTO, true);
	        	}else {
	        		checkCreateParamIntersectionOfSets(param, userDTO);
	        	}
	        }
	        batchAddAdjustPriceOrderDetails(param, userDTO);
    	}
    }

    private void checkSensitiveGoods(AdjustPriceOrder order,AdjustPriceOrderDetailAddV2Param param, TokenUserDTO userDTO,boolean checkAdjustDetailOrg){
    	List<String> sensitiveGoodsList = priceMdmDataService.getEntityListByTypeAndEntityIdList(CommonEnums.MdmDataBizEntryTypeEnum.MDM_BIZ_SENSITIVE_GOODS_LIST.getCode(), param.getGoodsNoList());
    	if(CollectionUtils.isEmpty(sensitiveGoodsList)) {
    		throw new AmisBadRequestException(ReturnCodeEnum.SENSITIVE_GOODS_ERROR_ALERT,"保存的商品都不在敏感商品目录中");
    	}
    	//敏感商品范围外的商品编码
    	List<String> noScopeSensitiveGoodsList = Lists.newArrayList();
    	//不在敏感商品映射的机构的门店
    	List<String> sensitiveGoodsMapperNoScopeStoreList = Lists.newArrayList();
    	//存在敏感商品信息中的商品编码
    	List<String> existsSensitiveGoodsList = Lists.newArrayList();
    	//存在调价单明细中的门店
    	List<Long> existsAdjustDetailStoreOrgIdList = Lists.newArrayList();
    	//以下门店机构与已存在的调价单明细机构都相同
    	List<String> sensitiveGoodsUnStoreSameList = Lists.newArrayList();
    	String goodsTabTypeValues = "";
    	if(order.getAdjustOrgTabType()!=PriceOrderTabTypeEnum.STORE_LIST.getType()) {
    		PriceOrderTabTypeDetail tabTypeDetail = selectPriceOrderTabTypeDetail(order.getAdjustCode());
    		goodsTabTypeValues = tabTypeDetail.getTabTypeValue();
    	}
    	PriceOrderGoodsOrgDetailV2Param orgDetailParam = buildPriceOrderGoodsOrgDetailV2Param(param);
    	//主单机构转化为门店id
    	List<Long> currAdjustStoreIdList = currAdjustOrderStoreIdList(orgDetailParam, userDTO);
    	//主单门店id转化为sapCode
    	List<String> currAdjustSapCodeList = currAdjustStoreIdList.stream().map(id -> CacheVar.storeCacheMap.get(id).getSapCode()).filter(sapCode -> sapCode != null && !sapCode.isEmpty()).collect(Collectors.toList());
    	for (String goodsNo : param.getGoodsNoList()) {
    		if(sensitiveGoodsList.contains(goodsNo)) {
    			existsSensitiveGoodsList.add(goodsNo);
			}else {
				noScopeSensitiveGoodsList.add(goodsNo);
			}
		}
    	if(CollectionUtils.isNotEmpty(noScopeSensitiveGoodsList) && CollectionUtils.isEmpty(existsSensitiveGoodsList)) {
	   		 String unSensitiveGoods = noScopeSensitiveGoodsList.stream().collect(Collectors.joining(","));
	   		 unSensitiveGoods = "如下商品不在敏感商品目录中:"+unSensitiveGoods;
	   		 throw new AmisBadRequestException(ReturnCodeEnum.SENSITIVE_GOODS_ERROR_ALERT,unSensitiveGoods);
	   	}
    	param.setGoodsNoList(existsSensitiveGoodsList);
    	List<GoodsNoOrgIdsDTO> goodsNoOrgList = Lists.newArrayList();
    	List<String> commonSapCodes = Lists.newArrayList();
    	List<String> filteredSapCodes = Lists.newArrayList();
    	List<Long> orgIdList = Lists.newArrayList();
    	List<AdjustPriceOrderOrgStoreDetail> localOrgStoreDetailList = Lists.newArrayList();
    	Date operateTime = new Date();
    	List<Map<String, List<OrgLevelVO>>> goodsOrgLevelList = Lists.newArrayList();
    	List<List<String>> partition = Lists.partition(param.getGoodsNoList(), 50);
    	Map<String,List<String>> sensitiveGoodsMap = Maps.newHashMap();
    	for (List<String> subGoodsList : partition) {
    		sensitiveGoodsMap.clear();
    		sensitiveGoodsMap = priceMdmDataService.getEntityMapByTypeAndEntityIdList(CommonEnums.MdmDataBizEntryTypeEnum.MDM_BIZ_STORE_GROUP_SENSITIVE_GOODS_REF.getCode(), subGoodsList);
    		for (String goodsNo : subGoodsList) {
        		commonSapCodes.clear();filteredSapCodes.clear();goodsOrgLevelList.clear();localOrgStoreDetailList.clear();orgIdList.clear();;
        		List<String> sensitiveSapCodeList = sensitiveGoodsMap.get(goodsNo);
        		//调价单商品机构在敏感商品映射的门店
                commonSapCodes = currAdjustSapCodeList.stream().filter(sensitiveSapCodeList::contains).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(commonSapCodes)){
                	orgIdList = commonSapCodes.stream().filter(CacheVar.storeSapCodeCacheMap::containsKey).map(CacheVar.storeSapCodeCacheMap::get).map(OrgToRedisDTO::getId).collect(Collectors.toList()); // 收集结果到 List<Long>
                	if(CollectionUtils.isNotEmpty(orgIdList)) {
                		if(checkAdjustDetailOrg) {
                			//查询当前调价单相同商品编码的机构信息相同门店的进行过滤
                    		goodsOrgLevelList = goodsOrgLevelList(order.getAdjustCode(),Lists.newArrayList(goodsNo));
                    		if(CollectionUtils.isNotEmpty(goodsOrgLevelList)){
                    			List<OrgLevelVO> orgLevelVoList = goodsOrgLevelList.stream().flatMap(map -> map.values().stream()).flatMap(List::stream).collect(Collectors.toList());
                        		localOrgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(order.getAdjustCode(), orgLevelVoList,operateTime);
                        		List<Long> localItemOrgIdList = localOrgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getOrgId).collect(Collectors.toList());
                        		List<Long> existsItemOrgId = orgIdList.stream()
                                        .filter(id -> localItemOrgIdList.contains(id))
                                        .collect(Collectors.toList());
                        		existsAdjustDetailStoreOrgIdList.addAll(existsItemOrgId);
                        		orgIdList = orgIdList.stream()
                                        .filter(id -> !localItemOrgIdList.contains(id))
                                        .collect(Collectors.toList());
                        		if(CollectionUtils.isEmpty(orgIdList)) {
                        			sensitiveGoodsUnStoreSameList.add(goodsNo);
                        		}
                    		}
                		}
                		if(CollectionUtils.isNotEmpty(orgIdList)) {
                			String orgIdsStr = Joiner.on(",").join(orgIdList);
                        	goodsNoOrgList.add(new GoodsNoOrgIdsDTO(order.getAdjustCode(),goodsNo,order.getAdjustOrgTabType(),
                       				orgIdsStr,"","",goodsTabTypeValues,OverrideLowerLevelEnum.YES.getCode(),false,param.getPrice(), param.getAdjustDetailReason()));
                        	param.setTempOrgIdList(orgIdList);
                		}
                    }
                	//调价单商品机构不在敏感商品里面的门店
                    filteredSapCodes = currAdjustSapCodeList.stream().filter(sapCode -> !sensitiveSapCodeList.contains(sapCode)).collect(Collectors.toList());
                    sensitiveGoodsMapperNoScopeStoreList.addAll(filteredSapCodes);
                    if(null!=sensitiveSapCodeList) {
                    	sensitiveSapCodeList.clear();
                    }
                }else {
                	noScopeSensitiveGoodsList.add(goodsNo);
                }
    		}
		}
    	StringBuffer sb = new StringBuffer();
    	if(CollectionUtils.isEmpty(goodsNoOrgList)){
    		sb.append("全部商品添加失败!").append("失败原因如下：").append("\n");
    	}else if(CollectionUtils.isNotEmpty(noScopeSensitiveGoodsList) || CollectionUtils.isNotEmpty(sensitiveGoodsMapperNoScopeStoreList) ||
    			CollectionUtils.isNotEmpty(existsAdjustDetailStoreOrgIdList) || CollectionUtils.isNotEmpty(sensitiveGoodsUnStoreSameList)) {
    		sb.append("部分成功!,失败原因如下：").append("\n");
    	}
    	if(CollectionUtils.isNotEmpty(existsAdjustDetailStoreOrgIdList)) {
    		List<String> existsAdjustDetailStoreOrgIdStrList = Lists.newArrayList();
    		for (Long storeOrgId : existsAdjustDetailStoreOrgIdList) {
    			Long storeId = CacheVar.storeOrgIdAndOutIdMapping.get(storeOrgId);
    			if(null == storeId) {
    				continue;
    			}
    			OrgToRedisDTO orgToRedisDTO = CacheVar.storeCacheMap.get(storeId);
    			if(null!=orgToRedisDTO && StringUtils.isNotBlank(orgToRedisDTO.getSapCode())) {
    				existsAdjustDetailStoreOrgIdStrList.add(orgToRedisDTO.getSapCode());
    			}
			}
            sb.append("以下门店已存在，不能重复添加:").append(String.join(",", existsAdjustDetailStoreOrgIdStrList)).append(";").append("\n");
        }
        if(CollectionUtils.isNotEmpty(sensitiveGoodsUnStoreSameList)) {
            sb.append("以下商品已存在，不能重复添加:").append(String.join(",", sensitiveGoodsUnStoreSameList)).append(";").append("\n");
        }
        if(CollectionUtils.isNotEmpty(sensitiveGoodsMapperNoScopeStoreList)) {
            sb.append("以下门店未配置敏感商品目录:").append(String.join(",", sensitiveGoodsMapperNoScopeStoreList)).append(";").append("\n");
        }
    	if(CollectionUtils.isNotEmpty(noScopeSensitiveGoodsList)) {
    		sb.append("以下商品不存在于敏感商品目录中:").append(String.join(",", noScopeSensitiveGoodsList)).append(";").append("\n");
    	}
    	logger.info("checkSensitiveGoods|SensitiveGoodsMessage：{}",sb.toString());
    	if(CollectionUtils.isEmpty(goodsNoOrgList)){
    		throw new AmisBadRequestException(ReturnCodeEnum.SENSITIVE_GOODS_ERROR_ALERT,sb.toString());
    	}
    	if(StringUtils.isNotBlank(sb.toString())){
    		cacheRedisMsg(order.getId(),sb.toString());
    	}
    	param.setGoodsNoOrgList(goodsNoOrgList);
    }

    private void clearCacheRedisMsg(Long adjustId) {
    	String cacheKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ADJUST_SENSITIVE_GOODS_MESSAGE+adjustId;
    	RBucket<String> bucket = redissonClient.getBucket(cacheKey);
    	if(bucket.isExists()) {
    		bucket.deleteAsync();
    	}
    }

    private void cacheRedisMsg(Long adjustId,String msg) {
    	String cacheKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ADJUST_SENSITIVE_GOODS_MESSAGE+adjustId;
		redissonClient.getBucket(cacheKey).set(msg,1,TimeUnit.HOURS);
    }

    private PriceOrderTabTypeDetail selectPriceOrderTabTypeDetail(String adjustCode){
    	PriceOrderTabTypeDetailExample tabTypeDetailExample = new PriceOrderTabTypeDetailExample();
		tabTypeDetailExample.createCriteria().andAdjustOrderCodeEqualTo(adjustCode);
    	List<PriceOrderTabTypeDetail> tabTypeDetailList = priceOrderTabTypeDetailService.selectByExample(tabTypeDetailExample);
    	return tabTypeDetailList.get(0);
    }

    private void saveB2cAdjustPriceOrderDetail(AdjustPriceOrderDetailAddV2Param param, TokenUserDTO userDTO) {
    	Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustPriceOrderId());
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);

        List<GoodsNoOrgIdsDTO> goodsNoOrgIdsList = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(param.getB2cItemList())){
        	List<ItemSkuVo> distinctList = param.getB2cItemList().stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getGoodsNo() + ";" + o.getItemSkuId() + ";" + o.getStoreId()))), ArrayList::new));
        	param.setB2cItemList(distinctList);
        	List<List<ItemSkuVo>> partition = Lists.partition(param.getB2cItemList(), 50);
        	for (List<ItemSkuVo> list : partition) {
        		List<AdjustPriceOrderDetail> detailList = adjustPriceOrderDetailExMapper.selectDetailByB2cGoods(adjustPriceOrder.getAdjustCode(), list);
        		if(CollectionUtils.isNotEmpty(detailList)){
        			adjustPriceOrderDetailExMapper.deleteDetailByB2cGoods(adjustPriceOrder.getAdjustCode(), detailList);
        		}
			}
        	List<Long> channelMappingStoreIdOrgIdList = Lists.newArrayList();
        	channelMappingStoreIdOrgIdList.add(param.getB2cItemList().get(0).getStoreId());
        	List<OrgDTO> orgDtoList = permissionExtService.listOrgByOutId(channelMappingStoreIdOrgIdList, OrgTypeEnum.STORE.getCode());
	   		GoodsNoOrgIdsDTO goodsNoOrgIdsDTO = null;
	   		for (ItemSkuVo itemSkuVo : param.getB2cItemList()) {
	   			goodsNoOrgIdsDTO = new GoodsNoOrgIdsDTO();
	   			goodsNoOrgIdsDTO.setAdjustCode(adjustPriceOrder.getAdjustCode());
	   			goodsNoOrgIdsDTO.setGoodsNo(itemSkuVo.getGoodsNo());
	   			goodsNoOrgIdsDTO.setItemSkuId(itemSkuVo.getItemSkuId().toString());
	   			goodsNoOrgIdsDTO.setGoodsOrgTabType(adjustPriceOrder.getAdjustOrgTabType());
	   			goodsNoOrgIdsDTO.setOrgIds(orgDtoList.get(0).getId().toString());
	   			goodsNoOrgIdsDTO.setOrgNames(itemSkuVo.getStoreName());
	   			goodsNoOrgIdsDTO.setOrgLevels(String.valueOf(OrgLevelTypeEnum.STORE.getCode()));
	   			goodsNoOrgIdsDTO.setGoodsTabTypeValue("");
	   			goodsNoOrgIdsDTO.setOverrideLowerLevel(OverrideLowerLevelEnum.YES.getCode());
	   			goodsNoOrgIdsDTO.setCopyOrderOrgTabType(false);
	   			goodsNoOrgIdsDTO.setAdjustDetailReason(param.getAdjustDetailReason());
	   			goodsNoOrgIdsList.add(goodsNoOrgIdsDTO);
			}
	   	}
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(adjustPriceOrder.getAdjustPriceType());
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(adjustPriceOrder.getChannel());
        List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(adjustPriceOrder.getChannel());
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());
        List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList = getInsertAdjustPriceOrderDetailList(adjustPriceOrder, goodsNoOrgIdsList, priceChannelMap, priceTypeMap,
            channelIdList, priceTypeCodeList, userDTO, operateTime);
        for (AdjustPriceOrderDetail adjustPriceOrderDetail : insertAdjustPriceOrderDetailList) {
        	Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
        	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = instance.get();
        	adjustPriceOrderDetailExtend1.setChannelMappingStoreId(param.getB2cItemList().get(0).getStoreId());
        	adjustPriceOrderDetail.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
		}
        Lists.partition(insertAdjustPriceOrderDetailList, 200)
            .forEach(insertPartitionAdjustPriceOrderDetailList -> adjustPriceOrderDetailExMapper.batchInsert(insertPartitionAdjustPriceOrderDetailList));
        //事务提交后执行
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
            	sendToSupplementDataMsgs(false,insertAdjustPriceOrderDetailList);
            }
        });
    }

    private PriceOrderGoodsOrgDetailV2Param buildPriceOrderGoodsOrgDetailV2Param(AdjustPriceOrderDetailAddV2Param param) {
    	AdjustPriceOrderExample orderExample = new AdjustPriceOrderExample();
    	orderExample.createCriteria().andIdEqualTo(param.getAdjustPriceOrderId());
    	AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustPriceOrderId());
    	List<Long> orgIdList = Lists.newArrayList();
    	List<Integer> orgLevelList = Lists.newArrayList();
		//param.getOrgTabType为空 复制主表机构信息
    	if(null==param.getOrgTabType()) {
    		AdjustPriceOrderOrgDetailExample orgDetailExample = new AdjustPriceOrderOrgDetailExample();
	    	orgDetailExample.createCriteria().andAdjustCodeEqualTo(adjustPriceOrder.getAdjustCode());
	    	List<AdjustPriceOrderOrgDetail> orgDetailList = adjustPriceOrderOrgDetailService.selectByExample(orgDetailExample);
	    	List<AdjustPriceOrderOrgDetail> orgDetailListSort = orgDetailList.stream().sorted(Comparator.comparing(AdjustPriceOrderOrgDetail::getOrgId)).collect(Collectors.toList());
	    	orgIdList = orgDetailListSort.stream().map(AdjustPriceOrderOrgDetail::getOrgId).collect(Collectors.toList());
	    	orgLevelList = orgDetailListSort.stream().map(AdjustPriceOrderOrgDetail::getOrgLevel).collect(Collectors.toList());
    	}else {
    		if(PriceOrderTabTypeEnum.STORE_LIST.getType() == param.getOrgTabType() && null != param.getOrgIdList()) {
    			orgIdList = param.getOrgIdList();
    		}else if((PriceOrderTabTypeEnum.STORE_GROUP.getType() == param.getOrgTabType() ||
    				PriceOrderTabTypeEnum.STORE_FLAG.getType() == param.getOrgTabType()) &&
    				CollectionUtils.isNotEmpty(param.getTabTypeValList())) {
    			orgIdList = param.getOrgIdList();
    		}else {
    			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
    		}
    	}
    	PriceOrderGoodsOrgDetailV2Param orgDetail = new PriceOrderGoodsOrgDetailV2Param();
    	orgDetail.setOrderCode(adjustPriceOrder.getAdjustCode());
    	orgDetail.setGoodsNoList(param.getGoodsNoList());
    	orgDetail.setOrgIdList(orgIdList);
    	orgDetail.setOrgLevelList(orgLevelList);
    	return orgDetail;
    }

    /**
     *
     * @Title: checkCreateParamIntersectionOfSets
     * @Description: 验证 商品+机构 是否存在交集
     * @param: @param param
     * @param: @param userDTO
     * @return: void
     * @throws
     */
    private void checkCreateParamIntersectionOfSets(AdjustPriceOrderDetailAddV2Param param,TokenUserDTO userDTO) {
    	PriceOrderGoodsOrgDetailV2Param orgDetail = buildPriceOrderGoodsOrgDetailV2Param(param);
    	checkSaveGoodsIntersectionOfSets(orgDetail, userDTO);
    }

    @Override
    public boolean isAdjustPriceOrderDetailImportCompleted(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        boolean result = false;
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        boolean isAllDataInsert = isDetailDataRowSuccess(adjustPriceOrder);

        if (isAllDataInsert) {
            int notCompleted = adjustPriceOrderDetailExMapper.countAdjustPriceOrderDetailSupplementNotCompleted(adjustPriceOrder.getAdjustCode());
            result = notCompleted > 0 ? false : true;
        }
        return result;
    }

    @Override
    public  List<AdjustPriceOrderDetail> listByAdjustCodeAndPriceTypeCodeAndChannelId(String adjustPriceCode, String priceTypeCode, Integer channelId) {
        if (adjustPriceCode == null || priceTypeCode == null || channelId == null) {
            return Collections.emptyList();
        }
        AdjustPriceOrderDetailExample adjustPriceOrderDetailExample = new AdjustPriceOrderDetailExample();
        adjustPriceOrderDetailExample.createCriteria()
            .andAdjustCodeEqualTo(adjustPriceCode)
            .andPriceTypeCodeEqualTo(priceTypeCode)
            .andChannelIdEqualTo(channelId);
        List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = adjustPriceOrderDetailMapper.selectByExample(adjustPriceOrderDetailExample);
        return adjustPriceOrderDetailList == null ? Collections.emptyList() : adjustPriceOrderDetailList;
    }

    /**
     * 调价单商品明细个数是否正确，个数应该等于 渠道个数 * 价格类型个数 * 商品个数
     * @param adjustPriceOrder
     */
    private boolean isDetailDataRowSuccess(AdjustPriceOrder adjustPriceOrder) {
        AdjustPriceOrderDetailExample detailExample = new AdjustPriceOrderDetailExample();
        detailExample.createCriteria()
            .andAdjustCodeEqualTo(adjustPriceOrder.getAdjustCode())
            .andStatusEqualTo((byte)StatusEnum.NORMAL.getCode());
        long count = adjustPriceOrderDetailMapper.countByExample(detailExample);

        int priceTypeCount = Splitter.on(PriceConstant.VALUE_SEPARATOR).splitToList(adjustPriceOrder.getAdjustPriceType()).size();
        int channelCount = Splitter.on(PriceConstant.VALUE_SEPARATOR).splitToList(adjustPriceOrder.getChannel()).size();
        int goodsCount = adjustPriceOrderDetailExMapper.countOfGoodsByAdjustCode(adjustPriceOrder.getAdjustCode());
        boolean result =  count == priceTypeCount * channelCount * goodsCount ? true : false;

        return result;
    }

    @Override
    public boolean isAdjustPriceOrderDetailImportCompletedByCode(String adjustCode, TokenUserDTO userDTO) {
        if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        int notCompleted = adjustPriceOrderDetailExMapper.countAdjustPriceOrderDetailSupplementNotCompleted(adjustCode);
        return notCompleted > 0 ? false : true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editAdjustPriceOrderDetail(AmisTableEditParam param, TokenUserDTO userDTO) {
        Date now = new Date();
        checkAmisTableEditParam(param);
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustPriceOrderId());
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());
        List<Map<String, String>> rowDiffList = param.getRowsDiff();
        List<Map<String, String>> rowList = param.getRows();
        for (int i = 0; i < rowDiffList.size(); i++) {
            editOneAdjustPriceOrderDetail(rowDiffList.get(i), rowList.get(i), userDTO, now, adjustPriceOrder, priceTypeCodeList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void immediatelyEditAdjustPriceOrderDetail(Map<String, String> param, TokenUserDTO userDTO) {
        if (param == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        Date now = new Date();
        String adjustPriceOrderIdStr = param.get("adjustPriceOrderId");
        if (adjustPriceOrderIdStr == null) {
            logger.info("<===[AdjustPriceOrderDetailV2ServiceImpl.immediatelyEditAdjustPriceOrderDetail] 调价单号和商品编码都不能为空");
            throw new AmisBusinessException(ReturnCodeEnum.PARAM_ERROR);
        }
        Long adjustPriceOrderId = Long.parseLong(adjustPriceOrderIdStr);
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null != storeId){
            Arrays.asList("price_LSJ", "price_HYJ", "price_CLJ")
                .forEach(key -> {
                    if (StringUtils.isBlank(param.get(key))) {
                        param.put(key, Constants.INVALID_INDEX.toString());
                    }
                });
        }
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());

        Map<String, String>  rowDiff = getAmisRowDiffFromMap(param, priceTypeCodeList,adjustPriceOrder);
        editOneAdjustPriceOrderDetail(rowDiff, param, userDTO, now, adjustPriceOrder, priceTypeCodeList);
    }

    @Override
    public List<AdjustPriceOrderDetail> copyByAdjustPriceTypeAndChannelCopyList(Long adjustPriceOrderId) {
        Date operateTime = new Date();
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        deleteAllDataNotInAdjustPriceTypeCodes(adjustPriceOrder.getAdjustCode(), adjustPriceOrder.getAdjustPriceType());
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());
        List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(adjustPriceOrder.getChannel());
        if (adjustPriceTypeAndChannelCopyList == null) {
            logger.error("<===[AdjustPriceOrderDetailV2ServiceImpl.copyByAdjustPriceTypeAndChannelCopyList] adjustPriceTypeAndChannelCopyList: {} 配置数据不能为空");
            return null;
        }
        logger.info("<===[AdjustPriceOrderDetailV2ServiceImpl.copyByAdjustPriceTypeAndChannelCopyList] adjustPriceTypeAndChannelCopyList: {}", adjustPriceTypeAndChannelCopyList);
        List<AdjustPriceTypeAndChannelCopy> enableAdjustPriceTypeAndChannelCopyList =
            adjustPriceTypeAndChannelCopyList.stream().filter(copy -> copy.getEnable() != null && copy.getEnable()).collect(Collectors.toList());;
        boolean isDataSuccess = checkAdjustPriceTypeAndChannelCopyList(enableAdjustPriceTypeAndChannelCopyList);
        if (!isDataSuccess) {
            logger.error("<===[AdjustPriceOrderDetailV2ServiceImpl.copyByAdjustPriceTypeAndChannelCopyList] adjustPriceTypeAndChannelCopyList: {} 配置数据有问题" ,
                adjustPriceTypeAndChannelCopyList);
            return null;
        }

        if (CollectionUtils.isEmpty(priceTypeCodeList) || CollectionUtils.isEmpty(channelIdList)) {
            logger.error("<===[AdjustPriceOrderDetailV2ServiceImpl.copyByAdjustPriceTypeAndChannelCopyList] adjustPriceOrder: {} 调价单价格类型和渠道数据有问题" ,
                adjustPriceOrder);
            return null;
        }
        return executeCopyByAdjustPriceTypeAndChannelCopyList(adjustPriceOrder, priceTypeCodeList, channelIdList, enableAdjustPriceTypeAndChannelCopyList, operateTime);
    }

    @Override
    public void deleteAllDataNotInAdjustPriceTypeCodes(String adjustCode, String priceTypeCodes) {
        if (adjustCode == null || StringUtils.isBlank(priceTypeCodes)) {
            return;
        }
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(priceTypeCodes);
        AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode)
            .andPriceTypeCodeNotIn(priceTypeCodeList);

        adjustPriceOrderDetailMapper.deleteByExample(example);
    }

    private List<AdjustPriceOrderDetail> executeCopyByAdjustPriceTypeAndChannelCopyList(AdjustPriceOrder adjustPriceOrder, List<String> orderPriceTypeCodeList, List<Integer> orderChannelIdList,
        List<AdjustPriceTypeAndChannelCopy> adjustPriceTypeAndChannelCopyList, Date operateTime) {
        Set<String> orderPriceTypeCodeSet = orderPriceTypeCodeList.stream().collect(Collectors.toSet());
        Set<Integer> orderChannelIdSet = orderChannelIdList.stream().collect(Collectors.toSet());
        Map<AdjustPriceTypeAndChannelSource, List<AdjustPriceTypeAndChannelCopy>> toCopyMap = Maps.newHashMap();
        Set<String> priceTypeCodeSet = Sets.newHashSet();
        Set<Integer> channelIdSet = Sets.newHashSet();
        for (AdjustPriceTypeAndChannelCopy adjustPriceTypeAndChannelCopy : adjustPriceTypeAndChannelCopyList) {
            priceTypeCodeSet.add(adjustPriceTypeAndChannelCopy.getTargetPriceTypeCode());
            channelIdSet.add(adjustPriceTypeAndChannelCopy.getTargetChannelId());
            if (isNeedToCopy(adjustPriceTypeAndChannelCopy, orderPriceTypeCodeSet, orderChannelIdSet)) {
                AdjustPriceTypeAndChannelSource adjustPriceTypeAndChannelSource = AdjustPriceTypeAndChannelSource.getFromAdjustPriceTypeAndChannelCopy(adjustPriceTypeAndChannelCopy);
                List<AdjustPriceTypeAndChannelCopy> copyList = Lists.newArrayList();
                if (toCopyMap.containsKey(adjustPriceTypeAndChannelSource)) {
                    copyList = toCopyMap.get(adjustPriceTypeAndChannelSource);
                } else {
                    toCopyMap.put(adjustPriceTypeAndChannelSource, copyList);
                }
                copyList.add(adjustPriceTypeAndChannelCopy);
            }
        }
        List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = Lists.newArrayList();
        if (MapUtils.isNotEmpty(toCopyMap)) {
            Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(priceTypeCodeSet.stream().collect(Collectors.toList()));
            Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(channelIdSet.stream().collect(Collectors.toList()));
            for (Map.Entry<AdjustPriceTypeAndChannelSource, List<AdjustPriceTypeAndChannelCopy>> entry : toCopyMap.entrySet()) {
                List<AdjustPriceOrderDetail> toInsertOrderDetailList = getToInsertOrderDetailList(adjustPriceOrder.getAdjustCode(), entry.getKey(), entry.getValue(),
                    priceTypeMap, priceChannelMap, operateTime);
                adjustPriceOrderDetailList.addAll(toInsertOrderDetailList);
            }
            if (adjustPriceOrderDetailList.size() > 0) {
                Lists.partition(adjustPriceOrderDetailList, 200).forEach(orderDetailList -> adjustPriceOrderDetailExMapper.batchInsert(orderDetailList));
            }
        }
        return adjustPriceOrderDetailList;
    }

    private List<AdjustPriceOrderDetail> getToInsertOrderDetailList(String adjustCode, AdjustPriceTypeAndChannelSource source, List<AdjustPriceTypeAndChannelCopy> targets,
        Map<String, PriceType> priceTypeMap, Map<Integer, PriceChannel> priceChannelMap, Date operateTime) {

        List<AdjustPriceOrderDetail>  sourceOrderDetail = listByAdjustCodeAndPriceTypeCodeAndChannelId(adjustCode, source.getPriceTypeCode(), source.getChannelId());
        List<AdjustPriceOrderDetail> orderDetailListResult = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(sourceOrderDetail) && CollectionUtils.isNotEmpty(targets)) {
            Integer size = sourceOrderDetail.size() * targets.size();
            orderDetailListResult = new ArrayList<>(size);
            long [] adjustDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.ADJUST_PRICE_ORDER_DETAIL.getBiz(), size);
            int idIndex = 0;
            for (AdjustPriceOrderDetail adjustPriceOrderDetail : sourceOrderDetail) {
                for (AdjustPriceTypeAndChannelCopy target : targets) {
                    PriceChannel priceChannel = priceChannelMap.get(target.getChannelId());
                    PriceType priceType = priceTypeMap.get(target.getTargetPriceTypeCode());
                    AdjustPriceOrderDetail orderDetailResult = new AdjustPriceOrderDetail();
                    BeanUtils.copyProperties(adjustPriceOrderDetail, orderDetailResult);
                    orderDetailResult.setId(null);
                    orderDetailResult.setAdjustDetailId(String.valueOf(adjustDetailIds[idIndex++]));
                    orderDetailResult.setPriceTypeCode(priceType.getCode());
                    orderDetailResult.setPriceTypeId(priceType.getId());
                    orderDetailResult.setPriceTypeName(priceType.getName());
                    orderDetailResult.setChannelId(priceChannel.getChannelId());
                    orderDetailResult.setChannelEnCode(priceChannel.getChannelEnCode());
                    orderDetailResult.setChannelOutCode(priceChannel.getOutChannelCode());
                    orderDetailResult.setGmtUpdate(operateTime);
                    orderDetailListResult.add(orderDetailResult);
                }
            }
        }
        return orderDetailListResult;
    }

    /**
     * 是否需要去拷贝数据
     * @param adjustPriceTypeAndChannelCopy
     * @param orderPriceTypeCodeSet
     * @param orderChannelIdSet
     * @return
     */
    private boolean isNeedToCopy(AdjustPriceTypeAndChannelCopy adjustPriceTypeAndChannelCopy, Set<String> orderPriceTypeCodeSet, Set<Integer> orderChannelIdSet) {
        return orderPriceTypeCodeSet.contains(adjustPriceTypeAndChannelCopy.getPriceTypeCode()) &&
            orderChannelIdSet.contains(adjustPriceTypeAndChannelCopy.getChannelId()) &&
            (!orderPriceTypeCodeSet.contains(adjustPriceTypeAndChannelCopy.getTargetPriceTypeCode()) ||
            !orderChannelIdSet.contains(adjustPriceTypeAndChannelCopy.getTargetChannelId()));
    }

    /**
     * 校验配置数据是否正确
     * @param adjustPriceTypeAndChannelCopyList
     * @return
     */
    private boolean checkAdjustPriceTypeAndChannelCopyList(List<AdjustPriceTypeAndChannelCopy> adjustPriceTypeAndChannelCopyList) {
        boolean result = true;
        if (CollectionUtils.isEmpty(adjustPriceTypeAndChannelCopyList)) {
            result = false;
        } else {
            List<String> priceTypeCodeList = Lists.newArrayList();
            List<String> targetPriceTypeCodeList = Lists.newArrayList();
            List<Integer> channelIdList = Lists.newArrayList();
            List<Integer> targetChannelIdList = Lists.newArrayList();
            for (AdjustPriceTypeAndChannelCopy adjustPriceTypeAndChannelCopy : adjustPriceTypeAndChannelCopyList) {
                if (adjustPriceTypeAndChannelCopy.getChannelId() == null ||
                    adjustPriceTypeAndChannelCopy.getTargetChannelId() == null ||
                    StringUtils.isEmpty(adjustPriceTypeAndChannelCopy.getPriceTypeCode()) ||
                    StringUtils.isEmpty(adjustPriceTypeAndChannelCopy.getTargetPriceTypeCode())) {
                    result = false;
                    break;
                }
                priceTypeCodeList.add(adjustPriceTypeAndChannelCopy.getPriceTypeCode());
                targetPriceTypeCodeList.add(adjustPriceTypeAndChannelCopy.getTargetPriceTypeCode());
                channelIdList.add(adjustPriceTypeAndChannelCopy.getChannelId());
                targetChannelIdList.add(adjustPriceTypeAndChannelCopy.getTargetChannelId());
            }
            if (result) {
                result = basePriceOrderService.isAllPriceTypeCodeExist(priceTypeCodeList) &&
                    basePriceOrderService.isAllPriceTypeCodeExist(targetPriceTypeCodeList) &&
                    basePriceOrderService.isAllPriceChannelIdExist(channelIdList) &&
                    basePriceOrderService.isAllPriceChannelIdExist(targetChannelIdList)
                ;
            }
        }
        return result;
    }

    /**
     * 找出所有可能被编辑的字段的值
     * @param param
     */
    private Map<String, String> getAmisRowDiffFromMap(Map<String, String> param, List<String> priceTypeCodeList,AdjustPriceOrder adjustPriceOrder) {
        Map<String, String> rowDiff = new HashMap<>();
        for (AdjustPriceOrderDetailBaseColumnEnum commonColumnEnum : AdjustPriceOrderDetailBaseColumnEnum.values()) {
            commonColumnEnum.setAttrMapValue().accept(rowDiff,param);
        }
        if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
        	for (String priceTypeCode : priceTypeCodeList) {
                for (AdjustPriceOrderDetailB2CPriceTypeColumnEnum priceColumnEnum : AdjustPriceOrderDetailB2CPriceTypeColumnEnum.values()) {
                    priceColumnEnum.setAttrMapValue().accept(rowDiff, param, priceTypeCode);
                }
            }
        }else {
        	for (String priceTypeCode : priceTypeCodeList) {
                for (AdjustPriceOrderDetailPriceTypeColumnEnum priceColumnEnum : AdjustPriceOrderDetailPriceTypeColumnEnum.values()) {
                    priceColumnEnum.setAttrMapValue().accept(rowDiff, param, priceTypeCode);
                }
            }
        }
        for (AdjustPriceOrderDetailLastColumnEnum lastColumnEnum : AdjustPriceOrderDetailLastColumnEnum.values()) {
            lastColumnEnum.setAttrMapValue().accept(rowDiff,param);
        }
        for (AdjustPriceOrderDetailAuditColumnEnum auditColumnEnum : AdjustPriceOrderDetailAuditColumnEnum.values()) {
        	auditColumnEnum.setAttrMapValue().accept(rowDiff,param);
        }
        return rowDiff;
    }

    /**
     * 编辑调价单明细单商品
     * @param rowDiff
     * @param row
     * @param userDTO
     * @param now
     * @param adjustPriceOrder
     * @param priceTypeCodeList
     */
    private void editOneAdjustPriceOrderDetail(Map<String, String> rowDiff,  Map<String, String> row, TokenUserDTO userDTO, Date now, AdjustPriceOrder adjustPriceOrder, List<String> priceTypeCodeList) {
        String goodsNo = row.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getName());
        String goodsType = rowDiff.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_9.getName());
        String activityType = rowDiff.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_10.getName());
        String reason = rowDiff.get(AdjustPriceOrderDetailLastColumnEnum.COLUMN_1.getName());
        String priceFlag = rowDiff.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_11.getName());
        String adjustDetailMergeIds = row.get("adjustDetailMergeIds");
        String oaUserId = row.get("oaUserId");
        List<String> editPriceTypeCodeList = priceTypeCodeList;
        if (!isBaseOrLastColumnsChange(goodsType, activityType, reason, priceFlag)) {
            editPriceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromAmisDiffRow(rowDiff.keySet());
        }
        rebateData(adjustPriceOrder,adjustDetailMergeIds,rowDiff);
        for (String priceTypeCode : editPriceTypeCodeList) {
            updateAdjustPriceOrderDetailByPriceCode(userDTO, now, adjustPriceOrder, rowDiff, goodsNo, goodsType, activityType, reason, priceTypeCode, adjustDetailMergeIds, oaUserId);
        }
    }

    private void rebateData(AdjustPriceOrder adjustPriceOrder,String adjustDetailMergeIds,Map<String, String> rowDiff){
        //是否线下pos、价格类型是否存在零售价和会员价
        List<String> adjustPriceTypeList = Arrays.asList(adjustPriceOrder.getAdjustPriceType().split(","));
        if(!adjustPriceOrder.getChannel().equals(Constants.DEFAULT_CHANNEL) ||
            !adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode()) ||
            !adjustPriceTypeList.contains(PriceTypeModeEnum.HYJ.getPriceTypeCode())){
            return;
        }
        //查询调价单明细
        List<Long> detailIdList = Arrays.asList(adjustDetailMergeIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
        example.createCriteria().andAdjustCodeEqualTo(adjustPriceOrder.getAdjustCode()).andIdIn(detailIdList);
        List<AdjustPriceOrderDetail> adjustPriceOrderDetails = adjustPriceOrderDetailMapper.selectByExample(example);
        Map<String, AdjustPriceOrderDetail> collectMap = adjustPriceOrderDetails.stream().collect(Collectors.toMap(AdjustPriceOrderDetail::getPriceTypeCode, Function.identity(), (v1, v2) -> v1));
        //表里存在的金额
        BigDecimal lsjPrice = collectMap.get(PriceTypeModeEnum.LSJ.getPriceTypeCode()).getPrice()==null?BigDecimal.ZERO:collectMap.get(PriceTypeModeEnum.LSJ.getPriceTypeCode()).getPrice();
        BigDecimal hyjPrice = collectMap.get(PriceTypeModeEnum.HYJ.getPriceTypeCode()).getPrice()==null?BigDecimal.ZERO:collectMap.get(PriceTypeModeEnum.HYJ.getPriceTypeCode()).getPrice();
        Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(collectMap.get(PriceTypeModeEnum.HYJ.getPriceTypeCode()).getExtend1());
        BigDecimal rebate = instance.get().getRebate()==null?BigDecimal.ZERO:instance.get().getRebate();
        //正要修改的金额
        String lsjKey = new StringBuilder().append(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_2.getName()).append("_").append(PriceTypeModeEnum.LSJ.getPriceTypeCode()).toString();
        BigDecimal lsjCurrent = rowDiff.get(lsjKey)==null?BigDecimal.ZERO:BigDecimalUtils.stringToDecimal(rowDiff.get(lsjKey));
        String hyjKey = new StringBuilder().append(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_2.getName()).append("_").append(PriceTypeModeEnum.HYJ.getPriceTypeCode()).toString();
        BigDecimal hyjCurrent = rowDiff.get(hyjKey)==null?BigDecimal.ZERO:BigDecimalUtils.stringToDecimal(rowDiff.get(hyjKey));
        String rebateKey = new StringBuilder().append(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_10.getName()).append("_").append(PriceTypeModeEnum.HYJ.getPriceTypeCode()).toString();
        BigDecimal rebateCurrent = rowDiff.get(rebateKey)==null?BigDecimal.ZERO:BigDecimalUtils.stringToDecimal(rowDiff.get(rebateKey));
        BigDecimal base = new BigDecimal(100);
        //找出修改的金额类型
        if(lsjPrice.compareTo(lsjCurrent.multiply(base))!=0 || hyjPrice.compareTo(hyjCurrent.multiply(base))!=0){
            if(lsjCurrent.compareTo(BigDecimal.ZERO)!=0){
                rebateCurrent = hyjCurrent.divide(lsjCurrent,2, RoundingMode.HALF_UP);
                rowDiff.put(rebateKey,String.valueOf(rebateCurrent));
            }
        }else if(rebate.compareTo(rebateCurrent)!=0 && lsjCurrent.compareTo(BigDecimal.ZERO)!=0){
            hyjCurrent = rebateCurrent.multiply(lsjCurrent).setScale(3, RoundingMode.HALF_UP);
            rowDiff.put(hyjKey,String.valueOf(hyjCurrent));
        }
    }

    @Override
    public void deleteAdjustPriceOrderDetail(Long adjustPriceOrderId, String goodsNo, String adjustDetailMergeIds, TokenUserDTO userDTO) {
    	if(StringUtils.isBlank(adjustDetailMergeIds)) {
    		throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
    	}
    	List<Long> detaildList = Arrays.asList(adjustDetailMergeIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        deleteAdjustPriceOrderDetails(adjustPriceOrderId, null, detaildList, userDTO, null);
    }

    @Override
    public void deleteAdjustPriceOrderDetails(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        deleteAdjustPriceOrderDetails(adjustPriceOrderId, null,null, userDTO, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAdjustPriceOrderDetails(Long adjustPriceOrderId, List<String> goodsNoList, List<Long> detaildList, TokenUserDTO userDTO, List<Long> skuIdList) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);
        adjustPriceOrderDetailExMapper.deleteByAdjustCode(adjustPriceOrder.getAdjustCode(), goodsNoList, null, null, detaildList, skuIdList);
        deleteAdjustPriceOrderDetailStoreIdCache(adjustPriceOrder.getAdjustCode(),detaildList);
    }

    @Override
    @NewSpan
    public void importAdjustPriceOrderDetails(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO) {
    	AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(importAdjustPriceOrder.getAdjustPriceOrderId());
    	try {

    		basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);

    		basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);

    		clearAdjustImportResultRedisKey(adjustPriceOrder.getAdjustCode());

            Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);

            checkGoodsNoIsExist(importAdjustPriceOrder,storeId);

            checkRowImportTag(importAdjustPriceOrder, userDTO);

    		if(GoodsScopeEnum.SENSITIVE_GOODS.getCode()==adjustPriceOrder.getGoodsScope()) {
    			checkSensitiveGoodsNo(importAdjustPriceOrder);
        	}

    		copyAdjustPriceOrderBaseConfigOrgId(importAdjustPriceOrder, adjustPriceOrder);

    		checkRowImportAdjustPriceOrderDetails(importAdjustPriceOrder, adjustPriceOrder, userDTO);

    		checkMoreRowEqualGoodsNoIntersection(importAdjustPriceOrder, adjustPriceOrder, userDTO);

	        if (adjustPriceOrder.getGoodsScope() != null && adjustPriceOrder.getGoodsScope() == GoodsScopeEnum.WHITE_GOODS_SCOPE.getCode()) {
	            getWhiteGoodsNoList(adjustPriceOrder.getAdjustCode(), importAdjustPriceOrder, userDTO);
	        }

	        deleteExistedOrderDetailRecord(importAdjustPriceOrder, userDTO, adjustPriceOrder);

	        batchAddAdjustPriceOrderDetailsForImport(importAdjustPriceOrder,userDTO);

	        sendImportDataResult(importAdjustPriceOrder, adjustPriceOrder);
		} catch (Exception e) {
			logger.error("AdjustPriceOrderDetailV2ServiceImpl|importAdjustPriceOrderDetails检查excel数据发生异常",e);
			sendAdjustPriceImportMsg(adjustPriceOrder.getAdjustCode(), 0, 0, 0, -1);
			throw new BusinessErrorException(ReturnCodeEnum.ERROR_CHECK_IMPORT);
		}

    }

    /**
     * 验证调整后商品类型和调整后活动名称是否在标签中存在
     * @param importAdjustPriceOrder
     * @param userDTO
     */
    private void checkRowImportTag(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO) {
        List<TagDTO> tagList = getTagList(userDTO);
        //调整后商品类型
        List<OptionDto> goodsTypeOptions = getTagOptions(TagTypeEnums.GOODS.getType(),tagList);
        Set<String> goodsTypeOptionsSet = goodsTypeOptions.stream().map(OptionDto::getValue).filter(Objects::nonNull).collect(Collectors.toSet());
        //调整后活动名称
        List<OptionDto> activityTypeOptions = getTagOptions(TagTypeEnums.STORE_GROUP.getType(),tagList);
        Set<String> activityTypeOptionsSet = activityTypeOptions.stream().map(OptionDto::getValue).filter(Objects::nonNull).collect(Collectors.toSet());
        List<ImportAdjustPriceOrderDetailDTO> importList = importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList();
        // 处理导入的每一行数据
        importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList().forEach(detail -> {
            validateTagOption(detail, detail.getGoodsType(), goodsTypeOptionsSet, "调整后商品类型在标签中不存在");
            validateTagOption(detail, detail.getActivityType(), activityTypeOptionsSet, "调整后活动类型在标签中不存在");
        });
    }

    /**
     * 验证标签选项是否存在
     */
    private void validateTagOption(ImportAdjustPriceOrderDetailDTO detail, String fieldValue,
                                   Set<String> validOptions, String errorMessage) {
        if (StringUtils.isNotBlank(fieldValue) && !validOptions.contains(fieldValue)) {
            detail.setResult(Boolean.FALSE);

            String fullErrorMessage = fieldValue + " " + errorMessage;
            if (StringUtils.isNotBlank(detail.getMessage())) {
                detail.setMessage(detail.getMessage() + "," + fullErrorMessage);
            } else {
                detail.setMessage(fullErrorMessage);
            }
        }
    }

    private void checkSensitiveGoodsNo(AdjustPriceOrderDetailImportV2Param importData) {
    	List<ImportAdjustPriceOrderDetailDTO> importList = importData.getAdjustPriceOrderDetailDTOList();
    	if(CollectionUtils.isEmpty(importList)) {
    		return;
    	}
    	List<String> goodsNoList = importData.getAdjustPriceOrderDetailDTOList().stream().map(ImportAdjustPriceOrderDetailDTO::getGoodsNo).distinct().collect(Collectors.toList());
    	//敏感商品和门店信息
    	List<String> sensitiveGoodsList = priceMdmDataService.getEntityListByTypeAndEntityIdList(CommonEnums.MdmDataBizEntryTypeEnum.MDM_BIZ_SENSITIVE_GOODS_LIST.getCode(), goodsNoList);
    	for (ImportAdjustPriceOrderDetailDTO item : importList) {
    		if(!Boolean.TRUE.equals(item.getResult())){
        		continue;
        	}
    		if(!sensitiveGoodsList.contains(item.getGoodsNo())) {
    			item.setResult(Boolean.FALSE);
    			item.setMessage("商品编码不存在敏感商品目录中");
    		}
		}
    }

    private void checkSensitiveGoodsOrgInfo(ImportAdjustPriceOrderDetailDTO item, AdjustPriceOrder adjustPriceOrder, TokenUserDTO userDTO, Long receiveTimeStamp) {
        if (Boolean.FALSE.equals(item.getResult())) {
            return;
        }
        // 获取敏感商品和门店信息
        Map<String, List<String>> sensitiveGoodsMap = priceMdmDataService.getEntityMapByTypeAndEntityIdList(CommonEnums.MdmDataBizEntryTypeEnum.MDM_BIZ_STORE_GROUP_SENSITIVE_GOODS_REF.getCode(), Lists.newArrayList(item.getGoodsNo()));
        if (sensitiveGoodsMap == null) {
            item.setResult(Boolean.FALSE);
            item.setMessage("商品编码不存在敏感商品目录中");
            return;
        }
        if (null != item.getIsCopyAdjustOrderOrg() && item.getIsCopyAdjustOrderOrg()) {
            // 复制主单机构 不需要权限验证 需要把对应的门店放缓存进行交集验证
            checkCopyAdjustOrderOrgIdAuth(item, adjustPriceOrder, receiveTimeStamp);
            processSensitiveGoodsOrgInfo(item, adjustPriceOrder, sensitiveGoodsMap, receiveTimeStamp, userDTO);
        } else if (null != item.getGoodsOrgTabType() && item.getGoodsOrgTabType() == PriceOrderTabTypeEnum.STORE_LIST.getType()) {
            processStoreListOrgInfo(item, adjustPriceOrder, sensitiveGoodsMap, receiveTimeStamp, userDTO);
        } else if (null != item.getGoodsOrgTabType() &&
                (item.getGoodsOrgTabType() == PriceOrderTabTypeEnum.STORE_GROUP.getType() ||
                        item.getGoodsOrgTabType() == PriceOrderTabTypeEnum.STORE_FLAG.getType())) {
            processStoreGroupTagOrgInfo(item, adjustPriceOrder, sensitiveGoodsMap, receiveTimeStamp, userDTO);
        }
    }

    private void processSensitiveGoodsOrgInfo(ImportAdjustPriceOrderDetailDTO item, AdjustPriceOrder adjustPriceOrder, Map<String, List<String>> sensitiveGoodsMap, Long receiveTimeStamp, TokenUserDTO userDTO) {
        String uniqueId = adjustPriceOrder.getAdjustCode() + "_" + item.getGoodsNo() + "_" + item.getLineNo() + "_" + receiveTimeStamp;
        String detailKey = getAdjustItemStoreIdRedisKey(uniqueId);
        RBucket<List<Long>> storeIdListBucket = redissonClient.getBucket(detailKey);
        List<Long> currAdjustStoreIdList = storeIdListBucket.get();
        if (currAdjustStoreIdList == null || currAdjustStoreIdList.isEmpty()) {
            item.setResult(Boolean.FALSE);
            item.setMessage("机构都不在敏感商品映射的门店");
            return;
        }

        List<String> currAdjustSapCodeList = currAdjustStoreIdList.stream()
            .map(id -> {
                OrgToRedisDTO store = CacheVar.storeCacheMap.get(id);
                return store != null ? store.getSapCode() : null;
            })
            .filter(sapCode -> sapCode != null && !sapCode.isEmpty())
            .collect(Collectors.toList());

        List<String> goodsMapperSapCodeList = sensitiveGoodsMap.get(item.getGoodsNo());
        List<String> commonSapCodes = currAdjustSapCodeList.stream()
                .filter(goodsMapperSapCodeList::contains)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(commonSapCodes)) {
            item.setResult(Boolean.FALSE);
            item.setMessage("机构都不在敏感商品映射的门店");
            return;
        }

        List<String> noCommonSapCodes = currAdjustSapCodeList.stream()
                .filter(sapCode -> !goodsMapperSapCodeList.contains(sapCode))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noCommonSapCodes)) {
            item.setMessage(noCommonSapCodes + "不在敏感商品映射的门店");
        }

        if (CollectionUtils.isNotEmpty(noCommonSapCodes)) {
            Set<Long> orgIdSet = commonSapCodes.stream()
                    .filter(CacheVar.storeSapCodeCacheMap::containsKey)
                    .map(CacheVar.storeSapCodeCacheMap::get)
                    .map(OrgToRedisDTO::getId)
                    .collect(Collectors.toSet());

            List<Long> storeIdList = commonSapCodes.stream()
                    .filter(CacheVar.storeSapCodeCacheMap::containsKey)
                    .map(CacheVar.storeSapCodeCacheMap::get)
                    .map(OrgToRedisDTO::getOutId)
                    .collect(Collectors.toList());

            stagingRedisadjustItemScopeStoreId(item, adjustPriceOrder, storeIdList, receiveTimeStamp);
            fillOrderDetailOrgData(item, orgIdSet, userDTO);
        }
    }

    private void processStoreListOrgInfo(ImportAdjustPriceOrderDetailDTO item, AdjustPriceOrder adjustPriceOrder, Map<String, List<String>> sensitiveGoodsMap, Long receiveTimeStamp, TokenUserDTO userDTO) {
        List<String> sapCodeList = Arrays.asList(item.getOrgIdsParam().split(",")).stream()
                .map(String::trim)
                .collect(Collectors.toList());

        Map<Long, OrgToRedisDTO> businessOrgIdMap = new HashMap<>();
        Map<Long, OrgToRedisDTO> storeOrgIdMap = new HashMap<>();
        List<String> errorSapCodeList = Lists.newArrayList();
        Set<Long> storeIdIdSet = new HashSet<>();

        for (String sapCode : sapCodeList) {
            OrgToRedisDTO businessDto = CacheVar.businessSapCodeCacheMap.get(sapCode);
            if (businessDto != null) {
                businessOrgIdMap.put(businessDto.getId(), businessDto);
            }
            OrgToRedisDTO storeDto = CacheVar.storeSapCodeCacheMap.get(sapCode);
            if (storeDto != null) {
                storeOrgIdMap.put(storeDto.getId(), storeDto);
                storeIdIdSet.add(storeDto.getOutId());
            }
            if (businessDto == null && storeDto == null) {
                errorSapCodeList.add(sapCode);
            }
        }

        Set<Long> orgIdSet = new HashSet<>();
        if (MapUtils.isNotEmpty(businessOrgIdMap)) {
            orgIdSet.addAll(businessOrgIdMap.keySet());
        }
        if (MapUtils.isNotEmpty(storeOrgIdMap)) {
            orgIdSet.addAll(storeOrgIdMap.keySet());
        }

        if (orgIdSet.isEmpty()) {
            logger.warn("checkRowImportAdjustPriceOrderDetails|checkRowImport|根据sapCode:{},分别从连锁和门店获取不到数据,请检查是否存在", sapCodeList);
            item.setResult(Boolean.FALSE);
            item.setMessage("请填写正确的机构编码");
            return;
        }

        if (MapUtils.isNotEmpty(businessOrgIdMap)) {
            List<Long> orgIdList = new ArrayList<>(businessOrgIdMap.keySet());
            List<OrgDTO> orgDtoList = permissionExtService.getUserDataScopeStoreBatch(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), orgIdList, 1);
            List<Long> authStoreIdList = orgDtoList.stream()
                    .map(OrgDTO::getOutId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(authStoreIdList)) {
                storeIdIdSet.addAll(authStoreIdList);
            }
        }

        List<Long> currAdjustStoreIdList = new ArrayList<>(storeIdIdSet);
        List<String> currAdjustSapCodeList = currAdjustStoreIdList.stream()
            .map(id -> {
                OrgToRedisDTO store = CacheVar.storeCacheMap.get(id);
                return store != null ? store.getSapCode() : null;
            })
            .filter(sapCode -> sapCode != null && !sapCode.isEmpty())
            .collect(Collectors.toList());

        List<String> goodsMapperSapCodeList = sensitiveGoodsMap.get(item.getGoodsNo());
        List<String> commonSapCodes = currAdjustSapCodeList.stream()
                .filter(goodsMapperSapCodeList::contains)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(commonSapCodes)) {
            item.setResult(Boolean.FALSE);
            item.setMessage("机构都不在敏感商品映射的门店");
            return;
        }

        List<String> noCommonSapCodes = currAdjustSapCodeList.stream()
                .filter(sapCode -> !goodsMapperSapCodeList.contains(sapCode))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(errorSapCodeList) || CollectionUtils.isNotEmpty(noCommonSapCodes)) {
            StringBuffer sb = new StringBuffer();
            if (CollectionUtils.isNotEmpty(errorSapCodeList)) {
                sb.append("下列机构编码不正确：").append(String.join(",", errorSapCodeList));
            }
            if (CollectionUtils.isNotEmpty(noCommonSapCodes)) {
                sb.append("下列机构编码不在敏感商品映射的门店：").append(String.join(",", noCommonSapCodes));
            }
            item.setMessage(sb.toString());
        }

        List<Long> storeIdList = new ArrayList<>();
        Set<Long> useOrgIdSet = new HashSet<>();
        if (CollectionUtils.isEmpty(errorSapCodeList) && CollectionUtils.isEmpty(noCommonSapCodes)) {
            useOrgIdSet = orgIdSet;
            storeIdList = currAdjustStoreIdList;
        } else {
            useOrgIdSet = commonSapCodes.stream()
                    .filter(CacheVar.storeSapCodeCacheMap::containsKey)
                    .map(CacheVar.storeSapCodeCacheMap::get)
                    .map(OrgToRedisDTO::getId)
                    .collect(Collectors.toSet());
            storeIdList = commonSapCodes.stream()
                    .filter(CacheVar.storeSapCodeCacheMap::containsKey)
                    .map(CacheVar.storeSapCodeCacheMap::get)
                    .map(OrgToRedisDTO::getOutId)
                    .collect(Collectors.toList());
        }

        stagingRedisadjustItemScopeStoreId(item, adjustPriceOrder, storeIdList, receiveTimeStamp);
        fillOrderDetailOrgData(item, useOrgIdSet, userDTO);
    }

    private void processStoreGroupTagOrgInfo(ImportAdjustPriceOrderDetailDTO item, AdjustPriceOrder adjustPriceOrder, Map<String, List<String>> sensitiveGoodsMap, Long receiveTimeStamp, TokenUserDTO userDTO) {
        checkStoreGroupTagOrgIdAuth(item, adjustPriceOrder, userDTO, receiveTimeStamp);
        List<Long> errorTagCodeList = Lists.newArrayList();
        List<Long> normalTagCodeList = Lists.newArrayList();
        List<Long> tabTypeValList = Arrays.asList(item.getTabTypeVals().split(",")).stream()
                .map(s -> Long.parseLong(s.trim()))
                .collect(Collectors.toList());

        for (Long tagId : tabTypeValList) {
            String tagIdRedisKey = getStoreGroupTagRedisKey(tagId);
            if (redissonClient.getBucket(tagIdRedisKey).isExists()) {
                normalTagCodeList.add(tagId);
            } else {
                errorTagCodeList.add(tagId);
            }
        }

        if (CollectionUtils.isEmpty(normalTagCodeList)) {
            item.setResult(Boolean.FALSE);
            item.setMessage("填写的门店组id、标签id在tag系统中不存在");
            return;
        }

        List<Long> storeOrgIdList = Lists.newArrayList();
        for (Long tagId : normalTagCodeList) {
            String tagIdScopeStoreOrgIdKey = getTagIdScopeStoreOrgIdRedisKey(tagId);
            RBucket<List<Long>> bucket = redissonClient.getBucket(tagIdScopeStoreOrgIdKey);
            storeOrgIdList.addAll(bucket.get());
        }

        List<Long> currAdjustStoreIdList = storeOrgIdList.stream()
                .map(CacheVar.storeOrgIdAndOutIdMapping::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<String> currAdjustSapCodeList = currAdjustStoreIdList.stream()
            .map(id -> {
                OrgToRedisDTO store = CacheVar.storeCacheMap.get(id);
                return store != null ? store.getSapCode() : null;
            })
            .filter(sapCode -> sapCode != null && !sapCode.isEmpty())
            .collect(Collectors.toList());

        List<String> goodsMapperSapCodeList = sensitiveGoodsMap.get(item.getGoodsNo());
        List<String> commonSapCodes = currAdjustSapCodeList.stream()
                .filter(goodsMapperSapCodeList::contains)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(commonSapCodes)) {
            item.setResult(Boolean.FALSE);
            item.setMessage("机构都不在敏感商品映射的门店");
            return;
        }

        List<String> noCommonSapCodes = currAdjustSapCodeList.stream()
                .filter(sapCode -> !goodsMapperSapCodeList.contains(sapCode))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(noCommonSapCodes)) {
            StringBuffer sb = new StringBuffer();
            if (CollectionUtils.isNotEmpty(noCommonSapCodes)) {
                sb.append("下列机构编码不在敏感商品映射的门店：").append(String.join(",", noCommonSapCodes));
            }
            item.setMessage(sb.toString());
        }

        Set<Long> useOrgIdSet = commonSapCodes.stream()
                .filter(CacheVar.storeSapCodeCacheMap::containsKey)
                .map(CacheVar.storeSapCodeCacheMap::get)
                .map(OrgToRedisDTO::getId)
                .collect(Collectors.toSet());

        List<Long> storeIdList = commonSapCodes.stream()
                .filter(CacheVar.storeSapCodeCacheMap::containsKey)
                .map(CacheVar.storeSapCodeCacheMap::get)
                .map(OrgToRedisDTO::getOutId)
                .collect(Collectors.toList());

        stagingRedisadjustItemScopeStoreId(item, adjustPriceOrder, storeIdList, receiveTimeStamp);
        fillOrderDetailOrgData(item, useOrgIdSet, userDTO);
    }

    /**
     *
     * @Title: checkGoodsNoIsExist
     * @Description: 验证商品编码是存在
     * @param: @param param
     * @return: void
     * @throws
     */
    private void checkGoodsNoIsExist(AdjustPriceOrderDetailImportV2Param param,Long storeId) {
        List<ImportAdjustPriceOrderDetailDTO> importList = param.getAdjustPriceOrderDetailDTOList();
        if (CollectionUtils.isEmpty(importList)) {
            return;
        }
        // 收集所有需要检查的商品编码
        Set<String> allGoodsNoSet = importList.stream().map(ImportAdjustPriceOrderDetailDTO::getGoodsNo).filter(Objects::nonNull).collect(Collectors.toSet());
        if (allGoodsNoSet.isEmpty()) {
            return;
        }
        // 批量检查Redis缓存
        String[] redisKeys = allGoodsNoSet.stream().map(this::buildGoodsNoRedisKey).toArray(String[]::new);

        RBuckets buckets = redissonClient.getBuckets();
        Map<String, String> redisResult = buckets.get(redisKeys);
        final Map<String, String> cachedGoodsMap = redisResult != null ? redisResult : Collections.emptyMap();

        // 找出未缓存的商品编码
        Set<String> uncachedGoodsNoSet = allGoodsNoSet.stream().filter(goodsNo -> !cachedGoodsMap.containsKey(buildGoodsNoRedisKey(goodsNo))).collect(Collectors.toSet());
        // 分批查询数据库获取未缓存的商品信息（每批最多50个）
        Map<String, SpuListVO> spuVOMap = new HashMap<>();
        Map<String, String> cacheData = new HashMap<>();

        if (!uncachedGoodsNoSet.isEmpty()) {
            List<String> uncachedGoodsNoList = new ArrayList<>(uncachedGoodsNoSet);
            List<List<String>> partitions = Lists.partition(uncachedGoodsNoList, 50);
            for (List<String> partition : partitions) {
                Map<String, SpuListVO> batchSpuVOMap = searchExtService.getSpuVOMap(partition);
                if (MapUtils.isNotEmpty(batchSpuVOMap)) {
                    spuVOMap.putAll(batchSpuVOMap);
                    // 准备当前批次的缓存数据
                    for (String goodsNo : batchSpuVOMap.keySet()) {
                        String redisKey = buildGoodsNoRedisKey(goodsNo);
                        cacheData.put(redisKey, goodsNo);
                    }
                }
            }
            // 批量更新Redis缓存
            if (!cacheData.isEmpty()) {
                buckets.set(cacheData); // 先设置值
                //批量设置过期时间
                RBatch batch = redissonClient.createBatch();
                cacheData.keySet().forEach(key -> {
                    batch.getBucket(key).expireAsync(7, TimeUnit.DAYS);
                });
                batch.execute();
            }
        }
        // 合并缓存和数据库查询结果，构建存在的商品编码集合
        Set<String> existingGoodsNoSet = new HashSet<>();
        // 从缓存结果中提取商品编码
        for (String value : cachedGoodsMap.values()) {
            existingGoodsNoSet.add(value);
        }
        // 添加数据库查询到的商品编码
        existingGoodsNoSet.addAll(spuVOMap.keySet());

        //单体加盟 没查到的商品 去查单体加盟商品
        if (storeId != null) {
            Set<String> uniqueGoodsNoSet = new HashSet<>();
            for (ImportAdjustPriceOrderDetailDTO detail : importList) {
                String goodsNo = detail.getGoodsNo(); // 假设DTO中有getGoodsNo()方法
                if (goodsNo != null && !existingGoodsNoSet.contains(goodsNo)) {
                    uniqueGoodsNoSet.add(goodsNo);
                }
            }
            existingGoodsNoSet.addAll(checkJoinStoreGoodsNoIsExist(storeId,uniqueGoodsNoSet));
        }
        // 批量更新导入数据的结果
        for (ImportAdjustPriceOrderDetailDTO detailDTO : importList) {
            String goodsNo = detailDTO.getGoodsNo();
            if (goodsNo == null || !existingGoodsNoSet.contains(goodsNo)) {
                detailDTO.setResult(Boolean.FALSE);
                detailDTO.setMessage("商品编码不存在");
            }
        }
    }

    private String buildGoodsNoRedisKey(String goodsNo) {
        return RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.GOODS_NO_KEY + goodsNo;
    }

    private List<String> checkJoinStoreGoodsNoIsExist(Long storeId,Set<String> uniqueGoodsNoSet) {
        if (uniqueGoodsNoSet.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 转为List并分批处理（防止SQL参数过多）
        List<String> uniqueGoodsNoList = new ArrayList<>(uniqueGoodsNoSet);
        List<String> existingGoodsNos = new ArrayList<>();

        // 分批查询（每批次300条，避免触发数据库IN参数限制）
        List<List<String>> batches = Lists.partition(uniqueGoodsNoList, Constants.THREE_HUNDRED);

        // 2. 串行分批查询
        for (List<String> batch : batches) {
            Map<String, JoinStoreGoodsInfoDTO> batchResult = getJoinStoreGoodsMap(storeId, batch);
            existingGoodsNos.addAll(batchResult.keySet());
        }

        return existingGoodsNos;
    }

    private String getAdjustPriceOrderDetailDownloadExceptionFileName(String adjustCode) {
        return "调价单导入异常_" + adjustCode;
    }
    /**
     *
     * @Title: clearAdjustImportResultRedisKey
     * @Description: 清理导入结果
     * @param: @param adjustCode
     * @return: void
     * @throws
     */
    private void clearAdjustImportResultRedisKey(String adjustCode) {
    	String adjustImportResultRedisKey = getAdjustImportResultRedisKey(adjustCode);
    	RBucket<AdjustImportResultDTO> bucket = redissonClient.getBucket(adjustImportResultRedisKey);
    	if(bucket.isExists()) {
    		bucket.delete();
    	}
    }
    /**
     *
     * @Title: sendImportDataResult
     * @Description: 发送导入结果
     * @param: @param importAdjustPriceOrder
     * @param: @param adjustPriceOrder
     * @return: void
     * @throws
     */
    private void sendImportDataResult(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder,AdjustPriceOrder adjustPriceOrder) {
    	List<ImportAdjustPriceOrderDetailDTO> importList = importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList();
        List<ImportAdjustPriceOrderDetailDTO> exceptionData = importList.stream().filter(v -> Boolean.FALSE.equals(v.getResult()) || StringUtils.isNotBlank(v.getMessage())).collect(Collectors.toList());
    	if(CollectionUtils.isNotEmpty(exceptionData)) {
    		exceptionData.forEach(item -> {
    			item.setOrgIds(item.getOrgIdsParam());
    			if(null!=item.getPriceCLJ()) {
    				item.setPriceCLJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPriceCLJ())));
    			}
				if(null!=item.getPriceHYJ()) {
					item.setPriceHYJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPriceHYJ())));
				}
				if(null!=item.getPriceLSJ()) {
					item.setPriceLSJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPriceLSJ())));
				}
				if(null!=item.getPriceCHYJ()) {
					item.setPriceCHYJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPriceCHYJ())));
				}
				if(null!=item.getPricePDDDMJ()) {
					item.setPricePDDDMJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPricePDDDMJ().longValue())));
				}
				if(null!=item.getPricePDDPTJ()){
					item.setPricePDDPTJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPricePDDPTJ().longValue())));
				}
				if(null!=item.getPriceMTLSJ()) {
					item.setPriceMTLSJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPriceMTLSJ().longValue())));
				}
				if(null!=item.getPriceDYLSJ()) {
					item.setPriceDYLSJ(BigDecimalUtils.stringToDecimal(BigDecimalUtils.convertYuanByFen(item.getPriceDYLSJ().longValue())));
				}
    		});

    		List<Map<String, Object>> adjustPriceOrderDetailListResults = EntityConvertMap(exceptionData);
    		//组装异常数据
            basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
            String fileName = getAdjustPriceOrderDetailDownloadExceptionFileName(adjustPriceOrder.getAdjustCode());
            boolean isPortal = importAdjustPriceOrder.getIsPortal();
            LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
            Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
            if(null!=storeId){
                fieldMap = getSingleJoinAdjustPriceOrderDetailDownloadFieldMap(adjustPriceOrder);
            }else{
                fieldMap = getAdjustPriceOrderDetailDownloadFieldMap(adjustPriceOrder,false,null,null,isPortal);
            }
            fieldMap.put("message", "错误原因");
			List<Map<String, Object>> tempList = Lists.newArrayList();
			ExportFileCubeVO instance = ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_ADJUST_ORDER_ERROR_DETAIL.getUploadUrl(),
               fieldMap, tempList);
			//结果
			String key = getAdjustImportExceptionResult(adjustPriceOrder.getAdjustCode());
			RList<Map<String, Object>> resultList = redissonClient.getList(key);
			resultList.clear();
			resultList.addAll(adjustPriceOrderDetailListResults);
			resultList.expire(TIMETOLIVEHOURS,TimeUnit.HOURS);
			//结构
			String structureKey = getAdjustImportExceptionResultStructureRediskey(adjustPriceOrder.getAdjustCode());
			redissonClient.getBucket(structureKey).set(instance,TIMETOLIVEHOURS,TimeUnit.HOURS);
    	}
    	int total = importList.size();
    	int failCount = exceptionData.size();
    	int successCount = (int)importList.stream().filter(dto -> Boolean.TRUE.equals(dto.getResult())).count();;
    	sendAdjustPriceImportMsg(adjustPriceOrder.getAdjustCode(), total, successCount, failCount, 1);
    }

    private void sendAdjustPriceImportMsg(String adjustCode, int total,int successCount,int failCount,int status) {
    	AdjustImportResultDTO importResult = new AdjustImportResultDTO();
    	importResult.setTotal(total);
    	importResult.setSuccessCount(successCount);
    	importResult.setFailCount(failCount);
    	importResult.setStatus(status);
    	String adjustImportResultRedisKey = getAdjustImportResultRedisKey(adjustCode);
    	redissonClient.getBucket(adjustImportResultRedisKey).set(importResult,3,TimeUnit.HOURS);
    }

    //list实体类转换成map
    private List<Map<String,Object>> EntityConvertMap(List<ImportAdjustPriceOrderDetailDTO> list){
       List<Map<String,Object>> l = new LinkedList<>();
       try {
          for(ImportAdjustPriceOrderDetailDTO t : list){
             Map<String,Object> map = new HashMap<>();
             Method[] methods = t.getClass().getMethods();
             for (Method method : methods) {
                if (method.getName().startsWith("get")) {
                   String name = method.getName().substring(3);
                   name = name.substring(0, 1).toLowerCase() + name.substring(1);
                   Object value = method.invoke(t);
                   map.put(replaceFiled(name),replaceFiledValue(name,value));
                }
             }
             l.add(map);
          }
       } catch (Exception e) {
			logger.info("sendImportDataResult|EntityConvertMap转换异常",e);
       }
       return l;
    }
    private String replaceFiled(String name) {
    	if(null==name) {
    		return "";
    	}
    	return name.replace("priceLSJ", "price_LSJ").
    			replace("priceHYJ", "price_HYJ").
    			replace("priceCLJ", "price_CLJ").
    			replace("priceCHYJ", "price_CHYJ").
    			replace("pricePDDDMJ", "price_PDDDMJ").
    			replace("pricePDDPTJ", "price_PDDPTJ").
    			replace("priceMTLSJ", "price_MTLSJ").
    			replace("priceDYLSJ", "price_DYLSJ");
    }
    private Object replaceFiledValue(String name,Object value) {
    	if(null==name || null==value) {
    		return "";
    	}
    	if(name.equals("overrideLowerLevel") || name.equals("priceFlag")) {
    		if(Integer.valueOf(value.toString())==0) {
    			return "否";
    		}else {
    			return "是";
    		}
    	}
    	return value;
    }
    /**
     * 新导入数据覆盖 已存在历史记录
     * @param importAdjustPriceOrder
     * @param userDTO
     * @param adjustPriceOrder
     */
    private void deleteExistedOrderDetailRecord(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO, AdjustPriceOrder adjustPriceOrder) {
        List<String> hasAddGoodsNoList = getGoodsNoListbyAdjustCode(adjustPriceOrder.getAdjustCode());
        List<String> removeGoodsNoList = importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList().stream()
            .filter(v->!Boolean.FALSE.equals(v.getResult())&& hasAddGoodsNoList.contains(v.getGoodsNo()))
            .map(ImportAdjustPriceOrderDetailDTO::getGoodsNo).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(removeGoodsNoList)){
            deleteAdjustPriceOrderDetails(importAdjustPriceOrder.getAdjustPriceOrderId(),removeGoodsNoList,null,userDTO,null);
        }
    }

    /**
     * 新导入数据覆盖 已存在历史记录
     * @param importAdjustPriceOrder
     * @param userDTO
     * @param adjustPriceOrder
     */
    private void deleteExistedOrderB2cDetailRecord(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO, AdjustPriceOrder adjustPriceOrder) {
        List<String> hasAddSkuIdList = getSkuIdListbyAdjustCode(adjustPriceOrder.getAdjustCode());
        List<Long> removeSkuIdList = importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList().stream()
            .filter(v->!Boolean.FALSE.equals(v.getResult())&& hasAddSkuIdList.contains(v.getSkuId()))
            .map(v -> Long.parseLong(v.getSkuId())).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(removeSkuIdList)){
            deleteAdjustPriceOrderDetails(importAdjustPriceOrder.getAdjustPriceOrderId(),null,null,userDTO,removeSkuIdList);
        }
    }

    /**
     * 当调价单的商品范围是白名单，过滤提交商品编码
     * @param adjustCode
     * @param param
     * @param userDTO
     * @return
     */
    private void getWhiteGoodsNoList(String adjustCode, AdjustPriceOrderDetailImportV2Param param, TokenUserDTO userDTO) {
        List<OrgLevelVO> orgLevelVOList = adjustPriceOrderOrgDetailService.listOrgLevelVOListByAdjustCode(adjustCode);
        if (CollectionUtils.isNotEmpty(orgLevelVOList)) {
            List<OrgLevelVO> businessList = permissionExtService.listBusinessInfoByOrg(orgLevelVOList, userDTO.getUserId());
            if (CollectionUtils.isNotEmpty(businessList)) {
                logger.info("<===[AdjustPriceOrderDetailV2ServiceImpl.getWhiteGoodsNoList] 去获取白名单的企业信息：{}",
                    businessList.subList(0, businessList.size() <= 5 ? businessList.size() : 5));
                SearchWhiteListParam searchWhiteListParam = new SearchWhiteListParam();
                List<String> goodsNoList = param.getAdjustPriceOrderDetailDTOList().stream().
                    map(ImportAdjustPriceOrderDetailDTO::getGoodsNo)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                Set<String> whiteListSet = Sets.newConcurrentHashSet();
                List<List<String>> partition = Lists.partition(goodsNoList, whiteListQueryPageMax);
         	    for (List<String> subGoodsNoList : partition) {
         	    	searchWhiteListParam.setGoodsNos(subGoodsNoList);
                    searchWhiteListParam.setBusinessIdList(businessList.stream().map(OrgLevelVO::getOutId).collect(Collectors.toSet()));
                    searchWhiteListParam.setPage(1);
                    searchWhiteListParam.setSize(subGoodsNoList.size());
                    PageResult<WhiteListDTO> pageResult = bizGoodsWhiteListService.getWhiteList(searchWhiteListParam, userDTO);
                    if(null!=pageResult && CollectionUtils.isNotEmpty(pageResult.getRows())) {
                    	whiteListSet.addAll(pageResult.getRows().stream().map(WhiteListDTO::getGoodsNo).collect(Collectors.toSet()));
                    }
         	    }

         	   if (CollectionUtils.isNotEmpty(whiteListSet)) {
                   param.getAdjustPriceOrderDetailDTOList().stream().filter(v->!Boolean.FALSE.equals(v.getResult())).forEach(v->{
                       if(!whiteListSet.contains(v.getGoodsNo())){
                           v.setResult(Boolean.FALSE);
                           v.setMessage("商品不在白名单");
                       }
                   });
               }else {
                   param.getAdjustPriceOrderDetailDTOList().stream().filter(v->!Boolean.FALSE.equals(v.getResult())).forEach(v->{
                           v.setResult(Boolean.FALSE);
                           v.setMessage("商品不在白名单");
                   });
               }

                List<String> notInWhiteGoodsnoList = param.getAdjustPriceOrderDetailDTOList().stream().filter(detailDTO -> !detailDTO.getResult())
                    .map(ImportAdjustPriceOrderDetailDTO::getGoodsNo).collect(
                        Collectors.toList());
                if (CollectionUtils.isNotEmpty(notInWhiteGoodsnoList)) {
                    logger.error("<===[AdjustPriceOrderDetailV2ServiceImpl.getWhiteGoodsNoList] 不在白名单的商品编码有：{}",
                        StringUtils.join(notInWhiteGoodsnoList, ","));
                } else {
                    logger.info("<===[AdjustPriceOrderDetailV2ServiceImpl.getWhiteGoodsNoList] 所有商品编码都在白名单里面");
                }

            }
        }
    }

    /**
     * 获取商品集团属性
     * @param goodsNo
     * @param propertyList
     * @return
     */
    private Map<String, Map<String, Object>> getGroupProperty(String goodsNo,List<String> propertyList){
    	try {
			List<PropertyParamDto> propertyParamDtoList = new ArrayList<PropertyParamDto>();
			PropertyParamDto propertyDto = new PropertyParamDto();
			propertyDto.setGoodsNo(goodsNo);
			List<PropertyParamRankDto> paramRankDtos=new ArrayList<PropertyParamRankDto>();
			for (String property : propertyList) {
				PropertyParamRankDto rankDto = new PropertyParamRankDto();
				rankDto.setProperty(property);
				rankDto.setRank(PropertyRankEnum.GROUP.getCode());
				paramRankDtos.add(rankDto);
			}
			propertyDto.setParamRankDtos(paramRankDtos);
			propertyParamDtoList.add(propertyDto);
			ResponseEntity<Map<String, Map<String, Object>>> responseEntity = feignForestService.getProperty(propertyParamDtoList);
			if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
				logger.error("AdjustPriceOrderDetailV2ServiceImpl|getGroupProperty|调用forest系统获取商品属性失败");
			}else {
				return responseEntity.getBody();
			}
		} catch (Exception e) {
			logger.error("AdjustPriceOrderDetailV2ServiceImpl|getGroupProperty|调用forest系统获取商品属性失败，失败原因:",e);
		}
    	return null;
    }

    // 辅助方法：将JoinStoreGoodsInfoDTO转换为SpuListVO
    private SpuListVO convertJoinStoreGoodsToSpuListVO(JoinStoreGoodsInfoDTO joinStoreGoodsInfoDTO) {
        if (joinStoreGoodsInfoDTO == null) {
            return null;
        }
        SpuListVO spuListVO = new SpuListVO();
        spuListVO.setGoodsNo(joinStoreGoodsInfoDTO.getGoodsNo());
        spuListVO.setCurName(joinStoreGoodsInfoDTO.getCommonName());
        spuListVO.setName(joinStoreGoodsInfoDTO.getGoodsName());
        spuListVO.setDosageForms(joinStoreGoodsInfoDTO.getDosage());
        spuListVO.setJhiSpecification(joinStoreGoodsInfoDTO.getSpecification());
        spuListVO.setGoodsunit(joinStoreGoodsInfoDTO.getUnit());
        spuListVO.setFactoryid(joinStoreGoodsInfoDTO.getManufacturer());
        if(null!=joinStoreGoodsInfoDTO.getDtpGoods()){
            if(YNEnum.YES.getType().equals(joinStoreGoodsInfoDTO.getDtpGoods())){
                spuListVO.setDTPgood(YNEnum.YES.getDesc());
            }
            if(YNEnum.NO.getType().equals(joinStoreGoodsInfoDTO.getDtpGoods())){
                spuListVO.setDTPgood(YNEnum.NO.getDesc());
            }
        }

        return spuListVO;
    }

    @Override
    public void supplementAdjustPriceOrderDetailData(AdjustPriceOrderDetailSupplementDataVO supplementDataVO) {
        logger.info("<===[AdjustPriceOrderDetailV2ServiceImpl.supplementAdjustPriceOrderDetailData] supplementDataVO:{}", supplementDataVO);
        Date now = new Date();
        String adjustCode = supplementDataVO.getAdjustCode();
		String goodsNo = supplementDataVO.getGoodsNo();
		String orgIds = supplementDataVO.getOrgIds();
        String goodsName = null;
        String categoryId = null;
        String covidType = null;
        String dtpGood = "";
		String goodsNoOrgIds = goodsNo+"_"+orgIds;
        String priceTypeCode = supplementDataVO.getPriceTypeCode();
        List<Long> idList = Arrays.asList(supplementDataVO.getAdjustDetailMergeIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        AdjustPriceOrder adjustPriceOrder = basePriceOrderService.getAdjustPriceOrderByAdjustCode(adjustCode);
        if (adjustPriceOrder == null) {
            logger.error("<===[AdjustPriceOrderDetailV2ServiceImpl.supplementAdjustPriceOrderDetailData] adjustCode: {} 不存在 ", adjustCode);
            return;
        }
        Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null!=storeId){
            supplementDataVO.setDataFull(true);
        }
        Long userId = adjustPriceOrder.getCreatedBy();
        Map<String, AdjustBaseDetailDTO> baseDetailDTOMap = listAdjustPriceOrderBaseDetailsByCode(adjustPriceOrder.getAdjustCode(),goodsNo,null);
        boolean isGoodsNoExist = false;
        boolean isGoodsHasExist = false;
        Optional<SpuListVO> goodsInfoOptional = Optional.empty();
        if (baseDetailDTOMap.containsKey(goodsNoOrgIds)) {
            isGoodsNoExist = true;
            isGoodsHasExist = true;
        } else {
            goodsInfoOptional = getSpuInfo(goodsNo);
            if (goodsInfoOptional.isPresent()) {
                isGoodsNoExist = true;
            }
            if(!isGoodsNoExist && null!=storeId){
                //如果不存在就查询单体加盟商品
                Map<String, JoinStoreGoodsInfoDTO> joinStoreGoodsMap = getJoinStoreGoodsMap(storeId, Lists.newArrayList(goodsNo));
                if (MapUtils.isNotEmpty(joinStoreGoodsMap)) {
                    JoinStoreGoodsInfoDTO joinStoreGoodsInfoDTO = joinStoreGoodsMap.get(goodsNo);
                    if (joinStoreGoodsInfoDTO != null) {
                        isGoodsNoExist = true;
                        // 将JoinStoreGoodsInfoDTO转换为SpuListVO
                        SpuListVO spuListVO = convertJoinStoreGoodsToSpuListVO(joinStoreGoodsInfoDTO);
                        goodsInfoOptional = Optional.ofNullable(spuListVO);
                    }
                }
            }
        }
        //商品编码不存在，不处理这条消息
        if (!isGoodsNoExist) {
            return;
        } else {
        	Map<String, Map<String, Object>> groupProperty = getGroupProperty(goodsNo, Lists.newArrayList(Constants.COVIDTYPE));
        	if(null!=groupProperty && groupProperty.containsKey(goodsNo)) {
        		Map<String, Object> map = groupProperty.get(goodsNo);
        		covidType = map.get(Constants.COVIDTYPE)==null?"":map.get(Constants.COVIDTYPE).toString();
        	}
            //获取商品级别机构
            List<OrgLevelVO> orgLevelVOList = listOrgLevelVO(orgIds, supplementDataVO.getOrgLevels());

            List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(adjustPriceOrder.getChannel());
            List<Long> detailIdList = Arrays.asList(supplementDataVO.getAdjustDetailMergeIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            AdjustPriceOrderDetailEditV2 detailEditV2 = null;
            GoodsLimitStatusEnum goodsLimitStatusEnum = GoodsLimitStatusEnum.NONE;
            if (goodsInfoOptional.isPresent()) {
                SpuListVO spuListVO = goodsInfoOptional.get();
                detailEditV2 = getAdjustPriceOrderDetailEditV2FromGoodInfo(supplementDataVO.getAdjustCode(), spuListVO);
                goodsName = spuListVO.getName();
                categoryId = spuListVO.getCategoryId();
                if(StringUtils.isNotBlank(spuListVO.getDTPgood())){
                    try {
                        if(YNEnum.YES.getType()==Integer.valueOf(spuListVO.getDTPgood())){
                            dtpGood = YNEnum.YES.getDesc();
                        }
                        if(YNEnum.NO.getType()==Integer.valueOf(spuListVO.getDTPgood())){
                            dtpGood = YNEnum.NO.getDesc();
                        }
                    } catch (NumberFormatException e) {
                        logger.error("supplementAdjustPriceOrderDetailData|DTPgood转换异常",e);
                    }
                }
            } else if (isGoodsHasExist) {
                AdjustBaseDetailDTO adjustBaseDetailDTO = baseDetailDTOMap.get(goodsNoOrgIds);
                detailEditV2 = copyDetailEditV2FromBaseDetail(supplementDataVO.getAdjustCode(), adjustBaseDetailDTO);
                goodsName = adjustBaseDetailDTO.getGoodsName();
                Optional<AdjustPriceOrderDetailExtend1> detailExtentOption = AdjustPriceOrderDetailExtend1.getInstance(adjustBaseDetailDTO.getExtend1());
                if (detailExtentOption.isPresent()) {
                    categoryId = detailExtentOption.get().getCategoryId();
                    dtpGood = detailExtentOption.get().getDtpGood();
                    if(StringUtils.isNotBlank(detailExtentOption.get().getCovidType())) {
                    	covidType = detailExtentOption.get().getCovidType();
                    }
                }
            }
            AdjustPriceOrderManagePriceDTO priceDTO = getAdjustPriceOrderManagePriceDTO(adjustPriceOrder, supplementDataVO, channelIdList, orgLevelVOList);
            Optional<PriceModeResult> priceModeResultOptional = marketingExtService.getPriceModeByOrgLevelVO(goodsNo, orgLevelVOList);
            Optional<BigDecimal> priceModeOptional = basePriceOrderService.getPriceMode(goodsNo, priceTypeCode, orgLevelVOList, priceModeResultOptional, channelIdList);
            detailEditV2 = getAdjustPriceOrderDetailEditV2FromManagePriceDTO(detailEditV2, priceDTO);
            if (priceModeOptional.isPresent()) {
                detailEditV2.setOriginalPrice(priceModeOptional.get());
            }
            detailEditV2.setGmtUpdate(now);
            detailEditV2.setAdjustDetailMergeIds(detailIdList);
            DataFullEnum dataFullEnum = DataFullEnum.NOT_FULL;
            if (Boolean.TRUE.equals(supplementDataVO.getDataFull())) {
                dataFullEnum = DataFullEnum.HAS_FULL;
            }

            AdjustPriceOrderDetailExample orderDetailExample = new AdjustPriceOrderDetailExample();
            orderDetailExample.createCriteria().andAdjustCodeEqualTo(adjustCode).andGoodsNoEqualTo(goodsNo).andIdIn(idList).andPriceTypeCodeEqualTo(priceTypeCode);
            List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailMapper.selectByExample(orderDetailExample);
            AdjustPriceOrderDetail orderDetail = orderDetailList.get(0);
            AdjustPriceOrderDetailExtend1 extend1 = null;
            if (priceModeResultOptional.isPresent()) {
                BigDecimal priceCost = PriceUtil.getFenFromYuanWithNull(priceModeResultOptional.get().getModePriceCost());
                extend1 = AdjustPriceOrderDetailExtend1.getInstance(priceCost,
                    DataCompletionEnum.HAS_COMPLETED.getCode(),dataFullEnum.getCode());
            } else {
                extend1 = AdjustPriceOrderDetailExtend1.getInstance(DataCompletionEnum.HAS_COMPLETED.getCode(), dataFullEnum.getCode());

            }
            Optional<AdjustPriceOrderDetailExtend1> detailExtent1 = AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
            if(detailExtent1.isPresent()) {
            	AdjustPriceOrderDetailExtend1 extent2 = detailExtent1.get();
            	extend1.setGoodsOrgTabType(extent2.getGoodsOrgTabType());
                extend1.setGoodsTabTypeValue(extent2.getGoodsTabTypeValue());
                extend1.setOverrideLowerLevel(extent2.getOverrideLowerLevel());
                extend1.setRebate(extent2.getRebate());
            }
            List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList = findAdjustDetailStoreInfo(adjustCode, orderDetail.getOrgIds(), orgLevelVOList);
            saveDetailStoreIdCache(adjustCode, orderDetail.getId(), orgStoreDetailList, null,null);
            List<Long> businessIdList = Lists.newArrayList();
            Map<Long, List<Long>> adjustOrgIdConvertBusinessIdStoreIdMap = null;
            if(supplementDataVO.getIsPortal()) {
            	adjustOrgIdConvertBusinessIdStoreIdMap = adjustOrgIdConvertBusinessIdStoreIdMap(adjustCode, orgLevelVOList,orgStoreDetailList);
            	if(MapUtils.isNotEmpty(adjustOrgIdConvertBusinessIdStoreIdMap)) {
            		businessIdList = adjustOrgIdConvertBusinessIdStoreIdMap.keySet().stream().collect(Collectors.toList());
            	}
            }else {
            	businessIdList = adjustOrgIdConvertBusinessIdList(adjustPriceOrder.getAdjustCode(),orgLevelVOList,orgStoreDetailList);
            }
    		List<SpuNewVo> businessIdGoodsList = getBusinessIdGoodsList(businessIdList, Lists.newArrayList(goodsNo));
    		//根据连锁id集合+商品编码 验证是否首营
            extend1.setLawful(true);
    		if(businessIdGoodsList.size()!=businessIdList.size()){
    			extend1.setLawful(false);
    		}
    		Map<Long, String> pushLevelsMap = getPushLevelsMap(businessIdGoodsList);
    		if(null!=pushLevelsMap) {
    			extend1.setPushLevel(JSON.toJSONString(pushLevelsMap));
    		}
            goodsLimitStatusEnum = adjustControlService.checkGoodsLimit(goodsNo, goodsName, categoryId, covidType, pushLevelsMap, orgLevelVOList, orderDetail, userId);
            extend1.setLimitStatus(goodsLimitStatusEnum.getCode());
            extend1.setCategoryId(categoryId);
            extend1.setCovidType(covidType);
            extend1.setDetailAuditStatus(DetailAuditStatusEnum.AUDIT_PASS.getCode());
            extend1.setDetailAuditMessage("");
            extend1.setDtpGood(dtpGood);
    		extend1.setBusinessIds(StringUtils.join(businessIdList,","));
    		extend1.setLyStoreNum("-");
    		extend1.setLyPfj("-");
    		detailEditV2.setExtend1(extend1);
            detailEditV2.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(extend1));
            if (supplementDataVO.getPrice() != null) {
                AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_2.setAttrValue().accept(detailEditV2, PriceUtil.getYuanFromFen(supplementDataVO.getPrice()).toString());
                detailEditV2.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(extend1));
            }
            detailEditV2.setPriceTypeCode(supplementDataVO.getPriceTypeCode());
            List<Integer> channelList = Arrays.asList(adjustPriceOrder.getChannel().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            detailEditV2.setChannelId(channelList.get(0));
            try {
				supplementReferencePrice(detailEditV2,orgLevelVOList,now);
			} catch (Exception e) {
				logger.error("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustPriceOrderDetailData|补参考价异常|adjustCode:{},goodsNo:{},",adjustCode,goodsNo,e);
			}
            try {
            	suppLastPurchasePrice(detailEditV2,orderDetail);
			} catch (Exception e) {
				logger.error("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustPriceOrderDetailData|补最后一次采购价、定价建议异常|adjustCode:{},goodsNo:{},",adjustCode,goodsNo,e);
			}
            if(supplementDataVO.getIsPortal()) {
            	try {
            		logger.info("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustPriceOrderDetailData|suppLastPortalInfo机构:{}",adjustOrgIdConvertBusinessIdStoreIdMap);
            		Map<Long, List<Long>> validateAndExtract = validateAndExtract(adjustOrgIdConvertBusinessIdStoreIdMap);
            		if(MapUtils.isNotEmpty(validateAndExtract)) {
            			Long firstBusinessId = validateAndExtract.keySet().iterator().next();
            			List<Long> firstStoreIdList = validateAndExtract.get(firstBusinessId);
            			Long firstStoreId = firstStoreIdList.get(0);
            			suppLastPortalInfo(detailEditV2,orderDetail,firstBusinessId,firstStoreId);
            		}
    			} catch (Exception e) {
    				logger.error("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustPriceOrderDetailData|获取paotal渠道库存数量、批发价异常|adjustCode:{},goodsNo:{},",adjustCode,goodsNo,e);
    			}
            }
            adjustPriceOrderDetailExMapper.updateByAdjustCode(detailEditV2);
        }
    }

    private void saveDetailStoreIdCache(String adjustCode,Long detailId,List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList,List<Long> businessIdList,List<Long> storeIdList){
        logger.info("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustPriceOrderDetailData|saveDetailStoreIdCache|执行记录orgIds转门店维度缓存|adjustCode:{},detailId:{},orgStoreDetailList:{}",adjustCode,detailId,orgStoreDetailList);
        if(CollectionUtils.isEmpty(orgStoreDetailList) && CollectionUtils.isEmpty(storeIdList)){
            logger.error("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustPriceOrderDetailData|saveDetailStoreIdCache调价单明细行通过orgIds转门店维度数据为空|adjustCode:{},detailId:{},",adjustCode,detailId);
            return;
        }
        AdjustPriceOrderDetailStoreIdCacheDTO detailStoreIdCacheDTO = new AdjustPriceOrderDetailStoreIdCacheDTO();
        detailStoreIdCacheDTO.setAdjustCode(adjustCode);
        detailStoreIdCacheDTO.setDetailId(detailId);
        detailStoreIdCacheDTO.setBusinessIdList(CollectionUtils.isEmpty(businessIdList) ? orgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getBusinessId).collect(Collectors.toList()) : businessIdList);
        detailStoreIdCacheDTO.setStoreIdList(CollectionUtils.isEmpty(storeIdList) ? orgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).collect(Collectors.toList()) : storeIdList);
        saveAdjustPriceOrderDetailStoreIdCache(adjustCode,Collections.singletonList(detailStoreIdCacheDTO));
    }

    /**
     * 根据调价单明细行的orgIds转换门店id相关信息
     * @param adjustCode
     * @param orgIds
     * @param orgLevelVOList
     * @return
     */
    private List<AdjustPriceOrderOrgStoreDetail> findAdjustDetailStoreInfo(String adjustCode,String orgIds,List<OrgLevelVO> orgLevelVOList){
        if(StringUtils.isBlank(adjustCode) || StringUtils.isBlank(orgIds) || CollectionUtils.isEmpty(orgLevelVOList)){
            return Lists.newArrayList();
        }
        String orgStoreDetailUniqueKey = adjustCode+"_"+orgIds;
        String md5Key = Md5Utils.MD5Encode(getAdjustOrgStoreDetailRedisKey(orgStoreDetailUniqueKey));
        RBucket<List<AdjustPriceOrderOrgStoreDetail>> bucket = redissonClient.getBucket(md5Key);
        if(bucket.isExists()) {
            return bucket.get();
        }
        List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(adjustCode, orgLevelVOList,null);
        bucket.set(orgStoreDetailList,30,TimeUnit.MINUTES);
        return orgStoreDetailList;
    }

    private String getAdjustOrgStoreDetailRedisKey(String orgStoreDetailUniqueKey) {
        return RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ADJUST_ORG_STORE_DETAIL_+orgStoreDetailUniqueKey;
    }

    private void suppLastPortalInfo(AdjustPriceOrderDetailEditV2 detailEditV2,AdjustPriceOrderDetail orderDetail,Long businessId,Long storeId) {
    	//库存
    	String lyStoreNum = getLyStoreNum(orderDetail, businessId, storeId);
    	//批发价
    	List<String> goodsCodeList = Lists.newArrayList(orderDetail.getGoodsNo());
    	String lyPfj = skuMLJPrice(businessId,storeId,goodsCodeList);
    	String extend1Str = detailEditV2.getExtend1Str();
		Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(extend1Str);
		if(instance.isPresent()) {
            AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = instance.get();
            adjustPriceOrderDetailExtend1.setLyStoreNum(lyStoreNum);
            adjustPriceOrderDetailExtend1.setLyPfj(lyPfj);
            detailEditV2.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
        }
    }

    private String getLyStoreNum(AdjustPriceOrderDetail orderDetail,Long businessId,Long storeId) {
    	//库存
    	String lyStoreNum = "-";
		try {
			StockGoodsPagableQueryParam stockParam = new StockGoodsPagableQueryParam();
			stockParam.setBusinessId(businessId);
			stockParam.setStoreId(storeId);
			stockParam.setGoodsNos(Lists.newArrayList(orderDetail.getGoodsNo()));
			List<StockGoodsBatchCodeSimpleInfo> goodsBatchStoreList = stockcenterService.goodsBatchCodePage(stockParam);
			Map<String, BigDecimal> goodsBuyStockMap = getGoodsStockMap(goodsBatchStoreList, StockGoodsBatchCodeSimpleInfo::getBuyStock, BigDecimal::add);
			if(null!=goodsBuyStockMap.get(orderDetail.getGoodsNo())) {
				lyStoreNum = String.valueOf(goodsBuyStockMap.get(orderDetail.getGoodsNo()));
			}
		} catch (Exception e) {
			logger.error("AdjustPriceOrderDetailV2ServiceImpl|getLyStoreNum|异常",e);
		}
        return lyStoreNum;
    }

    /**
     * 获取目录价
     * @param businessId
     * @param channelStoreId
     * @param goodsCodeList
     */
    private String skuMLJPrice(Long businessId, Long channelStoreId, List<String> goodsCodeList) {
    	//批发价
    	String lyPfj = "-";
		try {
			Long storeId = findBusinessStoreIdApi(businessId);
			LYBusinessCommonParam commonParam = new LYBusinessCommonParam();
			commonParam.setBusinessId(businessId);
			commonParam.setStoreId(storeId);//大B
			commonParam.setChannelStoreId(channelStoreId);//小B
			commonParam.setSkuMerchantCodes(goodsCodeList);
			commonParam.setPriceTypeCode(Constants.MLJ_TYPE);//"MLJ"
			Map<String, ItemSkuQueryApiDTO> spuListVoMap = itemCenterCpExtService.querySkuPriceMap(commonParam);
			if(MapUtils.isNotEmpty(spuListVoMap)) {
				String middleLineKey = CommonUtil.createMiddleLineKey(goodsCodeList.get(0),String.valueOf(storeId), String.valueOf(channelStoreId));
				ItemSkuQueryApiDTO itemSkuQueryApiDTO = spuListVoMap.get(middleLineKey);
				BigDecimal pfj = PriceUtil.getYuanFromFenWithNull(itemSkuQueryApiDTO.getPrice());
				if(null!=pfj) {
					lyPfj = String.valueOf(pfj);
				}
			}
		} catch (Exception e) {
			logger.error("AdjustPriceOrderDetailV2ServiceImpl|skuMLJPrice|异常",e);
		}
        return lyPfj;
    }

    private Map<String, BigDecimal> getGoodsStockMap(List<StockGoodsBatchCodeSimpleInfo> goodsBatchStoreList, Function<StockGoodsBatchCodeSimpleInfo, BigDecimal> valueGetter, BinaryOperator<BigDecimal> mergeFunction) {
        return goodsBatchStoreList.stream().collect(Collectors.groupingBy(StockGoodsBatchCodeSimpleInfo::getSkuMerchantCode, Collectors.reducing(BigDecimal.ZERO, valueGetter, mergeFunction)));
    }
    public Map<Long, List<Long>> validateAndExtract(Map<Long, List<Long>> map) {
    	if(null == map) {
    		return Collections.emptyMap();
    	}
        if (map.size() != 1) {
        	return Collections.emptyMap();
        }
        Map.Entry<Long, List<Long>> entry = map.entrySet().iterator().next();
        List<Long> valueList = entry.getValue();
        if (valueList.size() != 1) {
        	return Collections.emptyMap();
        }
        return map;
    }

    private Map<Long, String> getPushLevelsMap(List<SpuNewVo> list) {
    	Map<Long, String> map = new HashMap<Long, String>();
    	if(CollectionUtils.isNotEmpty(list)) {
    		for (SpuNewVo spuNewVo : list) {
    			if(null!=spuNewVo.getBusinessId() && StringUtils.isNotBlank(spuNewVo.getPushlevel())) {
    				map.put(spuNewVo.getBusinessId(), spuNewVo.getPushlevel());
    			}
            }
    	}
    	return map;
    }

    /**
     *
     * @Title: getBusinessIdGoodsList
     * @Description: 根据连锁id和商品编码查询
     * @param: @param businessIdList
     * @param: @param goodsNoList
     * @param: @return
     * @return: List<SpuNewVo>
     * @throws
     */
    private List<SpuNewVo> getBusinessIdGoodsList(List<Long> businessIdList,List<String> goodsNoList) {
    	List<SpuNewVo> resultList = Lists.newArrayList();
    	SpuNewParamVo spuNewParamVo = new SpuNewParamVo();
        spuNewParamVo.setBusinessIdList(businessIdList);
        spuNewParamVo.setGoodsNoList(goodsNoList);
        spuNewParamVo.setPageSize(businessIdList.size() * goodsNoList.size());
        //TypeData 1 返回分页信息
        spuNewParamVo.setTypeData(1);
        try {
//            ResponseEntity<PageResponse<List<SpuNewVo>>> responseEntity = searchService.getNewSpuList4Post(spuNewParamVo);
//            itemSearchEngineService.getNewSpuList4Post()
            List<SpuNewVo> spuNewVoList = itemSearchEngineFacadeService.getNewSpuList4Post(spuNewParamVo);
            if (CollectionUtils.isEmpty(spuNewVoList)) {
                return resultList;
            }
            resultList = spuNewVoList;
        } catch (Exception e) {
            logger.error("Es|getSKuMap 获取商品信息 异常", e);
        }
        return resultList;
    }

    /**
     *
     * @Title: suppLastPurchasePrice
     * @Description: 补最后一次采购价、定价建议
     * @param:
     * @return: void
     * @throws
     */
    private void suppLastPurchasePrice(AdjustPriceOrderDetailEditV2 detailEditV2,AdjustPriceOrderDetail orderDetail) {
        try {
    		List<OrgLevelVO> orgLevelList = listOrgLevelVO(orderDetail.getOrgIds(), orderDetail.getOrgLevels());
        	List<Long> businessIdList = adjustOrgIdConvertBusinessIdList(orderDetail.getAdjustCode(),orgLevelList,null);
        	List<PriceModelQueryparam> priceModelQueryparamList = Lists.newArrayList();
        	List<PriceModelQueryOrgParam> queryOrgParamList = Lists.newArrayList();
        	PriceModelQueryparam queryParam = new PriceModelQueryparam();
        	queryParam.setGoodsNo(orderDetail.getGoodsNo());
        	PriceModeResult priceModeResult = null;
        	String lastPurchaseBusinessKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.LAST_PURCHASE_BUSINESSID_KEY + orderDetail.getGoodsNo() + StringUtils.join(businessIdList,",");
        	RBucket<PriceModeResult> bucketPrice = redissonClient.getBucket(lastPurchaseBusinessKey);
        	if(bucketPrice.isExists()) {
    			priceModeResult = bucketPrice.get();
    		}else {
    			for (Long businessId : businessIdList) {
            		PriceModelQueryOrgParam priceParam = new PriceModelQueryOrgParam(OrgLevelTypeEnum.BUSINESS.getType(),null,businessId);
            		queryOrgParamList.add(priceParam);
    			}
            	if(CollectionUtils.isEmpty(queryOrgParamList)) {
            		return;
            	}
            	queryParam.setOrgParams(queryOrgParamList);
            	priceModelQueryparamList.add(queryParam);
                ResponseEntity<List<PriceModeResult>> responseEntityList = marketingService.getPriceModelByGoodsNoListAndOrgs(priceModelQueryparamList);
                if(responseEntityList == null || responseEntityList.getStatusCode() != HttpStatus.OK) {
                    throw new AmisBadRequestException(ReturnCodeEnum.CALL_MARKET_ERROR);
                }
                if(CollectionUtils.isNotEmpty(responseEntityList.getBody())) {
                	priceModeResult = responseEntityList.getBody().get(0);
                    redissonClient.getBucket(lastPurchaseBusinessKey).set(priceModeResult,10,TimeUnit.MINUTES);
                }else {
                	return;
                }
    		}
            List<PriceDetailVO> priceDetailVOS = priceModeResult.getPriceDetailVOS();
            List<String> priceDics=new ArrayList<String>();
        	priceDics.add(lastPurchasePriceType);
        	priceDics.add(suggestPriceType);
        	List<PriceDictionaryDTO> priceDicList = priceDictionaryService.getByCodes(DictCodeEnum.PRICE_TYPE.getCode(), priceDics);
        	if(CollectionUtils.isEmpty(priceDicList) || priceDicList.size()!=priceDics.size()) {
        		throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR, "未查到最后1次采购价格或建议价格");
        	}
        	List<PriceBusinessDetail> priceBusinessDetailList = createPriceBusinessDetail(orderDetail,priceDetailVOS, priceDics, priceDicList);
        	priceDetailVOS.clear();

        	Map<String, List<PriceBusinessDetail>> pbMap = priceBusinessDetailList.stream().collect(Collectors.groupingBy(e -> e.getPriceTypeCode()));
        	String extend1Str = detailEditV2.getExtend1Str();
    		Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(extend1Str);
    		if(instance.isPresent()) {
                AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = instance.get();
                if(pbMap.containsKey(lastPurchasePriceType) && CollectionUtils.isNotEmpty(pbMap.get(lastPurchasePriceType))){
                	adjustPriceOrderDetailExtend1.setLastPurchasingPrice(pbMap.get(lastPurchasePriceType).stream().min(Comparator.comparing(PriceBusinessDetail::getPrice)).get().getPrice());
                }
                if(pbMap.containsKey(suggestPriceType) && CollectionUtils.isNotEmpty(pbMap.get(suggestPriceType))){
                	adjustPriceOrderDetailExtend1.setSuggestPrice(pbMap.get(suggestPriceType).stream().min(Comparator.comparing(PriceBusinessDetail::getPrice)).get().getPrice());
                }
    			detailEditV2.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
            }
        	savePriceBusinessDetail(priceBusinessDetailList,priceDics);
        } catch (Exception e) {
            logger.error("异步补最后一次采购价失败|suppLastPurchasePrice|adjustCode:{},goodsNo:{},priceTypeCode:{}", orderDetail.getAdjustCode(),orderDetail.getGoodsNo(),orderDetail.getPriceTypeCode(),e);
        }
    }

    private void savePriceBusinessDetail(List<PriceBusinessDetail> priceBusinessDetailList,List<String> priceDics) {
 		List<Long> batchIdList = IdGeneratorUtils.getPriceBusinessDetailHistoryIdBatch(priceBusinessDetailList.size());
        List<PriceBusinessDetailHistory> priceBusinessDetailHistoryList = priceBusinessDetailList.stream().map(priceBusinessDetail -> {
             PriceBusinessDetailHistory history = new PriceBusinessDetailHistory();
             BeanUtils.copyProperties(priceBusinessDetail, history);
             history.setId(String.valueOf(batchIdList.remove(0)));
             return history;
        }).collect(Collectors.toList());
        //此次需要验证 连锁id+调价单号+商品编码+价格类型 是否存在
        checkPriceBusinessDetailHistoryExist(priceBusinessDetailHistoryList, priceDics);
        if(CollectionUtils.isEmpty(priceBusinessDetailHistoryList)){
        	return;
        }
        priceBusinessDetailHistoryService.batchInsert(priceBusinessDetailHistoryList);
        priceBusinessDetailList.clear();
        priceBusinessDetailHistoryList.clear();
    }

    private List<PriceBusinessDetail> createPriceBusinessDetail(AdjustPriceOrderDetail orderDetail,List<PriceDetailVO> priceDetailVOS,List<String> priceDics,List<PriceDictionaryDTO> priceDicList){
    	Date currentDate = new Date();
    	int dataSize = priceDetailVOS.size() * priceDics.size();
		int idIndex = 0;
		List<Long> batchIdList = IdGeneratorUtils.getPriceBusinessDetailHistoryIdBatch(dataSize);
        //封装price_business_detail_history_
		List<PriceBusinessDetail> priceBusinessDetailList = Lists.newArrayList();
		PriceBusinessDetail priceBusinessDetail = null;
		Map<String, String> uniqueMap = Maps.newHashMap();
        for (PriceDetailVO priceDetailVO : priceDetailVOS) {
			for (PriceDictionaryDTO priceDic : priceDicList) {
				priceBusinessDetail = new PriceBusinessDetail();
				priceBusinessDetail.setId(String.valueOf(batchIdList.get(idIndex++)));
				priceBusinessDetail.setGoodsNo(orderDetail.getGoodsNo());
				priceBusinessDetail.setOrderCode(orderDetail.getAdjustCode());
				priceBusinessDetail.setOrderType(OrderTypeEnum.ORDER_TYPE_ADJUST.getTypeCode());
				priceBusinessDetail.setChannelId(orderDetail.getChannelId());
				priceBusinessDetail.setBusinessId(priceDetailVO.getOrgOutId());
				if(priceDic.getDictCode().equals(lastPurchasePriceType)) {
					priceBusinessDetail.setPrice(priceDetailVO.getLastPurchasingPrice());
				}else if(priceDic.getDictCode().equals(suggestPriceType)){
					priceBusinessDetail.setPrice(priceDetailVO.getSuggestPrice());
				}else {
					priceBusinessDetail.setPrice(BigDecimal.ZERO);
				}
				priceBusinessDetail.setPriceTypeCode(priceDic.getDictCode());
				priceBusinessDetail.setPriceTypeId(priceDic.getId());
				priceBusinessDetail.setPriceTypeName(priceDic.getDictName());
				priceBusinessDetail.setStatus((byte)0);
				priceBusinessDetail.setVersion(1);
				priceBusinessDetail.setGmtCreate(currentDate);
				priceBusinessDetail.setCreatedBy(orderDetail.getCreatedBy());
				priceBusinessDetail.setGmtUpdate(currentDate);
				priceBusinessDetail.setComment("");
				if(uniqueMap.containsKey(businessEetailUnique(priceBusinessDetail))) {
					continue;
				}
				priceBusinessDetailList.add(priceBusinessDetail);
				uniqueMap.put(businessEetailUnique(priceBusinessDetail),"");
			}
		}
        return priceBusinessDetailList;
    }

    private String businessEetailUnique(PriceBusinessDetail pbd) {
    	return pbd.getBusinessId()+"_"+pbd.getGoodsNo()+"_"+pbd.getPriceTypeCode()+"_"+pbd.getChannelId();
    }

    private List<OrgLevelVO> listOrgLevelVO(String orgIds, String orgLevels) {
        List<Long> orgIdList = Arrays.asList(orgIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<Integer> orgLevelList = Arrays.asList(orgLevels.split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
        List<OrgLevelVO> orgLevelVOList = Lists.newArrayList();
        OrgLevelVO orgLevelVO = null;
        for (int i = 0; i < orgIdList.size(); i++) {
            orgLevelVO = new OrgLevelVO();
            orgLevelVO.setOrgId(orgIdList.get(i));
            orgLevelVO.setOrgLevel(orgLevelList.get(i));
            orgLevelVOList.add(orgLevelVO);
        }
        return orgLevelVOList;
    }

    /**
     *
     * 获取商品数据，最多3次
     * @param goodsNo
     * @return
     */
    private Optional<SpuListVO> getSpuInfo(String goodsNo) {
        int count = 0;
        int totalCount = 3;
        Optional<SpuListVO> goodsInfoOptional = Optional.empty();
        while(count++ < totalCount) {
            try {
                goodsInfoOptional = searchExtService.getSpuInfo(goodsNo);
                break;
            } catch (Exception e) {
                logger.info("AdjustPriceOrderDetailV2ServiceImpl|getSpuInfo| ex:", e);
            }
        }
        return goodsInfoOptional;
    }

    /**
     *
     * @Title: adjustOrgIdConvertBusinessIdList
     * @Description: 调价单机构转换连锁id
     * @param: @param adjustCode
     * @param: @param orgLevelVOList
     * @param: @return
     * @return: List<Long>
     * @throws
     */
    private List<Long> adjustOrgIdConvertBusinessIdList(String adjustCode,List<OrgLevelVO> orgLevelVOList,List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList){
    	Date now = new Date();
    	List<Long> businessIdList = Lists.newArrayList();
    	List<Long> orgIdList = orgLevelVOList.stream().map(OrgLevelVO::getOrgId).sorted().collect(Collectors.toList());
    	String orgIdKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ORGID_BUSINESSID_KEY+Md5Utils.MD5Encode(StringUtils.join(orgIdList,","));
    	RBucket<List<Long>> bucket = redissonClient.getBucket(orgIdKey);
    	if(bucket.isExists()) {
    		businessIdList = bucket.get();
    	}else {
            if(CollectionUtils.isEmpty(orgStoreDetailList)){
                orgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(adjustCode, orgLevelVOList,now);
            }
        	if(CollectionUtils.isNotEmpty(orgStoreDetailList)) {
        		businessIdList = orgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getBusinessId).distinct().collect(Collectors.toList());
            	redissonClient.getBucket(orgIdKey).set(businessIdList,1,TimeUnit.HOURS);
            	orgStoreDetailList.clear();
        	}
    	}
    	return businessIdList;
    }

    private Map<Long,List<Long>> adjustOrgIdConvertBusinessIdStoreIdMap(String adjustCode,List<OrgLevelVO> orgLevelVOList,List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList){
    	Date now = new Date();
    	Map<Long,List<Long>> businessIdStoreIdMap = Maps.newHashMap();
    	List<Long> orgIdList = orgLevelVOList.stream().map(OrgLevelVO::getOrgId).sorted().collect(Collectors.toList());
    	String orgIdKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ORGID_BUSINESSID_STOREID_KEY+Md5Utils.MD5Encode(StringUtils.join(orgIdList,","));
    	//RBucket<Map<Long,List<Long>>> bucket = redissonClient.getBucket(orgIdKey);
    	RMap<Long, List<Long>> rMap = redissonClient.getMap(orgIdKey);
    	if(rMap.isExists()) {
    		businessIdStoreIdMap = rMap.readAllMap();
    	}else {
            if(CollectionUtils.isEmpty(orgStoreDetailList)){
                orgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(adjustCode, orgLevelVOList,now);
            }
        	if(CollectionUtils.isNotEmpty(orgStoreDetailList)) {
        		businessIdStoreIdMap = orgStoreDetailList.stream()
                        .collect(Collectors.groupingBy(
                                AdjustPriceOrderOrgStoreDetail::getBusinessId,
                                Collectors.mapping(AdjustPriceOrderOrgStoreDetail::getStoreId, Collectors.toList())
                        ));
            	rMap.putAll(businessIdStoreIdMap);
            	rMap.expire(30,TimeUnit.MINUTES);
            	orgStoreDetailList.clear();
        	}
    	}
    	return businessIdStoreIdMap;
    }
    /**
     *
     * @Title: supplementReferencePrice
     * @Description: 补充  集团参考零售价、公司参考零售价、参考进价、最低限价、限价开始时间、限价结束时间
     * @param: @param detailEditV2
     * @param: @param orgLevelVOList
     * @param: @param now
     * @return: void
     * @throws
     */
    private void supplementReferencePrice(AdjustPriceOrderDetailEditV2 detailEditV2,List<OrgLevelVO> orgLevelVOList,Date now) {
    	List<Long> businessIdList = adjustOrgIdConvertBusinessIdList(detailEditV2.getAdjustCode(),orgLevelVOList,null);
    	if(CollectionUtils.isNotEmpty(businessIdList)) {
    		Map<String, Map<String, Object>> propertyMap = getReferencePrice(businessIdList,detailEditV2,orgLevelVOList,now);
            savePriceBusinessDetailHistory(detailEditV2,propertyMap);
    	}
    }
    /**
     *
     * @Title: getReferencePrice
     * @Description: 获取  集团参考零售价、公司参考零售价、参考进价、最低限价、限价开始时间、限价结束时间
     * @param: @param detailEditV2
     * @return: void
     * @throws
     */
    private Map<String, Map<String, Object>> getReferencePrice(List<Long> businessIdList,AdjustPriceOrderDetailEditV2 detailEditV2,List<OrgLevelVO> orgLevelVOList,Date now) {
    	Map<String, Map<String, Object>> propertyAllMap = new HashedMap<String, Map<String,Object>>();
    	Map<String, Map<String, Object>> propertyMap = null;
		List<PropertyParamDto> propertyParamDtoList = null;
		PropertyParamDto propertyDto= null;
		for (Long businessId : businessIdList) {
			String key = detailEditV2.getGoodsNo()+"_"+businessId;
			String bIdGoodNoKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.BUSINESSID_GOODSNO_REFERENCEPRICE_KEY+businessId+"_"+detailEditV2.getGoodsNo();
			RBucket<Map<String, Object>> bIdGoodNoMap = redissonClient.getBucket(bIdGoodNoKey);
			if(bIdGoodNoMap.isExists()) {
				propertyAllMap.put(key, bIdGoodNoMap.get());
				continue;
			}
			propertyParamDtoList = new ArrayList<PropertyParamDto>();
			propertyDto = new PropertyParamDto();
			propertyDto.setBusinessId(businessId);
			propertyDto.setGoodsNo(detailEditV2.getGoodsNo());
			List<PropertyParamRankDto> paramRankDtos = new ArrayList<PropertyParamRankDto>();
			PropertyParamRankDto paramRankDto=null;
			for (AdjustPriceOrderDetailRefPriceColumnEnum refColumnEnum : AdjustPriceOrderDetailRefPriceColumnEnum.values()) {
				paramRankDto = new PropertyParamRankDto();
				paramRankDto.setProperty(refColumnEnum.getName());
				if(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_1.getName().equals(refColumnEnum.getName())) {
					paramRankDto.setRank(PropertyRankEnum.GROUP.getCode());
				}else {
					paramRankDto.setRank(PropertyRankEnum.BUSINESS.getCode());
				}
				paramRankDtos.add(paramRankDto);
				propertyDto.setParamRankDtos(paramRankDtos);
	        }
			setOtherProperty(propertyDto);
			propertyParamDtoList.add(propertyDto);
			//由于getProperty返回map数据结构-key为商品编码，入参为同一个商品编码查询多个连锁属性时 最后一个商品编码会覆盖前面相同商品编码数据 所以每个连锁单独请求数据
			ResponseEntity<Map<String, Map<String, Object>>> responseEntity = feignForestService.getProperty(propertyParamDtoList);
			if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
				logger.error("AdjustPriceOrderDetailV2ServiceImpl|supplementVariousPrice|调用forest系统获取商品属性失败");
			}else {
				propertyMap = responseEntity.getBody();
				if(MapUtils.isNotEmpty(propertyMap)) {
					propertyAllMap.put(key, propertyMap.get(detailEditV2.getGoodsNo()));
					redissonClient.getBucket(bIdGoodNoKey).set(propertyMap.get(detailEditV2.getGoodsNo()),1,TimeUnit.HOURS);
				}
			}
			propertyParamDtoList.clear();
		}
		return propertyAllMap;
    }

    private void savePriceBusinessDetailHistory(AdjustPriceOrderDetailEditV2 detailEditV2,Map<String, Map<String, Object>> propertyMap) {
    	List<String> priceDics=new ArrayList<String>();
    	Map<String, String> priceTypeReferenceConfig = getPriceTypeReferenceConfig();
    	priceTypeReferenceConfig.forEach((k,v) -> {
    		priceDics.add(v);
    	});
    	List<PriceDictionaryDTO> priceDicList = priceDictionaryService.getByCodes(DictCodeEnum.PRICE_TYPE.getCode(), priceDics);
		Map<String, PriceDictionaryDTO> priceDicMap = priceDicList.stream().collect(Collectors.toMap(PriceDictionaryDTO::getDictCode, Function.identity(), (v1, v2) -> v1));

		//集团参考零售价取第一条refretailprice即可  限价开始时间和限价结束时间存储在限价字段扩展属性里
		BigDecimal refRetailPrice = null;
		List<BigDecimal> comRetailPriceList = Lists.newArrayList();
		List<BigDecimal> refPurchPriceList = Lists.newArrayList();
		List<BigDecimal> floorPriceList = Lists.newArrayList();
		int dataSize = 0;
    	Iterator<Entry<String, Map<String, Object>>> iterator = propertyMap.entrySet().iterator();
    	while(iterator.hasNext()) {
    		Entry<String, Map<String, Object>> entry = iterator.next();
    		Map<String, Object> propertys = entry.getValue();
    		if(checkProperty(propertys, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_2.getName())) {
    			dataSize++;
    			comRetailPriceList.add(new BigDecimal(propertys.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_2.getName()).toString()));
    		}
    		if(checkProperty(propertys, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName())) {
    			dataSize++;
    			refPurchPriceList.add(new BigDecimal(propertys.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName()).toString()));
    		}
    		if(checkProperty(propertys, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName())) {
    			dataSize++;
    			floorPriceList.add(new BigDecimal(propertys.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName()).toString()));
    		}
    		if(checkProperty(propertys, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_1.getName())) {
				refRetailPrice = new BigDecimal(propertys.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_1.getName()).toString());
			}
    	}
    	String extend1Str = detailEditV2.getExtend1Str();
		Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(extend1Str);
		if(instance.isPresent()) {
            AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = instance.get();
			adjustPriceOrderDetailExtend1.setRefRetailPrice(refRetailPrice);
			adjustPriceOrderDetailExtend1.setComretailprice(CollectionUtils.isNotEmpty(comRetailPriceList)?Collections.min(comRetailPriceList):null);
			adjustPriceOrderDetailExtend1.setRefpurchprice(CollectionUtils.isNotEmpty(refPurchPriceList)?Collections.min(refPurchPriceList):null);
			adjustPriceOrderDetailExtend1.setFloorprice(CollectionUtils.isNotEmpty(floorPriceList)?Collections.min(floorPriceList):null);
			if(PriceTypeModeEnum.LSJ.getPriceTypeCode().equals(detailEditV2.getPriceTypeCode()) && null != detailEditV2.getPrice()) {
				boolean lessThanAny = isLessThanAny(PriceUtil.getYuanFromFen(detailEditV2.getPrice()), floorPriceList);
				adjustPriceOrderDetailExtend1.setPriceFloorRemind(lessThanAny ? 1 : 0);
			}
			detailEditV2.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
        }

    	List<Long> batchIdList = IdGeneratorUtils.getPriceBusinessDetailHistoryIdBatch(dataSize);
    	int idIndex = 0;
    	String id = null;
    	List<PriceBusinessDetailHistory> adjustPriceHisList = new ArrayList<PriceBusinessDetailHistory>();
    	Iterator<Entry<String, Map<String, Object>>> propertyIt = propertyMap.entrySet().iterator();
    	while(propertyIt.hasNext()) {
    		Entry<String, Map<String, Object>> entry = propertyIt.next();
    		Map<String, Object> propertys = entry.getValue();
    		if(checkProperty(propertys, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_2.getName())) {
    			id = String.valueOf(batchIdList.get(idIndex++));
    			PriceBusinessDetailHistory his = new PriceBusinessDetailHistory();
    			createPriceBusinessDetailHistory(id,detailEditV2,priceDicMap, propertys, priceTypeReferenceConfig, his, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_2.getName());
    			if(null!=his.getPrice()) {
    				adjustPriceHisList.add(his);
    			}
    		}
    		if(checkProperty(propertys, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName())) {
    			id = String.valueOf(batchIdList.get(idIndex++));
    			PriceBusinessDetailHistory his = new PriceBusinessDetailHistory();
    			createPriceBusinessDetailHistory(id,detailEditV2,priceDicMap, propertys, priceTypeReferenceConfig, his, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName());
    			if(null!=his.getPrice()) {
    				adjustPriceHisList.add(his);
    			}
    		}
    		if(checkProperty(propertys, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName())) {
    			id = String.valueOf(batchIdList.get(idIndex++));
    			PriceBusinessDetailHistory his = new PriceBusinessDetailHistory();
    			createPriceBusinessDetailHistory(id,detailEditV2,priceDicMap, propertys, priceTypeReferenceConfig, his, AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName());
    			if(null!=his.getPrice()) {
    				adjustPriceHisList.add(his);
    			}
    		}
    	}
    	//此次需要验证 连锁id+调价单号+商品编码+渠道+价格类型 是否存在
    	checkPriceBusinessDetailHistoryExist(adjustPriceHisList, priceDics);
    	if(CollectionUtils.isEmpty(adjustPriceHisList)){
    		return;
    	}
    	Lists.partition(adjustPriceHisList, 50).forEach(insertPartitionAdjustPriceOrderDetailList -> priceBusinessDetailHistoryService.batchInsert(insertPartitionAdjustPriceOrderDetailList));
    	batchIdList.clear();
    	adjustPriceHisList.clear();
    }

    public static boolean isLessThanAny(BigDecimal price, List<BigDecimal> floorPriceList) {
        return floorPriceList.stream().anyMatch(floorPrice -> price.compareTo(floorPrice) < 0);
    }

    private void checkPriceBusinessDetailHistoryExist(List<PriceBusinessDetailHistory> adjustPriceHisList,List<String> priceTypeCodeList) {
    	if(CollectionUtils.isEmpty(adjustPriceHisList)) {
    		return;
    	}
    	PriceBusinessDetailHistory his = adjustPriceHisList.get(0);
    	PriceBusinessDetailHistoryExample example = new PriceBusinessDetailHistoryExample();
    	List<Long> businessIdList = adjustPriceHisList.stream().map(PriceBusinessDetailHistory::getBusinessId).distinct().collect(Collectors.toList());
    	for (Long businessId : businessIdList) {
    		example.clear();
            example.createCriteria().andBusinessIdEqualTo(businessId).
    	    andOrderCodeEqualTo(his.getOrderCode()).
    	    andGoodsNoEqualTo(his.getGoodsNo()).
    	    andChannelIdEqualTo(his.getChannelId()).
    	    andPriceTypeCodeIn(priceTypeCodeList);
    	    long count = priceBusinessDetailHistoryService.countByExample(example);
    	    if(count>0) {
    	    	//存在 则不在保存
    	    	Iterator<PriceBusinessDetailHistory> it = adjustPriceHisList.iterator();
    	    	while(it.hasNext()){
    	    		PriceBusinessDetailHistory hisData = it.next();
    	    	    if(hisData.getBusinessId().equals(businessId)) {
    	    	    	it.remove();
    	    	    }
    	    	}
    	    }
		}
    }
    /**
     *
     * @Title: checkProperty
     * @Description: 检查属性是否有值
     * @param: @param propertys
     * @param: @param column
     * @param: @return
     * @return: boolean
     * @throws
     */
    private boolean checkProperty(Map<String, Object> propertys,String column) {
    	if(propertys.containsKey(column) && StringUtils.isNotBlank(propertys.get(column).toString())) {
			return true;
		}
    	return false;
    }

    private void createPriceBusinessDetailHistory(String id,AdjustPriceOrderDetailEditV2 detailEditV2,Map<String, PriceDictionaryDTO> priceDicMap,Map<String, Object> propertys,
    		Map<String, String> priceTypeReferenceConfig,PriceBusinessDetailHistory his,String column) {
    	Object object = propertys.get(column);
    	String goodsNo = propertys.get("goodsNo").toString();
		Long businessId = Long.valueOf(propertys.get("businessId").toString());
		BigDecimal price = BigDecimal.ZERO;
		if(null==object || "".equals(object)) {
			return;
		}
		price = new BigDecimal(object.toString());
		String priceTypeCode = priceTypeReferenceConfig.get(column);
		PriceDictionaryDTO priceDic = priceDicMap.get(priceTypeCode);
		his.setPrice(price);
		his.setPriceTypeCode(priceDic.getDictCode());
		his.setPriceTypeId(priceDic.getId());
		his.setPriceTypeName(priceDic.getDictName());
		his.setId(id);
		his.setGoodsNo(goodsNo);
		his.setOrderCode(detailEditV2.getAdjustCode());
		his.setOrderType(OrderTypeEnum.ORDER_TYPE_ADJUST.getTypeCode());
		his.setChannelId(detailEditV2.getChannelId());
		his.setBusinessId(businessId);
		his.setStatus((byte)0);
		his.setVersion(1);
		his.setComment("");
		his.setGmtCreate(new Date());
		supplementPriceBusinessDetailHistoryExtend(his, propertys, column);
    }

    private void supplementPriceBusinessDetailHistoryExtend(PriceBusinessDetailHistory his,Map<String, Object> propertys,String column) {
    	if(column.equals(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName())) {
			String startdate = propertys.get(Constants.FLOORPRICE_STARTDATE)==null?"":propertys.get(Constants.FLOORPRICE_STARTDATE).toString();
			String enddate = propertys.get(Constants.FLOORPRICE_ENDDATE)==null?"":propertys.get(Constants.FLOORPRICE_ENDDATE).toString();
			String extend = his.getExtend();
			Optional<PriceBusinessDetailHistoryExtend> instance = PriceBusinessDetailHistoryExtend.getInstance(extend);
			if(instance.isPresent()) {
				PriceBusinessDetailHistoryExtend priceBusinessDetailHistoryExtend = instance.get();
				priceBusinessDetailHistoryExtend.setFloorPriceStartDate(toFormatDate(startdate));
				priceBusinessDetailHistoryExtend.setFloorPriceEndDate(toFormatDate(enddate));
				his.setExtend(PriceBusinessDetailHistoryExtend.toJSONFormatStr(priceBusinessDetailHistoryExtend));
			}else {
				String jsonFormatStr = PriceBusinessDetailHistoryExtend.getJSONFormatStr(toFormatDate(startdate), toFormatDate(enddate));
				his.setExtend(jsonFormatStr);
			}
		}
    }

    private String toFormatDate(String strDate) {
		try {
			if(StringUtils.isBlank(strDate)){
				return "";
			}
			Date date = new SimpleDateFormat("yyyyMMdd").parse(strDate);
			return new SimpleDateFormat("yyyy-MM-dd").format(date);
		} catch (ParseException e) {
			return "";
		}
	}
    /**
     *
     * @Title: getPriceTypeReferenceConfig
     * @Description: 参考价相关编码配置
     * @param: @return
     * @return: JSONObject
     * @throws
     */
    @SuppressWarnings("unchecked")
	private Map<String, String> getPriceTypeReferenceConfig() {
    	Map<String, String> priceTypeReferenceMap = JSONObject.parseObject(priceTypeReference, Map.class);
        return priceTypeReferenceMap;
    }

    /**
     *
     * @Title: setOtherProperty
     * @Description: 补充限价开始时间和限价结束时间等其他属性
     * @param: @param propertyDto
     * @return: void
     * @throws
     */
    private void setOtherProperty(PropertyParamDto propertyDto) {
    	PropertyParamRankDto startDateDto = new PropertyParamRankDto();
    	startDateDto.setProperty(Constants.FLOORPRICE_STARTDATE);
    	startDateDto.setRank(PropertyRankEnum.BUSINESS.getCode());
    	propertyDto.getParamRankDtos().add(startDateDto);
    	PropertyParamRankDto endDateDto = new PropertyParamRankDto();
    	endDateDto.setProperty(Constants.FLOORPRICE_ENDDATE);
    	endDateDto.setRank(PropertyRankEnum.BUSINESS.getCode());
    	propertyDto.getParamRankDtos().add(endDateDto);
    }


    /**
     * 根据调价单获取已有商品的基础信息
     * @param adjustCode
     * @return
     */
    private Map<String, AdjustBaseDetailDTO> listAdjustPriceOrderBaseDetailsByCode(String adjustCode,String goodsNo,Long skuId) {
        Map<String, AdjustBaseDetailDTO> results = Collections.emptyMap();
        List<AdjustBaseDetailDTO> adjustBaseDetailDTOList = adjustPriceOrderDetailExMapper.listAdjustPriceOrderBaseDetailsByCode(adjustCode,goodsNo,skuId);
        if (CollectionUtils.isNotEmpty(adjustBaseDetailDTOList)) {
            results = adjustBaseDetailDTOList.stream().collect(Collectors.toMap(AdjustBaseDetailDTO::getGoodsNoOrgIds, Function.identity(),
                (value1, value2) -> {
                   if (value1.getComment() != null || value1.getLabel() != null
                       || value1.getReason() != null ||  (value1.getPriceFlag() != null && value1.getPriceFlag() == PriceFlagEnum.YES.getCode())) {
                       return value1;
                   }
                   return value2;
                }));
        }
        return results;
    }

    /**
     * 从已经存在的调价单商品明细的基本信息拷贝值
     * @param adjustCode
     * @param adjustBaseDetailDTO
     * @return
     */
    private AdjustPriceOrderDetailEditV2 copyDetailEditV2FromBaseDetail(String adjustCode, AdjustBaseDetailDTO adjustBaseDetailDTO) {
        AdjustPriceOrderDetailEditV2 detailEditV2 = new AdjustPriceOrderDetailEditV2();
        detailEditV2.setAdjustCode(adjustCode);
        detailEditV2.setSpuId(adjustBaseDetailDTO.getSpuId());
        detailEditV2.setGoodsNo(adjustBaseDetailDTO.getGoodsNo());
        detailEditV2.setCurName(adjustBaseDetailDTO.getCurName());
        detailEditV2.setManufacturer(adjustBaseDetailDTO.getManufacturer());
        detailEditV2.setJhiSpecification(adjustBaseDetailDTO.getJhiSpecification());
        detailEditV2.setDosage(adjustBaseDetailDTO.getDosage());
        detailEditV2.setProdarea(adjustBaseDetailDTO.getProdarea());
        detailEditV2.setGoodsName(adjustBaseDetailDTO.getGoodsName());
        detailEditV2.setGoodsUnit(adjustBaseDetailDTO.getGoodsUnit());
        detailEditV2.setComment(adjustBaseDetailDTO.getComment());
        detailEditV2.setLabel(adjustBaseDetailDTO.getLabel());
        detailEditV2.setPriceFlag(adjustBaseDetailDTO.getPriceFlag());
        detailEditV2.setReason(adjustBaseDetailDTO.getReason());
        detailEditV2.setExtend1Str(adjustBaseDetailDTO.getExtend1());
        return detailEditV2;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByAdjustCodeAndGoodsNoAndPriceType(String adjustCode, String goodsNo, String priceTypeCode) {
        if (adjustCode == null || goodsNo == null || priceTypeCode == null) {
            throw new AmisBusinessException(ReturnCodeEnum.PARAM_ERROR);
        }
        adjustPriceOrderDetailExMapper.deleteByAdjustCodeAndGoodsNoAndPriceType(adjustCode, goodsNo, priceTypeCode);
    }

    @Override
    public boolean isAdjustPriceOrderDetailDataFull(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        int notFull = adjustPriceOrderDetailExMapper.countAdjustPriceOrderDetailSupplementNotFull(adjustPriceOrder.getAdjustCode());
        return notFull > 0 ? false : true;
    }

    @Override
    public boolean isAdjustPriceOrderDetailDataFullByCode(String adjustCode, TokenUserDTO userDTO) {
        if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        int notFull = adjustPriceOrderDetailExMapper.countAdjustPriceOrderDetailSupplementNotFull(adjustCode);
        return notFull > 0 ? false : true;
    }

    @Override
    public List<AdjustPriceOrderDetail> getToCheckOrderDetailListForDetailDataNotFull(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailExMapper.getToCheckOrderDetailListForDetailDataNotFull(adjustPriceOrder.getAdjustCode());
        return orderDetailList == null ? Collections.emptyList() : orderDetailList;
    }

    @Override
    public List<AdjustPriceOrderDetail> getToCheckOrderDetailListForDetailDataNotFullByCode(String adjustCode, TokenUserDTO userDTO) {
        if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailExMapper.getToCheckOrderDetailListForDetailDataNotFull(adjustCode);
        return orderDetailList == null ? Collections.emptyList() : orderDetailList;
    }

    @Override
    public boolean hasGoodDetail(String adjustCode, TokenUserDTO userDTO) {
        if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
        example.createCriteria().andStatusEqualTo((byte)StatusEnum.NORMAL.getCode())
                .andAdjustCodeEqualTo(adjustCode);
        long count = adjustPriceOrderDetailMapper.countByExample(example);
        return count > 0 ? true : false;
    }

    @Override
    public List<String> getGoodsNoListForDetailDataNotFull(Long adjustPriceOrderId, TokenUserDTO userDTO) {
        if (adjustPriceOrderId == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        List<String> goodsNoList = adjustPriceOrderDetailExMapper.getGoodsNoListForDetailDataNotFull(adjustPriceOrder.getAdjustCode());
        return goodsNoList == null ? Collections.emptyList() : goodsNoList;
    }

    @Override
    public List<String> getGoodsNoListForDetailDataNotFullByCode(String adjustCode, TokenUserDTO userDTO) {
        if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<String> goodsNoList = adjustPriceOrderDetailExMapper.getGoodsNoListForDetailDataNotFull(adjustCode);
        return goodsNoList == null ? Collections.emptyList() : goodsNoList;
    }

    @Override
    public Optional<AdjustPriceOrderDetail> getAdjustPriceOrderDetail(String adjustCode, String goodsNo, Integer channelId, String priceTypeCode) {
        AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
        example.setLimit(1);
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode)
            .andGoodsNoEqualTo(goodsNo)
            .andChannelIdEqualTo(channelId)
            .andPriceTypeCodeEqualTo(priceTypeCode)
            .andStatusEqualTo((byte)0);
        List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = adjustPriceOrderDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(adjustPriceOrderDetailList)) {
            return Optional.empty();
        }
        return Optional.of(adjustPriceOrderDetailList.get(0));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetOrderDetailsByChannelsAndPriceTypes(AdjustPriceOrder adjustPriceOrder, PriceOrderPriceTypeAndChannelChange orderPriceTypeAndChannelChange,
        TokenUserDTO userDTO, Date operateTime) {
        if (adjustPriceOrder == null || adjustPriceOrder.getAdjustCode() == null || orderPriceTypeAndChannelChange == null) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.resetOrderDetailsByChannelsAndPriceTypes]  参数都不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        String adjustCode = adjustPriceOrder.getAdjustCode();
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getDeleteChannelIdList())) {
            List<Long> detailIdList = selectAdjustPriceOrderDetailIdByAdjustCode(adjustCode, orderPriceTypeAndChannelChange.getDeleteChannelIdList(), null);
            deleteByAdjustCodeAndChannelIds(adjustCode, orderPriceTypeAndChannelChange.getDeleteChannelIdList());
            deleteAdjustPriceOrderDetailStoreIdCache(adjustCode,detailIdList);
        }
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getDeletePriceTypeCodeList())) {
            List<Long> detailIdList = selectAdjustPriceOrderDetailIdByAdjustCode(adjustCode, null, orderPriceTypeAndChannelChange.getDeletePriceTypeCodeList());
            deleteByAdjustPriceTypeCodes(adjustCode, orderPriceTypeAndChannelChange.getDeletePriceTypeCodeList());
            deleteAdjustPriceOrderDetailStoreIdCache(adjustCode,detailIdList);
        }
        //有新增渠道和有旧的价格类型保留
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getAddChannelIdList()) &&
            CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getLeavePriceTypeCodeList())) {
            copyOrderDetailByChannelIdsAndPriceTypeCodes(adjustCode, orderPriceTypeAndChannelChange.getAddChannelIdList(),
                orderPriceTypeAndChannelChange.getLeavePriceTypeCodeList(), operateTime);
        }
        if (CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getChangeAfterChannelIdList()) &&
            CollectionUtils.isNotEmpty(orderPriceTypeAndChannelChange.getAddPriceTypeCodeList())
        ) {
            addOrderDetailByChannelIdsAndPriceTypeCodes(adjustPriceOrder, orderPriceTypeAndChannelChange.getChangeAfterChannelIdList(),
                orderPriceTypeAndChannelChange.getAddPriceTypeCodeList(), userDTO, operateTime);
        }
    }

    @Override
    public List<AdjustPriceOrderDetail> selectOrderDetailV2ListGroupByGoodAndPriceType(String adjustCode, List<String> priceTypeCodeList) {
        AdjustPriceOrderDetailListV2Param param = new AdjustPriceOrderDetailListV2Param();
        param.setAdjustCode(adjustCode);
        param.setToPage(false);
        param.setPriceTypeCodeList(priceTypeCodeList);
        List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = adjustPriceOrderDetailExMapper.selectOrderDetailV2ListPageGroupByGoodAndPriceType(param);
        return adjustPriceOrderDetailList == null ? Collections.emptyList() : adjustPriceOrderDetailList;
    }

    /**
     * 根据渠道类型拷贝遗留下来的价格类型数据
     * @param adjustCode
     * @param addChannelIdList
     * @param leavePriceTypeCodeList
     */
    private void copyOrderDetailByChannelIdsAndPriceTypeCodes(String adjustCode, List<Integer> addChannelIdList, List<String> leavePriceTypeCodeList, Date operateTime) {
        List<AdjustPriceOrderDetail> adjustPriceOrderDetails = selectOrderDetailV2ListGroupByGoodAndPriceType(adjustCode, leavePriceTypeCodeList);
        int size = addChannelIdList.size() * adjustPriceOrderDetails.size();
        if (size > 0) {
            List<AdjustPriceOrderDetail> toInsertOrderDetais = new ArrayList<>(size);
            long [] adjustDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.ADJUST_PRICE_ORDER_DETAIL.getBiz(), size);
            Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(addChannelIdList);
            int idIndex = 0;
            for (PriceChannel priceChannel : priceChannelMap.values()) {
                for (AdjustPriceOrderDetail adjustPriceOrderDetail : adjustPriceOrderDetails) {
                    AdjustPriceOrderDetail toInsertOrderDetail = copyFromPriceTypeOrderDetail(adjustPriceOrderDetail, operateTime);
                    toInsertOrderDetail.setChannelId(priceChannel.getChannelId());
                    toInsertOrderDetail.setChannelOutCode(priceChannel.getOutChannelCode());
                    toInsertOrderDetail.setChannelEnCode(priceChannel.getChannelEnCode());
                    toInsertOrderDetail.setAdjustDetailId(String.valueOf(adjustDetailIds[idIndex++]));
                    toInsertOrderDetais.add(toInsertOrderDetail);
                }
            }
            Lists.partition(toInsertOrderDetais, 200)
                .forEach(toInsertPartitionOrderDetais -> adjustPriceOrderDetailExMapper.batchInsert(toInsertPartitionOrderDetais));
        }
    }

    /**
     * 新增新的渠道、新价格类型对应所有商品的调价单明细
     * @param adjustPriceOrder
     * @param addChannelIdList
     * @param addPriceTypeCodeList
     */
    private void addOrderDetailByChannelIdsAndPriceTypeCodes(AdjustPriceOrder adjustPriceOrder, List<Integer> addChannelIdList, List<String> addPriceTypeCodeList,
        TokenUserDTO userDTO, Date operateTime) {
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(addPriceTypeCodeList);
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(addChannelIdList);
		/*List<String> goodsNoList = getGoodsNoListbyAdjustCode(adjustPriceOrder.getAdjustCode());*/
        List<GoodsNoOrgIdsDTO> goodsNoOrgIdsList = copyCurrentGoodsNoOrgIds(adjustPriceOrder);
        List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList = getInsertAdjustPriceOrderDetailList(adjustPriceOrder, goodsNoOrgIdsList, priceChannelMap, priceTypeMap,
            addChannelIdList, addPriceTypeCodeList, userDTO, operateTime);
        Lists.partition(insertAdjustPriceOrderDetailList, 200)
            .forEach(toInsertGoodsNoList -> adjustPriceOrderDetailExMapper.batchInsert(toInsertGoodsNoList));
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
            	sendToSupplementDataMsgs(false,insertAdjustPriceOrderDetailList);
            }
        });
    }
    /**
     *
     * @Title: copyCurrentGoodsNoOrgIds
     * @Description: 填充新增新的渠道、新价格类型对应所有商品的调价单明细相关机构
     * @param: @param param
     * @param: @param adjustPriceOrder
     * @param: @return
     * @return: List<GoodsNoOrgIdsDTO>
     * @throws
     */
    private List<GoodsNoOrgIdsDTO> copyCurrentGoodsNoOrgIds(AdjustPriceOrder adjustPriceOrder){
    	List<GoodsNoOrgIdsDTO> goodsNoOrgIdsList = Lists.newArrayList();

    	List<AdjustPriceOrderDetail> orderDatailList = adjustPriceOrderDetailExMapper.listOrderDetailByGoodsNoOrgIds(adjustPriceOrder.getAdjustCode(),null,null,null);

		String goodsTabTypeValues = "";
		//门店组、门店标签
		if(PriceOrderTabTypeEnum.STORE_GROUP.getType() == adjustPriceOrder.getAdjustOrgTabType() ||
				PriceOrderTabTypeEnum.STORE_FLAG.getType() == adjustPriceOrder.getAdjustOrgTabType()) {
			PriceOrderTabTypeDetailExample tabTypeDetailExample = new PriceOrderTabTypeDetailExample();
    		tabTypeDetailExample.createCriteria().andAdjustOrderCodeEqualTo(adjustPriceOrder.getAdjustCode());
    		List<PriceOrderTabTypeDetail> tabTypeDetailList = priceOrderTabTypeDetailService.selectByExample(tabTypeDetailExample);
    		PriceOrderTabTypeDetail tabTypeDetail = tabTypeDetailList.get(0);
    		goodsTabTypeValues = tabTypeDetail.getTabTypeValue();
		}
    	GoodsNoOrgIdsDTO goodsNoOrgIds = null;
    	for (AdjustPriceOrderDetail orderDetail : orderDatailList) {
    		Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
        	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = detailExtend1Optional.get();

    		goodsNoOrgIds = new GoodsNoOrgIdsDTO();
    		goodsNoOrgIds.setAdjustCode(adjustPriceOrder.getAdjustCode());
    		goodsNoOrgIds.setGoodsNo(orderDetail.getGoodsNo());
    		goodsNoOrgIds.setItemSkuId(String.valueOf(orderDetail.getSkuId()));
    		goodsNoOrgIds.setOrgIds(orderDetail.getOrgIds());
    		goodsNoOrgIds.setOrgNames(orderDetail.getOrgNames());
    		goodsNoOrgIds.setOrgLevels(orderDetail.getOrgLevels());
    		goodsNoOrgIds.setGoodsOrgTabType(adjustPriceOrderDetailExtend1.getGoodsOrgTabType());
    		goodsNoOrgIds.setGoodsTabTypeValue(adjustPriceOrderDetailExtend1.getGoodsTabTypeValue());
    		goodsNoOrgIds.setOverrideLowerLevel(adjustPriceOrderDetailExtend1.getOverrideLowerLevel());
    		goodsNoOrgIds.setCopyOrderOrgTabType(true);
    		goodsNoOrgIdsList.add(goodsNoOrgIds);
		}
        return goodsNoOrgIdsList;
    }

    /**
     * 删除调价单号不在渠道Id列表的调价单明细
     * @param adjustCode
     * @param channelIdList
     */
    private void deleteByAdjustCodeAndChannelIds(String adjustCode, List<Integer> channelIdList) {
        AdjustPriceOrderDetailExample deleteNotInChannelIdsExample = new AdjustPriceOrderDetailExample();
        deleteNotInChannelIdsExample.createCriteria().andAdjustCodeEqualTo(adjustCode).andChannelIdIn(channelIdList);
        adjustPriceOrderDetailMapper.deleteByExample(deleteNotInChannelIdsExample);
    }

    /**
     * 删除调价单号不在渠道Id列表的调价单明细
     * @param adjustCode
     * @param priceTypeCodeList
     */
    private void deleteByAdjustPriceTypeCodes(String adjustCode, List<String> priceTypeCodeList) {
        AdjustPriceOrderDetailExample deleteNotInPriceTypeIdsExample = new AdjustPriceOrderDetailExample();
        deleteNotInPriceTypeIdsExample.createCriteria().andAdjustCodeEqualTo(adjustCode).andPriceTypeCodeIn(priceTypeCodeList);
        adjustPriceOrderDetailMapper.deleteByExample(deleteNotInPriceTypeIdsExample);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void nonPhysicalDeleteByAdjustCode(String adjustCode, TokenUserDTO userDTO, Date operateTime) {
        if (StringUtils.isEmpty(adjustCode) || userDTO == null || operateTime == null) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.nonPhysicalDeleteByAdjustCode]  参数都不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrderDetail adjustPriceOrderDetail = new AdjustPriceOrderDetail();
        adjustPriceOrderDetail.setStatus((byte)StatusEnum.DELETE.getCode());
        adjustPriceOrderDetail.setGmtUpdate(operateTime);
        adjustPriceOrderDetail.setUpdatedBy(userDTO.getUserId());
        adjustPriceOrderDetail.setUpdatedByName(userDTO.getUserName());
        AdjustPriceOrderDetailExample adjustPriceOrderDetailExample = new AdjustPriceOrderDetailExample();
        adjustPriceOrderDetailExample.createCriteria()
                .andAdjustCodeEqualTo(adjustCode);
        adjustPriceOrderDetailMapper.updateByExampleSelective(adjustPriceOrderDetail, adjustPriceOrderDetailExample);
    }

    @Override
    public List<String> getGoodsNoListbyAdjustCode(String adjustCode) {
        if (StringUtils.isEmpty(adjustCode)) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.getGoodsNoListbyAdjustCode]  参数都不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<String> hasAddGoodsNoList = adjustPriceOrderDetailExMapper.getGoodsNoListbyAdjustCode(adjustCode);
        if (hasAddGoodsNoList == null) {
            hasAddGoodsNoList = Collections.emptyList();
        }
        return hasAddGoodsNoList;
    }

    @SuppressWarnings("unchecked")
	@Override
    public ExportFileCubeVO<Map<String, Object>> exportAdjustPriceOrderDetailsFile(Long adjustPriceOrderId,String adjustPriceOrderCode,
    		Integer page, Integer pageSize,String goodsNo,Long userId,Long workcode,Boolean isPortal) {
        if (Objects.isNull(page) || Objects.isNull(pageSize)) {
            logger.info("分页参数为空,赋初始值");
            page = 1;
            pageSize = 200;
        }
        if (page < 1 || pageSize < 1) {
            logger.info("分页参数小于1,赋初始值");
            page = 1;
            pageSize = 200;
        }
        AdjustPriceOrder adjustPriceOrder = null;
        if(null!=adjustPriceOrderId) {
        	adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        }else{
        	adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(adjustPriceOrderCode);
        }
        boolean refFlag = StringUtils.isEmpty(adjustPriceOrderCode)?false:true;
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
        String fileName = basePriceOrderService.getAdjustPriceOrderDetailDownloadFileName(adjustPriceOrder.getAdjustCode());


        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null!=storeId){
            fieldMap = getSingleJoinAdjustPriceOrderDetailDownloadFieldMap(adjustPriceOrder);
        }else{
            fieldMap = getAdjustPriceOrderDetailDownloadFieldMap(adjustPriceOrder,refFlag,userId,workcode,isPortal);
        }
        AdjustPriceOrderDetailListV2Param param = new AdjustPriceOrderDetailListV2Param();
        param.setAdjustCode(adjustPriceOrder.getAdjustCode());
        param.setGoodsNo(goodsNo);
        param.setPage(page);
        param.setSize(pageSize);
        param.setOffset((page - 1) * pageSize);
        List<Map<String, Object>> adjustPriceOrderDetailListResults = Collections.emptyList();
        List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = Lists.newArrayList();
        List<AdjustPriceOrderDetailSku> goodsNoOrgIdsList = adjustPriceOrderDetailExMapper.selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType(param);
        if(CollectionUtils.isNotEmpty(goodsNoOrgIdsList)) {
        	adjustPriceOrderDetailList = adjustPriceOrderDetailExMapper.selectOrderDetailV2ListGroupByGoodsAndPriceType(
                    adjustPriceOrder.getAdjustCode(), goodsNoOrgIdsList);
        }

        if (CollectionUtils.isEmpty(adjustPriceOrderDetailList)) {
            return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_ADJUST_ORDER_DETAIL.getUploadUrl(),
                fieldMap, adjustPriceOrderDetailListResults);
        }
        adjustPriceOrderDetailListResults = getAdjustPriceOrderDetailListResults(null,adjustPriceOrder,adjustPriceOrderDetailList, adjustPriceOrder.getAdjustPriceType(), false,false,true);
        if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
        	return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_ADJUST_ORDER_DETAIL.getUploadUrl(),
        	           fieldMap, adjustPriceOrderDetailListResults);
        }
        CountDownLatch countDownLatch = new CountDownLatch(adjustPriceOrderDetailListResults.size());
		for (Map<String, Object> item : adjustPriceOrderDetailListResults) {
			adjustOrderDetailExportThreadExecutor.execute(() -> {
	    		try {
	    			fillOrgIdsSapCode(item);
				} catch (Exception e) {
					logger.error("AdjustPriceOrderDetailV2ServiceImpl|exportAdjustPriceOrderDetailsFile|fillOrgIdsSapCode|定调价商品明细导出异常",e);
				} finally {
	                countDownLatch.countDown();
	            }
	    	});
		}
    	try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("AdjustPriceOrderDetailV2ServiceImpl|exportAdjustPriceOrderDetailsFile|fillOrgIdsSapCode|定调价商品明细导出异常, adjustCode:{}", adjustPriceOrder.getAdjustCode(),e);
        }
		try {
			List<String> adjustPriceTypeList = Arrays.asList(adjustPriceOrder.getAdjustPriceType().split(","));
			if(adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode())) {
				suppWisdomPrice(adjustPriceOrderDetailListResults, adjustPriceOrder);
			}
		} catch (Exception e) {
			logger.error("AdjustPriceOrderDetailV2ServiceImpl|suppBusinessHistoryPrice|定调价商品明细补充智慧建议价格异常",e);
		}

        return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_ADJUST_ORDER_DETAIL.getUploadUrl(),
           fieldMap, adjustPriceOrderDetailListResults);
    }

    /**
    *
    * @Title: fillOrgIdsSapCode
    * @Description: 连锁级别以上orgId 返回连锁sapCode，连锁级别一下orgId 返回门店sapCode
    * @param: @param adjustPriceOrderDetailListResults
    * @return: void
    * @throws
    */
    @Override
    public void fillOrgIdsSapCode(Map<String, Object> item) {
	    Set<String> sapCodeSet = Sets.newConcurrentHashSet();
  		Set<String> sapNameSet = Sets.newConcurrentHashSet();
  		String orgIds = "";
  		String orgLevels = "";
  		String itemOrgIdSapcodeKey = "";
  		String itemOrgIdNameKey = "";
  		//门店组、门店标签 orgIds直接返回id
  		String goodsOrgTabType = item.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_14.getName()).toString();
  		if(Integer.valueOf(goodsOrgTabType)!=PriceOrderTabTypeEnum.STORE_LIST.getType()) {
  			String extent1 = item.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_23.getName()).toString();
  			Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(extent1);
          	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = detailExtend1Optional.get();
            List<String> goodsTabTypeValueList = Arrays.asList(adjustPriceOrderDetailExtend1.getGoodsTabTypeValue().split(",")).stream().map(s -> String.valueOf(s.trim())).collect(Collectors.toList());
            sapCodeSet.addAll(goodsTabTypeValueList);
          	sapNameSet.addAll(goodsTabTypeValueList);
  		}else {
  			orgIds = item.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName()).toString();
          	orgLevels =item.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_17.getName()).toString();
          	itemOrgIdSapcodeKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ITEM_ORGID_SAPCODE_KEY+Md5Utils.MD5Encode(orgIds);
          	itemOrgIdNameKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ITEM_ORGID_SHORTNAME_KEY+Md5Utils.MD5Encode(orgIds);
          	RBucket<String> resSapCodes = redissonClient.getBucket(itemOrgIdSapcodeKey);
          	RBucket<String> resNames = redissonClient.getBucket(itemOrgIdNameKey);
          	if(resSapCodes.isExists()) {
          		item.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName(), resSapCodes.get());
          		item.put("orgNames", resNames.isExists()?resNames.get():"");
          		return;
          	}
      		List<Long> orgIdList = Arrays.asList(orgIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
      		List<Integer> orgLevelList = Arrays.asList(orgLevels.split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
      		List<Long> paramOrgIdList = Lists.newArrayList();
      		Map<Integer,List<Long>> orgLevelMap = Maps.newHashMap();
      		orgLevelMap.put(OrgLevelTypeEnum.BUSINESS.getCode(), Lists.newArrayList());
      		orgLevelMap.put(OrgLevelTypeEnum.STORE.getCode(), Lists.newArrayList());
      		for (int i = 0; i < orgLevelList.size(); i++) {
      			if(orgLevelList.get(i)==OrgLevelTypeEnum.ORG.getCode() ||
      					orgLevelList.get(i)==OrgLevelTypeEnum.PLATFORM.getCode()) {
      				orgLevelMap.get(OrgLevelTypeEnum.BUSINESS.getCode()).add(orgIdList.get(i));
      			}else if(orgLevelList.get(i)==OrgLevelTypeEnum.BUSINESS_REGIONAL.getCode()) {
      				orgLevelMap.get(OrgLevelTypeEnum.STORE.getCode()).add(orgIdList.get(i));
      			}else if(orgLevelList.get(i)==OrgLevelTypeEnum.STORE.getCode() ||
      					orgLevelList.get(i)==OrgLevelTypeEnum.BUSINESS.getCode()) {
      				paramOrgIdList.add(orgIdList.get(i));
      			}
  			}
      		if(CollectionUtils.isNotEmpty(paramOrgIdList)) {
      			OrgToRedisDTO orgToRedisDTO = null;
      			for (Long orgId : paramOrgIdList) {
      				if(CacheVar.storeOrgIdAndOutIdMapping.containsKey(orgId)) {
      					orgToRedisDTO = CacheVar.storeCacheMap.get(CacheVar.storeOrgIdAndOutIdMapping.get(orgId));
      					if(StringUtils.isNotBlank(orgToRedisDTO.getSapCode())) {
      						sapCodeSet.add(orgToRedisDTO.getSapCode());
      					}
      					if(StringUtils.isNotBlank(orgToRedisDTO.getShortName())) {
      						sapNameSet.add(orgToRedisDTO.getShortName());
      					}
      					continue;
      				}
      				if(CacheVar.businessOrgIdAndOutIdMapping.containsKey(orgId)) {
      					orgToRedisDTO = CacheVar.businessCacheMap.get(CacheVar.businessOrgIdAndOutIdMapping.get(orgId));
      					if(StringUtils.isNotBlank(orgToRedisDTO.getSapCode())) {
      						sapCodeSet.add(orgToRedisDTO.getSapCode());
      					}
      					if(StringUtils.isNotBlank(orgToRedisDTO.getShortName())) {
      						sapNameSet.add(orgToRedisDTO.getShortName());
      					}
      				}
    			}
      		}

      		orgLevelMap.forEach((orgLevelCode,groupOrgIdList) -> {
      			if(CollectionUtils.isEmpty(groupOrgIdList)) {
      				return;
      			}
      			String[] prefixOrgIdArray = groupOrgIdList.stream().map(orgId -> String.format(RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ORGID_SAPCODES_KEY, orgId)).toArray(String[]::new);
      			RBuckets buckets = redissonClient.getBuckets();
      			Map<String,List<ShortNameVO>> redisMap = buckets.get(prefixOrgIdArray);
      			List<Long> redisOrgIdKeyList = Lists.newArrayList();
      			redisMap.forEach((key,orgLevelVOList) -> {
      				redisOrgIdKeyList.add(Long.valueOf(key.substring(key.lastIndexOf("_")+1)));
  					if(CollectionUtils.isNotEmpty(orgLevelVOList)) {
  						orgLevelVOList.forEach(orgLevel -> {
      						if(StringUtils.isNotBlank(orgLevel.getSapcode())) {
  								sapCodeSet.add(orgLevel.getSapcode());
  							}
  							if(StringUtils.isNotBlank(orgLevel.getShortName())) {
  								sapNameSet.add(orgLevel.getShortName());
  							}
          				});
          			}
      			});
      			List<Long> diffOrgIdList = groupOrgIdList.stream().filter(x -> !redisOrgIdKeyList.contains(x)).collect(Collectors.toList());
      			if(CollectionUtils.isNotEmpty(diffOrgIdList)) {
      				List<ChildOrgsDTO> childOrgsList = permissionExtService.listChildOrgAssignedType(diffOrgIdList, OrgLevelTypeEnum.getByCode(orgLevelCode).get().getType());
      				if(CollectionUtils.isNotEmpty(childOrgsList)) {
          				childOrgsList.forEach(chileOrg -> {
          					if(CollectionUtils.isNotEmpty(chileOrg.getChildren())) {
          						List<ShortNameVO> listVo = Lists.newArrayList();
          						chileOrg.getChildren().forEach(orgLevel -> {
          							ShortNameVO shortNameVO = new ShortNameVO();
          							BeanUtils.copyProperties(orgLevel, shortNameVO);
          							listVo.add(shortNameVO);
          							if(StringUtils.isNotBlank(shortNameVO.getSapcode())) {
          								sapCodeSet.add(shortNameVO.getSapcode());
          							}
          							if(StringUtils.isNotBlank(shortNameVO.getShortName())) {
          								sapNameSet.add(shortNameVO.getShortName());
          							}
          						});
          						String key = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ORGID_SAPCODES_KEY+chileOrg.getId();
              					redissonClient.getBucket(key).set(listVo,10,TimeUnit.DAYS);
              					listVo.clear();
          					}
          				});
          				childOrgsList.clear();
          			}
      			}
      			diffOrgIdList.clear();
      		});
      		paramOrgIdList.clear();
      		orgLevelList.clear();
      		orgLevelMap.clear();
  		}
  		if(CollectionUtils.isNotEmpty(sapCodeSet)) {
  			item.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName(), Joiner.on(";").join(sapCodeSet));
  			if(!redissonClient.getBucket(itemOrgIdSapcodeKey).isExists()) {
  				redissonClient.getBucket(itemOrgIdSapcodeKey).set(Joiner.on(";").join(sapCodeSet),5,TimeUnit.MINUTES);
  			}
  		}else {
  			item.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName(), "");
  		}
  		if(CollectionUtils.isNotEmpty(sapNameSet)) {
  			item.put("orgNames", Joiner.on(";").join(sapNameSet));
  			if(!redissonClient.getBucket(itemOrgIdNameKey).isExists()) {
  				redissonClient.getBucket(itemOrgIdNameKey).set(Joiner.on(";").join(sapNameSet),5,TimeUnit.MINUTES);
  			}
  		}else {
  			item.put("orgNames", "");
  		}
  		sapCodeSet.clear();
  		sapNameSet.clear();
   }


    @Override
    public Optional<AdjustPriceOrderDetail> getSingleAdjustPriceOrderDetail(String adjustCode, String goodsNo, String priceTypeCode,List<Long> detaildList) {
        Optional<AdjustPriceOrderDetail> result = Optional.empty();
        if (adjustCode != null && goodsNo != null && priceTypeCode != null) {
            AdjustPriceOrderDetailListV2Param param = new AdjustPriceOrderDetailListV2Param();
            param.setOffset(0);
            param.setSize(1);
            param.setAdjustCode(adjustCode);
            param.setGoodsNo(goodsNo);
            param.setPriceTypeCodeList(Lists.newArrayList(priceTypeCode));
            if(CollectionUtils.isNotEmpty(detaildList)) {
            	param.setDetaildList(detaildList);
            }
            List<AdjustPriceOrderDetail> adjustPriceOrderDetails = adjustPriceOrderDetailExMapper.selectOrderDetailV2ListPageGroupByGoodAndPriceType(param);
            if (!CollectionUtils.isEmpty(adjustPriceOrderDetails)) {
                result = Optional.of(adjustPriceOrderDetails.get(0));
            }
        }
        return result;
    }

    /**
     * 调价单的一个商品，一种价格类型更新多条数据
     * @param userDTO
     * @param now
     * @param adjustPriceOrder
     * @param rowDiff
     * @param goodsNo
     * @param goodsType
     * @param activityType
     * @param reason
     * @param priceTypeCode
     */
    private void updateAdjustPriceOrderDetailByPriceCode(TokenUserDTO userDTO, Date now, AdjustPriceOrder adjustPriceOrder, Map<String, String> rowDiff, String goodsNo, String goodsType,
        String activityType, String reason, String priceTypeCode, String adjustDetailMergeIds, String oaUserId) {
    	List<Long> detailIdList = Arrays.asList(adjustDetailMergeIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        Optional<AdjustPriceOrderDetail> adjustPriceOrderDetailOptional = getSingleAdjustPriceOrderDetail(adjustPriceOrder.getAdjustCode(),
            goodsNo, priceTypeCode,detailIdList);
        if (!adjustPriceOrderDetailOptional.isPresent()) {
            logger.error("<===[AdjustPriceOrderDetailV2ServiceImpl.updateAdjustPriceOrderDetailByPriceCode] AdjustCode:{}, goodsNo: {}, priceTypeCode: {}明细数据不存在", adjustPriceOrder.getAdjustCode(),
                goodsNo, priceTypeCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrderDetail adjustPriceOrderDetail = adjustPriceOrderDetailOptional.get();
        AdjustPriceOrderDetailEditV2 adjustPriceOrderDetailEditV2 = new AdjustPriceOrderDetailEditV2();
        adjustPriceOrderDetailEditV2.setAdjustCode(adjustPriceOrder.getAdjustCode());
        adjustPriceOrderDetailEditV2.setGoodsNo(goodsNo);
        adjustPriceOrderDetailEditV2.setSkuId(adjustPriceOrderDetail.getSkuId());
        adjustPriceOrderDetailEditV2.setPriceTypeCode(priceTypeCode);
        adjustPriceOrderDetailEditV2.setOriginalPrice(adjustPriceOrderDetail.getOriginalPrice());
        adjustPriceOrderDetailEditV2.setAdjustDetailMergeIds(detailIdList);
        Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
        detailExtend1Optional.ifPresent(detailExtend1 -> adjustPriceOrderDetailEditV2.setExtend1(detailExtend1));
        if (goodsType != null) {
            adjustPriceOrderDetailEditV2.setGoodsType(goodsType);
        }
        if (activityType != null) {
            adjustPriceOrderDetailEditV2.setActivityType(activityType);
        }
        if (reason != null) {
        	if(reason.length()>50) {
        		throw new AmisBadRequestException("调价原因不要超过50个字");
        	}
            adjustPriceOrderDetailEditV2.setReason(reason);
        }
        adjustPriceOrderDetailEditV2.setGmtUpdate(now);
        adjustPriceOrderDetailEditV2.setUpdatedBy(userDTO.getUserId());
        adjustPriceOrderDetailEditV2.setUpdatedByName(userDTO.getName());
        String priceKey = null;
        if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
        	priceKey = new StringBuilder().append(AdjustPriceOrderDetailB2CPriceTypeColumnEnum.COLUMN_1.getName()).append("_").append(priceTypeCode).toString();
        }else {
        	priceKey = new StringBuilder().append(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_2.getName()).append("_").append(priceTypeCode).toString();
        }
        String rebateKey = new StringBuilder().append(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_10.getName()).append("_").append(priceTypeCode).toString();
        AdjustPriceOrderDetailExtend1 extend1 = adjustPriceOrderDetailEditV2.getExtend1();
        for (Map.Entry<String, String> rowDiffEntry : rowDiff.entrySet()) {
            Optional<AdjustPriceOrderDetailBaseColumnEnum> baseColumnEnumOptional = AdjustPriceOrderDetailBaseColumnEnum.getByName(rowDiffEntry.getKey());
            Optional<AdjustPriceOrderDetailPriceTypeColumnEnum> goodPriceColumnEnumOptional = null;
            Optional<AdjustPriceOrderDetailB2CPriceTypeColumnEnum> goodB2cPriceColumnEnumOptional = null;
            if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
            	goodB2cPriceColumnEnumOptional = AdjustPriceOrderDetailB2CPriceTypeColumnEnum.getByName(rowDiffEntry.getKey());
            }else {
            	goodPriceColumnEnumOptional = AdjustPriceOrderDetailPriceTypeColumnEnum.getByName(rowDiffEntry.getKey());
            }
            Optional<AdjustPriceOrderDetailLastColumnEnum> lastColumnEnumOptional = AdjustPriceOrderDetailLastColumnEnum.getByName(rowDiffEntry.getKey());
            Optional<AdjustPriceOrderDetailAuditColumnEnum> auditColumnEnumOptional = AdjustPriceOrderDetailAuditColumnEnum.getByName(rowDiffEntry.getKey());
            if (baseColumnEnumOptional.isPresent()) {
                baseColumnEnumOptional.get().setAttrValue().accept(adjustPriceOrderDetailEditV2, rowDiffEntry.getValue());
            } else if (getB2CChannelIdList().contains(adjustPriceOrder.getChannel()) && goodB2cPriceColumnEnumOptional.isPresent() && rowDiffEntry.getKey().equals(priceKey)){
            	goodB2cPriceColumnEnumOptional.get().setAttrValue().accept(adjustPriceOrderDetailEditV2, rowDiffEntry.getValue());
            } else if (!getB2CChannelIdList().contains(adjustPriceOrder.getChannel()) && goodPriceColumnEnumOptional.isPresent() && rowDiffEntry.getKey().equals(priceKey)){
                goodPriceColumnEnumOptional.get().setAttrValue().accept(adjustPriceOrderDetailEditV2, rowDiffEntry.getValue());
            } else if (lastColumnEnumOptional.isPresent()) {
                lastColumnEnumOptional.get().setAttrValue().accept(adjustPriceOrderDetailEditV2, rowDiffEntry.getValue());
            } else if (auditColumnEnumOptional.isPresent()) {
            	auditColumnEnumOptional.get().setAttrValue().accept(adjustPriceOrderDetailEditV2, rowDiffEntry.getValue());
            } else if (!getB2CChannelIdList().contains(adjustPriceOrder.getChannel()) && goodPriceColumnEnumOptional.isPresent() && rowDiffEntry.getKey().equals(rebateKey)){
                extend1.setRebate(BigDecimalUtils.stringToDecimal(rowDiffEntry.getValue()));
            }
        }
        boolean isNeedSpecialAudit = isNeedSpecialAudit(adjustPriceOrderDetailEditV2.getPrice(), adjustPriceOrderDetail.getLowerLimit(),
            adjustPriceOrderDetail.getUpperLimit());
        boolean isDataFull = adjustPriceOrderDetail.getPrice() != null || adjustPriceOrderDetailEditV2.getPrice() != null;

        if (adjustPriceOrderDetailEditV2.getExtend1() != null) {
            if (isDataFull) {
                extend1.setDataFull(DataFullEnum.HAS_FULL.getCode());
            }
            extend1.setOverrideLowerLevel(rowDiff.get("overrideLowerLevel")==null?OverrideLowerLevelEnum.YES.getCode() :Integer.valueOf(rowDiff.get("overrideLowerLevel")));
            extend1.setNeedSpecialAudit(isNeedSpecialAudit ? AdjustPriceOrderDetailNeedSpecialAuditEnum.NEED.getCode()
                : AdjustPriceOrderDetailNeedSpecialAuditEnum.NOT_NEED.getCode());
            if(StringUtils.isNotBlank(rowDiff.get("detailAuditStatus"))) {
            	Boolean detailAuditStatus = Boolean.valueOf(rowDiff.get("detailAuditStatus"));
            	if(detailAuditStatus || DetailAuditStatusEnum.AUDIT_PASS.getMessage().equals(rowDiff.get("detailAuditStatus"))) {
            		extend1.setDetailAuditStatus(DetailAuditStatusEnum.AUDIT_PASS.getCode());
            	}else {
            		extend1.setDetailAuditStatus(DetailAuditStatusEnum.PRE_REFUSE.getCode());
            	}
            }
            extend1.setDetailAuditMessage(rowDiff.get("detailAuditMessage"));
            if(StringUtils.isNotBlank(rowDiff.get("imageUrl"))) {
            	extend1.setImageUrl(rowDiff.get("imageUrl"));
            }
            adjustPriceOrderDetailEditV2.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(extend1));
        }
        adjustPriceOrderDetailExMapper.updateByAdjustCode(adjustPriceOrderDetailEditV2);
        //发送消息
    	AdjustPriceOrderDetailSupplementDataVO data = new AdjustPriceOrderDetailSupplementDataVO();
    	data.setUserId(adjustPriceOrder.getCreatedBy());
    	data.setAdjustCode(adjustPriceOrderDetailEditV2.getAdjustCode());
    	data.setAdjustDetailMergeIds(StringUtils.join(adjustPriceOrderDetailEditV2.getAdjustDetailMergeIds(), ","));
    	data.setGoodsNo(adjustPriceOrderDetailEditV2.getGoodsNo());
    	data.setSkuId(String.valueOf(adjustPriceOrderDetailEditV2.getSkuId()));
    	data.setPriceTypeCode(adjustPriceOrderDetailEditV2.getPriceTypeCode());
    	TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
            	limitStatusProducer.sendMq(data);
            }
        });
    }


    /**
     * 调价单明细基础列或者最后列被修改
     * @param goodsType
     * @param activityType
     * @param reason
     * @param priceFlag
     * @return
     */
    private boolean isBaseOrLastColumnsChange(String goodsType, String activityType, String reason, String priceFlag) {
        return goodsType != null || activityType != null || reason != null || priceFlag != null;
    }

    /**
     * 单体加盟导出字段
     * @param adjustPriceOrder
     * @return
     */
    private LinkedHashMap<String, String> getSingleJoinAdjustPriceOrderDetailDownloadFieldMap(AdjustPriceOrder adjustPriceOrder) {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        fieldMap.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getName(), AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getLabel());
        List<PriceType> sortedPriceTypeList = basePriceOrderService.getSortedPriceTypeList(adjustPriceOrder.getAdjustPriceType());
        for (PriceType priceType : sortedPriceTypeList) {
            fieldMap.put(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_2.getNameByPriceTypeCode(priceType.getCode()),
                AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_2.getLabelByPriceTypeName(priceType.getName()));
        }
        return fieldMap;
    }

    /**
     * 获取下载需要对象
     * @param adjustPriceOrder
     * @param refFlag
     * @param userId
     * @param workcode
     * @param isPortal
     * @return
     */
    private LinkedHashMap<String, String> getAdjustPriceOrderDetailDownloadFieldMap(AdjustPriceOrder adjustPriceOrder,boolean refFlag,Long userId,Long workcode,Boolean isPortal) {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        for (AdjustPriceOrderDetailBaseColumnEnum commonColumnEnum : AdjustPriceOrderDetailBaseColumnEnum.values()) {
            fieldMap.put(commonColumnEnum.getName(), commonColumnEnum.getLabel());
        }
        if(!getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
        	if(refFlag) {
            	for (AdjustPriceOrderDetailRefPriceColumnEnum refPriceColumnEnum : AdjustPriceOrderDetailRefPriceColumnEnum.values()) {
                    fieldMap.put(refPriceColumnEnum.getName(), refPriceColumnEnum.getLabel());
                }
            }
    	}
        List<PriceType> sortedPriceTypeList = basePriceOrderService.getSortedPriceTypeList(adjustPriceOrder.getAdjustPriceType());

        if(!getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
        	for (PriceType priceType : sortedPriceTypeList) {
                for (AdjustPriceOrderDetailPriceTypeColumnEnum priceColumnEnum : AdjustPriceOrderDetailPriceTypeColumnEnum.values()) {
                    fieldMap.put(priceColumnEnum.getNameByPriceTypeCode(priceType.getCode()),
                        priceColumnEnum.getLabelByPriceTypeName(priceType.getName()));
                }
            }
    	}else {
    		for (PriceType priceType : sortedPriceTypeList) {
                for (AdjustPriceOrderDetailB2CPriceTypeColumnEnum priceColumnEnum : AdjustPriceOrderDetailB2CPriceTypeColumnEnum.values()) {
                    fieldMap.put(priceColumnEnum.getNameByPriceTypeCode(priceType.getCode()),
                        priceColumnEnum.getLabelByPriceTypeName(priceType.getName()));
                }
            }
    	}

        for (AdjustPriceOrderDetailLastColumnEnum lastColumnEnum : AdjustPriceOrderDetailLastColumnEnum.values()) {
            fieldMap.put(lastColumnEnum.getName(), lastColumnEnum.getLabel());
        }
        List<String> removeFieldList = Lists.newArrayList("orgLevels","orgNames","orgCount","lastPurchasingPrices","suggestPrices",
        		"extend1","gmtCreate","beforeGoodsTypeName","beforeActivityName","suggestPrice");

        if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_9.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_10.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_11.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_14.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_16.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_18.getName());
    	}else {
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_19.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_26.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_27.getName());
    	}
        if(isPortal) {
        	removeFieldList.addAll(removePortalFieldList(adjustPriceOrder));
        }else {
        	removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_29.getName());
        	removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_30.getName());
        }
        removeFieldList.addAll(removeSingleStoreFieldList(adjustPriceOrder));
        List<String> adjustPriceTypeList = Arrays.asList(adjustPriceOrder.getAdjustPriceType().split(","));
    	if(adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode())) {
    		for (String priceType : adjustPriceTypeList) {
    			if(PriceTypeModeEnum.LSJ.getPriceTypeCode().equals(priceType)) {
    				continue;
    			}
    			removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_9.getName()+"_"+priceType);
			}
    	}else {
    		for (String priceType : adjustPriceTypeList) {
    			removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_9.getName()+"_"+priceType);
			}
    	}

        //验证调价的价格类型是否同时存在零售价和会员价
        if(adjustPriceOrder.getChannel().equals(Constants.DEFAULT_CHANNEL)  &&
            adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode()) &&
            adjustPriceTypeList.contains(PriceTypeModeEnum.HYJ.getPriceTypeCode())){
            for (String priceType : adjustPriceTypeList) {
                if(PriceTypeModeEnum.HYJ.getPriceTypeCode().equals(priceType)) {
                    continue;
                }
                removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_10.getName()+"_"+priceType);
            }
        }else{
            for (String priceType : adjustPriceTypeList) {
                removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_10.getName()+"_"+priceType);
            }
        }

        if(null!=userId) {
        	List<UserRoleRelateDTO> userRoles = permissionExtService.getUserRoles(userId);
        	if(CollectionUtils.isNotEmpty(userRoles)){
        		Set<String> roleSet = userRoles.stream().map(UserRoleRelateDTO::getRoleCode).collect(Collectors.toSet());
        		TokenUserDTO userDTO = new TokenUserDTO();
                userDTO.setUserId(userId);
                userDTO.setRoles(roleSet);
                jmdRolesCheck(userDTO, null, removeFieldList);
        	}
        }else if(null!=workcode) {
        	jmdRolesCheck(null, workcode, removeFieldList);
        }
        Iterator<String> iterator = fieldMap.keySet().iterator();
        while(iterator.hasNext()) {
        	String fieldName = iterator.next();
        	if(removeFieldList.contains(fieldName)) {
        		iterator.remove();
        	}
        }
        return fieldMap;
    }

    /**
     * 根据调价单明细和组织ID列表获取最小指导价，最大的最低限价，最小的最高限价，
     * 假如最大的最低限价大于最小的最高限价，则取最高限价那一条管控单明细的指导价，最低限价，最高限价
     *
     * @param supplementDataVO
     * @param orgLevelVOList
     * @return
     */
    private AdjustPriceOrderManagePriceDTO getAdjustPriceOrderManagePriceDTO(AdjustPriceOrder adjustPriceOrder, AdjustPriceOrderDetailSupplementDataVO supplementDataVO, List<Integer> channelIdList, List<OrgLevelVO> orgLevelVOList) {
    	List<Long> orgIdList = orgLevelVOList.stream().map(OrgLevelVO::getOrgId).collect(Collectors.toList());
        Map<Long, List<OrgDTO>> orgDtoMap = permissionExtService.getAncestorsOrgList(orgIdList);
        List<CompletableFuture<Optional<AdjustPriceOrderManagePriceDTO>>> priceDTOFutures = new ArrayList<>(orgIdList.size() * channelIdList.size());
        for (Integer channelId : channelIdList) {
            for (Long orgId : orgIdList) {
                List<OrgDTO> ancestorOrgList = orgDtoMap.get(orgId);
                priceDTOFutures.add(getOneOrgAdjustPriceOrderManagePriceDTO(adjustPriceOrder, supplementDataVO.getGoodsNo(), ancestorOrgList,
                    supplementDataVO.getPriceTypeCode(), channelId));
            }
        }
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(priceDTOFutures.toArray(new CompletableFuture[priceDTOFutures.size()]));
        CompletableFuture<List<Optional<AdjustPriceOrderManagePriceDTO>>> allPriceDTOFutures = allFutures.thenApply(v ->
            priceDTOFutures.stream().map(priceDTOFuture -> priceDTOFuture.join()).collect(Collectors.toList()));
        BigDecimal minGuidePrice = PriceConstant.DEFAULT_LESS_ZERO;
        BigDecimal maxLowerLimit = PriceConstant.DEFAULT_LESS_ZERO;
        BigDecimal minUpperLimit = PriceConstant.DEFAULT_LESS_ZERO;
        try {
            List<Optional<AdjustPriceOrderManagePriceDTO>> priceDtoOptionalList = allPriceDTOFutures.get(10, TimeUnit.MINUTES);
            List<AdjustPriceOrderManagePriceDTO> managePriceDTOS = priceDtoOptionalList.stream()
                .filter(priceDtoOptional -> priceDtoOptional.isPresent())
                .map(priceDtoOptional -> priceDtoOptional.get()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(managePriceDTOS)) {
                minGuidePrice = managePriceDTOS.stream().map(AdjustPriceOrderManagePriceDTO::getGuidePrice).min((x1, x2) -> x1.compareTo(x2)).get();
                maxLowerLimit = managePriceDTOS.stream().map(AdjustPriceOrderManagePriceDTO::getLowerLimit).max((x1, x2) -> x1.compareTo(x2)).get();
                minUpperLimit = managePriceDTOS.stream().map(AdjustPriceOrderManagePriceDTO::getUpperLimit).min((x1, x2) -> x1.compareTo(x2)).get();
                // 假如最大的最低限价大于最小的最高限价，则取最高限价那一条管控单明细的指导价，最低限价，最高限价
                if (maxLowerLimit.compareTo(minUpperLimit) >= 0) {
                    AdjustPriceOrderManagePriceDTO adjustPriceOrderManagePriceDTO = managePriceDTOS.stream().max((x1, x2) -> x1.getUpperLimit().compareTo(x2.getUpperLimit())).get();
                    minGuidePrice = adjustPriceOrderManagePriceDTO.getGuidePrice();
                    maxLowerLimit = adjustPriceOrderManagePriceDTO.getLowerLimit();
                    minUpperLimit = adjustPriceOrderManagePriceDTO.getUpperLimit();
                }
            }
        } catch (InterruptedException e) {
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, e);
        } catch (ExecutionException e) {
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, e);
        } catch (TimeoutException e) {
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, e);
        }
        return new AdjustPriceOrderManagePriceDTO(minGuidePrice, maxLowerLimit, minUpperLimit);
    }

    /**
     * 找自己最近（包括自己）管控单明细信息
     * @param goodsNo
     * @param orgDTOList
     * @param priceTypeCode
     * @param channelId
     * @return
     */
    private CompletableFuture<Optional<AdjustPriceOrderManagePriceDTO>> getOneOrgAdjustPriceOrderManagePriceDTO(AdjustPriceOrder adjustPriceOrder, String goodsNo, List<OrgDTO> orgDTOList,
        String priceTypeCode, Integer channelId) {
        Date effectTime = adjustPriceOrder.getEffectTime();

        return CompletableFuture.supplyAsync(() -> {
            List<Long> selfAndParentsOrgIdList = orgDTOList.stream().filter(v -> OrgLevelTypeEnum.getByType(v.getType()).isPresent()).map(OrgDTO::getId).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(selfAndParentsOrgIdList)){
                Optional<AdjustPriceOrderManagePriceDTO> priceDTO = priceManageControlOrderDetailReadService.getNewestAdjustPriceOrderManagePriceDTO(goodsNo, selfAndParentsOrgIdList,
                        priceTypeCode, channelId, effectTime);
                if (priceDTO.isPresent()) {
                    return priceDTO;
                }
            }
            return Optional.empty();
        }, getPriceManageControlOrderPricesMaxThreadExecutor);
    }


    /**
     * 校验添加商品列表参数是否有效
     * @param param
     */
    private void checkAdjustPriceOrderDetailAddV2Param(AdjustPriceOrderDetailAddV2Param param) {
        if (param.getAdjustPriceOrderId() == null || CollectionUtils.isEmpty(param.getGoodsNoList())
        )  {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
    }


    /**
     * 循环渠道，价格类型，商品获取调价明细列表
     * @param adjustPriceOrder
     * @param goodsNoOrgIdsList
     * @param userDTO
     * @param operateTime
     * @return
     */
    private List<AdjustPriceOrderDetail> getInsertAdjustPriceOrderDetailList(AdjustPriceOrder adjustPriceOrder, List<GoodsNoOrgIdsDTO> goodsNoOrgIdsList,
        Map<Integer, PriceChannel> priceChannelMap, Map<String, PriceType> priceTypeMap, List<Integer> channelIdList, List<String> priceTypeCodeList, TokenUserDTO userDTO, Date operateTime) {
        int size = channelIdList.size() * priceTypeCodeList.size() * goodsNoOrgIdsList.size();
        logger.info("<===[AdjustPriceOrderV2ServiceImpl.getInsertAdjustPriceOrderDetailList]  导入商品数量：: {}", size);
        List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = Collections.emptyList();
        if (size > 0) {
            adjustPriceOrderDetailList = new ArrayList<>(size);
            long [] adjustDetailIds = tocExtService.getDistributedIDList(DistributedIDTypeEnum.ADJUST_PRICE_ORDER_DETAIL.getBiz(), size);
            int idIndex = 0;
            for (Integer channelId : channelIdList) {
                PriceChannel priceChannel = priceChannelMap.get(channelId);
                if (priceChannel == null) {
                    logger.error("<===[AdjustPriceOrderV2ServiceImpl.getInsertAdjustPriceOrderDetailList]  channelId: {} 不存在", channelId);
                    throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR);
                }
                for (String priceTypeCode : priceTypeCodeList) {
                    PriceType priceType = priceTypeMap.get(priceTypeCode);
                    if (priceType == null) {
                        logger.error("<===[AdjustPriceOrderV2ServiceImpl.getInsertAdjustPriceOrderDetailList]  priceTypeCode: {} 不存在", priceTypeCode);
                        throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR);
                    }
                    for (GoodsNoOrgIdsDTO goodsNoOrgsBean : goodsNoOrgIdsList) {
                        AdjustPriceOrderDetail adjustPriceOrderDetail = new AdjustPriceOrderDetail();
                        if(!getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
                        	if(!goodsNoOrgsBean.isCopyOrderOrgTabType()) {
                            	fillGoodsNoOrgIdsDTO(goodsNoOrgsBean,userDTO);
                            }
                        }
                        if (null!=goodsNoOrgsBean.getPrice()){
                            adjustPriceOrderDetail.setPrice(goodsNoOrgsBean.getPrice());
                        }
                        adjustPriceOrderDetail.setReason(goodsNoOrgsBean.getAdjustDetailReason());
                        adjustPriceOrderDetail.setOrgIds(goodsNoOrgsBean.getOrgIds());
                    	adjustPriceOrderDetail.setOrgNames(goodsNoOrgsBean.getOrgNames());
                    	adjustPriceOrderDetail.setOrgLevels(goodsNoOrgsBean.getOrgLevels());

                        adjustPriceOrderDetail.setAuthOrgId(adjustPriceOrder.getOrgId());
                        adjustPriceOrderDetail.setAuthOrgName(adjustPriceOrder.getOrgName());
                        adjustPriceOrderDetail.setAdjustCode(adjustPriceOrder.getAdjustCode());
                        adjustPriceOrderDetail.setAdjustDetailId(String.valueOf(adjustDetailIds[idIndex++]));
                        adjustPriceOrderDetail.setChannelId(channelId);
                        adjustPriceOrderDetail.setChannelEnCode(priceChannel.getChannelEnCode());
                        adjustPriceOrderDetail.setChannelOutCode(priceChannel.getOutChannelCode());
                        adjustPriceOrderDetail.setPriceTypeCode(priceTypeCode);
                        adjustPriceOrderDetail.setPriceTypeId(priceType.getId());
                        adjustPriceOrderDetail.setPriceTypeName(priceType.getName());
                        adjustPriceOrderDetail.setPriceFlag(PriceFlagEnum.NO.getCode());
                        adjustPriceOrderDetail.setGoodsNo(goodsNoOrgsBean.getGoodsNo());
                        //默认存储组织
                        adjustPriceOrderDetail.setAdjustType((byte)TargetTypeEnum.ORG.getCode());
                        // SpuId不能为空，先设置成0L
                        adjustPriceOrderDetail.setSpuId(0L);
                        adjustPriceOrderDetail.setGmtCreate(operateTime);
                        adjustPriceOrderDetail.setCreatedBy(userDTO.getUserId());
                        adjustPriceOrderDetail.setUpdatedBy(userDTO.getUserId());
                        adjustPriceOrderDetail.setUpdatedByName(userDTO.getUserName());
                        adjustPriceOrderDetail.setGmtUpdate(operateTime);
                        adjustPriceOrderDetail.setAdjustPriceVersion(AdjustPriceVersionEnum.VERSION_2_0.getVersion());
                        adjustPriceOrderDetail.setStatus((byte)StatusEnum.NORMAL.getCode());
                        adjustPriceOrderDetail.setVersion(1);
                        adjustPriceOrderDetail.setSkuId(0L);
                        if(null!=goodsNoOrgsBean.getItemSkuId()) {
                        	adjustPriceOrderDetail.setSkuId(Long.valueOf(goodsNoOrgsBean.getItemSkuId()));
                        }
                        adjustPriceOrderDetail.setExtend1(AdjustPriceOrderDetailExtend1.getJSONFormatStr(DataCompletionEnum.NOT_COMPLETE.getCode(),
                        		DataFullEnum.NOT_FULL.getCode(),
                        		goodsNoOrgsBean.getGoodsOrgTabType(),
                        		goodsNoOrgsBean.getGoodsTabTypeValue(),
                        		goodsNoOrgsBean.getOverrideLowerLevel()));

                        adjustPriceOrderDetailList.add(adjustPriceOrderDetail);
                    }
                }
            }
        }

        return adjustPriceOrderDetailList;
    }
    /**
     *
     * @Title: fillGoodsNoOrgIdsDTO
     * @Description: 填充 非 复制主单机构
     * @param: @param goodsNoOrgIdsDTO
     * @return: void
     * @throws
     */
    private void fillGoodsNoOrgIdsDTO(GoodsNoOrgIdsDTO bean,TokenUserDTO userDTO) {
    	String key = bean.getAdjustCode()+"_"+bean.getGoodsNo()+"_"+bean.getOrgIds();
    	RBucket<List<OrgLevelVO>> response = redissonClient.getBucket(RedisKeysConstant.ADJUST_GOODS_ORGIDS + key);
    	if (response.isExists()){
			List<OrgLevelVO> orgLevelVOList = response.get();
			fillData(orgLevelVOList, bean);
    	}else {
    		List<Long> orgIdList = Arrays.asList(bean.getOrgIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    		List<OrgLevelVO> orgLevelVOList = adjustPriceOrderV2Service.getUserActualSelectOrgDTOList(userDTO.getUserId(), bean.getResourceId(), orgIdList);
    		fillData(orgLevelVOList, bean);
			redissonClient.getBucket(RedisKeysConstant.ADJUST_GOODS_ORGIDS + key).set(orgLevelVOList,12,TimeUnit.HOURS);
    	}

    }
    private void fillData(List<OrgLevelVO> orgLevelVOList,GoodsNoOrgIdsDTO bean) {
    	List<OrgLevelVO> orgLevelVOListSort = orgLevelVOList.stream().sorted(Comparator.comparing(OrgLevelVO::getOrgId)).collect(Collectors.toList());
		List<Long> orgIds = orgLevelVOListSort.stream().map(OrgLevelVO::getOrgId).collect(Collectors.toList());
		List<String> orgNames = orgLevelVOListSort.stream().map(OrgLevelVO::getOrgName).collect(Collectors.toList());
		List<Integer> orgLevels = orgLevelVOListSort.stream().map(OrgLevelVO::getOrgLevel).collect(Collectors.toList());
		bean.setOrgIds(Joiner.on(",").join(orgIds));
		bean.setOrgNames(Joiner.on(",").join(orgNames));
		bean.setOrgLevels(Joiner.on(",").join(orgLevels));
    }
    /**
     * 发送需要补全的调价单明细信息
     * @param insertAdjustPriceOrderDetailList
     */
    private void sendToSupplementDataMsgs(boolean isPortal,List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList) {
        sendAdjustOrderDetailThreadExecutor.execute(() -> {
        	sendToSupplementDataMsgs(isPortal,insertAdjustPriceOrderDetailList,Boolean.FALSE);
        });
    }
    /**
     * 发送需要补全的调价单明细信息
     * @param insertAdjustPriceOrderDetailList
     * @param dataFull  是已补齐价格信息
     */
    @Override
    public void sendToSupplementDataMsgs(boolean isPortal,List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList,Boolean dataFull) {
    	if(CollectionUtils.isEmpty(insertAdjustPriceOrderDetailList)) {
    		return;
    	}
    	Integer channelId = insertAdjustPriceOrderDetailList.get(0).getChannelId();
    	Map<String, List<AdjustPriceOrderDetail>> orderDetailMap = insertAdjustPriceOrderDetailList.stream().collect(Collectors.groupingBy(e -> e.getGoodsNo()+"_"+e.getOrgIds()+"_"+e.getSkuId()));
    	for (Map.Entry<String, List<AdjustPriceOrderDetail>> entry : orderDetailMap.entrySet()) {
    		List<AdjustPriceOrderDetail> orderDetailList = entry.getValue();
    		AdjustPriceOrderDetail orderDetail = orderDetailList.get(0);
    		AdjustPriceOrderDetailExample orderDetailExample = new AdjustPriceOrderDetailExample();
    		Criteria createCriteria = orderDetailExample.createCriteria();
    		createCriteria.andAdjustCodeEqualTo(orderDetail.getAdjustCode()).andGoodsNoEqualTo(orderDetail.getGoodsNo()).andOrgIdsEqualTo(orderDetail.getOrgIds());
    		if(null!=orderDetail.getSkuId()) {
    			createCriteria.andSkuIdEqualTo(orderDetail.getSkuId());
    		}
    		List<AdjustPriceOrderDetail> detailList = adjustPriceOrderDetailMapper.selectByExample(orderDetailExample);
    		Map<String, AdjustPriceOrderDetail> detailMap = detailList.stream().collect(Collectors.toMap(k -> k.getPriceTypeCode(), part -> part ,(v1,v2)->v2));
    		orderDetailList.stream().map(adjustPriceOrderDetail -> {
                AdjustPriceOrderDetailSupplementDataVO supplementDataVO = new AdjustPriceOrderDetailSupplementDataVO();
                supplementDataVO.setAdjustCode(adjustPriceOrderDetail.getAdjustCode());
                supplementDataVO.setPriceTypeCode(adjustPriceOrderDetail.getPriceTypeCode());
                supplementDataVO.setGoodsNo(adjustPriceOrderDetail.getGoodsNo());
                supplementDataVO.setPrice(adjustPriceOrderDetail.getPrice());
                supplementDataVO.setDataFull(dataFull);
                supplementDataVO.setOrgIds(adjustPriceOrderDetail.getOrgIds());
                supplementDataVO.setOrgLevels(adjustPriceOrderDetail.getOrgLevels());
                // 使用Java8 Optional语法优化空指针异常处理
                supplementDataVO.setAdjustDetailMergeIds(
                    Optional.ofNullable(detailMap.get(adjustPriceOrderDetail.getPriceTypeCode()))
                        .map(AdjustPriceOrderDetail::getId)
                        .map(Object::toString)
                        .orElseGet(() -> Optional.ofNullable(adjustPriceOrderDetail.getId())
                            .map(Object::toString)
                            .orElse(""))
                );
                supplementDataVO.setChannelId(channelId);
                supplementDataVO.setSkuId(Optional.ofNullable(adjustPriceOrderDetail.getSkuId())
                    .map(Object::toString)
                    .orElse(""));
                supplementDataVO.setIsPortal(isPortal);
                return supplementDataVO;
            }).distinct().forEach(supplementDataVO -> {
            	try {
					Thread.sleep(50L);
				} catch (InterruptedException e1) {
					logger.error("休眠失败",e1);
				}
            	supplementDataProducer.sendMq(supplementDataVO);
            });
		}
    }

    /**
     * 根据价格类型Code返回调价单Amis表格的ColumnVO对象列表
     * @param isPortal
     * @param userDTO
     * @param adjustPriceOrder
     * @param isEdit
     * @param isAudit
     * @param workcode
     * @return
     */
    private List<ColumnVO> getAdjustPriceOrderDetailColumnVO(boolean isPortal, TokenUserDTO userDTO,AdjustPriceOrder adjustPriceOrder, boolean isEdit, boolean isAudit,Long workcode) {
        List<PriceType> sortedPriceTypeList = basePriceOrderService.getSortedPriceTypeList(adjustPriceOrder.getAdjustPriceType());
        List<ColumnVO> columns = new ArrayList<>();
        columns.addAll(Arrays.stream(AdjustPriceOrderDetailBaseColumnEnum.values())
            .map(AdjustPriceOrderDetailBaseColumnEnum::getColumnVO).collect(Collectors.toList()));
        columns.addAll(Arrays.stream(AdjustPriceOrderDetailRefPriceColumnEnum.values()).map(AdjustPriceOrderDetailRefPriceColumnEnum::getColumnVO).collect(Collectors.toList()));
        for (PriceType priceType : sortedPriceTypeList) {
            columns.addAll(Arrays.stream(AdjustPriceOrderDetailPriceTypeColumnEnum.values())
                .map(goodPriceColumnEnum -> goodPriceColumnEnum.getColumnVOByPriceType(priceType))
                .collect(Collectors.toList()));
        }
        columns.addAll(Arrays.stream(AdjustPriceOrderDetailLastColumnEnum.values()).map(AdjustPriceOrderDetailLastColumnEnum::getColumnVO).collect(Collectors.toList()));
        if (!isEdit) {
            columns = columns.stream().map(columnVO -> columnVO.obtainCommonTableColumnWithGroup()).collect(Collectors.toList());
        }
        List<ColumnVO> columnVOList = Arrays.stream(AdjustPriceOrderDetailAuditColumnEnum.values()).map(AdjustPriceOrderDetailAuditColumnEnum::getColumnVO).collect(Collectors.toList());
    	if(!isAudit) {
    		columnVOList = columnVOList.stream().map(columnVO -> columnVO.obtainCommonTableColumnWithGroup()).collect(Collectors.toList());
    	}
    	columns.addAll(columnVOList);
        removeColumns(isPortal,columns,userDTO,workcode,adjustPriceOrder);
        changeColumns(columns, userDTO);
        return columns;
    }

    private List<TagDTO> getTagList(TokenUserDTO userDTO){
        List<TagDTO> tagList = Lists.newArrayList();
        try {
            Long businessId = userDTO.getBusinessId();
            // 获取标签属性
            if(CollectionUtils.isEmpty(businessTagBizTypeList)){
                logger.warn("同步价格门店数据失败：未获取到有效连锁和标签业务类型映射apollo配置,apollo属性：business.tagbiztype.mapper|businessId:{}",businessId);
                return tagList;
            }
            Map<Long, BusinessTagBizTypeDto> collectMap = businessTagBizTypeList.stream().collect(Collectors.toMap(BusinessTagBizTypeDto::getBusinessId, Function.identity(), (v1, v2) -> v1));
            if(!collectMap.containsKey(businessId)){
                logger.warn("同步价格门店数据失败：根据BusinessId:{},未获取到有效连锁和标签业务类型映射apollo配置,apollo属性：business.tagbiztype.mapper",businessId);
                return tagList;
            }
            tagList = storeAttrTagService.batchFindTagByBizType(collectMap.get(businessId).getTagBizType());
            if(CollectionUtils.isEmpty(tagList)){
                logger.warn("同步价格门店数据失败：未获取到有效标签数据|businessId:{}",businessId);
                return tagList;
            }
        } catch (Exception e) {
            logger.info("getTagList|异常",e);
        }
        return tagList;
    }

    public void changeColumns(List<ColumnVO> columns,TokenUserDTO userDTO) {
        List<TagDTO> tagList = getTagList(userDTO);
    	//调整后商品类型
    	List<OptionDto> goodsTypeOptions = getTagOptions(TagTypeEnums.GOODS.getType(),tagList);
    	//调整后活动名称
        List<OptionDto> activityTypeOptions = getTagOptions(TagTypeEnums.STORE_GROUP.getType(),tagList);
        for(int i = 0; i < columns.size(); i++){
            ColumnVO column = columns.get(i);
            if(column.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_9.getName())){
                ColumnVO newColumn = ColumnVO.getTableSelectColumn(
                    AdjustPriceOrderDetailBaseColumnEnum.COLUMN_9.getName(),
                    AdjustPriceOrderDetailBaseColumnEnum.COLUMN_9.getLabel(),
                    goodsTypeOptions,
                    true,"基本信息"
                );
                columns.set(i, newColumn);
            }
            if(column.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_10.getName())){
                ColumnVO newColumn = ColumnVO.getTableSelectColumn(
                    AdjustPriceOrderDetailBaseColumnEnum.COLUMN_10.getName(),
                    AdjustPriceOrderDetailBaseColumnEnum.COLUMN_10.getLabel(),
                    activityTypeOptions,
                    true,"基本信息"
                );
                columns.set(i, newColumn);
            }
        }
    }

/**
     * 根据连锁Id查询调整后商品类型
     * @param tagType
     * @param tagList
     * @return
     */
    private List<OptionDto> getTagOptions(Integer tagType,List<TagDTO> tagList) {
    	List<OptionDto> goodsTypeList = Lists.newArrayList();
        if(CollectionUtils.isEmpty(tagList)){
            return goodsTypeList;
        }
    	try {
            for (TagDTO tagInfo : tagList) {
                if(tagInfo.getType().equals(tagType) && StringUtils.isNotBlank(tagInfo.getTagCode())){
                    goodsTypeList.add(new OptionDto(tagInfo.getTagCode(), tagInfo.getTagCode()));
                }
            }
		} catch (Exception e) {
			logger.info("getTagOptions|异常",e);
		}
    	return goodsTypeList;
    }

    /**
     * 检查ResponseEntity<AmisCommonResponse<PageResult<BusinessTagMappingDTO>>>是否为空
     * @param businessGoodsNoType 需要检查的响应对象
     * @return true:为空; false:不为空
     */
    private boolean isBusinessGoodsNoTypeEmpty(ResponseEntity<AmisCommonResponse<com.cowell.pricecenter.service.dto.response.PageResult<BusinessTagMappingDTO>>> businessGoodsNoType) {
        return businessGoodsNoType == null
               || businessGoodsNoType.getBody() == null
               || businessGoodsNoType.getBody().getData() == null
               || CollectionUtils.isEmpty(businessGoodsNoType.getBody().getData().getRows());
    }

    /**
     * 根据价格类型Code返回调价单Amis表格的ColumnVO对象列表
     * @param isPortal
     * @param userDTO
     * @param adjustPriceOrder
     * @param isEdit
     * @param isAudit
     * @param workcode
     * @return
     */
    private List<ColumnVO> getAdjustPriceOrderDetailB2CColumnVO(boolean isPortal,TokenUserDTO userDTO,AdjustPriceOrder adjustPriceOrder, boolean isEdit, boolean isAudit,Long workcode) {
        List<PriceType> sortedPriceTypeList = basePriceOrderService.getSortedPriceTypeList(adjustPriceOrder.getAdjustPriceType());
        List<ColumnVO> columns = new ArrayList<>();
        columns.addAll(Arrays.stream(AdjustPriceOrderDetailBaseColumnEnum.values())
            .map(AdjustPriceOrderDetailBaseColumnEnum::getColumnVO).collect(Collectors.toList()));
        for (PriceType priceType : sortedPriceTypeList) {
            columns.addAll(Arrays.stream(AdjustPriceOrderDetailB2CPriceTypeColumnEnum.values())
                .map(goodPriceColumnEnum -> goodPriceColumnEnum.getColumnVOByPriceType(priceType))
                .collect(Collectors.toList()));
        }
        columns.addAll(Arrays.stream(AdjustPriceOrderDetailLastColumnEnum.values()).map(AdjustPriceOrderDetailLastColumnEnum::getColumnVO).collect(Collectors.toList()));
        if (!isEdit) {
            columns = columns.stream().map(columnVO -> columnVO.obtainCommonTableColumnWithGroup()).collect(Collectors.toList());
        }
        List<ColumnVO> columnVOList = Arrays.stream(AdjustPriceOrderDetailAuditColumnEnum.values()).map(AdjustPriceOrderDetailAuditColumnEnum::getColumnVO).collect(Collectors.toList());
    	if(!isAudit) {
    		columnVOList = columnVOList.stream().map(columnVO -> columnVO.obtainCommonTableColumnWithGroup()).collect(Collectors.toList());
    	}
    	columns.addAll(columnVOList);
        removeColumns(isPortal,columns,userDTO,workcode,adjustPriceOrder);
        changeColumns(columns, userDTO);
        return columns;
    }

    private void removeColumns(boolean isPortal,List<ColumnVO> columns,TokenUserDTO userDTO,Long workcode,AdjustPriceOrder adjustPriceOrder) {
    	List<String> removeFieldList = Lists.newArrayList("orgIds","orgLevels","orgNames","lastPurchasingPrices","suggestPrices","goodsOrgTabType","extend1","refuseUserId","imageUrl","suggestPrice");
    	if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_9.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_10.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_11.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_16.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_18.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_24.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_25.getName());
    	}else {
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_19.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_26.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_27.getName());
    	}
    	if(!isPortal) {
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_29.getName());
        	removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_30.getName());
    	}
        removeFieldList.addAll(removeSingleStoreFieldList(adjustPriceOrder));
    	List<String> adjustPriceTypeList = Arrays.asList(adjustPriceOrder.getAdjustPriceType().split(","));
    	if(adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode())) {
    		for (String priceType : adjustPriceTypeList) {
    			if(PriceTypeModeEnum.LSJ.getPriceTypeCode().equals(priceType)) {
    				continue;
    			}
    			removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_9.getName()+"_"+priceType);
			}
    	}else {
    		for (String priceType : adjustPriceTypeList) {
    			removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_9.getName()+"_"+priceType);
			}
    	}
        //验证调价的价格类型是否同时存在零售价和会员价
        if(adjustPriceOrder.getChannel().equals(Constants.DEFAULT_CHANNEL)  &&
            adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode()) &&
            adjustPriceTypeList.contains(PriceTypeModeEnum.HYJ.getPriceTypeCode())){
            for (String priceType : adjustPriceTypeList) {
                if(PriceTypeModeEnum.HYJ.getPriceTypeCode().equals(priceType)) {
                    continue;
                }
                removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_10.getName()+"_"+priceType);
            }
        }else{
            for (String priceType : adjustPriceTypeList) {
                removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_10.getName()+"_"+priceType);
            }
        }
    	if(null!=userDTO && CollectionUtils.isNotEmpty(showBeforeAdjustgoodstypeBusinessIdList) && !showBeforeAdjustgoodstypeBusinessIdList.contains(userDTO.getBusinessId())) {
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_24.getName());
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_25.getName());
		}
    	jmdRolesCheck(userDTO, workcode, removeFieldList);
    	Iterator<ColumnVO> iterator = columns.iterator();
        while(iterator.hasNext()) {
        	ColumnVO columnVO = iterator.next();
        	if(removeFieldList.contains(columnVO.getName())) {
        		iterator.remove();
        	}
        }
    }

    /**
     * 验证加盟店定价专员角色  如果是则删除最后一次采购价和参考进价列
     * @param userDTO
     * @param workcode
     * @param removeFieldList
     */
    private void jmdRolesCheck(TokenUserDTO userDTO,Long workcode,List<String> removeFieldList) {
    	boolean isJmdDjzy = false;
    	if(null!=userDTO && CollectionUtils.isNotEmpty(priceAdjustJmdRoles)) {
    		List<String> intersectionList = userDTO.getRoles().stream().filter(s -> priceAdjustJmdRoles.contains(s)).collect(Collectors.toList());
    		if(CollectionUtils.isNotEmpty(intersectionList)) {
    			isJmdDjzy = true;
    		}
    	}else if(null!=workcode && CollectionUtils.isNotEmpty(priceAdjustJmdRoles)) {
    		Map<String, Long> userIdsMap = permissionService.getUserIdsByEmpCodes(Lists.newArrayList(String.valueOf(workcode)));
    		if(MapUtils.isNotEmpty(userIdsMap)){
    			Long userId = userIdsMap.get(String.valueOf(workcode));
    			List<UserRoleRelateDTO> userRoles = permissionExtService.getUserRoles(userId);
    			if(CollectionUtils.isNotEmpty(userRoles)) {
    				List<String> roleList = userRoles.stream().map(UserRoleRelateDTO::getRoleCode).collect(Collectors.toList());
    				List<String> intersectionList = roleList.stream().filter(s -> priceAdjustJmdRoles.contains(s)).collect(Collectors.toList());
    	    		if(CollectionUtils.isNotEmpty(intersectionList)) {
    	    			isJmdDjzy = true;
    	    		}
    			}
    		}
    	}
    	if(isJmdDjzy) {
    		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_18.getName());
    		removeFieldList.add(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName());
    	}
    }

    /**
     * 返回按照商品横向打平的数据
     * @param adjustPriceOrderDetailList
     * @return
     */
    @SuppressWarnings("unchecked")
	private List<Map<String, Object>> getAdjustPriceOrderDetailListResults(TokenUserDTO userDTO,AdjustPriceOrder adjustPriceOrder,List<AdjustPriceOrderDetail> adjustPriceOrderDetailList, String priceTypeCodes, boolean isEdit, boolean isAudit,boolean isExport) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>(adjustPriceOrderDetailList.size());
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(priceTypeCodes);
        for (AdjustPriceOrderDetail adjustPriceOrderDetail : adjustPriceOrderDetailList) {
            String goodsNo = adjustPriceOrderDetail.getGoodsNo();
            Long skuId = null == adjustPriceOrderDetail.getSkuId()?0L:adjustPriceOrderDetail.getSkuId();
            String priceTypeCode = adjustPriceOrderDetail.getPriceTypeCode();
            String orgIds = adjustPriceOrderDetail.getOrgIds()==null?"":adjustPriceOrderDetail.getOrgIds();
            String goodsNoOrgIds = goodsNo +"_" + orgIds+"_"+skuId;
            if (!priceTypeCodeList.contains(priceTypeCode)) {
                continue;
            }
            adjustPriceOrderDetail.setDetailExtend1(AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1()));
            if (!resultMap.containsKey(goodsNoOrgIds)) {
                Map<String, Object> result = new HashMap<>();
                resultMap.put(goodsNoOrgIds, result);
                for (AdjustPriceOrderDetailBaseColumnEnum commonColumnEnum : AdjustPriceOrderDetailBaseColumnEnum.values()) {
                    result.put(commonColumnEnum.getName(), commonColumnEnum.getAttrValue().apply(adjustPriceOrderDetail, isEdit));
                }
            }
            Map<String, Object> result = resultMap.get(goodsNoOrgIds);

            if(!getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
            	for (AdjustPriceOrderDetailPriceTypeColumnEnum priceColumnEnum : AdjustPriceOrderDetailPriceTypeColumnEnum.values()) {
                    Object value = priceColumnEnum.getAttrValue().apply(adjustPriceOrderDetail);
                    if (value instanceof String) {
                        try {
                            BigDecimal decimal = new BigDecimal((String) value);
                            if (decimal.compareTo(BigDecimal.ZERO) < 0) {
                                value = null;
                            }
                        } catch (NumberFormatException e) {
                            // 格式异常时按业务需要处理，例如记录日志或保持原值
                            logger.warn("字符串无法解析为数字：{}", value);
                        }
                    } else if (value instanceof BigDecimal && ((BigDecimal) value).compareTo(BigDecimal.ZERO) < 0) {
                        value = null;
                    }
                    result.put(priceColumnEnum.getNameByPriceTypeCode(priceTypeCode), value);
                }
        	}else {
        		for (AdjustPriceOrderDetailB2CPriceTypeColumnEnum priceColumnEnum : AdjustPriceOrderDetailB2CPriceTypeColumnEnum.values()) {
                    result.put(priceColumnEnum.getNameByPriceTypeCode(priceTypeCode), priceColumnEnum.getAttrValue().apply(adjustPriceOrderDetail));
                }
        	}
            for (AdjustPriceOrderDetailLastColumnEnum lastColumnEnum : AdjustPriceOrderDetailLastColumnEnum.values()) {
                result.put(lastColumnEnum.getName(), lastColumnEnum.getAttrValue().apply(adjustPriceOrderDetail,isEdit));
            }
            for (AdjustPriceOrderDetailAuditColumnEnum auditColumnEnum : AdjustPriceOrderDetailAuditColumnEnum.values()) {
                result.put(auditColumnEnum.getName(), auditColumnEnum.getAttrValue().apply(adjustPriceOrderDetail,isAudit));
            }
            if(!getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
            	for (AdjustPriceOrderDetailRefPriceColumnEnum refPriceColumnEnum : AdjustPriceOrderDetailRefPriceColumnEnum.values()) {
                    result.put(refPriceColumnEnum.getName(), refPriceColumnEnum.getAttrValue().apply(adjustPriceOrderDetail,isEdit));
                }
        	}
            setAdjustDetailMergeIds(result, adjustPriceOrderDetail);
            result.put("channelId", adjustPriceOrder.getChannel());
            if(isExport && getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
            	Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
                if(instance.isPresent()) {
                	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = instance.get();
                	//result.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_27.getName(), adjustPriceOrderDetailExtend1.getOutSkuCode());
                    result.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName(), adjustPriceOrderDetailExtend1.getChannelStoreId());
                }
            }
            String columnName = AdjustPriceOrderDetailBaseColumnEnum.COLUMN_31.getName();
            if(YNEnum.YES.getType().equals(adjustPriceOrderDetail.getMedicalPriceInterception())){
                result.put(columnName, "医保限价强制拦截");
            }else{
                Object existingValue = result.get(columnName);
                if(existingValue == null || StringUtils.isBlank(existingValue.toString()) || existingValue.toString().equals(YNEnum.NO.getType().toString())){
                    result.put(columnName, "");
                }
            }
        }
        List<Map<String, Object>> listResult = resultMap.values().stream().collect(Collectors.toList());
        if(!getB2CChannelIdList().contains(adjustPriceOrder.getChannel()) && CollectionUtils.isNotEmpty(listResult) && !isExport) {
        	suppBusinessHistoryPrice(listResult, adjustPriceOrder, userDTO);
        }
        return listResult;
    }

    /**
     * 补充企业级相关价格
     * @param listResult
     * @param adjustPriceOrder
     * @param userDTO
     */
    private void suppBusinessHistoryPrice(List<Map<String, Object>> listResult,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO) {
    	CountDownLatch countDownLatch = new CountDownLatch(listResult.size()+1);
    	for (Map<String, Object> map : listResult) {
    		adjustOrderDetailSearchThreadExecutor.execute(() -> {
    			try {
    				PriceBusinessDetailHistoryExample detailHistoryExample = new PriceBusinessDetailHistoryExample();
					String adjustCode = adjustPriceOrder.getAdjustCode();
					String goodsNo = map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getName()).toString();
					String businessIds = "";
					Optional<AdjustPriceOrderDetailExtend1> extend1Instance = AdjustPriceOrderDetailExtend1.getInstance(map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_23.getName()).toString());
					if(null!=extend1Instance && extend1Instance.isPresent()) {
						AdjustPriceOrderDetailExtend1 extend1 = extend1Instance.get();
						if(null!=extend1 && StringUtils.isNotBlank(extend1.getBusinessIds())){
							businessIds = extend1.getBusinessIds();
						}
					}
					if(StringUtils.isNotBlank(businessIds)) {
						List<PriceBusinessDetailHistory> hisAllList = Lists.newArrayList();
						List<Long> businessIdList = Arrays.asList(businessIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
						for (Long businessId : businessIdList) {
							String key = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.PRICE_BUSINESS_DETAIL_HISTORY_KEY+adjustCode+"_"+businessId+"_"+goodsNo;
							RBucket<List<PriceBusinessDetailHistory>> hisBucket = redissonClient.getBucket(key);
							if(hisBucket.isExists()) {
								hisAllList.addAll(hisBucket.get());
								continue;
							}
							detailHistoryExample.clear();
							detailHistoryExample.createCriteria().
							andOrderCodeEqualTo(adjustCode).andGoodsNoEqualTo(goodsNo).andBusinessIdEqualTo(businessId).
							andOrderTypeEqualTo(OrderTypeEnum.ORDER_TYPE_ADJUST.getTypeCode()).andStatusEqualTo((byte)0);
							List<PriceBusinessDetailHistory> hisList = priceBusinessDetailHistoryService.selectByExample(detailHistoryExample);
							if(CollectionUtils.isEmpty(hisList)) {
								continue;
							}
							hisList = hisList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(book -> book.getGoodsNo() + "-" + book.getPriceTypeCode()))), ArrayList::new));
							hisBucket.set(hisList,5,TimeUnit.MINUTES);
							hisAllList.addAll(hisList);
							hisList.clear();
						}
						supplementAdjustDetailPriceOne(map, hisAllList);
						hisAllList.clear();
					}
					if(null!=userDTO && CollectionUtils.isNotEmpty(showBeforeAdjustgoodstypeBusinessIdList) && showBeforeAdjustgoodstypeBusinessIdList.contains(userDTO.getBusinessId())) {
						fillAdjustBeforeGoodsActivity(adjustPriceOrder, map);
					}
				} catch (Exception e) {
					logger.error("AdjustPriceOrderDetailV2ServiceImpl|suppBusinessHistoryPrice|定调价商品明细补充企业级相关价格异常",e);
				} finally {
                    countDownLatch.countDown();
                }
    		});
		}
    	adjustOrderDetailSearchThreadExecutor.execute(() -> {
    		try {
				List<String> adjustPriceTypeList = Arrays.asList(adjustPriceOrder.getAdjustPriceType().split(","));
				if(adjustPriceTypeList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode())) {
					suppWisdomPrice(listResult, adjustPriceOrder);
				}
			} catch (Exception e) {
				logger.error("AdjustPriceOrderDetailV2ServiceImpl|suppBusinessHistoryPrice|定调价商品明细补充智慧建议价格异常",e);
			} finally {
                countDownLatch.countDown();
            }
    	});

    	try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("getAdjustPriceOrderDetailListResults|异常, adjustCode:{}", adjustPriceOrder.getAdjustCode(),e);
        }
    }

    /**
     * 补充智慧定价建议
     * @param listResult
     * @param adjustPriceOrder
     */
    private void suppWisdomPrice(List<Map<String, Object>> listResult,AdjustPriceOrder adjustPriceOrder) {
    	List<Long> erpBusinessIdList = Lists.newArrayList();
    	List<String> erpGoodsNoList = Lists.newArrayList();
    	for (Map<String, Object> map : listResult) {
    		erpGoodsNoList.add(map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getName()).toString());
    		Optional<AdjustPriceOrderDetailExtend1> extend1Instance = AdjustPriceOrderDetailExtend1.getInstance(map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_23.getName()).toString());
			if(null!=extend1Instance && extend1Instance.isPresent()) {
				AdjustPriceOrderDetailExtend1 extend1 = extend1Instance.get();
				if(null!=extend1 && StringUtils.isNotBlank(extend1.getBusinessIds())){
					erpBusinessIdList.addAll(Arrays.asList(extend1.getBusinessIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList()));
				}
			}
    	}
    	ResponseEntity<List<MakePriceSuggestResultDTO>> response = erpBizSupportService.selectConfirmPriceByBusinessGoodsNo(erpBusinessIdList.stream().distinct().collect(Collectors.toList()), erpGoodsNoList.stream().distinct().collect(Collectors.toList()));
    	if(null!=response && CollectionUtils.isNotEmpty(response.getBody())) {
    		List<MakePriceSuggestResultDTO> makePriceList = response.getBody();
    		Map<String, List<MakePriceSuggestResultDTO>> makePriceMap = makePriceList.stream().collect(Collectors.groupingBy(e -> e.getGoodsNo()+"_"+e.getBusinessId()));
    		List<MakePriceSuggestResultDTO> makePriceAllList = Lists.newArrayList();
    		for (Map<String, Object> map : listResult) {
    			String goodsNo = map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getName()).toString();
    			List<Long> businessIdList = Lists.newArrayList();
    			Optional<AdjustPriceOrderDetailExtend1> extend1Instance = AdjustPriceOrderDetailExtend1.getInstance(map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_23.getName()).toString());
    			if(null!=extend1Instance && extend1Instance.isPresent()) {
    				AdjustPriceOrderDetailExtend1 extend1 = extend1Instance.get();
    				if(null!=extend1 && StringUtils.isNotBlank(extend1.getBusinessIds())){
    					businessIdList = Arrays.asList(extend1.getBusinessIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    				}
    			}
    			makePriceAllList.clear();
    			for (Long businessId : businessIdList) {
    				List<MakePriceSuggestResultDTO> list = makePriceMap.get(goodsNo+"_"+businessId);
    				if(CollectionUtils.isNotEmpty(list)) {
    					makePriceAllList.addAll(list);
    				}
				}
    			if(CollectionUtils.isEmpty(makePriceAllList)) {
    				continue;
    			}
    			makePriceAllList = makePriceAllList.stream().sorted((Comparator.nullsLast(Comparator.comparing(MakePriceSuggestResultDTO::getConfirmMakepriceSuggestPrice,Comparator.nullsLast(BigDecimal::compareTo))))).collect(Collectors.toList());
    			map.put("wisdomPrice_"+PriceTypeModeEnum.LSJ.getPriceTypeCode(), makePriceAllList.get(0).getConfirmMakepriceSuggestPrice());
    			StringBuffer wisdomPricesSb = new StringBuffer();
    			for (MakePriceSuggestResultDTO result : makePriceAllList) {
    				OrgToRedisDTO orgDto = CacheVar.businessCacheMap.get(result.getBusinessId());
    				wisdomPricesSb.append("<div>").append(orgDto==null?"":orgDto.getName()).append(":").append(result.getConfirmMakepriceSuggestPrice()).append("</div>");
    			}
    			map.put("wisdomPrices_"+PriceTypeModeEnum.LSJ.getPriceTypeCode(), wisdomPricesSb.toString());
    		}
    	}
    }

    private void fillAdjustBeforeGoodsActivity(AdjustPriceOrder order,Map<String, Object> map) {
    	Date operateTime = new Date();
        List<String> priceTypeCodeList = Arrays.asList(order.getAdjustPriceType().split(",")).stream().map(s -> s.trim()).collect(Collectors.toList());
    	List<OrgLevelVO> listOrgLevelVO = null;
    	List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList = null;
    	List<Long> storeIdList = null;
    	String goodsNo = map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getName()).toString();
		String orgIds = map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName()).toString();
		String orgLevels = map.get(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_17.getName()).toString();
		String itemOrgIdStoreIdsKey = RedisKeysConstant.PROJECT_NAME+RedisKeysConstant.ITEM_ORGID_STOREID_KEY+Md5Utils.MD5Encode(orgIds);
		RBucket<List<Long>> redisStoreIdList = redissonClient.getBucket(itemOrgIdStoreIdsKey);
		if(redisStoreIdList.isExists()) {
			storeIdList = redisStoreIdList.get();
		}else {
			listOrgLevelVO = listOrgLevelVO(orgIds, orgLevels);
    		orderOrgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(order.getAdjustCode(),listOrgLevelVO,operateTime);
    		storeIdList = orderOrgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).distinct().collect(Collectors.toList());
    		redissonClient.getBucket(itemOrgIdStoreIdsKey).set(storeIdList,30,TimeUnit.MINUTES);
		}
		if(order.getAuditStatus()==AuditStatusEnum.AUDIT_PASS.getCode()) {
			adjustBeforeGoodsForDs(order.getAdjustCode(), storeIdList, priceTypeCodeList.get(0), goodsNo, map);
    	}else {
    		adjustBeforeGoodsForEs(order.getAdjustCode(), storeIdList, priceTypeCodeList.get(0), goodsNo, map);
    	}
		if(null!=storeIdList) {
			storeIdList.clear();
		}
		if(null!=orderOrgStoreDetailList) {
			orderOrgStoreDetailList.clear();
		}
    }

    private void adjustBeforeGoodsForDs(String adjustCode,List<Long> storeIdList,String priceTypeCode,String goodsNo,Map<String, Object> map) {
    	String beforeGoodsType = null;
    	String beforeActivity = null;
    	boolean beforeGoodsTypeFlag = false;
    	boolean beforeActivityFlag = false;
    	StringBuffer beforeGoodsTypeSb = new StringBuffer();
		StringBuffer beforeActivitySb = new StringBuffer();
    	AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
		orgStoreDetailExample.createCriteria().andAdjustCodeEqualTo(adjustCode).andStoreIdIn(storeIdList).andPriceTypeCodeEqualTo(priceTypeCode).andGoodsNoEqualTo(goodsNo);
		List<AdjustPriceOrderOrgStoreDetail> orderOrgStoreDetailList = adjustPriceOrderOrgStoreDetailMapper.selectByExample(orgStoreDetailExample);
		if(CollectionUtils.isNotEmpty(orderOrgStoreDetailList)) {
			for (AdjustPriceOrderOrgStoreDetail detail : orderOrgStoreDetailList) {
				Optional<AdjustPriceOrderOrgStoreDetailExtend> instance = AdjustPriceOrderOrgStoreDetailExtend.getInstance(detail.getExtend());
    			if(instance.isPresent()) {
    				AdjustPriceOrderOrgStoreDetailExtend detailExtend = instance.get();
    				if(StringUtils.isNotBlank(detailExtend.getBeforeGoodsTypeName())){
    					if(!beforeGoodsTypeFlag) {
    						beforeGoodsType = detailExtend.getBeforeGoodsTypeName();
    						beforeGoodsTypeFlag = true;
    					}
    					beforeGoodsTypeSb.append("<div>").append(detail.getStoreName()).append(":").append(detailExtend.getBeforeGoodsTypeName()).append("</div>");
    				}
    				if(StringUtils.isNotBlank(detailExtend.getBeforeActivityName())) {
    					if(!beforeActivityFlag) {
    						beforeActivity = detailExtend.getBeforeActivityName();
    						beforeActivityFlag = true;
    					}
    					beforeActivitySb.append("<div>").append(detail.getStoreName()).append(":").append(detailExtend.getBeforeActivityName()).append("</div>");
    				}
    			}
			}
			map.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_24.getName(), beforeGoodsType);
			map.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_25.getName(), beforeActivity);
			map.put("beforeGoodsTypeNames", beforeGoodsTypeSb.toString());
			map.put("beforeActivityNames", beforeActivitySb.toString());
			if(null!=orderOrgStoreDetailList) {
				orderOrgStoreDetailList.clear();
			}
		}
    }

    private void adjustBeforeGoodsForEs(String adjustCode,List<Long> storeIdList,String priceTypeCode,String goodsNo,Map<String, Object> map) {
    	String beforeGoodsType = null;
    	String beforeActivity = null;
    	boolean beforeGoodsTypeFlag = false;
    	boolean beforeActivityFlag = false;
    	StringBuffer beforeGoodsTypeSb = new StringBuffer();
		StringBuffer beforeActivitySb = new StringBuffer();
    	List<List<Long>> partition = Lists.partition(storeIdList, Constants.BATCH_QUERY_SIZE_50);
		List<PriceStoreDetailVo> storeDetailList = Lists.newArrayList();
		List<PriceStoreDetailVo> subStoreDetailList = Lists.newArrayList();
		for (List<Long> subStoreIdList : partition) {
			subStoreDetailList.clear();
			subStoreDetailList = searchExtService.getByGoodsNoAndStoreIdListAndPriceTypeCode(subStoreIdList, goodsNo, priceTypeCode);
			if(CollectionUtils.isNotEmpty(subStoreDetailList)) {
				storeDetailList.addAll(subStoreDetailList);
			}
		}
		if(CollectionUtils.isNotEmpty(storeDetailList)) {
			for (PriceStoreDetailVo detail : storeDetailList) {
				if(StringUtils.isNotBlank(detail.getComment())){
					if(!beforeGoodsTypeFlag) {
						beforeGoodsType = detail.getComment();
						beforeGoodsTypeFlag = true;
					}
					beforeGoodsTypeSb.append("<div>").append(detail.getStoreName()).append(":").append(detail.getComment()).append("</div>");
				}
				if(StringUtils.isNotBlank(detail.getLabel())) {
					if(!beforeActivityFlag) {
						beforeActivity = detail.getLabel();
						beforeActivityFlag = true;
					}
					beforeActivitySb.append("<div>").append(detail.getStoreName()).append(":").append(detail.getLabel()).append("</div>");
				}
			}
			map.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_24.getName(), beforeGoodsType);
			map.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_25.getName(), beforeActivity);
			map.put("beforeGoodsTypeNames", beforeGoodsTypeSb.toString());
			map.put("beforeActivityNames", beforeActivitySb.toString());
			if(null!=storeDetailList) {
				storeDetailList.clear();
			}
		}
    }

    private void supplementAdjustDetailPriceOne(Map<String, Object> map,List<PriceBusinessDetailHistory> hisList) {
    	if(CollectionUtils.isEmpty(hisList)) {
    		return;
    	}
    	Map<String, List<PriceBusinessDetailHistory>> hisMap = hisList.stream().collect(Collectors.groupingBy(e -> e.getPriceTypeCode()));
    	Map<String, String> priceTypeRefMap = getPriceTypeReferenceConfig();
		List<PriceBusinessDetailHistory> lastOrderList = hisMap.get(lastPurchasePriceType);//最后异常采购价格
		List<PriceBusinessDetailHistory> sortLastOrderList = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(lastOrderList)){
			sortLastOrderList = lastOrderList.stream().sorted((Comparator.nullsLast(Comparator.comparing(PriceBusinessDetailHistory::getPrice,Comparator.nullsLast(BigDecimal::compareTo))))).collect(Collectors.toList());
		}
		List<PriceBusinessDetailHistory> suggestOrderList = hisMap.get(suggestPriceType);//定价建议
		List<PriceBusinessDetailHistory> sortSuggestOrderList = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(suggestOrderList)){
			sortSuggestOrderList = suggestOrderList.stream().sorted((Comparator.nullsLast(Comparator.comparing(PriceBusinessDetailHistory::getPrice,Comparator.nullsLast(BigDecimal::compareTo))))).collect(Collectors.toList());
		}
		List<PriceBusinessDetailHistory> comRetailPriceList = hisMap.get(priceTypeRefMap.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_2.getName()));//公司参考零售价
		List<PriceBusinessDetailHistory> sortComRetailPriceList = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(comRetailPriceList)){
			sortComRetailPriceList = comRetailPriceList.stream().sorted((Comparator.nullsLast(Comparator.comparing(PriceBusinessDetailHistory::getPrice,Comparator.nullsLast(BigDecimal::compareTo))))).collect(Collectors.toList());
		}
		List<PriceBusinessDetailHistory> refPurchPriceList = hisMap.get(priceTypeRefMap.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName()));//参考进价
		List<PriceBusinessDetailHistory> sortRefPurchPriceList = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(refPurchPriceList)){
			sortRefPurchPriceList = refPurchPriceList.stream().sorted((Comparator.nullsLast(Comparator.comparing(PriceBusinessDetailHistory::getPrice,Comparator.nullsLast(BigDecimal::compareTo))))).collect(Collectors.toList());
		}
		List<PriceBusinessDetailHistory> floorPriceList = hisMap.get(priceTypeRefMap.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName()));//最低限价
		List<PriceBusinessDetailHistory> sortFloorPriceList = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(floorPriceList)){
			sortFloorPriceList = floorPriceList.stream().sorted((Comparator.nullsLast(Comparator.comparing(PriceBusinessDetailHistory::getPrice,Comparator.nullsLast(BigDecimal::compareTo))))).collect(Collectors.toList());
		}
		map.put(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_18.getName(), sortLastOrderList.size()>0?sortLastOrderList.get(0).getPrice():null);
		map.put(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_2.getName(), sortComRetailPriceList.size()>0?sortComRetailPriceList.get(0).getPrice():null);
		map.put(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName(), sortRefPurchPriceList.size()>0?sortRefPurchPriceList.get(0).getPrice():null);
		map.put(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName(), sortFloorPriceList.size()>0?sortFloorPriceList.get(0).getPrice():null);

		StringBuffer lastSb = new StringBuffer();
		StringBuffer suggestSb = new StringBuffer();
		StringBuffer comRetailPriceSb = new StringBuffer();
		StringBuffer refPurchPriceSb = new StringBuffer();
		StringBuffer floorPriceSb = new StringBuffer();
		for (PriceBusinessDetailHistory his : sortLastOrderList) {
			OrgToRedisDTO orgDto = CacheVar.businessCacheMap.get(his.getBusinessId());
			lastSb.append("<div>").append(orgDto==null?"":orgDto.getName()).append(":").append(his.getPrice()).append("</div>");
		}
		for (PriceBusinessDetailHistory his : sortSuggestOrderList) {
			OrgToRedisDTO orgDto = CacheVar.businessCacheMap.get(his.getBusinessId());
			suggestSb.append("<div>").append(orgDto==null?"":orgDto.getName()).append(":").append(his.getPrice()).append("</div>");
		}
		for (PriceBusinessDetailHistory his : sortComRetailPriceList) {
			OrgToRedisDTO orgDto = CacheVar.businessCacheMap.get(his.getBusinessId());
			comRetailPriceSb.append("<div>").append(orgDto==null?"":orgDto.getName()).append(":").append(his.getPrice()).append("</div>");
		}
		for (PriceBusinessDetailHistory his : sortRefPurchPriceList) {
			OrgToRedisDTO orgDto = CacheVar.businessCacheMap.get(his.getBusinessId());
			refPurchPriceSb.append("<div>").append(orgDto==null?"":orgDto.getName()).append(":").append(his.getPrice()).append("</div>");
		}
		for (PriceBusinessDetailHistory his : sortFloorPriceList) {
			OrgToRedisDTO orgDto = CacheVar.businessCacheMap.get(his.getBusinessId());
			floorPriceSb.append("<div>").append(orgDto==null?"":orgDto.getName()).append(":").append(his.getPrice());
			Optional<PriceBusinessDetailHistoryExtend> instance = PriceBusinessDetailHistoryExtend.getInstance(his.getExtend());
			if(instance.isPresent()) {
				PriceBusinessDetailHistoryExtend hisExtend = instance.get();
				floorPriceSb.append("&nbsp;限价时间：").
				append(StringUtils.isBlank(hisExtend.getFloorPriceStartDate())?"":hisExtend.getFloorPriceStartDate()).append("~").
				append(StringUtils.isBlank(hisExtend.getFloorPriceEndDate())?"":hisExtend.getFloorPriceEndDate()).
				append("</div>");
			}
		}
		map.put("lastPurchasingPrices", lastSb.toString());
		map.put("suggestPrices", suggestSb.toString());
		map.put("comretailprices", comRetailPriceSb.toString());
		map.put("refpurchprices", refPurchPriceSb.toString());
		map.put("floorprices", floorPriceSb.toString());

		if(CollectionUtils.isNotEmpty(sortLastOrderList)) {
			sortLastOrderList.clear();
		}
		if(CollectionUtils.isNotEmpty(sortSuggestOrderList)) {
			sortSuggestOrderList.clear();
		}
		if(CollectionUtils.isNotEmpty(sortComRetailPriceList)) {
			sortComRetailPriceList.clear();
		}
		if(CollectionUtils.isNotEmpty(sortRefPurchPriceList)) {
			sortRefPurchPriceList.clear();
		}
		if(CollectionUtils.isNotEmpty(sortFloorPriceList)) {
			sortFloorPriceList.clear();
		}
    }

    private void setAdjustDetailMergeIds(Map<String, Object> result,AdjustPriceOrderDetail adjustPriceOrderDetail) {
    	List<Long> detailIds = Lists.newArrayList();
    	String mergeIds = result.get("adjustDetailMergeIds")==null?"":result.get("adjustDetailMergeIds").toString();
    	if(StringUtils.isNotBlank(mergeIds)) {
    		detailIds = Arrays.asList(mergeIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    	}
    	detailIds.add(adjustPriceOrderDetail.getId());
    	result.put("adjustDetailMergeIds", Joiner.on(",").join(detailIds));
    }
    /**
     * 校验Amis的Table组件传过来的参数，错误则抛出异常
     * @param param
     */
    private void checkAmisTableEditParam(AmisTableEditParam param) {
        if (param.getAdjustPriceOrderId() == null ||
            CollectionUtils.isEmpty(param.getRows()) ||
            CollectionUtils.isEmpty(param.getRowsDiff())
        ) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.checkAmisTableEditParam]  参数不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
    }

    /**
     * 根据管控单价格DTO获取调价单编辑对象
     * @param detailEditV2
     * @param priceDTO
     * @return
     */
    private AdjustPriceOrderDetailEditV2 getAdjustPriceOrderDetailEditV2FromManagePriceDTO(AdjustPriceOrderDetailEditV2 detailEditV2, AdjustPriceOrderManagePriceDTO priceDTO) {
        detailEditV2.setUpperLimit(priceDTO.getUpperLimit());
        detailEditV2.setLowerLimit(priceDTO.getLowerLimit());
        detailEditV2.setGuidePrice(priceDTO.getGuidePrice());
        return detailEditV2;
    }

    /**
     * 根据ES的商品信息查询结果获取调价单编辑对象
     * @param adjustCode
     * @param goodsInfo
     * @return
     */
    private AdjustPriceOrderDetailEditV2 getAdjustPriceOrderDetailEditV2FromGoodInfo(String adjustCode, SpuListVO goodsInfo) {
        AdjustPriceOrderDetailEditV2 detailEditV2 = new AdjustPriceOrderDetailEditV2();
        detailEditV2.setAdjustCode(adjustCode);
        detailEditV2.setSpuId(goodsInfo.getId());
        detailEditV2.setGoodsNo(goodsInfo.getGoodsNo());
        detailEditV2.setCurName(goodsInfo.getCurName());
        detailEditV2.setManufacturer(goodsInfo.getFactoryid());
        detailEditV2.setJhiSpecification(goodsInfo.getJhiSpecification());
        detailEditV2.setDosage(goodsInfo.getDosageForms());
        detailEditV2.setProdarea(goodsInfo.getProdarea());
        detailEditV2.setGoodsName(goodsInfo.getName());
        detailEditV2.setGoodsUnit(goodsInfo.getGoodsunit());
        return detailEditV2;
    }

    /**
     *
     * @Title: getStoreGroupTagRedisKey
     * @Description: 门店组门店标签 一个tagId 对应一个tagBean
     * @param: @param tagId
     * @param: @return
     * @return: String
     * @throws
     */
    private String getStoreGroupTagRedisKey(Long tagId) {
    	return RedisKeysConstant.STORE_GROUP_TAG+tagId;
    }

    /**
     *
     * @Title: getTagIdScopeStoreOrgIdRedisKey
     * @Description:门店组、标签 包含的门店 一个tagId 对应storeOrgIdList
     * @param: @param tagId
     * @param: @return
     * @return: String
     * @throws
     */
    private String getTagIdScopeStoreOrgIdRedisKey(Long tagId) {
    	return RedisKeysConstant.TAGID_SCOPE_STORE_ORGID+tagId;
    }

    /**
     *
     * @Title: getAdjustImportResultRedisKey
     * @Description: 调价单导入结果  key
     * @param: @param adjustCode
     * @param: @return
     * @return: String
     * @throws
     */
    private String getAdjustImportResultRedisKey(String adjustCode) {
    	return RedisKeysConstant.ADJUST_IMPORT_RESULT+adjustCode;
    }
    /**
     *
     * @Title: getAdjustImportExceptionResult_
     * @Description: 调价单导入异常数据
     * @param: @param adjustCode
     * @param: @return
     * @return: String
     * @throws
     */
    private String getAdjustImportExceptionResult(String adjustCode) {
    	return RedisKeysConstant.ADJUST_IMPORT_EXCEPTION_RESULT_+adjustCode;
    }
    /**
     *
     * @Title: getAdjustImportExceptionResultStructureRediskey
     * @Description: 调价单导入异常结构
     * @param: @param adjustCode
     * @param: @return
     * @return: String
     * @throws
     */
    private String getAdjustImportExceptionResultStructureRediskey(String adjustCode) {
    	return RedisKeysConstant.ADJUST_IMPORT_EXCEPTION_RESULT_STRUCTURE_+adjustCode;
    }

    /**
     * 调价单明细行门店id
     * @param adjustItemUnique
     * @return
     */
    private String getAdjustItemStoreIdRedisKey(String adjustItemUnique) {
    	return RedisKeysConstant.ADJUST_ITEM_STOREID+adjustItemUnique;
    }

    /**
     *
     * @Title: storeGroupTagInfo
     * @Description: 查询门店组、门店标签
     * @param: @param importList
     * @param: @param userDTO
     * @return: void
     * @throws
     */
    private void storeGroupTagInfo(AdjustPriceOrder adjustPriceOrder,List<ImportAdjustPriceOrderDetailDTO> importList,TokenUserDTO userDTO) {
		Map<Integer, List<ImportAdjustPriceOrderDetailDTO>> groupTagMap = importList.stream().filter(v -> !Boolean.FALSE.equals(v.getResult()) &&
    			null!=v.getGoodsOrgTabType()  &&
    			v.getGoodsOrgTabType().intValue()!=PriceOrderTabTypeEnum.STORE_LIST.getType()).collect(Collectors.groupingBy(e -> e.getGoodsOrgTabType()));
		Map<Integer,Set<Long>> storeGroupMapperDetailMap = Maps.newHashMap();
		groupTagMap.forEach((orgTatType,detailList) -> {
			Set<Long> groupTagIdSet = null;
			for (ImportAdjustPriceOrderDetailDTO detailDto : detailList) {
				groupTagIdSet = Arrays.asList(detailDto.getTabTypeVals().split(",")).stream().map(s ->Long.valueOf(s.trim())).collect(Collectors.toSet());
				if(orgTatType==PriceOrderTabTypeEnum.STORE_GROUP.getType()) {
					if(storeGroupMapperDetailMap.containsKey(TagTypeEnums.STORE_GROUP.getType())) {
						storeGroupMapperDetailMap.get(TagTypeEnums.STORE_GROUP.getType()).addAll(groupTagIdSet);
					}else{
						storeGroupMapperDetailMap.put(TagTypeEnums.STORE_GROUP.getType(), groupTagIdSet);
					}
				}else {
					if(storeGroupMapperDetailMap.containsKey(TagTypeEnums.STORE.getType())) {
						storeGroupMapperDetailMap.get(TagTypeEnums.STORE.getType()).addAll(groupTagIdSet);
					}else{
						storeGroupMapperDetailMap.put(TagTypeEnums.STORE.getType(), groupTagIdSet);
					}
				}

			}
		});
		storeGroupMapperDetailMap.forEach((orgTatType,tagIdSet) -> {
			storeGroupTagInfo(orgTatType, tagIdSet, userDTO);
		});
    }

    private void storeGroupTagInfo(Integer tagType, Set<Long> groupTagIdAllSet, TokenUserDTO userDTO) {
    	if(CollectionUtils.isNotEmpty(groupTagIdAllSet)) {
			List<StoreGroupDTO> storeGroupList = storeGroupService.getStoreGroupByStoreGroupIdList(userDTO.getUserId(),tagType, Lists.newArrayList(groupTagIdAllSet));
			if(CollectionUtils.isNotEmpty(storeGroupList)) {
        		storeGroupList.forEach(item -> {
        			String storeGroupTagStoreKey = getStoreGroupTagRedisKey(item.getId());
        	    	redissonClient.getBucket(storeGroupTagStoreKey).set(item,TIMETOLIVEHOURS,TimeUnit.HOURS);
        		});
        		storeGroupList.clear();
        	}
            groupTagIdAllSet.clear();
    	}
    }
    /**
     *
     * @Title: storeGroupTagMapperStoreOrgIdByTagId
     * @Description: 门店组、门店标签 对应的门店orgId
     * @param: @param adjustPriceOrder
     * @param: @param importList
     * @param: @param userDTO
     * @return: void
     * @throws
     */
    private void storeGroupTagMapperStoreOrgIdByTagId(AdjustPriceOrder adjustPriceOrder,List<ImportAdjustPriceOrderDetailDTO> importList,TokenUserDTO userDTO) {
    	List<String> tabTypeVals = importList.stream().filter(v -> !Boolean.FALSE.equals(v.getResult()) &&
    			null!=v.getGoodsOrgTabType()  &&
    			(v.getGoodsOrgTabType().intValue()==PriceOrderTabTypeEnum.STORE_GROUP.getType() ||
    			v.getGoodsOrgTabType().intValue()==PriceOrderTabTypeEnum.STORE_FLAG.getType())).map(ImportAdjustPriceOrderDetailDTO::getTabTypeVals).collect(Collectors.toList());
    	Set<Long> tagIdSet = new HashSet<Long>();
    	if(CollectionUtils.isNotEmpty(tabTypeVals)) {
    		for (String tabTypeIds : tabTypeVals) {
                tagIdSet.addAll(Arrays.asList(tabTypeIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toSet()));
			}
    	}
    	//String storeGroupTagStoreKey = getStoreGroupTagRedisKey(item.getId());
    	CountDownLatch countDownLatch = new CountDownLatch(tagIdSet.size());
    	//所有tagid 对应的门店
    	for (Long tagId : tagIdSet) {
    		adjustOrderDetailImportThreadExecutor.execute(() -> {
				try {
					String storeGroupTagStoreKey = getStoreGroupTagRedisKey(tagId);
					RBucket<StoreGroupDTO> bucket = redissonClient.getBucket(storeGroupTagStoreKey);
					if(bucket.isExists()) {
						List<String> storeOrgIdList = storeGroupService.tagEntityCodeListQuery(tagId);
						if(CollectionUtils.isNotEmpty(storeOrgIdList)) {
							List<Long> storeIdList = storeOrgIdList.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
							String tagIdScopeStoreOrgIdKey = getTagIdScopeStoreOrgIdRedisKey(tagId);
							redissonClient.getBucket(tagIdScopeStoreOrgIdKey).set(storeIdList,TIMETOLIVEHOURS,TimeUnit.HOURS);
							storeIdList.clear();
						}
					}
				} catch (Exception e){
		            logger.error("AdjustPriceOrderDetailV2ServiceImpl|storeGroupTagMapperStoreOrgIdByTagId|查询门店组、门店标签 对应的门店orgId异常, adjustCode:{}", adjustPriceOrder.getAdjustCode(),e);
				} finally {
                    countDownLatch.countDown();
                }
            });
		}
    	try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("AdjustPriceOrderDetailV2ServiceImpl|storeGroupTagMapperStore, adjustCode:{}", adjustPriceOrder.getAdjustCode(),e);
        }
    }

    /**
     *
     * @Title: fillOrderDetailOrgData
     * @Description: 封装调价单明细 orgId orgName orgLevel
     * @param: @param item
     * @param: @param orgIdList
     * @param: @param userDTO
     * @return: void
     * @throws
     */
    private void fillOrderDetailOrgData(ImportAdjustPriceOrderDetailDTO item,Set<Long> orgIdSet,TokenUserDTO userDTO) {
    	List<OrgLevelVO> orgLevelVOList = null;
		try {
			orgLevelVOList = adjustPriceOrderV2Service.getUserActualSelectOrgDTOList(userDTO.getUserId(), item.getResourceId(), Lists.newArrayList(orgIdSet));
		} catch (Exception e) {
			item.setResult(Boolean.FALSE);
			item.setMessage("选择的组织及其子组织都没有权限");
		}
		if(CollectionUtils.isNotEmpty(orgLevelVOList)) {
			List<OrgLevelVO> orderOrgDetailListSort = orgLevelVOList.stream().sorted(Comparator.comparing(OrgLevelVO::getOrgId)).collect(Collectors.toList());
			List<Long> sortOrgIdList = orderOrgDetailListSort.stream().map(OrgLevelVO::getOrgId).collect(Collectors.toList());
	    	List<String> sortOrgNameList = orderOrgDetailListSort.stream().map(OrgLevelVO::getOrgName).collect(Collectors.toList());
	    	List<Integer> sortOrgLevels = orderOrgDetailListSort.stream().map(OrgLevelVO::getOrgLevel).collect(Collectors.toList());
	    	item.setOrgIds(Joiner.on(",").join(sortOrgIdList));
			item.setOrgNames(Joiner.on(",").join(sortOrgNameList));
			item.setOrgLevels(Joiner.on(",").join(sortOrgLevels));
		}
    }

    /**
     *
     * @Title: checkRowImport
     * @Description: 验证导入行数据
     * @param: @param item
     * @param: @param userDTO
     * @param: @param userStoreIdList
     * @return: void
     * @throws
     */
    private void checkRowImport(ImportAdjustPriceOrderDetailDTO item,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO,Long receiveTimeStamp) {
    	logger.info("AdjustPriceOrderDetailV2ServiceImpl|checkRowImport|userDTO:{}",JSON.toJSONString(userDTO));
    	if(null != item.getIsCopyAdjustOrderOrg() && item.getIsCopyAdjustOrderOrg()) {
    		//复制主单机构 不需要权限验证 需要把对应的门店放缓存进行交集验证
    		checkCopyAdjustOrderOrgIdAuth(item, adjustPriceOrder, receiveTimeStamp);
    	} else if(Boolean.TRUE.equals(item.getResult()) && null!=item.getGoodsOrgTabType() &&
				item.getGoodsOrgTabType().intValue()==PriceOrderTabTypeEnum.STORE_LIST.getType()) {
    		//校验机构类型=1时数据正确性
    		checkStoreListOrgIdAuth(item, adjustPriceOrder, userDTO, receiveTimeStamp);
		} else if(Boolean.TRUE.equals(item.getResult()) && null!=item.getGoodsOrgTabType() &&
				(item.getGoodsOrgTabType().intValue()==PriceOrderTabTypeEnum.STORE_GROUP.getType() ||
				item.getGoodsOrgTabType().intValue()==PriceOrderTabTypeEnum.STORE_FLAG.getType())) {
			//验证门店组、门店标签
			checkStoreGroupTagOrgIdAuth(item, adjustPriceOrder, userDTO, receiveTimeStamp);
		}
    }

    private void checkCopyAdjustOrderOrgIdAuth(ImportAdjustPriceOrderDetailDTO item,AdjustPriceOrder adjustPriceOrder,Long receiveTimeStamp) {
    	if(Boolean.TRUE.equals(item.getResult())) {
        	List<Long> orgIdList = Arrays.stream(item.getOrgIds().split(",")).map(Long::valueOf).collect(Collectors.toList());
        	List<Long> orgLevelList = Arrays.stream(item.getOrgLevels().split(",")).map(Long::valueOf).collect(Collectors.toList());
        	if(orgIdList.size()!=orgLevelList.size()) {
        		item.setResult(Boolean.FALSE);
				item.setMessage("主单机构id和机构级别不对应");
				return;
        	}
        	List<Long> storeOrgIdList = Lists.newArrayList();
        	List<Long> otherOrgIdList = Lists.newArrayList();
			for (int i = 0; i < orgLevelList.size(); i++) {
				if(orgLevelList.get(i) == OrgLevelEnum.STORE.getCode()) {
					storeOrgIdList.add(orgIdList.get(i));
				}else {
					otherOrgIdList.add(orgIdList.get(i));
				}
			}
			List<Long> storeIdList = Lists.newArrayList();
        	if(CollectionUtils.isNotEmpty(storeOrgIdList)) {
        		for (Long storeOrgId : storeOrgIdList) {
					storeIdList.add(CacheVar.storeOrgIdAndOutIdMapping.get(storeOrgId));
				}
        	}
        	if(CollectionUtils.isNotEmpty(otherOrgIdList)) {
        		List<OrgLevelVO> orgVoList = permissionExtService.listStoreInfosByOrgIds(otherOrgIdList);
        		if(CollectionUtils.isNotEmpty(orgVoList)) {
        			for (OrgLevelVO orgLevelVO : orgVoList) {
        				storeIdList.add(orgLevelVO.getOutId());
					}
        		}
        	}
        	stagingRedisadjustItemScopeStoreId(item, adjustPriceOrder, storeIdList, receiveTimeStamp);
        }
    }

    private void checkStoreListOrgIdAuth(ImportAdjustPriceOrderDetailDTO item,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO,Long receiveTimeStamp) {
    	Map<Long,OrgToRedisDTO> businessOrgIdMap = new HashMap<Long, OrgToRedisDTO>();
        Map<Long,OrgToRedisDTO> storeOrgIdMap = new HashMap<Long, OrgToRedisDTO>();
		List<String> sapCodeList = Arrays.asList(item.getOrgIdsParam().split(",")).stream().map(s -> s.trim()).collect(Collectors.toList());
		OrgToRedisDTO businessDto = null;
		OrgToRedisDTO storeDto = null;
		for (String sapCode : sapCodeList) {
			//根据sapCode分表从连锁和门店缓存取值
			businessDto = CacheVar.businessSapCodeCacheMap.get(sapCode);
			if(null != businessDto) {
				businessOrgIdMap.put(businessDto.getId(), businessDto);
			}
			storeDto = CacheVar.storeSapCodeCacheMap.get(sapCode);
			if(null != storeDto) {
				storeOrgIdMap.put(storeDto.getId(), storeDto);
			}
			if(null == businessDto && null == storeDto) {
				logger.warn("checkRowImportAdjustPriceOrderDetails|checkRowImport|根据sapCode:{},分别从连锁和门店获取不到数据,请检查是否存在",sapCode);
				item.setResult(Boolean.FALSE);
				item.setMessage("请填写正确的机构编码");
				return;
			}
		}
		Set<Long> orgIdSet = new HashSet<Long>(businessOrgIdMap.keySet());
		orgIdSet.addAll(storeOrgIdMap.keySet());
		Boolean checkUserOrg = permissionExtService.checkUserOrgPermissions(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), Lists.newArrayList(orgIdSet));
		if(!checkUserOrg) {
			item.setResult(Boolean.FALSE);
			item.setMessage("机构编码存在没有权限的数据");
			return;
		}
		//查询连锁包含的门店 验证当前行填写的orgIds是否存在上下级关系
		List<Long> storeIdList = Lists.newArrayList();
		if(MapUtils.isNotEmpty(businessOrgIdMap)) {
			List<Long> excelStoreOrgIdList = storeOrgIdMap.values().stream().map(OrgToRedisDTO::getId).collect(Collectors.toList());
			List<Long> businessOrgIdList = businessOrgIdMap.values().stream().map(OrgToRedisDTO::getId).collect(Collectors.toList());
			List<OrgDTO> orgDtoList = permissionExtService.getUserDataScopeStoreBatch(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), businessOrgIdList, 1);
			List<Long> authStoreOrgIdList = orgDtoList.stream().map(x -> x.getId()).collect(Collectors.toList());
			List<Long> intersectionList = excelStoreOrgIdList.stream().filter(s -> authStoreOrgIdList.contains(s)).collect(Collectors.toList());
			if(CollectionUtils.isNotEmpty(intersectionList)) {
				logger.warn("checkRowImportAdjustPriceOrderDetails|checkRowImport|填写的机构编码存在上下级关系,intersectionList:{}",intersectionList);
				item.setResult(Boolean.FALSE);
				item.setMessage("填写的机构编码存在上下级关系");
				return;
			}
			storeIdList.addAll(orgDtoList.stream().map(x -> x.getOutId()).collect(Collectors.toList()));
		}
		storeIdList.addAll(storeOrgIdMap.values().stream().map(OrgToRedisDTO::getOutId).collect(Collectors.toList()));
		stagingRedisadjustItemScopeStoreId(item, adjustPriceOrder, storeIdList, receiveTimeStamp);
		fillOrderDetailOrgData(item, orgIdSet, userDTO);
		storeIdList.clear();
    }

    private void checkStoreGroupTagOrgIdAuth(ImportAdjustPriceOrderDetailDTO item,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO,Long receiveTimeStamp) {
    	List<Long> tabTypeValList = Arrays.asList(item.getTabTypeVals().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		for (Long tagId : tabTypeValList) {
			String tagIdRedisKey = getStoreGroupTagRedisKey(tagId);
			if(!redissonClient.getBucket(tagIdRedisKey).isExists()) {
				item.setResult(Boolean.FALSE);
				item.setMessage("填写的门店组id、标签id在tag系统中不存在");
				return;
			}
		}
		if(Boolean.TRUE.equals(item.getResult())){
			List<Long> storeOrgIdList = Lists.newArrayList();
			RBucket<List<Long>> bucket = null;
			for (Long tagId : tabTypeValList) {
				String tagIdScopeStoreOrgIdKey = getTagIdScopeStoreOrgIdRedisKey(tagId);
				bucket = redissonClient.getBucket(tagIdScopeStoreOrgIdKey);
				storeOrgIdList.addAll(bucket.get());
			}
			if(storeOrgIdList.size()>storemaxcount) {
				logger.warn("checkRowImportAdjustPriceOrderDetails|checkRowImport|门店组id、标签id包含的门店数不能大于"+storemaxcount+",tagIds:{}",tabTypeValList);
				item.setResult(Boolean.FALSE);
				item.setMessage("门店组id、标签id包含的门店数不能大于"+storemaxcount);
				return;
			}
			Boolean checkUserOrg = permissionExtService.checkUserOrgPermissions(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), Lists.newArrayList(storeOrgIdList));
			if(!checkUserOrg) {
				item.setResult(Boolean.FALSE);
				item.setMessage("门店组id、标签id存在当前登录人权限范围外门店");
				return;
			}
			List<Long> storeIdList = Lists.newArrayList();
			for (Long storeOrgId : storeOrgIdList) {
				storeIdList.add(CacheVar.storeOrgIdAndOutIdMapping.get(storeOrgId));
			}
			Set<Long> orgIdSet = new HashSet<Long>(storeOrgIdList);
			stagingRedisadjustItemScopeStoreId(item, adjustPriceOrder, storeIdList, receiveTimeStamp);
			fillOrderDetailOrgData(item, orgIdSet, userDTO);
		}
    }

    private void stagingRedisadjustItemScopeStoreId(ImportAdjustPriceOrderDetailDTO item,AdjustPriceOrder adjustPriceOrder,List<Long> storeIdList,Long receiveTimeStamp) {
    	String uniqueId = adjustPriceOrder.getAdjustCode()+"_"+item.getGoodsNo()+"_"+item.getLineNo()+"_"+receiveTimeStamp;
		String adjustItemStoreIdRedisKey = getAdjustItemStoreIdRedisKey(uniqueId);
		redissonClient.getBucket(adjustItemStoreIdRedisKey).set(storeIdList,30,TimeUnit.MINUTES);
    }

    /**
     * 校验调价单明细导入参数，单行数据正确性
     * @param param
     */
    private void checkRowImportAdjustPriceOrderDetails(AdjustPriceOrderDetailImportV2Param param,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO) {

    	List<ImportAdjustPriceOrderDetailDTO> importList = param.getAdjustPriceOrderDetailDTOList();
    	if(CollectionUtils.isEmpty(importList)) {
    		return;
    	}
    	Long receiveTimeStamp = param.getReceiveTimeStamp();
    	//门店组 返回excel中门店组id、门店标签对应门店组数据
    	storeGroupTagInfo(adjustPriceOrder,importList, userDTO);

    	//门店组id和门店标签id 对应的storeOrgId
    	storeGroupTagMapperStoreOrgIdByTagId(adjustPriceOrder, importList, userDTO);

    	//goodsOrgTabType=1时  验证录入的orgIds是否存在当前登录人权限连锁、门店里
    	CountDownLatch countDownLatch = new CountDownLatch(importList.size());
    	importList.forEach(item -> {
    		adjustOrderDetailImportThreadExecutor.execute(() -> {
				try {
					if(GoodsScopeEnum.SENSITIVE_GOODS.getCode()==adjustPriceOrder.getGoodsScope()) {
						checkSensitiveGoodsOrgInfo(item, adjustPriceOrder, userDTO,receiveTimeStamp);
		        	}else {
		        		checkRowImport(item,adjustPriceOrder, userDTO,receiveTimeStamp);
		        	}
				}catch (Exception e){
		            logger.error("AdjustPriceOrderDetailV2ServiceImpl|checkRowImport|检查调价单导入数据异常", e);
				}finally {
                    countDownLatch.countDown();
                }
            });
    	});
    	try {
            countDownLatch.await();
        } catch (Exception e) {
            logger.error("AdjustPriceOrderDetailV2ServiceImpl|checkRowImportAdjustPriceOrderDetails|checkRowImport, adjustCode:{}", adjustPriceOrder.getAdjustCode(),e);
        }
    }

    private void copyAdjustPriceOrderBaseConfigOrgId(AdjustPriceOrderDetailImportV2Param param,AdjustPriceOrder adjustPriceOrder) {
    	List<ImportAdjustPriceOrderDetailDTO> importList = param.getAdjustPriceOrderDetailDTOList();
    	if(CollectionUtils.isEmpty(importList)) {
    		return;
    	}
    	List<ImportAdjustPriceOrderDetailDTO> copyOrderDetailList = importList.stream().filter(v -> !Boolean.FALSE.equals(v.getResult()) &&
    			null==v.getGoodsOrgTabType()).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(copyOrderDetailList)) {
			return;
		}
    	//填充复制主单机构
    	GoodsNoOrgIdsDTO goodsNoOrgIdsDTO = copyAdjustOrderConfig(adjustPriceOrder);
    	//取出orgId门店
    	for (ImportAdjustPriceOrderDetailDTO item : importList) {
    		if(Boolean.TRUE.equals(item.getResult()) && null==item.getGoodsOrgTabType()) {
    			item.setOrgIds(goodsNoOrgIdsDTO.getOrgIds());
    			item.setGoodsOrgTabType(goodsNoOrgIdsDTO.getGoodsOrgTabType());
    			item.setOrgNames(goodsNoOrgIdsDTO.getOrgNames());
    			item.setOrgLevels(goodsNoOrgIdsDTO.getOrgLevels());
    			item.setTabTypeVals(goodsNoOrgIdsDTO.getGoodsTabTypeValue());
    			item.setIsCopyAdjustOrderOrg(true);
    		}
		}
    }


    /**
     *
     * @Title: checkMoreRowEqualGoodsNoIntersectionImportDetails
     * @Description: 验证excel中多行是否交集
     * @param: @param param
     * @param: @param userDTO
     * @return: void
     * @throws
     */
    private void checkMoreRowEqualGoodsNoIntersection(AdjustPriceOrderDetailImportV2Param param,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO) {
    	List<ImportAdjustPriceOrderDetailDTO> importList = param.getAdjustPriceOrderDetailDTOList();
    	if(CollectionUtils.isEmpty(importList)) {
    		return;
    	}

    	//excel中数据自行校验交集
    	Map<String, List<ImportAdjustPriceOrderDetailDTO>> excelGoodsNoMap = importList.stream().filter(x -> Boolean.TRUE.equals(x.getResult())).collect(Collectors.groupingBy(e -> e.getGoodsNo()+"_"+e.getSkuId()));
    	if(MapUtils.isNotEmpty(excelGoodsNoMap)) {
    		for (Map.Entry<String, List<ImportAdjustPriceOrderDetailDTO>> entry : excelGoodsNoMap.entrySet()) {
    			List<ImportAdjustPriceOrderDetailDTO> orderDetailList = entry.getValue();
    			if(orderDetailList.size()==1) {
    				continue;
    			}
    			for (ImportAdjustPriceOrderDetailDTO detailOut : orderDetailList) {
        			String detailOutUniqueId = adjustPriceOrder.getAdjustCode()+"_"+detailOut.getGoodsNo()+"_"+detailOut.getLineNo()+"_"+param.getReceiveTimeStamp();
        			String detailOutKey = getAdjustItemStoreIdRedisKey(detailOutUniqueId);
        			RBucket<List<Long>> detailOutBucket = redissonClient.getBucket(detailOutKey);
        			List<Long> outOrgIdsScopeStoreId = detailOutBucket.get();
    				for (ImportAdjustPriceOrderDetailDTO detailIn : orderDetailList) {
    					if(Objects.equals(detailOut.getLineNo(), detailIn.getLineNo())) {
    						continue;
    					}
	        			String detailInUniqueId = adjustPriceOrder.getAdjustCode()+"_"+detailIn.getGoodsNo()+"_"+detailIn.getLineNo()+"_"+param.getReceiveTimeStamp();
	        			String detailInKey = getAdjustItemStoreIdRedisKey(detailInUniqueId);
	        			RBucket<List<Long>> detailInKeyBucket = redissonClient.getBucket(detailInKey);
	        			List<Long> inOrgIdsScopeStoreId = detailInKeyBucket.get();
						List<Long> intersectionList = outOrgIdsScopeStoreId.stream().filter(s -> inOrgIdsScopeStoreId.contains(s)).collect(Collectors.toList());
						if(CollectionUtils.isNotEmpty(intersectionList)) {
							logger.warn("checkRowImportAdjustPriceOrderDetails|checkMoreRowEqualGoodsNoIntersection|相同商品、机构编码存在交集（重复）。商品编码:{},mintersectionList:{}",entry.getKey(),intersectionList);
							detailIn.setResult(Boolean.FALSE);
                            String msg = detailIn.getMessage();
                            if (msg == null || "null".equals(msg.toLowerCase()) || "".equals(msg)) {
                                msg = "";
                            } else {
                                msg = msg + ";";
                            }
							detailIn.setMessage(msg+detailIn.getGoodsNo()+"相同商品、机构编码存在交集（重复），请修改");
						}
    				}
				}
	    	}
    		excelGoodsNoMap = null;
    	}
    }

    /**
     * 价格不在最低限价和最高限价之间，则需要特殊审核
     * @param price
     * @param lowerLimit
     * @param upperLimit
     * @return
     */
    private boolean isNeedSpecialAudit(BigDecimal price, BigDecimal lowerLimit, BigDecimal upperLimit) {
        boolean result = false;
        if (price != null && lowerLimit != null && upperLimit != null) {
            result = price.compareTo(lowerLimit) < 0 || price.compareTo(upperLimit) > 0;
        }
        return result;
    }


    /**
     * 批量导调价单明细
     * @param importAdjustPriceOrder
     * @param userDTO
     */
    private void batchAddAdjustPriceOrderDetailsForImport(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO) {
        Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(importAdjustPriceOrder.getAdjustPriceOrderId());
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(adjustPriceOrder.getAdjustPriceType());
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(adjustPriceOrder.getChannel());
        List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(adjustPriceOrder.getChannel());
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());
        checkImportPriceTypeCode(priceTypeCodeList,importAdjustPriceOrder);
		/*List<String> toAddGoodsNoList = importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList().stream()
		    .filter(v -> !Boolean.FALSE.equals(v.getResult())).map(ImportAdjustPriceOrderDetailDTO::getGoodsNo).collect(Collectors.toList());*/
        //验证是否有效
        List<GoodsNoOrgIdsDTO> goodsNoOrgIdList = createGoodsNoOrgIdsForImport(importAdjustPriceOrder, adjustPriceOrder, userDTO);

        if(CollectionUtils.isEmpty(goodsNoOrgIdList)) {
        	logger.info("AdjustPriceOrderDetailV2ServiceImpl|batchAddAdjustPriceOrderDetailsForImport没有有效的数据|goodsNoOrgIdList:{}",JSON.toJSONString(goodsNoOrgIdList));
        	return;
        }
        List<AdjustPriceOrderDetail> detailList = getInsertAdjustPriceOrderDetailList(adjustPriceOrder, goodsNoOrgIdList, priceChannelMap, priceTypeMap,
            channelIdList, priceTypeCodeList, userDTO, operateTime);
        setPriceTypeDataForInsert(detailList,importAdjustPriceOrder);
        transactionTemplate.execute(ts -> {
        	Lists.partition(detailList, 200).forEach(subDetailList -> adjustPriceOrderDetailExMapper.batchInsert(subDetailList));
			return Boolean.TRUE;
		});
        Lists.partition(detailList, 100).forEach(subDetailList -> sendToSupplementDataMsgs(importAdjustPriceOrder.getIsPortal(), subDetailList,Boolean.TRUE));
    }

    /**
     *
     * @Title: createGoodsNoOrgIdsForImport
     * @Description: 导入商品组装商品+orgIds数据
     * @param: @param param
     * @param: @return
     * @return: List<GoodsNoOrgIdsDTO>
     * @throws
     */
    private List<GoodsNoOrgIdsDTO> createGoodsNoOrgIdsForImport(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO){
    	List<GoodsNoOrgIdsDTO> goodsNoOrgIdsList = Lists.newArrayList();
    	List<ImportAdjustPriceOrderDetailDTO> importList = importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList().stream().filter(v -> !Boolean.FALSE.equals(v.getResult())).collect(Collectors.toList());
    	importList.forEach(item -> {
    		GoodsNoOrgIdsDTO goodsNoOrgIds = new GoodsNoOrgIdsDTO();
    		goodsNoOrgIds.setAdjustCode(adjustPriceOrder.getAdjustCode());
    		goodsNoOrgIds.setGoodsNo(item.getGoodsNo());
    		if(StringUtils.isNotBlank(item.getSkuId())) {
    			goodsNoOrgIds.setItemSkuId(item.getSkuId());
    		}
    		goodsNoOrgIds.setGoodsOrgTabType(item.getGoodsOrgTabType());
    		goodsNoOrgIds.setOrgIds(item.getOrgIds());
    		goodsNoOrgIds.setOrgNames(item.getOrgNames());
    		goodsNoOrgIds.setOrgLevels(item.getOrgLevels());
    		goodsNoOrgIds.setGoodsTabTypeValue(item.getTabTypeVals());
    		goodsNoOrgIds.setOverrideLowerLevel(item.getOverrideLowerLevel());
    		goodsNoOrgIds.setCopyOrderOrgTabType(true);
    		goodsNoOrgIdsList.add(goodsNoOrgIds);
    	});

        return goodsNoOrgIdsList;
    }

    /**
     * 填充新价格信息
     * @param insertAdjustPriceOrderDetailList
     * @param importAdjustPriceOrder
     */
    private void setPriceTypeDataForInsert(List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList, AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder) {
        Map<String, ImportAdjustPriceOrderDetailDTO> detailDTOMap = importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList().stream()
            .filter(v -> !Boolean.FALSE.equals(v.getResult())).collect(Collectors.toMap(k -> k.getGoodsNo()+"_"+k.getOrgIds()+"_"+k.getSkuId(), Function.identity(), (key1, key2) -> key2));
        importAdjustPriceOrder.getAdjustPriceOrderDetailDTOList().stream().forEach(v->{
            logger.info("单条数据导入结果detailDTO={}",v);
        });
        insertAdjustPriceOrderDetailList.stream().forEach(v->{
            ImportAdjustPriceOrderDetailDTO detailDTO = detailDTOMap.get(v.getGoodsNo()+"_"+v.getOrgIds()+"_"+v.getSkuId());
            if (Objects.nonNull(detailDTO)){
                BigDecimal rebate = null;
                v.setPriceFlag(detailDTO.getPriceFlag());
                v.setComment(detailDTO.getGoodsType());
                v.setLabel(detailDTO.getActivityType());
                v.setReason(detailDTO.getAdjustReason()==null?"":detailDTO.getAdjustReason().trim());
                if (PriceTypeModeEnum.CLJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPriceCLJ());
                }
                if (PriceTypeModeEnum.LSJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPriceLSJ());
                }
                if (PriceTypeModeEnum.HYJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPriceHYJ());
                    rebate = detailDTO.getRebateHYJ();
                }
                if (PriceTypeModeEnum.CHYJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPriceCHYJ());
                }
                if (PriceTypeModeEnum.PDDDMJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPricePDDDMJ());
                }
                if (PriceTypeModeEnum.PDDPTJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPricePDDPTJ());
                }
                if (PriceTypeModeEnum.MTLSJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPriceMTLSJ());
                }
                if (PriceTypeModeEnum.DYLSJ.getPriceTypeCode().equals(v.getPriceTypeCode())){
                    v.setPrice(detailDTO.getPriceDYLSJ());
                }
                if (null!= v.getPrice()) {
                	Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(v.getExtend1());
                	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = null;
                    if (detailExtend1Optional.isPresent()) {
                    	adjustPriceOrderDetailExtend1 = detailExtend1Optional.get();
                    } else {
                    	adjustPriceOrderDetailExtend1 = AdjustPriceOrderDetailExtend1.getInstance();
                    }
                    adjustPriceOrderDetailExtend1.setDataCompletion(DataCompletionEnum.NOT_COMPLETE.getCode());
                    adjustPriceOrderDetailExtend1.setDataFull(DataFullEnum.HAS_FULL.getCode());
                    adjustPriceOrderDetailExtend1.setChannelMappingStoreId(detailDTO.getChannelMappingStoreId());
                    adjustPriceOrderDetailExtend1.setRebate(rebate);
                    v.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
                    //v.setExtend1(AdjustPriceOrderDetailExtend1.getJSONFormatStr(DataCompletionEnum.NOT_COMPLETE.getCode(), DataFullEnum.HAS_FULL.getCode()));
                }
            }
        });
    }

    /**
     * 校验导入数: 是否缺失价格类型
     * @param priceTypeCodeList
     * @param param
     */
    private void checkImportPriceTypeCode(List<String> priceTypeCodeList, AdjustPriceOrderDetailImportV2Param param) {
        param.getAdjustPriceOrderDetailDTOList().stream().filter(v->!Boolean.FALSE.equals(v.getResult())).forEach(v->{
            List<String> importDataPriceTypeCode=new ArrayList<>();
            if(null!=v.getPriceCLJ()){
                importDataPriceTypeCode.add(PriceTypeModeEnum.CLJ.getPriceTypeCode());
            }
            if(null!=v.getPriceLSJ()){
                importDataPriceTypeCode.add(PriceTypeModeEnum.LSJ.getPriceTypeCode());
            }
            if(null!=v.getPriceHYJ()){
                importDataPriceTypeCode.add(PriceTypeModeEnum.HYJ.getPriceTypeCode());
            }
            if(null!=v.getPriceCHYJ()){
                importDataPriceTypeCode.add(PriceTypeModeEnum.CHYJ.getPriceTypeCode());
            }
            if(null!=v.getPricePDDDMJ()){
                importDataPriceTypeCode.add(PriceTypeModeEnum.PDDDMJ.getPriceTypeCode());
            }
            if(null!=v.getPricePDDPTJ()) {
                importDataPriceTypeCode.add(PriceTypeModeEnum.PDDPTJ.getPriceTypeCode());
            }
            if(null!=v.getPriceMTLSJ()) {
            	importDataPriceTypeCode.add(PriceTypeModeEnum.MTLSJ.getPriceTypeCode());
            }
            if(null!=v.getPriceDYLSJ()) {
            	importDataPriceTypeCode.add(PriceTypeModeEnum.DYLSJ.getPriceTypeCode());
            }
            String missPriceTypeCode = priceTypeCodeList.stream().filter(x -> !importDataPriceTypeCode.contains(x)).findFirst().orElse(null);
            if(StringUtils.isNotBlank(missPriceTypeCode) && PriceTypeModeEnum.getByPriceCode(missPriceTypeCode).isPresent()){
                String priceTypeName = PriceTypeModeEnum.getByPriceCode(missPriceTypeCode).get().getPriceTypeName();
                v.setResult(Boolean.FALSE);
                v.setMessage(priceTypeName.concat("新价格字段不能为空"));
            }
        });
    }

    /**
     * 批量新增调价单明细
     * @param param
     * @param userDTO
     */
    private void batchAddAdjustPriceOrderDetails(AdjustPriceOrderDetailAddV2Param param, TokenUserDTO userDTO) {
        Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustPriceOrderId());
        basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);
        basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);

        List<GoodsNoOrgIdsDTO> goodsNoOrgIdsList = createGoodsNoOrgIds(param,adjustPriceOrder,userDTO);
        Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null!=storeId && CollectionUtils.isNotEmpty(goodsNoOrgIdsList)){
            goodsNoOrgIdsList.forEach(v->{
                v.setAdjustDetailReason(AdjustTypeEnum.JM_SHICHANG_TYPE.getMessage());
                v.setPrice(new BigDecimal(Constants.ERROR_CODE_DEFAULT));
            });
        }
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(adjustPriceOrder.getAdjustPriceType());
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(adjustPriceOrder.getChannel());
        List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(adjustPriceOrder.getChannel());
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());
        List<AdjustPriceOrderDetail> insertAdjustPriceOrderDetailList = getInsertAdjustPriceOrderDetailList(adjustPriceOrder, goodsNoOrgIdsList, priceChannelMap, priceTypeMap,
            channelIdList, priceTypeCodeList, userDTO, operateTime);
        Lists.partition(insertAdjustPriceOrderDetailList, 200)
            .forEach(insertPartitionAdjustPriceOrderDetailList -> adjustPriceOrderDetailExMapper.batchInsert(insertPartitionAdjustPriceOrderDetailList));
        //事务提交后执行
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
            	sendToSupplementDataMsgs(param.getIsPortal(), insertAdjustPriceOrderDetailList);
            }
        });
    }
    /**
    *
    * @Title: createGoodsNoOrgIds
    * @Description: 组装商品+orgIds数据
    * @param: @param param
    * @param: @return
    * @return: List<GoodsNoOrgIdsDTO>
    * @throws
    */
   private List<GoodsNoOrgIdsDTO> createGoodsNoOrgIds(AdjustPriceOrderDetailAddV2Param param,AdjustPriceOrder adjustPriceOrder,TokenUserDTO userDTO){
	    if(CollectionUtils.isNotEmpty(param.getGoodsNoOrgList())) {
		    return param.getGoodsNoOrgList();
	    }
	   	List<GoodsNoOrgIdsDTO> goodsNoOrgIdsList = Lists.newArrayList();
	   	Integer goodsOrgTabType = adjustPriceOrder.getAdjustOrgTabType();
	   	Map<String, List<Long>> goodsNoMapperStoreIdsMap = param.getGoodsNoMapperStoreIdsMap();
	   	if(null!=param && MapUtils.isNotEmpty(goodsNoMapperStoreIdsMap)) {
	   		GoodsNoOrgIdsDTO goodsNoOrgIdsDTO = null;
	   		for (String goodsNo : param.getGoodsNoList()) {
	   			List<Long> storeIdList = goodsNoMapperStoreIdsMap.get(goodsNo).stream().distinct().collect(Collectors.toList());
	   			List<Long> storeOrgIdList = permissionExtService.listOrgIdByOutId(storeIdList, OrgTypeEnum.STORE.getCode());
	   			List<String> storeOrgNameList = new ArrayList<String>();
	   			List<Integer> storeOrgLevelList = new ArrayList<Integer>();
	   			for (int i = 0; i < storeOrgIdList.size(); i++) {
	   				storeOrgNameList.add(CacheVar.getStoreName(CacheVar.storeOrgIdAndOutIdMapping.get(storeOrgIdList.get(i))));
	   				storeOrgLevelList.add(OrgLevelTypeEnum.STORE.getCode());
				}
	   			goodsNoOrgIdsDTO = new GoodsNoOrgIdsDTO();
	   			goodsNoOrgIdsDTO.setAdjustCode(adjustPriceOrder.getAdjustCode());
	   			goodsNoOrgIdsDTO.setGoodsNo(goodsNo);
	   			goodsNoOrgIdsDTO.setGoodsOrgTabType(goodsOrgTabType);
	   			goodsNoOrgIdsDTO.setOrgIds(Joiner.on(",").join(storeOrgIdList));
	   			goodsNoOrgIdsDTO.setOrgNames(Joiner.on(",").join(storeOrgNameList));
	   			goodsNoOrgIdsDTO.setOrgLevels(Joiner.on(",").join(storeOrgLevelList));
	   			goodsNoOrgIdsDTO.setGoodsTabTypeValue("");
	   			goodsNoOrgIdsDTO.setOverrideLowerLevel(OverrideLowerLevelEnum.YES.getCode());
	   			goodsNoOrgIdsDTO.setCopyOrderOrgTabType(false);
	   			Map<String, List<BigDecimal>> lowerLimits = param.getGoodsNoMapperLowerLimitMap();
	   			if(MapUtils.isNotEmpty(lowerLimits) && lowerLimits.containsKey(goodsNo)) {
	   				List<BigDecimal> lowerLimitList = param.getGoodsNoMapperLowerLimitMap().get(goodsNo);
	   				lowerLimitList.sort(Comparator.reverseOrder());
	   				goodsNoOrgIdsDTO.setPrice(lowerLimitList.get(0));
	   			}
	   			goodsNoOrgIdsDTO.setAdjustDetailReason(param.getAdjustDetailReason());
	   			goodsNoOrgIdsList.add(goodsNoOrgIdsDTO);
			}
	   		return goodsNoOrgIdsList;
	   	}

        //验证是否复制主单机构
        if(checkCopyAdjustPriceOrderOrg(param.getOrgTabType())) {
	       	GoodsNoOrgIdsDTO goodsNoOrgIdsParam = copyAdjustOrderConfig(adjustPriceOrder);
	       	goodsNoOrgIdsParam.setOverrideLowerLevel(OverrideLowerLevelEnum.YES.getCode());
	       	goodsNoOrgIdsParam.setCopyOrderOrgTabType(true);
	   		goodsNoOrgIdsList = createGoodsNoOrgIdsDTO(param.getGoodsNoList(), goodsNoOrgIdsParam);
        }else {
	       	List<OrgLevelVO> orgLevelVOList = adjustPriceOrderV2Service.getUserActualSelectOrgDTOList(userDTO.getUserId(), param.getResourceId(), param.getOrgIdList());
	       	List<OrgLevelVO> orderOrgDetailListSort = orgLevelVOList.stream().sorted(Comparator.comparing(OrgLevelVO::getOrgId)).collect(Collectors.toList());
	       	List<Long> orgIdList = orderOrgDetailListSort.stream().map(OrgLevelVO::getOrgId).collect(Collectors.toList());
	       	String orgIdsStr = Joiner.on(",").join(orgIdList);
	       	String goodsTabTypeValues = "";
	       	if(PriceOrderTabTypeEnum.STORE_LIST.getType() != param.getOrgTabType()) {
	       		goodsTabTypeValues = Joiner.on(",").join(param.getTabTypeValList());
			}
	       	GoodsNoOrgIdsDTO goodsNoOrgIdsParam = new GoodsNoOrgIdsDTO(adjustPriceOrder.getAdjustCode(),"",goodsOrgTabType,
	   				orgIdsStr,"","",goodsTabTypeValues,OverrideLowerLevelEnum.YES.getCode(),false,param.getPrice(), param.getAdjustDetailReason());
	   		goodsNoOrgIdsList = createGoodsNoOrgIdsDTO(param.getGoodsNoList(), goodsNoOrgIdsParam);
        }
        return goodsNoOrgIdsList;
   }

   private GoodsNoOrgIdsDTO copyAdjustOrderConfig(AdjustPriceOrder adjustPriceOrder) {
	   	AdjustPriceOrderOrgDetailExample orgDetailExample = new AdjustPriceOrderOrgDetailExample();
	   	orgDetailExample.createCriteria().andAdjustCodeEqualTo(adjustPriceOrder.getAdjustCode());
	   	List<AdjustPriceOrderOrgDetail> orderOrgDetailList = adjustPriceOrderOrgDetailService.selectByExample(orgDetailExample);
	   	List<AdjustPriceOrderOrgDetail> orderOrgDetailListSort = orderOrgDetailList.stream().sorted(Comparator.comparing(AdjustPriceOrderOrgDetail::getOrgId)).collect(Collectors.toList());
	   	List<Long> orgIdList = orderOrgDetailListSort.stream().map(AdjustPriceOrderOrgDetail::getOrgId).collect(Collectors.toList());
	   	String orgIdsStr = Joiner.on(",").join(orgIdList);
	   	List<String> orgNameList = orderOrgDetailListSort.stream().filter(x -> x.getOrgName() !=null).map(AdjustPriceOrderOrgDetail::getOrgName).collect(Collectors.toList());
	   	String orgNameStr = Joiner.on(",").join(orgNameList);
	   	List<Integer> orgLevelList = orderOrgDetailListSort.stream().map(AdjustPriceOrderOrgDetail::getOrgLevel).collect(Collectors.toList());
		String orgLevelsStr = Joiner.on(",").join(orgLevelList);
		String goodsTabTypeValues = "";
		Integer goodsOrgTabType = adjustPriceOrder.getAdjustOrgTabType();
		//门店组、门店标签
		if(PriceOrderTabTypeEnum.STORE_GROUP.getType() == adjustPriceOrder.getAdjustOrgTabType() ||
				PriceOrderTabTypeEnum.STORE_FLAG.getType() == adjustPriceOrder.getAdjustOrgTabType()) {
			PriceOrderTabTypeDetailExample tabTypeDetailExample = new PriceOrderTabTypeDetailExample();
	   		tabTypeDetailExample.createCriteria().andAdjustOrderCodeEqualTo(adjustPriceOrder.getAdjustCode());
	   		List<PriceOrderTabTypeDetail> tabTypeDetailList = priceOrderTabTypeDetailService.selectByExample(tabTypeDetailExample);
	   		PriceOrderTabTypeDetail tabTypeDetail = tabTypeDetailList.get(0);
	   		goodsOrgTabType = tabTypeDetail.getOrgTabType();
	   		goodsTabTypeValues = tabTypeDetail.getTabTypeValue();
		}
		return new GoodsNoOrgIdsDTO(adjustPriceOrder.getAdjustCode(),"",goodsOrgTabType,
				orgIdsStr,orgNameStr,orgLevelsStr,goodsTabTypeValues,OverrideLowerLevelEnum.YES.getCode(),true);
   }

   private List<GoodsNoOrgIdsDTO> createGoodsNoOrgIdsDTO(List<String> goodsNoList,GoodsNoOrgIdsDTO bean) {
	   	List<GoodsNoOrgIdsDTO> list = Lists.newArrayList();
	   	GoodsNoOrgIdsDTO goodsNoOrgIds = null;
	   	for (String goodsNo : goodsNoList) {
	   		goodsNoOrgIds = new GoodsNoOrgIdsDTO();
	   		BeanUtils.copyProperties(bean, goodsNoOrgIds);
	   		goodsNoOrgIds.setGoodsNo(goodsNo);
	   		goodsNoOrgIds.setCopyOrderOrgTabType(bean.isCopyOrderOrgTabType());
	   		list.add(goodsNoOrgIds);
		}
	   	return list;
   }

    private AdjustPriceOrderDetail copyFromPriceTypeOrderDetail(AdjustPriceOrderDetail adjustPriceOrderDetail, Date operateTime) {
        AdjustPriceOrderDetail result = new AdjustPriceOrderDetail();
        result.setAdjustCode(adjustPriceOrderDetail.getAdjustCode());
        result.setAuthOrgId(adjustPriceOrderDetail.getAuthOrgId());
        result.setAuthOrgName(adjustPriceOrderDetail.getAuthOrgName());
        result.setAuthLevel(adjustPriceOrderDetail.getAuthLevel());
        result.setSpuId(adjustPriceOrderDetail.getSpuId());
        result.setGoodsNo(adjustPriceOrderDetail.getGoodsNo());
        result.setSkuId(adjustPriceOrderDetail.getSkuId());
        result.setGoodsName(adjustPriceOrderDetail.getGoodsName());
        result.setGoodsUnit(adjustPriceOrderDetail.getGoodsUnit());
        result.setProdarea(adjustPriceOrderDetail.getProdarea());
        result.setPriceTypeId(adjustPriceOrderDetail.getPriceTypeId());
        result.setPriceTypeCode(adjustPriceOrderDetail.getPriceTypeCode());
        result.setPriceTypeName(adjustPriceOrderDetail.getPriceTypeName());
        result.setPrice(adjustPriceOrderDetail.getPrice());
        result.setAdjustType(adjustPriceOrderDetail.getAdjustType());
        result.setOriginalPrice(adjustPriceOrderDetail.getOriginalPrice());
        result.setUpperLimit(adjustPriceOrderDetail.getUpperLimit());
        result.setLowerLimit(adjustPriceOrderDetail.getLowerLimit());
        result.setCurName(adjustPriceOrderDetail.getCurName());
        result.setManufacturer(adjustPriceOrderDetail.getManufacturer());
        result.setJhiSpecification(adjustPriceOrderDetail.getJhiSpecification());
        result.setDosage(adjustPriceOrderDetail.getDosage());
        result.setStatus(adjustPriceOrderDetail.getStatus());
        result.setGmtCreate(operateTime);
        result.setCreatedBy(adjustPriceOrderDetail.getCreatedBy());
        result.setExtend1(adjustPriceOrderDetail.getExtend1());
        result.setVersion(1);
        result.setComment(adjustPriceOrderDetail.getComment());
        result.setLabel(adjustPriceOrderDetail.getLabel());
        result.setReason(adjustPriceOrderDetail.getReason());
        result.setGoodsName(adjustPriceOrderDetail.getGoodsName());
        result.setGuidePrice(adjustPriceOrderDetail.getGuidePrice());
        result.setUpdatedBy(adjustPriceOrderDetail.getUpdatedBy());
        result.setUpdatedByName(adjustPriceOrderDetail.getUpdatedByName());
        result.setGmtUpdate(operateTime);
        result.setAdjustPriceVersion(adjustPriceOrderDetail.getAdjustPriceVersion());
        return result;
    }

    /**
     * 获取单体加盟版定调价 门店id
     * @param adjustPriceOrder
     * @return
     */
    @Override
    public Long getSingleStoreFranchisePricingByStoreId(AdjustPriceOrder adjustPriceOrder){
        String extend = adjustPriceOrder.getExtend();
        if(StringUtils.isBlank(extend)){
            return null;
        }
        Optional<AdjustPriceOrderExtend> instance = AdjustPriceOrderExtend.getInstance(extend);
        if(instance.isPresent()) {
            AdjustPriceOrderExtend adjustPriceOrderExtend = instance.get();
            if(CommonEnums.SourceEnum.HD.getCode().equals(adjustPriceOrderExtend.getSource()) && null!=adjustPriceOrderExtend.getStoreId()){
                return adjustPriceOrderExtend.getStoreId();
            }
        }else {
            return null;
        }
        return null;
    }

    /**
     * 补充单体加盟版定调价明细数据
     * @param storeId
     * @param adjustPriceOrderDetailList
     */
    private void supplementSingleStoreFranchisePricingDetailData(Long storeId, List<AdjustPriceOrderDetail> adjustPriceOrderDetailList) {
        // 参数校验
        if (storeId == null || CollectionUtils.isEmpty(adjustPriceOrderDetailList)) {
            return;
        }
        // 提取商品编号列表
        List<String> goodsNoList = adjustPriceOrderDetailList.stream()
            .map(AdjustPriceOrderDetail::getGoodsNo)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(goodsNoList)) {
            return;
        }
        // 获取门店商品信息映射
        Map<String, JoinStoreGoodsInfoDTO> joinStoreGoodsMap = getJoinStoreGoodsMap(storeId, goodsNoList);
        if (MapUtils.isEmpty(joinStoreGoodsMap)) {
            return;
        }
        // 补充详情数据
        for (AdjustPriceOrderDetail detail : adjustPriceOrderDetailList) {
            String goodsNo = detail.getGoodsNo();
            if (StringUtils.isBlank(goodsNo) || !joinStoreGoodsMap.containsKey(goodsNo)) {
                continue;
            }
            JoinStoreGoodsInfoDTO joinStoreGoods = joinStoreGoodsMap.get(goodsNo);
            // 设置基本商品信息
            detail.setAvailableInventory(joinStoreGoods.getAvailableInventory());
            detail.setCostPrice(joinStoreGoods.getCostPrice());
            detail.setPushClassLevel(joinStoreGoods.getPushClassLevel());
            detail.setBusinessAttribute(joinStoreGoods.getBusinessAttribute());
            detail.setSpecialAttribute(joinStoreGoods.getSpecialAttribute());
            detail.setDosage(joinStoreGoods.getDosage());
            // 处理DTP商品扩展信息
            if (joinStoreGoods.getDtpGoods() != null) {
                AdjustPriceOrderDetailExtend1 detailExtend = AdjustPriceOrderDetailExtend1
                    .getInstance(detail.getExtend1())
                    .orElseGet(AdjustPriceOrderDetailExtend1::getInstance);
                boolean isDtpGoods = YNEnum.YES.getType().equals(joinStoreGoods.getDtpGoods());
                String dtpGoodsDesc = isDtpGoods ? YNEnum.YES.getDesc() : YNEnum.NO.getDesc();
                detailExtend.setDtpGood(dtpGoodsDesc);
                detail.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(detailExtend));
            }
        }
    }

    /**
     * 获取门店商品信息映射
     */
    private Map<String, JoinStoreGoodsInfoDTO> getJoinStoreGoodsMap(Long storeId, List<String> goodsNoList) {
        PriceOrgGoodsParam param = new PriceOrgGoodsParam();
        param.setStoreId(storeId);
        param.setGoodsNoList(goodsNoList);

        ResponseEntity<List<JoinStoreGoodsInfoDTO>> responseEntity = erpBizSupportService.getJoinStoreGoodsList(param);
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK ||
            CollectionUtils.isEmpty(responseEntity.getBody())) {
            return Collections.emptyMap();
        }

        return responseEntity.getBody().stream()
            .collect(Collectors.toMap(
                JoinStoreGoodsInfoDTO::getGoodsNo,
                Function.identity(),
                (existing, replacement) -> existing
            ));
    }

    private PageResult<Map<String, Object>> listAdjustPriceOrderDetailsPage(AdjustPriceOrder adjustPriceOrder, AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO, boolean isCanEdit, boolean isAudit) {
        Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        basePriceOrderService.checkAdjustPriceOrderMustHasProperties(adjustPriceOrder);
        param.setAdjustCode(adjustPriceOrder.getAdjustCode());
        param.setOffsetByPageAndSize();
        param.setGoodsNo(StringUtils.trimToNull(param.getGoodsNo()));
        long count = adjustPriceOrderDetailExMapper.selectOrderDetailV2ListPageGroupByGoodAndPriceTypeCount(param);
        List<Map<String, Object>> adjustPriceOrderDetailListResults = Collections.emptyList();
        if (count > PriceConstant.MIN_TOTAL_COUNT) {
            List<AdjustPriceOrderDetailSku> goodsNoOrgIdsList = adjustPriceOrderDetailExMapper.selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType(param);
            if (CollectionUtils.isNotEmpty(goodsNoOrgIdsList)) {
                List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = adjustPriceOrderDetailExMapper.selectOrderDetailV2ListGroupByGoodsAndPriceType(
                    adjustPriceOrder.getAdjustCode(), goodsNoOrgIdsList);
                //单体加盟版定调价 补充行数据
                supplementSingleStoreFranchisePricingDetailData(storeId,adjustPriceOrderDetailList);
                adjustPriceOrderDetailListResults = getAdjustPriceOrderDetailListResults(userDTO,adjustPriceOrder,adjustPriceOrderDetailList,
                    adjustPriceOrder.getAdjustPriceType(), isCanEdit,isAudit,false);
            }
        }
        List<ColumnVO> columns = new ArrayList<ColumnVO>();
        if(getB2CChannelIdList().contains(adjustPriceOrder.getChannel())) {
        	columns = getAdjustPriceOrderDetailB2CColumnVO(param.getIsPortal(),userDTO,adjustPriceOrder, isCanEdit, isAudit, param.getWorkcode());
        }else {
        	columns = getAdjustPriceOrderDetailColumnVO(param.getIsPortal(),userDTO,adjustPriceOrder, isCanEdit, isAudit, param.getWorkcode());
        	fixedColumnsHeader(columns);
        }
        if(null != param.getIsPortal() && param.getIsPortal()) {
        	dealPortalColumnVO(columns,adjustPriceOrder);
        }
        clearMatchBusinessId(adjustPriceOrderDetailListResults, userDTO);
        sortList(adjustPriceOrderDetailListResults);
        //补充医保限价提醒
        supplementaryInsuranceLimitAlert(adjustPriceOrder,adjustPriceOrderDetailListResults);
        return new PageResult<>(count, columns, adjustPriceOrderDetailListResults);
    }

    private void supplementaryInsuranceLimitAlert(AdjustPriceOrder adjustPriceOrder,List<Map<String, Object>> adjustPriceOrderDetailListResults){
        // 基础验证
        List<String> priceTypeCodeLimitList = validateAndGetPriceTypeCodeList(adjustPriceOrder);
        if(CollectionUtils.isEmpty(priceTypeCodeLimitList)){
            return;
        }

        if(CollectionUtils.isEmpty(adjustPriceOrderDetailListResults)){
            logger.debug("调价单明细结果为空|adjustCode:{}", adjustPriceOrder.getAdjustCode());
            return;
        }

        String adjustCode = adjustPriceOrder.getAdjustCode();
        logger.info("开始执行价格限制预警检查|adjustCode:{}|详情数量:{}", adjustCode, adjustPriceOrderDetailListResults.size());

        // 遍历处理每个明细
        for (Map<String, Object> map : adjustPriceOrderDetailListResults) {
            try {
                Long detailId = getDetailId(map.get("adjustDetailMergeIds").toString());
                String goodsNo = map.get("goodsNo").toString();

                // 设置默认值
                map.put("insuranceLsjPriceLimitMsg","");
                map.put("insuranceHyjPriceLimitMsg","");
                //封装调用规则引擎参数
                Map<String, Object> ruleParamMap = buildRuleParamMap(adjustCode, goodsNo, detailId, priceTypeCodeLimitList, CommonEnums.InterventionMeasuresEnum.SET_PRICING_REMINDER.getCode());
                //调用规则引擎
                PriceLimitCheckResult checkResult = getPriceLimitCheckResultFromRuleEngine(ruleParamMap);
                //调用本地规则
                //PriceLimitCheckResult checkResult = interceptMedicarePriceLimitCheck(adjustCode, goodsNo, detailId, priceTypeCodeLimitList, CommonEnums.InterventionMeasuresEnum.SET_PRICING_REMINDER.getCode());
                logger.info("supplementaryInsuranceLimitAlert|价格限制检查结果|adjustCode:{}|checkResult:{}", adjustCode, checkResult);
                // 根据检查结果设置提醒消息
                if(checkResult.isHasLsjLimit()) {
                    map.put("insuranceLsjPriceLimitMsg","注意！医保限价：" + checkResult.getLsjMinLimitPrice() + "元");
                }

                if(checkResult.isHasHyjLimit()) {
                    map.put("insuranceHyjPriceLimitMsg","注意！医保限价：" + checkResult.getHyjMinLimitPrice() + "元");
                }

            } catch (Exception e) {
                logger.error("处理价格限制预警异常|adjustCode:{}|", adjustCode, e);
            }
        }
    }

    /**
     * 从规则引擎获取价格限制检查结果
     * @param ruleParamMap 规则参数
     * @return PriceLimitCheckResult 价格限制检查结果
     */
    private PriceLimitCheckResult getPriceLimitCheckResultFromRuleEngine(Map<String, Object> ruleParamMap) {
        String adjustCode = ruleParamMap.get("adjustCode").toString();
        Long detailId = Long.valueOf(ruleParamMap.get("detailId").toString());
        PriceLimitCheckResult checkResult = new PriceLimitCheckResult();
        try {
            checkResult.setAdjustCode(adjustCode);
            checkResult.setDetailId(detailId);
            logger.info("getPriceLimitCheckResultFromRuleEngine|调用规则引擎|价格限制检查结果|ruleParamMap:{}", JSON.toJSONString(ruleParamMap));
            CommonResponse<Map<String, Object>> execute = ruleEngineClient.execute(null, null, ruleNo, ruleParamMap);
            logger.info("getPriceLimitCheckResultFromRuleEngine|调用规则引擎|价格限制检查结果||execute:{}", JSON.toJSONString(execute));

            // 检查调用是否成功
            if (execute != null && execute.isSuccess() && execute.getData() != null) {
                Map<String, Object> data = execute.getData();
                Object resultObj = data.get("result");

                if (resultObj != null) {
                    try {
                        String resultJson;
                        if (resultObj instanceof String) {
                            // 如果result是字符串格式的JSON
                            resultJson = (String) resultObj;
                        } else {
                            // 如果result是Map对象，转换为JSON字符串
                            resultJson = JSON.toJSONString(resultObj);
                        }

                        // 使用Fastjson解析为实体类
                        checkResult = JSON.parseObject(resultJson, PriceLimitCheckResult.class);
                        logger.info("getPriceLimitCheckResultFromRuleEngine|价格限制检查结果解析成功: {}", checkResult);
                        return checkResult;

                    } catch (Exception parseException) {
                        logger.error("getPriceLimitCheckResultFromRuleEngine|解析价格限制检查结果异常|adjustCode:{}|resultObj:{}", adjustCode, resultObj, parseException);
                        return checkResult;
                    }
                } else {
                    logger.warn("getPriceLimitCheckResultFromRuleEngine|价格限制检查结果为空|adjustCode:{}", adjustCode);
                    return checkResult;
                }
            } else {
                logger.error("getPriceLimitCheckResultFromRuleEngine|规则引擎调用失败|adjustCode:{}|execute:{}", adjustCode, execute);
                return checkResult;
            }

        } catch (Exception e) {
            logger.error("getPriceLimitCheckResultFromRuleEngine|调用规则引擎异常|adjustCode:{}|ruleParamMap:{}", adjustCode, ruleParamMap, e);
            return checkResult;
        }
    }

    @Override
    public FunctionResult interceptMedicarePriceLimitResult(RuleFunctionMain ruleFunctionMain) {

        logger.info("interceptMedicarePriceLimitResult|入参|ruleFunctionMain:{}", JSON.toJSONString(ruleFunctionMain));
        FunctionParam functionParam = ruleFunctionMain.getFunctionParam();
        String adjustCode = functionParam.getMathParam().get(0).toString();
        String goodsNo = functionParam.getMathParam().get(1).toString();
        Long detailId = Long.valueOf(functionParam.getMathParam().get(2).toString());
        List<String> priceTypeCodeLimitList = JSONArray.parseArray(functionParam.getMathParam().get(3).toString(),String.class);
        Integer interventionCode = Integer.valueOf(functionParam.getMathParam().get(4).toString());
        PriceLimitCheckResult priceLimitCheckResult = interceptMedicarePriceLimitCheck(adjustCode, goodsNo, detailId, priceTypeCodeLimitList, interventionCode);
        return FunctionResult.resultSuccess(functionParam.getCode(), JSON.toJSONString(priceLimitCheckResult), FunctionResultTypeEnum.FUN_RESULT_TEXT.getType());
    }

    /**
     * 获取最小限制价格
     * 只有当interventionMeasures字段包含1、3时，才返回对应价格字段的最小值
     *
     * @param priceLimitControlCacheList 价格限制缓存列表
     * @param InterventionMeasures 价格限制缓存列表
     * @param priceFieldName 价格字段名称（lsjLimitPrice 或 hyjLimitPrice）
     * @return 最小限制价格，如果不满足条件则返回null
     */
    private BigDecimal getMinLimitPrice(List<PriceLimitControlStoreCache> priceLimitControlCacheList, Integer InterventionMeasures, String priceFieldName) {
        return priceLimitControlCacheList.stream()
            // 过滤：只处理interventionMeasures包含"1"的记录
            .filter(cache -> {
                String interventionMeasures = cache.getInterventionMeasures();
                return StringUtils.isNotBlank(interventionMeasures) &&
                    Arrays.asList(interventionMeasures.split(","))
                        .stream()
                        .map(String::trim)
                        .anyMatch(measure -> measure.equals(InterventionMeasures.toString()));
            })
            // 获取对应的价格字段值
            .map(cache -> getPriceFieldValue(cache, priceFieldName))
            // 过滤掉null和小于等于0的价格
            .filter(price -> price != null && price.compareTo(BigDecimal.ZERO) > 0)
            // 获取最小值
            .min(BigDecimal::compareTo)
            .orElse(null);
    }

    @Override
    public boolean interceptMedicarePrice(AdjustPriceOrder adjustPriceOrder) {
        boolean medicalPriceInterception = false;

        // 基础验证
        List<String> priceTypeCodeLimitList = validateAndGetPriceTypeCodeList(adjustPriceOrder);
        if(CollectionUtils.isEmpty(priceTypeCodeLimitList)){
            return false;
        }

        String adjustCode = adjustPriceOrder.getAdjustCode();

        // 获取调价单明细数据（原有的分批查询逻辑保持不变）
        AdjustPriceOrderDetailListV2Param param = new AdjustPriceOrderDetailListV2Param();
        param.setAdjustCode(adjustCode);
        param.setPriceTypeCodeList(priceTypeCodeLimitList);
        param.setToPage(false);
        List<AdjustPriceOrderDetailSku> goodsNoOrgIdsList =
            adjustPriceOrderDetailExMapper.selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType(param);

        List<List<AdjustPriceOrderDetailSku>> goodsNoOrgIdsListBatch = Lists.partition(goodsNoOrgIdsList, Constants.TWO_HUNDRED);

        for (List<AdjustPriceOrderDetailSku> batchItemList : goodsNoOrgIdsListBatch) {
            List<AdjustPriceOrderDetail> adjustPriceOrderDetailList =
                adjustPriceOrderDetailExMapper.selectOrderDetailV2ListGroupByGoodsAndPriceType(adjustCode, batchItemList);

            if(CollectionUtils.isEmpty(adjustPriceOrderDetailList)){
                continue;
            }
            // 分组处理逻辑保持原样
            Map<String, List<AdjustPriceOrderDetail>> groupedMap = adjustPriceOrderDetailList.stream()
                .collect(Collectors.groupingBy(
                    detail -> detail.getGoodsNo() + Constants.MIDDLE_LINE + Md5Utils.MD5Encode(getAdjustOrgStoreDetailRedisKey(detail.getOrgIds())),
                    LinkedHashMap::new,
                    Collectors.toList()
                ));
            Map<String, Integer> medPriceLimitMap = interceptMedicarePriceLimitResult(adjustCode, groupedMap, priceTypeCodeLimitList);
            if(MapUtils.isNotEmpty(medPriceLimitMap)){
                if(medPriceLimitMap.containsValue(YNEnum.YES.getType())){
                    medicalPriceInterception = true;
                }
                batchProcessMedPriceLimitMap(adjustCode,medPriceLimitMap,Constants.THREE_HUNDRED);
            }
        }

        return medicalPriceInterception;
    }

    /**
     * 分批修改医保限价预警状态
     * @param adjustCode
     * @param medPriceLimitMap
     * @param batchSize
     */
    private void batchProcessMedPriceLimitMap(String adjustCode, Map<String, Integer> medPriceLimitMap, int batchSize) {
        // 临时存储一批数据
        Map<String, Integer> batchMap = new HashMap<>();
        int count = 0;

        for (Map.Entry<String, Integer> entry : medPriceLimitMap.entrySet()) {
            batchMap.put(entry.getKey(), entry.getValue());
            count++;

            // 如果达到批次大小，则调用方法处理并清空当前批次
            if (count % batchSize == 0) {
                adjustPriceOrderDetailExMapper.batchUpdateExtend1InsurancePriceControl(adjustCode, batchMap);
                batchMap.clear(); // 清空临时批次
            }
        }

        // 处理剩余不足一个批次的数据
        if (!batchMap.isEmpty()) {
            adjustPriceOrderDetailExMapper.batchUpdateExtend1InsurancePriceControl(adjustCode, batchMap);
        }
    }


    /**
     * 处理价格限制检查并返回拦截结果映射
     *
     * @param adjustCode 调价单编码
     * @param groupedMap 按商品和机构分组的调价明细
     * @param priceTypeCodeLimitList 价格类型限制列表
     * @return 明细ID对应的拦截状态映射 (1-拦截, 0-不拦截)
     */
    private Map<String, Integer> interceptMedicarePriceLimitResult(String adjustCode,
                                                      Map<String, List<AdjustPriceOrderDetail>> groupedMap,
                                                      List<String> priceTypeCodeLimitList) {
        Map<String, Integer> medPriceLimitMap = new HashMap<>();

        for (Map.Entry<String, List<AdjustPriceOrderDetail>> entry : groupedMap.entrySet()) {
            // 初始化所有明细为未拦截状态
            List<AdjustPriceOrderDetail> lineDetailList = entry.getValue();
            for (AdjustPriceOrderDetail lineDetail : lineDetailList){
                medPriceLimitMap.put(String.valueOf(lineDetail.getId()), YNEnum.NO.getType());
            }
            String[] split = entry.getKey().split(Constants.MIDDLE_LINE);
            String goodsNo = split[0];
            Long detailId = entry.getValue().get(0).getId();
            //封装调用规则引擎参数
            Map<String, Object> ruleParamMap = buildRuleParamMap(adjustCode, goodsNo, detailId, priceTypeCodeLimitList, CommonEnums.InterventionMeasuresEnum.PROHIBIT_PRICING_ORDER.getCode());
            //调用规则引擎
            PriceLimitCheckResult checkResult = getPriceLimitCheckResultFromRuleEngine(ruleParamMap);
            //调用本地规则
            //PriceLimitCheckResult checkResult = interceptMedicarePriceLimitCheck(adjustCode, goodsNo, detailId, priceTypeCodeLimitList, CommonEnums.InterventionMeasuresEnum.SET_PRICING_REMINDER.getCode());
            logger.info("interceptMedicarePriceLimitResult|价格限制检查结果|adjustCode:{}|checkResult:{}", adjustCode, checkResult);
            // 如果没有找到限价，跳过
            if(!checkResult.isHasLsjLimit() && !checkResult.isHasHyjLimit()) {
                continue;
            }

            // 验证每个明细的价格
            for (AdjustPriceOrderDetail detail : entry.getValue()) {
                Long id = detail.getId();
                BigDecimal price = detail.getPrice();
                String priceTypeCode = detail.getPriceTypeCode();

                boolean isIntercepted = false;

                // 检查零售价
                if(PriceTypeModeEnum.LSJ.getPriceTypeCode().equals(priceTypeCode) && checkResult.isHasLsjLimit()){
                    isIntercepted = validatePriceAgainstLimit(price, checkResult.getLsjMinLimitPrice(),
                        "零售", adjustCode, detailId, goodsNo);
                }

                // 检查会员价
                if(PriceTypeModeEnum.HYJ.getPriceTypeCode().equals(priceTypeCode) && checkResult.isHasHyjLimit()){
                    isIntercepted = validatePriceAgainstLimit(price, checkResult.getHyjMinLimitPrice(),
                        "会员", adjustCode, detailId, goodsNo);
                }

                if(isIntercepted) {
                    medPriceLimitMap.put(String.valueOf(id), YNEnum.YES.getType());
                }
            }
        }

        return medPriceLimitMap;
    }

    /**
     * 通过反射获取价格字段值
     *
     * @param cache 价格限制缓存对象
     * @param fieldName 字段名称
     * @return 价格值
     */
    private BigDecimal getPriceFieldValue(PriceLimitControlStoreCache cache, String fieldName) {
        try {
            if ("lsjLimitPrice".equals(fieldName)) {
                return cache.getLsjLimitPrice();
            } else if ("hyjLimitPrice".equals(fieldName)) {
                return cache.getHyjLimitPrice();
            }
            return null;
        } catch (Exception e) {
            logger.warn("获取价格字段值失败|fieldName:{}|cache:{}", fieldName, cache, e);
            return null;
        }
    }


    private List<String> getPriceTypeCodeLimitList(AdjustPriceOrder adjustPriceOrder){
        String adjustPriceTypes = adjustPriceOrder.getAdjustPriceType();
        if (StringUtils.isBlank(adjustPriceTypes)) {
            return Collections.emptyList();
        }
        return Arrays.stream(adjustPriceTypes.split(","))
            .map(String::trim)
            .filter(priceType -> PriceTypeModeEnum.LSJ.getPriceTypeCode().equals(priceType) || PriceTypeModeEnum.HYJ.getPriceTypeCode().equals(priceType))
            .collect(Collectors.toList());
    }

    private Long getDetailId(String adjustDetailMergeIds){
        String firstId = adjustDetailMergeIds.split(",")[0].trim();
        Long id = Long.parseLong(firstId);
        return id;
    }

    private List<String> removePortalFieldList(AdjustPriceOrder adjustPriceOrder) {
    	List<String> adjustPriceTypeList = Arrays.asList(adjustPriceOrder.getAdjustPriceType().split(","));
    	List<String> removeFieldList = Lists.newArrayList();
		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_9.getName());
		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_10.getName());
		removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_18.getName());
		for (String priceType : adjustPriceTypeList) {
			removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_9.getName()+"_"+priceType);
			removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_4.getName()+"_"+priceType);
			removeFieldList.add(AdjustPriceOrderDetailPriceTypeColumnEnum.COLUMN_5.getName()+"_"+priceType);
		}
		removeFieldList.add(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_1.getName());
		removeFieldList.add(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_2.getName());
		removeFieldList.add(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName());
		removeFieldList.add(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName());
		return removeFieldList;
    }

    /**
     * 非单店加盟调价单删除一下列头
     * @param adjustPriceOrder
     * @return
     */
    private List<String> removeSingleStoreFieldList(AdjustPriceOrder adjustPriceOrder) {
        Long storeId = getSingleStoreFranchisePricingByStoreId(adjustPriceOrder);
        if(null != storeId){
            return Lists.newArrayList();
        }
        List<String> removeFieldList = Lists.newArrayList();
        removeFieldList.add(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_32.getName());
        removeFieldList.add(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_5.getName());
        removeFieldList.add(AdjustPriceOrderDetailLastColumnEnum.COLUMN_3.getName());
        removeFieldList.add(AdjustPriceOrderDetailLastColumnEnum.COLUMN_4.getName());
        removeFieldList.add(AdjustPriceOrderDetailLastColumnEnum.COLUMN_5.getName());
        removeFieldList.add(AdjustPriceOrderDetailLastColumnEnum.COLUMN_6.getName());
        return removeFieldList;
    }

    private void dealPortalColumnVO(List<ColumnVO> columns, AdjustPriceOrder adjustPriceOrder) {
    	List<String> removeFieldList = removePortalFieldList(adjustPriceOrder);
		String adjustReason = AdjustPriceOrderDetailLastColumnEnum.COLUMN_1.getName();
    	Iterator<ColumnVO> iterator = columns.iterator();
        while(iterator.hasNext()) {
        	ColumnVO columnVO = iterator.next();
        	if(removeFieldList.contains(columnVO.getName())) {
        		iterator.remove();
        	}
        	//调价原因设置为不必填
        	if(columnVO.getName().equals(adjustReason)) {
        		if(null!=columnVO.getQuickEdit()) {
        			columnVO.getQuickEdit().setSaveImmediately(false);
        		}
        	}
        }
    }

    //商品编码和商品名称去掉分组名称 固定列头
    private void fixedColumnsHeader(List<ColumnVO> columns) {
    	for (ColumnVO columnVO : columns) {
			if(columnVO.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_1.getName()) ||
					columnVO.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_2.getName()) ||
					columnVO.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_3.getName()) ||
					columnVO.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_4.getName()) ||
					columnVO.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_5.getName()) ||
                    columnVO.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_31.getName()) ||
                    columnVO.getName().equals(AdjustPriceOrderDetailBaseColumnEnum.COLUMN_32.getName())) {
				columnVO.setGroupName("");
				columnVO.setFixed(CommonEnums.FixedHeaderStatusEnum.Fixed_Left.getCode());
			}
		}
    }

    private void sortList(List<Map<String, Object>> resultList) {
        if (resultList == null || resultList.isEmpty()) {
            return;
        }

        final String DATETIME_FORMAT = "yy-MM-dd HH:mm:ss";
        final SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);

        Collections.sort(resultList, new Comparator<Map<String, Object>>() {
            @Override
            public int compare(Map<String, Object> o1, Map<String, Object> o2) {
                // 第一级排序：按 medicalPriceInterception 排序
                int medicalPriceCompare = compareMedicalPrice(o1, o2);
                if (medicalPriceCompare != 0) {
                    return medicalPriceCompare;
                }

                // 第二级排序：按 gmtCreate 时间降序排序
                int gmtCreateCompare = compareGmtCreate(o1, o2, sdf);
                if (gmtCreateCompare != 0) {
                    return gmtCreateCompare;
                }

                // 第三级排序：按 goodsNo 降序排序
                String goodsNo1 = String.valueOf(o1.get("goodsNo"));
                String goodsNo2 = String.valueOf(o2.get("goodsNo"));
                return goodsNo2.compareTo(goodsNo1); // 降序排序
            }
        });

        // 格式化日期
        formatDateFields(resultList, sdf, DATETIME_FORMAT);
    }

    private int compareMedicalPrice(Map<String, Object> o1, Map<String, Object> o2) {
        Object medicalPrice1 = o1.get("medicalPriceInterception");
        Object medicalPrice2 = o2.get("medicalPriceInterception");

        if (medicalPrice1 == null && medicalPrice2 == null) {
            return 0;
        } else if (medicalPrice1 == null || "".equals(String.valueOf(medicalPrice1).trim())) {
            return 1; // null 值或空字符串排在后面
        } else if (medicalPrice2 == null || "".equals(String.valueOf(medicalPrice2).trim())) {
            return -1;
        } else {
            // 如果都是数字类型，按数字比较
            if (medicalPrice1 instanceof Number && medicalPrice2 instanceof Number) {
                Double d1 = ((Number) medicalPrice1).doubleValue();
                Double d2 = ((Number) medicalPrice2).doubleValue();
                return d2.compareTo(d1); // 降序
            } else {
                // 其他情况按字符串比较
                String str1 = String.valueOf(medicalPrice1);
                String str2 = String.valueOf(medicalPrice2);
                return str2.compareTo(str1); // 降序
            }
        }
    }

    private int compareGmtCreate(Map<String, Object> o1, Map<String, Object> o2, SimpleDateFormat sdf) {
        Long gmtCreateTime1 = null;
        Long gmtCreateTime2 = null;
        try {
            Object gmtCreate1 = o1.get("gmtCreate");
            Object gmtCreate2 = o2.get("gmtCreate");
            if (gmtCreate1 != null) {
                gmtCreateTime1 = sdf.parse(gmtCreate1.toString()).getTime();
            }
            if (gmtCreate2 != null) {
                gmtCreateTime2 = sdf.parse(gmtCreate2.toString()).getTime();
            }
        } catch (ParseException e) {
            logger.error("排序时日期解析失败", e);
        }

        if (gmtCreateTime1 != null && gmtCreateTime2 != null) {
            return gmtCreateTime2.compareTo(gmtCreateTime1); // 降序排序
        } else if (gmtCreateTime1 != null) {
            return -1; // 有效时间排在前面
        } else if (gmtCreateTime2 != null) {
            return 1;
        }
        return 0;
    }

    private void formatDateFields(List<Map<String, Object>> resultList, SimpleDateFormat sdf, String targetFormat) {
        resultList.forEach(item -> {
            try {
                Object gmtCreateObj = item.get("gmtCreate");
                if (gmtCreateObj != null) {
                    Date parse = sdf.parse(gmtCreateObj.toString());
                    // 日期格式化
                    item.put("gmtCreate", DateUtils.dateToString(parse, targetFormat));
                }
            } catch (ParseException e) {
                logger.error("listAdjustPriceOrderDetailsPage|sortList|创建日期格式化失败", e);
            }
        });
    }

    private void clearMatchBusinessId(List<Map<String, Object>> adjustPriceOrderDetailListResults,TokenUserDTO userDTO) {
    	if(StringUtils.isNotBlank(displayEffectResultButton) && null!=userDTO && CollectionUtils.isNotEmpty(adjustPriceOrderDetailListResults)) {
    		List<Long> businessIdList = Arrays.asList(displayEffectResultButton.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    		if(businessIdList.contains(userDTO.getBusinessId())) {
    			for (Map<String, Object> map : adjustPriceOrderDetailListResults) {
    				map.put("lastPurchasingPrice", "");
    				map.put("lastPurchasingPrices", "");
    				map.put("refpurchprice", "");
    				map.put("refpurchprices", "");
    			}
    		}
    	}
    }

    private PageResult<OAMobileAdjustPriceOrderDetailVO> listMobileAdjustPriceOrderDetailsPage(AdjustPriceOrder adjustPriceOrder,
        AdjustPriceOrderDetailListV2Param param, TokenUserDTO userDTO) {
        param.setAdjustCode(adjustPriceOrder.getAdjustCode());
        param.setOffsetByPageAndSize();
        param.setGoodsNo(StringUtils.trimToNull(param.getGoodsNo()));
        param.setPriceTypeCodeList(Splitter.on(",").splitToList(adjustPriceOrder.getAdjustPriceType()).stream().collect(Collectors.toList()));
        long count = adjustPriceOrderDetailExMapper.selectOrderDetailV2ListPageGroupByGoodAndPriceTypeCount(param);
        List<OAMobileAdjustPriceOrderDetailVO> adjustPriceOrderDetailListResults = Collections.emptyList();
        if (count > PriceConstant.MIN_TOTAL_COUNT) {
        	Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(adjustPriceOrder.getAdjustPriceType());
        	List<AdjustPriceOrderDetailSku> goodsNoOrgIdsList = adjustPriceOrderDetailExMapper.selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType(param);
        	if (CollectionUtils.isNotEmpty(goodsNoOrgIdsList)) {
                List<AdjustPriceOrderDetail> adjustPriceOrderDetailList = adjustPriceOrderDetailExMapper.selectOrderDetailV2ListGroupByGoodsAndPriceType(
                    adjustPriceOrder.getAdjustCode(), goodsNoOrgIdsList);
                List<AdjustPriceOrderDetail> adjustPriceOrderDetailFilterGjList = adjustPriceOrderDetailList.stream().filter(orderDetail -> {
                	//过滤掉复制的价格类型
			        return priceTypeMap.containsKey(orderDetail.getPriceTypeCode());
                }).collect(Collectors.toList());
                OAMobileAdjustPriceOrderDetailVO oaDetail = null;
                OAMobileAdjustPriceOrderDetailPriceTypeVO oaPriceTypeVo = null;
                List<OAMobileAdjustPriceOrderDetailPriceTypeVO> detailPriceList = null;
                Map<String,OAMobileAdjustPriceOrderDetailVO> oaMap = new HashedMap<String, OAMobileAdjustPriceOrderDetailVO>();
                Map<String,BigDecimal> wisdomPriceMap = new HashMap<String, BigDecimal>();
                for (AdjustPriceOrderDetail orderDetail : adjustPriceOrderDetailFilterGjList) {
                	String goodsNo = orderDetail.getGoodsNo();
                	String orgIds = orderDetail.getOrgIds();
                	String key = goodsNo+"_"+orgIds;
                	String md5Key = Md5Utils.MD5Encode(key);
                	PriceType priceType = priceTypeMap.get(orderDetail.getPriceTypeCode());
                	if(oaMap.containsKey(md5Key)) {
                		oaDetail = oaMap.get(md5Key);
                		oaDetail.setAdjustDetailMergeIds(oaDetail.getAdjustDetailMergeIds()+","+orderDetail.getId());
                		detailPriceList = oaDetail.getDetailPriceList();
                	}else {
                		oaDetail = new OAMobileAdjustPriceOrderDetailVO();
                		oaDetail.setAdjustCode(orderDetail.getAdjustCode());
                    	oaDetail.setGoodsNo(orderDetail.getGoodsNo());
                    	oaDetail.setCurName(orderDetail.getCurName());
                    	oaDetail.setSpecification(orderDetail.getJhiSpecification());
                    	oaDetail.setAdjustReason(orderDetail.getReason());
                    	oaDetail.setOrgIds(orderDetail.getOrgIds());
                    	oaDetail.setOrgNames(orderDetail.getOrgNames());
                    	oaDetail.setAdjustDetailMergeIds(orderDetail.getId().toString());
                    	detailPriceList = new ArrayList<OAMobileAdjustPriceOrderDetailPriceTypeVO>();
                	}
                	Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
                	oaPriceTypeVo = new OAMobileAdjustPriceOrderDetailPriceTypeVO();
            		oaPriceTypeVo.setPriceTypeName(priceType.getName());
            		oaPriceTypeVo.setOriginalPrice(PriceUtil.getYuanStrFromFenWithNull(orderDetail.getOriginalPrice()));
            		oaPriceTypeVo.setPrice(PriceUtil.getYuanStrFromFenWithNull(orderDetail.getPrice()));
            		if(detailExtend1Optional.isPresent()) {
            			AdjustPriceOrderDetailExtend1 detailExtend1 = detailExtend1Optional.get();
            			oaPriceTypeVo.setRebateHYJ(detailExtend1.getRebate());
            		}
            		detailPriceList.add(oaPriceTypeVo);
            		oaDetail.setDetailPriceList(detailPriceList);

            		if(detailExtend1Optional.isPresent()) {

            			AdjustPriceOrderDetailExtend1 detailExtend1 = detailExtend1Optional.get();
            			oaDetail.setGrossMargin(PriceUtil.getYuanStrFromFenWithNull(detailExtend1.getGrossMargin()));
            			oaDetail.setDetailAuditMessage(detailExtend1.getDetailAuditMessage());
            			oaDetail.setImageUrl(detailExtend1.getImageUrl());
            			if(DetailAuditStatusEnum.AUDIT_PASS.getCode()==detailExtend1.getDetailAuditStatus()) {
            				oaDetail.setDetailAuditStatus(param.getAuditEdit()==true?String.valueOf(DetailAuditStatusEnum.AUDIT_PASS.isStatus()):DetailAuditStatusEnum.AUDIT_PASS.getMessage());
			    		}else{
			    			oaDetail.setDetailAuditStatus(param.getAuditEdit()==true?String.valueOf(DetailAuditStatusEnum.AUDIT_REFUSE.isStatus()):DetailAuditStatusEnum.AUDIT_REFUSE.getMessage());
			    			if(DetailAuditStatusEnum.AUDIT_REFUSE.getCode()==detailExtend1.getDetailAuditStatus()) {
			    				oaDetail.setUseAudit(false);
		        			}
			    		}

            			List<Long> nextAuditors = getNextAuditors(adjustPriceOrder);
                    	if(CollectionUtils.isNotEmpty(nextAuditors)) {
                    		if(!nextAuditors.contains(param.getWorkcode())) {
                    			oaDetail.setUseAudit(false);
                			}
                    	}else {
                    		oaDetail.setUseAudit(false);
                    	}
                    	if(adjustPriceOrder.getAuditStatus()==AuditStatusEnum.AUDIT_PASS.getCode() || adjustPriceOrder.getAuditStatus()==AuditStatusEnum.AUDIT_REJECTED.getCode()) {
                    		oaDetail.setUseAudit(false);
                    	}
                    	if(null!=detailExtend1.getWisdomPrice()) {
                    		wisdomPriceMap.put(md5Key, PriceUtil.getYuanFromFen(detailExtend1.getWisdomPrice()));
                    	}
                    	if(wisdomPriceMap.containsKey(md5Key)) {
                    		oaDetail.setWisdomPrice(wisdomPriceMap.get(md5Key));
                    	}
            		}
            		fillMobileAdjustPriceOrderDetail(orderDetail, oaDetail);
            		oaMap.put(md5Key, oaDetail);
				}
                wisdomPriceMap.clear();
                adjustPriceOrderDetailListResults = oaMap.values().stream().collect(Collectors.toList());
            }
        }
        return new PageResult<>(count, adjustPriceOrderDetailListResults);
    }


    private void fillMobileAdjustPriceOrderDetail(AdjustPriceOrderDetail orderDetail,OAMobileAdjustPriceOrderDetailVO oaDetail) {
    	Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
    	if(!instance.isPresent()) {
    		return;
    	}
    	AdjustPriceOrderDetailExtend1 extend1 = instance.get();
    	if(StringUtils.isEmpty(extend1.getBusinessIds())) {
    		return;
    	}
    	List<Long> businessIdList = Arrays.asList(extend1.getBusinessIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		Map<String, String> priceTypeReferenceConfig = getPriceTypeReferenceConfig();
		//通过adjust_code、goods_no、business_id、order_type、status
		for (Long businessId : businessIdList) {
			PriceBusinessDetailHistoryExample detailHistoryExample = new PriceBusinessDetailHistoryExample();
			detailHistoryExample.createCriteria().
			andOrderCodeEqualTo(orderDetail.getAdjustCode()).andGoodsNoEqualTo(orderDetail.getGoodsNo()).andBusinessIdEqualTo(businessId).
			andOrderTypeEqualTo(OrderTypeEnum.ORDER_TYPE_ADJUST.getTypeCode()).andStatusEqualTo((byte)0);
			List<PriceBusinessDetailHistory> hisList = priceBusinessDetailHistoryService.selectByExample(detailHistoryExample);
			if(CollectionUtils.isNotEmpty(hisList)) {
				List<PriceBusinessDetailHistory> sortLastOrderList = hisList.stream().
						filter(x -> x.getPriceTypeCode().equals(lastPurchasePriceType)).
						sorted(Comparator.comparing(PriceBusinessDetailHistory::getPrice)).
						collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(sortLastOrderList)) {
					oaDetail.setLastPurchasingPrice(sortLastOrderList.get(0).getPrice());
				}
				List<PriceBusinessDetailHistory> sortSuggestOrderList = hisList.stream().
						filter(x -> x.getPriceTypeCode().equals(suggestPriceType)).
						sorted(Comparator.comparing(PriceBusinessDetailHistory::getPrice)).
						collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(sortSuggestOrderList)) {
					oaDetail.setSuggestPrice(sortSuggestOrderList.get(0).getPrice());
				}
				List<PriceBusinessDetailHistory> sortFloorPriceOrderList = hisList.stream().
						filter(x -> x.getPriceTypeCode().equals(priceTypeReferenceConfig.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName()))).
						sorted(Comparator.comparing(PriceBusinessDetailHistory::getPrice)).
						collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(sortFloorPriceOrderList)) {
					oaDetail.setFloorPrice(sortFloorPriceOrderList.get(0).getPrice());
				}
				List<PriceBusinessDetailHistory> sortRefPurchPriceOrderList = hisList.stream().
						filter(x -> x.getPriceTypeCode().equals(priceTypeReferenceConfig.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_3.getName()))).
						sorted(Comparator.comparing(PriceBusinessDetailHistory::getPrice)).
						collect(Collectors.toList());
				if(CollectionUtils.isNotEmpty(sortRefPurchPriceOrderList)) {
					oaDetail.setRefPurchPrice(sortRefPurchPriceOrderList.get(0).getPrice());
				}
			}
		}
    }

    @Override
	public void addOrEditPriceOrderGoodsOrgDetail(PriceOrderGoodsOrgDetailV2Param param,TokenUserDTO userDTO) {
    	checkPriceOrderGoodsOrgDetailV2Param(param);
    	AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustPriceOrderId());
    	if(null==adjustPriceOrder) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}
    	List<String> goodsNoList = Lists.newArrayList();
    	//敏感品进行过滤 OrgId
    	if(GoodsScopeEnum.SENSITIVE_GOODS.getCode()==adjustPriceOrder.getGoodsScope()) {
    		for (Map<String, String> map : param.getGoodsNoMergeIds()) {
                // 获取当前Map的所有key并添加到keys列表中
    			goodsNoList.addAll(map.keySet());
            }
    		AdjustPriceOrderDetailAddV2Param detailAddparam = new AdjustPriceOrderDetailAddV2Param();
    		detailAddparam.setAdjustPriceOrderId(param.getAdjustPriceOrderId());
    		detailAddparam.setGoodsNoList(goodsNoList);
    		detailAddparam.setOrgIdList(param.getOrgIdList());
    		detailAddparam.setOrgTabType(param.getOrgTabType());
    		detailAddparam.setTabTypeValList(param.getTabTypeValList());
    		checkSensitiveGoods(adjustPriceOrder, detailAddparam, userDTO, false);
    		param.setOrgIdList(detailAddparam.getTempOrgIdList());
    	}
    	param.setOrderCode(adjustPriceOrder.getAdjustCode());
		List<OrgLevelVO> currOrgLevelVOList = adjustPriceOrderV2Service.getUserActualSelectOrgDTOList(userDTO.getUserId(), param.getResourceId(), param.getOrgIdList());
        checkAddOrEditGoodsOrgIntersectionOfSets(param,currOrgLevelVOList);
		List<Long> priceOrderDetailIds = Lists.newArrayList();
		param.getGoodsNoMergeIds().stream().forEach(m -> m.forEach((k,v) ->{
			priceOrderDetailIds.addAll(Arrays.asList(v.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList()));
		}));

		List<OrgLevelVO> orgLevelVOListSort = currOrgLevelVOList.stream().sorted(Comparator.comparing(OrgLevelVO::getOrgId)).collect(Collectors.toList());
        AdjustPriceOrderDetailExample orderDetailexample = new AdjustPriceOrderDetailExample();
        orderDetailexample.createCriteria().andAdjustCodeEqualTo(param.getOrderCode()).andIdIn(priceOrderDetailIds);
        List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailMapper.selectByExample(orderDetailexample);
		transactionTemplate.execute(ts -> {
			for (AdjustPriceOrderDetail adjustPriceOrderDetail : orderDetailList) {
				Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
				AdjustPriceOrderDetailExtend1 detailExtend1 = instance.get();
				detailExtend1.setDataCompletion(DataCompletionEnum.NOT_COMPLETE.getCode());
				adjustPriceOrderDetail.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(detailExtend1));
				createAdjustPriceOrderDetailOrgIds(orgLevelVOListSort, adjustPriceOrderDetail);
				createAdjustOrgTabTypeDetailList(orgLevelVOListSort, param, adjustPriceOrderDetail, adjustPriceOrder.getCreatedBy());
				AdjustPriceOrderDetailExample updateDetailexample = new AdjustPriceOrderDetailExample();
				updateDetailexample.createCriteria().andAdjustCodeEqualTo(param.getOrderCode()).andIdEqualTo(adjustPriceOrderDetail.getId());
                adjustPriceOrderDetailExMapper.updateByExampleSelective(adjustPriceOrderDetail, updateDetailexample);
			}
			return null;
		});
		List<Integer> channelList = Arrays.asList(adjustPriceOrder.getChannel().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
		AdjustPriceOrderDetailSupplementDataVO supplementDataVO = null;
		for (AdjustPriceOrderDetail adjustPriceOrderDetail : orderDetailList) {
            supplementDataVO = new AdjustPriceOrderDetailSupplementDataVO();
            supplementDataVO.setAdjustCode(adjustPriceOrderDetail.getAdjustCode());
            supplementDataVO.setPriceTypeCode(adjustPriceOrderDetail.getPriceTypeCode());
            supplementDataVO.setGoodsNo(adjustPriceOrderDetail.getGoodsNo());
            supplementDataVO.setPrice(adjustPriceOrderDetail.getPrice());
            supplementDataVO.setDataFull(Boolean.TRUE);
            supplementDataVO.setOrgIds(adjustPriceOrderDetail.getOrgIds());
            supplementDataVO.setOrgLevels(adjustPriceOrderDetail.getOrgLevels());
            supplementDataVO.setAdjustDetailMergeIds(adjustPriceOrderDetail.getId().toString());
            supplementDataVO.setChannelId(channelList.get(0));
            supplementDataVO.setSkuId(adjustPriceOrderDetail.getSkuId().toString());
            supplementDataVO.setIsPortal(param.getIsPortal());
            supplementDataProducer.sendMq(supplementDataVO);
		}
	}

    /**
     *
     * @Title: checkAddOrEditGoodsOrgIntersectionOfSets
     * @Description: 验证修改商品机构是否存在交集
     * @param: @param param
     * @return: void
     * @throws
     */
    private void checkAddOrEditGoodsOrgIntersectionOfSets(PriceOrderGoodsOrgDetailV2Param param,List<OrgLevelVO> currOrgLevelVOList) {
    	String adjustCode = param.getOrderCode();
    	List<String> goodsNoList = Lists.newArrayList();
    	List<Long> detailIdList = Lists.newArrayList();

		param.getGoodsNoMergeIds().stream().forEach(m -> m.forEach((k,v) ->{
			goodsNoList.add(k);
			detailIdList.addAll(Arrays.asList(v.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList()));
		}));
        List<AdjustPriceOrderDetail> orderDetailHistoryList = adjustPriceOrderDetailExMapper.listOrderDetailByGoodsNoOrgIds(adjustCode, goodsNoList, null,detailIdList);
        if(CollectionUtils.isNotEmpty(orderDetailHistoryList)) {
        	//获取当前需要修改的orgId对应的门店信息
        	Date operateTime = new Date();
        	List<AdjustPriceOrderOrgStoreDetail> currOrgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(adjustCode, currOrgLevelVOList,operateTime);
    		if(null!=param.getOrgTabType() && (param.getOrgTabType()==PriceOrderTabTypeEnum.STORE_GROUP.getType() || param.getOrgTabType()==PriceOrderTabTypeEnum.STORE_FLAG.getType())) {
    			if(currOrgStoreDetailList.size()>storemaxcount) {
    				throw new AmisBadRequestException(ReturnCodeEnum.ERROR_STOREGROUP_STOREMAXCOUNT,ReturnCodeEnum.ERROR_STOREGROUP_STOREMAXCOUNT.getMessage()+storemaxcount);
    			}
    		}
        	List<Long> currStoreIdList = currOrgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).distinct().collect(Collectors.toList());
        	for (AdjustPriceOrderDetail orderDetail : orderDetailHistoryList) {
        		List<OrgLevelVO> orgLevelVOList = Lists.newArrayList();
        		List<Long> orgIdList = Arrays.asList(orderDetail.getOrgIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        		List<Integer> orgLevelList = Arrays.asList(orderDetail.getOrgLevels().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
        		OrgLevelVO orgLevelVO = null;
        		for (int i = 0; i < orgIdList.size(); i++) {
        			orgLevelVO = new OrgLevelVO();
        			orgLevelVO.setOrgId(orgIdList.get(i));
        			orgLevelVO.setOrgLevel(orgLevelList.get(i));
        			orgLevelVOList.add(orgLevelVO);
				}
            	List<AdjustPriceOrderOrgStoreDetail> localOrgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(adjustCode, orgLevelVOList,operateTime);
        		List<Long> localStoreIdList = localOrgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).distinct().collect(Collectors.toList());
            	List<Long> intersectionList = currStoreIdList.stream().filter(s -> localStoreIdList.contains(s)).collect(Collectors.toList());
            	if(CollectionUtils.isNotEmpty(intersectionList)) {
            		throw new AmisBadRequestException(ReturnCodeEnum.ERROR_GOODS_ORG_INTERSECTIONOFSETS);
            	}
        	}
        }
    }

    /**
	 *
	 * @Title: checkPriceOrderGoodsOrgDetailV2Param
	 * @Description: 新增/编辑调价单商品机构参数校验
	 * @param: @param param
	 * @return: void
	 * @throws
	 */
	private void checkPriceOrderGoodsOrgDetailV2Param(PriceOrderGoodsOrgDetailV2Param param) {
		if(null==param) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}
		if(null==param.getAdjustPriceOrderId() || null==param.getGoodsNoMergeIds()) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}
		//param.getOrgTabType为空 复制主表信息
		if(null != param.getOrgTabType() && (PriceOrderTabTypeEnum.STORE_GROUP.getType() == param.getOrgTabType() || PriceOrderTabTypeEnum.STORE_FLAG.getType() == param.getOrgTabType())) {
			if(CollectionUtils.isEmpty(param.getTabTypeValList())) {
				throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
			}
		}
	}
	/**
	 *
	 * @Title: checkCopyAdjustPriceOrderOrg
	 * @Description: 验证是否需要复制主单机构
	 * @param: @param param
	 * @param: @return
	 * @return: boolean
	 * @throws
	 */
	private boolean checkCopyAdjustPriceOrderOrg(Integer orgTabType) {
		if(null == orgTabType) {
			return true;
		}
		try {
			if(PriceOrderTabTypeEnum.STORE_LIST.getType() == orgTabType ||
					PriceOrderTabTypeEnum.STORE_GROUP.getType() == orgTabType ||
					PriceOrderTabTypeEnum.STORE_FLAG.getType() == orgTabType) {
				return false;
			}
		} catch (Exception e) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}
		return true;
	}
	/**
	 *
	 * @Title: createAdjustOrgTabTypeDetailList
	 * @Description: 保存调价单 门店列表/标签/门店组
	 * @param: @param param
	 * @param: @param userDTO
	 * @param: @param operateTime
	 * @return: void
	 * @throws
	 */
    private void createAdjustOrgTabTypeDetailList(List<OrgLevelVO> orgLevelVOListSort, PriceOrderGoodsOrgDetailV2Param param,
        AdjustPriceOrderDetail adjustPriceOrderDetail, Long userId) {
    	Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
    	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = null;
        if (detailExtend1Optional.isPresent()) {
        	adjustPriceOrderDetailExtend1 = detailExtend1Optional.get();
        } else {
        	adjustPriceOrderDetailExtend1 = AdjustPriceOrderDetailExtend1.getInstance();
        }
    	adjustPriceOrderDetailExtend1.setGoodsOrgTabType(param.getOrgTabType());
    	if (PriceOrderTabTypeEnum.STORE_GROUP.getType() == param.getOrgTabType() || PriceOrderTabTypeEnum.STORE_FLAG.getType() == param.getOrgTabType()) {
    		if (CollectionUtils.isEmpty(param.getTabTypeValList())) {
	            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
	        }
	    	adjustPriceOrderDetailExtend1.setGoodsTabTypeValue(Joiner.on(",").join(param.getTabTypeValList()));
    	}
        String categoryId = adjustPriceOrderDetailExtend1.getCategoryId();
        String covidType = adjustPriceOrderDetailExtend1.getCovidType();

        List<Long> businessIdList = adjustOrgIdConvertBusinessIdList(adjustPriceOrderDetail.getAdjustCode(),orgLevelVOListSort,null);
		List<SpuNewVo> businessIdGoodsList = getBusinessIdGoodsList(businessIdList, Lists.newArrayList(adjustPriceOrderDetail.getGoodsNo()));
		Map<Long, String> pushLevelsMap = getPushLevelsMap(businessIdGoodsList);
		if(null!=pushLevelsMap) {
			adjustPriceOrderDetailExtend1.setPushLevel(JSON.toJSONString(pushLevelsMap));
		}
        GoodsLimitStatusEnum goodsLimitStatusEnum = adjustControlService.checkGoodsLimit(adjustPriceOrderDetail.getGoodsNo(),
            adjustPriceOrderDetail.getGoodsName(), categoryId, covidType, pushLevelsMap, orgLevelVOListSort,adjustPriceOrderDetail,userId);
        adjustPriceOrderDetailExtend1.setLimitStatus(goodsLimitStatusEnum.getCode());
    	adjustPriceOrderDetail.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
    }

    /**
     *
     * @Title: createAdjustPriceOrderDetailOrgIds
     * @Description: 保存调价单 商品机构 门店列表tab
     * @param: @param orgLevelVOList
     * @param: @param param
     * @param: @param userDTO
     * @param: @param operateTime
     * @return: String
     * @throws
     */
	private void createAdjustPriceOrderDetailOrgIds(List<OrgLevelVO> orgLevelVOList,AdjustPriceOrderDetail adjustPriceOrderDetail) {
		List<Long> orgIdList = orgLevelVOList.stream().map(OrgLevelVO::getOrgId).collect(Collectors.toList());
		List<String> orgNameList = orgLevelVOList.stream().map(OrgLevelVO::getOrgName).collect(Collectors.toList());
		List<Integer> orgLevelList = orgLevelVOList.stream().map(OrgLevelVO::getOrgLevel).collect(Collectors.toList());
		adjustPriceOrderDetail.setOrgIds(Joiner.on(",").join(orgIdList));
		adjustPriceOrderDetail.setOrgNames(Joiner.on(",").join(orgNameList));
		adjustPriceOrderDetail.setOrgLevels(Joiner.on(",").join(orgLevelList));
	}

	@Override
	public AdjustPriceOrderOrgDetailVO getAdjustPriceOrderGoodsOrg(Long adjustPriceOrderId, String adjustDetailMergeIds) {
		AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
		List<Long> detaildList = Arrays.asList(adjustDetailMergeIds.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		AdjustPriceOrderDetailExample orderDetailExample = new AdjustPriceOrderDetailExample();
		orderDetailExample.createCriteria().andAdjustCodeEqualTo(adjustPriceOrder.getAdjustCode()).andIdEqualTo(detaildList.get(0));
		List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailMapper.selectByExample(orderDetailExample);
		if(CollectionUtils.isEmpty(orderDetailList)) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}
		AdjustPriceOrderDetail adjustPriceOrderDetail = orderDetailList.get(0);
		if(StringUtils.isBlank(adjustPriceOrderDetail.getOrgIds())) {
			return null;
		}
		List<Long> orgIdList = Arrays.asList(adjustPriceOrderDetail.getOrgIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
		String orgNames = adjustPriceOrderDetail.getOrgNames();
		Optional<AdjustPriceOrderDetailExtend1> detailExtend1Optional = AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
		Integer orgTabType = null;
		List<String> tabTypeValList = null;
        if (detailExtend1Optional.isPresent()) {
        	AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = detailExtend1Optional.get();
        	orgTabType = adjustPriceOrderDetailExtend1.getGoodsOrgTabType();
            String goodsTabTypeValues = adjustPriceOrderDetailExtend1.getGoodsTabTypeValue();
            tabTypeValList = StringUtils.isBlank(goodsTabTypeValues) ? null : Arrays.asList(goodsTabTypeValues.split(","));
        }
		return new AdjustPriceOrderOrgDetailVO(orgTabType,tabTypeValList,orgIdList,orgNames);
	}

	private List<Long> currAdjustOrderStoreIdList(PriceOrderGoodsOrgDetailV2Param param,TokenUserDTO userDTO){
		//初步验证入参如果存在相同商品 则提示存在交集
    	List<String> goodsNoList = param.getGoodsNoList();
    	HashSet<String> goodsNoSet = new HashSet<String>(goodsNoList);
		boolean checkResult = goodsNoSet.size()==goodsNoList.size() ? true : false;
		if(!checkResult) {
			throw new AmisBadRequestException(ReturnCodeEnum.ERROR_GOODS_ORG_INTERSECTIONOFSETS);
		}
		String adjustCode = param.getOrderCode();
		List<OrgLevelVO> orgLevelVOList = Lists.newArrayList();
		//验证入参orgId数据是完整
		if(CollectionUtils.isEmpty(param.getOrgLevelList())) {
	    	orgLevelVOList = adjustPriceOrderV2Service.getUserActualSelectOrgDTOList(userDTO.getUserId(), param.getResourceId(), param.getOrgIdList());
		}else {
			OrgLevelVO orgLevelVO = null;
    		for (int i = 0; i < param.getOrgIdList().size(); i++) {
    			orgLevelVO = new OrgLevelVO();
    			orgLevelVO.setOrgId(param.getOrgIdList().get(i));
    			orgLevelVO.setOrgLevel(param.getOrgLevelList().get(i));
    			orgLevelVOList.add(orgLevelVO);
			}
		}
		Date operateTime = new Date();
		//入参orgId对应的门店信息
		List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(adjustCode, orgLevelVOList,operateTime);
		if(null!=param.getOrgTabType() && (param.getOrgTabType()==PriceOrderTabTypeEnum.STORE_GROUP.getType() || param.getOrgTabType()==PriceOrderTabTypeEnum.STORE_FLAG.getType())) {
			if(orgStoreDetailList.size()>storemaxcount) {
				throw new AmisBadRequestException(ReturnCodeEnum.ERROR_STOREGROUP_STOREMAXCOUNT,ReturnCodeEnum.ERROR_STOREGROUP_STOREMAXCOUNT.getMessage()+storemaxcount);
			}
		}
		List<Long> currStoreIdList = orgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).sorted().distinct().collect(Collectors.toList());

		return currStoreIdList;
	}

	private List<Map<String,List<OrgLevelVO>>> goodsOrgLevelList(String adjustCode,List<String> goodsNoList){
		//二、组装库已存在的历史数据封装org信息
        //根据调价单号、商品编码返回的数据即是同一单号相同商品历史数据 进行验证
    	List<AdjustPriceOrderDetail> adjustPriceOrderDetailHistoryList = adjustPriceOrderDetailExMapper.listOrderDetailByGoodsNoOrgIds(adjustCode, goodsNoList,null,null);
    	List<Map<String,List<OrgLevelVO>>> goodsOrgLevelList = Lists.newArrayList();
    	if(CollectionUtils.isNotEmpty(adjustPriceOrderDetailHistoryList)) {
    		for (AdjustPriceOrderDetail detail : adjustPriceOrderDetailHistoryList) {
    			Map<String,List<OrgLevelVO>> goodsOrgMap = Maps.newConcurrentMap();
    			List<OrgLevelVO> historyList = Lists.newArrayList();
    			List<Long> orgIdsHistoryList = Arrays.asList(detail.getOrgIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    			List<Integer> orgLevelsHistoryList = Arrays.asList(detail.getOrgLevels().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
    			for (int i = 0; i < orgIdsHistoryList.size(); i++) {
    				OrgLevelVO orgLevelVO =new OrgLevelVO();
    				orgLevelVO.setOrgId(orgIdsHistoryList.get(i));
    				orgLevelVO.setOrgLevel(orgLevelsHistoryList.get(i));
    				historyList.add(orgLevelVO);
				}
    			goodsOrgMap.put(detail.getGoodsNo(),historyList);
    			goodsOrgLevelList.add(goodsOrgMap);
			}
    	}
    	return goodsOrgLevelList;
	}

    /**
     *
     * @Title: checkSaveGoodsIntersectionOfSets
     * @Description: 通过页面添加商品使用。验证传过来的数据+库里数据 org是否存在交集。调价单+商品+mergeIds  取出各自门店 验证是否存在交集
     * @param: @param param
     * @return: void
     * @throws
     */
    private void checkSaveGoodsIntersectionOfSets(PriceOrderGoodsOrgDetailV2Param param,TokenUserDTO userDTO) {
    	Date operateTime = new Date();
    	List<Long> currStoreIdList = currAdjustOrderStoreIdList(param, userDTO);
    	List<Map<String, List<OrgLevelVO>>> goodsOrgLevelList = goodsOrgLevelList(param.getOrderCode(),param.getGoodsNoList());
    	//返回每个历史商品机构对应门店信息
    	for (Map<String,List<OrgLevelVO>> map : goodsOrgLevelList) {
    		Iterator<Map.Entry<String, List<OrgLevelVO>>> itEntry = map.entrySet().iterator();
    		while (itEntry.hasNext()) {
    			 Map.Entry<String, List<OrgLevelVO>> next = itEntry.next();
    			 List<AdjustPriceOrderOrgStoreDetail> localOrgStoreDetailList = adjustPriceOrderV2Service.getAdjustStoreDetailsFromOrgs(param.getOrderCode(), next.getValue(),operateTime);
    			 if(CollectionUtils.isNotEmpty(localOrgStoreDetailList)) {
    					List<Long> localStoreIdList = localOrgStoreDetailList.stream().map(AdjustPriceOrderOrgStoreDetail::getStoreId).distinct().collect(Collectors.toList());
    					List<Long> intersectionList = currStoreIdList.stream().filter(s -> localStoreIdList.contains(s)).collect(Collectors.toList());
    	            	if(CollectionUtils.isNotEmpty(intersectionList)) {
    	            		throw new AmisBadRequestException(ReturnCodeEnum.ERROR_GOODS_ORG_INTERSECTIONOFSETS);
    	            	}
    			 }
    		}
		}
    }

    @Override
   	public AdjustImportResultDTO getAdjustImportResult(Long adjustPriceOrderId,String downloadCode) {
    	AdjustImportResultDTO importResult = new AdjustImportResultDTO();
    	if(StringUtils.isNotBlank(downloadCode)) {
    		String cacheResultKey = RedisKeysConstant.getInitNewStoreAdjustPriceImportResultKey(downloadCode);
    		RBucket<String> bucket = redissonClient.getBucket(cacheResultKey);
        	if(bucket.isExists()) {
        		importResult = JSON.parseObject(bucket.get(), AdjustImportResultDTO.class);
        	}
        	return importResult;
    	}
    	AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
    	String adjustImportResultRedisKey = getAdjustImportResultRedisKey(adjustPriceOrder.getAdjustCode());
    	RBucket<AdjustImportResultDTO> bucket = redissonClient.getBucket(adjustImportResultRedisKey);
    	if(bucket.isExists()) {
    		importResult = bucket.get();
    	}
		/*if(importResult.getStatus()==ImportStatusEnum.IMPORT_FAIL.getCode() ||
				importResult.getStatus()==ImportStatusEnum.IMPORTING.getCode() ) {
			return importResult;
		}
		int notCompleted = adjustPriceOrderDetailExMapper.countAdjustPriceOrderDetailSupplementNotCompleted(adjustPriceOrder.getAdjustCode());
		if(notCompleted>0) {
			//说明正在补数据中
			importResult.setStatus(ImportStatusEnum.IMPORTING.getCode());
		}*/
   		return importResult;
   	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Override
	public ExportFileCubeVO<Map<String, Object>> asyncExportErrorAdjustPriceOrderDetailsFile(Long adjustPriceOrderId,
			Integer page, Integer pageSize) {
		AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
        basePriceOrderService.checkAdjustPriceOrderIsNull(adjustPriceOrder);
		if(null==page || page<0) {
			page = 1;
		}
		if(null==pageSize || pageSize<0) {
			pageSize = 10;
		}
		int fromIndex = (page-1)*pageSize;
		int toIndex = fromIndex+pageSize;
		String key = getAdjustImportExceptionResult(adjustPriceOrder.getAdjustCode());
		RList<Map<String, Object>> dataList = redissonClient.getList(key);
		List<Map<String, Object>> exceptionDetail = dataList.range(fromIndex, toIndex);
		if(CollectionUtils.isEmpty(exceptionDetail)) {
			exceptionDetail = Lists.newArrayList();
		}
		String orgIdName = AdjustPriceOrderDetailBaseColumnEnum.COLUMN_15.getName();
		for (Map<String, Object> map : exceptionDetail) {
			map.forEach((field,value) -> {
				if(field.equals(orgIdName) && null!=value) {
					map.put(orgIdName, value.toString().replace(",", ";"));
				}
			});
		}
		String structureKey = getAdjustImportExceptionResultStructureRediskey(adjustPriceOrder.getAdjustCode());
		RBucket<ExportFileCubeVO> bucket = redissonClient.getBucket(structureKey);
		ExportFileCubeVO exportFileCubeVO = bucket.get();
		if(null==exportFileCubeVO) {
			exportFileCubeVO = new ExportFileCubeVO();
		}
		exportFileCubeVO.setDataList(exceptionDetail);
		return exportFileCubeVO;
	}

	@Override
	public void saveAdjustPriceDetailAuditInfo(AdjustPriceOrderDetailImageV2Param param) {
		if(null==param) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}else if((StringUtils.isBlank(param.getAdjustCode()) && null==param.getAdjustId()) ||
				StringUtils.isBlank(param.getAdjustDetailMergeIds())) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}
		AdjustPriceOrder adjustPriceOrder = null;
		if(null!=param.getAdjustId()) {
			adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(param.getAdjustId());
			param.setAdjustCode(adjustPriceOrder.getAdjustCode());
		}

		if(null==adjustPriceOrder && StringUtils.isNotBlank(param.getAdjustCode())) {
			adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode(param.getAdjustCode());
		}

		if(null==adjustPriceOrder) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}

		List<Long> nextAuditors = getNextAuditors(adjustPriceOrder);
    	if(CollectionUtils.isNotEmpty(nextAuditors)) {
    		if(null==param.getWorkcode()) {
    			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
    		}
    		if(!nextAuditors.contains(param.getWorkcode())) {
				throw new AmisBadRequestException(ReturnCodeEnum.ADJUST_ORDER_AUDIT_USER_ERROR);
			}
    	}
        List<Long> idList = Arrays.asList(param.getAdjustDetailMergeIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
        example.createCriteria().andAdjustCodeEqualTo(param.getAdjustCode()).andIdIn(idList);
        List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(orderDetailList)) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        for (AdjustPriceOrderDetail adjustPriceOrderDetail : orderDetailList) {
            AdjustPriceOrderDetailEditV2 detailEdit = new AdjustPriceOrderDetailEditV2();
            detailEdit.setAdjustCode(param.getAdjustCode());
            detailEdit.setAdjustDetailMergeIds(Lists.newArrayList(adjustPriceOrderDetail.getId()));
            Optional<AdjustPriceOrderDetailExtend1> detailExtend1 = AdjustPriceOrderDetailExtend1.getInstance(adjustPriceOrderDetail.getExtend1());
            AdjustPriceOrderDetailExtend1 adjustPriceOrderDetailExtend1 = detailExtend1.get();
            if(StringUtils.isNotBlank(param.getImageUrl())) {
                adjustPriceOrderDetailExtend1.setImageUrl(param.getImageUrl());
            }
            if(StringUtils.isNotBlank(param.getDetailAuditMessage())) {
                adjustPriceOrderDetailExtend1.setDetailAuditMessage(param.getDetailAuditMessage());
            }
            if(null!=param.getDetailAuditStatus()) {
                Integer detailAuditStatus = adjustPriceOrderDetailExtend1.getDetailAuditStatus();
                if(detailAuditStatus!=DetailAuditStatusEnum.AUDIT_REFUSE.getCode()) {
                    adjustPriceOrderDetailExtend1.setDetailAuditStatus(param.getDetailAuditStatus()==true?DetailAuditStatusEnum.AUDIT_PASS.getCode():DetailAuditStatusEnum.PRE_REFUSE.getCode());
                }
            }
            detailEdit.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(adjustPriceOrderDetailExtend1));
            adjustPriceOrderDetailExMapper.updateByAdjustCode(detailEdit);
        }
	}

	@Override
	public boolean countAdjustPriceOrderDetailReasonIsNull(String adjustCode) {
		int countNull = adjustPriceOrderDetailExMapper.countAdjustPriceOrderDetailReasonIsNull(adjustCode);
        return countNull > 0 ? true : false;
	}

	@Override
	public void updateAdjustPriceOrderDetailAuditStatus(String adjustCode,Integer auditStatusNew,Integer auditStatusOld,String operatorUserName,Boolean init) {
		if(null==init) {
			throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
		}
		adjustPriceOrderDetailExMapper.updateAdjustPriceOrderDetailAuditStatus(adjustCode,auditStatusNew,auditStatusOld,operatorUserName,init);
	}

	@Override
	public List<AdjustPriceOrderDetail> selectAdjustPriceOrderDetailSupplementNotCompleted(String adjustCode) {
		return adjustPriceOrderDetailExMapper.selectAdjustPriceOrderDetailSupplementNotCompleted(adjustCode);
	}

	@Override
	public void changeItemSuppLimitStatus(AdjustPriceOrderDetailSupplementDataVO supplementDataVO) {
		logger.info("<===[AdjustPriceOrderDetailV2ServiceImpl.changeItemSuppLimitStatus] supplementDataVO:{}", supplementDataVO);
        String adjustCode = supplementDataVO.getAdjustCode();
		String goodsNo = supplementDataVO.getGoodsNo();
		Long skuId = Long.valueOf(supplementDataVO.getSkuId());
        Long userId = supplementDataVO.getUserId();
        List<Long> idList = Arrays.asList(supplementDataVO.getAdjustDetailMergeIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        AdjustPriceOrder adjustPriceOrder = basePriceOrderService.getAdjustPriceOrderByAdjustCode(adjustCode);
        if (adjustPriceOrder == null) {
            logger.error("<===[AdjustPriceOrderDetailV2ServiceImpl.changeItemSuppLimitStatus] adjustCode: {} 不存在 ", adjustCode);
            return;
        }
        AdjustPriceOrderDetailExample orderDetailExample = new AdjustPriceOrderDetailExample();
        orderDetailExample.createCriteria().andAdjustCodeEqualTo(adjustCode).andGoodsNoEqualTo(goodsNo).andIdIn(idList).andSkuIdEqualTo(skuId);
        List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailMapper.selectByExample(orderDetailExample);
        List<BigDecimal> floorPriceList = selectFloorPriceList(orderDetailList.get(0));
        orderDetailList.forEach(orderDetail -> {
            String goodsName = orderDetail.getCurName();
            String categoryId = null;
            String covidType = null;
            String extend1Str = orderDetail.getExtend1();
    		Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(extend1Str);
    		AdjustPriceOrderDetailExtend1 extend1 = instance.get();
            if(StringUtils.isBlank(extend1.getCategoryId())) {
            	Optional<SpuListVO> spuInfo = getSpuInfo(goodsNo);
            	if(spuInfo.isPresent()) {
            		categoryId = spuInfo.get().getCategoryId();
            		covidType = spuInfo.get().getCovidType();
            	}
            }else {
            	categoryId = extend1.getCategoryId();
            	covidType = extend1.getCovidType();
            }
            List<Long> orgIdList = Arrays.asList(orderDetail.getOrgIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
    		List<Integer> orgLevelList = Arrays.asList(orderDetail.getOrgLevels().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
    		List<OrgLevelVO> orgLevelVOList = Lists.newArrayList();
    		OrgLevelVO orgLevelVO = null;
    		for (int i = 0; i < orgIdList.size(); i++) {
    			orgLevelVO = new OrgLevelVO();
    			orgLevelVO.setOrgId(orgIdList.get(i));
    			orgLevelVO.setOrgLevel(orgLevelList.get(i));
    			orgLevelVOList.add(orgLevelVO);
    		}
    		GoodsLimitStatusEnum goodsLimitStatusEnum = GoodsLimitStatusEnum.NONE;

    		Map<Long, String> pushLevelsMap = Maps.newHashMap();
            if(StringUtils.isNotBlank(extend1.getPushLevel())) {
            	pushLevelsMap = JSON.parseObject(extend1.getPushLevel(), new TypeReference<Map<Long, String>>() {});
            }
    		goodsLimitStatusEnum = adjustControlService.checkGoodsLimit(goodsNo, goodsName, categoryId, covidType, pushLevelsMap, orgLevelVOList, orderDetail, userId);
            extend1.setLimitStatus(goodsLimitStatusEnum.getCode());
            extend1.setCategoryId(categoryId);
            extend1.setCovidType(covidType);
            if(PriceTypeModeEnum.LSJ.getPriceTypeCode().equals(supplementDataVO.getPriceTypeCode()) && null != orderDetail.getPrice()) {
            	boolean lessThanAny = isLessThanAny(PriceUtil.getYuanFromFen(orderDetail.getPrice()), floorPriceList);
				extend1.setPriceFloorRemind(lessThanAny ? 1 : 0);
			}
            AdjustPriceOrderDetail newDetail = new AdjustPriceOrderDetail();
            newDetail.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(extend1));
            orderDetailExample.clear();
            orderDetailExample.createCriteria().andAdjustCodeEqualTo(adjustCode).andIdEqualTo(orderDetail.getId());
            adjustPriceOrderDetailExMapper.updateByExampleSelective(newDetail, orderDetailExample);
        });
	}

	private List<BigDecimal> selectFloorPriceList(AdjustPriceOrderDetail detail){
		List<BigDecimal> floorPriceList = Lists.newArrayList();
        String detailExtend1 = detail.getExtend1();
		Optional<AdjustPriceOrderDetailExtend1> detailExtend1Instance = AdjustPriceOrderDetailExtend1.getInstance(detailExtend1);
		String businessIds = detailExtend1Instance.get().getBusinessIds();
		Map<String, String> priceTypeRefMap = getPriceTypeReferenceConfig();
		String priceTypeCode = priceTypeRefMap.get(AdjustPriceOrderDetailRefPriceColumnEnum.COLUMN_4.getName());
		if(StringUtils.isNotBlank(businessIds)){
			List<Long> businessIdList = Arrays.stream(businessIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
			PriceBusinessDetailHistoryExample example = new PriceBusinessDetailHistoryExample();
			for (Long businessId : businessIdList) {
				example.clear();
				example.createCriteria().andOrderCodeEqualTo(detail.getAdjustCode()).andBusinessIdEqualTo(businessId).andPriceTypeCodeEqualTo(priceTypeCode).andGoodsNoEqualTo(detail.getGoodsNo());
				List<PriceBusinessDetailHistory> businessPriceList = priceBusinessDetailHistoryService.selectByExample(example);
				floorPriceList.addAll(businessPriceList.stream().filter(x -> null!=x.getPrice()).map(PriceBusinessDetailHistory::getPrice).collect(Collectors.toList()));
			}
		}
		return floorPriceList;
	}

	@Override
	public void updateAdjustPriceOrderDetailLimitStatus(String adjustCode, Integer limitStatusNew,Integer dataFullNew) {
		adjustPriceOrderDetailExMapper.updateAdjustPriceOrderDetailLimitStatus(adjustCode, limitStatusNew,dataFullNew);
	}

	@Override
	public boolean checkAdjustPriceIsNull(String adjustCode) {
		if (adjustCode == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
        example.createCriteria().andStatusEqualTo((byte)StatusEnum.NORMAL.getCode())
                .andAdjustCodeEqualTo(adjustCode).andPriceIsNull();
        long count = adjustPriceOrderDetailMapper.countByExample(example);
        return count > 0 ? true : false;
	}

	@SuppressWarnings("unchecked")
	@Override
	public ExportFileCubeVO<Map<String, Object>> asyncExportErrorInitNewStoreDetailsFile(String downloadCode,
			Integer page) {
		String cacheKey = RedisKeysConstant.getInitNewStoreAdjustPriceImportErrorDataKey(downloadCode, page);
		List<InitNewStorePriceDTO> exportList = Lists.newArrayList();
		ExportFileCubeVO<Map<String, Object>> instance = new ExportFileCubeVO<Map<String, Object>>();
		RBucket<String> bucket = redissonClient.getBucket(cacheKey);
		if(bucket.isExists()) {
			exportList = JSON.parseArray(bucket.get(), InitNewStorePriceDTO.class);
		}
		instance = ExportFileCubeVO.getInstance("初始化新店价格异常数据", null, AsyncExportActionEnum.INIT_NEWSTORE_PRICE_ADJUST_ERROR_DETAIL.getUploadUrl(),
				InitNewStorePriceDTO.getExportFieldMap(), exportList);
		return instance;
	}

	@Override
	public void updateExtend1BusinessIds(List<String> adjustCodeList) {
		List<AdjustPriceOrderDetail> orderDetailList = Lists.newArrayList();
		AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
		AdjustPriceOrderDetailExtend1 extend1 = null;
		List<AdjustPriceOrderDetailPrice> detailPriceList = Lists.newArrayList();
		AdjustPriceOrderDetailPrice detailPrice = null;
		for (int i = 0; i < adjustCodeList.size(); i++) {
			example.clear();
			example.createCriteria().andAdjustCodeEqualTo(adjustCodeList.get(i));
			example.setOrderByClause("id desc");
			example.setOffset(i*50);
	        example.setLimit(50);
	        orderDetailList.clear();
	        orderDetailList = adjustPriceOrderDetailMapper.selectByExample(example);
	        if(CollectionUtils.isNotEmpty(orderDetailList)) {
	        	detailPriceList.clear();
	        	for (AdjustPriceOrderDetail detail : orderDetailList) {
	        		//获取商品级别机构
	                List<OrgLevelVO> orgLevelVOList = listOrgLevelVO(detail.getOrgIds(),detail.getOrgLevels());
	        		List<Long> businessIdList = adjustOrgIdConvertBusinessIdList(adjustCodeList.get(i),orgLevelVOList,null);
	        		if(CollectionUtils.isNotEmpty(businessIdList)) {
	        			String businessIdStr = StringUtils.join(businessIdList,",");
	        			Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(detail.getExtend1());
	        			if(instance.isPresent()) {
	        				extend1 = instance.get();
	        				extend1.setBusinessIds(businessIdStr);
	        			}else {
	        				extend1 = AdjustPriceOrderDetailExtend1.getInstance();
	        				extend1.setBusinessIds(businessIdStr);
	        			}
	        			detail.setExtend1(AdjustPriceOrderDetailExtend1.toJSONFormatStr(extend1));
	        		}
	        		detailPrice = new AdjustPriceOrderDetailPrice();
	        		BeanUtils.copyProperties(detail, detailPrice);
	        		detailPriceList.add(detailPrice);
				}
	        	adjustPriceOrderDetailExMapper.batchUpdateExtend1(adjustCodeList.get(i),detailPriceList);
	        }
		}
	}

	@Override
	public long findAdjustPriceOrderDetailCount(Long adjustPriceOrderId) {
		long result = 0L;
		if(null==adjustPriceOrderId) {
			return result;
		}
		AdjustPriceOrder order = adjustPriceOrderMapper.selectByPrimaryKey(adjustPriceOrderId);
		if(null==order || StringUtils.isBlank(order.getChannel())) {
			return result;
		}
		/**
		List<Integer> channelList = Arrays.asList(order.getChannel().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
		List<Integer> intersectionList = channelList.stream().filter(s -> getB2CChannelIdList().contains(s.toString())).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(intersectionList)) {
			return result;
		}
		*/
		AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
		example.createCriteria().andAdjustCodeEqualTo(order.getAdjustCode()).andStatusEqualTo((byte)0);
		result = adjustPriceOrderDetailMapper.countByExample(example);
		return result;
	}

	@Override
	public void supplementAdjustB2CPriceOrderDetailData(AdjustPriceOrderDetailSupplementDataVO vo) {
		logger.info("<===[AdjustPriceOrderDetailV2ServiceImpl.supplementAdjustB2CPriceOrderDetailData] supplementDataVO:{}", vo);
        String adjustCode = vo.getAdjustCode();
		String goodsNoOrgIds = vo.getGoodsNo()+"_"+vo.getOrgIds()+"_"+vo.getSkuId();
        AdjustPriceOrder adjustPriceOrder = basePriceOrderService.getAdjustPriceOrderByAdjustCode(adjustCode);
        if (adjustPriceOrder == null) {
            logger.error("<===[supplementAdjustB2CPriceOrderDetailData] adjustCode: {} 不存在 ", adjustCode);
            return;
        }
        String dtpGood = "";
        Map<String, AdjustBaseDetailDTO> baseDetailDTOMap = listAdjustPriceOrderBaseDetailsByCode(adjustPriceOrder.getAdjustCode(),vo.getGoodsNo(),Long.valueOf(vo.getSkuId()));
        boolean isGoodsNoExist = false;
        boolean isGoodsHasExist = false;
        Optional<SpuListVO> goodsInfoOptional = Optional.empty();
        if (baseDetailDTOMap.containsKey(goodsNoOrgIds)) {
            isGoodsNoExist = true;
            isGoodsHasExist = true;
        } else {
            goodsInfoOptional = getSpuInfo(vo.getGoodsNo());
            if (goodsInfoOptional.isPresent()) {
                isGoodsNoExist = true;
            }
        }
        //商品编码不存在，不处理这条消息
        if (!isGoodsNoExist) {
            return;
        } else {
            //获取商品级别机构
            List<Long> detailIdList = Arrays.asList(vo.getAdjustDetailMergeIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            AdjustPriceOrderDetailEditV2 detailEditV2 = null;
            if (goodsInfoOptional.isPresent()) {
                SpuListVO spuListVO = goodsInfoOptional.get();
                if(StringUtils.isNotBlank(spuListVO.getDTPgood())){
                    try {
                        if(YNEnum.YES.getType()==Integer.valueOf(spuListVO.getDTPgood())){
                            dtpGood = YNEnum.YES.getDesc();
                        }
                        if(YNEnum.NO.getType()==Integer.valueOf(spuListVO.getDTPgood())){
                            dtpGood = YNEnum.NO.getDesc();
                        }
                    } catch (NumberFormatException e) {
                        logger.error("supplementAdjustB2CPriceOrderDetailData|DTPgood转换异常",e);
                    }
                }
                detailEditV2 = getAdjustPriceOrderDetailEditV2FromGoodInfo(vo.getAdjustCode(), spuListVO);
            } else if (isGoodsHasExist) {
                AdjustBaseDetailDTO adjustBaseDetailDTO = baseDetailDTOMap.get(goodsNoOrgIds);
                detailEditV2 = copyDetailEditV2FromBaseDetail(vo.getAdjustCode(), adjustBaseDetailDTO);
            }
            detailEditV2.setGmtUpdate(new Date());
            detailEditV2.setAdjustDetailMergeIds(detailIdList);
            DataFullEnum dataFullEnum = DataFullEnum.NOT_FULL;
            if (Boolean.TRUE.equals(vo.getDataFull())) {
                dataFullEnum = DataFullEnum.HAS_FULL;
            }
            List<Long> idList = Arrays.asList(vo.getAdjustDetailMergeIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            AdjustPriceOrderDetailExample orderDetailExample = new AdjustPriceOrderDetailExample();
            orderDetailExample.createCriteria().andAdjustCodeEqualTo(adjustCode).andGoodsNoEqualTo(vo.getGoodsNo()).andIdIn(idList).andPriceTypeCodeEqualTo(vo.getPriceTypeCode());
            List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailMapper.selectByExample(orderDetailExample);
            AdjustPriceOrderDetail orderDetail = orderDetailList.get(0);
            AdjustPriceOrderDetailExtend1 extend1 = null;
            if(StringUtils.isNotBlank(orderDetail.getExtend1())) {
            	Optional<AdjustPriceOrderDetailExtend1> instance = AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
            	if(instance.isPresent()) {
            		extend1 = instance.get();
            		extend1.setDataCompletion(DataCompletionEnum.HAS_COMPLETED.getCode());
            		extend1.setDataFull(dataFullEnum.getCode());
            	}
            }else {
                extend1 = AdjustPriceOrderDetailExtend1.getInstance(DataCompletionEnum.HAS_COMPLETED.getCode(), dataFullEnum.getCode());
            }
            Optional<AdjustPriceOrderDetailExtend1> detailExtent1 = AdjustPriceOrderDetailExtend1.getInstance(orderDetail.getExtend1());
            if(detailExtent1.isPresent()) {
            	AdjustPriceOrderDetailExtend1 extent2 = detailExtent1.get();
            	extend1.setGoodsOrgTabType(extent2.getGoodsOrgTabType());
                extend1.setGoodsTabTypeValue(extent2.getGoodsTabTypeValue());
                extend1.setOverrideLowerLevel(extent2.getOverrideLowerLevel());
            }
            extend1.setDetailAuditStatus(DetailAuditStatusEnum.AUDIT_PASS.getCode());
            extend1.setDetailAuditMessage("");
            //根据连锁id集合+商品编码 验证是否首营
            extend1.setLawful(true);
            extend1.setDtpGood(dtpGood);
            List<Long> orgIdList = Arrays.asList(vo.getOrgIds().split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            ResponseEntity<List<OrgDTO>> responseEntity = permissionService.listOrgInfoById(orgIdList, false);
            if(responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
                throw new AmisBadRequestException(ReturnCodeEnum.CALL_PERMISSION_ERROR);
            }
            List<OrgDTO> orgDtoList = responseEntity.getBody();
            if(CollectionUtils.isEmpty(orgDtoList)) {
            	throw new AmisBadRequestException(ReturnCodeEnum.CALL_PERMISSION_ERROR);
            }
        	List<Long> channelMapperStoreIdList = orgDtoList.stream().map(OrgDTO::getOutId).collect(Collectors.toList());
    		List<StoreChannelMappingDTO> storeChannelMapping = getStoreChannelMapping(channelMapperStoreIdList);
    		List<Long> businessIdList = storeChannelMapping.stream().map(StoreChannelMappingDTO::getBusinessId).collect(Collectors.toList());
            saveDetailStoreIdCache(adjustCode, orderDetail.getId(), null, businessIdList, channelMapperStoreIdList);
    		extend1.setBusinessIds(StringUtils.join(businessIdList,","));
    		try {
    			BigDecimal costv = BigDecimal.ZERO;
            	if(CollectionUtils.isNotEmpty(businessIdList)) {
            		AmisCommonResponse<PageResult<B2cGoodsPriceInfoDTO>> commonResult = nyuwaErpService.getBusinessPrice(businessIdList.get(0),vo.getGoodsNo(), vo.getChannelId(), 1, 1);
            		if(null!=commonResult && null!=commonResult.getData() && CollectionUtils.isNotEmpty(commonResult.getData().getRows())) {
            			B2cGoodsPriceInfoDTO b2cGoodsPrice = commonResult.getData().getRows().get(0);
            			if(b2cGoodsPrice.getWarehouse_verpr()!=null) {
            				costv = b2cGoodsPrice.getWarehouse_verpr();
            			}
            			extend1.setCost(b2cGoodsPrice.getWarehouse_verpr()!=null?b2cGoodsPrice.getWarehouse_verpr().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);
            			extend1.setRebateRate(b2cGoodsPrice.getGoods_rebate_rate()!=null?b2cGoodsPrice.getGoods_rebate_rate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);
            			extend1.setExpressFeeRate(b2cGoodsPrice.getExpress_fee_rate()!=null?b2cGoodsPrice.getExpress_fee_rate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);
            			extend1.setPlatformPoints(b2cGoodsPrice.getDiscount_points()!=null?b2cGoodsPrice.getDiscount_points().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);
            			extend1.setOpenFeeRate(b2cGoodsPrice.getRooting_amount_rate()!=null?b2cGoodsPrice.getRooting_amount_rate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);
            			extend1.setPackagFeeRate(b2cGoodsPrice.getPackage_amount_rate()!=null?b2cGoodsPrice.getPackage_amount_rate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);
            			extend1.setLargeWarehouseFeeRate(b2cGoodsPrice.getStorehouse_cost_ratio()!=null?b2cGoodsPrice.getStorehouse_cost_ratio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);
            			extend1.setDeliveryFeeRate(b2cGoodsPrice.getDelivery_fee_rate()!=null?b2cGoodsPrice.getDelivery_fee_rate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_DOWN):null);

            			extend1.setGrossMargin(PriceUtil.getGrossMargin(extend1.getCost(),orderDetail.getPrice()));
            			extend1.setGrossSynthesizeMargin(PriceUtil.getGrossSynthesizeMargin(extend1.getGrossMargin(), extend1.getRebateRate()));
            			extend1.setGrossPureMargin(PriceUtil.getGrossPureMargin(extend1.getGrossSynthesizeMargin(),extend1));
            		}
            	}
            	//记录price_business_detail_history表
            	List<String> priceDics=new ArrayList<String>();
            	priceDics.add(detailCbj);
            	List<PriceDictionaryDTO> priceDicList = priceDictionaryService.getByCodes(DictCodeEnum.PRICE_TYPE.getCode(), priceDics);
            	if(CollectionUtils.isNotEmpty(priceDicList)) {
            		createPriceBusinessDetailHistory(orderDetail, costv, businessIdList.get(0), priceDicList, priceDics);
            	}
            	//补充渠道门店id
            	StoreChannelMappingDTO storeChannelMappingDTO = storeChannelMapping.get(0);
				extend1.setChannelStoreId(Long.valueOf(storeChannelMappingDTO.getChannelStoreId()));
				extend1.setChannelMappingStoreId(storeChannelMappingDTO.getChannelMappingStoreId());
			} catch (Exception e) {
				logger.error("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustB2CPriceOrderDetailData|补充b2c成本相关价格异常|adjustCode:{},goodsNo:{},",adjustCode,vo.getGoodsNo(),e);
			}

    		//补充三方商品编码
    		try {
				List<ItemSkuVo> itemSkuList = Lists.newArrayList();
				ItemSkuQueryApiDTO itemSkuQuery = new ItemSkuQueryApiDTO();
				itemSkuQuery.setBusinessId(businessIdList.get(0));
				itemSkuQuery.setSkuId(Long.valueOf(vo.getSkuId()));
				itemSkuQuery.setPage(1);
				itemSkuQuery.setPageSize(1);
				itemSkuList.addAll(getItemSkuList(itemSkuQuery));
				if(CollectionUtils.isNotEmpty(itemSkuList)) {
					extend1.setOutSkuCode(itemSkuList.get(0).getOutSkuCode());
				}
			} catch (Exception e) {
				logger.error("AdjustPriceOrderDetailV2ServiceImpl|supplementAdjustB2CPriceOrderDetailData|补充b2c三方商品编码异常|adjustCode:{},goodsNo:{},",adjustCode,vo.getGoodsNo(),e);
			}
    		detailEditV2.setSkuId(Long.valueOf(vo.getSkuId()));
            detailEditV2.setExtend1(extend1);
            detailEditV2.setExtend1Str(AdjustPriceOrderDetailExtend1.toJSONFormatStr(extend1));
            detailEditV2.setPriceTypeCode(vo.getPriceTypeCode());
            List<Integer> channelList = Arrays.asList(adjustPriceOrder.getChannel().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            detailEditV2.setChannelId(channelList.get(0));
            adjustPriceOrderDetailExMapper.updateByAdjustCode(detailEditV2);
        }
	}

	private void createPriceBusinessDetailHistory(AdjustPriceOrderDetail orderDetail,BigDecimal costv,Long businessId,List<PriceDictionaryDTO> priceDicList,List<String> priceDics) {
		Date date = new Date();
		List<Long> batchIdList = IdGeneratorUtils.getPriceBusinessDetailHistoryIdBatch(1);
		int idIndex = 0;
		List<PriceBusinessDetailHistory> hisList = Lists.newArrayList();
		PriceBusinessDetailHistory hisDetail = new PriceBusinessDetailHistory();
		hisDetail.setId(String.valueOf(batchIdList.get(idIndex++)));
		hisDetail.setGoodsNo(orderDetail.getGoodsNo());
		hisDetail.setOrderCode(orderDetail.getAdjustCode());
		hisDetail.setOrderType(OrderTypeEnum.ORDER_TYPE_ADJUST.getTypeCode());
		hisDetail.setChannelId(orderDetail.getChannelId());
		hisDetail.setBusinessId(businessId);
		hisDetail.setPrice(costv);
		hisDetail.setPriceTypeCode(priceDicList.get(0).getDictCode());
		hisDetail.setPriceTypeId(priceDicList.get(0).getId());
		hisDetail.setPriceTypeName(priceDicList.get(0).getDictName());
		hisDetail.setStatus((byte)0);
		hisDetail.setVersion(1);
		hisDetail.setGmtCreate(date);
		hisDetail.setCreatedBy(orderDetail.getCreatedBy());
		hisDetail.setGmtUpdate(date);
		hisDetail.setComment("");
		hisList.add(hisDetail);
		checkPriceBusinessDetailHistoryExist(hisList, priceDics);
        if(CollectionUtils.isEmpty(hisList)){
        	return;
        }
		priceBusinessDetailHistoryService.batchInsert(hisList);
	}

	private List<StoreChannelMappingDTO> getStoreChannelMapping(List<Long> channelMapperStoreIdList){
		List<StoreChannelMappingDTO> storeChannelMappingDTOList = Lists.newArrayList();
		ResponseEntity<List<StoreChannelMappingDTO>> response = storeService.list(null, channelMapperStoreIdList, null, null);
		if (Objects.isNull(response) || !HttpStatus.OK.equals(response.getStatusCode()) || Objects.isNull(response.getBody())) {
			return storeChannelMappingDTOList;
        }
		return response.getBody();
		//return response.getBody().stream().map(StoreChannelMappingDTO::getBusinessId).collect(Collectors.toList());
	}

	@Override
    @NewSpan
    public void importAdjustPriceOrderDetailsForB2C(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO) {
    	AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(importAdjustPriceOrder.getAdjustPriceOrderId());
    	try {
    		basePriceOrderService.checkAdjustPriceOrderIsNullAndOwner(adjustPriceOrder, userDTO);

    		basePriceOrderService.checkAdjustPriceOrderIsNullAndCanChange(adjustPriceOrder);

    		clearAdjustImportResultRedisKey(adjustPriceOrder.getAdjustCode());

    		checkB2COrgIsExist(adjustPriceOrder,importAdjustPriceOrder,userDTO);

    		checkB2CGoodsNoIsExist(importAdjustPriceOrder);

			checkMoreRowEqualGoodsNoIntersection(importAdjustPriceOrder, adjustPriceOrder, userDTO);

			deleteExistedOrderB2cDetailRecord(importAdjustPriceOrder, userDTO, adjustPriceOrder);

			batchAddAdjustPriceOrderDetailsForB2cImport(importAdjustPriceOrder,userDTO);

	        sendImportDataResult(importAdjustPriceOrder, adjustPriceOrder);

		} catch (Exception e) {
			logger.error("AdjustPriceOrderDetailV2ServiceImpl|importAdjustPriceOrderDetailsForB2C检查excel数据发生异常",e);
			sendAdjustPriceImportMsg(adjustPriceOrder.getAdjustCode(), 0, 0, 0, -1);
			throw new BusinessErrorException(ReturnCodeEnum.ERROR_CHECK_IMPORT);
		}

    }

	private void checkB2COrgIsExist(AdjustPriceOrder adjustPriceOrder,AdjustPriceOrderDetailImportV2Param param,TokenUserDTO userDTO) {
		List<Integer> channelList = Arrays.asList(adjustPriceOrder.getChannel().split(",")).stream().map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
		List<ImportAdjustPriceOrderDetailDTO> importList = param.getAdjustPriceOrderDetailDTOList();
    	List<List<ImportAdjustPriceOrderDetailDTO>> partition = Lists.partition(importList, 50);
    	List<Long> channelStoreIdList = Lists.newArrayList();
    	ResponseEntity<List<StoreChannelMappingDTO>> response = null;
    	Map<String,StoreChannelMappingDTO> storeChannelMap = Maps.newConcurrentMap();
    	for (List<ImportAdjustPriceOrderDetailDTO> subList : partition) {
    		channelStoreIdList = subList.stream().filter(s -> Boolean.TRUE.equals(s.getResult())).map(s -> Long.valueOf(s.getOrgIdsParam())).distinct().collect(Collectors.toList());
    		if(CollectionUtils.isEmpty(channelStoreIdList)){
    			continue;
    		}
    		response = storeService.list(null, null, channelList, channelStoreIdList);
    		if (Objects.isNull(response) || !HttpStatus.OK.equals(response.getStatusCode()) || Objects.isNull(response.getBody())) {
    			continue;
            }
    		storeChannelMap.putAll(response.getBody().stream().collect(Collectors.toMap(k -> k.getChannelStoreId(), part -> part ,(v1,v2)->v2)));
    	}
    	List<Long> scopeStoreIdList = Lists.newArrayList();
    	for (ImportAdjustPriceOrderDetailDTO detail : importList) {
			if(!detail.getResult()) {
				continue;
			}
			if(!storeChannelMap.containsKey(detail.getOrgIdsParam()) || null == storeChannelMap.get(detail.getOrgIdsParam()).getChannelMappingStoreId()) {
				detail.setResult(Boolean.FALSE);
				detail.setMessage("机构不存在");
				continue;
			}
			scopeStoreIdList.clear();
			detail.setBusinessId(storeChannelMap.get(detail.getOrgIdsParam()).getBusinessId());
			detail.setOrgIds(storeChannelMap.get(detail.getOrgIdsParam()).getChannelMappingStoreId().toString());
			detail.setChannelMappingStoreId(storeChannelMap.get(detail.getOrgIdsParam()).getChannelMappingStoreId());
			scopeStoreIdList.add(Long.valueOf(storeChannelMap.get(detail.getOrgIdsParam()).getChannelMappingStoreId()));
			String uniqueId = adjustPriceOrder.getAdjustCode()+"_"+detail.getGoodsNo()+"_"+detail.getLineNo()+"_"+param.getReceiveTimeStamp();
			String adjustItemStoreIdRedisKey = getAdjustItemStoreIdRedisKey(uniqueId);
			redissonClient.getBucket(adjustItemStoreIdRedisKey).set(scopeStoreIdList,30,TimeUnit.MINUTES);
		}
    	List<Long> channelMappingStoreIdList = null;
    	Map<Long,OrgDTO> orgDTOMap = null;
    	List<OrgDTO> scopeOrgDtoList = null;
    	for (List<ImportAdjustPriceOrderDetailDTO> subList : partition) {
    		channelMappingStoreIdList = Lists.newArrayList();
    		orgDTOMap = Maps.newConcurrentMap();
    		channelMappingStoreIdList = subList.stream().filter(s -> Boolean.TRUE.equals(s.getResult())).map(s -> s.getChannelMappingStoreId()).distinct().collect(Collectors.toList());
    		if(CollectionUtils.isEmpty(channelMappingStoreIdList)) {
    			continue;
    		}
    		List<OrgDTO> orgDtoList = permissionExtService.listOrgByOutId(channelMappingStoreIdList, OrgTypeEnum.STORE.getCode());
        	if(CollectionUtils.isNotEmpty(orgDtoList)) {
        		List<Long> orgIdList = orgDtoList.stream().map(OrgDTO::getId).collect(Collectors.toList());
            	scopeOrgDtoList = permissionExtService.getUserDataScopeChildOrgByOrgId(userDTO.getUserId(), RequestHeaderContextUtils.getResourceId(), orgIdList, 1);
            	if(CollectionUtils.isNotEmpty(scopeOrgDtoList)) {
            		orgDTOMap = scopeOrgDtoList.stream().collect(Collectors.toMap(OrgDTO::getOutId, Function.identity(), (v1, v2) -> v1));
            	}
        	}
        	for (ImportAdjustPriceOrderDetailDTO detailDto : subList) {
        		if(detailDto.getResult()) {
        			if(!orgDTOMap.containsKey(Long.valueOf(detailDto.getOrgIds()))) {
    					detailDto.setResult(Boolean.FALSE);
    					detailDto.setMessage("机构权限存在");
    					continue;
    				}else {
    					OrgDTO orgDTO = orgDTOMap.get(Long.valueOf(detailDto.getOrgIds()));
    					detailDto.setOrgIds(orgDTO.getId().toString());
    					detailDto.setOrgNames(orgDTO.getShortName());
    					detailDto.setOrgLevels(String.valueOf(OrgLevelTypeEnum.STORE.getCode()));
    				}
        		}
			}
    	}
	}

	private List<ItemSkuVo> getItemSkuList(ItemSkuQueryApiDTO itemSkuQuery){
		ResponseEntity<PageResult<ItemSkuVo>> responseEntity = itemCenterCpservice.query(itemSkuQuery);
		if (Objects.isNull(responseEntity) || !HttpStatus.OK.equals(responseEntity.getStatusCode()) || Objects.isNull(responseEntity.getBody())) {
			return Lists.newArrayList();
        }
		PageResult<ItemSkuVo> itemList = responseEntity.getBody();
		return itemList.getRows();
	}

	private void checkB2CGoodsNoIsExist(AdjustPriceOrderDetailImportV2Param param) {
		List<ImportAdjustPriceOrderDetailDTO> importList = param.getAdjustPriceOrderDetailDTOList();
    	List<Long> channelMappingStoreIdList = Lists.newArrayList();
    	List<String> outSkuCodeList = Lists.newArrayList();
    	ItemSkuQueryApiDTO itemSkuQuery = null;
    	List<ItemSkuVo> itemSkuList = Lists.newArrayList();
    	Map<Long, List<ImportAdjustPriceOrderDetailDTO>> detailsMap = importList.stream().filter(s -> Boolean.TRUE.equals(s.getResult())).collect(groupingBy(ImportAdjustPriceOrderDetailDTO::getBusinessId));
    	for (Map.Entry<Long, List<ImportAdjustPriceOrderDetailDTO>> entry : detailsMap.entrySet()) {
            List<List<ImportAdjustPriceOrderDetailDTO>> partition = Lists.partition(entry.getValue(), 50);
            for (List<ImportAdjustPriceOrderDetailDTO> subList : partition) {
            	channelMappingStoreIdList = subList.stream().filter(s -> Boolean.TRUE.equals(s.getResult())).map(s -> Long.valueOf(s.getChannelMappingStoreId())).distinct().collect(Collectors.toList());
                outSkuCodeList = subList.stream().filter(s -> Boolean.TRUE.equals(s.getResult())).map(ImportAdjustPriceOrderDetailDTO::getOutSkuCode).distinct().collect(Collectors.toList());
                if(!CollectionUtils.isEmpty(channelMappingStoreIdList) && !CollectionUtils.isEmpty(outSkuCodeList)) {
                	itemSkuQuery = new ItemSkuQueryApiDTO();
            		itemSkuQuery.setBusinessId(entry.getKey());
            		itemSkuQuery.setOutSkuCodeList(outSkuCodeList);
            		itemSkuQuery.setStoreIdList(channelMappingStoreIdList);
            		itemSkuQuery.setPage(1);
            		itemSkuQuery.setPageSize(outSkuCodeList.size());
            		itemSkuList.addAll(getItemSkuList(itemSkuQuery));
                }
        	}
        }
    	Map<String, ItemSkuVo> itemSkuMap = itemSkuList.stream().collect(Collectors.toMap(k -> k.getStoreId()+"_"+k.getOutSkuCode(), part -> part ,(v1,v2)->v2));
    	for (ImportAdjustPriceOrderDetailDTO detail : importList) {
			if(!detail.getResult()) {
				continue;
			}
			if(!itemSkuMap.containsKey(detail.getChannelMappingStoreId()+"_"+detail.getOutSkuCode())) {
				detail.setResult(Boolean.FALSE);
				detail.setMessage("商品不存在");
				continue;
			}
			ItemSkuVo itemSkuVo = itemSkuMap.get(detail.getChannelMappingStoreId()+"_"+detail.getOutSkuCode());
			detail.setGoodsNo(itemSkuVo.getGoodsNo());
			detail.setSkuId(itemSkuVo.getItemSkuId().toString());
		}
	}

	@Override
    public List<String> getSkuIdListbyAdjustCode(String adjustCode) {
        if (StringUtils.isEmpty(adjustCode)) {
            logger.error("<===[AdjustPriceOrderV2ServiceImpl.getSkuIdListbyAdjustCode]  参数都不能为空");
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<String> hasAddSkuIdList = adjustPriceOrderDetailExMapper.getSkuIdListbyAdjustCode(adjustCode);
        if (hasAddSkuIdList == null) {
        	hasAddSkuIdList = Collections.emptyList();
        }
        return hasAddSkuIdList;
    }

    @Override
    public List<Long> selectAdjustPriceOrderDetailIdByAdjustCode(String adjustCode, List<Integer> channelIdList, List<String> priceTypeCodes) {
        return adjustPriceOrderDetailExMapper.selectAdjustPriceOrderDetailIdByAdjustCode(adjustCode,channelIdList,priceTypeCodes);
    }

    @Override
    public void deleteAdjustPriceOrderDetailStoreIdCache(String adjustCode, List<Long> idList) {
        // 参数校验
        if (adjustCode == null || CollectionUtils.isEmpty(idList)) {
            return;
        }
        for (List<Long> ids : Lists.partition(idList, Constants.THREE_HUNDRED)) {
            RBatch delBatch = redissonClient.createBatch();
            for (Long id : ids) {
                if(null == id){
                    continue;
                }
                String key = RedisKeysConstant.getAdjustPriceOrderDetailStoreidsKey(adjustCode,id);
                delBatch.getBucket(key).deleteAsync();
            }
            try {
                delBatch.execute();
            } catch (Exception e) {
                logger.error("批量删除Redis缓存失败, adjustCode: {}, 当前批次大小: {}",
                    adjustCode, ids.size(), e);
            }
        }
    }

    @Override
    public void saveAdjustPriceOrderDetailStoreIdCache(String adjustCode, List<AdjustPriceOrderDetailStoreIdCacheDTO> detailStoreIdList) {
        // 参数校验
        if (StringUtils.isBlank(adjustCode) || CollectionUtils.isEmpty(detailStoreIdList)) {
            logger.warn("saveAdjustPriceOrderDetailStoreIdCache|保存缓存参数无效: adjustCode={}, listSize={}",
                adjustCode, detailStoreIdList == null ? 0 : detailStoreIdList.size());
            return;
        }
        logger.info("开始批量保存Redis缓存, adjustCode: {}, 总数量: {}", adjustCode, detailStoreIdList.size());
        // 分批处理，每批处理300条记录
        List<List<AdjustPriceOrderDetailStoreIdCacheDTO>> partitions = Lists.partition(detailStoreIdList, Constants.THREE_HUNDRED);
        for (int i = 0; i < partitions.size(); i++) {
            List<AdjustPriceOrderDetailStoreIdCacheDTO> batch = partitions.get(i);
            logger.debug("正在处理第{}/{}批，当前批次数量: {}", i + 1, partitions.size(), batch.size());
            try {
                processBatch(adjustCode, batch);
            } catch (Exception e) {
                logger.error("saveAdjustPriceOrderDetailStoreIdCache|批次处理失败, adjustCode: {}, 批次: {}/{}, 错误: {}",
                    adjustCode, i + 1, partitions.size(), e.getMessage(), e);
            }
        }
        logger.info("批量保存Redis缓存完成, adjustCode: {}, 有效记录数: {}", adjustCode, detailStoreIdList.size());
    }

    /**
     * 根据调价单号和id查询门店缓存
     * @param adjustCode
     * @param detailId
     * @return
     */
    private AdjustPriceOrderDetailStoreIdCacheDTO getAdjustPriceOrderDetailStoreIdCache(String adjustCode, Long detailId) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(RedisKeysConstant.getAdjustPriceOrderDetailStoreidsKey(adjustCode, detailId));
            // 直接获取值，如果不存在会返回null
            String cacheValue = bucket.get();
            if (StringUtils.isNotBlank(cacheValue)) {
                return JSON.parseObject(cacheValue, AdjustPriceOrderDetailStoreIdCacheDTO.class);
            }
            return null;
        } catch (Exception e) {
            // 记录日志，避免缓存异常影响主流程
            logger.error("获取调价单详情门店ID缓存失败, adjustCode: {}, detailId: {}", adjustCode, detailId, e);
            return null;
        }
    }

    /**
     * 根据门店编码和商品编码查询医保限价规则
     * @param storeIdList
     * @param goodsNo
     * @return
     */
    private List<PriceLimitControlStoreCache> getPriceLimitControlBatchStoreCache(List<Long> storeIdList, String goodsNo) {
        // 参数校验
        if (StringUtils.isBlank(goodsNo) || CollectionUtils.isEmpty(storeIdList)) {
            return Collections.emptyList();
        }
        try {
            RBatch readBatch = redissonClient.createBatch();
            // 使用LinkedHashMap保持门店ID顺序，便于后续处理
            Map<Long, RFuture<String>> futureMap = new LinkedHashMap<>();

            // 批量添加异步读取操作
            for (Long storeId : storeIdList) {
                String key = RedisKeysConstant.getPriceLimitControlKey(storeId, goodsNo);
                RBucketAsync<String> rBucket = readBatch.getBucket(key);
                // 调用getAsync()并保存Future结果
                futureMap.put(storeId, rBucket.getAsync());
            }

            // 执行批量操作
            readBatch.execute();

            // 处理异步结果并转换为目标对象
            List<PriceLimitControlStoreCache> resultList = new ArrayList<>();
            for (Map.Entry<Long, RFuture<String>> entry : futureMap.entrySet()) {
                Long storeId = entry.getKey();
                try {
                    String cacheValue = entry.getValue().get(); // 获取异步结果
                    if (StringUtils.isNotBlank(cacheValue)) {
                        PriceLimitControlStoreCache cache = JSON.parseObject(cacheValue, PriceLimitControlStoreCache.class);
                        if (cache != null) {
                            // 确保storeId信息完整（如果对象中没有storeId字段的话）
                            resultList.add(cache);
                        }
                    }
                } catch (Exception e) {
                    // 单个门店缓存解析失败不影响其他门店
                    logger.warn("解析门店价格限制缓存失败, storeId: {}, goodsNo: {}, error: {}",
                        storeId, goodsNo, e.getMessage());
                }
            }

            logger.debug("批量获取价格限制缓存完成, goodsNo: {}, 查询门店数: {}, 成功获取: {}",
                goodsNo, storeIdList.size(), resultList.size());

            return resultList;

        } catch (Exception e) {
            logger.error("批量获取价格限制缓存异常, goodsNo: {}, storeIdList: {}",
                goodsNo, storeIdList, e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理单个批次的缓存保存
     */
    private void processBatch(String adjustCode, List<AdjustPriceOrderDetailStoreIdCacheDTO> batch) {
        RBatch saveBatch = redissonClient.createBatch();
        int validCount = 0;

        for (AdjustPriceOrderDetailStoreIdCacheDTO detail : batch) {
            Long id = detail.getDetailId();
            String key = RedisKeysConstant.getAdjustPriceOrderDetailStoreidsKey(adjustCode, id);
            RBucketAsync<String> bucket = saveBatch.getBucket(key);
            bucket.setAsync(JSON.toJSONString(detail), 365, TimeUnit.DAYS);
            validCount++;
        }

        if (validCount > 0) {
            saveBatch.execute();
            logger.debug("批次保存完成，adjustCode：{},有效记录数: {}", adjustCode,validCount);
        } else {
            logger.warn("当前批次没有有效记录需要保存到缓存,adjustCode:{}",adjustCode);
        }
    }



    /**
     * 批量导调价单明细
     * @param importAdjustPriceOrder
     * @param userDTO
     */
    private void batchAddAdjustPriceOrderDetailsForB2cImport(AdjustPriceOrderDetailImportV2Param importAdjustPriceOrder, TokenUserDTO userDTO) {
        Date operateTime = new Date();
        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByPrimaryKey(importAdjustPriceOrder.getAdjustPriceOrderId());
        Map<String, PriceType> priceTypeMap = basePriceOrderService.getPriceTypesByCodes(adjustPriceOrder.getAdjustPriceType());
        Map<Integer, PriceChannel> priceChannelMap = basePriceOrderService.getPriceChannelByChannelIds(adjustPriceOrder.getChannel());
        List<Integer> channelIdList = basePriceOrderService.getChannelIdListFromStr(adjustPriceOrder.getChannel());
        List<String> priceTypeCodeList = basePriceOrderService.getPriceTypeCodeListFromStr(adjustPriceOrder.getAdjustPriceType());
        checkImportPriceTypeCode(priceTypeCodeList,importAdjustPriceOrder);
        //验证是否有效
        List<GoodsNoOrgIdsDTO> goodsNoOrgIdList = createGoodsNoOrgIdsForImport(importAdjustPriceOrder, adjustPriceOrder, userDTO);

        if(CollectionUtils.isEmpty(goodsNoOrgIdList)) {
        	logger.info("AdjustPriceOrderDetailV2ServiceImpl|batchAddAdjustPriceOrderDetailsForImport没有有效的数据|goodsNoOrgIdList:{}",JSON.toJSONString(goodsNoOrgIdList));
        	return;
        }
        List<AdjustPriceOrderDetail> detailList = getInsertAdjustPriceOrderDetailList(adjustPriceOrder, goodsNoOrgIdList, priceChannelMap, priceTypeMap,
            channelIdList, priceTypeCodeList, userDTO, operateTime);
        setPriceTypeDataForInsert(detailList,importAdjustPriceOrder);
        transactionTemplate.execute(ts -> {
        	Lists.partition(detailList, 200).forEach(subDetailList -> adjustPriceOrderDetailExMapper.batchInsert(subDetailList));
			return Boolean.TRUE;
		});
        sendAdjustOrderDetailThreadExecutor.execute(() -> {
        	Lists.partition(detailList, 100).forEach(subDetailList -> sendToSupplementDataMsgs(false, subDetailList,Boolean.TRUE));
        });
    }

    private Long findBusinessStoreIdApi(Long businessId){
        try {
        	logger.info("AdjustPriceOrderDetailV2ServiceImpl|findBusinessStoreIdApi|findMdmBusinessBaseByBusinessIds入参businessId:{}.", businessId);
            ResponseEntity<List<MdmBusinessBaseDTO>> responseEntity = storeService.findMdmBusinessBaseByBusinessIds(Lists.newArrayList(businessId),Constants.B2B_JOIN_MALL_SELLER,portalBizType);
            if (responseEntity == null
                || !(responseEntity.getStatusCode().equals(HttpStatus.OK) || responseEntity.getStatusCode().equals(HttpStatus.CREATED))) {
                return null;
            }
            if(CollectionUtils.isNotEmpty(responseEntity.getBody())){
            	//logger.info("AdjustPriceOrderDetailV2ServiceImpl|findBusinessStoreIdApi|businessStoreInfo:{}.", responseEntity.getBody());
                MdmBusinessBaseDTO businessBaseDTO = responseEntity.getBody().stream().findFirst().get();
                if(Objects.isNull(businessBaseDTO) || StringUtils.isBlank(businessBaseDTO.getExtend())){
                    return null;
                }
                logger.info("AdjustPriceOrderDetailV2ServiceImpl|findBusinessStoreIdApi|extend:{}.", businessBaseDTO.getExtend());
                JSONObject jsonObject = JSON.parseObject(businessBaseDTO.getExtend());
                if(Objects.isNull(jsonObject) || jsonObject.isEmpty()){
                    return null;
                }
                JSONArray jsonArray = jsonObject.getJSONArray("b2bJoinMallSellerList");
                if(Objects.isNull(jsonArray) || jsonArray.isEmpty() || jsonArray.size() == 0){
                    return null;
                }
                JSONObject storeJson = jsonArray.getJSONObject(0);
                if(storeJson.containsKey("storeId")){
                    return storeJson.getLong("storeId");
                }
            }
        } catch (Exception e) {
        	logger.error("AdjustPriceOrderDetailV2ServiceImpl|findBusinessStoreIdApi|调用store根据连锁ID获取运营店失败:", e);
            return null;
        }
        return null;
    }

    /**
     * 根据调价单验证并获取价格类型代码列表
     *
     * 当调价单中的价格类型不包含零售价和会员价时，会记录日志并返回空列表
     *
     * @param adjustPriceOrder 调价单对象，包含调价信息
     * @return 价格类型代码列表，如果不符合条件则返回空列表
     */
    private List<String> validateAndGetPriceTypeCodeList(AdjustPriceOrder adjustPriceOrder) {
        List<String> priceTypeCodeLimitList = getPriceTypeCodeLimitList(adjustPriceOrder);
        if(CollectionUtils.isEmpty(priceTypeCodeLimitList)){
            logger.info("调价单价格类型不包含零售价和会员价|adjustPriceOrder:{}",adjustPriceOrder);
            return Collections.emptyList();
        }
        return priceTypeCodeLimitList;
    }

    /**
     * 获取调价单行门店缓存获取逻辑
     * @param adjustCode
     * @param detailId
     * @param goodsNo
     * @return
     */
    private List<Long> getValidStoreIdList(String adjustCode, Long detailId, String goodsNo) {
        AdjustPriceOrderDetailStoreIdCacheDTO detailStoreIdCache =
            getAdjustPriceOrderDetailStoreIdCache(adjustCode, detailId);
        if(null == detailStoreIdCache){
            return Collections.emptyList();
        }

        List<Long> storeIdList = detailStoreIdCache.getStoreIdList();
        if (CollectionUtils.isEmpty(storeIdList)) {
            logger.debug("门店列表为空|adjustCode:{}|detailId:{}|goodsNo:{}", adjustCode, detailId, goodsNo);
            return Collections.emptyList();
        }
        return storeIdList;
    }

    /**
     * 获取医保限价规则缓存获取逻辑
     * @param storeIdList
     * @param goodsNo
     * @return
     */
    private List<PriceLimitControlStoreCache> getPriceLimitControlCache(List<Long> storeIdList, String goodsNo) {
        if(CollectionUtils.isEmpty(storeIdList)) {
            return Collections.emptyList();
        }

        List<PriceLimitControlStoreCache> priceLimitControlCacheList =
            getPriceLimitControlBatchStoreCache(storeIdList, goodsNo);
        return CollectionUtils.isEmpty(priceLimitControlCacheList) ?
            Collections.emptyList() : priceLimitControlCacheList;
    }

    /**
     * 核心的价格限制检查逻辑
     * @param adjustCode
     * @param goodsNo
     * @param detailId
     * @param priceTypeCodeLimitList
     * @param interventionCode
     * @return
     */
    private PriceLimitCheckResult interceptMedicarePriceLimitCheck(String adjustCode, String goodsNo, Long detailId,
                                                  List<String> priceTypeCodeLimitList,
                                                  Integer interventionCode) {
        PriceLimitCheckResult result = new PriceLimitCheckResult();
        result.setAdjustCode(adjustCode);
        result.setDetailId(detailId);
        // 获取有效的门店列表
        List<Long> storeIdList = getValidStoreIdList(adjustCode, detailId, goodsNo);
        if(CollectionUtils.isEmpty(storeIdList)) {
            return result;
        }

        // 获取价格限制缓存
        List<PriceLimitControlStoreCache> priceLimitControlCacheList =
            getPriceLimitControlCache(storeIdList, goodsNo);
        if(CollectionUtils.isEmpty(priceLimitControlCacheList)){
            return result;
        }

        // 检查零售价限制
        if(priceTypeCodeLimitList.contains(PriceTypeModeEnum.LSJ.getPriceTypeCode())){
            BigDecimal lsjMinLimitPrice = getMinLimitPrice(priceLimitControlCacheList,
                interventionCode, "lsjLimitPrice");
            if (lsjMinLimitPrice != null) {
                result.setLsjMinLimitPrice(lsjMinLimitPrice);
                result.setHasLsjLimit(true);
                logger.info("零售价最小限价|adjustCode:{}|detailId:{}|goodsNo:{}|minPrice:{}",
                    adjustCode, detailId, goodsNo, lsjMinLimitPrice);
            }
        }

        // 检查会员价限制
        if(priceTypeCodeLimitList.contains(PriceTypeModeEnum.HYJ.getPriceTypeCode())){
            BigDecimal hyjMinLimitPrice = getMinLimitPrice(priceLimitControlCacheList,
                interventionCode, "hyjLimitPrice");
            if (hyjMinLimitPrice != null) {
                result.setHyjMinLimitPrice(hyjMinLimitPrice);
                result.setHasHyjLimit(true);
                logger.info("会员价最小限价|adjustCode:{}|detailId:{}|goodsNo:{}|minPrice:{}",
                    adjustCode, detailId, goodsNo, hyjMinLimitPrice);
            }
        }

        return result;
    }

    /**
     * 医保限价拦截验证的专用方法
     * @param price
     * @param limitPrice
     * @param priceType
     * @param adjustCode
     * @param detailId
     * @param goodsNo
     * @return
     */
    private boolean validatePriceAgainstLimit(BigDecimal price, BigDecimal limitPrice, String priceType,
                                              String adjustCode, Long detailId, String goodsNo) {
        if(price == null || limitPrice == null) {
            return false;
        }

        if(limitPrice.compareTo(PriceUtil.getYuanFromFenWithNull(price)) < 0) {
            logger.info("{}价格超过限价|adjustCode:{}|detailId:{}|goodsNo:{}|price:{}|limitPrice:{}",
                priceType, adjustCode, detailId, goodsNo, price, limitPrice);
            return true;
        }
        return false;
    }

    /**
     * 构建调用规则引擎参数
     * @param adjustCode
     * @param goodsNo
     * @param detailId
     * @param priceTypeCodeLimitList
     * @param interventionCode
     * @return
     */
    private Map<String,Object> buildRuleParamMap(String adjustCode, String goodsNo, Long detailId,List<String> priceTypeCodeLimitList,Integer interventionCode){
        Map<String, Object> ruleParam = new HashMap<>();
        ruleParam.put("adjustCode",adjustCode);
        ruleParam.put("goodsNo",goodsNo);
        ruleParam.put("detailId",detailId);
        ruleParam.put("priceTypeCodeLimitList",priceTypeCodeLimitList);
        ruleParam.put("interventionCode",interventionCode);
        logger.info("调用规则引擎参数|adjustCode:{}|goodsNo:{}|detailId:{}|priceTypeCodeLimitList:{}|interventionCode:{}",
            adjustCode,goodsNo,detailId,priceTypeCodeLimitList,interventionCode);
        return ruleParam;
    }

}
