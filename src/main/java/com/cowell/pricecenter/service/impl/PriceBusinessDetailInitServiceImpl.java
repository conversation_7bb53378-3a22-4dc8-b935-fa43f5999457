package com.cowell.pricecenter.service.impl;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.pricecenter.config.AdjustPriceConfig;
import com.cowell.pricecenter.entity.PriceBusinessDetail;
import com.cowell.pricecenter.entity.PriceOrgGoods;
import com.cowell.pricecenter.enums.OrgLevelTypeEnum;
import com.cowell.pricecenter.enums.OrgTypeEnum;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.service.IPriceOrgGoodsService;
import com.cowell.pricecenter.service.PriceBusinessDetailInitService;
import com.cowell.pricecenter.service.PriceBusinessDetailService;
import com.cowell.pricecenter.service.feign.MarketingService;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.cowell.pricecenter.service.feign.vo.PriceModeResult;
import com.cowell.pricecenter.service.feign.vo.PriceModelQueryOrgParam;
import com.cowell.pricecenter.service.feign.vo.PriceModelQueryparam;
import com.cowell.pricecenter.utils.IdGeneratorUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/9/14 14:17
 */
@Service
public class PriceBusinessDetailInitServiceImpl implements PriceBusinessDetailInitService {

    private final Logger logger = LoggerFactory.getLogger(PriceBusinessDetailInitServiceImpl.class);

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private IPriceOrgGoodsService priceOrgGoodsService;

    @Autowired
    private PriceBusinessDetailService priceBusinessDetailService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private AdjustPriceConfig adjustPriceConfig;

    @Override
    public void initCGJByBusiness(Long businessId) {
        Long orgId = getOrgId(businessId);
        if (Objects.isNull(orgId)) {
            logger.debug("未查询到组织：{}", businessId);
            return;
        }
        List<PriceModelQueryOrgParam> queryOrgParamList = Lists.newArrayList();
        PriceModelQueryOrgParam priceParam = new PriceModelQueryOrgParam(OrgLevelTypeEnum.BUSINESS.getType(),null, businessId);
        queryOrgParamList.add(priceParam);

        int page = 1, pageSize = 100;
        List<PriceOrgGoods> priceOrgGoods;
        while (true) {
            priceOrgGoods = priceOrgGoodsService.listByOrgId(orgId, page, pageSize);
            if (CollectionUtils.isEmpty(priceOrgGoods)) {
                logger.debug("未查询到商品：{}，page:{}", businessId, page);
                break;
            }
            page++;
            List<PriceModelQueryparam> priceModelQueryParamList = Lists.newArrayList();

            priceOrgGoods.forEach(item -> {
                PriceModelQueryparam queryParam = new PriceModelQueryparam();
                queryParam.setGoodsNo(item.getGoodsNo());
                queryParam.setOrgParams(queryOrgParamList);
                priceModelQueryParamList.add(queryParam);
            });

            ResponseEntity<List<PriceModeResult>> marketingResponse = marketingService.getPriceModelByGoodsNoListAndOrgs(priceModelQueryParamList);
            if(marketingResponse == null || marketingResponse.getStatusCode() != HttpStatus.OK) {
                logger.debug("未查询到促销价格：{}", businessId);
                continue;
            }
            List<PriceModeResult> priceModeResultList = marketingResponse.getBody();
            if (CollectionUtils.isEmpty(priceModeResultList)) {
                continue;
            }
            List<PriceBusinessDetail> priceBusinessDetailList = new ArrayList<>();
            List<Long> batchIdList = IdGeneratorUtils.getPriceBusinessDetailIdBatch(priceModeResultList.size());
            priceModeResultList.forEach(priceModeResult -> {
                PriceBusinessDetail priceBusinessDetail = new PriceBusinessDetail();
                priceBusinessDetail.setId(String.valueOf(batchIdList.remove(0)));
                priceBusinessDetail.setBusinessId(businessId);
                priceBusinessDetail.setPrice(priceModeResult.getPriceDetailVOS().get(0).getLastPurchasingPrice());
                priceBusinessDetail.setGoodsNo(priceModeResult.getGoodsNo());
                priceBusinessDetail.setChannelId(adjustPriceConfig.getDefaultChannelId());
                priceBusinessDetail.setPriceTypeCode(PTypeEnum.LAST_CGJ.getCode());
                priceBusinessDetail.setPriceTypeId(PTypeEnum.LAST_CGJ.getType().longValue());
                priceBusinessDetail.setPriceTypeName(PTypeEnum.LAST_CGJ.getDesc());
                priceBusinessDetail.setVersion(1);
                priceBusinessDetail.setGmtCreate(new Date());
                priceBusinessDetail.setGmtUpdate(new Date());
                priceBusinessDetailList.add(priceBusinessDetail);
            });
            // 先删除，在增加
            priceBusinessDetailService.save(priceBusinessDetailList);
        }


    }

    private Long getOrgId(Long businessId) {
        ResponseEntity<List<OrgDTO>> responseEntity = permissionService.listOrgByOutId(OrgTypeEnum.BUSINESS.getCode(), Collections.singletonList(businessId));
        if (responseEntity == null || responseEntity.getStatusCode() != HttpStatus.OK) {
            return null;
        }
        List<OrgDTO> list = responseEntity.getBody();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0).getId();
    }
}
