package com.cowell.pricecenter.service.impl;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cowell.pricecenter.service.ImportDataResultService;
import com.cowell.pricecenter.service.dto.ImportErrorDataDTO;
import com.cowell.pricecenter.service.dto.response.AdjustImportResultDTO;
import com.cowell.pricecenter.service.dto.response.ExportFileCubeVO;
import com.google.common.collect.Lists;

@Service
public class ImportDataResultServiceImpl implements ImportDataResultService {

	@Autowired
    private RedissonClient redissonClient;
    
    private static final Integer TIMETOLIVEHOURS = 3;
    
	@Override
	public void sendImportErrorData(ImportErrorDataDTO importErrorData) {
		List<Map<String, Object>> tempList = Lists.newArrayList();
		ExportFileCubeVO instance = ExportFileCubeVO.getInstance(importErrorData.getFileName(), null, importErrorData.getUploadUrl(),importErrorData.getFieldMap(), tempList);
		RList<Map<String, Object>> resultList = redissonClient.getList(importErrorData.getDataKey());
		resultList.clear();
		resultList.addAll(importErrorData.getListData());
		resultList.expire(TIMETOLIVEHOURS,TimeUnit.HOURS);
		redissonClient.getBucket(importErrorData.getStructureKey()).set(instance,TIMETOLIVEHOURS,TimeUnit.HOURS);
	}

	@Override
	public void sendImportResult(String redisKey, int total, int successCount, int failCount, int status) {
		AdjustImportResultDTO importResult = new AdjustImportResultDTO();
		importResult.setTotal(total);
		importResult.setSuccessCount(successCount);
		importResult.setFailCount(failCount);
		importResult.setStatus(status);
		redissonClient.getBucket(redisKey).set(importResult,TIMETOLIVEHOURS,TimeUnit.HOURS);
	}

	@Override
	public AdjustImportResultDTO getAdjustImportResult(String redisKey) {
		AdjustImportResultDTO importResult = new AdjustImportResultDTO();
    	RBucket<AdjustImportResultDTO> bucket = redissonClient.getBucket(redisKey);
    	if(bucket.isExists()) {
    		importResult = bucket.get();
    	}
   		return importResult;
	}

	@Override
	public ExportFileCubeVO<Map<String, Object>> selectErrorData(String dataKey, String structureKey, Integer page,
			Integer pageSize) {
		if(null==page || page<0) {
			page = 1;
		}
		if(null==pageSize || pageSize<0) {
			pageSize = 10;
		}
		int fromIndex = (page-1)*pageSize;
		int toIndex = fromIndex+pageSize;
		RList<Map<String, Object>> dataList = redissonClient.getList(dataKey);
		List<Map<String, Object>> exceptionDetail = dataList.range(fromIndex, toIndex);
		if(CollectionUtils.isEmpty(exceptionDetail)) {
			exceptionDetail = Lists.newArrayList();
		}
		RBucket<ExportFileCubeVO> bucket = redissonClient.getBucket(structureKey);
		ExportFileCubeVO exportFileCubeVO = bucket.get();
		if(null==exportFileCubeVO) {
			exportFileCubeVO = new ExportFileCubeVO();
		}
		exportFileCubeVO.setDataList(exceptionDetail);
		return exportFileCubeVO;
	}

}
