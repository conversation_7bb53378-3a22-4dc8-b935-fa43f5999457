package com.cowell.pricecenter.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import com.cowell.pricecenter.entity.PriceBusinessDetailHistory;
import com.cowell.pricecenter.entity.PriceBusinessDetailHistoryExample;
import java.util.List;
import com.cowell.pricecenter.mapper.PriceBusinessDetailHistoryMapper;
import com.cowell.pricecenter.mapper.extension.PriceBusinessDetailHistoryExtMapper;
import com.cowell.pricecenter.service.PriceBusinessDetailHistoryService;
import com.cowell.pricecenter.service.dto.request.AdjustPriceOrderDetailEditV2;

/**
 * <AUTHOR>
 * @date 2022/7/4 14:25
 */
@Service
public class PriceBusinessDetailHistoryServiceImpl implements PriceBusinessDetailHistoryService {

    @Resource
    private PriceBusinessDetailHistoryMapper priceBusinessDetailHistoryMapper;
    @Resource
    private PriceBusinessDetailHistoryExtMapper priceBusinessDetailHistoryExtMapper;

    @Override
    public long countByExample(PriceBusinessDetailHistoryExample example) {
        return priceBusinessDetailHistoryMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PriceBusinessDetailHistoryExample example) {
        return priceBusinessDetailHistoryMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(String id) {
        return priceBusinessDetailHistoryMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PriceBusinessDetailHistory record) {
        return priceBusinessDetailHistoryMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceBusinessDetailHistory record) {
        return priceBusinessDetailHistoryMapper.insertSelective(record);
    }

    @Override
    public List<PriceBusinessDetailHistory> selectByExample(PriceBusinessDetailHistoryExample example) {
        return priceBusinessDetailHistoryMapper.selectByExample(example);
    }

    @Override
    public PriceBusinessDetailHistory selectByPrimaryKey(String id) {
        return priceBusinessDetailHistoryMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(PriceBusinessDetailHistory record, PriceBusinessDetailHistoryExample example) {
        return priceBusinessDetailHistoryMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceBusinessDetailHistory record, PriceBusinessDetailHistoryExample example) {
        return priceBusinessDetailHistoryMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceBusinessDetailHistory record) {
        return priceBusinessDetailHistoryMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceBusinessDetailHistory record) {
        return priceBusinessDetailHistoryMapper.updateByPrimaryKey(record);
    }

	@Override
	@Transactional(rollbackFor = {Exception.class})
	public int batchInsert(List<PriceBusinessDetailHistory> list) {
		return priceBusinessDetailHistoryExtMapper.batchInsert(list);
	}

	@Override
	public void deleteHistoryRefPrice(String adjustCode,String goodsNo, Integer channelId,Long businessId,List<String> priceTypeCodes) {
		priceBusinessDetailHistoryExtMapper.deleteHistoryRefPrice(adjustCode,goodsNo, channelId,businessId,priceTypeCodes);
	}

}

