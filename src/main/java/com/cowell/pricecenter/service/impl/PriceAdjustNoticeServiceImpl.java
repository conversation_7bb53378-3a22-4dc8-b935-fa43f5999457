package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.cache.CacheVar;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.AdjustPriceOrderDetailMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderOrgStoreDetailMapper;
import com.cowell.pricecenter.mapper.PriceChangeNoticeMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderDetailExMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderOrgStoreDetailExtMapper;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.security.SecurityUtils;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.MdmStoreBaseDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.NoticeReminder;
import com.cowell.pricecenter.service.dto.response.PriceDictionaryDTO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.priceAdjustNotice.PriceAdjustDetailDTO;
import com.cowell.pricecenter.service.dto.response.priceAdjustNotice.PriceAdjustNoticeDTO;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.utils.AuxTools;
import com.cowell.pricecenter.utils.CriteriaUtils;
import com.cowell.pricecenter.utils.PriceUtil;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.google.common.collect.Lists;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 价格调整通知服务类
 *
 * Created by schuangxigang on 2022/3/28 19:29.
 */
@Service
public class PriceAdjustNoticeServiceImpl implements PriceAdjustNoticeService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PriceChangeNoticeMapper priceChangeNoticeMapper;
    @Autowired
    private AdjustPriceOrderMapper adjustPriceOrderMapper;
    @Autowired
    private AdjustPriceOrderDetailMapper adjustPriceOrderDetailMapper;
    @Autowired
    private PriceQueryService priceQueryService;
    @Autowired
    private PriceDictionaryService priceDictionaryService;

    @Autowired
    private IPermissionExtService permissionExtService;
    @Autowired
    private PriceChannelService priceChannelService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private AdjustPriceOrderOrgStoreDetailMapper adjustPriceOrderOrgStoreDetailMapper;
    @Autowired
    private AdjustPriceOrderOrgStoreDetailExtMapper adjustPriceOrderOrgStoreDetailExtMapper;
    @Autowired
    private AdjustPriceOrderDetailExMapper adjustPriceOrderDetailExMapper;
    @Autowired
    private StoreService storeService;

    @Value("${price.adjust.notice.query.days:1}")
    private int adjustNoticeQueryDays;
    @Value("${price.adjust.notice.reminder.times:3}")
    private int adjustNoticeReminderTimes;
    @Value("${price.adjust.notice.reminder.roles:}")
    private String adjustNoticeReminderRoles;

    @Override
    public PageResult<PriceAdjustNoticeDTO> pageNoticeList(PriceAdjustNoticeQueryParam param) {
        TokenUserDTO tokenUserDTO = AuxTools.getCurrentUserToken(param.getUserId());
        try {
            PriceChangeNoticeExample example = new PriceChangeNoticeExample();
            PriceChangeNoticeExample.Criteria criteria = example.createCriteria();
            if (null != param.getChannelId()) {
                criteria.andChannelLike("%"+param.getChannelId()+"%");
            }
            if (StringUtils.isNotBlank(param.getPriceTypeCode())) {
                criteria.andAdjustPriceTypeLike("%"+param.getPriceTypeCode()+"%");
            }
            if (StringUtils.isNotBlank(param.getAdjustCode())) {
                criteria.andAdjustCodeEqualTo(param.getAdjustCode());
            }
            if (StringUtils.isNotBlank(param.getEffectTime())) {
                String[] times = param.getEffectTime().split(",");
                try {
                    criteria.andAdjustEffectTimeBetween(DateUtils.parseDate(times[0], "yyyy-MM-dd"), DateUtils.parseDate(times[1], "yyyy-MM-dd"));
                } catch (ParseException e) {
                    throw new BusinessErrorException("生效时间解析错误");
                }
            }
            List<Long> userDataOutIdList;
            if (CollectionUtils.isEmpty(param.getStoreIdList()) && null != param.getBusinessId()) {
                userDataOutIdList = getUserDataStoreIdList(tokenUserDTO, param.getBusinessId(), param.isStoreCenter());
            } else {
                userDataOutIdList = permissionExtService.retainUserDataScopeOutIdListByType(tokenUserDTO.getUserId(), OrgTypeEnum.STORE.getCode(), param.getStoreIdList());
            }
            if(CollectionUtils.isEmpty(userDataOutIdList)) {
            	return new PageResult<>(0L, new ArrayList<>());
            }
            //门店数量大于一定数量时 转换连锁id
            if(userDataOutIdList.size()>Constants.MAX_STORE_COUNT_TRANSFORM_BUSINESS) {
            	if(null != param.getBusinessId()) {
            		criteria.andBusinessIdEqualTo(CacheVar.businessOrgIdAndOutIdMapping.get(param.getBusinessId()));
            	}else {
            		criteria.andBusinessIdIn(permissionExtService.retainUserDataScopeOutIdListByType(tokenUserDTO.getUserId(), OrgTypeEnum.BUSINESS.getCode(), null));
            	}
            }else {
            	criteria.andStoreIdIn(userDataOutIdList);
            }
            if (CollectionUtils.isNotEmpty(param.getIds())) {
                criteria.andIdIn(param.getIds());
            }
            // 商家中心取数 T-1
            if (param.isStoreCenter()) {
                Date now = new Date();
                criteria.andGmtCreateBetween(DateUtils.addDays(now, -adjustNoticeQueryDays), now);
            }
            long count = priceChangeNoticeMapper.countByExample(example);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }

            example.setOrderByClause("gmt_create desc,adjust_effect_time desc");
            example.setOffset((param.getPage()-1) * param.getPageSize());
            example.setLimit(param.getPageSize());
            List<PriceChangeNotice> pageList = priceChangeNoticeMapper.selectByExample(example);
            List<String> adjustCodes = pageList.stream().map(PriceChangeNotice::getAdjustCode).distinct().collect(Collectors.toList());
            // 查询调价单
            Map<String, AdjustPriceOrder> orderMap = priceQueryService.getAdjustOrderMap(adjustCodes);
            // 查询价格类型
            List<PriceDictionaryDTO> priceTypes = priceDictionaryService.getChildrenByCode(DictCodeEnum.PRICE_TYPE.getCode());
            // 查询渠道
            List<PriceDictionaryDTO> priceChannels = priceDictionaryService.getChildrenByCode(DictCodeEnum.CHANNEL.getCode());

            List<PriceAdjustNoticeDTO> dtoList = pageList.stream().map(v -> {
                PriceAdjustNoticeDTO dto = new PriceAdjustNoticeDTO();
                BeanUtils.copyProperties(v, dto);
                AdjustPriceOrder order = orderMap.get(v.getAdjustCode());
                if (order != null) {
                    dto.setAdjustTypeName(order.getAdjustType() == null ? "" : AdjustTypeEnum.getNameV2(order.getAdjustType()));
                }
                dto.setMdmStore(CacheVar.getSapCode(dto.getStoreId()));
                dto.setAdjustReason(v.getReason());
                dto.setPriceType(String.join(",", priceTypes.stream().filter(dict -> Arrays.asList(v.getAdjustPriceType().split(",")).contains(dict.getDictCode())).map(PriceDictionaryDTO::getDictName).collect(Collectors.toSet())));
                dto.setChannel(String.join(",", priceChannels.stream().filter(dict -> Arrays.asList(v.getChannel().split(",")).contains(dict.getDictCode())).map(PriceDictionaryDTO::getDictName).collect(Collectors.toSet())));
                dto.setRowColorIdx(getRowColorIdx(v.getAdjustEffectTime()));
                return dto;
            }).collect(Collectors.toList());
            // 设置已提醒次数+1
            if (param.isStoreCenter()) {
                RBucket<Integer> remindCount = redissonClient.getBucket(RedisKeysConstant.PRICE_ADJUST_NOTICE_REMIND_KEY + tokenUserDTO.getUserId() + "_" + tokenUserDTO.getBusinessId());
                remindCount.setAsync(Optional.ofNullable(remindCount.get()).orElse(0) + 1, 1L, TimeUnit.DAYS);
            }
            if (param.isExport()) {
                dtoList = getNoticeExportList(dtoList);
            }
            return new PageResult<>(count, dtoList);
        } catch (Exception e) {
            logger.error("获取价格调整通知列表异常", e);
            throw e;
        }
    }

    /**
     * 根据门店id查询 门店属性是否 加盟-单体-药证不含济加盟
     * @param storeId
     * @return
     */
    private Boolean isChainNameVisible(Long storeId){
        if(null == storeId){
            return false;
        }
        Boolean isChainNameVisible = false;
        try {
            ResponseEntity<MdmStoreBaseDTO> response = storeService.findByStoreId(storeId);
            if (response != null && response.getBody() != null) {
                MdmStoreBaseDTO mdmStoreBaseDTO =response.getBody();
                if(StoreAttrTypeEnum.JOIN_SINGLE_NO_CONTAIN.getMessage().equals(mdmStoreBaseDTO.getStoreAttr())){
                    isChainNameVisible = true;
                }
            }
        } catch (Exception e) {
            logger.error("根据store id获取mdm的门店信息异常|isChainNameVisible|storeId:{}",storeId, e);
        }
        return isChainNameVisible;
    }

    @Override
    public PageResult<PriceAdjustDetailDTO> pageDetailList(PriceAdjustDetailQueryParam param) {
        try {
            Optional.ofNullable(param.getAdjustCode()).orElseThrow(() -> new BusinessErrorException("调价单号不能为空"));
            Optional.ofNullable(param.getStoreId()).orElseThrow(() -> new BusinessErrorException("门店Id不能为空"));

            PriceChangeNoticeExample noticeExample = new PriceChangeNoticeExample();
            noticeExample.createCriteria().andStoreIdEqualTo(param.getStoreId()).andAdjustCodeEqualTo(param.getAdjustCode()).andNoticeTypeEqualTo(PriceChangeNoticeTypeEnum.CHANGE_TYPE.getCode());
            long noticeCount = priceChangeNoticeMapper.countByExample(noticeExample);
            if (noticeCount == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }

            List<PriceChangeNotice> notices = priceChangeNoticeMapper.selectByExample(noticeExample);
            Long storeId = null;
            if(CollectionUtils.isNotEmpty(notices)){
                storeId = notices.get(0).getStoreId();
            }
            Boolean isChainNameVisible = isChainNameVisible(storeId);
            AdjustPriceOrderExample orderExample = new AdjustPriceOrderExample();
            orderExample.createCriteria().andAdjustCodeEqualTo(param.getAdjustCode());
            List<AdjustPriceOrder> orderList = adjustPriceOrderMapper.selectByExample(orderExample);
            if (CollectionUtils.isEmpty(orderList)) {
                throw new BusinessErrorException("调价单"+param.getAdjustCode()+"不存在");
            }

            AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
            AdjustPriceOrderDetailExample.Criteria criteria = example.createCriteria();
            List<String> idStrList = adjustPriceOrderOrgStoreDetailExtMapper.selectAdjustOrderDetailIdByStore(param.getStoreId(), Lists.newArrayList(param.getAdjustCode()));
            if(CollectionUtils.isNotEmpty(idStrList)){
                List<Long> idLongList = idStrList.stream().filter(v->StringUtils.isNotBlank(v)).map(v->Long.parseLong(v)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(idLongList)){
                    criteria.andIdIn(idLongList);
                }
            }
            criteria.andPriceTypeCodeNotEqualTo(PTypeEnum.GJYJS.getCode());
            criteria.andDetailAuditStatusEqualTo();
            criteria.andAdjustCodeEqualTo(param.getAdjustCode());
            AdjustPriceOrderDetailExample.Criteria criteria2 = example.or();
            if (null != param.getChannelId()) {
                criteria.andChannelIdEqualTo(param.getChannelId());
            }
            if (StringUtils.isNotBlank(param.getPriceTypeCode())) {
                criteria.andPriceTypeCodeEqualTo(param.getPriceTypeCode());
            }
            if (StringUtils.isNotBlank(param.getGoodsName())) {
                CriteriaUtils.copyCriteria(criteria, criteria2);
                criteria.andCurNameLike("%"+param.getGoodsName().trim()+"%");
                criteria2.andGoodsNoLike("%"+param.getGoodsName().trim()+"%");
            }
            if (CollectionUtils.isNotEmpty(param.getIds())) {
                criteria.andIdIn(param.getIds());
            }
            long count = adjustPriceOrderDetailMapper.countByExample(example);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }

            example.setOffset((param.getPage()-1) * param.getPageSize());
            example.setLimit(param.getPageSize());
            example.setOrderByClause(" id DESC");
            List<AdjustPriceOrderDetail> pageList = adjustPriceOrderDetailMapper.selectByExample(example);
            // 查询渠道
            List<Integer> channelIds = pageList.stream().map(AdjustPriceOrderDetail::getChannelId).distinct().collect(Collectors.toList());
            Map<Integer, PriceChannel> priceChannelMap = priceChannelService.getChannelMap(channelIds);

            List<String> goodsNoList = pageList.stream().map(AdjustPriceOrderDetail::getGoodsNo).distinct().collect(Collectors.toList());
            Map<String, AdjustPriceOrderOrgStoreDetail> orgStoreDetailMap = selectOrgStoreDetailList(param.getStoreId(), Lists.newArrayList(param.getAdjustCode()), goodsNoList);

            List<PriceAdjustDetailDTO> dtoList = pageList.stream().map(v -> {
                PriceAdjustDetailDTO dto = new PriceAdjustDetailDTO();
                String sku = detailSku(param.getStoreId(),v.getGoodsNo(),v.getPriceTypeCode(),v.getChannelId());
                dto.set(v, orderList.get(0), notices.get(0),orgStoreDetailMap.get(sku),isChainNameVisible);
                dto.setChannelName(Optional.ofNullable(priceChannelMap.get(v.getChannelId())).map(PriceChannel::getChannelName).orElse(""));
                dto.setAdjustReason(v.getReason());
                return dto;
            }).collect(Collectors.toList());
            if (param.isExport()) {
                dtoList = getDetailExportList(dtoList);
            }
            return new PageResult<>(count, dtoList);
        } catch (Exception e) {
            logger.error("获取价格调整明细列表异常", e);
            throw e;
        }
    }

    private List<PriceAdjustNoticeDTO> getNoticeExportList(List<PriceAdjustNoticeDTO> list) {
        return list.stream().peek(v -> {
            v.setAdjustEffectTimeStr(null == v.getAdjustEffectTime() ? "" : DateFormatUtils.format(v.getAdjustEffectTime(), "yyyy-MM-dd HH:mm:ss"));
            v.setGmtCreateStr(null == v.getGmtCreate() ? "" : DateFormatUtils.format(v.getGmtCreate(), "yyyy-MM-dd HH:mm:ss"));
        }).collect(Collectors.toList());
    }

    private List<PriceAdjustDetailDTO> getDetailExportList(List<PriceAdjustDetailDTO> list) {
        return list.stream().peek(v -> {
            v.setPriceYuan(PriceUtil.getYuanFromFenWithNull(v.getPrice()));
            v.setPriceOldYuan(PriceUtil.getYuanFromFenWithNull(v.getPriceOld()));
            v.setPriceNewYuan(PriceUtil.getYuanFromFenWithNull(v.getPriceNew()));
            v.setEffectTimeStr(null == v.getEffectTime() ? "" : DateFormatUtils.format(v.getEffectTime(), "yyyy-MM-dd HH:mm:ss"));
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<PriceAdjustDetailDTO> pageDetailList(OrderViewParam param) {
        // 取数 T-1
        Date now = new Date();
        try {
        	Date startDate = com.cowell.pricecenter.web.rest.util.DateUtils.stringToDate(param.getStartDateStr().replace(Constants.LINE, " "));
        	Date endDate = com.cowell.pricecenter.web.rest.util.DateUtils.stringToDate(param.getEndDateStr().replace(Constants.LINE, " "));
            PriceChangeNoticeExample example = new PriceChangeNoticeExample();
            example.createCriteria().andStoreIdEqualTo(param.getStoreId()).andGmtCreateBetween(startDate, endDate).andNoticeTypeEqualTo(PriceChangeNoticeTypeEnum.CHANGE_TYPE.getCode());
            long count = priceChangeNoticeMapper.countByExample(example);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }

            List<PriceChangeNotice> notices = priceChangeNoticeMapper.selectByExample(example);
            List<String> adjustCodes = notices.stream().map(PriceChangeNotice::getAdjustCode).distinct().collect(Collectors.toList());
    		Map<String, PriceChangeNotice> noticesMap = notices.stream().collect(Collectors.toMap(PriceChangeNotice::getAdjustCode, Function.identity(), (v1, v2) -> v1));

            Map<String, AdjustPriceOrder> orderMap = priceQueryService.getAdjustOrderMap(adjustCodes);

            GetAdjustPriceOrderDetailParam getAdjustPriceOrderDetailParam = new GetAdjustPriceOrderDetailParam();
            getAdjustPriceOrderDetailParam.setAdjustCodeList(adjustCodes);
            List<String> idStrList = adjustPriceOrderOrgStoreDetailExtMapper.selectAdjustOrderDetailIdByStore(param.getStoreId(), adjustCodes);
            if(CollectionUtils.isNotEmpty(idStrList)){
                List<Long> idLongList = idStrList.stream().filter(v->StringUtils.isNotBlank(v)).map(v->Long.parseLong(v)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(idLongList)){
                    getAdjustPriceOrderDetailParam.setAdjustDetailIdList(idLongList);
                }
            }
            getAdjustPriceOrderDetailParam.setPage((param.getPage()-1) * param.getPageSize());

            long detailCount = adjustPriceOrderDetailExMapper.selectAdjustPriceOrderDetailCountNoticePage(getAdjustPriceOrderDetailParam);
            if (detailCount == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            List<AdjustPriceOrderDetail> pageList = adjustPriceOrderDetailExMapper.selectAdjustPriceOrderDetailListNoticePage(getAdjustPriceOrderDetailParam);
            // 查询渠道
            List<Integer> channelIds = pageList.stream().map(AdjustPriceOrderDetail::getChannelId).distinct().collect(Collectors.toList());
            Map<Integer, PriceChannel> priceChannelMap = priceChannelService.getChannelMap(channelIds);
            List<String> goodsNoList = pageList.stream().map(AdjustPriceOrderDetail::getGoodsNo).distinct().collect(Collectors.toList());
            Map<String, AdjustPriceOrderOrgStoreDetail> orgStoreDetailMap = selectOrgStoreDetailList(param.getStoreId(), adjustCodes, goodsNoList);
            List<PriceAdjustDetailDTO> dtoList = pageList.stream().map(v -> {
                PriceAdjustDetailDTO dto = new PriceAdjustDetailDTO();
                String sku = detailSku(param.getStoreId(),v.getGoodsNo(),v.getPriceTypeCode(),v.getChannelId());
                AdjustPriceOrderOrgStoreDetail orgStoreDetail = orgStoreDetailMap.get(sku);
                dto.set(v, orderMap.get(v.getAdjustCode()), noticesMap.get(v.getAdjustCode()), orgStoreDetail,false);
                dto.setChannelName(Optional.ofNullable(priceChannelMap.get(v.getChannelId())).map(PriceChannel::getChannelName).orElse(""));
                dto.setAdjustCode(v.getAdjustCode());
                dto.setAdjustReason(v.getReason());
                return dto;
            }).collect(Collectors.toList());
            return new PageResult<>(detailCount, dtoList);
        } catch (Exception e) {
            logger.error("根据门店Id获取价格调整明细列表异常", e);
            throw e;
        } finally {
		}
    }

    private Map<String, AdjustPriceOrderOrgStoreDetail> selectOrgStoreDetailList(Long storeId,List<String> adjustCodes,List<String> goodsNoList) {
    	AdjustPriceOrderOrgStoreDetailExample orgStoreDetailExample = new AdjustPriceOrderOrgStoreDetailExample();
        AdjustPriceOrderOrgStoreDetailExample.Criteria criteria = orgStoreDetailExample.createCriteria();
        criteria.andStoreIdEqualTo(storeId).andAdjustCodeIn(adjustCodes);
        if(CollectionUtils.isNotEmpty(goodsNoList)){
            criteria.andGoodsNoIn(goodsNoList);
        }
        List<AdjustPriceOrderOrgStoreDetail> orgStoreDetailList = adjustPriceOrderOrgStoreDetailMapper.selectByExample(orgStoreDetailExample);
        Map<String, AdjustPriceOrderOrgStoreDetail> orgStoreDetailMap = orgStoreDetailList.stream().collect(Collectors.toMap(x->detailSku(x.getStoreId(),x.getGoodsNo(),x.getPriceTypeCode(),x.getChannelId()), Function.identity(),(key1, key2) -> key1));
        return orgStoreDetailMap;
    }

    /**
     *
     * @Title: detailSku
     * @Description: 商品价格类型sku
     * @param: @param storeId
     * @param: @param goodsNo
     * @param: @param priceTypeCode
     * @param: @param channelId
     * @param: @return
     * @return: String
     * @throws
     */
    private String detailSku(Long storeId,String goodsNo,String priceTypeCode,Integer channelId) {
    	return storeId+"_"+goodsNo+"_"+priceTypeCode+"_"+channelId;
    }

    @Override
    public NoticeReminder getNoticeReminder() {
        NoticeReminder noticeReminder = new NoticeReminder();
        Date now = new Date();
        try {
            TokenUserDTO tokenUserDTO = SecurityUtils.getCurrentUserToken();
            noticeReminder.setBusinessId(tokenUserDTO.getBusinessId());

            List<Long> userDataOutIdList = getUserDataStoreIdList(tokenUserDTO, null, true);

            PriceChangeNoticeExample example = new PriceChangeNoticeExample();
            example.createCriteria().andBusinessIdEqualTo(tokenUserDTO.getBusinessId())
                .andStoreIdIn(userDataOutIdList)
                .andGmtCreateBetween(DateUtils.addDays(now, -adjustNoticeQueryDays), now);
            long count = priceChangeNoticeMapper.countByExample(example);

            RBucket<Integer> remindCount = redissonClient.getBucket(RedisKeysConstant.PRICE_ADJUST_NOTICE_REMIND_KEY + tokenUserDTO.getUserId() + "_" + tokenUserDTO.getBusinessId());
            logger.info("getNoticeReminder|remindCount={},userId={},businessId={}", remindCount.get(), tokenUserDTO.getUserId(), tokenUserDTO.getBusinessId());
            if (count > 0 && (remindCount.get() == null || remindCount.get() <= adjustNoticeReminderTimes)) {
                noticeReminder.setRemind(true);
            }
            return noticeReminder;
        } catch (Exception e) {
            if (e instanceof BusinessErrorException) {
                logger.info("getNoticeReminder|{}", ((BusinessErrorException) e).getErrorMessage());
            }
            noticeReminder.setRemind(false);
            return noticeReminder;
        }
    }

    /**
     * 获取用户权限范围的门店Id(outId)
     *
     * @param tokenUserDTO
     * @return
     */
    private List<Long> getUserDataStoreIdList(TokenUserDTO tokenUserDTO, Long orgId, boolean storeCenter) {
        boolean checkRole = false;
        List<String> roleCodes = Lists.newArrayList();
        if (storeCenter) {
            // 校验角色
            if (StringUtils.isBlank(adjustNoticeReminderRoles)) {
                throw new BusinessErrorException("没有配置价格调整通知提醒的角色");
            }
            checkRole = true;
            roleCodes = Lists.newArrayList(adjustNoticeReminderRoles.split(","));
        }

        return priceQueryService.getUserDataStoreIdList(tokenUserDTO, orgId, storeCenter, checkRole, roleCodes);
    }

    /**
     * 商家中心价格调整通知列表按颜色展示
     *
     * 1.生效日期-系统日期≤3，此行显示红色.
     *
     * 2.生效日期-系统日期≤7，此行显示黄色.
     *
     * 3.生效日期-系统日期≤30，此行显示绿色.
     *
     * 4.其它为无底色。
     *
     * @param effectTime
     * @return
     */
    private int getRowColorIdx(Date effectTime) {
        if (null == effectTime) {
            return 0;
        }
        Date now = new Date();
        if (effectTime.compareTo(DateUtils.addDays(now, 3)) <= 0) {
            return 1;
        }
        if (effectTime.compareTo(DateUtils.addDays(now, 7)) <= 0) {
            return 2;
        }
        if (effectTime.compareTo(DateUtils.addDays(now, 30)) <= 0) {
            return 3;
        }
        return 0;
    }
}
