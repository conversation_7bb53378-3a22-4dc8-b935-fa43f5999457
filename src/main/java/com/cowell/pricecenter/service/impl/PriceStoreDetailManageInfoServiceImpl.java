package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.entity.PriceStoreDetailExample;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.EditPriceEnum;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.mapper.PriceStoreDetailMapper;
import com.cowell.pricecenter.mq.producer.HisPriceForSelfProducer;
import com.cowell.pricecenter.mq.producer.PricePushYJSPriceTypeToHLProducer;
import com.cowell.pricecenter.mq.vo.HisPriceForSelfMqVO;
import com.cowell.pricecenter.mq.vo.PricePushYJSVo;
import com.cowell.pricecenter.service.PriceStoreDetailManageInfoService;
import com.cowell.pricecenter.service.PriceStoreDetailReadService;
import com.cowell.pricecenter.service.PriceSyncBaseService;
import com.cowell.pricecenter.service.dto.StoreBriefDTO;
import com.cowell.pricecenter.service.dto.request.EditPriceParam;
import com.cowell.pricecenter.service.dto.request.ImportBJHisPriceDTO;
import com.cowell.pricecenter.service.dto.request.PriceManagerParam;
import com.cowell.pricecenter.service.dto.request.PriceQueryV2Param;
import com.cowell.pricecenter.service.dto.response.PriceQueryGoodsNoInfoDTO;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;
import com.cowell.pricecenter.utils.HutoolExcelUtils;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cowell.pricecenter.enums.PTypeEnum.*;
import static org.apache.commons.lang.StringUtils.isBlank;

/**
 * @Auther: zhc
 * @Description:门店价格管理信息业务层实现类
 */
@Service
public class PriceStoreDetailManageInfoServiceImpl extends BasePermissionService implements PriceStoreDetailManageInfoService {

    private final Logger logger = LoggerFactory.getLogger(PriceStoreDetailManageInfoServiceImpl.class);

    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;

    @Autowired
    private PriceSyncBaseService priceSyncBaseService;

    @Autowired
    @Qualifier("queryItemPriceThreadExecutor")
    private AsyncTaskExecutor executor;

    @Autowired
    private PricePushYJSPriceTypeToHLProducer pricePushYJSPriceTypeToHLProducer;

    @Autowired
    private HisPriceForSelfProducer hisPriceForSelfProducer;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    @Autowired
    @Qualifier("queryItemPriceThreadExecutor2")
    private AsyncTaskExecutor threadPoolExecutor2;

    @ApolloJsonValue("${price.hmoPriceTypeList:[]}")
    private List<String> hmoPriceTypeList;

    private final static Set<String> fsjList = new HashSet<>();

    static {
        List<String> asList = Arrays.asList(HBBJ.getCode(), HDAH.getCode(), XNCQ.getCode(), HDFJ.getCode(), XBGS.getCode(),
            HNGD.getCode(), HNGX.getCode(), XNGZ.getCode(), HNHN.getCode(), HBHB.getCode(),
            HZHENAN.getCode(), DBHLJ.getCode(), HZHB.getCode(), HZHUNAN.getCode(), DBJL.getCode(),
            HDJS.getCode(), HDJX.getCode(), DBLN.getCode(), HBNMG.getCode(), XBNX.getCode(),
            XBQH.getCode(), DBLN.getCode(), HBNMG.getCode(), XBNX.getCode(), XBQH.getCode(),
            HDSD.getCode(), HBSX.getCode(), XBSX.getCode(), HDSH.getCode(), XNSC.getCode(),
            HBTJ.getCode(), XNXZ.getCode(), XBXJ.getCode(), XNYN.getCode(), HDZJ.getCode());
        fsjList.addAll(asList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse editItemPrice(EditPriceParam param) {
        //校验参数
        checkEditPriceParam(param);
        HisPriceForSelfMqVO mqVO = new HisPriceForSelfMqVO();
        if (CollectionUtils.isEmpty(param.getBodyList())) {
            logger.info("PriceStoreDetailManageServiceImpl|editItemPrice|bodyList筛选后为空不推送！");
            return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK.getCode(), ReturnCodeEnum.HTTP_STATUS_OK.getMessage());
        }
        BeanUtils.copyProperties(param, mqVO);
        hisPriceForSelfProducer.sendMq(mqVO);
        return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK.getCode(), ReturnCodeEnum.HTTP_STATUS_OK.getMessage());
    }

    private List<PriceQueryV2Param> buildNewQueryParam(List<PriceQueryV2Param> list) {
        // 按照门店查询
        List<PriceQueryV2Param> newList = new ArrayList<>();
        Map<String, List<PriceQueryV2Param>> collect = list.stream().collect(Collectors.groupingBy(k -> k.getStoreId()
            + "_" + k.getPriceTypeCode()
            + "_" + k.getChannelPriceTypeCode()));
        collect.forEach((k, v) -> {
            PriceQueryV2Param param = new PriceQueryV2Param();
            PriceQueryV2Param queryV2Param = v.get(0);
            param.setStoreId(queryV2Param.getStoreId());
            param.setPriceTypeCode(queryV2Param.getPriceTypeCode());
            param.setGoodsNos(v.stream().map(PriceQueryV2Param::getGoodsNo).collect(Collectors.toList()));
            param.setChannelPriceTypeCode(queryV2Param.getChannelPriceTypeCode());
            newList.add(param);
        });
        return newList;
    }

    @Override
    public List<PriceQueryGoodsNoInfoDTO> getPriceByParam(List<PriceQueryV2Param> list) {
        //参数校验
        checkPriceByParam(list);
        List<PriceQueryV2Param> newList = buildNewQueryParam(list);
        List<Future<List<PriceStoreDetail>>> collect = newList.stream().map(priceQueryV2Param -> {
            ItemPriceTask itemPriceTask = new ItemPriceTask(priceQueryV2Param);
            return executor.submit(itemPriceTask);
        }).collect(Collectors.toList());
        List<PriceStoreDetail> details = new ArrayList<>();
        collect.forEach(a -> {
            try {
                details.addAll(a.get());
            } catch (InterruptedException | ExecutionException e) {
                logger.error("PriceStoreDetailManageServiceImpl|getPriceByParam|error：【{}】", e);
            }
        });
        //通过门店商品分组
        Map<String, List<PriceStoreDetail>> goodsNoToPriceMap = details.stream().collect(Collectors.groupingBy(k -> k.getStoreId() + k.getGoodsNo()));
        List<PriceQueryGoodsNoInfoDTO> goodsNoInfoDTOS = new ArrayList<>();
        goodsNoToPriceMap.forEach((k,v)->{
            PriceQueryGoodsNoInfoDTO dto = new PriceQueryGoodsNoInfoDTO();
            if(CollectionUtils.isNotEmpty(v)){
                PriceStoreDetail priceStoreDetail = v.get(0);
                BeanUtils.copyProperties(priceStoreDetail, dto, new String[]{"id", "price"});
            }
            v.forEach(a->{
                String priceTypeCode = a.getPriceTypeCode();
                BigDecimal price = a.getPrice();
                if (StringUtils.isNotBlank(priceTypeCode)) {
                    if (PTypeEnum.LSJ.getCode().equals(priceTypeCode)) {
                        dto.setLsjPrice(price);
                    } else if (PTypeEnum.GJHIS.getCode().equals(priceTypeCode)) {
                        dto.setHisPrice(price);
                    } else if (hmoPriceTypeList.contains(priceTypeCode)) {
                        dto.setHmoPrice(price);
                    } else if (fsjList.contains(priceTypeCode) || Objects.equals(CBJ.getCode(), priceTypeCode)) {
                        dto.setFsjPrice(price);
                    } else if (PTypeEnum.GJYJS.getCode().equals(priceTypeCode)){
                        dto.setGjyjsPrice(price);
                    } else {
                        dto.setPrice(price);
                    }
                }
            });
            goodsNoInfoDTOS.add(dto);
        });
        return goodsNoInfoDTOS;
    }

    @Override
    public List<PriceQueryGoodsNoInfoDTO> getPriceStoreDetailsByParam(PriceQueryV2Param param) {
        logger.info("PriceStoreDetailManageServiceImpl|getPriceStoreDetailsByParam|param：【{}】", param);
        if (CollectionUtils.isEmpty(param.getStoreIds()) || StringUtils.isBlank(param.getGoodsNo())) {
            throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR.getMessage());
        }
        List<PriceStoreDetail> details = getPriceDetailOnTypeCodes(param.getStoreIds(), param.getGoodsNo(), param.getPriceTypeCodes());
        logger.info("PriceStoreDetailManageServiceImpl|getPriceStoreDetailsByParam|details：【{}】", details);
        List<PriceQueryGoodsNoInfoDTO> priceQueryGoodsNoHisDTOS = new ArrayList<>();
        Map<String, PriceStoreDetail> detailMap = details.stream().collect(Collectors.toMap(k -> k.getStoreId() + k.getPriceTypeCode(), a -> a, (k1, k2) -> k1));
        logger.info("PriceStoreDetailManageServiceImpl|getPriceStoreDetailsByParam|detailMap：【{}】", detailMap);
        param.getStoreIds().forEach(store -> {
            PriceQueryGoodsNoInfoDTO hisDTO = new PriceQueryGoodsNoInfoDTO();
            PriceStoreDetail lsj = detailMap.get(store + PTypeEnum.LSJ.getCode());
            PriceStoreDetail his = detailMap.get(store + PTypeEnum.GJHIS.getCode());
            if (Objects.nonNull(lsj) && Objects.nonNull(lsj.getPrice())) {
                if (StringUtils.isBlank(lsj.getBusinessName()) || StringUtils.isBlank(lsj.getStoreName())) {
                    syncBusinessNameAndStoreNameByParam(lsj, Arrays.asList(PTypeEnum.GJHIS.getCode(), PTypeEnum.LSJ.getCode()));
                }
                BeanUtils.copyProperties(lsj, hisDTO);
                hisDTO.setLsjPrice(new BigDecimal(BigDecimalUtils.convertYuanByFen(lsj.getPrice().longValue())));
            }
            if (Objects.nonNull(his) && Objects.nonNull(his.getPrice())) {
                hisDTO.setHisPrice(new BigDecimal(BigDecimalUtils.convertYuanByFen(his.getPrice().longValue())));
            }
            logger.info("PriceStoreDetailManageServiceImpl|getPriceStoreDetailsByParam|hisDTO：【{}】", hisDTO);
            if (Objects.isNull(hisDTO.getId())) {
                StoreBriefDTO storeDTO = priceSyncBaseService.getStoreDTOByStoreId(store);
                if (Objects.nonNull(storeDTO)) {
                    hisDTO.setStoreName(storeDTO.getStoreName());
                    hisDTO.setBusinessId(storeDTO.getBusinessId());
                    hisDTO.setBusinessName(priceSyncBaseService.getBusinessName(storeDTO.getBusinessId()));
                }
                hisDTO.setStoreId(store);
                hisDTO.setLsjPrice(new BigDecimal(-1));
            }
            priceQueryGoodsNoHisDTOS.add(hisDTO);
        });
        return priceQueryGoodsNoHisDTOS;
    }

    @Override
    public List<PriceStoreDetail> getPriceByStoreIdAndGoodsNoListV2(PriceQueryV2Param param) {
        List<String> goodsNos = param.getGoodsNos();
        String priceTypeCode = param.getPriceTypeCode();
        List<PriceStoreDetail> dtoList = new ArrayList<>();

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(param.getStoreId())
            .goodsNoList(goodsNos)
            .build();

        if (PTypeEnum.LSJ.getCode().equals(priceTypeCode)) {
            query.setPriceTypeCode(PTypeEnum.LSJ.getCode());
        } else if (PTypeEnum.GJHIS.getCode().equals(priceTypeCode)) {
            query.setPriceTypeCodeList(Arrays.asList(PTypeEnum.GJHIS.getCode(), PTypeEnum.LSJ.getCode()));
        } else {
            query.setPriceTypeCodeList(Arrays.asList(priceTypeCode, PTypeEnum.GJHIS.getCode(), PTypeEnum.LSJ.getCode()));
        }
        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        if (CollectionUtils.isEmpty(priceList)) {
            return dtoList;
        }
        Map<String, PriceStoreDetail> collect = priceList.stream().collect(Collectors.toMap(k -> k.getGoodsNo() + k.getPriceTypeCode(), Function.identity(), (k1, k2) -> k1));
        for (String goodsNo : goodsNos) {
            PriceStoreDetail price = collect.get(goodsNo + priceTypeCode);
            if (Objects.nonNull(price) && Objects.nonNull(price.getPrice()) && !PTypeEnum.GJHIS.getCode().equals(priceTypeCode)) {
                dtoList.add(price);
                continue;
            }
            PriceStoreDetail hisPrice = collect.get(goodsNo + PTypeEnum.GJHIS.getCode());
            if (Objects.nonNull(hisPrice) && Objects.nonNull(hisPrice.getPrice()) && !Objects.equals(0, hisPrice.getPrice().compareTo(BigDecimal.ZERO))) {
                dtoList.add(hisPrice);
                continue;
            }
            PriceStoreDetail lsjPrice = collect.get(goodsNo + PTypeEnum.LSJ.getCode());
            if (Objects.nonNull(lsjPrice) && Objects.nonNull(lsjPrice.getPrice())) {
                dtoList.add(lsjPrice);
            }
        }
        return dtoList;
    }

    @Override
    public List<PriceStoreDetail> getPriceStoreDetailSByStoreIdAndGoodsNo(Long storeId, String goodsNo) {
        logger.info("PriceStoreDetailManageServiceImpl|getPriceStoreDetailSByStoreIdAndGoodsNo|storeId：【{}】，goodsNo：【{}】", storeId, goodsNo);
        if (Objects.isNull(storeId) || StringUtils.isBlank(goodsNo)) {
            throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR.getMessage());
        }

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .goodsNo(goodsNo)
            .build();

        List<PriceStoreDetail> priceList = priceStoreDetailReadService.query(query);

        if (CollectionUtils.isNotEmpty(priceList)) {
            priceList = priceList.stream().filter(a -> Objects.nonNull(a.getPrice())).collect(Collectors.toList());
        }
        return priceList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editItemPriceSelf(HisPriceForSelfMqVO param) {
        logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|param：【{}】", param);
        Integer editPriceType = param.getEditPiiceType();
        List<EditPriceParam.Body> bodyList = param.getBodyList();
        Long syncDate = param.getSyncDate();
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        //默认价格
        if (Objects.equals(EditPriceEnum.DEFAULT_PRICE.getCode(), editPriceType)) {
            EditPriceParam.Body body = bodyList.get(0);
            String goodsNo = body.getGoodsNo();
            List<String> priceTypeCode = body.getPriceTypeCode();
            for (Long storeId : body.getStoreIds()) {
                Boolean isHmoFlag = CollectionUtils.isNotEmpty(priceTypeCode) && hmoPriceTypeList.containsAll(priceTypeCode);
                if (isHmoFlag) {
                    priceTypeCode.addAll(fsjList);
                }
                //取消门店商品对应所以分省价格
                List<List<String>> partition = Lists.partition(priceTypeCode, 10);
                int count = 0;
                for (List<String> codes : partition) {
                    PriceStoreDetailExample priceStoreDetailExample = new PriceStoreDetailExample();
                    priceStoreDetailExample.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoEqualTo(goodsNo).andPriceTypeCodeIn(codes);
                    int i = priceStoreDetailMapper.deleteByExample(priceStoreDetailExample);
                    count = count + i;
                }
                logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|默认价格|取消门店商品分省价格|门店:【{}】，商品:【{}】,数量为：{}", storeId, goodsNo, count);
                //取消门店商品对应互医价格
                if (priceTypeCode.contains(GJHIS.getCode())) {
                    List<PriceStoreDetail> priceStoreDetails = getOnePriceStoreDetail(storeId, goodsNo, PTypeEnum.GJHIS.getCode());
                    if (CollectionUtils.isEmpty(priceStoreDetails)) {
                        logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|默认价格|priceStoreDetails获取:【{}】门店，【{}】商品,数据为NULL！", storeId, goodsNo, priceStoreDetails);
                        continue;
                    }
                    PriceStoreDetail priceStoreDetail = priceStoreDetails.get(0);
                    PriceStoreDetail record = new PriceStoreDetail();

                    int i = updateOnePriceStoreDetail(record, storeId, priceStoreDetail.getGoodsNo(), priceStoreDetail.getPriceTypeCode(), new BigDecimal(0), priceStoreDetail.getPrice(), syncDate, priceStoreDetail.getSyncDate());
                    if (i > 0) {
                        // 推送MQ同步商品价格
                        PriceStoreDetail detail = new PriceStoreDetail();
                        detail.setPrice(new BigDecimal(0));
                        detail.setBusinessId(priceStoreDetail.getBusinessId());
                        detail.setStoreId(priceStoreDetail.getStoreId());
                        detail.setGoodsNo(priceStoreDetail.getGoodsNo());
                        detail.setPriceTypeCode(priceStoreDetail.getPriceTypeCode());
                        syncItemCenterNewPrice(detail);
                    }
                }
            }
        }
        //统一价格
        if (Objects.equals(EditPriceEnum.UNIFY_PRICE.getCode(), editPriceType)) {
            EditPriceParam.Body body = bodyList.get(0);
            BigDecimal newPrice = new BigDecimal(BigDecimalUtils.convertFenByYuan(body.getPrice().toString()));
            List<Long> storeIds = body.getStoreIds();
            String priceTypeCode = body.getPriceTypeCode().get(0);
            String goodsNo = body.getGoodsNo();
            PriceType priceType = priceTypeMap.get(priceTypeCode);
            storeIds.forEach(storeId -> {
                List<PriceStoreDetail> priceStoreDetails = getOnePriceStoreDetail(storeId, goodsNo, priceTypeCode);
                logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|统一价格|priceStoreDetails获取:【{}】门店，【{}】商品,数据为:【{}】", storeId, goodsNo, priceStoreDetails);
                int i = 0;
                PriceStoreDetail record = new PriceStoreDetail();
                if (CollectionUtils.isNotEmpty(priceStoreDetails)) {
                    PriceStoreDetail priceStoreDetail = priceStoreDetails.get(0);
                    i = updateOnePriceStoreDetail(record, priceStoreDetail.getStoreId(), goodsNo, priceTypeCode, newPrice, priceStoreDetail.getPrice(), syncDate, priceStoreDetail.getSyncDate());
                    BeanUtils.copyProperties(priceStoreDetail, record, new String[]{"price"});
                } else {
                    List<PriceStoreDetail> priceStoreDetailList = getOnePriceStoreDetail(storeId, goodsNo, PTypeEnum.LSJ.getCode());
                    logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|统一价格|新增获取零售价:【{}】门店，【{}】商品,数据为:【{}】", storeId, goodsNo, priceStoreDetailList);
                    if (CollectionUtils.isNotEmpty(priceStoreDetailList)) {
                        record = bulidPriceStoreDetail(priceStoreDetailList.get(0), priceType, newPrice);
                        logger.debug("PriceStoreDetailManageServiceImpl|editItemPriceSelf|record:{}", record);
                        i = priceStoreDetailMapper.insertSelective(record);
                    }
                }
                if (i > 0) {
                    // 推送MQ同步商品价格
                    syncItemCenterNewPrice(record);
                }
            });
        }
        //分省价格
        if (Objects.equals(EditPriceEnum.PROVINCIAL_PRICE.getCode(), editPriceType)) {
            //多价格组门店商品都一致，只是设置渠道区域不一致 取门店查询出所有门店LSJ
            List<Long> storeIds = bodyList.get(0).getStoreIds();
            for (EditPriceParam.Body body : bodyList) {
                BigDecimal price = body.getPrice();
                List<String> priceTypeCode = body.getPriceTypeCode();
                String goodsNo = body.getGoodsNo();
                for (int i = storeIds.size() - 1; i >= 0; i--) {
                    Long storeId = storeIds.get(i);
                    List<PriceStoreDetail> list = new ArrayList<>();
                    List<PriceStoreDetail> priceStoreDetails = getOnePriceStoreDetail(storeId, goodsNo, PTypeEnum.LSJ.getCode());
                    if (CollectionUtils.isEmpty(priceStoreDetails)) {
                        logger.warn("PriceStoreDetailManageServiceImpl|editItemPriceSelf|分省价格|获取:【{}】门店，【{}】商品，零售价数据为NULL！在门店集合剔除！", storeId, goodsNo);
                        storeIds.remove(storeId);
                        continue;
                    }
                    PriceStoreDetail lsj = priceStoreDetails.get(0);
                    List<List<String>> partition = Lists.partition(priceTypeCode, 10);
                    for (List<String> codes : partition) {
                        PriceStoreDetailExample priceStoreDetailExample = new PriceStoreDetailExample();
                        priceStoreDetailExample.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoEqualTo(goodsNo).andPriceTypeCodeIn(codes);
                        priceStoreDetailMapper.deleteByExample(priceStoreDetailExample);
                    }
                    for (String code : priceTypeCode) {
                        PriceType priceType = priceTypeMap.get(code);
                        PriceStoreDetail priceStoreDetail = bulidPriceStoreDetail(lsj, priceType, new BigDecimal(BigDecimalUtils.convertFenByYuan(price.toString())));
                        list.add(priceStoreDetail);
                    }
                    logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|分省价格|list：【{}】", list);
                    //批量插入数据控制在一个门店多个渠道 同一个价格
                    if (CollectionUtils.isNotEmpty(list)) {
                        priceStoreDetailMapper.insertBatch(list);
                    }
                }
            }
        }
        //指定药店定价
        if (Objects.equals(EditPriceEnum.SPECIFIED_STORE_PRICE.getCode(), editPriceType)) {
            String priceTypeCode = bodyList.get(0).getPriceTypeCode().get(0);
            PriceType priceType = priceTypeMap.get(priceTypeCode);
            bodyList.forEach(body -> {
                Long storeId = body.getStoreIds().get(0);
                BigDecimal newPrice = new BigDecimal(BigDecimalUtils.convertFenByYuan(body.getPrice().toString()));
                String goodsNo = body.getGoodsNo();
                List<PriceStoreDetail> priceStoreDetails = getOnePriceStoreDetail(storeId, goodsNo, priceTypeCode);
                logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|指定药店定价|priceStoreDetails获取:【{}】门店，【{}】商品,数据为:【{}】", storeId, goodsNo, priceStoreDetails.size());
                int i = 0;
                PriceStoreDetail record = new PriceStoreDetail();
                if (CollectionUtils.isNotEmpty(priceStoreDetails)) {
                    PriceStoreDetail hisPrice = priceStoreDetails.get(0);
                    i = updateOnePriceStoreDetail(record, storeId, hisPrice.getGoodsNo(), hisPrice.getPriceTypeCode(), newPrice, hisPrice.getPrice(), syncDate, hisPrice.getSyncDate());
                    BeanUtils.copyProperties(hisPrice, record, new String[]{"price"});
                } else {
                    List<PriceStoreDetail> priceStoreDetailList = getOnePriceStoreDetail(storeId, goodsNo, PTypeEnum.LSJ.getCode());
                    logger.info("PriceStoreDetailManageServiceImpl|editItemPriceSelf|指定药店定价|新增获取零售价:【{}】门店，【{}】商品,数据为:【{}】", storeId, goodsNo, priceStoreDetailList.size());
                    if (CollectionUtils.isNotEmpty(priceStoreDetailList)) {
                        record = bulidPriceStoreDetail(priceStoreDetailList.get(0), priceType, newPrice);
                        logger.debug("PriceStoreDetailManageServiceImpl|editItemPriceSelf|record:{}", record);
                        i = priceStoreDetailMapper.insertSelective(record);
                    }
                }
                if (i > 0) {
                    // 推送MQ同步商品价格
                    syncItemCenterNewPrice(record);
                }
            });
        }
    }

    @Override
    public ResponseEntity<HashMap<String, Object>> importB2CPrice(MultipartFile file) {
        HashMap<String, Object> map = new HashMap<>();
        try {
            Map<String, String> hearderMap = new LinkedHashMap();
            hearderMap.put("商品编码", "goodsNo");
            hearderMap.put("商品名称", "itemName");
            hearderMap.put("唯一标识码", "priceTypeCode");
            hearderMap.put("调整后", "price");
            hearderMap.put("门店ID", "storeId");
            List<ImportBJHisPriceDTO> importBJHisPriceDTOS = HutoolExcelUtils.excelToBean(file.getInputStream(), ImportBJHisPriceDTO.class, hearderMap, 0);
            Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
            List<ImportBJHisPriceDTO> errorList = new ArrayList<>();
            List<ImportBJHisPriceDTO> errorListOnTypeCode = new ArrayList<>();
            for (ImportBJHisPriceDTO dto : importBJHisPriceDTOS) {
                Long storeId = dto.getStoreId();
                String goodsNo = dto.getGoodsNo();
                int i = 0;
                PriceStoreDetail record = new PriceStoreDetail();
                BigDecimal newPrice = new BigDecimal(BigDecimalUtils.convertFenByYuan(dto.getPrice().toString()));
                PriceType priceType = priceTypeMap.get(dto.getPriceTypeCode());
                if (Objects.isNull(priceType)) {
                    errorListOnTypeCode.add(dto);
                    continue;
                }
                List<PriceStoreDetail> priceStoreDetail = getOnePriceStoreDetail(storeId, goodsNo, dto.getPriceTypeCode());
                if (CollectionUtils.isNotEmpty(priceStoreDetail)) {
                    PriceStoreDetail price = priceStoreDetail.get(0);
                    i = updateOnePriceStoreDetail(record, storeId, price.getGoodsNo(), price.getPriceTypeCode(),
                        newPrice, price.getPrice(), System.currentTimeMillis(), price.getSyncDate());
                    BeanUtils.copyProperties(price, record, new String[]{"price"});
                } else {
                    List<PriceStoreDetail> priceStoreDetailList = getOnePriceStoreDetail(storeId, goodsNo, PTypeEnum.LSJ.getCode());
                    if (CollectionUtils.isEmpty(priceStoreDetailList)) {
                        errorList.add(dto);
                        continue;
                    }
                    if (CollectionUtils.isNotEmpty(priceStoreDetailList)) {
                        record = bulidPriceStoreDetail(priceStoreDetailList.get(0), priceType, newPrice);
                        i = priceStoreDetailMapper.insertSelective(record);
                    }
                }
                if (i > 0) {
                    // 推送MQ同步商品价格
                    syncItemCenterNewPrice(record);
                }
            }
            map.put("唯一标识码不存在失败数据", errorListOnTypeCode);
            map.put("无零售价导入失败数据", errorList);
            logger.info("PriceStoreDetailManageServiceImpl|importB2CPrice|导入价格|失败数据:{}", map);
        } catch (IOException e) {
            logger.error("importBJHisPrice 导入失败", e);
            return new ResponseEntity(e.getMessage(), HttpStatus.OK);
        }
        logger.info("PriceStoreDetailManageServiceImpl|importB2CPrice|导入互医价格-通过模版导入结束");
        return new ResponseEntity(map, HttpStatus.OK);
    }

    @Override
    public ResponseEntity<HashMap<String, Object>> importReplenishBusinessNameAndStoreName(MultipartFile file) {
        HashMap<String, Object> map = new HashMap<>();
        try {
            Map<String, String> hearderMap = new LinkedHashMap();
            hearderMap.put("商品编码", "goodsNo");
            hearderMap.put("商品名称", "itemName");
            hearderMap.put("唯一标识码", "priceTypeCode");
            hearderMap.put("调整后", "price");
            hearderMap.put("门店ID", "storeId");
            List<ImportBJHisPriceDTO> importBJHisPriceDTOS = HutoolExcelUtils.excelToBean(file.getInputStream(), ImportBJHisPriceDTO.class, hearderMap, 0);
            Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
            List<ImportBJHisPriceDTO> errorList = new ArrayList<>();
            List<ImportBJHisPriceDTO> errorListOnTypeCode = new ArrayList<>();
            for (ImportBJHisPriceDTO dto : importBJHisPriceDTOS) {
                Long storeId = dto.getStoreId();
                String goodsNo = dto.getGoodsNo();
                PriceStoreDetail record = new PriceStoreDetail();
                PriceType priceType = priceTypeMap.get(dto.getPriceTypeCode());
                if (Objects.isNull(priceType)) {
                    errorListOnTypeCode.add(dto);
                    continue;
                }
                StoreBriefDTO storeDTO = priceSyncBaseService.getStoreDTOByStoreId(storeId);
                if (Objects.isNull(storeDTO)) {
                    errorList.add(dto);
                    continue;
                }
                record.setStoreName(storeDTO.getStoreName());
                record.setBusinessName(priceSyncBaseService.getBusinessName(storeDTO.getBusinessId()));
                PriceStoreDetailExample example = new PriceStoreDetailExample();
                example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoEqualTo(goodsNo).andPriceTypeCodeIn(Arrays.asList(dto.getPriceTypeCode(),PTypeEnum.LSJ.getCode()));
                priceStoreDetailMapper.updateByExampleSelective(record,example);
            }
            map.put("唯一标识码不存在失败数据", errorListOnTypeCode);
            map.put("未查询到门店导入失败数据", errorList);
            logger.info("PriceStoreDetailManageServiceImpl|importReplenishBusinessNameAndStoreName|补充名称|失败数据:{}", map);
        } catch (IOException e) {
            logger.error("importReplenishBusinessNameAndStoreName 导入失败", e);
            return new ResponseEntity(e.getMessage(), HttpStatus.OK);
        }
        logger.info("PriceStoreDetailManageServiceImpl|importReplenishBusinessNameAndStoreName|补充名称-通过模版导入结束");
        return new ResponseEntity(map, HttpStatus.OK);
    }

    @Override
    public CommonResponse saveOrUpdateItemPrice(List<PriceManagerParam> param) {
        //校验参数
        checkEditPriceParamV2(param);
        if (CollectionUtils.isEmpty(param)) {
            logger.info("PriceStoreDetailManageServiceImpl|saveOrUpdateItemPrice|param筛选后为空不推送！");
            return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK.getCode(), ReturnCodeEnum.HTTP_STATUS_OK.getMessage());
        }
        sendPriceForSelfProducer(param, EditPriceEnum.UNIFY_PRICE.getCode());

        return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK.getCode(), ReturnCodeEnum.HTTP_STATUS_OK.getMessage());
    }

    @Override
    public CommonResponse deleteItemPrice(List<PriceManagerParam> param) {
        //校验参数
        checkEditPriceParamV2(param);
        if (CollectionUtils.isEmpty(param)) {
            logger.info("PriceStoreDetailManageServiceImpl|deleteItemPrice|param筛选后为空不推送！");
            return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK.getCode(), ReturnCodeEnum.HTTP_STATUS_OK.getMessage());
        }
        sendPriceForSelfProducer(param, EditPriceEnum.DEFAULT_PRICE.getCode());

        return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK.getCode(), ReturnCodeEnum.HTTP_STATUS_OK.getMessage());
    }

    /**
     * 异步批量查询价格Task
     */
    class ItemPriceTask implements Callable<List<PriceStoreDetail>> {

        private PriceQueryV2Param priceQueryV2Param;

        public ItemPriceTask(PriceQueryV2Param requestBaseDTO) {
            this.priceQueryV2Param = requestBaseDTO;
        }

        @Override
        public List<PriceStoreDetail> call() {
            logger.info("批量获取商品价格，多线程异步任务获取某个条记录,请求参数 priceQueryV2Param={}", priceQueryV2Param);

            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(priceQueryV2Param.getStoreId())
                .goodsNoList(priceQueryV2Param.getGoodsNos())
                .build();

            String priceTypeCode = priceQueryV2Param.getPriceTypeCode();
            if (PTypeEnum.GJHIS.getCode().equals(priceTypeCode) || StringUtils.isBlank(priceTypeCode)) {
                query.setPriceTypeCodeList(Arrays.asList(PTypeEnum.GJHIS.getCode(), PTypeEnum.LSJ.getCode()));
            } else {
                query.setPriceTypeCodeList(Arrays.asList(priceTypeCode, PTypeEnum.LSJ.getCode(),
                    isBlank(priceQueryV2Param.getChannelPriceTypeCode()) ? GJHIS.getCode() : priceQueryV2Param.getChannelPriceTypeCode()));
            }

            return priceStoreDetailReadService.query(query);
        }
    }

    class ItemPriceTask2 implements Callable<List<PriceStoreDetail>> {

        private PriceQueryV2Param priceQueryV2Param;

        public ItemPriceTask2(PriceQueryV2Param requestBaseDTO) {
            this.priceQueryV2Param = requestBaseDTO;
        }

        @Override
        public List<PriceStoreDetail> call() {
            logger.info("批量获取商品价格，多线程异步任务获取某个条记录,请求参数 priceQueryV2Param={}", priceQueryV2Param);
            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(priceQueryV2Param.getStoreId())
                .goodsNo(priceQueryV2Param.getGoodsNo())
                .priceTypeCodeList(priceQueryV2Param.getPriceTypeCodes())
                .build();
            return priceStoreDetailReadService.query(query);
        }
    }

    private List<PriceStoreDetail> getPriceDetailOnTypeCodes(List<Long> storeIds, String goodsNo, List<String> priceTypeCodes) {
        List<Future<List<PriceStoreDetail>>> collect = storeIds.stream().map(storeId -> {
            PriceQueryV2Param priceQueryV2Param = new PriceQueryV2Param();
            priceQueryV2Param.setStoreId(storeId);
            priceQueryV2Param.setGoodsNo(goodsNo);
            priceQueryV2Param.setPriceTypeCodes(priceTypeCodes);
            ItemPriceTask2 itemPriceTask = new ItemPriceTask2(priceQueryV2Param);
            Future<List<PriceStoreDetail>> future = threadPoolExecutor2.submit(itemPriceTask);
            return future;
        }).collect(Collectors.toList());
        List<PriceStoreDetail> details = new ArrayList<>();
        collect.forEach(a -> {
            try {
                details.addAll(a.get());
            } catch (InterruptedException e) {
                logger.error("PriceStoreDetailManageServiceImpl|getPriceDetailOnTypeCodes|error：【{}】", e);
            } catch (ExecutionException e) {
                logger.error("PriceStoreDetailManageServiceImpl|getPriceDetailOnTypeCodes|error：【{}】", e);
            }
        });
        return details;
    }

    private PriceStoreDetail bulidPriceStoreDetail(PriceStoreDetail priceStoreDetail1, PriceType priceType, BigDecimal price) {
        PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
        BeanUtils.copyProperties(priceStoreDetail1, priceStoreDetail, new String[]{"id", "gmtCreate", "gmtUpdate", "syncDate"});
        priceStoreDetail.setPrice(price);
        priceStoreDetail.setPriceTypeId(priceType.getId());
        priceStoreDetail.setPriceTypeName(priceType.getName());
        priceStoreDetail.setPriceTypeCode(priceType.getCode());
        priceStoreDetail.setSyncDate(System.currentTimeMillis());
        if (StringUtils.isBlank(priceStoreDetail.getBusinessName()) || StringUtils.isBlank(priceStoreDetail.getStoreName())) {
            syncBusinessNameAndStoreNameByParam(priceStoreDetail, Arrays.asList(PTypeEnum.LSJ.getCode()));
        }
        return priceStoreDetail;
    }

    private void checkEditPriceParam(EditPriceParam param) {
        logger.info("PriceStoreDetailManageHisServiceImpl|checkEditPriceParam|param【{}】", param);
        Integer editPriceType = param.getEditPiiceType();
        if (Objects.isNull(editPriceType) && CollectionUtils.isEmpty(param.getBodyList())) {
            throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR.getMessage());
        }
        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (Objects.isNull(priceTypeMap)) {
            throw new BusinessErrorException("价格列表异常");
        }
        List<EditPriceParam.Body> paramBodyList = param.getBodyList();
        for (int i = paramBodyList.size() - 1; i >= 0; i--) {
            EditPriceParam.Body body = paramBodyList.get(i);
            if (StringUtils.isEmpty(body.getGoodsNo())) {
                throw new BusinessErrorException("商品不可为空");
            }
            boolean minusCheck = Objects.equals(-1, body.getPrice().compareTo(BigDecimal.ZERO));
            boolean zeroCheck = Objects.equals(0, body.getPrice().compareTo(BigDecimal.ZERO));
            logger.info("PriceStoreDetailManageHisServiceImpl|checkEditPriceParam|负数校验结果:{}，零校验结果：{}", minusCheck, zeroCheck);
            boolean existed = (!Objects.equals(EditPriceEnum.SPECIFIED_STORE_PRICE.getCode(), editPriceType) && (minusCheck || zeroCheck));
            if (existed) {
                throw new BusinessErrorException("商品金额不可为负数或空");
            }
            //指定药店定价价格校验
            boolean existed2 = (Objects.equals(EditPriceEnum.SPECIFIED_STORE_PRICE.getCode(), editPriceType) && (minusCheck || zeroCheck));
            if (existed2) {
                paramBodyList.remove(body);
            }
            if (CollectionUtils.isEmpty(body.getStoreIds())) {
                throw new BusinessErrorException("门店ID不可以为空");
            }
            List<String> priceTypeCodes = body.getPriceTypeCode();
            if (CollectionUtils.isEmpty(priceTypeCodes)) {
                throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR.getMessage());
            }
            priceTypeCodes.forEach(a -> {
                if (Objects.isNull(priceTypeMap.get(a))) {
                    throw new BusinessErrorException(ReturnCodeEnum.PRICE_TYPE_NOT_EXIST.getMessage());
                }
            });
        }
    }

    private void checkPriceByParam(List<PriceQueryV2Param> list) {
        //获取可用的价格列表
//        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
//        if (Objects.isNull(priceTypeMap)) {
//            throw new BusinessErrorException("价格列表异常");
//        }
        list.forEach(a -> {
            if (StringUtils.isEmpty(a.getGoodsNo())) {
                throw new BusinessErrorException("商品不可为空");
            }
            if (Objects.isNull(a.getStoreId())) {
                throw new BusinessErrorException("门店ID不可以为空");
            }
//            if (Objects.isNull(priceTypeMap.get(a.getPriceTypeCode()))) {
//                throw new BusinessErrorException(ReturnCodeEnum.PRICE_TYPE_NOT_EXIST.getMessage());
//            }
        });
    }

    /**
     * 同步商品中心
     *
     * @param priceStoreDetail
     */
    private void syncItemCenterNewPrice(PriceStoreDetail priceStoreDetail) {
        logger.warn("PriceStoreDetailManageHisServiceImpl|syncItemCenterNewPrice|价格完成并同步商品中心 门店:【{}】，商品:【{}】，价格类型:【{}】,价格：【{}】",
            priceStoreDetail.getStoreId(), priceStoreDetail.getGoodsNo(), priceStoreDetail.getPriceTypeCode(), priceStoreDetail.getPrice());
        PricePushYJSVo pricePushYJSVo = new PricePushYJSVo();
        pricePushYJSVo.setPrice(priceStoreDetail.getPrice());
        pricePushYJSVo.setBusinessId(priceStoreDetail.getBusinessId());
        pricePushYJSVo.setStoreId(priceStoreDetail.getStoreId());
        pricePushYJSVo.setGoodsNo(priceStoreDetail.getGoodsNo());
        pricePushYJSVo.setPriceType(priceStoreDetail.getPriceTypeCode());

        pricePushYJSPriceTypeToHLProducer.sendMq(pricePushYJSVo);
        logger.warn("PriceStoreDetailManageHisServiceImpl|syncItemCenterNewPrice|价格完成并同步商品中心 商品:【{}】", priceStoreDetail.getGoodsNo());
    }

    private List<PriceStoreDetail> getOnePriceStoreDetail(Long storeId, String goodsNo, String priceTypeCode) {

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .goodsNo(goodsNo)
            .priceTypeCode(priceTypeCode)
            .build();
        return priceStoreDetailReadService.query(query);
    }

    private int updateOnePriceStoreDetail(PriceStoreDetail record, Long storeId, String goodsNo, String priceTypeCode,
                                          BigDecimal newPrice, BigDecimal oldPrice, Long syncDateMQ, Long syncDate) {
        if (syncDate != null && syncDateMQ < syncDate) {
            logger.info("PriceStoreDetailManageServiceImpl|updateOnePriceStoreDetail|MQ时间戳小于库中时间戳不需要更新！|门店:【{}】，商品:【{}】,渠道：{}", storeId, goodsNo, priceTypeCode);
            return 0;
        }
        if (oldPrice.compareTo(newPrice) == 0) {
            logger.info("PriceStoreDetailManageServiceImpl|updateOnePriceStoreDetail|当前价格和修改价格一致不需要更新！|门店:【{}】，商品:【{}】,渠道：{}", storeId, goodsNo, priceTypeCode);
            return 0;
        }
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoEqualTo(goodsNo).andPriceTypeCodeEqualTo(priceTypeCode);
        record.setPrice(newPrice);
        record.setSyncDate(System.currentTimeMillis());
        return priceStoreDetailMapper.updateByExampleSelective(record, example);
    }

    private void syncBusinessNameAndStoreNameByParam(PriceStoreDetail dto,List<String> priceTypeCodes) {
        Long storeId = dto.getStoreId();
        StoreBriefDTO storeDTO = priceSyncBaseService.getStoreDTOByStoreId(storeId);
        if (Objects.isNull(storeDTO)) {
            return;
        }
        String storeName = storeDTO.getStoreName();
        String businessName = priceSyncBaseService.getBusinessName(storeDTO.getBusinessId());
        dto.setStoreName(storeName);
        dto.setBusinessName(businessName);
        //同步连锁门店名称
        threadPoolExecutor2.execute(()->{
            PriceStoreDetail record = new PriceStoreDetail();
            record.setStoreName(storeName);
            record.setBusinessName(businessName);
            PriceStoreDetailExample example = new PriceStoreDetailExample();
            example.createCriteria().andStoreIdEqualTo(storeId).andGoodsNoEqualTo(dto.getGoodsNo()).andPriceTypeCodeIn(priceTypeCodes);
            priceStoreDetailMapper.updateByExampleSelective(record, example);
        });
    }

    private void checkEditPriceParamV2(List<PriceManagerParam> param) {
        logger.info("PriceStoreDetailManageHisServiceImpl|checkEditPriceParamV2|param【{}】", param);
        if (CollectionUtils.isEmpty(param)) {
            throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR.getMessage());
        }
        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (Objects.isNull(priceTypeMap)) {
            throw new BusinessErrorException("价格列表异常");
        }
        for (int i = param.size() - 1; i >= 0; i--) {
            PriceManagerParam body = param.get(i);
            if (StringUtils.isEmpty(body.getGoodsNo())) {
                throw new BusinessErrorException("商品不可为空");
            }
            boolean minusCheck = Objects.equals(-1, body.getPrice().compareTo(BigDecimal.ZERO));
            boolean zeroCheck = Objects.equals(0, body.getPrice().compareTo(BigDecimal.ZERO));
            logger.info("PriceStoreDetailManageHisServiceImpl|checkEditPriceParamV2|负数校验结果:{}，零校验结果：{}", minusCheck, zeroCheck);
            if (minusCheck || zeroCheck) {
                param.remove(body);
//                throw new BusinessErrorException("商品金额不可为负数或空");
            }
            if (Objects.isNull(body.getStoreId())) {
                throw new BusinessErrorException("门店ID不可以为空");
            }
            String priceTypeCodes = body.getPriceTypeCode();
            if (StringUtils.isBlank(priceTypeCodes)) {
                throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR.getMessage());
            }
            if (Objects.isNull(priceTypeMap.get(priceTypeCodes))) {
                throw new BusinessErrorException(ReturnCodeEnum.PRICE_TYPE_NOT_EXIST.getMessage());
            }
        }
    }

    /**
     * 消息发送
     * @param param
     * @param editPriceType
     */
    private void sendPriceForSelfProducer(List<PriceManagerParam> param, Integer editPriceType) {
        Map<String, List<PriceManagerParam>> listMap = new LinkedHashMap<>();
        if (Objects.equals(EditPriceEnum.DEFAULT_PRICE.getCode(), editPriceType)) {
            listMap = param.stream().collect(Collectors.groupingBy(k -> k.getGoodsNo() + k.getPriceTypeCode()));
        } else if(Objects.equals(EditPriceEnum.UNIFY_PRICE.getCode(), editPriceType)){
            listMap = param.stream().collect(Collectors.groupingBy(k -> k.getGoodsNo() + k.getPrice() + k.getPriceTypeCode()));
        }

        listMap.forEach((k, v) -> {
            if (CollectionUtils.isNotEmpty(v)) {
                HisPriceForSelfMqVO mqVO = new HisPriceForSelfMqVO();
                mqVO.setEditPiiceType(editPriceType);
                List<EditPriceParam.Body> bodyList = new ArrayList<>();
                EditPriceParam.Body body = new EditPriceParam.Body();
                bodyList.add(body);
                body.setStoreIds(v.stream().map(a -> a.getStoreId()).collect(Collectors.toList()));
                body.setGoodsNo(v.get(0).getGoodsNo());
                body.setPrice(v.get(0).getPrice());
                body.setPriceTypeCode(Arrays.asList(v.get(0).getPriceTypeCode()));
                mqVO.setBodyList(bodyList);
                hisPriceForSelfProducer.sendMq(mqVO);
            }
        });
    }
}
