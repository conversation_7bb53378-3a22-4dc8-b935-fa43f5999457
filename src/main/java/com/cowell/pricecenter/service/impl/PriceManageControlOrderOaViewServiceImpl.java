package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.PlatformChannelEnum;
import com.cowell.pricecenter.enums.PriceManageTypeEnum;
import com.cowell.pricecenter.enums.PriceNoticeStatusEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.mapper.PriceControlNoticeMapper;
import com.cowell.pricecenter.mapper.PriceManageControlOrderDetailMapper;
import com.cowell.pricecenter.mapper.PriceManageControlOrderMapper;
import com.cowell.pricecenter.mapper.PriceManageControlOrgDetailMapper;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrderDetailExtMapper;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.request.ControlOrderDetailListV2Param;
import com.cowell.pricecenter.service.dto.response.PriceTypeDTO;
import com.cowell.pricecenter.service.dto.response.amis.ColumnVO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.controlOrder.ControlExtendVO;
import com.cowell.pricecenter.service.dto.response.controlOrder.ControlOrderDetailListVO;
import com.cowell.pricecenter.service.dto.response.controlOrder.ControlOrderSimpleDTO;
import com.cowell.pricecenter.service.dto.response.controlOrder.PriceManageControlOrderVO;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by schuangxigang on 2022/4/21 19:10.
 */
@Service
public class PriceManageControlOrderOaViewServiceImpl extends BasePermissionService implements PriceManageControlOrderOaViewService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PriceManageControlOrderMapper priceManageControlOrderMapper;

    @Autowired
    private PriceManageControlOrgDetailMapper priceManageControlOrgDetailMapper;

    @Autowired
    private PriceManageControlOrderDetailMapper priceManageControlOrderDetailMapper;

    @Autowired
    private PriceManageControlOrderDetailExtMapper priceManageControlOrderDetailExtMapper;

    @Autowired
    private PriceControlNoticeMapper priceControlNoticeMapper;

    @Resource
    private IBasePriceOrderService basePriceOrderService;

    @Autowired
    private IPriceTypeService priceTypeService;
    @Autowired
    private PriceChannelService priceChannelService;

    @Autowired
    private PriceQueryService priceQueryService;

    @Autowired
    private IPermissionExtService permissionExtService;

    @Override
    public PriceManageControlOrderVO getControlOrderBaseInfo(String orderNo) {
        PriceManageControlOrder controlOrder = getControlOrderByOrderNo(orderNo);

        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        orgDetailExample.createCriteria().andControlOrderCodeEqualTo(controlOrder.getControlOrderId());
        List<PriceManageControlOrgDetail> orgDetailList = priceManageControlOrgDetailMapper.selectByExample(orgDetailExample);
        if(CollectionUtils.isEmpty(orgDetailList)){
            throw new BusinessErrorException(ReturnCodeEnum.ERROR_ORDER_ORG_NULL);
        }
        List<Long> orgIdList = orgDetailList.stream().map(PriceManageControlOrgDetail::getOrgId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<Long, OrgLevelVO> orgMap = permissionExtService.listPermissionByOrgIds(orgIdList);

        PriceManageControlOrderVO orderVO = new PriceManageControlOrderVO();
        orderVO.setControlOrderName(controlOrder.getControlOrderName());
        orderVO.setControlNoExecReason(controlOrder.getControlNoExecReason());
        orderVO.setOrgIdList(orgIdList);
        orderVO.setOrgNameList(orgIdList.stream().map(v -> {
            OrgLevelVO org = orgMap.get(v);
            if (null != org) {
                return org.getOrgName();
            }
            return null;
        }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList()));
        orderVO.setId(controlOrder.getId());
        orderVO.setControlOrderId(controlOrder.getControlOrderId());
        orderVO.setAuditStatus(controlOrder.getAuditStatus());
        orderVO.setControlReason(controlOrder.getControlReason());
        orderVO.setChannelList(channelToList(controlOrder.getChannel()));
        orderVO.setChannelNameList(getChannelNameListByCodeList(controlOrder.getChannel()));
        orderVO.setPriceTypeList(priceTypeToList(controlOrder.getPriceType()));
        orderVO.setPriceTypeNameList(getPriceTypeNameListByCodeList(controlOrder.getPriceType()));
        orderVO.setControlType(controlOrder.getControlType());
        orderVO.setControlTypName(PriceManageTypeEnum.getDescByCode(controlOrder.getControlType()));
        orderVO.setGmtCreate(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(controlOrder.getGmtCreate()));
        if (controlOrder.getScheduledTime() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            orderVO.setScheduledTime(controlOrder.getScheduledTime().format(formatter));
        }
        return orderVO;
    }

    @Override
    public ControlOrderSimpleDTO getControlOrderSimpleInfo(String orderNo) {
        PriceManageControlOrder controlOrder = getControlOrderByOrderNo(orderNo);
        ControlOrderSimpleDTO simpleDTO = new ControlOrderSimpleDTO();
        BeanUtils.copyProperties(controlOrder, simpleDTO);
        return simpleDTO;
    }

    @Override
    public PageResult<Map<String, Object>> pageControlOrderDetailList(String orderNo, Integer page, Integer pageSize) {

        PriceManageControlOrder controlOrder = getControlOrderByOrderNo(orderNo);

        //校验
        basePriceOrderService.checkControlOrderMustHasProperties(controlOrder);

        ControlOrderDetailListV2Param param = new ControlOrderDetailListV2Param();

        param.setControlOrderCode(controlOrder.getControlOrderId());
        param.setOffset((long) (page - 1) * pageSize);
        param.setSize(pageSize);
        long count = priceManageControlOrderDetailExtMapper.selectRemoveChannelAttOrderDetailV2ListCount(param);
        List<Map<String, Object>> controlOrderDetailListResults = Collections.emptyList();
        if (count > 0) {
            List<String> goodsNoList = priceManageControlOrderDetailExtMapper.selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType(param);
            if(CollectionUtils.isNotEmpty(goodsNoList)){
                List<PriceManageControlOrderDetail> controlOrderDetailList = priceManageControlOrderDetailExtMapper.selectOrderDetailListGroupByGoodsAndPriceType(controlOrder.getControlOrderId(), goodsNoList);
                controlOrderDetailListResults = getControlOrderDetailListResults(controlOrder, controlOrderDetailList, false);
            }
        }

        List<ColumnVO> columns = getControlOrderDetailColumnVO(controlOrder.getPriceType());

        return new PageResult<>((long)controlOrderDetailListResults.size(), columns, controlOrderDetailListResults);
    }

    @Override
    public PageResult<ControlOrderDetailListVO> pageNonExecControlOrderDetailList(String orderNo, Integer page, Integer pageSize) {
        return pageGoodsList(orderNo, page, pageSize, true);
    }

    @Override
    public PageResult<ControlOrderDetailListVO> pageGoodsList(String orderNo, Integer page, Integer pageSize) {
        return pageGoodsList(orderNo, page, pageSize, false);
    }

    @Override
    public PageResult<ControlOrderDetailListVO> pageGoodsList(String orderNo, Integer page, Integer pageSize, boolean isNonExecControl) {
        PriceManageControlOrder controlOrder = getControlOrderByOrderNo(orderNo, isNonExecControl);
        List<ControlOrderDetailListVO> pageList;
        long count;
        if (isNonExecControl) {
            ControlExtendVO controlExtendVO = getControlExtendVO(controlOrder);
            // 不执行管控申请查管控通知表
            PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
            PriceControlNoticeExample.Criteria criteria = noticeExample.createCriteria();
            criteria.andControlOrderIdEqualTo(controlExtendVO.getControlOrderCode())
                .andStatusNotIn(Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.DELETE.getCode()));
            if (StringUtils.isNotBlank(controlExtendVO.getNoticeIds())) {
                criteria.andIdIn(Arrays.stream(controlExtendVO.getNoticeIds().split(",")).map(Long::parseLong).collect(Collectors.toList()));
            }
            count = priceControlNoticeMapper.countByExample(noticeExample);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            noticeExample.setOffset((page-1)*pageSize);
            noticeExample.setLimit(pageSize);
            List<PriceControlNotice> notices = priceControlNoticeMapper.selectByExample(noticeExample);
            pageList = notices.stream().map(v -> {
                ControlOrderDetailListVO orderDetailListVO = new ControlOrderDetailListVO();
                BeanUtils.copyProperties(v, orderDetailListVO);
                orderDetailListVO.setControlOrderCode(v.getControlOrderId());
                orderDetailListVO.setSpecifications(v.getJhiSpecification());
                orderDetailListVO.setHabitat(v.getProdarea());
                return orderDetailListVO;
            }).collect(Collectors.toList());
        } else {
            PriceManageControlOrderDetailExample detailExample = new PriceManageControlOrderDetailExample();
            detailExample.createCriteria().andControlOrderCodeEqualTo(controlOrder.getControlOrderId());
            count = priceManageControlOrderDetailMapper.countByExample(detailExample);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            detailExample.setOffset((long)(page-1)*pageSize);
            detailExample.setLimit(pageSize);
            List<PriceManageControlOrderDetail> details = priceManageControlOrderDetailMapper.selectByExample(detailExample);
            pageList = details.stream().map(v -> {
                ControlOrderDetailListVO orderDetailListVO = new ControlOrderDetailListVO();
                BeanUtils.copyProperties(v, orderDetailListVO);
                orderDetailListVO.setSpecifications(v.getJhiSpecification());
                orderDetailListVO.setHabitat(v.getProdarea());
                return orderDetailListVO;
            }).collect(Collectors.toList());
        }
        // 查询渠道
        List<Integer> channelIds = pageList.stream().map(ControlOrderDetailListVO::getChannelId).distinct().collect(Collectors.toList());
        Map<Integer, PriceChannel> priceChannelMap = priceChannelService.getChannelMap(channelIds);
        pageList = pageList.stream().peek(v -> {
            v.setEffectTime(controlOrder.getEffectTime() == null ? null : Date.from(controlOrder.getEffectTime().atZone(ZoneId.systemDefault()).toInstant()));
            v.setChannelName(Optional.ofNullable(priceChannelMap.get(v.getChannelId())).map(PriceChannel::getChannelName).orElse(""));
        }).collect(Collectors.toList());
        return new PageResult<>(count, pageList);
    }

    private PriceManageControlOrder getControlOrderByOrderNo(String orderNo) {
        return getControlOrderByOrderNo(orderNo, false);
    }

    /**
     * 根据单号获取管控单
     *
     * @param orderNo           单号
     * @param isNonExecControl  是否不执行管控单
     * @return
     */
    private PriceManageControlOrder getControlOrderByOrderNo(String orderNo, boolean isNonExecControl) {
        if (StringUtils.isBlank(orderNo)) {
            throw new BusinessErrorException(ReturnCodeEnum.PARAM_ERROR);
        }

        PriceManageControlOrderExample example = new PriceManageControlOrderExample();
        example.createCriteria().andControlOrderIdEqualTo(orderNo);
        List<PriceManageControlOrder> controlOrders = priceManageControlOrderMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(controlOrders)) {
            if (isNonExecControl) {
                throw new BusinessErrorException("管控单号"+orderNo+"不存在");
            } else {
                throw new BusinessErrorException("不执行管控单号"+orderNo+"不存在");
            }
        }
        return controlOrders.get(0);
    }

    /**
     * 根据不执行管控单获取管控单
     *
     * @param controlOrder
     * @return
     */
    private ControlExtendVO getControlExtendVO(PriceManageControlOrder controlOrder) {
        try {
            Optional<ControlExtendVO> controlExtendVO = ControlExtendVO.getInstance(controlOrder.getExtend());
            if (Objects.isNull(controlExtendVO) || !controlExtendVO.isPresent() || StringUtils.isBlank(controlExtendVO.get().getControlOrderCode())) {
                throw new BusinessErrorException(ReturnCodeEnum.ERROR_UNCONTROL_NO_MAPPING);
            }
            String orderNo = controlExtendVO.get().getControlOrderCode();
            PriceManageControlOrderExample example = new PriceManageControlOrderExample();
            example.createCriteria().andControlOrderIdEqualTo(orderNo);
            long count = priceManageControlOrderMapper.countByExample(example);
            if (count == 0) {
                throw new BusinessErrorException("管控单号"+orderNo+"不存在");
            }
            return controlExtendVO.get();
        } catch (Exception e) {
            throw new BusinessErrorException("不执行管控单号"+controlOrder.getControlOrderId()+"获取对应的管控单异常");
        }
    }

    private List<String> getChannelNameListByCodeList(String codes) {
        if(org.apache.commons.lang3.StringUtils.isBlank(codes)){
            return com.google.common.collect.Lists.newArrayList();
        }
        List<String> names = new ArrayList<>();
        List<PriceChannel> priceChannelList = priceChannelService.getAllPriceChannelList();
        for (PriceChannel priceChannel : priceChannelList) {
            if (codes.contains(priceChannel.getChannelId()+"")) {
                names.add(priceChannel.getChannelName());
            }
        }
        return names;
    }

    private List<String> getPriceTypeNameListByCodeList(String codes) {
        if(org.apache.commons.lang3.StringUtils.isBlank(codes)){
            return com.google.common.collect.Lists.newArrayList();
        }
        List<String> names = new ArrayList<>();
        List<PriceTypeDTO> priceTypeDTOList = priceTypeService.getAllPriceType();
        for (PriceTypeDTO priceTypeDTO : priceTypeDTOList) {
            if (codes.contains(priceTypeDTO.getCode())) {
                names.add(priceTypeDTO.getName());
            }
        }
        return names;
    }
}
