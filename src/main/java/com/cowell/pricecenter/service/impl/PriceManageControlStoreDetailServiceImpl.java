package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Lists;
import com.cowell.framework.utils.IdUtils;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.PriceManageControlStoreDetailMapper;
import com.cowell.pricecenter.mq.producer.ControlOrderStoreDetailEffectProducer;
import com.cowell.pricecenter.mq.producer.PricePushTaskProducer;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.ControlOrderQueryDTO;
import com.cowell.pricecenter.service.dto.ControlOrderStoreDetailQueryDTO;
import com.cowell.pricecenter.service.dto.ControlStoreGoodsDetailResultDTO;
import com.cowell.pricecenter.service.dto.PriceManageControlStoreDetailDTO;
import com.cowell.pricecenter.service.vo.PricePushProduceVo;
import com.cowell.pricecenter.utils.StreamUtils;
import com.cowell.pricecenter.web.rest.util.GetApplicationContextUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.cursor.Cursor;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.cowell.pricecenter.enums.PriceSourceEnum.CONTROL_ORDER_CODE;
import static com.cowell.pricecenter.web.rest.util.DateUtils.getToday_YYYYMMDD;

/**
 * <AUTHOR>
 * @date 2022/3/22 14:49
 */

@Service
public class PriceManageControlStoreDetailServiceImpl implements PriceManageControlStoreDetailService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceManageControlStoreDetailServiceImpl.class);

    private static final Integer DEFAULT_PAGE_SIZE = 100;

    @Autowired
    private PriceManageControlStoreDetailMapper storeDetailMapper;

    @Autowired
    private IPriceStoreDetailService priceStoreDetailService;

    @Autowired
    private PriceManageControlStoreDetailService controlStoreDetailService;

    @Autowired
    private PriceManageControlStoreDetailMapper priceManageControlStoreDetailMapper;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    @Autowired
    private PriceControlNoticeService priceControlNoticeService;

    @Autowired
    private IPriceManageControlOrderService priceManageControlOrderService;

    @Autowired
    private PriceManageControlOrderReadService priceManageControlOrderReadService;

    @Autowired
    private ControlOrderStoreDetailEffectProducer controlOrderStoreDetailEffectProducer;

    @Autowired
    private PricePushHistoryService pricePushHistoryService;

    @Autowired
    private IPricePushService pricePushService;

    @Autowired
    private PricePushTaskProducer pricePushTaskProducer;

    @Autowired
    private RedissonClient redissonClient;

    @Value("${control.clear.goods.price.switch:false}")
    private Boolean controlClearGoodsPriceSwitch;
    
    @Value("${price.control.notice.save:false}")
    private Boolean priceControlNoticeSave;

    @Override
    public long countByExample(PriceManageControlStoreDetailExample example) {
        return priceManageControlStoreDetailMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PriceManageControlStoreDetailExample example) {
        return priceManageControlStoreDetailMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return priceManageControlStoreDetailMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PriceManageControlStoreDetail record) {
        return priceManageControlStoreDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceManageControlStoreDetail record) {
        return priceManageControlStoreDetailMapper.insertSelective(record);
    }

    @Override
    public List<PriceManageControlStoreDetail> selectByExample(PriceManageControlStoreDetailExample example) {
        return priceManageControlStoreDetailMapper.selectByExample(example);
    }

    @Override
    public PriceManageControlStoreDetail selectByPrimaryKey(Long id) {
        return priceManageControlStoreDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(PriceManageControlStoreDetail record, PriceManageControlStoreDetailExample example) {
        return priceManageControlStoreDetailMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int updateByExample(PriceManageControlStoreDetail record, PriceManageControlStoreDetailExample example) {
        return priceManageControlStoreDetailMapper.updateByExample(record, example);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceManageControlStoreDetail record) {
        return priceManageControlStoreDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceManageControlStoreDetail record) {
        return priceManageControlStoreDetailMapper.updateByPrimaryKey(record);
    }

    @Override
    public int insertOrUpdate(PriceManageControlStoreDetail record) {
        return priceManageControlStoreDetailMapper.insertOrUpdate(record);
    }

    @Override
    public int insertOrUpdateSelective(PriceManageControlStoreDetail record) {
        return priceManageControlStoreDetailMapper.insertOrUpdateSelective(record);
    }

    @Override
    public int updateBatch(List<PriceManageControlStoreDetail> list) {
        return priceManageControlStoreDetailMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<PriceManageControlStoreDetail> list) {
        return priceManageControlStoreDetailMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<PriceManageControlStoreDetail> list) {
        return priceManageControlStoreDetailMapper.batchInsert(list);
    }

    @Override
    public List<PriceManageControlStoreDetail> getControlStoreDetailByParam(ControlOrderStoreDetailQueryDTO detailQueryDTO) {
        if (Objects.isNull(detailQueryDTO)) {
            LOGGER.info("PriceManageControlStoreDetailServiceImpl|getControlStoreDetailByParam|查询门店管控单明细参数为空!");
            return Lists.newArrayList();
        }

        PriceManageControlStoreDetailExample detailExample = new PriceManageControlStoreDetailExample();
        PriceManageControlStoreDetailExample.Criteria criteria = detailExample.createCriteria();

        if (Objects.nonNull(detailQueryDTO.getStoreId())) {
            if (CollectionUtils.isNotEmpty(detailQueryDTO.getStoreIdList())) {
                detailQueryDTO.getStoreIdList().add(detailQueryDTO.getStoreId());
            } else {
                detailQueryDTO.setStoreIdList(Lists.newArrayList(detailQueryDTO.getStoreId()));
            }
        }

        if (CollectionUtils.isNotEmpty(detailQueryDTO.getStoreIdList())) {
            criteria.andStoreIdIn(detailQueryDTO.getStoreIdList());
        }

        if (CollectionUtils.isNotEmpty(detailQueryDTO.getChannelIdList())) {
            criteria.andChannelIdIn(detailQueryDTO.getChannelIdList());
        }

        if (CollectionUtils.isNotEmpty(detailQueryDTO.getPriceTypeCodeList())) {
            criteria.andPriceTypeCodeIn(detailQueryDTO.getPriceTypeCodeList());
        }

        if (CollectionUtils.isNotEmpty(detailQueryDTO.getGoodsNoList())) {
            criteria.andGoodsNoIn(detailQueryDTO.getGoodsNoList());
        }

        if (StringUtils.isNotBlank(detailQueryDTO.getGoodsNo())) {
            criteria.andGoodsNoEqualTo(detailQueryDTO.getGoodsNo());
        }

        if (Objects.nonNull(detailQueryDTO.getChannelId())) {
            criteria.andChannelIdEqualTo(detailQueryDTO.getChannelId());
        }

        if (StringUtils.isNotBlank(detailQueryDTO.getPriceTypeCode())) {
            criteria.andPriceTypeCodeEqualTo(detailQueryDTO.getPriceTypeCode());
        }

        if (Objects.nonNull(detailQueryDTO.getPageSize()) && Objects.nonNull(detailQueryDTO.getPageNum())) {
            detailExample.setLimit(detailQueryDTO.getPageSize());
            detailExample.setOffset(detailQueryDTO.getPageNum() * detailQueryDTO.getPageSize());
        }

        if (Objects.nonNull(detailQueryDTO.getStartLocalDate()) && Objects.nonNull(detailQueryDTO.getEndLocalDate())) {
            criteria.andDxsDateBetween(detailQueryDTO.getStartLocalDate(), detailQueryDTO.getEndLocalDate());
        }

        if (Objects.nonNull(detailQueryDTO.getEffectStatus())) {
            criteria.andEffectStatusEqualTo(detailQueryDTO.getEffectStatus());
        }
        return priceManageControlStoreDetailMapper.selectByExample(detailExample);
    }

    @Override
    public long countControlStoreDetailByParam(ControlOrderStoreDetailQueryDTO queryDTO) {
        LOGGER.info("PriceManageControlStoreDetailServiceImpl|countControlStoreDetailByParam|查询门店管控商品明细|queryDTO={}", queryDTO);
        PriceManageControlStoreDetailExample storeDetailExample = new PriceManageControlStoreDetailExample();
        PriceManageControlStoreDetailExample.Criteria criteria = storeDetailExample.createCriteria();
        if (CollectionUtils.isNotEmpty(queryDTO.getControlOrderCodeList())) {
            criteria.andControlOrderCodeIn(queryDTO.getControlOrderCodeList());
        }
        return priceManageControlStoreDetailMapper.countByExample(storeDetailExample);
    }

    @Override
    @NewSpan
    @Transactional(rollbackFor = {Exception.class})
    public void savePriceControlStoreDetail(ControlStoreGoodsDetailResultDTO resultDTO) {
        LOGGER.info("PriceManageControlStoreDetailServiceImpl|savePriceControlStoreDetail|执行计算保存管控通知明细|controlOrderCode={}," +
            "uuId={},insertList.size={}", resultDTO.getControlOrderCode(), resultDTO.getUuid(), resultDTO.getInsertList().size());

        if (CollectionUtils.isEmpty(resultDTO.getInsertList())) {
            return;
        }

        List<PriceManageControlStoreDetail> existStoreDetailList = Lists.newArrayList();
        resultDTO.getInsertList().forEach(insert -> {
            ControlOrderStoreDetailQueryDTO storeDetailQueryDTO = new ControlOrderStoreDetailQueryDTO();
            storeDetailQueryDTO.setStoreIdList(Lists.newArrayList(insert.getStoreId()));
            storeDetailQueryDTO.setGoodsNoList(Lists.newArrayList(insert.getGoodsNo()));
            if (Objects.nonNull(insert.getChannelId())) {
                List<Integer> channelIdList= Lists.newArrayList(1);
                channelIdList.add(insert.getChannelId());
                storeDetailQueryDTO.setChannelIdList(channelIdList);
            }
            storeDetailQueryDTO.setPriceTypeCodeList(Lists.newArrayList(insert.getPriceTypeCode()));
            storeDetailQueryDTO.setEffectStatus(EffectStatusEnum.NO_EFFECT.getCode());
            existStoreDetailList.addAll(controlStoreDetailService.getControlStoreDetailByParam(storeDetailQueryDTO));
        });

        Map<String, List<PriceManageControlStoreDetail>> existStoreDetailMap = existStoreDetailList.stream().collect(Collectors.groupingBy(detail -> detail.getStoreId()
            + "_" + detail.getGoodsNo() + "_" + detail.getChannelId() + "_" + detail.getPriceTypeCode()));

        List<PriceManageControlStoreDetail> updateList = Lists.newArrayList();
        for (PriceManageControlStoreDetailDTO storeDetail : resultDTO.getInsertList()) {
            String key = storeDetail.getStoreId() + "_" + storeDetail.getGoodsNo() + "_" + storeDetail.getChannelId() + "_" + storeDetail.getPriceTypeCode();
            if (Objects.isNull(existStoreDetailMap.get(key))) {
                continue;
            }

            List<PriceManageControlStoreDetail> existStoreDetailGroup = existStoreDetailMap.get(key);
            if (CollectionUtils.isEmpty(existStoreDetailList)) {
                continue;

            }

            existStoreDetailGroup.forEach(existStoreDetail -> {
                PriceManageControlOrderDetail orderDetail = storeDetail.getOrderDetail();

                if (StringUtils.equals(storeDetail.getControlOrderCode(), existStoreDetail.getControlOrderCode())) {
                    return;
                }

                LocalDateTime newDxsDate = orderDetail.getDxsDate();
                LocalDateTime existDxsDate = existStoreDetail.getDxsDate();
                //计算新选创建的管控单商品生效时间和已经存在的商品生效时间差 如果两个生效时间是一样的，则判断审核时间
                long days = Duration.between(newDxsDate, existDxsDate).toDays();
                if (0L != days) {
                    return;
                }

                LocalDateTime newAuditTime = orderDetail.getControlOrderAuditTime();
                LocalDateTime existAuditTime = existStoreDetail.getControlOrderAuditTime();
                //判断已经存在的管控单商品明细生效时间是否相等，如果相等则判断已经存在的管控单审核时间是否晚于新创建审核通过的管控单
                //如果已经存在管控单的审核通过时间晚于新创建的，则新建的管控单门店明细里面不生成数据
                //如果已经存在管控单的审核通过时间早于新创建的，则将已经存在的管控单门店明细废弃掉
                if (!existAuditTime.isAfter(newAuditTime)) {
                    //如果已经存在门店商品上下限价格大于新审核通过的则将已经存在的门店商品上下限的价格记录改为失效
                    existStoreDetail.setEffectStatus(EffectStatusEnum.DEPRECATED.getCode());
                    PriceManageControlStoreDetail.ExtendInfo extendInfo = new PriceManageControlStoreDetail.ExtendInfo();
                    extendInfo.setConflictControlOrderCode(orderDetail.getControlOrderCode());
                    extendInfo.setGoodsNo(orderDetail.getGoodsNo());
                    extendInfo.setNotEffectReason("与新审核通过的管控单执行时间相同但是审核时间比新审核通过的早");
                    existStoreDetail.setExtend1(JSONObject.toJSONString(extendInfo));
                    updateList.add(existStoreDetail);
                    return;
                }
            });


        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            for (PriceManageControlStoreDetail update : updateList) {
                //将比对结果出来的明细不合格的废弃掉
                storeDetailMapper.updateByPrimaryKey(update);
            }

            //管控单明细表中的主键id
            List<Long> controlOrderDetailIds = updateList.stream().map(update -> {
                String extend1 = update.getExtend1();
                if (StringUtils.isNotBlank(extend1)) {
                    PriceManageControlStoreDetail.ExtendInfo extendInfo = JSONObject.parseObject(extend1,
                        PriceManageControlStoreDetail.ExtendInfo.class);
                    return extendInfo.getPriceManageControlOrderDetailPrimaryId();
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());

            //将price_manage_control_order_detail 表中的比对的数据废弃掉
            if (CollectionUtils.isNotEmpty(controlOrderDetailIds)) {
                priceManageControlOrderService.batchUpdateControlOrderDetail(controlOrderDetailIds, EffectStatusEnum.DEPRECATED.getCode());
            }

        }

        List<PriceControlNotice> priceControlNotices = Lists.newArrayList();
        List<PriceManageControlStoreDetail> list = resultDTO.getInsertList().stream().map(insert -> {
            List<Integer> channelIds = Lists.newArrayList();
            if (Objects.nonNull(insert.getChannelId())) {
                channelIds.add(insert.getChannelId());
            }

            List<PriceStoreDetail> storePriceDetails = priceStoreDetailReadService.getPriceStoreDetailByParam(insert.getStoreId(),
                channelIds, Lists.newArrayList(insert.getGoodsNo()), Lists.newArrayList(insert.getPriceTypeCode()));
            if (CollectionUtils.isEmpty(storePriceDetails)) {
                return null;
            }

            PriceStoreDetail storePriceDetail = storePriceDetails.get(0);
            if (Objects.isNull(storePriceDetail) || Objects.isNull(storePriceDetail.getPrice())) {
                LOGGER.info("PriceManageControlStoreDetailServiceImpl|savePriceControlStoreDetail|生成管控通知门店商品价格为空!|" +
                    "controlOrderCode={},goodsNo={},storeId={},priceTypeCode={},channelId={}", insert.getControlOrderCode(),
                    storePriceDetail.getGoodsNo(), storePriceDetail.getStoreId(), storePriceDetail.getPriceTypeCode(),
                    storePriceDetail.getChannelId());
                return null;
            }

            PriceManageControlStoreDetail controlStoreDetail = new PriceManageControlStoreDetail();
            BeanUtils.copyProperties(insert, controlStoreDetail);
            controlStoreDetail.setGmtCreate(LocalDateTime.now());
            controlStoreDetail.setGmtUpdate(LocalDateTime.now());
            controlStoreDetail.setControlOrderAuditTime(Objects.nonNull(insert.getOrderDetail()) ?
                insert.getOrderDetail().getControlOrderAuditTime() : null);
            JSONObject jsonObject1 = new JSONObject();
            //对应的管控单明细表(price_manage_control_order_detail)的主键id
            jsonObject1.put("priceManageControlOrderDetailPrimaryId", insert.getOrderDetail().getId());
            controlStoreDetail.setExtend1(jsonObject1.toJSONString());

            String extend1 = storePriceDetail.getExtend1();
            if (StringUtils.isNotBlank(extend1)) {
                JSONObject extendJson = JSONObject.parseObject(extend1, JSONObject.class);
                //不执行管控单id
                String notExecControlOrderId = extendJson.getString("notExecControlOrderId");
                if (StringUtils.isNotBlank(notExecControlOrderId)) {
                    LOGGER.info("PriceManageControlStoreDetailServiceImpl|savePriceControlStoreDetail|该门店商品不需要执行管控通知|storeId={}," +
                            "goodsNo={},priceTypeCode={},channelId={},notExecControlOrderId={}", storePriceDetail.getStoreId(),
                        storePriceDetail.getGoodsNo(), storePriceDetail.getPriceTypeCode(), storePriceDetail.getChannelId(),
                        notExecControlOrderId);
                    return null;
                }
            }

            BigDecimal up = insert.getUpperLimit();
            BigDecimal low = insert.getLowerLimit();
            if (storePriceDetail.getPrice().compareTo(low) >= 0 && storePriceDetail.getPrice().compareTo(up) <= 0) {
                LOGGER.info("PriceManageControlStoreDetailServiceImpl|savePriceControlStoreDetail|该门店商品在价格管控范围之内不需要生成管控明细|storeId={}," +
                        "goodsNo={},priceTypeCode={},channelId={},ControlOrderCode={}", storePriceDetail.getStoreId(),
                    storePriceDetail.getGoodsNo(), storePriceDetail.getPriceTypeCode(), storePriceDetail.getChannelId(),
                    insert.getControlOrderCode());
                return null;
            }

            PriceControlNotice priceControlNotice = getPriceControlNotice(insert, storePriceDetail);
            priceControlNotices.add(priceControlNotice);
            return controlStoreDetail;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        try {
            if (priceControlNoticeSave && CollectionUtils.isNotEmpty(priceControlNotices)) {
                priceControlNoticeService.saveControlNotice(priceControlNotices);
            }


            if (CollectionUtils.isNotEmpty(list)) {
                priceManageControlStoreDetailMapper.batchInsert(list);
            }

        } catch (Exception e) {
            LOGGER.error("PriceManageControlStoreDetailServiceImpl|savePriceControlStoreDetail|保存管控通知异常!", e);
        }

    }

    /**
     * 判断已经存在的管控单明细上下限是否比新创建的管控单明细上下限值大
     * 如果大的话需要将已经存在的管控单门店商品明细数据废弃掉
     * @param existStoreDetail 库表已经存在的管控单门店商品明细
     * @param newOrderDetail 新创建的管控单门店商品明细
     * @return boolean
     */
    private boolean compareLimitPrice(PriceManageControlStoreDetail existStoreDetail, PriceManageControlOrderDetail newOrderDetail) {

        BigDecimal existUpLimit = existStoreDetail.getUpperLimit();
        BigDecimal newUpLimit = existStoreDetail.getUpperLimit();

        BigDecimal existLowLimit = existStoreDetail.getLowerLimit();
        BigDecimal newLowLimit = newOrderDetail.getLowerLimit();

        if (Objects.nonNull(existUpLimit) && Objects.nonNull(newUpLimit)
            && Objects.nonNull(existLowLimit) && Objects.nonNull(newLowLimit)) {
            return existUpLimit.add(existLowLimit).compareTo(newUpLimit.add(newLowLimit)) > 0;
        }

        return false;
    }

    private PriceControlNotice getPriceControlNotice(PriceManageControlStoreDetailDTO insert, PriceStoreDetail storePriceDetail) {
        PriceControlNotice priceControlNotice = new PriceControlNotice();
        BeanUtils.copyProperties(insert, priceControlNotice);
        priceControlNotice.setAdjustCode(storePriceDetail.getAdjustCode());
        priceControlNotice.setControlOrderId(insert.getControlOrderCode());
        priceControlNotice.setGmtCreate(LocalDateTime.now());
        priceControlNotice.setGmtUpdate(LocalDateTime.now());
        priceControlNotice.setPrice(storePriceDetail.getPrice());
        priceControlNotice.setGuidePrice(insert.getGuidePrice());
        priceControlNotice.setStatus(PriceNoticeStatusEnum.UN_DEAL.getCode());
        return priceControlNotice;
    }

    @Override
    public void executePriceControl() {

        if(!controlClearGoodsPriceSwitch) {
            LOGGER.info("PriceManageControlStoreDetailServiceImpl|executePriceControl|价格清空任务开关未打开！");
            return;
        }

        ControlOrderQueryDTO queryDTO = new ControlOrderQueryDTO();
        queryDTO.setControlOrderType(ControlOrderTypeEnum.CONTROL_ORDER.getCode());
        queryDTO.setAuditStatus(AuditStatusEnum.AUDIT_PASS.getCode());
        List<PriceManageControlOrder> orderList = priceManageControlOrderReadService.getControlOrderListByParam(queryDTO);
        if (CollectionUtils.isEmpty(orderList)) {
            LOGGER.info("PriceManageControlStoreDetailServiceImpl|executePriceControl|未查询到管控单");
            return;
        }

        //过滤出带有生效时间的管控单
        orderList = orderList.stream().filter(order -> Objects.equals(AuditStatusEnum.AUDIT_PASS.getCode(), order.getAuditStatus()))
            .filter(order -> Objects.nonNull(order) && Objects.nonNull(order.getScheduledTime()))
            .filter(order -> Objects.nonNull(order.getEffectTime()))
            .filter(order -> {
                LocalDateTime now = LocalDateTime.now();
                Duration duration = Duration.between(now, order.getEffectTime());
                return 0L == duration.toDays();
            }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(orderList)) {
            LOGGER.info("PriceManageControlStoreDetailServiceImpl|executePriceControl|未查询到审核通过和需要执行的管控单!");
            return;
        }

        List<String> conOrderCodeList = orderList.stream().map(controlOrder -> controlOrder.getControlOrderId()).collect(Collectors.toList());
        ControlOrderStoreDetailQueryDTO detailQueryDTO = new ControlOrderStoreDetailQueryDTO();
        detailQueryDTO.setControlOrderCodeList(conOrderCodeList);
        long count = countControlStoreDetailByParam(detailQueryDTO);
        if (0L == count) {
            LOGGER.info("PriceManageControlStoreDetailServiceImpl|executePriceControl|未查询到管控门店商品明细!");
            return;
        }

        Double totalPage = Math.ceil((new BigDecimal(count).divide(new BigDecimal(DEFAULT_PAGE_SIZE))).doubleValue());
        Integer total = new BigDecimal(Objects.isNull(totalPage) ? 0 : totalPage).intValue();

        //当天零点
        LocalDateTime today_start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        String td_start_str = today_start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        //获取当天结束时间
        LocalDateTime today_end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        String td_end_str = today_end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        ControlOrderStoreDetailQueryDTO storeDetailQueryDTO = new ControlOrderStoreDetailQueryDTO();
        storeDetailQueryDTO.setControlOrderCodeList(conOrderCodeList);
        storeDetailQueryDTO.setStartDateStr(td_start_str);
        storeDetailQueryDTO.setEndDateStr(td_end_str);
        storeDetailQueryDTO.setStartLocalDate(today_start);
        storeDetailQueryDTO.setEndLocalDate(today_end);
        storeDetailQueryDTO.setEffectStatus(EffectStatusEnum.NO_EFFECT.getCode());
        PriceManageControlStoreDetailService detailService = GetApplicationContextUtils.getBean(PriceManageControlStoreDetailService.class);
        detailService.scanControlStoreDetail(storeDetailQueryDTO);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scanControlStoreDetail(ControlOrderStoreDetailQueryDTO queryDTO) {

        PriceManageControlStoreDetailExample storeDetailExample = new PriceManageControlStoreDetailExample();
        PriceManageControlStoreDetailExample.Criteria criteria = storeDetailExample.createCriteria();
        if (CollectionUtils.isNotEmpty(queryDTO.getControlOrderCodeList())) {
            criteria.andControlOrderCodeIn(queryDTO.getControlOrderCodeList());
        }

        //查询待生效的管控门店商品明细
        if (Objects.nonNull(queryDTO.getEffectStatus())) {
            criteria.andEffectStatusEqualTo(queryDTO.getEffectStatus());
        }

        List<PriceManageControlStoreDetail> list = Lists.newArrayList();
        try (Cursor<PriceManageControlStoreDetail> cursor = priceManageControlStoreDetailMapper.cursorScanControlStoreDetail(storeDetailExample)) {
            Iterator iterator = cursor.iterator();
            while (iterator.hasNext()) {
                PriceManageControlStoreDetail storeDetail = (PriceManageControlStoreDetail) iterator.next();
                if (list.stream().count() >= 100L) {
                    controlOrderStoreDetailEffectProducer.sendMq(list);
                    list.clear();
                }
                list.add(storeDetail);
            }

            if (CollectionUtils.isNotEmpty(list)) {
                controlOrderStoreDetailEffectProducer.sendMq(list);
            }


        } catch (Exception e) {
            LOGGER.error("PriceManageControlStoreDetailServiceImpl|scanControlStoreDetail|通过游标查询管控单门店商品明细异常!", e);
        }
    }

    @Override
    public void updateControlOrderStoreDetail(List<PriceManageControlStoreDetail> storeDetails) {
        if (CollectionUtils.isEmpty(storeDetails)) {
            LOGGER.info("PriceManageControlStoreDetailServiceImpl|updateControlOrderStoreDetail|管控通知门店商品明细为空");
            return;
        }

        Map<String, List<PriceManageControlStoreDetail>> storeDetailGroup = storeDetails.stream().collect(Collectors.groupingBy(storeDetail -> storeDetail.getStoreId() + "_" + storeDetail.getBusinessId()));
        List<PriceStoreDetail> updatePriceStoreDetailList = Lists.newArrayList();
        List<PriceManageControlStoreDetail> updateManageControlStoreList = Lists.newArrayList();
        storeDetailGroup.forEach((key, listValue) -> {
            Long storeId = Long.valueOf(StringUtils.split(key, "_")[0]);
            for (PriceManageControlStoreDetail controlStoreDetail : listValue) {
                List<String> priceTypeCodeList = Lists.newArrayList(controlStoreDetail.getPriceTypeCode());
                List<String> goodsNoList = Lists.newArrayList(controlStoreDetail.getGoodsNo());
                List<Integer> channelIdList = Lists.newArrayList();
                if (Objects.nonNull(controlStoreDetail.getChannelId())) {
                    channelIdList.add(controlStoreDetail.getChannelId());
                }

                List<PriceStoreDetail> storePriceDetails = priceStoreDetailReadService.getPriceStoreDetailByParam(storeId, channelIdList, goodsNoList, priceTypeCodeList);
                if (CollectionUtils.isEmpty(storePriceDetails)) {
                    continue;
                }

                PriceStoreDetail priceStoreDetail = storePriceDetails.get(0);
                BigDecimal price = priceStoreDetail.getPrice();
                if (Objects.isNull(price)) {
                    return;
                }

                String extend1 = priceStoreDetail.getExtend1();
                if (StringUtils.isNotBlank(extend1)) {
                    JSONObject json = JSONObject.parseObject(extend1);
                    //此商品是否存在不执行管控单id
                    String noControlOrderId = json.getString("notExecControlOrderId");
                    if (StringUtils.isNotBlank(noControlOrderId)) {
                        return;
                    }
                }

                // 调整后逻辑: 若价格低于管控范围下限时，系统自动批量生成调价单，且自动提交OA审核（OA自动审核通过），对应的价格为管控范围下限。
                if (price.compareTo(controlStoreDetail.getLowerLimit()) > 0) {
                    return;
                }

//                if (price.compareTo(controlStoreDetail.getLowerLimit()) >= 0 && price.compareTo(controlStoreDetail.getUpperLimit()) <= 0) {
//                    return;
//                }

                priceStoreDetail.setPrice(null);
                JSONObject jsonObject1;
                if (StringUtils.isEmpty(priceStoreDetail.getExtend1())) {
                    jsonObject1 = new JSONObject();
                } else {
                    jsonObject1 = JSONObject.parseObject(priceStoreDetail.getExtend1());
                }

                jsonObject1.put("clearPriceReason", "不符合管控单_" + controlStoreDetail.getControlOrderCode() + "_要求");
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                String clearPriceDate = LocalDateTime.now().format(formatter);
                jsonObject1.put("clearPriceDate", clearPriceDate);
                jsonObject1.put("clearPrice", priceStoreDetail.getPrice());
                jsonObject1.put("controlCode", controlStoreDetail.getControlOrderCode());
                priceStoreDetail.setExtend1(jsonObject1.toJSONString());
                updatePriceStoreDetailList.add(priceStoreDetail);
                updateManageControlStoreList.add(controlStoreDetail);
            }


        });

        if (CollectionUtils.isEmpty(updatePriceStoreDetailList)) {
            LOGGER.info("PriceManageControlStoreDetailServiceImpl|updateControlOrderStoreDetail|未查询到符合管控的门店商品价格");
            return;
        }

//        List<PricePushTask> taskList = Lists.newArrayList();
//        List<PricePushHistory> historyList = Lists.newArrayList();
//        for (PriceStoreDetail updateStoreDetail : updatePriceStoreDetailList) {
//            priceStoreDetailService.updatePriceStoreDetail(updateStoreDetail);
//            PricePushHistory pricePushHistory = new PricePushHistory();
//            BeanUtils.copyProperties(updateStoreDetail, pricePushHistory);
//            pricePushHistory.setBatch(0);
//            pricePushHistory.setAdjustCode(storeDetails.get(0).getControlOrderCode());
//            pricePushHistory.setPriceFlag(0);
//            pricePushHistory.setStatus((byte) StatusEnum.NORMAL.getCode());
//            //按门店进行任务分组
//            RBucket<String> storePushTaskMap = redissonClient.getBucket(updateStoreDetail.getStoreId() + "_" + storeDetails.get(0).getControlOrderCode());
//            String pushCode = storePushTaskMap.get();
//            if (StringUtils.isBlank(pushCode)) {
//                pushCode = StringUtils.isNotBlank(pushCode) ? pushCode : CONTROL_ORDER_CODE.getCode() + getToday_YYYYMMDD() + IdUtils.getNumByLength(4) + updateStoreDetail.getStoreId() + "";
//                storePushTaskMap.set(pushCode);
//            }
//            pricePushHistory.setPushCode(pushCode);
//            historyList.add(pricePushHistory);
//            PricePushTask task = getPricePushTask(updateStoreDetail.getBusinessId(), updateStoreDetail.getStoreId(), storeDetails.get(0).getControlOrderCode(), pushCode);
//            taskList.add(task);
//        }
//
//        if (CollectionUtils.isNotEmpty(taskList)) {
//            pricePushService.batInsertPricePushTask(taskList);
//            taskList.forEach(task -> {
//                PricePushProduceVo pushProduceVo = getPricePushProduceVo(task.getStoreId(), task.getBusinessId(), task.getAdjustCode(), task.getPushCode());
//                pricePushTaskProducer.sendMq(pushProduceVo);
//            });
//        }
//
//        if (CollectionUtils.isNotEmpty(historyList)) {
//            pricePushHistoryService.batchInsert(historyList);
//        }

        //批量生成调价单
        batchGenAdjustPriceOrder(updateManageControlStoreList);

        //批量将管控单门店商品明细(price_manage_control_store_detail表)改为已生效
        if (CollectionUtils.isNotEmpty(updateManageControlStoreList)) {
            List<Long> ids = updateManageControlStoreList.stream().map(PriceManageControlStoreDetail::getId).collect(Collectors.toList());
            priceManageControlStoreDetailMapper.batchUpdateControlStoreDetailStatus(ids, EffectStatusEnum.EFFECTED.getCode());
        }

        //获取到所有的管控单明细主键id(price_manage_control_detail表) 需要将表中的生效状态改为已生效
        List<Long> controlDetailIdList = updateManageControlStoreList.stream().map(storeDetail -> {
            String extend1 = storeDetail.getExtend1();
            if (StringUtils.isNotBlank(extend1)) {
                JSONObject jsonObject1 = JSONObject.parseObject(extend1);
                //获取到price_manage_control_order_detail表中的主键id
                return jsonObject1.getLong("priceManageControlOrderDetailPrimaryId");
            }
            return null;
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<String> controlOrderCodeList = updateManageControlStoreList.stream().map(PriceManageControlStoreDetail::getControlOrderCode)
            .distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(controlDetailIdList)) {
            priceManageControlOrderService.batchUpdateControlOrderDetail(controlDetailIdList, EffectStatusEnum.EFFECTED.getCode());
        }

        if (CollectionUtils.isNotEmpty(controlOrderCodeList)) {
            priceManageControlOrderService.batchUpdateControlOrder(controlOrderCodeList, EffectStatusEnum.EFFECTED.getCode());
        }
    }

    /**
     * 调整后逻辑: 若价格低于管控范围下限时，系统自动批量生成调价单，且自动提交OA审核（OA自动审核通过），对应的价格为管控范围下限。
     *
     * @param priceManageControlStoreDetails
     */
    private void batchGenAdjustPriceOrder(List<PriceManageControlStoreDetail> priceManageControlStoreDetails) {
        if (CollectionUtils.isEmpty(priceManageControlStoreDetails)) {
            LOGGER.info("batchGenAdjustPriceOrder|priceManageControlStoreDetails is empty.");
            return;
        }
        List<String> controlOrderCodeList = priceManageControlStoreDetails.stream().map(PriceManageControlStoreDetail::getControlOrderCode)
            .distinct().collect(Collectors.toList());

        List<String> storeKeys = priceManageControlStoreDetails.stream().map(v -> v.getStoreId() + "-" + v.getGoodsNo() + "-" + v.getChannelId() + "-" + v.getPriceTypeCode())
            .distinct().collect(Collectors.toList());

        int limit = 1000;
        for (int i = 0;; i++) {
            PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
            noticeExample.createCriteria().andControlOrderIdIn(controlOrderCodeList);
            noticeExample.setOffset(i * limit);
            noticeExample.setLimit(limit);
            List<PriceControlNotice> noticeList = priceControlNoticeService.selectByExample(noticeExample);
            if (CollectionUtils.isEmpty(noticeList)) {
                break;
            }
            noticeList.removeIf(v -> !storeKeys.contains(v.getStoreId() + "-" + v.getGoodsNo() + "-" + v.getChannelId() + "-" + v.getPriceTypeCode()));
            noticeList.removeIf(v -> v.getPrice() == null || v.getLowerLimit() == null || v.getPrice().compareTo(v.getLowerLimit()) > 0);

            if (CollectionUtils.isNotEmpty(noticeList)) {
                priceManageControlOrderService.autoBatchOrSingleAddAdjustPriceOrder(noticeList);
            }
        }
    }

    private PricePushTask getPricePushTask(Long businessId, Long storeId, String controlOrderCode, String pushCode) {
        PricePushTask task = new PricePushTask();
        task.setBatchCount(0);
        task.setBatch(0);
        task.setBatchTotalCount(0);
        task.setBusinessId(businessId);
        task.setStoreId(storeId);
        task.setAdjustTime(new Date());
        task.setAdjustCode(controlOrderCode);
        task.setPushCode(pushCode);
        task.setType(PushTaskOrderTypeEnum.CONTROL_ORDER.getCode());
        //未同步
        task.setResult(new Byte(PushResultEnum.WAIT.getCode() + ""));
        task.setItemCount(0);
        task.setStatus(new Byte(StatusEnum.NORMAL.getCode() + ""));
        task.setGmtCreate(new Date());
        task.setGmtUpdate(new Date());
        task.setVersion(0);
        return task;
    }

    private PricePushProduceVo getPricePushProduceVo(Long storeId, Long businessId, String controlOrderCode, String pushCode) {
        PricePushProduceVo pushProduceVo = new PricePushProduceVo();
        pushProduceVo.setPushCode(pushCode);
        pushProduceVo.setStoreId(storeId);
        pushProduceVo.setType(PricePushTaskEnum.Type.CONTROL.getType());
        pushProduceVo.setSource(CONTROL_ORDER_CODE.getCode());
        pushProduceVo.setAdjustCode(controlOrderCode);
        pushProduceVo.setBusinessId(businessId);
        return pushProduceVo;
    }
}


