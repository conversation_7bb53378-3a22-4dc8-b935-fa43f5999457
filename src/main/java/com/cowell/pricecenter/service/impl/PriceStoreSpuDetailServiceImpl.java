package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.PriceStoreSpuDetail;
import com.cowell.pricecenter.entity.PriceStoreSpuDetailExample;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.mapper.PriceStoreSpuDetailMapper;
import com.cowell.pricecenter.mq.producer.PushPriceToHLProducer;
import com.cowell.pricecenter.mq.vo.PushPriceVo;
import com.cowell.pricecenter.service.PriceStoreSpuDetailService;
import com.cowell.pricecenter.service.PriceSyncBaseService;
import com.cowell.pricecenter.service.dto.request.PriceStoreSpuDetailParam;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import com.cowell.pricecenter.web.rest.util.Pagination;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Auther: zhc
 * @Description:门店SPU价格业务层实现类
 */
@Service
public class PriceStoreSpuDetailServiceImpl implements PriceStoreSpuDetailService {

    private final Logger logger = LoggerFactory.getLogger(PriceStoreSpuDetailServiceImpl.class);

    @Autowired
    private PriceStoreSpuDetailMapper priceStoreSpuDetailMapper;

    @Autowired
    private PriceSyncBaseService priceSyncBaseService;

    @Autowired
    @Qualifier("taskExecutor")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Autowired
    private PushPriceToHLProducer pushPriceToHLProducer;

    @Override
    public CommonResponse saveOrUpdatePriceSyncItem(PriceStoreSpuDetailParam param) {
        if (Objects.isNull(param.getStoreId()) && Objects.isNull(param.getSpuId()) &&
            Objects.isNull(param.getSkuId()) && Objects.isNull(param.getPrice()) &&
            StringUtils.isNotBlank(param.getPriceTypeCode()) && Objects.isNull(param.getItemId()) &&
            Objects.equals(-1, param.getPrice().compareTo(BigDecimal.ZERO))) {
            return new CommonResponse(ReturnCodeEnum.PARAM_ERROR.getCode(), ReturnCodeEnum.PARAM_ERROR.getMessage());
        }
        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (Objects.isNull(priceTypeMap)) {
            logger.info("PriceStoreSpuDetailServiceImpl|saveOrUpdatePriceSyncItem|获取可用的价格列表异常{}", priceTypeMap);
            return new CommonResponse(ReturnCodeEnum.FAIL.getCode(), "价格列表异常");
        }
        PriceType priceType = priceTypeMap.get(param.getPriceTypeCode());
        if (Objects.isNull(priceType)) {
            return new CommonResponse(ReturnCodeEnum.FAIL.getCode(), "价格类型不存在");
        }
        BigDecimal price = new BigDecimal(BigDecimalUtils.convertFenByYuan(param.getPrice().toString()));
        param.setPrice(price);
        PriceStoreSpuDetailExample example = new PriceStoreSpuDetailExample();
        example.createCriteria().andStoreIdEqualTo(param.getStoreId()).andSpuIdEqualTo(param.getSpuId())
            .andSkuIdEqualTo(param.getSkuId()).andPriceTypeCodeEqualTo(param.getPriceTypeCode());
        List<PriceStoreSpuDetail> priceStoreSpuDetails = priceStoreSpuDetailMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(priceStoreSpuDetails)) {
            PriceStoreSpuDetail priceStoreSpuDetail = new PriceStoreSpuDetail();
            BeanUtils.copyProperties(param, priceStoreSpuDetail);
            priceStoreSpuDetail.setPriceTypeId(priceType.getId());
            priceStoreSpuDetail.setPriceTypeName(priceType.getName());
            priceStoreSpuDetailMapper.insertSelective(priceStoreSpuDetail);
        } else {
            PriceStoreSpuDetail spuDetail = new PriceStoreSpuDetail();
            spuDetail.setPrice(param.getPrice());
            priceStoreSpuDetailMapper.updateByExampleSelective(spuDetail, example);
        }
        syncItemCenterNewSpuPrice(param);
        return new CommonResponse(ReturnCodeEnum.HTTP_STATUS_OK);
    }

    /**
     * 根据(门店、skuId、spuId、typeCode)查询商品价格
     */
    @Override
    public CommonResponse getSpuPriceDetail(PriceStoreSpuDetailParam param) {
        if (Objects.isNull(param.getStoreId()) && Objects.isNull(param.getSpuId()) && Objects.isNull(param.getSkuId())) {
            return new CommonResponse(ReturnCodeEnum.PARAM_ERROR.getCode(), ReturnCodeEnum.PARAM_ERROR.getMessage());
        }
        PriceStoreSpuDetailExample example = new PriceStoreSpuDetailExample();
        PriceStoreSpuDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(param.getStoreId()).andSpuIdEqualTo(param.getSpuId()).andSkuIdEqualTo(param.getSkuId());
        if (StringUtils.isNotBlank(param.getPriceTypeCode())) {
            criteria.andPriceTypeCodeEqualTo(param.getPriceTypeCode());
        } else {
            criteria.andPriceTypeCodeEqualTo(PTypeEnum.GJHIS.getCode());
        }
        List<PriceStoreSpuDetail> priceStoreSpuDetails = priceStoreSpuDetailMapper.selectByExample(example);
        logger.info("PriceStoreSpuDetailServiceImpl|getSpuPriceDetail|priceStoreSpuDetails{}", priceStoreSpuDetails);
        return new CommonResponse(priceStoreSpuDetails);
    }

    @Override
    public CommonResponse getPriceStoreSpuDetailList(PriceStoreSpuDetailParam param) {
        if (Objects.isNull(param.getStoreId())) {
            return new CommonResponse(ReturnCodeEnum.PARAM_ERROR.getCode(), ReturnCodeEnum.PARAM_ERROR.getMessage());
        }
        PriceStoreSpuDetailExample example = new PriceStoreSpuDetailExample();
        PriceStoreSpuDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(param.getStoreId());
        if (Objects.nonNull(param.getSpuId())) {
            criteria.andSpuIdEqualTo(param.getSpuId());
        }
        if (Objects.nonNull(param.getSkuId())) {
            criteria.andSkuIdEqualTo(param.getSkuId());
        }
        if (Objects.nonNull(param.getItemId())) {
            criteria.andItemIdEqualTo(param.getItemId());
        }
        example.setLimit(param.getPageSize());
        example.setOffset((param.getPage() - 1L) * param.getPageSize());
        long count = priceStoreSpuDetailMapper.countByExample(example);
        List<PriceStoreSpuDetail> priceStoreSpuDetails = priceStoreSpuDetailMapper.selectByExample(example);
        com.cowell.pricecenter.web.rest.util.Pagination<PriceStoreSpuDetail> pagination = new Pagination<>(param.getPage(), param.getPageSize());
        if (CollectionUtils.isEmpty(priceStoreSpuDetails)) {
            pagination.setSource(new ArrayList());//查询结果
            pagination.setHitCount(0);//总条数
        } else {
            pagination.setSource(priceStoreSpuDetails);//查询结果
            pagination.setHitCount(count);//总条数
        }
        return new CommonResponse(pagination);
    }

    private void syncItemCenterNewSpuPrice(PriceStoreSpuDetailParam param) {
        logger.info("PriceStoreSpuDetailServiceImpl|syncItemCenterNewSpuPrice|修改价格同步商品中心 param:{}", param);
        PushPriceVo pushPriceVo = new PushPriceVo();
        BeanUtils.copyProperties(param, pushPriceVo);
        asyncTaskExecutor.execute(() -> {
            pushPriceToHLProducer.sendMq(pushPriceVo);
        });
    }
}
