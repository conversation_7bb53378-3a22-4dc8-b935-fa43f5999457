package com.cowell.pricecenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.cowell.permission.dto.EmployeeDetailDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.pricecenter.config.Constants;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.*;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrderDetailExtMapper;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrderExtMapper;
import com.cowell.pricecenter.mapper.extension.PriceManageControlOrgDetailExtMapper;
import com.cowell.pricecenter.mq.producer.PricePushDZTMsgProducer;
import com.cowell.pricecenter.mq.producer.PushBackLogProducer;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.security.SecurityUtils;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.ControlOrderLimitDTO;
import com.cowell.pricecenter.service.dto.ControlOrderQueryDTO;
import com.cowell.pricecenter.service.dto.OrgInfoDTO;
import com.cowell.pricecenter.service.dto.PriceControlOrderQueryDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.AdjustPriceOrderV2VO;
import com.cowell.pricecenter.service.dto.response.ExportFileCubeVO;
import com.cowell.pricecenter.service.dto.response.NoticeReminder;
import com.cowell.pricecenter.service.dto.response.PriceDictionaryDTO;
import com.cowell.pricecenter.service.dto.response.amis.AmisSelectQuickEditVO;
import com.cowell.pricecenter.service.dto.response.amis.ColumnVO;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.controlOrder.*;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.utils.CriteriaUtils;
import com.cowell.pricecenter.utils.PriceUtil;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.cowell.pricecenter.web.rest.errors.AmisBusinessException;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.esotericsoftware.minlog.Log;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/29 10:54
 */
@Service
public class PriceManageControlOrderReadServiceImpl extends BasePermissionService implements PriceManageControlOrderReadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PriceManageControlOrderReadServiceImpl.class);

    private static final String KEY = "code";

    private static final String VALUE = "num";

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMMDD);

    @Value("${perm.head.org:3}")
    private String permHead;
    @Value("${control.message.notice.prop:}")
    private String controlMessageNoticeProp;
    @Value("${control.backlog.notice.prop:}")
    private String controlBacklogNoticeProp;
    @Value("${price.control.notice.query.days:1}")
    private int controlNoticeQueryDays;
    @Value("${price.control.notice.reminder.times:3}")
    private int controlNoticeReminderTimes;
    @Value("${notcontrol.roll.days:60}")
    private int notcontrolRollDays;

    @Autowired
    private PriceManageControlOrgDetailMapper controlOrgDetailMapper;

    @Autowired
    private PriceManageControlOrgDetailMapper priceManageControlOrgDetailMapper;

    @Autowired
    private PriceManageControlOrderMapper priceManageControlOrderMapper;

    @Resource
    private PriceManageControlOrderDetailMapper priceManageControlOrderDetailMapper;

    @Autowired
    private PriceManageControlOrderDetailExtMapper priceManageControlOrderDetailExtMapper;

    @Autowired
    private PriceControlNoticeMapper priceControlNoticeMapper;
    @Autowired
    private PricePushDZTMsgProducer pricePushDZTMsgProducer;
    @Autowired
    private PriceOrderOperatorLogMapper priceOrderOperatorLogMapper;

    @Autowired
    private PriceManageControlOrderExtMapper priceManageControlOrderExtMapper;

    @Autowired
    private PriceManageControlOrgDetailExtMapper priceManageControlOrgDetailExtMapper;

    @Resource
    private IAdjustPriceOrderV2Service adjustPriceOrderV2Service;

    @Resource
    private IBasePriceOrderService basePriceOrderService;

    @Autowired
    private IPermissionExtService permissionExtService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private PriceChannelService priceChannelService;

    @Autowired
    private IPriceTypeService priceTypeService;

    @Autowired
    private PriceDictionaryService priceDictionaryService;

    @Autowired
    @Qualifier("controlLimitThreadExecutor")
    private AsyncTaskExecutor controlLimitThreadExecutor;

    @Resource
    private PushBackLogProducer pushBackLogProducer;

    @Autowired
    private RedissonClient redissonClient;

    @ApolloJsonValue("${price.type.option:[]}")
    public List<OptionDto> priceTypeOptionList;

    @ApolloJsonValue("${price.channel.option:[]}")
    public List<OptionDto> priceChannelOptionList;
    
    /**
     * B2C渠道 价格
     */
    @ApolloJsonValue("${b2c.channel:{}}")
    public Map<String, List<String>> b2cChannelsMap;
    
    public List<String> getB2CPriceTypeConfig(Integer b2cChannelId) {
        if(MapUtils.isEmpty(b2cChannelsMap) || Objects.isNull(b2cChannelId)){
            return null;
        }
        return b2cChannelsMap.get(String.valueOf(b2cChannelId));
    }
    
    public List<String> getB2CChannelIdList() {
        if(MapUtils.isEmpty(b2cChannelsMap)){
            return Lists.newArrayList();
        }
        List<String> keyList = new ArrayList<>(b2cChannelsMap.keySet());
        return keyList;
    }
    
    public List<String> getB2CPriceTypeConfigAllValue() {
    	List<String> priceTypeList = Lists.newArrayList();
        if(MapUtils.isEmpty(b2cChannelsMap)){
            return priceTypeList;
        }
        for (Map.Entry<String, List<String>> entry : b2cChannelsMap.entrySet()) {
        	priceTypeList.addAll(entry.getValue());
        }
        return priceTypeList;
    }

    @Override
    public long countByExample(PriceManageControlOrderExample example) {
        return priceManageControlOrderMapper.countByExample(example);
    }

    @Override
    public List<PriceManageControlOrder> selectByExample(PriceManageControlOrderExample example) {
        return priceManageControlOrderMapper.selectByExample(example);
    }

    @Override
    public PriceManageControlOrder selectByPrimaryKey(Long id) {
        return priceManageControlOrderMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<PriceManageControlOrder> getControlOrderListByParam(ControlOrderQueryDTO queryDTO) {

        PriceManageControlOrderExample controlOrderExample = new PriceManageControlOrderExample();
        PriceManageControlOrderExample.Criteria criteria = controlOrderExample.createCriteria();
        if (StringUtils.isNotBlank(queryDTO.getControlOrderCode())) {
            criteria.andControlOrderIdEqualTo(queryDTO.getControlOrderCode());
        }

        if (Objects.nonNull(queryDTO.getControlOrderType())) {
            criteria.andControlOrderTypeEqualTo(queryDTO.getControlOrderType());
        }

        if (Objects.nonNull(queryDTO.getAuditStatus())) {
            criteria.andAuditStatusEqualTo(queryDTO.getAuditStatus());
        }
        List<PriceManageControlOrder> orderList = priceManageControlOrderMapper.selectByExample(controlOrderExample);
        return orderList;
    }

    /**
     * 获取审核通过的管控单
     * @param queryDTO
     * @return List<PriceManageControlOrder>
     */
    @Override
    public List<PriceManageControlOrder> getPriceManageControlOrderList(PriceControlOrderQueryDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            LOGGER.warn("PriceManageControlOrderServiceImpl|getPriceManageControlOrderList|查询参数为空");
            return Lists.newArrayList();
        }

        PriceManageControlOrderExample orderExample = new PriceManageControlOrderExample();
        PriceManageControlOrderExample.Criteria criteria = orderExample.createCriteria();
        if (Objects.nonNull(queryDTO.getAuditStatus())) {
            criteria.andAuditStatusEqualTo(queryDTO.getAuditStatus());
        }
        if (StringUtils.isNotBlank(queryDTO.getControlOrderId())) {
            criteria.andControlOrderIdEqualTo(queryDTO.getControlOrderId());
        }
        List<PriceManageControlOrder> controlOrders = priceManageControlOrderMapper.selectByExample(orderExample);
        return controlOrders;
    }

    /**
     * 管控单基本信息
     *
     * @param controlOrderId
     * @return
     */
    @Override
    public PriceManageControlOrderVO controlOrderBaseInfo(Long controlOrderId) {
        PriceManageControlOrder controlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlOrderId);
        if (controlOrder == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }
        List<Long> orgIdList = getOrgIdListByControlCode(controlOrder);

        List<TypeInfo<String, String>> channelInfoList = permissionExtService.getPriceChannelList();
        List<TypeInfo<String, String>> priceTypeInfoList = permissionExtService.getPriceTypeList();

        PriceManageControlOrderVO orderVO = new PriceManageControlOrderVO();
        orderVO.setControlOrderName(controlOrder.getControlOrderName());
        orderVO.setControlNoExecReason(controlOrder.getControlNoExecReason());
        orderVO.setRehectedReason(getAuditRegectedLogByControlCode(controlOrder));
        orderVO.setOrgIdList(orgIdList);
        orderVO.setId(controlOrder.getId());
        orderVO.setControlOrderId(controlOrder.getControlOrderId());
        orderVO.setAuditStatus(controlOrder.getAuditStatus());
        orderVO.setAuditStatusName(AuditStatusEnum.getName(controlOrder.getAuditStatus()));
        orderVO.setControlReason(controlOrder.getControlReason());
        orderVO.setChannelList(channelToList(controlOrder.getChannel()));
        orderVO.setChannelNameList(getNameListByCodeList(controlOrder.getChannel(), channelInfoList));
        orderVO.setPriceTypeList(priceTypeToList(controlOrder.getPriceType()));
        orderVO.setPriceTypeNameList(getNameListByCodeList(controlOrder.getPriceType(), priceTypeInfoList));
        orderVO.setControlType(controlOrder.getControlType());
        orderVO.setControlTypName(PriceManageTypeEnum.getDescByCode(controlOrder.getControlType()));
        if (controlOrder.getScheduledTime() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMMDD);
            orderVO.setScheduledTime(controlOrder.getScheduledTime().format(formatter));
        }
        return orderVO;
    }

    /**
     * 管控单商品列表
     *
     * @param controlOrderId
     * @return
     */
    @Override
    public PageResult controlOrderGoodsList(Long controlOrderId, String goodsNo, Integer page, Integer pageSize) {
        //根据管控单号和商品编码，查询商品数量
        PriceManageControlOrder manageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlOrderId);
        if (manageControlOrder == null) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
        }
        String controlOrderCode = manageControlOrder.getControlOrderId();
        int totalCount = priceManageControlOrderDetailExtMapper.controlOrderDetailListCount(controlOrderCode, goodsNo);

        List<ControlOrderDetailVO> orderDetailVOS = new ArrayList<>();
        List<PriceManageControlOrderDetail> priceManageControlOrderDetails = new ArrayList<>();
        if (totalCount > 0) {
            //根据管控单号和商品编码，查询商品列表
            orderDetailVOS = priceManageControlOrderDetailExtMapper.controlOrderDetailList(controlOrderCode, goodsNo, page * pageSize, pageSize);
            if (CollectionUtils.isNotEmpty(orderDetailVOS)) {
                List<String> goodsNoList = orderDetailVOS.stream().map(ControlOrderDetailVO::getGoodsNo).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(goodsNoList)) {
                    PriceManageControlOrderDetailExample controlOrderDetailExample = new PriceManageControlOrderDetailExample();
                    controlOrderDetailExample.createCriteria().andControlOrderCodeEqualTo(controlOrderCode).andGoodsNoIn(goodsNoList);
                    priceManageControlOrderDetails = priceManageControlOrderDetailMapper.selectByExample(controlOrderDetailExample);
                }
            }
        }
        Map<String, List<PriceManageControlOrderDetail>> controlOrderDetailMap = new HashMap<>();

        Map<String, String> priceTypeNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(priceManageControlOrderDetails)) {
            controlOrderDetailMap = priceManageControlOrderDetails.stream().collect(Collectors.groupingBy(o -> o.getControlOrderCode() + "_" + o.getGoodsNo()));
            priceManageControlOrderDetails.forEach(o -> {
                priceTypeNameMap.put(o.getPriceTypeCode(), o.getPriceTypeName());
            });
        }

        List<OptionDto> controlWayList = Lists.newLinkedList();
        List<OptionDto> controlSetWayList = Lists.newLinkedList();
        if (CollectionUtils.isNotEmpty(orderDetailVOS)) {
            for (ControlOrderDetailVO orderDetailVO : orderDetailVOS) {
                List<ControlOrderDetailPriceVO> orderDetailPriceVOS = new ArrayList<>();
                List<PriceManageControlOrderDetail> detailListByGoodsNo = controlOrderDetailMap.get(orderDetailVO.getControlOrderCode() + "_" + orderDetailVO.getGoodsNo());
                if (CollectionUtils.isEmpty(detailListByGoodsNo)) {
                    continue;
                }
                for (PriceManageControlOrderDetail orderDetail : detailListByGoodsNo) {
                    String extend = orderDetail.getExtend();
                    ControlOrderExtendDTO extendDTO = null;
                    if (StringUtils.isNotBlank(extend)) {
                        extendDTO = JSON.parseObject(extend, ControlOrderExtendDTO.class);
                    }
                    ControlOrderDetailPriceVO orderDetailPriceVO = new ControlOrderDetailPriceVO();
                    orderDetailPriceVO.setPriceTypeId(orderDetail.getPriceTypeId());
                    orderDetailPriceVO.setPriceTypeCode(orderDetail.getPriceTypeCode());
                    orderDetailPriceVO.setPriceTypeName(orderDetail.getPriceTypeName());
                    orderDetailPriceVO.setPriceTypeOutCode(orderDetail.getPriceTypeOutCode());
                    if (extendDTO != null) {
                        orderDetailPriceVO.setCurrentPrice(extendDTO.getCurrentPrice()); // 取大数据推送数据计算
                        orderDetailPriceVO.setControlWay(extendDTO.getControlWay());
                        orderDetailPriceVO.setControlWayDesc(ControlWayEnum.getName(orderDetailPriceVO.getControlWay()));
                        orderDetailPriceVO.setControlSetWay(extendDTO.getControlSetWay());
                        orderDetailPriceVO.setControlSetWayDesc(ControlSetWayEnum.getName(orderDetailPriceVO.getControlSetWay()));
                        orderDetailPriceVO.setParentUpperLimit(extendDTO.getParentUpperLimit());
                        orderDetailPriceVO.setParentLowerLimit(extendDTO.getParentLowerLimit());
                        controlWayList.add(new OptionDto(ControlWayEnum.getName(orderDetailPriceVO.getControlWay()), extendDTO.getControlWay().toString()));
                        controlSetWayList.add(new OptionDto(ControlSetWayEnum.getName(orderDetailPriceVO.getControlSetWay()), extendDTO.getControlSetWay().toString()));
                    }
                    orderDetailPriceVO.setGuidePrice(orderDetail.getGuidePrice());
                    orderDetailPriceVO.setUpperLimit(orderDetail.getUpperLimit());
                    orderDetailPriceVO.setLowerLimit(orderDetail.getLowerLimit());
                    orderDetailPriceVOS.add(orderDetailPriceVO);
                }
                orderDetailVO.setControlOrderDetailPriceVOS(orderDetailPriceVOS);
            }
        }

        PageResult pageResult = new PageResult((long) totalCount, orderDetailVOS);
        List<ColumnVO> columns = new ArrayList<>();
        for (ControlOrderGoodsListColumnEnum goodsListColumnEnum : ControlOrderGoodsListColumnEnum.values()) {
            ColumnVO columnVO = new ColumnVO();
            columnVO.setName(goodsListColumnEnum.getName());
            columnVO.setLabel(goodsListColumnEnum.getLabel());
            columnVO.setGroupName("");
            columns.add(columnVO);
        }

        List<String> priceTypeList = priceTypeToList(manageControlOrder.getPriceType());
        for (String priceTypeCode : priceTypeList) {
            String priceTypeName = priceTypeNameMap.get(priceTypeCode);
            for (ControlOrderGoodsListPriceColumnEnum listPriceColumnEnum : ControlOrderGoodsListPriceColumnEnum.values()) {
                ColumnVO columnVO = new ColumnVO();
                columnVO.setName(listPriceColumnEnum.getName());
                columnVO.setLabel(listPriceColumnEnum.getLabel());
                columnVO.setGroupName(priceTypeName);
                if (ControlOrderGoodsListPriceColumnEnum.PRICE_COLUMN_2.equals(listPriceColumnEnum)) {
                    columnVO.setQuickEdit(AmisSelectQuickEditVO.getSelectQuickEditVO(CollectionUtils.isEmpty(controlWayList) ? ControlWayEnum.getTypeInfoList() : controlWayList, true));
                }
                if (ControlOrderGoodsListPriceColumnEnum.PRICE_COLUMN_3.equals(listPriceColumnEnum)) {
                    columnVO.setQuickEdit(AmisSelectQuickEditVO.getSelectQuickEditVO(CollectionUtils.isEmpty(controlSetWayList) ? ControlSetWayEnum.getTypeInfoList() : controlSetWayList, true));
                }
                columns.add(columnVO);
            }
        }
        pageResult.setColumns(columns);
        return pageResult;
    }


    /**
     * 获取管控单参考价格范围
     *
     * @param queryParamList
     * @return
     */
    @Override
    public List<ControlOrderLimitDTO> findCurrentControlLimitOfGoodsNo(List<ControlOrderQueryParam> queryParamList) {
        if (CollectionUtils.isEmpty(queryParamList)) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<Long> orgIdList = queryParamList.stream().map(ControlOrderQueryParam::getOrgId).collect(Collectors.toList());
        //获取当前机构的上级机构
        ResponseEntity<Map<Long, List<OrgDTO>>> responseEntity = permissionService.listDirectParentOrgByOrgIdBatch(orgIdList);
        Map<Long, List<OrgDTO>> orgMap = responseEntity.getBody();
        if (null == orgMap) {
            LOGGER.warn("PermissionService|listDirectParentOrgByOrgIdBatch is null");
            return null;
        }
        //管控单从当前机构节点的上一级节点开始找管控范围
        List<ControlOrderLimitDTO> list = this.getControlOrderLimit(orgMap, queryParamList);
        if (!CollectionUtils.isEmpty(list)) {
            list = this.getUpperAndLowerLimit(list);
        }
        return list;
    }

    /**
     * 获取某机构(不会跨机构)、多渠道、某价格类型的某品的管控单的管控范围
     * @param orgMap 机构
     * @param queryParamList 查询条件
     * @return
     */
    protected List<ControlOrderLimitDTO> getControlOrderLimit(Map<Long, List<OrgDTO>> orgMap, List<ControlOrderQueryParam> queryParamList) {
        List<ControlOrderLimitDTO> list = Collections.synchronizedList(new ArrayList<>());
        List<CompletableFuture<List<ControlOrderLimitDTO>>> completableFutureList = new ArrayList<>(queryParamList.size());
        for (ControlOrderQueryParam param : queryParamList) {
            final Long[] currentOrgId = {param.getOrgId()};
            List<OrgDTO> orgDTOList = orgMap.get(currentOrgId[0]);
            if (CollectionUtils.isEmpty(orgDTOList)) {
                continue;
            }
            Map<Long, OrgDTO> map = orgDTOList.stream().collect(Collectors.toMap(OrgDTO::getId, Function.identity(), (key1, key2) -> key2, LinkedHashMap::new));
            CompletableFuture<List<ControlOrderLimitDTO>> completableFuture = CompletableFuture.supplyAsync(() -> {
                List<ControlOrderLimitDTO> limitDTOList = null;
                while (Objects.nonNull(map.get(currentOrgId[0]))) {
                    param.setOrgId(map.get(currentOrgId[0]).getParentId());
                    param.setEffectStatus(EffectStatusEnum.EFFECTED.getCode());
                    param.setStatus((byte) StatusEnum.NORMAL.getCode());
                    //多渠道
                    limitDTOList = priceManageControlOrderDetailMapper.getCurrentControlLimitOfGoodsNo(param);
                    if (!CollectionUtils.isEmpty(limitDTOList)) {
                        //得到细颗粒度品的结果了
                        break;
                    }
                    currentOrgId[0] = map.get(currentOrgId[0]).getParentId();
                }
                return limitDTOList;
            }, controlLimitThreadExecutor).whenComplete((limitDTOList, throwable) -> {
                if (CollectionUtils.isNotEmpty(limitDTOList)) {
                    for (ControlOrderLimitDTO vo : limitDTOList) {
                        StringBuffer buffer = new StringBuffer();
                        StringBuffer  uniqueGoodsNo = buffer.append(vo.getGoodsNo()).append(",")
                            .append(vo.getPriceTypeCode());
                        vo.setUniqueGoodsNo(uniqueGoodsNo.toString());
                        vo.setTempUniqueGoodsNo(uniqueGoodsNo.append(",").append(vo.getChannelId()).toString());
                    }
                }
            });
            completableFutureList.add(completableFuture);
        }
        try {
            for (CompletableFuture<List<ControlOrderLimitDTO>> completableFuture : completableFutureList) {
                List<ControlOrderLimitDTO> limitDTOList = completableFuture.get();
                if (CollectionUtils.isEmpty(limitDTOList)) {
                    continue;
                }
                list.addAll(limitDTOList);
            }
        } catch (InterruptedException e) {
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, e);
        } catch (ExecutionException e) {
            throw new AmisBusinessException(ReturnCodeEnum.SYSTEM_ERROR, e);
        }
        return list;
    }

    @Override
    public List<OptionDto> getPriceChannelList() {
        return permissionExtService.getPriceChannelList().stream().map(v -> {
            return new OptionDto(v.getMessage(), v.getCode());
        }).collect(Collectors.toList());
    }

    @Override
    public List<OptionDto> getPriceTypeList(ControlOrderStatusParam param) {
        List<OptionDto> priceTypeList = permissionExtService.getPriceTypeList(param.getUserId()).stream().map(v -> {
            return new OptionDto(v.getMessage(), v.getCode());
        }).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(priceTypeList)) {
        	return Lists.newArrayList();
        }
        if(null!=param.getChannelId()) {
        	//根据渠道查询对应价格 并且和priceTypeList取交集
        	List<String> b2cPriceTypeConfig = getB2CPriceTypeConfig(param.getChannelId());
        	if(CollectionUtils.isNotEmpty(b2cPriceTypeConfig)) {
        		Iterator<OptionDto> iterator = priceTypeList.iterator();
                while (iterator.hasNext()){
                	OptionDto dto = iterator.next();
                	if(!b2cPriceTypeConfig.contains(dto.getValue())) {
                		iterator.remove();
                	}
                }
        	}else {
        		List<String> b2cPriceTypeConfigAll = getB2CPriceTypeConfigAllValue();
        		Iterator<OptionDto> iterator = priceTypeList.iterator();
                while (iterator.hasNext()){
                	OptionDto dto = iterator.next();
                	if(b2cPriceTypeConfigAll.contains(dto.getValue())) {
                		iterator.remove();
                	}
                }
        	}
        }
        return priceTypeList;
    }

    @Override
    public List<OptionDto> getPermGoodsList() {
        return permissionExtService.getPermGoodsList().stream().map(v -> {
            return new OptionDto(v.getMessage(), v.getCode());
        }).collect(Collectors.toList());
    }

    @Override
    public PageResult<ControlOrderNoticeListVO> controlNoticeList(ControlOrderNoticeParam param) {
        try {
            //获取当前用户下的storeId
            TokenUserDTO tokenUserDTO = SecurityUtils.getCurrentUserToken();
            Long userId = tokenUserDTO.getUserId();
            List<Long> storeIdList = permissionExtService.getCurrUserBelowStoreIdList(userId, OrgTypeEnum.STORE.getCode());
            List<Long> businessIdList = permissionExtService.getCurrUserBelowStoreIdList(userId, OrgTypeEnum.BUSINESS.getCode());
            if (CollectionUtils.isEmpty(storeIdList) && CollectionUtils.isEmpty(businessIdList)) {
                LOGGER.info("controlNoticeList|该用户下没有组织信息");
                return new PageResult<>(0L, new ArrayList<>());
            }

            if(Objects.nonNull(param.getBusinessOrgId())){
                //通过OrgId反查businessId
                List<OrgInfoDTO> orgVOS = permissionService.listOrgInfoByIdWithoutType(Lists.newArrayList(Long.parseLong(param.getBusinessOrgId())));
                if (CollectionUtils.isEmpty(orgVOS)) {
                    throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CHOOSE_ORG);
                }
                Long businessIdByOrg = orgVOS.stream().filter(v->Objects.nonNull(v.getOutId())&&OrgTypeEnum.BUSINESS.getCode()==v.getType()).map(v->v.getOutId()).distinct().findFirst().orElse(0L);
                if(businessIdByOrg == null || businessIdByOrg == 0L){
                    throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CHOOSE_ORG);
                }
                param.setBusinessId(String.valueOf(businessIdByOrg));
            }

            List<Long> finalBusinessIdList = businessIdList.stream().collect(Collectors.toList());
            List<Long> finalStoreIdList = storeIdList.stream().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeIdList) && StringUtils.isNotBlank(param.getStoreIdList())) {
                List<Long> paramStoreIdList = Splitter.on(",").splitToList(param.getStoreIdList()).stream().map(Long::valueOf).collect(Collectors.toList());
                finalStoreIdList = storeIdList.stream().filter(paramStoreIdList::contains).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(businessIdList) && StringUtils.isNotBlank(param.getBusinessId())) {
                finalBusinessIdList = businessIdList.stream().filter(v -> Long.parseLong(param.getBusinessId()) == v).collect(Collectors.toList());
            }

            if (CollectionUtils.isEmpty(finalStoreIdList) && StringUtils.isNotBlank(param.getStoreIdList()) && CollectionUtils.isEmpty(finalBusinessIdList) && StringUtils.isNotBlank(param.getBusinessId())) {
                LOGGER.info("controlNoticeList|传入的门店不在用户权限范围内");
                return new PageResult<>(0L, new ArrayList<>());
            }

            PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
            PriceControlNoticeExample.Criteria criteria = noticeExample.createCriteria();
            PriceControlNoticeExample.Criteria criteria2 = noticeExample.or();
            if (CollectionUtils.isNotEmpty(finalStoreIdList)) {
                criteria.andStoreIdIn(finalStoreIdList);
            }
            if (CollectionUtils.isNotEmpty(finalBusinessIdList)) {
                criteria.andBusinessIdIn(finalBusinessIdList);
            } else if (Objects.nonNull(param.getBusinessId())) {
                criteria.andBusinessIdEqualTo(Long.parseLong(param.getBusinessId()));
            }

            if (StringUtils.isNotBlank(param.getGoodsKeyword())) {
                criteria.andGoodsNoLike(new StringBuilder().append(Constants.LIKE).append(StringUtils.trim(param.getGoodsKeyword())).append(Constants.LIKE).toString());
                CriteriaUtils.copyCriteria(criteria, criteria2);
                criteria2.andCurNameLike(new StringBuilder().append(Constants.LIKE).append(StringUtils.trim(param.getGoodsKeyword())).append(Constants.LIKE).toString());
            }

            if (StringUtils.isNotBlank(param.getStoreKeyword())) {
                criteria.andStoreNameLike(new StringBuilder().append(Constants.LIKE).append(StringUtils.trim(param.getStoreKeyword())).append(Constants.LIKE).toString());
            }

            if (Objects.nonNull(param.getChannelId())) {
                criteria.andChannelIdEqualTo(param.getChannelId());
            }
            if (StringUtils.isNotBlank(param.getPriceTypeCode())) {
                criteria.andPriceTypeCodeEqualTo(param.getPriceTypeCode());
            }
            
            if(null!=param.getStatus()) {
            	criteria.andStatusEqualTo(param.getStatus());
            }

            if (Objects.nonNull(param.getIsPop()) && param.getIsPop()) {
                criteria.andStatusIn(Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.REJECTED_DEAL.getCode()));
                // 设置已提醒次数+1
                RBucket<Integer> remindCount = redissonClient.getBucket(RedisKeysConstant.PRICE_CONTROL_NOTICE_REMIND_KEY + tokenUserDTO.getUserId() + "_" + tokenUserDTO.getBusinessId());
                remindCount.setAsync(Optional.ofNullable(remindCount.get()).orElse(0) + 1, 1L, TimeUnit.DAYS);
            }

            long count = priceControlNoticeMapper.countByExample(noticeExample);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            noticeExample.setOffset((param.getPage() - 1) * param.getPageSize());
            noticeExample.setLimit(param.getPageSize());
            noticeExample.setOrderByClause(" gmt_create desc");
            List<PriceControlNotice> priceControlNoticeList = priceControlNoticeMapper.selectByExample(noticeExample);
            List<ControlOrderNoticeListVO> noticeListDTOList = noticeVOList(priceControlNoticeList);
            return new PageResult<>(count, noticeListDTOList);
        } catch (Exception e) {
            LOGGER.error("获取价格管控通知列表异常", e);
            throw e;
        }
    }

    @Override
    public PageResult<ControlOrderNoticeListVO> controlNoticeUnifyList(ControlOrderNoticeParam param) {
        if (StringUtils.isBlank(param.getReqResource())) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        switch (ControlNoticeSourceEnum.getEnum(param.getReqResource())) {
            case LIST:
                return controlNoticeList(param);
            case NOT_LIST:
                return notControlNoticeGoodsList(param);
            case APP_LIST:
                return controlNoticeAppList(param);
            default:
                break;
        }
        return null;
    }

    @Override
    public PageResult<AdjustPriceOrderV2VO> adjustPriceOrderList(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO) {
        AdjustPriceOrderListV2Param adjustPriceOrderListV2Param = new AdjustPriceOrderListV2Param();
        adjustPriceOrderListV2Param.setAdjustPriceOrderIdList(param.getAdjustPriceOrderIdList());
        PageResult<AdjustPriceOrderV2VO> pageResult = adjustPriceOrderV2Service.searchControlAdjustPriceOrderList(adjustPriceOrderListV2Param, userDTO);
        return new PageResult(pageResult.getTotal(), pageResult.getRows());
    }

    @Override
    public List<OptionDto> getBusinessInfoByCode(ControlOrderStatusParam param) {
        return permissionExtService.getGoodsBusinessList(getControlOrgInfo(param.getId()));
    }

    @Override
    public List<OptionDto> getStoreInfoByCode(ControlOrderStatusParam param) {
        return permissionExtService.getGoodsStoreList(getControlOrgInfo(param.getId()));
    }

    @Override
    public PageResult<Map<String, Object>> listControlOrderDetailsPage(ControlOrderDetailListV2Param param, TokenUserDTO userDTO) {
        if (Objects.isNull(param.getControlOrderId())) {
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        PriceManageControlOrder controlOrder = priceManageControlOrderMapper.selectByPrimaryKey(param.getControlOrderId());
        //校验
        basePriceOrderService.checkControlOrderMustHasProperties(controlOrder);
        param.setControlOrderCode(controlOrder.getControlOrderId());

        param.setControlOrderCode(controlOrder.getControlOrderId());
        param.setOffset((param.getPage() - 1) * param.getSize());
        param.setGoodsKeyword(org.springframework.util.StringUtils.trimAllWhitespace(param.getGoodsKeyword()));
        long count = priceManageControlOrderDetailExtMapper.selectRemoveChannelAttOrderDetailV2ListCount(param);
        List<Map<String, Object>> controlOrderDetailListResults = Collections.emptyList();
        if (count > 0) {
            List<String> goodsNoList = priceManageControlOrderDetailExtMapper.selectOrderDetailGoodsV2ListPageGroupByGoodAndPriceType(param);
            if (CollectionUtils.isNotEmpty(goodsNoList)) {
                List<PriceManageControlOrderDetail> controlOrderDetailList = priceManageControlOrderDetailExtMapper.selectOrderDetailListGroupByGoodsAndPriceType(controlOrder.getControlOrderId(), goodsNoList);
                controlOrderDetailListResults = getControlOrderDetailListResults(controlOrder, controlOrderDetailList, param.getIsEdit());
            }
        }

        List<ColumnVO> columns = getControlOrderDetailColumnVO(controlOrder.getPriceType());
        return new PageResult<>(count, columns, controlOrderDetailListResults);
    }

    @Override
    public ExportFileCubeVO<Map<String, Object>> exportControlOrderDetailsFile(Long controlOrderId, Integer page, Integer pageSize) {
        if (Objects.isNull(page) || Objects.isNull(pageSize)) {
            LOGGER.info("分页参数为空,赋初始值");
            page = 1;
            pageSize = 200;
        }
        if (page < 1 || pageSize < 1) {
            LOGGER.info("分页参数小于1,赋初始值");
            page = 1;
            pageSize = 200;
        }
        PriceManageControlOrder controlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlOrderId);
        //校验
        basePriceOrderService.checkControlOrderMustHasProperties(controlOrder);
        String fileName = "管控单商品明细_".concat(DateUtils.dateToString(new Date(), DateUtils.DATETIME_FORMAT));
        LinkedHashMap<String, String> fieldMap = getControlOrderDetailDownloadFieldMap(controlOrder.getPriceType());
        PriceManageControlOrderDetailExample controlOrderDetailExample = new PriceManageControlOrderDetailExample();
        controlOrderDetailExample.createCriteria().andControlOrderCodeEqualTo(controlOrder.getControlOrderId());
        controlOrderDetailExample.setLimit(pageSize);
        controlOrderDetailExample.setOffset(Long.valueOf((page - 1) * pageSize));
        List<PriceManageControlOrderDetail> controlOrderDetailList = priceManageControlOrderDetailMapper.selectByExample(controlOrderDetailExample);
        if (CollectionUtils.isEmpty(controlOrderDetailList)) {
            return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_CONTROL_ORDER_DETAIL.getUploadUrl(),
                fieldMap, new ArrayList<>());
        }
        List<Map<String, Object>> adjustPriceOrderDetailListResults = getControlOrderDetailListResults(controlOrder, controlOrderDetailList, false);

        return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_CONTROL_ORDER_DETAIL.getUploadUrl(),
            fieldMap, adjustPriceOrderDetailListResults);
    }

    @Override
    public ExportFileCubeVO<Map<String, Object>> asyncExportControlOrderNoticeFile(List<Long> controlOrderNoticeIdList, Integer page, Integer pageSize) {
        String fileName = "价格管控调价商品明细_".concat(DateUtils.dateToString(new Date(), DateUtils.DATETIME_FORMAT));
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        for (ControlOrderNoticeColumnEnum commonColumnEnum : ControlOrderNoticeColumnEnum.values()) {
            fieldMap.put(commonColumnEnum.getName(), commonColumnEnum.getLabel());
        }
        if (CollectionUtils.isEmpty(controlOrderNoticeIdList)) {
            return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_CONTROL_NOTICE_DETAIL.getUploadUrl(), fieldMap, new ArrayList<>());
        }
        PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
        noticeExample.createCriteria().andIdIn(controlOrderNoticeIdList);
        noticeExample.setLimit(pageSize);
        noticeExample.setOffset((page - 1) * pageSize);
        List<PriceControlNotice> priceControlNoticeList = priceControlNoticeMapper.selectByExample(noticeExample);

        if (CollectionUtils.isEmpty(priceControlNoticeList)) {
            return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_CONTROL_NOTICE_DETAIL.getUploadUrl(), fieldMap, new ArrayList<>());
        }
        return ExportFileCubeVO.getInstance(fileName, null, AsyncExportActionEnum.PRICE_CONTROL_NOTICE_DETAIL.getUploadUrl(), fieldMap, priceControlNoticeList);
    }

    @Override
    public PageResult<ControlOrderNoticeListVO> controlNoticeAppList(ControlOrderNoticeParam param) {
        PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
        PriceControlNoticeExample.Criteria criteria = noticeExample.createCriteria();
        criteria.andStoreIdIn(Lists.newArrayList(param.getStoreId()));
        criteria.andGmtCreateGreaterThanOrEqualTo(LocalDateTime.now().minusDays(controlNoticeQueryDays));// 取数 T-3
        if(Objects.nonNull(param.getReqResource()) && ControlNoticeSourceEnum.APP_LIST.getCode().equals(param.getReqResource())){
            //web端展示需要过滤状态，因为有操作；APP端不需要过滤状态，因为只是查看；否则状态变化，会查不到数据
            criteria.andStatusIn(Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.REJECTED_DEAL.getCode()));
        }
        long resultCount = priceControlNoticeMapper.countByExample(noticeExample);
        if (resultCount <= 0) {
            return new PageResult<>(0L, new ArrayList<>());
        }
        Map<Long, List<PriceControlNotice>> priceControlNoticeMap = priceControlNoticeMapper.selectByExample(noticeExample).stream().collect(Collectors.groupingBy(v -> v.getStoreId()));
        List<ControlOrderNoticeListVO> noticeListAppDTOList = Lists.newArrayList();
        priceControlNoticeMap.forEach((k, list) -> {
            ControlOrderNoticeListVO noticeListAppDTO = new ControlOrderNoticeListVO();
            PriceControlNotice priceControlNotice = list.get(0);
            noticeListAppDTO.setStoreId(k);
            noticeListAppDTO.setStoreName(priceControlNotice.getStoreName());
            noticeListAppDTO.setBusinessId(priceControlNotice.getBusinessId());
            noticeListAppDTO.setBusinessName(priceControlNotice.getBusinessName());
            noticeListAppDTO.setGoodsCount((int) list.stream().map(PriceControlNotice::getGoodsNo).distinct().count());
            noticeListAppDTOList.add(noticeListAppDTO);
        });
        return new PageResult<>((long) noticeListAppDTOList.size(), noticeListAppDTOList);
    }

    @Override
    public PageResult<ControlOrderNoticeListVO> notControlNoticeGoodsList(ControlOrderNoticeParam param) {
        try {
            //获取当前用户下的storeId
            Long userId = SecurityUtils.getCurrentUserToken().getUserId();
            List<Long> storeIdList = permissionExtService.getCurrUserBelowStoreIdList(userId, OrgTypeEnum.STORE.getCode());
            if (CollectionUtils.isEmpty(storeIdList)) {
                throw new AmisBadRequestException(ReturnCodeEnum.ERROR_USER_STORE_NULL);
            }

            if (StringUtils.isBlank(param.getUnControlOrderId()) && CollectionUtils.isEmpty(param.getNoticeIdList())) {
                //不执行管控单ID不能为空
                throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
            }

            PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
            PriceControlNoticeExample.Criteria criteria = noticeExample.createCriteria();
            criteria.andStoreIdIn(storeIdList);
            if (StringUtils.isNotBlank(param.getUnControlOrderId())) {
                //不执行管控详情页面逻辑，只展示处理过的通知
                criteria.andStatusNotIn(Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.DELETE.getCode()));
                PriceManageControlOrder unPriceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(Long.parseLong(param.getUnControlOrderId()));
                if (Objects.isNull(unPriceManageControlOrder)) {
                    throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UNCONTROL_ORDER_NULL);
                }
                if (StringUtils.isBlank(unPriceManageControlOrder.getExtend())) {
                    throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UNCONTROL_NO_MAPPING);
                }

                Optional<ControlExtendVO> controlExtendVO = ControlExtendVO.getInstance(unPriceManageControlOrder.getExtend());
                if (Objects.isNull(controlExtendVO) || !controlExtendVO.isPresent() || StringUtils.isBlank(controlExtendVO.get().getControlOrderCode())) {
                    throw new AmisBadRequestException(ReturnCodeEnum.ERROR_UNCONTROL_NO_MAPPING);
                }
                criteria.andControlOrderIdEqualTo(controlExtendVO.get().getControlOrderCode());
                if (StringUtils.isNotBlank(controlExtendVO.get().getNoticeIds())) {
                    criteria.andIdIn(Arrays.asList(controlExtendVO.get().getNoticeIds().split(",")).stream().map(v -> Long.parseLong(v)).collect(Collectors.toList()));
                }
            }
            if (CollectionUtils.isNotEmpty(param.getNoticeIdList())) {
                criteria.andIdIn(param.getNoticeIdList());
            }
            if (StringUtils.isNotBlank(param.getGoodsKeyword())) {
                criteria.andGoodsNoEqualTo(param.getGoodsKeyword());
            }
            if (StringUtils.isNotBlank(param.getPriceTypeCode())) {
                criteria.andPriceTypeCodeEqualTo(param.getPriceTypeCode());
            }
            if (Objects.nonNull(param.getChannelId())) {
                criteria.andChannelIdEqualTo(param.getChannelId());
            }

            long count = priceControlNoticeMapper.countByExample(noticeExample);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            noticeExample.setOrderByClause(" gmt_create desc");
            List<PriceControlNotice> priceControlNoticeList = priceControlNoticeMapper.selectByExample(noticeExample);
            List<ControlOrderNoticeListVO> noticeListDTOList = noticeVOList(priceControlNoticeList);
            return new PageResult<>(count, noticeListDTOList);
        } catch (Exception e) {
            LOGGER.error("管控通知商品列表异常", e);
            throw e;
        }
    }

    @Override
    public PageResult<PriceOperatorLogDTO> operatorLogList(PriceOperatorLogParam param, TokenUserDTO userDTO) {
        try {
            if (Objects.isNull(param) || StringUtils.isBlank(param.getOrderId())) {
                throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CONTROL_NULL);
            }
            PriceOrderOperatorLogExample logExample = new PriceOrderOperatorLogExample();
            logExample.createCriteria().andOrderIdEqualTo(param.getOrderId()).andPriceOrderTypeEqualTo(PriceOrderTypeEnum.CONTROL.getCode());
            logExample.setOrderByClause("gmt_create desc");
            long count = priceOrderOperatorLogMapper.countByExample(logExample);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            List<PriceOrderOperatorLog> logList = priceOrderOperatorLogMapper.selectByExample(logExample);

            List<PriceOperatorLogDTO> priceOperatorLogDTOList = logList.stream().map(v -> {
                PriceOperatorLogDTO priceOperatorLogDTO = new PriceOperatorLogDTO();
                priceOperatorLogDTO.setOperatorUserName(v.getOperatorUserName());
                priceOperatorLogDTO.setOrderStatus(v.getOrderStatus());
                priceOperatorLogDTO.setOrderStatusName(AuditStatusEnum.getName(v.getOrderStatus()));
                priceOperatorLogDTO.setRejectReasion(v.getRejectReasion());
                priceOperatorLogDTO.setGmtCreate(DateUtils.dateToString(v.getGmtCreate()));
                return priceOperatorLogDTO;
            }).collect(Collectors.toList());

            return new PageResult(count, priceOperatorLogDTOList);
        } catch (Exception e) {
            LOGGER.error("获取状态跟踪列表异常", e);
            throw e;
        }
    }

    @Override
    public NoticeReminder getNoticeReminder() {
        NoticeReminder noticeReminder = new NoticeReminder();
        try {
            TokenUserDTO tokenUserDTO = SecurityUtils.getCurrentUserToken();
            noticeReminder.setBusinessId(tokenUserDTO.getBusinessId());

            //获取当前用户下的storeId
            Long userId = tokenUserDTO.getUserId();
            List<Long> storeIdList = permissionExtService.getCurrUserBelowStoreIdList(userId, OrgTypeEnum.STORE.getCode());
            if(CollectionUtils.isEmpty(storeIdList)){
                noticeReminder.setRemind(false);
                return noticeReminder;
            }

            PriceControlNoticeExample example = new PriceControlNoticeExample();
            example.createCriteria().andBusinessIdEqualTo(tokenUserDTO.getBusinessId())
                .andStoreIdIn(storeIdList)
                .andGmtCreateGreaterThanOrEqualTo(LocalDateTime.now().minusDays(controlNoticeQueryDays));
            long count = priceControlNoticeMapper.countByExample(example);

            RBucket<Integer> remindCount = redissonClient.getBucket(RedisKeysConstant.PRICE_CONTROL_NOTICE_REMIND_KEY + tokenUserDTO.getUserId() + "_" + tokenUserDTO.getBusinessId());
            if (count > 0 && (remindCount.get() == null || remindCount.get() <= controlNoticeReminderTimes)) {
                noticeReminder.setRemind(true);
            }
            return noticeReminder;
        } catch (Exception e) {
            LOGGER.warn("获取价格管控通知提醒异常", e);
            noticeReminder.setRemind(false);
            return noticeReminder;
        }
    }

    @Override
    public Boolean pushCobtrolMessage() {
        try {
            List<PriceControlNotice> priceControlNoticeList = getUnDealNoticeList();

            // 根据角色编码查员工信息
            List<EmployeeDetailDTO> employeeDetailDTOs = getNeedPushUsers();
            if (CollectionUtils.isEmpty(employeeDetailDTOs)) {
                LOGGER.warn("pushCobtrolMessage|没有与角色相关的用户");
                return false;
            }
            ZDTMsgPushDTO pushDTO = messageNoticeInstance();
            priceControlNoticeList.stream().forEach(v -> {
                ZDTMsgPushDTO zDTMsgPushDTO = new ZDTMsgPushDTO();
                zDTMsgPushDTO.setTitle(pushDTO.getTitle());
                zDTMsgPushDTO.setDescription(pushDTO.getDescription());
                zDTMsgPushDTO.setReceiverIds(new Long[]{v.getCreatedBy()});
                zDTMsgPushDTO.setType(pushDTO.getType());
                zDTMsgPushDTO.setAppType(pushDTO.getAppType());
                zDTMsgPushDTO.setIdentification(new StringBuilder().append(Constants.APP_NAME).append(Constants.LINE).append(System.currentTimeMillis()).toString());
                zDTMsgPushDTO.setUrl(new StringBuilder().append(pushDTO.getUrl()).append(v.getStoreId()).toString());
                pricePushDZTMsgProducer.sendMq(zDTMsgPushDTO);
            });
        } catch (Exception e) {
            LOGGER.warn("pushCobtrolMessage|error", e);
            return false;
        }
        return true;
    }

    @Override
    public Boolean pushBackLog() {
        List<PriceControlNotice> priceControlNoticeList = getUnDealNoticeList();

        // 根据角色编码查员工信息
        List<EmployeeDetailDTO> employeeDetailDTOs = getNeedPushUsers();
        if (CollectionUtils.isEmpty(employeeDetailDTOs)) {
            LOGGER.warn("pushBackLog|没有与角色相关的用户");
            return false;
        }

        BackLogCreateParam headParam = backLogInstance();
        priceControlNoticeList.stream().forEach(v -> {
            employeeDetailDTOs.forEach(employeeDetailDTO -> {
                BackLogCreateParam createParam = new BackLogCreateParam();
                createParam.setBusinessId(v.getBusinessId());
                createParam.setStoreId(v.getStoreId());
                createParam.setUserId(employeeDetailDTO.getUserId());
                createParam.setTaskId(0L);
                createParam.setGoodsNo(v.getGoodsNo());
                createParam.setTypeId(0L);
                createParam.setTypeCode(headParam.getTypeCode());//待办中心提供
                createParam.setTaskTag(headParam.getTaskTag());
                createParam.setTaskName(headParam.getTaskName());//待办事项名称
                createParam.setDisplayType(headParam.getDisplayType());
                createParam.setShowChannel(headParam.getShowChannel());//展示渠道
                createParam.setCloseType(headParam.getCloseType());//关闭类型
                createParam.setDeWeight(headParam.getDeWeight());
                createParam.setJumpPath(new StringBuilder().append(headParam.getJumpPath()).append(v.getStoreId()).toString());//跳转路径
                pushBackLogProducer.sendMq(createParam);
            });
        });
        return true;
    }

    /**
     * 获取需要推送的用户
     * @return
     */
    private List<EmployeeDetailDTO> getNeedPushUsers() {
        BackLogCreateParam createParam = backLogInstance();
        // 角色编码
        String[] roleCodes = org.apache.commons.lang.StringUtils.split(createParam.getRoleCodes(), ",");
        // 根据角色编码查员工信息
        List<EmployeeDetailDTO> employeeDetailDTOs = permissionExtService.getBatchUsersByRolesAndOrgIds(Lists.newArrayList(roleCodes), null);
        return employeeDetailDTOs;
    }

    /**
     * 获取未处理的通知
     * @return
     */
    private List<PriceControlNotice> getUnDealNoticeList() {
        PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
        noticeExample.createCriteria().andStatusNotIn(Lists.newArrayList(PriceNoticeStatusEnum.UN_DEAL.getCode(), PriceNoticeStatusEnum.REJECTED_DEAL.getCode()))
            .andGmtCreateGreaterThanOrEqualTo(LocalDateTime.now().minusDays(controlNoticeQueryDays));
        return priceControlNoticeMapper.selectByExample(noticeExample);
    }

    @Override
    public List<OptionDto> getAllPriceTypeList() {
    	return priceTypeOptionList;
    }

    @Override
    public List<OptionDto> getAllPriceChannelList() {
        List<PriceDictionaryDTO> dictionaryDTOS = priceDictionaryService.getChildrenByCode(DictCodeEnum.CHANNEL.getCode());
        if (CollectionUtils.isEmpty(dictionaryDTOS)) {
            return Collections.emptyList();
        }
        return dictionaryDTOS.stream().map(v -> new OptionDto(v.getDictName(), v.getDictCode())).collect(Collectors.toList());
    }

    @Override
    public List<OptionDto> controlSelectUnifyList(ControlOrderStatusParam param) {
    	List<OptionDto> optionDtoList = buildControlSelectUnifyList(param);
    	if(null != optionDtoList) {
    		if(StringUtils.isNotBlank(param.getOrderCode())) {
            	AdjustPriceOrder priceOrder = adjustPriceOrderV2Service.selectByAdjustCode(param.getOrderCode());
            	if(null!=priceOrder) {
            		if(AdjustTypeEnum.ERROR_PRICE_SYNC.getCode()==priceOrder.getAdjustType()) {
                		OptionDto odto = new OptionDto(AdjustTypeEnum.ERROR_PRICE_SYNC.getMessageV2(),AdjustTypeEnum.ERROR_PRICE_SYNC.getCode()+"");
                		optionDtoList.add(odto);
                	}else if(AdjustTypeEnum.INIT_NEWSTORE_PRICE.getCode()==priceOrder.getAdjustType()) {
                		OptionDto odto = new OptionDto(AdjustTypeEnum.INIT_NEWSTORE_PRICE.getMessageV2(),AdjustTypeEnum.INIT_NEWSTORE_PRICE.getCode()+"");
                		optionDtoList.add(odto);
                	}
            	}
            	
            }
            if(param.getApiCode().equals(PriceManageStatusApiEnum.ADJUST_TYPE.getCode())) {
            	if(null !=param.getGoodsScope() && param.getGoodsScope().intValue()==GoodsScopeEnum.SENSITIVE_GOODS.getCode()) {
                	List<OptionDto> optionDtoNewList = Lists.newArrayList();
                	CommonEnums.SensitiveGoodsAdjustTypeEnum[] values = CommonEnums.SensitiveGoodsAdjustTypeEnum.values();
                	for (CommonEnums.SensitiveGoodsAdjustTypeEnum sensitiveGoods : values) {
                		if(optionDtoList.stream().anyMatch(optionDto -> sensitiveGoods.getCode() == Integer.valueOf(optionDto.getValue()).intValue())) {
                			OptionDto odto = new OptionDto(sensitiveGoods.getMessageV2(),sensitiveGoods.getCode()+"");
                			optionDtoNewList.add(odto);
                		}
        			}
                	optionDtoList.clear();
                	optionDtoList.addAll(optionDtoNewList);
                }else {
                	CommonEnums.SensitiveGoodsAdjustTypeEnum[] values = CommonEnums.SensitiveGoodsAdjustTypeEnum.values();
                	for (CommonEnums.SensitiveGoodsAdjustTypeEnum sensitiveGoods : values) {
                		Iterator<OptionDto> iterator = optionDtoList.iterator();
                		while (iterator.hasNext()) {
                			OptionDto optionDto = iterator.next();
                			if (sensitiveGoods.getCode() == Integer.valueOf(optionDto.getValue())) {
                				iterator.remove();
                			}
                		}
        			}
                }
            }
    	}
    	return optionDtoList;
    }
    
    private List<OptionDto> buildControlSelectUnifyList(ControlOrderStatusParam param){
    	switch (PriceManageStatusApiEnum.getEnum(param.getApiCode())) {
	        case AUDIT_STATUS:
	            return Arrays.stream(AuditStatusEnum.values()).filter(v -> AuditStatusEnum.AUDIT_OVERDUE.getCode() != v.getCode())
	                .map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case CONTROL_TYPE:
	            return Arrays.stream(PriceManageTypeEnum.values()).filter(v -> PriceManageTypeEnum.NOT_APPLY.getCode() != v.getCode())
	                .map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case EFFECT_STATUS:
	            return Arrays.stream(PriceEffectStatusEnum.values())
	                .map(typeEnum -> new OptionDto(typeEnum.getDesc(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case PRICE_CHANNEL:
	            return getPriceChannelList();
	        case ALL_PRICE_CHANNEL:
	            return getAllPriceChannelList();
	        case PRICE_TYPE:
	            return getPriceTypeList(param);
	        case ALL_PRICE_TYPE:
	            return getAllPriceTypeList();
	        case PRICE_PERM_GOODS:
	            return getPermGoodsList();
	        case ORDER_BUSINESS:
	            return getBusinessInfoByCode(param);
	        case ORDER_STORE:
	            return getStoreInfoByCode(param);
	        case CONTROL_STATUS:
	            return Arrays.stream(PriceNoticeStatusEnum.values()).
	            		map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case ADJUST_EFFECT_STATUS:
	            return Arrays.stream(EffectStatusEnum.values()).filter(te -> te.getCode()!=EffectStatusEnum.DEPRECATED.getCode())
	                .map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case ADJUST_TYPE:
	            return getAdjustTypeList();
	        case TASK_TYPE:
	            return Arrays.stream(TaskCodeTypeEnun.values()).
	            		map(typeEnum -> new OptionDto(typeEnum.getMsg(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case TASK_STATUS:
	            return Arrays.stream(TaskStatusEnun.values()).
	            		map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case PURCHASE_ATTRIBUTE_TYPE:
	            return Arrays.stream(PurchaseAttributeEnum.values()).
	            		map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case CONTROL_GOODS_PROPERTY:
	            return Arrays.stream(ControlGoodsPropertyEnum.values()).
	            		map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case XINGUAN_GOODS_TAG_ITEM:
	            return Arrays.stream(XinGuanGoodsTagEnum.values()).
	            		map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        case SALES_PROPERTY_ITEM:
	            return Arrays.stream(SalesPropertyEnum.values()).
	            		map(typeEnum -> new OptionDto(typeEnum.getMessage(), String.valueOf(typeEnum.getCode()))).collect(Collectors.toList());
	        default:
	            break;
	    }
	    return null;
    }

    /**
     *
     * @param adjustPriceTypeCodes
     * @return
     */
    @Override
    public LinkedHashMap<String, String> getControlOrderDetailDownloadFieldMap(String adjustPriceTypeCodes) {
        LinkedHashMap<String, String> fieldMap = new LinkedHashMap<>();
        for (ControlOrderDetailBaseColumnEnum commonColumnEnum : ControlOrderDetailBaseColumnEnum.values()) {
            fieldMap.put(commonColumnEnum.getName(), commonColumnEnum.getLabel());
        }
        List<PriceType> sortedPriceTypeList = basePriceOrderService.getSortedPriceTypeList(adjustPriceTypeCodes);
        for (PriceType priceType : sortedPriceTypeList) {
            for (ControlOrderDetailPriceTypeColumnEnum priceColumnEnum : ControlOrderDetailPriceTypeColumnEnum.values()) {
                fieldMap.put(priceColumnEnum.getNameByPriceTypeCode(priceType.getCode()),
                    priceColumnEnum.getLabelByPriceTypeName(priceType.getName()));
            }
        }
        return fieldMap;
    }

    /**
     * 查询管控单列表
     * @param param
     * @return
     */
    @Override
    public PageResult<ControlOrderListVO> controlOrderList(ControlOrderListParam param, Integer controlOrderType) {
        try {
            TokenUserDTO userDTO = SecurityUtils.getCurrentUserToken();
            List<Long> finalStoreIdList = Lists.newArrayList();
            param.setControlOrderType(controlOrderType);

            if (StringUtils.isNotBlank(param.getEffectStartEndTime())) {
                List<String> effectStartEndTimeList = Splitter.on(",").splitToList(param.getEffectStartEndTime());
                param.setEffectStartTime(LocalDateTime.ofInstant(new Date(Long.parseLong(effectStartEndTimeList.get(0)) * 1000L).toInstant(), ZoneId.systemDefault()));
                param.setEffectEndTime(LocalDateTime.ofInstant(new Date(Long.parseLong(effectStartEndTimeList.get(1)) * 1000L).toInstant(), ZoneId.systemDefault()));
            }
            if (StringUtils.isNotBlank(param.getGmtCreateStartEndTime())) {
                List<String> gmtStartEndTimeList = Splitter.on(",").splitToList(param.getGmtCreateStartEndTime());
                param.setStartTime(LocalDateTime.ofInstant(new Date(Long.parseLong(gmtStartEndTimeList.get(0)) * 1000L).toInstant(), ZoneId.systemDefault()));
                param.setEndTime(LocalDateTime.ofInstant(new Date(Long.parseLong(gmtStartEndTimeList.get(1)) * 1000L).toInstant(), ZoneId.systemDefault()));
            }

            if (Objects.nonNull(param.getPlatformOrgId())) {
                finalStoreIdList.add(param.getPlatformOrgId());
            }
            if (CollectionUtils.isNotEmpty(param.getOrgIdList())) {
                finalStoreIdList.addAll(param.getOrgIdList());
            }

            List<OrgDTO> orgLevelVOList = permissionExtService.findIsFullScopeSonOrgList(userDTO.getUserId(), Lists.newArrayList(Long.parseLong(permHead)));
            if (CollectionUtils.isEmpty(finalStoreIdList)) {
                if (CollectionUtils.isEmpty(orgLevelVOList)) {
                    throw new AmisBadRequestException(ReturnCodeEnum.ERROR_CURR_USER);
                }
                orgLevelVOList.stream().filter(v->StringUtils.isNotBlank(v.getOrgPath())).forEach(v->{
                    finalStoreIdList.addAll(Splitter.on(PriceConstant.SLANTING_BAR).splitToList(v.getOrgPath()).stream().map(id->Long.parseLong(id)).distinct().collect(Collectors.toList()));
                });
            }

            if (CollectionUtils.isNotEmpty(finalStoreIdList)) {
                List<String> codeList = getCodeListByOrg(finalStoreIdList.stream().distinct().collect(Collectors.toList()), controlOrderType, param);
                if (CollectionUtils.isNotEmpty(codeList)) {
                    param.setControlOrderIdList(codeList);
                } else {
                    return new PageResult<>(0L, new ArrayList<>());
                }
            }

            //商品编码搜索
            if (StringUtils.isNotBlank(param.getGoodsCode())) {
                List<String> codeList = getCodeListByGoodsCode(param.getGoodsCode(), controlOrderType, param);
                if (CollectionUtils.isNotEmpty(codeList)) {
                    param.setControlOrderIdList(codeList);
                } else {
                    return new PageResult<>(0L, new ArrayList<>());
                }
            }

            if (StringUtils.isNotBlank(param.getControlOrderId())) {
                param.setControlOrderIdList(null);
            }

            long count = priceManageControlOrderExtMapper.searchControlOrderCount(param);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }
            param.setOffset((param.getPage() - 1) * param.getPageSize());
            List<PriceManageControlOrder> priceManageControlOrderList = priceManageControlOrderExtMapper.searchControlOrderList(param);

            Map<String, List<PriceManageControlOrgDetail>> controlOrgMap = ControlOrderTypeEnum.CONTROL_ORDER.getCode() == controlOrderType ? getControlOrgMapByCode(priceManageControlOrderList.stream().filter(v -> StringUtils.isNotBlank(v.getControlOrderId())).map(v -> v.getControlOrderId()).distinct().collect(Collectors.toList())) : new HashMap<>();
            Map<String, Integer> goodsCountMap = ControlOrderTypeEnum.UNCONTROL_ORDER.getCode() == controlOrderType ? getUnControlGoodsCount(priceManageControlOrderList) : new HashMap<>();

            List<ControlOrderListVO> controlOrderListVOList = priceManageControlOrderList.stream().map(v -> {
                ControlOrderListVO controlOrderListVO = new ControlOrderListVO();
                controlOrderListVO.setIsOwner(isMyself(v.getCreatedBy(), userDTO.getUserId()));
                //获取组织机构数量和ID集合
                if (ControlOrderTypeEnum.CONTROL_ORDER.getCode() == controlOrderType) {
                    boolean orgResult = getControlOrgCountAndList(controlOrderListVO, controlOrgMap.get(v.getControlOrderId()), finalStoreIdList, orgLevelVOList);
                    if (!orgResult) {
                        return null;
                    }
                }
                BeanUtils.copyProperties(v, controlOrderListVO);
                controlOrderListVO.setAuditStatusDesc(AuditStatusEnum.getName(v.getAuditStatus()));
                controlOrderListVO.setChannelNameList(getNameListByCodeList(v.getChannel(), getChannelNames(v.getChannel())));
                controlOrderListVO.setPriceTypeNameList(getNameListByCodeList(v.getPriceType(), getPriceTypeNames(v.getPriceType())));
                controlOrderListVO.setGmtCreate(v.getGmtCreate().format(DateTimeFormatter.ofPattern(DateUtils.DATETIME_FORMAT)));
                controlOrderListVO.setGmtUpdate(v.getGmtUpdate().format(DateTimeFormatter.ofPattern(DateUtils.DATETIME_FORMAT)));
                controlOrderListVO.setEffectTime(Objects.nonNull(v.getEffectTime()) ? v.getEffectTime().format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMMDD)) : "");
                controlOrderListVO.setScheduledTime(Objects.nonNull(v.getScheduledTime()) ? v.getScheduledTime().format(DateTimeFormatter.ofPattern(DateUtils.DATE_FORMAT_YYYYMMDD)) : "");
                controlOrderListVO.setGoodsCount(goodsCountMap.get(v.getControlOrderId()));
                controlOrderListVO.setControlTypeName(PriceManageTypeEnum.getDescByCode(v.getControlType()));
                controlOrderListVO.setEffectStatus(v.getEffectStatus());
                controlOrderListVO.setEffectStatusName(PriceEffectStatusEnum.getName(v.getEffectStatus()));
                return controlOrderListVO;
            }).filter(v -> Objects.nonNull(v)).collect(Collectors.toList());

            return new PageResult(count, controlOrderListVOList);
        } catch (Exception e) {
            LOGGER.error("获取价格管控单/不执行管控申请单列表异常", e);
            throw e;
        }
    }

    /**
     * 组织数量
     * @param controlOrderListVO
     * @param priceManageControlOrgDetailList
     */
    private boolean getControlOrgCountAndList(ControlOrderListVO controlOrderListVO, List<PriceManageControlOrgDetail> priceManageControlOrgDetailList, List<Long> userOrgIdList, List<OrgDTO> orgLevelVOList) {
        if (CollectionUtils.isEmpty(priceManageControlOrgDetailList)) {
            return false;
        }

        if(controlOrderListVO.getIsOwner()){
            controlOrderListVO.setOrgCount(priceManageControlOrgDetailList.size());
            controlOrderListVO.setOrgIdList(priceManageControlOrgDetailList.stream().map(v->v.getOrgId()).distinct().collect(Collectors.toList()));
        }else{
            List<Long> finalOrgIdlist = orgLevelVOList.stream().map(v->v.getId()).distinct().collect(Collectors.toList());
            //取交集
            List<Long> viewOrgIdList = priceManageControlOrgDetailList.stream().filter(v->finalOrgIdlist.contains(v.getOrgId())).map(v->v.getOrgId()).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(viewOrgIdList)) {
                //如果没有交集
                orgLevelVOList.stream().filter(v->StringUtils.isNotBlank(v.getOrgPath())).forEach(v->{
                    for(PriceManageControlOrgDetail detail : priceManageControlOrgDetailList)
                        if(v.getOrgPath().contains(String.valueOf(detail.getOrgId()))){
                            viewOrgIdList.add(v.getId());
                            break;
                        }
                });
                if(CollectionUtils.isEmpty(viewOrgIdList)){
                    return false;
                }
            }
            controlOrderListVO.setOrgCount(viewOrgIdList.size());
            controlOrderListVO.setOrgIdList(viewOrgIdList);
        }

        return true;
    }

    /**
     * 通过管控单查询审核日志，执行管控单不查
     * @param controlOrder
     * @return
     */
    private String getAuditRegectedLogByControlCode(PriceManageControlOrder controlOrder) {
        if (ControlOrderTypeEnum.CONTROL_ORDER.getCode() == controlOrder.getControlOrderType() || org.apache.commons.lang3.StringUtils.isBlank(controlOrder.getControlOrderId())) {
            LOGGER.info("执行管控单不查审核信息");
            return null;
        }

        PriceOrderOperatorLogExample logExample = new PriceOrderOperatorLogExample();
        logExample.setOrderByClause(" gmt_update desc");
        logExample.setLimit(1);
        logExample.createCriteria().andOrderIdEqualTo(controlOrder.getControlOrderId()).andPriceOrderTypeEqualTo(PriceOrderTypeEnum.CONTROL_NON_EXEC.getCode());
        List<PriceOrderOperatorLog> priceOrderOperatorLogs = priceOrderOperatorLogMapper.selectByExample(logExample);
        if (CollectionUtils.isEmpty(priceOrderOperatorLogs)) {
            return null;
        }
        PriceOrderOperatorLog priceOrderOperatorLog = priceOrderOperatorLogs.get(0);
        //如果最后一条操作日志是审核驳回，就返回驳回日志
        if (AuditStatusEnum.AUDIT_REJECTED.getCode() == priceOrderOperatorLog.getOrderStatus()) {
            return priceOrderOperatorLog.getRejectReasion();
        }
        return null;
    }

    /**
     * 通过管控单查询组织信息，不执行管控单不查
     * @param controlOrder
     * @return
     */
    private List<Long> getOrgIdListByControlCode(PriceManageControlOrder controlOrder) {
        if (ControlOrderTypeEnum.UNCONTROL_ORDER.getCode() == controlOrder.getControlOrderType() || org.apache.commons.lang3.StringUtils.isBlank(controlOrder.getControlOrderId())) {
            LOGGER.info("不执行管控单不查组织信息");
            return com.google.common.collect.Lists.newArrayList();
        }
        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        orgDetailExample.createCriteria().andControlOrderCodeEqualTo(controlOrder.getControlOrderId());
        List<PriceManageControlOrgDetail> orgDetailList = priceManageControlOrgDetailMapper.selectByExample(orgDetailExample);
        if (CollectionUtils.isEmpty(orgDetailList)) {
            throw new AmisBadRequestException(ReturnCodeEnum.ERROR_ORDER_ORG_NULL);
        }
        return orgDetailList.stream().map(v -> {
            return v.getOrgId();
        }).collect(Collectors.toList());
    }

    /**
     * 通过code集合，获取渠道和价格类型名称集合
     * @param codes
     * @param typeInfoList
     * @return
     */
    protected List<String> getNameListByCodeList(String codes, List<TypeInfo<String, String>> typeInfoList) {
        if (org.apache.commons.lang3.StringUtils.isBlank(codes)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        Map<String, TypeInfo> typeInfoMap = typeInfoList.stream().filter(v -> org.apache.commons.lang3.StringUtils.isNotBlank(v.getCode())).collect(Collectors.toMap(v -> v.getCode(), Function.identity()));
        return Arrays.asList(codes.split(",")).stream().map(code -> {
            return typeInfoMap.get(code) != null && typeInfoMap.get(code).getMessage() != null ? typeInfoMap.get(code).getMessage().toString() : "";
        }).collect(Collectors.toList());
    }

    /**
     * 去掉双引号
     * @param targetStr
     * @return
     */
    private String delQuotation(String targetStr){
        if(targetStr.contains("\"")){
            targetStr = targetStr.replace("\"","");
        }
        return targetStr;
    }

    /**
     * 通过组织ID查询管控单编码集合
     * @param orgIdList
     * @return
     */
    protected List<String> getCodeListByOrg(List<Long> orgIdList, Integer controlOrderType, ControlOrderListParam param) {
        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        PriceManageControlOrgDetailExample.Criteria criteria = orgDetailExample.createCriteria();
        criteria.andOrderTypeEqualTo((byte)1).andOrgIdIn(orgIdList);

        if(ControlOrderTypeEnum.CONTROL_ORDER.getCode() == controlOrderType){
            return priceManageControlOrgDetailMapper.selectByExample(orgDetailExample).stream().map(v -> v.getControlOrderCode()).distinct().collect(Collectors.toList());
        }else if(ControlOrderTypeEnum.UNCONTROL_ORDER.getCode() == controlOrderType){
            resetDays(param);
            List<UnControlOrderVO>  unControlOrderVOList = priceManageControlOrderExtMapper.selectUmControlOrderListByControlOrderCodeKey(param);
            if(CollectionUtils.isEmpty(unControlOrderVOList)){
                LOGGER.info("getCodeListByOrg|不管控单信息为空");
                return Lists.newArrayList();
            }
            List<String> targetControlOrderIdList = priceManageControlOrgDetailMapper.selectByExample(orgDetailExample).stream().map(v -> v.getControlOrderCode()).distinct().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(targetControlOrderIdList)){
                LOGGER.info("getCodeListByOrg|该组织无对应的管控单明细");
                return Lists.newArrayList();
            }
            return unControlOrderVOList.stream().filter(v->StringUtils.isNotBlank(v.getControlOrderId())).filter(v->targetControlOrderIdList.contains(delQuotation(v.getControlOrderId()))).map(v->v.getUnControlOrderId()).distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 通过商品编码查询管控单编码集合
     * @param goodsCode
     * @return
     */
    private List<String> getCodeListByGoodsCode(String goodsCode, Integer controlOrderType, ControlOrderListParam param) {
        PriceManageControlOrderDetailExample orderDetailExample = new PriceManageControlOrderDetailExample();
        PriceManageControlOrderDetailExample.Criteria orderDetailCriteria = orderDetailExample.createCriteria();
        orderDetailCriteria.andOrderTypeEqualTo((byte)1).andGoodsNoEqualTo(goodsCode);

        if(ControlOrderTypeEnum.CONTROL_ORDER.getCode() == controlOrderType){
            return priceManageControlOrderDetailMapper.selectByExample(orderDetailExample).stream().map(v -> v.getControlOrderCode()).distinct().collect(Collectors.toList());
        }else if(ControlOrderTypeEnum.UNCONTROL_ORDER.getCode() == controlOrderType){
            resetDays(param);
            List<UnControlOrderVO>  unControlOrderVOList = priceManageControlOrderExtMapper.selectUmControlOrderListByControlOrderCodeKey(param);
            if(CollectionUtils.isEmpty(unControlOrderVOList)){
                LOGGER.info("getCodeListByGoodsCode|不管控单信息为空");
                return Lists.newArrayList();
            }
            List<String> targetControlOrderIdList = priceManageControlOrderDetailMapper.selectByExample(orderDetailExample).stream().map(v -> v.getControlOrderCode()).distinct().collect(Collectors.toList());
            if(CollectionUtils.isEmpty(targetControlOrderIdList)){
                LOGGER.info("getCodeListByGoodsCode|该组织无对应的管控单明细");
                return Lists.newArrayList();
            }
            return unControlOrderVOList.stream().filter(v->StringUtils.isNotBlank(v.getControlOrderId())).filter(v->targetControlOrderIdList.contains(delQuotation(v.getControlOrderId()))).map(v->v.getUnControlOrderId()).distinct().collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    /**
     * 回滚天数
     * @param param
     */
    private void resetDays(ControlOrderListParam param) {
        if(param.getStartTime()==null || param.getEndTime()==null){
            LocalDateTime localDateTime = LocalDateTime.now();
            localDateTime = localDateTime.minusDays(notcontrolRollDays);
            param.setStartTime(localDateTime);
            param.setEndTime(LocalDateTime.now());
        }

    }

    /**
     * 获取管控单组织机构ID和数量
     * @param controlOrderCodeList
     */
    protected Map<String, List<PriceManageControlOrgDetail>> getControlOrgMapByCode(List<String> controlOrderCodeList) {
        List<PriceManageControlOrgDetail> controlOrgDetailList = priceManageControlOrgDetailExtMapper.selectOrgByControlOrderCodeList(controlOrderCodeList);
        if (CollectionUtils.isEmpty(controlOrgDetailList)) {
            return new HashMap<>();
        }
        return controlOrgDetailList.stream().filter(v -> org.apache.commons.lang.StringUtils.isNotBlank(v.getControlOrderCode())).collect(Collectors.groupingBy(PriceManageControlOrgDetail::getControlOrderCode));
    }

    /**
     * 获取不执行管控单对应的商品数量
     * @param priceManageControlOrderList
     * @return
     */
    protected Map<String, Integer> getUnControlGoodsCount(List<PriceManageControlOrder> priceManageControlOrderList) {
        Map<String, Integer> resultMap = new HashMap<>();
        priceManageControlOrderList.stream().forEach(unOrder -> {
            Optional<ControlExtendVO> controlExtendVOOptional = ControlExtendVO.getInstance(unOrder.getExtend());
            if (!controlExtendVOOptional.isPresent()) {
                return;
            }
            String noticeIds = controlExtendVOOptional.get().getNoticeIds();
            if (StringUtils.isBlank(noticeIds)) {
                return;
            }
            List<Long> noticeIdList = Arrays.asList(noticeIds.split(",")).stream().filter(v -> StringUtils.isNotBlank(v)).map(v -> Long.parseLong(v)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(noticeIdList)) {
                return;
            }
            PriceControlNoticeExample noticeExample = new PriceControlNoticeExample();
            noticeExample.createCriteria().andIdIn(noticeIdList);
            Integer goodsCount = (int) priceControlNoticeMapper.selectByExample(noticeExample).stream().filter(v -> StringUtils.isNotBlank(v.getGoodsNo())).map(v -> v.getGoodsNo()).distinct().count();
            resultMap.put(unOrder.getControlOrderId(), goodsCount);
        });
        return resultMap;
    }

    /**
     * 根据channelId列表获取channelName列表
     * @param channels
     * @return
     */
    private List<TypeInfo<String, String>> getChannelNames(String channels) {
        if (StringUtils.isBlank(channels)) {
            return Lists.newArrayList();
        }
        return priceChannelService.getPriceChannelListByChannelIds(Arrays.asList(channels.split(",")).stream().map(v -> Integer.parseInt(v)).collect(Collectors.toList())).stream().
            map(v -> {
                TypeInfo<String, String> typeInfo = new TypeInfo<>();
                typeInfo.setCode(v.getChannelId().toString());
                typeInfo.setMessage(v.getChannelName());
                return typeInfo;
            }).collect(Collectors.toList());
    }

    /**
     * 根据价格类型Code列表获取价格类型名称列表
     * @param priceTypeCodes
     * @return
     */
    private List<TypeInfo<String, String>> getPriceTypeNames(String priceTypeCodes) {
        if (StringUtils.isBlank(priceTypeCodes)) {
            return Lists.newArrayList();
        }
        return priceTypeService.getPriceTypeListByCodes(Arrays.asList(priceTypeCodes.split(","))).stream().
            map(v -> {
                TypeInfo<String, String> typeInfo = new TypeInfo<>();
                typeInfo.setCode(v.getCode().toString());
                typeInfo.setMessage(v.getName());
                return typeInfo;
            }).collect(Collectors.toList());
    }


    /**
     * 判断制单人和登陆人是否同一人
     * @param createdBy
     * @param userId
     * @return
     */
    private Boolean isMyself(Long createdBy, Long userId) {
        if (Objects.isNull(createdBy) || Objects.isNull(userId)) {
            return false;
        }
        if (createdBy.equals(userId)) {
            return true;
        }
        return false;
    }

    /**
     * 获取每个品的最小颗粒度的上下限
     * @param list
     * @return
     */
    protected List<ControlOrderLimitDTO> getUpperAndLowerLimit(List<ControlOrderLimitDTO> list) {
        List<ControlOrderLimitDTO> resultList = new ArrayList<>();
        Map<String, List<ControlOrderLimitDTO>> map = list.stream().collect(Collectors.groupingBy(ControlOrderLimitDTO::getUniqueGoodsNo));
        for (Map.Entry<String, List<ControlOrderLimitDTO>> entry : map.entrySet()) {
            List<ControlOrderLimitDTO> value = entry.getValue();
            if (value.size() <= 1) {
                resultList.add(value.get(0));
                continue;
            }
            //处理某渠道多条记录的情况
            Map<String, List<ControlOrderLimitDTO>> tempMap = value.stream().collect(Collectors.groupingBy(ControlOrderLimitDTO::getTempUniqueGoodsNo));
            value.clear();
            for (List<ControlOrderLimitDTO> limitDTOList : tempMap.values()) {
                ControlOrderLimitDTO dto = limitDTOList.stream().max((a, b) -> {
                    return a.getGmtUpdate().compareTo(b.getGmtUpdate());
                }).get();
                value.add(dto);
            }
            //取上限最小值,下限最大值
            BigDecimal upperLimit = value.stream().map(ControlOrderLimitDTO::getUpperLimit).min((a, b) -> Double.compare(a.doubleValue(), b.doubleValue())).get();
            BigDecimal lowerLimit = value.stream().map(ControlOrderLimitDTO::getLowerLimit).max((a, b) -> Double.compare(a.doubleValue(), b.doubleValue())).get();
            ControlOrderLimitDTO temp;
            if (upperLimit.compareTo(lowerLimit) == 1 || upperLimit.compareTo(lowerLimit) == 0) {
                //正常上下限范围
                temp = value.get(0);
                temp.setUpperLimit(upperLimit);
                temp.setLowerLimit(lowerLimit);
            } else {
                //非正常上下限范围,取上限最大的范围
                temp = value.stream().max((a, b) -> Double.compare(a.getUpperLimit().doubleValue(), b.getUpperLimit().doubleValue())).get();
            }
            resultList.add(temp);
        }
        return resultList;
    }

    /**
     * 管控通知类型转换
     * @param priceControlNotice
     * @return
     */
    protected ControlOrderNoticeListVO controlNoticeListCommon(PriceControlNotice priceControlNotice, Map<Integer, PriceChannel> priceChannelMap, Map<String, PriceManageControlOrder> controlOrderMap) {
        ControlOrderNoticeListVO noticeListDTO = new ControlOrderNoticeListVO();
        BeanUtils.copyProperties(priceControlNotice, noticeListDTO);
        noticeListDTO.setNoticeId(priceControlNotice.getId());
        noticeListDTO.setStatusName(PriceNoticeStatusEnum.getEnumByCode(priceControlNotice.getStatus()).getMessage());
        noticeListDTO.setUpperLimit(PriceUtil.getYuanStrFromFenWithNull(priceControlNotice.getUpperLimit()));
        noticeListDTO.setLowerLimit(PriceUtil.getYuanStrFromFenWithNull(priceControlNotice.getLowerLimit()));
        noticeListDTO.setPrice(PriceUtil.getYuanStrFromFenWithNull(priceControlNotice.getPrice()));
        noticeListDTO.setGuidePrice(PriceUtil.getYuanStrFromFenWithNull(priceControlNotice.getGuidePrice()));
        noticeListDTO.setDxsDate(Objects.nonNull(priceControlNotice.getDxsDate()) ? priceControlNotice.getDxsDate().format(formatter) : null);
        noticeListDTO.setBackColour(createBackColour(priceControlNotice));
        noticeListDTO.setChannelName(Objects.nonNull(priceChannelMap.get(priceControlNotice.getChannelId())) ? priceChannelMap.get(priceControlNotice.getChannelId()).getChannelName() : "");
        noticeListDTO.setIsOperate(judgeIsOperate(priceControlNotice, controlOrderMap));
        return noticeListDTO;
    }

    /**
     * 判断是否有操作按钮
     * @param priceControlNotice
     * @param controlOrderMap
     * @return
     */
    private Boolean judgeIsOperate(PriceControlNotice priceControlNotice, Map<String, PriceManageControlOrder> controlOrderMap) {
        if(controlOrderMap.get(priceControlNotice.getControlOrderId()) == null){
            LOGGER.info("judgeIsOperate|没有对应的状态的管控单");
            return false;
        }
        PriceManageControlOrder priceManageControlOrder = controlOrderMap.get(priceControlNotice.getControlOrderId());
        return String.valueOf(PriceEffectStatusEnum.UN_EFFECT.getCode()).equals(priceManageControlOrder.getEffectStatus())
                && (PriceNoticeStatusEnum.UN_DEAL.getCode().equals(priceControlNotice.getStatus()) || PriceNoticeStatusEnum.REJECTED_DEAL.getCode().equals(priceControlNotice.getStatus()));
    }

    /**
     * 计算背景色标识
     * @param priceControlNotice
     * @return
     */
    private String createBackColour(PriceControlNotice priceControlNotice) {
        if (Objects.isNull(priceControlNotice.getDxsDate())) {
            return PriceNoticeColourEnum.COLOUR_OTHER.getCode();
        }
        long daysCha = Duration.between(priceControlNotice.getDxsDate(), LocalDateTime.now()).toDays();
        LOGGER.info("createBackColour|daysCha:{}.", daysCha);
        return PriceNoticeColourEnum.getBackColourCode(daysCha);
    }

    /**
     * 获取单据里的组织信息
     * @param controlOrderId
     * @return
     */
    private List<OrgLevelVO> getControlOrgInfo(Long controlOrderId) {
        PriceManageControlOrder priceManageControlOrder = priceManageControlOrderMapper.selectByPrimaryKey(controlOrderId);
        PriceManageControlOrgDetailExample orgDetailExample = new PriceManageControlOrgDetailExample();
        orgDetailExample.createCriteria().andControlOrderCodeEqualTo(priceManageControlOrder.getControlOrderId());
        List<PriceManageControlOrgDetail> orgDetailList = controlOrgDetailMapper.selectByExample(orgDetailExample);
        if (CollectionUtils.isEmpty(orgDetailList)) {
            return com.google.common.collect.Lists.newArrayList();
        }

        return orgDetailList.stream().map(v -> {
            OrgLevelVO orgLevelVO = new OrgLevelVO();
            orgLevelVO.setOrgId(v.getOrgId());
            orgLevelVO.setOrgLevel(v.getOrgLevel());
            orgLevelVO.setOrgName(v.getOrgName());
            orgLevelVO.setName(v.getOrgName());
            return orgLevelVO;
        }).collect(Collectors.toList());
    }

    /**
     * 转换通知列表
     * @param priceControlNoticeList
     * @return
     */
    private List<ControlOrderNoticeListVO> noticeVOList(List<PriceControlNotice> priceControlNoticeList) {
        if (CollectionUtils.isEmpty(priceControlNoticeList)) {
            return Lists.newArrayList();
        }

        List<Integer> channelIdList = priceControlNoticeList.stream().filter(v -> Objects.nonNull(v.getChannelId())).map(v -> v.getChannelId()).distinct().collect(Collectors.toList());
        Map<Integer, PriceChannel> priceChannelMap = priceChannelService.getPriceChannelListByChannelIds(channelIdList).stream().filter(v -> Objects.nonNull(v)).collect(Collectors.toMap(PriceChannel::getChannelId, Function.identity()));
        Map<String, PriceManageControlOrder> controlOrderMap = basePriceOrderService.getEffectStatusAndControlOrder(priceControlNoticeList.stream().map(v->v.getControlOrderId()).distinct().collect(Collectors.toList()));
        List<ControlOrderNoticeListVO> noticeListDTOList = priceControlNoticeList.stream().map(v -> {
            return controlNoticeListCommon(v, priceChannelMap, controlOrderMap);
        }).collect(Collectors.toList());
        return noticeListDTOList;
    }

    /**
     * 消息通知对象
     * @return
     */
    private ZDTMsgPushDTO messageNoticeInstance() {
        return JSON.parseObject(controlMessageNoticeProp, ZDTMsgPushDTO.class);
    }

    /**
     * 待办事项对象
     * @return
     */
    private BackLogCreateParam backLogInstance() {
        return JSON.parseObject(controlBacklogNoticeProp, BackLogCreateParam.class);
    }

	@Override
	public List<OptionDto> getAdjustTypeList() {
		return permissionExtService.getAdjustTypeList().stream().map(v -> {
            return new OptionDto(v.getMessage(), v.getCode());
        }).collect(Collectors.toList());
	}

	@Override
	public PageResult controlOrderList(ControlOrderListParam param) {
		if(CollectionUtils.isEmpty(param.getRuleCodeList())) {
			return new PageResult<>(0L, new ArrayList<>());
		}
		PriceManageControlOrderExample example = new PriceManageControlOrderExample();
		example.createCriteria().andControlOrderIdIn(param.getRuleCodeList());
		long count = priceManageControlOrderMapper.countByExample(example);
		if(count<1) {
			return new PageResult<>(0L, new ArrayList<>());
		}
		List<PriceManageControlOrder> controlList = priceManageControlOrderMapper.selectByExample(example);
		if(CollectionUtils.isEmpty(controlList)) {
			return new PageResult<>(0L, new ArrayList<>());
		}
		return new PageResult(count, controlList);
	}
}

