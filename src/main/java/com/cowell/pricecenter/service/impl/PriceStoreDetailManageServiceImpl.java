package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.PriceDetailHistory;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.entity.PriceStoreDetailExample;
import com.cowell.pricecenter.mapper.PriceStoreDetailMapper;
import com.cowell.pricecenter.mq.producer.PriceDetailHistoryProducer;
import com.cowell.pricecenter.security.dto.TokenUserDTO;
import com.cowell.pricecenter.service.PriceStoreDetailManageService;
import com.cowell.pricecenter.service.dto.PriceStoreDetailDeleteParam;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 3/18/21 2:15 PM
 */
@Service
public class PriceStoreDetailManageServiceImpl implements PriceStoreDetailManageService {


    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;
    @Autowired
    private PriceDetailHistoryProducer priceDetailHistoryProducer;

    private final Logger logger = LoggerFactory.getLogger(PriceStoreDetailManageServiceImpl.class);

    @Override
    public void deletePrice(List<PriceStoreDetailDeleteParam> params, TokenUserDTO tokenUser) {

        if (CollectionUtils.isEmpty(params)){
            logger.info("需要删除的商品价格为空");
            return;
        }

        //校验参数
        validParam(params);

        params.forEach(item->{
            PriceStoreDetailExample example = new PriceStoreDetailExample();

            PriceStoreDetailExample.Criteria criteria = example.createCriteria();
            criteria.andGoodsNoEqualTo(item.getGoodsNo());
            criteria.andStoreIdEqualTo(item.getStoreId());
            criteria.andPriceTypeCodeEqualTo(item.getPriceTypeCode());

            int result = priceStoreDetailMapper.deleteByExample(example);
            logger.info("删除商品价格结果:{}",result);

            recordHistory(item,tokenUser);

        });
    }


    private void recordHistory(PriceStoreDetailDeleteParam param,TokenUserDTO tokenUser){
        PriceDetailHistory history = new PriceDetailHistory();
        history.setBusinessId(tokenUser.getBusinessId());
        history.setStoreId(param.getStoreId());
        history.setGoodsNo(param.getGoodsNo());
        history.setAdjustCode("DEL_"+ DateUtils.getTodayYYYYMMDDHHmmss());
        history.setPriceTypeCode(param.getPriceTypeCode());
        history.setPriceTypeName("DEL_"+param.getPriceTypeCode());
        history.setPrice(new BigDecimal(0));
        history.setUpdatedBy(tokenUser.getUserId());
        history.setUpdatedByName(tokenUser.getUserName());
        history.setExtend("连锁人员操作价格删除");
        history.setVersion(99999);
        history.setId(null);
        priceDetailHistoryProducer.sendMq(history);
    }


    private void validParam(List<PriceStoreDetailDeleteParam> params){
        params.forEach(item->{
            if (item.getStoreId() == null){
                throw new BusinessErrorException("需要删除的门店ID不可以为空");
            }
            if (StringUtils.isEmpty(item.getGoodsNo())){
                throw new BusinessErrorException("需要删除的商品不可以为空");
            }
            if (StringUtils.isEmpty(item.getPriceTypeCode())){
                throw new BusinessErrorException("需要删除的商品价格类型不可以为空");
            }
        });

    }

    @Override
    public List<PriceStoreDetail> getPriceByGoodsNo(PriceStoreDetailDeleteParam param) {
        return priceStoreDetailMapper.getPriceByGoodsNo(param);
    }
}
