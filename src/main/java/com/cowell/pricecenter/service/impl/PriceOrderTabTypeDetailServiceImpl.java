package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.google.common.base.Splitter;
import java.util.Collections;
import java.util.Optional;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import com.cowell.pricecenter.entity.PriceOrderTabTypeDetail;
import java.util.List;
import com.cowell.pricecenter.mapper.PriceOrderTabTypeDetailMapper;
import com.cowell.pricecenter.entity.PriceOrderTabTypeDetailExample;
import com.cowell.pricecenter.service.PriceOrderTabTypeDetailService;
/**
 * <AUTHOR>
 * @date  2022/3/18 10:55
 */

@Service
public class PriceOrderTabTypeDetailServiceImpl implements PriceOrderTabTypeDetailService{

    private final Logger logger = LoggerFactory.getLogger(PriceOrderTabTypeDetailServiceImpl.class);

    @Resource
    private PriceOrderTabTypeDetailMapper priceOrderTabTypeDetailMapper;

    @Override
    public long countByExample(PriceOrderTabTypeDetailExample example) {
        return priceOrderTabTypeDetailMapper.countByExample(example);
    }

    @Override
    public int deleteByExample(PriceOrderTabTypeDetailExample example) {
        return priceOrderTabTypeDetailMapper.deleteByExample(example);
    }

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return priceOrderTabTypeDetailMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PriceOrderTabTypeDetail record) {
        return priceOrderTabTypeDetailMapper.insert(record);
    }

    @Override
    public int insertSelective(PriceOrderTabTypeDetail record) {
        return priceOrderTabTypeDetailMapper.insertSelective(record);
    }

    @Override
    public List<PriceOrderTabTypeDetail> selectByExample(PriceOrderTabTypeDetailExample example) {
        return priceOrderTabTypeDetailMapper.selectByExample(example);
    }

    @Override
    public PriceOrderTabTypeDetail selectByPrimaryKey(Integer id) {
        return priceOrderTabTypeDetailMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByExampleSelective(PriceOrderTabTypeDetail record,PriceOrderTabTypeDetailExample example) {
        return priceOrderTabTypeDetailMapper.updateByExampleSelective(record,example);
    }

    @Override
    public int updateByExample(PriceOrderTabTypeDetail record,PriceOrderTabTypeDetailExample example) {
        return priceOrderTabTypeDetailMapper.updateByExample(record,example);
    }

    @Override
    public int updateByPrimaryKeySelective(PriceOrderTabTypeDetail record) {
        return priceOrderTabTypeDetailMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PriceOrderTabTypeDetail record) {
        return priceOrderTabTypeDetailMapper.updateByPrimaryKey(record);
    }

    @Override
    public void deleteByAdjustCode(String adjustCode) {
        if (StringUtils.isEmpty(adjustCode)) {
            logger.error("===>[PriceOrderTabTypeDetailServiceImpl.deleteByAdjustCode] adjustCode 不能为空", adjustCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        PriceOrderTabTypeDetailExample example = new PriceOrderTabTypeDetailExample();
        example.createCriteria()
            .andAdjustOrderCodeEqualTo(adjustCode);
        deleteByExample(example);
    }

    @Override
    public List<String> getAdjustPriceOrderTabTypeValues(String adjustCode) {
        if (StringUtils.isEmpty(adjustCode)) {
            logger.error("===>[PriceOrderTabTypeDetailServiceImpl.getAdjustPriceOrderTabTypeValues] adjustCode 不能为空", adjustCode);
            throw new AmisBadRequestException(ReturnCodeEnum.PARAM_ERROR);
        }
        List<String> results = Collections.emptyList();
        PriceOrderTabTypeDetailExample priceOrderTabTypeDetailExample = new PriceOrderTabTypeDetailExample();
        priceOrderTabTypeDetailExample.createCriteria().andAdjustOrderCodeEqualTo(adjustCode);
        List<PriceOrderTabTypeDetail> priceOrderTabTypeDetailList = priceOrderTabTypeDetailMapper.selectByExample(priceOrderTabTypeDetailExample);
        if (CollectionUtils.isNotEmpty(priceOrderTabTypeDetailList)) {
            PriceOrderTabTypeDetail priceOrderTabTypeDetail = priceOrderTabTypeDetailList.get(0);
            String tableTypeValues = priceOrderTabTypeDetail.getTabTypeValue();
            results = Splitter.on(",").splitToList(tableTypeValues);
        }
        return results;
    }

}
