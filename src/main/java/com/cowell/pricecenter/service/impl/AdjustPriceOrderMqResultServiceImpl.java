/**
* @mbg.generated
* generator on Fri Mar 18 15:58:59 CST 2022
*/
package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.entity.AdjustPriceOrderMqResult;
import com.cowell.pricecenter.entity.AdjustPriceOrderMqResultExample;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMqResultMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderMqResultExtMapper;
import com.cowell.pricecenter.service.AdjustPriceOrderMqResultService;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class AdjustPriceOrderMqResultServiceImpl implements AdjustPriceOrderMqResultService {

    @Resource
    private AdjustPriceOrderMqResultMapper adjustPriceOrderMqResultMapper;

    @Resource
    private AdjustPriceOrderMqResultExtMapper adjustPriceOrderMqResultExtMapper;
    /**
    * insert
    * @param row row
    * @return int int
    */
    @Override
    public int insert(AdjustPriceOrderMqResult row) {
        return adjustPriceOrderMqResultMapper.insert(row);
    }

    /**
    * insertSelective
    * @param row row
    * @return int int
    */
    @Override
    public int insertSelective(AdjustPriceOrderMqResult row) {
        return adjustPriceOrderMqResultMapper.insertSelective(row);
    }

    @Override
    public int updateByExampleSelective(AdjustPriceOrderMqResult row, AdjustPriceOrderMqResultExample example) {
        return adjustPriceOrderMqResultMapper.updateByExampleSelective(row, example);
    }

    @Override
    public long countByExample(AdjustPriceOrderMqResultExample example) {
        return adjustPriceOrderMqResultMapper.countByExample(example);
    }

    @Override
    public List<Long> selectDistinctBusinessIdByAdjustCode(String adjustCode) {
        return adjustPriceOrderMqResultExtMapper.selectDistinctBusinessIdByAdjustCode(adjustCode);
    }

    @Override
    public int deleteByExample(AdjustPriceOrderMqResultExample example) {
        return adjustPriceOrderMqResultMapper.deleteByExample(example);
    }

    @Override
    public int deleteByAdjustCode(String adjustCode) {
        AdjustPriceOrderMqResultExample deleteExample = new AdjustPriceOrderMqResultExample();
        deleteExample.createCriteria()
            .andAdjustCodeEqualTo(adjustCode);
        return adjustPriceOrderMqResultMapper.deleteByExample(deleteExample);
    }

    @Override
    public AdjustPriceOrderMqResult selectByAdjustAndStoreId(String adjustCode, Long storeId) {
        AdjustPriceOrderMqResultExample example = new AdjustPriceOrderMqResultExample();
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode)
            .andStoreIdEqualTo(storeId);
        List<AdjustPriceOrderMqResult> list = adjustPriceOrderMqResultMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().findFirst().orElse(null);
    }

    @Override
    public List<String> selectDistinctAdjustCodeByGmtCreate(Date gmtCreate) {
        return adjustPriceOrderMqResultExtMapper.selectDistinctAdjustCodeByGmtCreate(gmtCreate);
    }
}
