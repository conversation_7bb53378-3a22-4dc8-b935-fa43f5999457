/**
* @mbg.generated
* generator on Fri Mar 18 13:53:27 CST 2022
*/
package com.cowell.pricecenter.service.impl;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.PriceChangeNotice;
import com.cowell.pricecenter.entity.PriceChangeNoticeExample;
import com.cowell.pricecenter.enums.PriceChangeNoticeEnum;
import com.cowell.pricecenter.mapper.PriceChangeNoticeMapper;
import com.cowell.pricecenter.service.PriceChangeNoticeService;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;

import java.util.List;
import java.util.Objects;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PriceChangeNoticeServiceImpl implements PriceChangeNoticeService {

    @Resource
    private PriceChangeNoticeMapper priceChangeNoticeMapper;
    
    /**
     * 已配置的调价类型，无需提交OA审核，默认自动审核通过 且 不生成价格调整通知
     */
    @ApolloJsonValue("${price.adjust.adjustType.autoaudit:[]}")
    private List<Integer> adjustTypeAutoAudits;

    /**
    * deleteByPrimaryKey
    * @param id id
    * @return int int
    */
    @Override
    public int deleteByPrimaryKey(Long id) {
        return priceChangeNoticeMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<PriceChangeNotice> selectByExample(PriceChangeNoticeExample example) {
        return priceChangeNoticeMapper.selectByExample(example);
    }

    @Override
    public long countByExample(PriceChangeNoticeExample example) {
        return priceChangeNoticeMapper.countByExample(example);
    }

    /**
    * insert
    * @param row row
    * @return int int
    */
    @Override
    public int insert(PriceChangeNotice row) {
        return priceChangeNoticeMapper.insert(row);
    }

    /**
    * insertSelective
    * @param row row
    * @return int int
    */
    @Override
    public int insertSelective(PriceChangeNotice row) {
        return priceChangeNoticeMapper.insertSelective(row);
    }

    /**
    * selectByPrimaryKey
    * @param id id
    * @return PriceChangeNotice PriceChangeNotice
    */
    @Override
    public PriceChangeNotice selectByPrimaryKey(Long id) {
        return priceChangeNoticeMapper.selectByPrimaryKey(id);
    }

    /**
    * updateByPrimaryKeySelective
    * @param row row
    * @return int int
    */
    @Override
    public int updateByPrimaryKeySelective(PriceChangeNotice row) {
        return priceChangeNoticeMapper.updateByPrimaryKeySelective(row);
    }

    /**
    * updateByPrimaryKey
    * @param row row
    * @return int int
    */
    @Override
    public int updateByPrimaryKey(PriceChangeNotice row) {
        return priceChangeNoticeMapper.updateByPrimaryKey(row);
    }

    @Override
    public void savePriceChangeNotice(AdjustPriceOrder adjustPriceOrder,
                                      Long storeId,
                                      OrgDTO storeOrgDTO,
                                      OrgDTO businessOrgDTO,
                                      OrgDTO platformOrgDTO,
                                      PriceChangeNoticeEnum.NoticeType noticeType) {
    	//根据调价单类型验证匹配config类型的调价单不生成价格变动通知
    	if(null != adjustPriceOrder.getAdjustType() && adjustTypeAutoAudits.contains(adjustPriceOrder.getAdjustType().intValue())) {
    		return;
    	}
        // 保存门店价格变动通知
        PriceChangeNotice priceChangeNotice = PriceChangeNotice.builder()
            .adjustCode(adjustPriceOrder.getAdjustCode())
            .adjustName(adjustPriceOrder.getAdjustName())
            .adjustPriceType(adjustPriceOrder.getAdjustPriceType())
            .channel(adjustPriceOrder.getChannel())
            .adjustPriceVersion(adjustPriceOrder.getAdjustPriceVersion())
            .storeId(storeId)
            .businessId(-1L)
            .adjustCreatedBy(adjustPriceOrder.getCreatedBy())
            .adjustCreatedByName(adjustPriceOrder.getCreatedByName())
            .adjustAuditBy(adjustPriceOrder.getAuditBy())
            .adjustAuditByName(adjustPriceOrder.getAuditByName())
            .adjustEffectTime(adjustPriceOrder.getEffectTime())
            .reason(adjustPriceOrder.getReason())
            .noticeType(noticeType.getCode())
            .build();

        if (Objects.nonNull(storeOrgDTO)) {
            priceChangeNotice.setStoreName(storeOrgDTO.getShortName());
        }
        if (Objects.nonNull(platformOrgDTO)) {
            priceChangeNotice.setPlatformId(platformOrgDTO.getOutId());
            priceChangeNotice.setPlatformName(platformOrgDTO.getShortName());
        }
        if (Objects.nonNull(businessOrgDTO)) {
            priceChangeNotice.setBusinessId(businessOrgDTO.getOutId());
            priceChangeNotice.setBusinessName(businessOrgDTO.getShortName());
        }
        insertSelective(priceChangeNotice);
    }
}
