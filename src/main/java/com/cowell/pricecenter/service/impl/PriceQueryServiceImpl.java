package com.cowell.pricecenter.service.impl;

import com.cowell.permission.dto.ChildOrgsDTO;
import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.UserRoleRelateDTO;
import com.cowell.pricecenter.cache.CacheVar;
import com.cowell.pricecenter.config.sharding.DatabaseTableEntity;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.PriceDetailHistoryMapper;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.*;
import com.cowell.pricecenter.service.dto.MdmStoreBaseDTO;
import com.cowell.pricecenter.service.dto.OrgToRedisDTO;
import com.cowell.pricecenter.service.dto.StoreToRedisDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import com.cowell.pricecenter.service.dto.response.StoreGoodsnoPriceDTO;
import com.cowell.pricecenter.service.dto.response.StoreGoodsnoPriceTypeColumnDTO;
import com.cowell.pricecenter.service.dto.response.amis.ColumnVO;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.priceQuery.GoodsDTO;
import com.cowell.pricecenter.service.dto.response.priceQuery.PriceHistoryDTO;
import com.cowell.pricecenter.service.dto.response.priceQuery.PriceHistoryLineChart;
import com.cowell.pricecenter.service.feign.MarketingService;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.service.feign.facade.NyuwaErpeFacadeService;
import com.cowell.pricecenter.service.feign.vo.*;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;
import com.cowell.pricecenter.utils.FenkuUtil;
import com.cowell.pricecenter.utils.Md5Utils;
import com.cowell.pricecenter.utils.PriceUtil;
import com.cowell.pricecenter.web.rest.errors.AmisBadRequestException;
import com.cowell.pricecenter.web.rest.errors.AmisBusinessException;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.annotation.NewSpan;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by schuangxigang on 2022/4/11 15:56.
 */
@Service
public class  PriceQueryServiceImpl implements PriceQueryService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private PriceDetailHistoryMapper priceDetailHistoryMapper;
    @Autowired
    private AdjustPriceOrderMapper adjustPriceOrderMapper;
    @Autowired
    private PriceChannelService priceChannelService;
    @Autowired
    private IPriceTypeService priceTypeService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private ISearchExtService searchExtService;

    @Autowired
    private IPermissionExtService permissionExtService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    @Autowired
    private IBasePriceOrderService basePriceOrderService;

    @Autowired
    private MarketingService marketingService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private AsyncTaskExecutor goodsPriceQueryThreadExecutor;

    @Autowired
    private PriceManageControlOrderReadService priceManageControlOrderReadService;

    @Autowired
    private NyuwaErpeFacadeService nyuwaErpeFacadeService;

    @Value("${price.goods.more.store.limit:500}")
    private int goodsMoreStoreLimit;

    private static final int GOODS_STORE_DETAIL_PAGE_SIZE = 100;

    private static final String PRICE_FLAG_COLUMN = "priceFlag";

    private static final String PRICE_FLAG_NAME_COLUMN = "priceFlagName";

    private static final String GMT_UPDATE_COLUMN = "gmtUpdate";

    @Override
    public PageResult<Map<String, Object>> pageGoodsList(GoodsQueryParam goodsQueryParam, TokenUserDTO userDTO) {
        AmisPageParam amisPageParam = AmisPageParam.createInstance(goodsQueryParam.getPage(), goodsQueryParam.getPageSize());
        PageResult<PriceStoreDetail>  priceStoreDetailPageReult = null;

        boolean isSearchOneStore = isSearchOneStore(goodsQueryParam);
        // 校验参数
        checkParam(userDTO, isSearchOneStore, goodsQueryParam);

        Map<String, PriceType> userPriceTypeMap = priceTypeService.getUserPriceTypeMap(userDTO.getUserId());
        Map<Integer, PriceChannel> userPriceChannelMap = priceChannelService.getUserChannelMap(userDTO.getUserId());

        if (!userPriceChannelMap.containsKey(goodsQueryParam.getChannelId())) {
            throw new AmisBusinessException(ReturnCodeEnum.PARAM_ERROR, "你没有当前渠道权限，请去权限系统配置。");
        }
        List<Integer> userPriceChannelIdList = Lists.newArrayList(goodsQueryParam.getChannelId());

        List<String> userPriceTypeCodeLsit = Lists.newArrayList(userPriceTypeMap.keySet());
        if (CollectionUtils.isEmpty(userPriceTypeCodeLsit)) {
            throw new AmisBusinessException(ReturnCodeEnum.PARAM_ERROR, "价格类型为空,请去权限系统配置。");
        }

        if (isSearchOneStore) {
            priceStoreDetailPageReult = searchByOneStore(goodsQueryParam.getStoreIdList().get(0), userPriceTypeCodeLsit,
                userPriceChannelIdList, goodsQueryParam.getGoodsNoList(), amisPageParam);
        } else {
            priceStoreDetailPageReult = searchByGoodsNoAndMoreStores(goodsQueryParam, userPriceTypeCodeLsit, userPriceChannelIdList, amisPageParam);
        }

        List<StoreGoodsnoPriceDTO> storeGoodsnoPriceDTOS =
            fromPriceStoreDetail(priceStoreDetailPageReult.getRows(), goodsQueryParam, userPriceTypeMap, userPriceChannelMap);
        PageResult<Map<String, Object>> pageResult = getPageResultFromStoreGoodsnoPriceDTO(goodsQueryParam.getChannelId(),userDTO.getUserId(),isSearchOneStore, priceStoreDetailPageReult.getTotal(), storeGoodsnoPriceDTOS,
            userPriceTypeMap, amisPageParam, goodsQueryParam.isDownload());
        return pageResult;
    }

    private PageResult<Map<String, Object>> getPageResultFromStoreGoodsnoPriceDTO(int channelId,Long userId, boolean isSearchOneStore, Long total, List<StoreGoodsnoPriceDTO> storeGoodsnoPriceDTOS,
            Map<String, PriceType> userPriceTypeMap, AmisPageParam amisPageParam, boolean isDownload) {
        long startTime = System.currentTimeMillis();
        logger.info("PriceQueryServiceImpl|getPageResultFromStoreGoodsnoPriceDTO|开始处理|输入参数: channelId={}, userId={}, isSearchOneStore={}, total={}, storeGoodsnoPriceDTOS.size={}, userPriceTypeMap.size={}, amisPageParam.size={}, isDownload={}",
            channelId, userId, isSearchOneStore, total,
            storeGoodsnoPriceDTOS != null ? storeGoodsnoPriceDTOS.size() : 0,
            userPriceTypeMap != null ? userPriceTypeMap.size() : 0,
            amisPageParam != null ? amisPageParam.getSize() : 0,
            isDownload);

        PageResult<Map<String, Object>>  pageResult = new PageResult<>();
        pageResult.setTotal(total);

        // key为商品编码+门店storeId
        Map<String, Map<String, Object>> resultMap = new HashMap<>(amisPageParam.getSize());
        // 数据转换统计
        for (StoreGoodsnoPriceDTO storeGoodsnoPriceDTO : storeGoodsnoPriceDTOS) {
            String key = storeGoodsnoPriceDTO.getGoodsNo() + "_" + storeGoodsnoPriceDTO.getStoreId();
            Map<String, Object> result = Collections.emptyMap();
            if (resultMap.containsKey(key)) {
                // 处理已存在的记录
                result = resultMap.get(key);
                setPriceFlagName(result, storeGoodsnoPriceDTO);
            } else {
                // 处理新记录
                result = new HashMap<>();
                resultMap.put(key, result);
                for (GoodsStorePriceSearchBaseColumnEnum baseColumnEnum : GoodsStorePriceSearchBaseColumnEnum.values()) {
                    String columnName = baseColumnEnum.getColumnVO().getName();
                    String columnValue = baseColumnEnum.getAttrValue().apply(storeGoodsnoPriceDTO);
                    result.put(columnName, columnValue);
                }
                result.put("channelId", storeGoodsnoPriceDTO.getChannelId());
                result.put("storeId", storeGoodsnoPriceDTO.getStoreId());
                setPriceFlagAndUpdateTime(result, storeGoodsnoPriceDTO);
            }

            // 处理价格类型列数据
            for (GoodsStorePriceSearchPriceCodeColumnEnum priceCodeColumnEnum : GoodsStorePriceSearchPriceCodeColumnEnum.values()) {
                PriceType priceType = userPriceTypeMap.get(storeGoodsnoPriceDTO.getPriceTypeCode());
                if (priceType == null) {
                    continue;
                }
                ColumnVO columnVO = priceCodeColumnEnum.getColumnVOByPriceType(priceType);
                StoreGoodsnoPriceTypeColumnDTO priceTypeColumnDTO = priceCodeColumnEnum.getAttrValue().apply(storeGoodsnoPriceDTO);

                if (isDownload) {
                    result.put(columnVO.getName(), priceTypeColumnDTO.getPrice());
                } else {
                    result.put(columnVO.getName(), priceTypeColumnDTO);
                }
            }
        }
        // 数据补全阶段
        if (!isDownload) {
            //补全没有的价格类型数据
            resultMap = toCompleteAllPriceTypeData(resultMap, userPriceTypeMap);
        }
        // 结果组装阶段
        logger.info("PriceQueryServiceImpl|getPageResultFromStoreGoodsnoPriceDTO|开始结果组装");
        List<Map<String, Object>> rowsList = resultMap.values().stream().collect(Collectors.toList());
        pageResult.setRows(rowsList);
        List<ColumnVO> columns = getGoodsStorePriceSearchColumns(channelId, userId, userPriceTypeMap);
        pageResult.setColumns(columns);
        long resultAssemblyEnd = System.currentTimeMillis();
        long totalTime = resultAssemblyEnd - startTime;
        logger.info("PriceQueryServiceImpl|getPageResultFromStoreGoodsnoPriceDTO|方法执行完成|总耗时={}ms, 最终结果: total={}, rows.size={}, columns.size={}",
            totalTime, pageResult.getTotal(), pageResult.getRows() != null ? pageResult.getRows().size() : 0,
            pageResult.getColumns() != null ? pageResult.getColumns().size() : 0);
        return pageResult;
    }

    private static void setPriceFlagAndUpdateTime(Map<String, Object> result, StoreGoodsnoPriceDTO storeGoodsnoPriceDTO) {
        result.put(GMT_UPDATE_COLUMN, storeGoodsnoPriceDTO.getGmtUpdate());
        result.put(PRICE_FLAG_COLUMN, storeGoodsnoPriceDTO.getPriceSign());
        result.put(PRICE_FLAG_NAME_COLUMN, PriceFlagEnum.getByCode(
            storeGoodsnoPriceDTO.getPriceSign() != null ? storeGoodsnoPriceDTO.getPriceSign().intValue() : null
        ).getName());
    }

    /**
     * 获取根据商品编码、门店、渠道获取最近被更新的那条的数据的会员特价设置
     * @param result
     * @param storeGoodsnoPriceDTO
     */
    private void setPriceFlagName(Map<String, Object> result, StoreGoodsnoPriceDTO storeGoodsnoPriceDTO) {
        Date preGmtUpdate = (Date) result.get(GMT_UPDATE_COLUMN);
        if (storeGoodsnoPriceDTO.getGmtUpdate() != null) {
            if (preGmtUpdate == null || storeGoodsnoPriceDTO.getGmtUpdate().after(preGmtUpdate)) {
                setPriceFlagAndUpdateTime(result, storeGoodsnoPriceDTO);
            }
        }
    }

    /**
     * 补全所有价格类型数据，确保每个商品-门店组合都包含所有用户可访问的价格类型列
     *
     * 业务场景：
     * 在商品价格查询中，某个商品在某个门店可能只有部分价格类型的数据（如只有零售价，没有会员价），
     * 但前端表格需要显示所有用户有权限查看的价格类型列。对于缺失的价格类型，需要填充空值占位符。
     *
     * 处理逻辑：
     * 1. 遍历所有商品-门店组合的结果数据
     * 2. 对每个组合，检查是否包含所有用户可访问的价格类型列
     * 3. 对于缺失的价格类型列，添加空值占位符（显示为"-"）
     *
     * @param resultMap 商品-门店组合的结果数据Map，key为"商品编码_门店ID"，value为该组合的所有列数据
     * @param userPriceTypeMap 用户可访问的价格类型映射表，key为价格类型代码，value为价格类型对象
     * @return 补全后的结果数据Map，确保每个商品-门店组合都包含所有价格类型列
     */
    private Map<String, Map<String, Object>> toCompleteAllPriceTypeData(Map<String, Map<String, Object>> resultMap,  Map<String, PriceType> userPriceTypeMap) {
        // 获取所有用户可访问的价格类型代码集合
        Set<String> priceCodeList = userPriceTypeMap.keySet();

        // 遍历所有商品-门店组合的结果数据
        // key格式：商品编码_门店ID，如："GOODS001_12345"
        for (String key : resultMap.keySet()) {
            // 获取当前商品-门店组合的所有列数据
            Map<String, Object> result = resultMap.get(key);

            // 遍历所有用户可访问的价格类型代码
            for (String priceCode : priceCodeList) {
                // 根据价格类型代码生成对应的列名
                // 例如：priceCode="RETAIL" -> priceCodePriceKey="price_RETAIL"
                String priceCodePriceKey = GoodsStorePriceSearchPriceCodeColumnEnum.getColumnNameByPriceType(
                    GoodsStorePriceSearchPriceCodeColumnEnum.COLUMN_1.getColumnVO().getName(), priceCode);

                // 检查当前商品-门店组合是否已包含该价格类型的数据
                if (!result.containsKey(priceCodePriceKey)) {
                    // 如果不包含，则添加空值占位符
                    // StoreGoodsnoPriceTypeColumnDTO.getEmptyInstance(priceCode) 创建一个空的价格类型列对象
                    // 该对象的price字段为"-"，priceTypeCode为对应的价格类型代码
                    result.put(priceCodePriceKey, StoreGoodsnoPriceTypeColumnDTO.getEmptyInstance(priceCode));
                }
            }
        }

        // 返回补全后的结果数据Map
        // 此时每个商品-门店组合都包含了所有用户可访问的价格类型列，缺失的以"-"显示
        return resultMap;
    }

    /**
     * 获取商品门店价格查询的动态列配置
     * 根据渠道权限和用户权限动态生成表格列，包括基础信息列和价格类型列
     *
     * @param channelId 渠道ID，用于权限控制
     * @param userId 用户ID，用于权限控制
     * @param userPriceTypeMap 用户可访问的价格类型映射表
     * @return 动态列配置列表，用于前端表格展示
     */
    private List<ColumnVO> getGoodsStorePriceSearchColumns(int channelId,Long userId,Map<String, PriceType> userPriceTypeMap) {
        // 初始化列配置列表
        List<ColumnVO> columnVOList = new ArrayList<>();

        // 第一步：添加基础信息列（平台、连锁、门店、商品信息等固定列）
        // 遍历所有基础列枚举，获取固定的基础信息列配置
        for (GoodsStorePriceSearchBaseColumnEnum baseColumnEnum : GoodsStorePriceSearchBaseColumnEnum.values()) {
            columnVOList.add(baseColumnEnum.getColumnVO());
        }

        // 第二步：根据渠道和用户权限获取可显示的价格类型
        // 构建权限控制参数，用于查询当前渠道和用户可访问的价格类型
        ControlOrderStatusParam param = new ControlOrderStatusParam();
        param.setChannelId(channelId);  // 设置渠道ID进行渠道级权限控制
        param.setUserId(userId);        // 设置用户ID进行用户级权限控制
        param.setApiCode(PriceManageStatusApiEnum.PRICE_TYPE.getCode()); // 设置API代码为价格类型权限

        // 调用权限服务获取当前用户在指定渠道下可访问的价格类型选项列表
        List<OptionDto> optionDtoList = priceManageControlOrderReadService.controlSelectUnifyList(param);

        // 第三步：将权限控制结果转换为Map便于快速查找
        // 将选项列表转换为以价格类型代码为key的Map，便于后续权限验证
        Map<String, OptionDto> collectMap = null;
        if(CollectionUtils.isNotEmpty(optionDtoList)){
            // 使用Stream API转换为Map，key为价格类型代码，value为选项对象
            // (v1,v2)->v2 表示如果有重复key则使用后面的值
            collectMap = optionDtoList.stream().collect(Collectors.toMap(k -> k.getValue(), part -> part ,(v1,v2)->v2));
        }

        // 第四步：获取用户价格类型并进行排序
        // 从用户价格类型映射表中提取所有价格类型代码
        List<String> userPriceTypeList = userPriceTypeMap.keySet().stream().collect(Collectors.toList());
        // 调用基础价格订单服务对价格类型进行排序，确保列的显示顺序符合业务规则
        List<PriceType> sortedPriceTypeList = basePriceOrderService.getSortedPriceTypeList(userPriceTypeList);

        // 第五步：根据权限和排序结果动态添加价格类型列
        // 遍历排序后的价格类型列表
        for (PriceType priceType : sortedPriceTypeList) {
            // 遍历所有价格代码列枚举（通常只有一个COLUMN_1，表示价格列）
            for (GoodsStorePriceSearchPriceCodeColumnEnum priceCodeColumnEnum : GoodsStorePriceSearchPriceCodeColumnEnum.values()) {
                // 权限验证：只有在权限映射表中存在且用户有权限访问的价格类型才添加到列配置中
                if(null!=collectMap && collectMap.containsKey(priceType.getCode())){
                    // 根据价格类型生成对应的列配置并添加到列表中
                    // 列名格式通常为：价格类型名称（元），如"零售价（元）"
                    columnVOList.add(priceCodeColumnEnum.getColumnVOByPriceType(priceType));
                }
            }
        }

        // 返回完整的列配置列表，包含基础信息列和有权限的价格类型列
        return columnVOList;
    }

    /**
     * 单门店查询价格明细，按照商品编码分页
     */
    private PageResult<PriceStoreDetail> searchByOneStore(Long storeId,  List<String> userPriceTypeCodeList,
        List<Integer> userPriceChannelIdList, List<String> goodsNoList, AmisPageParam amisPageParam) {
        PageResult<String> pageGoodsnoListPage =  listPageGoodsnoList(storeId, goodsNoList, amisPageParam);
        List<PriceStoreDetail> priceStoreDetails = priceStoreDetailReadService.queryByStoreIdAndGoodsnoList(storeId, pageGoodsnoListPage.getRows(),
           userPriceChannelIdList, userPriceTypeCodeList);
        logger.info("searchByOneStore - 查询价格门店详情完成，数量: {}", priceStoreDetails.size());
        return new PageResult<>(pageGoodsnoListPage.getTotal(), priceStoreDetails);
    }

    private PageResult<String> listPageGoodsnoList(Long storeId, List<String> goodsNoList, AmisPageParam amisPageParam) {
        List<String> pageGoodsnoList = Collections.emptyList();
        List<String> allGoodsNoList =  goodsNoList;
        if (CollectionUtils.isEmpty(allGoodsNoList)) {
            String priceQueryStoreIdKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.PRICE_QUERY_STORE_KEY + storeId;
            RBucket<List<String>> allGoodsNoListCache = redissonClient.getBucket(priceQueryStoreIdKey);
            allGoodsNoList = allGoodsNoListCache.get();
            if (CollectionUtils.isEmpty(allGoodsNoList)) {
                allGoodsNoList = priceStoreDetailReadService.listGoodsNoListByStoreId(storeId);
                if (CollectionUtils.isNotEmpty(allGoodsNoList)) {
                    allGoodsNoListCache.set(allGoodsNoList, 30, TimeUnit.MINUTES);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(allGoodsNoList)) {
            int startIndex = (int) amisPageParam.getOffset();
            int endIndex = startIndex + amisPageParam.getSize();
            endIndex = endIndex > allGoodsNoList.size() ? allGoodsNoList.size() : endIndex;
            if (endIndex > startIndex) {
                pageGoodsnoList = allGoodsNoList.subList(startIndex, endIndex);
            }
        }
        return new PageResult<>(allGoodsNoList == null ? 0L : allGoodsNoList.size(), pageGoodsnoList);
    }

    /**
     * 查询商品编码在多门店中价格明细数据，按照门店分页
     * @param goodsQueryParam
     * @param amisPageParam
     */
    /**
     * 为多门店商品查询构建一个缓存键。
     * 这个键是根据商品、门店、价格类型和渠道的组合生成的，确保相同查询条件的请求可以命中缓存。
     * @param goodsQueryParam 查询参数，包含商品和门店列表
     * @param userPriceTypeCodeList 用户有权限的价格类型
     * @param userPriceChannelIdList 用户有权限的渠道
     * @return 返回一个唯一的Redis缓存键
     */
    private String buildValidStoresCacheKey(GoodsQueryParam goodsQueryParam, List<String> userPriceTypeCodeList, List<Integer> userPriceChannelIdList) {
        // 对列表进行排序，以确保不同顺序的相同参数能生成同一个key
        List<String> sortedGoodsNoList = goodsQueryParam.getGoodsNoList().stream().sorted().collect(Collectors.toList());
        List<Long> sortedStoreIdList = goodsQueryParam.getStoreIdList().stream().sorted().collect(Collectors.toList());
        List<String> sortedPriceTypes = userPriceTypeCodeList.stream().sorted().collect(Collectors.toList());
        List<Integer> sortedChannels = userPriceChannelIdList.stream().sorted().collect(Collectors.toList());

        StringBuilder paramBuilder = new StringBuilder();
        paramBuilder.append("goodsNos:").append(StringUtils.join(sortedGoodsNoList, ",")).append("|");
        paramBuilder.append("storeIds:").append(StringUtils.join(sortedStoreIdList, ",")).append("|");
        paramBuilder.append("priceTypes:").append(StringUtils.join(sortedPriceTypes, ",")).append("|");
        paramBuilder.append("channels:").append(StringUtils.join(sortedChannels, ","));

        // 使用MD5生成参数字符串的哈希值，以缩短key的长度
        String paramsHash = Md5Utils.MD5Encode(paramBuilder.toString());

        // PRICE_QUERY_VALID_STORES_KEY 是一个新的键前缀，用于标识这类缓存
        return RedisKeysConstant.PROJECT_NAME + "PRICE_QUERY_VALID_STORES_" + paramsHash;
    }

    /**
     * [V2版本] 查询多个商品在多个门店中的价格明细，按照包含商品的有效门店进行分页。
     * 1. 首先，高效查询出所有指定门店中，实际包含目标商品的门店列表（validStoreIdList）。
     * 2. 将这个有效门店列表缓存到Redis，以加速后续相同条件的查询。
     * 3. 基于这个有效门店列表的总数进行分页。
     * 4. 只查询当页门店的价格明细，并充分利用分库分表规则保证性能。
     * @param goodsQueryParam 查询参数
     * @param userPriceTypeCodeList 用户可见的价格类型
     * @param userPriceChannelIdList 用户可见的渠道
     * @param amisPageParam 分页参数
     * @return 分页后的价格明细结果
     */
    private PageResult<PriceStoreDetail> searchByGoodsNoAndMoreStores(GoodsQueryParam goodsQueryParam,
        List<String> userPriceTypeCodeList, List<Integer> userPriceChannelIdList,
        AmisPageParam amisPageParam) {
        logger.info("多门店查询V2: 开始执行. goodsQueryParam: {}", goodsQueryParam);
        // 步骤 1: 根据查询参数构建缓存键
        String validStoresCacheKey = buildValidStoresCacheKey(goodsQueryParam, userPriceTypeCodeList, userPriceChannelIdList);
        RBucket<List<Long>> cacheBucket = redissonClient.getBucket(validStoresCacheKey);
        List<Long> validStoreIdList = cacheBucket.get();

        // 步骤 2: 如果缓存未命中，则查询数据库并缓存结果
        if (validStoreIdList == null) {
            logger.info("多门店查询V2: 缓存未命中，开始查询有效门店. Key: {}", validStoresCacheKey);
            List<Long> allStoreIdsToSearch = goodsQueryParam.getStoreIdList();
            // 如果没有提供门店或商品，则无需查询
            if (CollectionUtils.isEmpty(allStoreIdsToSearch) || CollectionUtils.isEmpty(goodsQueryParam.getGoodsNoList())) {
                validStoreIdList = Collections.emptyList();
            } else {
                List<PriceStoreDetail> priceDetailsFromAllStores = new ArrayList<>();
                // 遵循分库规则，按分片查询，避免跨分片的全表扫描
                Map<DatabaseTableEntity, List<Long>> allStoresByShardMap = FenkuUtil.getPriceStoreDetailFenkuMapByStoreIds(allStoreIdsToSearch);
                for (List<Long> storeIdsInShard : allStoresByShardMap.values()) {
                    List<PriceStoreDetail> detailsForShard = priceStoreDetailReadService.queryByGoodsnoesAndStoreIds(
                        goodsQueryParam.getGoodsNoList(),
                        storeIdsInShard,
                        userPriceTypeCodeList,
                        userPriceChannelIdList
                    );
                    if (CollectionUtils.isNotEmpty(detailsForShard)) {
                        priceDetailsFromAllStores.addAll(detailsForShard);
                    }
                }

                // 从查询结果中提取不重复的门店ID，并排序以保证分页稳定
                if (CollectionUtils.isNotEmpty(priceDetailsFromAllStores)) {
                    validStoreIdList = priceDetailsFromAllStores.stream()
                        .map(PriceStoreDetail::getStoreId)
                        .distinct()
                        .sorted()
                        .collect(Collectors.toList());
                } else {
                    validStoreIdList = Collections.emptyList();
                }
            }
            // 将查询到的有效门店ID列表（即使是空列表）缓存30分钟
            cacheBucket.set(validStoreIdList, 30, TimeUnit.MINUTES);
            logger.info("多门店查询V2: 查询并缓存有效门店. Key: {}, 数量: {}", validStoresCacheKey, validStoreIdList.size());
        } else {
            logger.info("多门店查询V2: 缓存命中. Key: {}, 数量: {}", validStoresCacheKey, validStoreIdList.size());
        }

        // 步骤 3: 在内存中对有效门店列表进行分页
        long total = validStoreIdList.size();
        List<Long> pageStoreIdList = Collections.emptyList();
        if (total > 0) {
            int startIndex = (int) amisPageParam.getOffset();
            int endIndex = startIndex + amisPageParam.getSize();
            if (startIndex < total) {
                endIndex = Math.min(endIndex, (int) total);
                pageStoreIdList = validStoreIdList.subList(startIndex, endIndex);
            }
        }

        // 步骤 4: 获取当前页门店的价格详情，同样遵循分库规则
        List<PriceStoreDetail> pagePriceStoreDetails = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(pageStoreIdList)) {
            Map<DatabaseTableEntity, List<Long>> pageStoresByShardMap = FenkuUtil.getPriceStoreDetailFenkuMapByStoreIds(pageStoreIdList);
            for (List<Long> storeIdsInShard : pageStoresByShardMap.values()) {
                List<PriceStoreDetail> priceStoreDetails = priceStoreDetailReadService.queryByGoodsnoesAndStoreIds(
                    goodsQueryParam.getGoodsNoList(),
                    storeIdsInShard,
                    userPriceTypeCodeList,
                    userPriceChannelIdList);
                if (CollectionUtils.isNotEmpty(priceStoreDetails)) {
                    pagePriceStoreDetails.addAll(priceStoreDetails);
                }
            }
        }

        // 步骤 5: 返回最终的分页结果，总数是有效门店的总数
        return new PageResult<>(total, pagePriceStoreDetails);
    }

    /**
     * 默认从已经缓存的全量门店列表分页；
     * @param goodsQueryParam
     * @param amisPageParam
     * @return
     */
    private  PageResult<Long> listPageStoreIdList(GoodsQueryParam goodsQueryParam,
            AmisPageParam amisPageParam) {
        List<Long> pageStoreIdList = Collections.emptyList();
        List<Long> allStoreIdList = goodsQueryParam.getStoreIdList();
        allStoreIdList = FenkuUtil.getPriceStoreDetailFenkuListByStoreIds(allStoreIdList);
        if (CollectionUtils.isNotEmpty(allStoreIdList)) {
            int startIndex = (int) amisPageParam.getOffset();
            int endIndex = startIndex + amisPageParam.getSize();
            endIndex = endIndex > allStoreIdList.size() ? allStoreIdList.size() : endIndex;
            if (endIndex > startIndex) {
                pageStoreIdList = allStoreIdList.subList(startIndex, endIndex);
            }
        }
        return new PageResult<>(allStoreIdList == null ? 0L : allStoreIdList.size(), pageStoreIdList);
    }


    private List<StoreGoodsnoPriceDTO> fromPriceStoreDetail(List<PriceStoreDetail> priceStoreDetailList, GoodsQueryParam goodsQueryParam,
            Map<String, PriceType> userPriceTypeMap, Map<Integer, PriceChannel> userPriceChannelMap) {
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(priceStoreDetailList)) {
            return Collections.emptyList();
        }
        // 数据准备阶段
        long dataPreparationStart = System.currentTimeMillis();
        List<String> goodsNoList = priceStoreDetailList.stream().map(PriceStoreDetail::getGoodsNo)
            .distinct().collect(Collectors.toList());
        List<Long> businessIdList = priceStoreDetailList.stream()
            .map(detail -> {
                Long businessId = detail.getBusinessId();
                Optional<StoreToRedisDTO> storeToRedisDTOOptional =  CacheVar.getStoreDTOByStoreId(detail.getStoreId());
                if (businessId == null && storeToRedisDTOOptional.isPresent()) {
                    businessId = storeToRedisDTOOptional.get().getBusinessId();
                }
                return businessId;
            }).distinct().collect(Collectors.toList());
        Map<String, SpuNewVo> skuMap = getSKuMap(businessIdList, goodsNoList, goodsQueryParam.isDownload());
        Map<String, SpuNewVo> spuMap = getSpuMapBySkuList(skuMap, goodsNoList, goodsQueryParam.isDownload());
        List<Long> storeIdList = priceStoreDetailList.stream().map(PriceStoreDetail::getStoreId).distinct().collect(Collectors.toList());
        long dataPreparationEnd = System.currentTimeMillis();
        logger.info("PriceQueryServiceImpl|fromPriceStoreDetail|数据准备阶段完成|耗时={}ms", dataPreparationEnd - dataPreparationStart);
        // 异步任务提交阶段
        long asyncTaskStart = System.currentTimeMillis();
        Future<Map<String, PriceModeResult>> futureModePriceMap = goodsPriceQueryThreadExecutor.submit(() -> getModePrice(priceStoreDetailList));
        Future<Map<Long, MdmStoreBaseDTO>> futureStoreMap = goodsPriceQueryThreadExecutor.submit(() -> getStoreInfo(storeIdList));
        Map<String, PriceModeResult> modePriceMap = Maps.newConcurrentMap();
        Map<Long, MdmStoreBaseDTO> storeMap = Maps.newConcurrentMap();
		try {
			modePriceMap = futureModePriceMap.get();

			storeMap = futureStoreMap.get();
            long asyncTaskEnd = System.currentTimeMillis();
            logger.info("PriceQueryServiceImpl|fromPriceStoreDetail|异步任务全部完成|耗时={}ms", asyncTaskEnd - asyncTaskStart);
		} catch (Exception e) {
			logger.error("PriceQueryServiceImpl|pageGoodsList|fromPriceStoreDetail|异常|",e);
		}
        // 数据转换处理阶段
        long dataProcessingStart = System.currentTimeMillis();
		List<StoreGoodsnoPriceDTO> storeGoodsNoPriceList = Lists.newArrayList();
        int processedCount = 0;
        int cacheHitCount = 0;
        int cacheMissCount = 0;
		for (PriceStoreDetail detail : priceStoreDetailList) {
            processedCount++;
            // 机构组织信息
            Optional<StoreToRedisDTO> storeToRedisDTOOptional =  CacheVar.getStoreDTOByStoreId(detail.getStoreId());
            if (detail.getPlatformId() == null && storeToRedisDTOOptional.isPresent()) {
                detail.setPlatformId(storeToRedisDTOOptional.get().getPlatformId());
            }
            if (detail.getBusinessId() == null && storeToRedisDTOOptional.isPresent()) {
                detail.setBusinessId(storeToRedisDTOOptional.get().getBusinessId());
            }
            String businessGoodsKey =  detail.getBusinessId() + "_" + detail.getGoodsNo();
            StoreGoodsnoPriceDTO storeGoodsnoPriceDTO = new StoreGoodsnoPriceDTO();
            BeanUtils.copyProperties(detail, storeGoodsnoPriceDTO);
            storeGoodsnoPriceDTO.setPrice(PriceUtil.getYuanFromFenWithNull(detail.getPrice()));

            // 渠道名称设置
            if (userPriceChannelMap.get(detail.getChannelId()) != null) {
                storeGoodsnoPriceDTO.setChannelName(userPriceChannelMap.get(detail.getChannelId()).getChannelName());
                cacheHitCount++;
            } else {
                storeGoodsnoPriceDTO.setChannelName("");
                cacheMissCount++;
            }

            // 价格类型设置
            PriceType priceType = userPriceTypeMap.get(detail.getPriceTypeCode());
            if (priceType != null) {
                storeGoodsnoPriceDTO.setPriceTypeId(priceType.getId());
                storeGoodsnoPriceDTO.setPriceTypeName(priceType.getName());
            } else {
                storeGoodsnoPriceDTO.setPriceTypeName("");
            }

            // SPU信息设置
            SpuNewVo spu = skuMap.getOrDefault(businessGoodsKey, spuMap.get(detail.getGoodsNo()));
            storeGoodsnoPriceDTO.setSpu(spu);

            // 缓存信息设置
            storeGoodsnoPriceDTO.setPlatformName(CacheVar.getPlatformName(detail.getPlatformId()));
            storeGoodsnoPriceDTO.setBusinessName(CacheVar.getBusinessName(detail.getBusinessId()));
            storeGoodsnoPriceDTO.setStoreName(CacheVar.getStoreName(detail.getStoreId()));
            storeGoodsnoPriceDTO.setMdmStore(CacheVar.getSapCode(detail.getStoreId()));
            // 众数价格设置
            PriceModeResult priceModeResult = modePriceMap.get(detail.getGoodsNo());
            if(null!=priceModeResult) {
            	Map<Long, PriceDetailVO> modePriceDetailMap = priceModeResult.getPriceDetailVOS().stream()
                    .collect(Collectors.toMap(PriceDetailVO::getOrgOutId, Function.identity(), (v1, v2) -> v1));
                PriceDetailVO priceDetailVO = modePriceDetailMap.get(detail.getBusinessId());
                if (priceDetailVO != null) {
                    storeGoodsnoPriceDTO.setModePriceCost(priceDetailVO.getModePriceCost().toString());
                } else {
                    storeGoodsnoPriceDTO.setModePriceCost(null);
                }
            }
            // 门店状态设置
            MdmStoreBaseDTO storeBaseDTO = storeMap.get(detail.getStoreId());
            if (storeBaseDTO != null) {
                storeGoodsnoPriceDTO.setStoreStatus(storeBaseDTO.getStoreStatus());
            } else {
                storeGoodsnoPriceDTO.setStoreStatus("");
            }
            storeGoodsNoPriceList.add(storeGoodsnoPriceDTO);
        }

        long dataProcessingEnd = System.currentTimeMillis();
        long totalTime = dataProcessingEnd - startTime;
        logger.info("PriceQueryServiceImpl|fromPriceStoreDetail|数据转换处理完成|处理记录数={}, 转换耗时={}ms, 缓存命中={}, 缓存未命中={}",
            processedCount, dataProcessingEnd - dataProcessingStart, cacheHitCount, cacheMissCount);
        logger.info("PriceQueryServiceImpl|fromPriceStoreDetail|方法执行完成|总耗时={}ms, 返回结果数={}",
            totalTime, storeGoodsNoPriceList.size());
		return storeGoodsNoPriceList;
    }

    /**
     * 多连锁多商品获取众数价格
     * @param priceStoreDetailList
     * @return
     */
    private Map<String,PriceModeResult> getModePrice(List<PriceStoreDetail> priceStoreDetailList) {
    	Map<String,PriceModeResult> modeResultAll = Maps.newConcurrentMap();
    	if(CollectionUtils.isEmpty(priceStoreDetailList)) {
    		return modeResultAll;
    	}
    	int size = 50;
    	Lists.partition(priceStoreDetailList, size).forEach(subList -> {
        	List<PriceModelQueryparam> priceModelQueryParamList = Lists.newArrayList();
        	priceStoreDetailList.forEach(item -> {
        		List<PriceModelQueryOrgParam> queryOrgParamList = Lists.newArrayList();
                PriceModelQueryOrgParam priceParam = new PriceModelQueryOrgParam(OrgLevelTypeEnum.BUSINESS.getType(),null, item.getBusinessId());
                queryOrgParamList.add(priceParam);

                PriceModelQueryparam queryParam = new PriceModelQueryparam();
                queryParam.setGoodsNo(item.getGoodsNo());
                queryParam.setOrgParams(queryOrgParamList);
                priceModelQueryParamList.add(queryParam);
            });
        	ResponseEntity<List<PriceModeResult>> marketingResponse = marketingService.getPriceModelByGoodsNoListAndOrgs(priceModelQueryParamList);
            if(marketingResponse == null || marketingResponse.getStatusCode() != HttpStatus.OK) {
                return;
            }
            List<PriceModeResult> priceModeResultList = marketingResponse.getBody();
            if (CollectionUtils.isEmpty(priceModeResultList)) {
                return;
            }
            modeResultAll.putAll(priceModeResultList.stream().collect(Collectors.toMap(PriceModeResult::getGoodsNo, Function.identity(), (v1, v2) -> v1)));
    	});
        return modeResultAll;
    }

    /**
     * 根据门店id 查询门店基本信息
     * @param storeIdList
     * @return
     */
    @Override
    public Map<Long,MdmStoreBaseDTO> getStoreInfo(List<Long> storeIdList) {
    	Map<Long,MdmStoreBaseDTO> mdmStoreMapAll = Maps.newConcurrentMap();
    	if(CollectionUtils.isEmpty(storeIdList)) {
    		return mdmStoreMapAll;
    	}
    	int size = 50;
    	Lists.partition(storeIdList, size).forEach(subList -> {
    		ResponseEntity<List<MdmStoreBaseDTO>> response = storeService.findStoreByStoreIds(storeIdList);
        	if(response == null || response.getStatusCode() != HttpStatus.OK) {
                return;
            }
        	List<MdmStoreBaseDTO> mdmStoreList = response.getBody();
            if (CollectionUtils.isEmpty(mdmStoreList)) {
                return;
            }
            mdmStoreMapAll.putAll(mdmStoreList.stream().collect(Collectors.toMap(MdmStoreBaseDTO::getStoreId, Function.identity(), (v1, v2) -> v1)));
    	});
    	return mdmStoreMapAll;
    }

    private Map<String, SpuNewVo> getSKuMap(List<Long> businessIdList, List<String> goodsNoList, boolean isDownload) {
        if (CollectionUtils.isEmpty(businessIdList) || CollectionUtils.isEmpty(goodsNoList)) {
            return Collections.emptyMap();
        }
        if (isDownload) {
            int partitionSize = 50;
            Map<String, SpuNewVo> allSkuMap = Maps.newHashMap();
            Lists.partition(goodsNoList, partitionSize).stream().forEach( partGoodsNoList -> {
                int maxSearchCount = 3;
                int searchCount = 1;
                Map<String, SpuNewVo> skuMap = Collections.emptyMap();
                do {
                    skuMap = searchExtService.getSKuMap(businessIdList, partGoodsNoList);
                    if (skuMap.size() == partitionSize || searchCount == maxSearchCount) {
                        allSkuMap.putAll(skuMap);
                        break;
                    }
                    try {
                        Thread.sleep(200);
                    } catch (InterruptedException e) {
                        logger.error("Es|getSKuMap 睡眠 异常", e);
                    }

                } while (++searchCount <= maxSearchCount);
            });

            return allSkuMap;
        } else {
           return searchExtService.getSKuMap(businessIdList, goodsNoList);
        }
    }


    /**
     * 获取所有商品的商品信息，下载没有商品的企业数据则获取集团数据代替
     * @param skuMap
     * @param
     * @return
     */
    private Map<String, SpuNewVo> getSpuMapBySkuList(Map<String, SpuNewVo> skuMap,  List<String> goodsNoList, boolean isDownload) {
        Collection<SpuNewVo> spuNewVos = skuMap.values();
        Map<String, SpuNewVo> spuNewVoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(spuNewVos)) {
            for (SpuNewVo spuNewVo : spuNewVos) {
                if (!spuNewVoMap.containsKey(spuNewVo.getGoodsNo())) {
                    spuNewVoMap.put(spuNewVo.getGoodsNo(), spuNewVo);
                }
            }
        }
        if (isDownload) {
            Set<String> existGoodsnoList = skuMap.values().stream().map(SpuNewVo::getGoodsNo).collect(Collectors.toSet());
            List<String> noGoodsInfoList = goodsNoList.stream().filter(goodsno ->!existGoodsnoList.contains(goodsno)).collect(Collectors.toList());
            int partitionSize = 50;
            if (CollectionUtils.isNotEmpty(noGoodsInfoList)) {
                Lists.partition(noGoodsInfoList, partitionSize).stream().forEach( partNoGoodsInfoList -> {
                    int maxSearchCount = 3;
                    int searchCount = 1;
                    Map<String, SpuNewVo> spuMap = Collections.emptyMap();
                    do {
                        try {
                            Map<String, SpuListVO> spuListMap = searchExtService.getSpuVOMap(partNoGoodsInfoList);
                            spuMap = spuListMap.values().stream().map(SpuListVO::toSpuNewVO)
                                .collect(Collectors.toMap(SpuNewVo::getGoodsNo,Function.identity()));
                        } catch (Exception e) {
                            logger.error("Es|getSpuMap 获取集团商品数据 异常", e);
                        }
                        if (spuMap.size() == partitionSize || searchCount == maxSearchCount) {
                            spuNewVoMap.putAll(spuMap);
                            break;
                        }
                        try {
                            Thread.sleep(200);
                        } catch (InterruptedException e) {
                            logger.error("Es|getSpuMap 睡眠 异常", e);
                        }
                    } while (++searchCount <= maxSearchCount);
                });
            }
        }
        return spuNewVoMap;
    }

    /**
     * 是否查询单店数据
     * @param goodsQueryParam
     * @return
     */
    private boolean isSearchOneStore(GoodsQueryParam goodsQueryParam) {
        return CollectionUtils.isNotEmpty(goodsQueryParam.getStoreIdList()) && goodsQueryParam.getStoreIdList().size() == 1;
    }

    private List<GoodsDTO> getGoodsExportList(List<GoodsDTO> list) {
        return list.stream().peek(v -> v.setPriceYuan(PriceUtil.getYuanFromFenWithNull(v.getPrice()))).collect(Collectors.toList());
    }

    @Override
    public GoodsDTO getGoodsPrice(TokenUserDTO userDTO, UniqueGoodsPriceQuery query) {
        try {
            Map<Integer, PriceChannel> userPriceChannelMap = priceChannelService.getUserChannelMap(userDTO.getUserId());
            checkUniqueGoodsPriceQuery(query);

            PriceStoreDetailQuery detailQuery = PriceStoreDetailQuery.builder()
                .storeId(query.getStoreId())
                .goodsNo(query.getGoodsNo())
                .priceTypeCode(query.getPriceTypeCode())
                .channelId(query.getChannelId())
                .build();
            List<PriceStoreDetail> details = priceStoreDetailReadService.query(detailQuery);
            if (CollectionUtils.isNotEmpty(details)) {
                PriceStoreDetail detail = details.get(0);
                GoodsDTO goodsDTO = new GoodsDTO();
                BeanUtils.copyProperties(detail, goodsDTO);
                goodsDTO.setGoodsName(detail.getCurName());
                // 查询渠道
                goodsDTO.setChannelName(Optional.ofNullable(userPriceChannelMap.get(detail.getChannelId())).map(PriceChannel::getChannelName).orElse(""));
                Optional<StoreToRedisDTO> storeToRedisDTOOptional =  CacheVar.getStoreDTOByStoreId(detail.getStoreId());
                Long  platformId = detail.getPlatformId() == null ? (storeToRedisDTOOptional.isPresent() ? storeToRedisDTOOptional.get().getPlatformId() : null) :
                    detail.getPlatformId();
                Long businessId = detail.getBusinessId() == null ? (storeToRedisDTOOptional.isPresent() ? storeToRedisDTOOptional.get().getBusinessId() : null) :
                    detail.getBusinessId();
                goodsDTO.setPlatformName(CacheVar.getPlatformName(platformId));
                goodsDTO.setBusinessName(CacheVar.getBusinessName(businessId));
                goodsDTO.setStoreName(CacheVar.getStoreName(detail.getStoreId()));
                setAncestorsOrgName(goodsDTO);
                return goodsDTO;
            }
        } catch (Exception e) {
            logger.error("获取商品价格信息异常", e);
            throw e;
        }
        return new GoodsDTO();
    }

    @Override
    public PageResult<PriceHistoryDTO> getHistoryList(PriceHistoryQueryParam param) {
        try {
            checkUniqueGoodsPriceQuery(param);

            PriceDetailHistoryExample example = new PriceDetailHistoryExample();
            example.createCriteria()
                .andStoreIdEqualTo(param.getStoreId())
                .andGoodsNoEqualTo(param.getGoodsNo())
                .andPriceTypeCodeEqualTo(param.getPriceTypeCode())
                .andChannelIdEqualTo(param.getChannelId());
            long count = priceDetailHistoryMapper.countByExample(example);
            if (count == 0) {
                return new PageResult<>(0L, new ArrayList<>());
            }

            example.setOrderByClause("gmt_create desc");
            example.setOffset((param.getPage()-1) * param.getPageSize());
            example.setLimit(param.getPageSize());
            List<PriceDetailHistory> histories = priceDetailHistoryMapper.selectByExample(example);
            // 查询调价单信息
            List<String> adjustCodes = histories.stream().map(PriceDetailHistory::getAdjustCode).distinct().collect(Collectors.toList());
            Map<String, AdjustPriceOrder> adjustPriceOrderMap = getAdjustOrderMap(adjustCodes);
            List<PriceHistoryDTO> historyDTOS = histories.stream().map(v -> {
                PriceHistoryDTO historyDTO = new PriceHistoryDTO();
                BeanUtils.copyProperties(v, historyDTO);
                historyDTO.setEffectTime(v.getGmtCreate());
                AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMap.get(v.getAdjustCode());
                if (null != adjustPriceOrder) {
                    historyDTO.setCreatedByName(adjustPriceOrder.getCreatedByName());
                    historyDTO.setAuditByName(adjustPriceOrder.getAuditByName());
                }
                return historyDTO;
            }).collect(Collectors.toList());
            return new PageResult<>(count, historyDTOS);
        } catch (Exception e) {
            logger.error("获取价格变动历史列表异常", e);
            throw e;
        }
    }

    @Override
    public PriceHistoryLineChart getHistoryLineChart(PriceHistoryQueryParam param) {
        try {
            checkUniqueGoodsPriceQuery(param);

//            Date[] dates = checkEffectDate(param.getEffectTime());
//            Date date1 = dates[0];
//            Date date2 = dates[1];

            PriceDetailHistoryExample example = new PriceDetailHistoryExample();
            example.createCriteria()
                .andStoreIdEqualTo(param.getStoreId())
                .andGoodsNoEqualTo(param.getGoodsNo())
                .andPriceTypeCodeEqualTo(param.getPriceTypeCode())
                .andChannelIdEqualTo(param.getChannelId());
//                .andGmtCreateBetween(date1, date2);
            List<PriceDetailHistory> histories = priceDetailHistoryMapper.selectByExample(example);
            logger.info("getHistoryLineChart|query result size={}", histories.size());

            List<String> date = new ArrayList<>();
            List<String> line = new ArrayList<>();

            // 按天分组
            Map<String, List<PriceDetailHistory>> historyMap = histories.stream().collect(Collectors.groupingBy(v -> DateFormatUtils.format(v.getGmtCreate(), "yyyMMdd")));
            // 对key排序
            historyMap = historyMap.entrySet().stream().sorted(Map.Entry.comparingByKey()).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
            historyMap.forEach((k, v) -> {
                date.add(k);
                BigDecimal price = v.stream().sorted(Comparator.comparing(PriceDetailHistory::getGmtCreate).reversed()).collect(Collectors.toList()).get(0).getPrice();
                price = PriceUtil.getYuanFromFenWithNull(price);
                price = Optional.ofNullable(price).orElse(BigDecimal.ZERO);
                line.add(price.toString());
            });
            return new PriceHistoryLineChart(date, line);
        } catch (Exception e) {
            logger.error("获取价格变动历史折线图异常", e);
            throw e;
        }
    }

    /**
     * 根据调价单号获取调价单
     *
     * @param adjustCodes
     * @return
     */
    @Override
    public Map<String, AdjustPriceOrder> getAdjustOrderMap(List<String> adjustCodes) {
        if (CollectionUtils.isEmpty(adjustCodes)) {
            return Maps.newHashMap();
        }
        AdjustPriceOrderExample orderExample = new AdjustPriceOrderExample();
        orderExample.createCriteria().andAdjustCodeIn(adjustCodes);
        List<AdjustPriceOrder> orderList = adjustPriceOrderMapper.selectByExample(orderExample);
        return orderList.stream().collect(Collectors.toMap(AdjustPriceOrder::getAdjustCode, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public Map<Long, List<OrgDTO>> getAncestorsOrgList(List<Long> storeIds) {
        logger.info("getAncestorsOrgList|storeIds -> {}", storeIds);
        List<OrgDTO> storeOrgDTOS = new ArrayList<>();
        Lists.partition(storeIds, 200).forEach(subList -> storeOrgDTOS.addAll(permissionExtService.listOrgByOutId(storeIds, OrgTypeEnum.STORE.getCode())));
        List<Long> storeOrgIds = storeOrgDTOS.stream().map(OrgDTO::getId).collect(Collectors.toList());
        Map<Long, Long> storeIdMap = storeOrgDTOS.stream().collect(Collectors.toMap(OrgDTO::getId, OrgDTO::getOutId, (k1, k2) -> k1));
        logger.info("getAncestorsOrgList|storeOrgIds -> {}", storeOrgIds);
        Map<Long, List<OrgDTO>> map = new HashMap<>();
        Map<Long, List<OrgDTO>> orgMap = new HashMap<>();
        Lists.partition(storeOrgIds, 1000).forEach(subList -> orgMap.putAll(permissionExtService.getAncestorsOrgList(subList)));
        if (MapUtils.isNotEmpty(orgMap)) {
            orgMap.forEach((k, v) -> map.put(storeIdMap.get(k), v));
            return map;
        }
        return Maps.newHashMap();
    }

    @Override
    public Map<Long, List<OrgToRedisDTO>> getCachedAncestorsOrgList(String redisKey, List<Long> storeIds) {
        RBucket<Map<Long, List<OrgToRedisDTO>>> ancestorsOrgCache = redissonClient.getBucket(RedisKeysConstant.PRICE_USER_DATA_ANCESTOR_ORG_KEY + redisKey);
        if (Objects.nonNull(ancestorsOrgCache) && MapUtils.isNotEmpty(ancestorsOrgCache.get())) {
            return ancestorsOrgCache.get();
        }

        AtomicReference<Map<Long, List<OrgDTO>>> atomicMap = new AtomicReference<>(new HashMap<>());
        RBucket<Map<Long, Long>> storeIdMapCache = redissonClient.getBucket(RedisKeysConstant.PRICE_USER_DATA_STORE_ID_MAP_KEY + redisKey);
        if (storeIdMapCache == null || MapUtils.isEmpty(storeIdMapCache.get())) {
            atomicMap.set(getAncestorsOrgList(storeIds));
        } else {
            Map<Long, Long> storeIdMap = storeIdMapCache.get();
            List<Long> storeOrgIds = new ArrayList<>(storeIdMap.keySet());
            Map<Long, List<OrgDTO>> orgMap = new HashMap<>();
            Lists.partition(storeOrgIds, 1000).forEach(subList -> orgMap.putAll(permissionExtService.getAncestorsOrgList(subList)));
            if (MapUtils.isNotEmpty(orgMap)) {
                orgMap.forEach((k, v) -> atomicMap.get().put(storeIdMap.get(k), v));
            }
        }

        if (MapUtils.isNotEmpty(atomicMap.get())) {
            Map<Long, List<OrgToRedisDTO>> redisMap = new HashMap<>();
            atomicMap.get().forEach((k, v) -> {
                List<OrgToRedisDTO> orgToRedisDTOS = new ArrayList<>();
                v.forEach(org -> {
                    OrgToRedisDTO orgToRedisDTO = new OrgToRedisDTO();
                    orgToRedisDTO.setId(org.getId());
                    orgToRedisDTO.setName(org.getName());
                    orgToRedisDTO.setShortName(org.getShortName());
                    orgToRedisDTO.setType(org.getType());
                    orgToRedisDTO.setSapCode(org.getSapcode());
                    orgToRedisDTO.setOutId(org.getOutId());
                    orgToRedisDTOS.add(orgToRedisDTO);
                });
                redisMap.put(k, orgToRedisDTOS);
            });
            ancestorsOrgCache.setAsync(redisMap, 1L, TimeUnit.DAYS);
            return redisMap;
        }
        return Maps.newHashMap();
    }

    @Override
    public void clearCache(String redisKey) {
        RBucket<List<Long>> userDataStoreIdListCache = redissonClient.getBucket(RedisKeysConstant.PRICE_USER_DATA_STORE_ID_LIST_KEY + redisKey);
        userDataStoreIdListCache.delete();
        RBucket<Map<Long, Long>> userDataStoreIdMapCache = redissonClient.getBucket(RedisKeysConstant.PRICE_USER_DATA_STORE_ID_MAP_KEY + redisKey);
        userDataStoreIdMapCache.delete();
        RBucket<Map<Long, List<OrgToRedisDTO>>> userDataOrgMapCache = redissonClient.getBucket(RedisKeysConstant.PRICE_USER_DATA_ANCESTOR_ORG_KEY + redisKey);
        userDataOrgMapCache.delete();
    }

    /**
     * 校验唯一查询参数
     *
     * @param query
     */
    private void checkUniqueGoodsPriceQuery(UniqueGoodsPriceQuery query) {
        Optional.ofNullable(query.getStoreId()).orElseThrow(() -> new BusinessErrorException("storeId参数不能为空"));
        Optional.ofNullable(query.getGoodsNo()).orElseThrow(() -> new BusinessErrorException("goodsNo参数不能为空"));
        Optional.ofNullable(query.getPriceTypeCode()).orElseThrow(() -> new BusinessErrorException("priceTypeCode参数不能为空"));
        Optional.ofNullable(query.getChannelId()).orElseThrow(() -> new BusinessErrorException("channelId参数不能为空"));
    }

    private Date[] checkEffectDate(String effectTime) {
        Date date1;
        Date date2;
        if (StringUtils.isNotBlank(effectTime) && effectTime.contains(",")) {
            String[] times = effectTime.split(",");
            try {
                date1 = DateUtils.parseDate(times[0], "yyyy-MM-dd");
                date2 = DateUtils.parseDate(times[1], "yyyy-MM-dd");
            } catch (ParseException e) {
                throw new BusinessErrorException("生效时间解析错误");
            }
        } else {
            throw new BusinessErrorException("生效时间不能为空");
        }

        // 时间范围
        if (date1.compareTo(date2) > 0) {
            throw new BusinessErrorException("起始时间不能大于结束时间");
        }

        if (DateUtils.addMonths(date1, 6).compareTo(date2) < 0) {
            throw new BusinessErrorException("最大只支持6个月查询");
        }
        return new Date[] {date1, date2};
    }

    /**
     * 校验参数
     * @param isSearchOneStore
     * @param goodsQueryParam
     */
    private void checkParam(TokenUserDTO tokenUserDTO, boolean isSearchOneStore, GoodsQueryParam goodsQueryParam) {
        logger.info("checkParam - isSearchOneStore: {}, goodsQueryParam: {}", isSearchOneStore, goodsQueryParam);
        if (null == goodsQueryParam.getPlatformId()) {
            throw new AmisBadRequestException("请选择平台");
        }
        if (null == goodsQueryParam.getChannelId()) {
            throw new AmisBadRequestException("请选择一个渠道");
        }
        if (CollectionUtils.isNotEmpty(goodsQueryParam.getStoreOrgIdList()) && goodsQueryParam.getStoreOrgIdList().size()>goodsMoreStoreLimit){
            throw new AmisBadRequestException("单次查询门店数量不能超过"+goodsMoreStoreLimit);
        }
        if (goodsQueryParam.isDownload() && StringUtils.isEmpty(goodsQueryParam.getDownloadCode())) {
            throw new AmisBadRequestException("商品价格查询下载时需要传downloadCode");
        }
        if (goodsQueryParam.isDownload()) {
            if (StringUtils.isEmpty(goodsQueryParam.getDownloadCode())) {
                throw new AmisBadRequestException("商品价格查询下载时需要传downloadCode");
            }
            if (StringUtils.isEmpty(goodsQueryParam.getGoodsnoes())
                && CollectionUtils.isNotEmpty(goodsQueryParam.getStoreIdList())
                && goodsQueryParam.getStoreIdList().size() > 1
            ) {
                throw new AmisBadRequestException("多个门店时需要选择商品进行查询");
            }
            if (StringUtils.isEmpty(goodsQueryParam.getGoodsnoes())
                && CollectionUtils.isEmpty(goodsQueryParam.getStoreIdList())) {
                throw new AmisBadRequestException("请选择单门店或者选择多商品进行查询");
            }
        } else {
        	if (CollectionUtils.isEmpty(goodsQueryParam.getGoodsNoList()) &&
            		(CollectionUtils.isEmpty(goodsQueryParam.getStoreOrgIdList()) ||
            		goodsQueryParam.getStoreOrgIdList().size()>1)) {
                throw new AmisBadRequestException("请先选择要查询的商品或指定1个门店");
            }
        }

    }
    private void setAncestorsOrgName(GoodsDTO goodsDTO) {
        if (StringUtils.isBlank(goodsDTO.getPlatformName()) || StringUtils.isBlank(goodsDTO.getBusinessName()) || StringUtils.isBlank(goodsDTO.getStoreName())) {
            Map<Long, List<OrgDTO>> ancestorsOrgMap = this.getAncestorsOrgList(Lists.newArrayList(goodsDTO.getStoreId()));
            List<OrgDTO> ancestorsOrgList = ancestorsOrgMap.get(goodsDTO.getStoreId());
            if (CollectionUtils.isNotEmpty(ancestorsOrgList)) {
                if (StringUtils.isBlank(goodsDTO.getPlatformName())) {
                    goodsDTO.setPlatformName(ancestorsOrgList.stream().filter(org -> Objects.equals(org.getType(), OrgTypeEnum.REGIONAL_PLATFORM.getCode())).findFirst().map(OrgDTO::getShortName).orElse(""));
                }
                if (StringUtils.isBlank(goodsDTO.getBusinessName())) {
                    goodsDTO.setBusinessName(ancestorsOrgList.stream().filter(org -> Objects.equals(org.getType(), OrgTypeEnum.BUSINESS.getCode())).findFirst().map(OrgDTO::getShortName).orElse(""));
                }
                if (StringUtils.isBlank(goodsDTO.getStoreName())) {
                    goodsDTO.setStoreName(ancestorsOrgList.stream().filter(org -> Objects.equals(org.getType(), OrgTypeEnum.STORE.getCode())).findFirst().map(OrgDTO::getShortName).orElse(""));
                }
            }
        }
    }

    @Override
    public List<Long> getUserDataStoreIdList(TokenUserDTO tokenUserDTO, Long orgId, boolean storeCenter, boolean checkRole, List<String> roleCodes) {
        String redisKey = RedisKeysConstant.PRICE_USER_DATA_STORE_ID_LIST_KEY + tokenUserDTO.getUserId() + "_" + Optional.ofNullable(orgId).orElse(tokenUserDTO.getBusinessId());
        RBucket<List<Long>> userDataStoreIdListCache = redissonClient.getBucket(redisKey);
        if (Objects.nonNull(userDataStoreIdListCache) && CollectionUtils.isNotEmpty(userDataStoreIdListCache.get())) {
        	return userDataStoreIdListCache.get();
        }
        if (storeCenter) {
            List<OrgDTO> businessOrgDTOS = permissionExtService.listOrgByOutId(Lists.newArrayList(tokenUserDTO.getBusinessId()), OrgTypeEnum.BUSINESS.getCode());
            if (CollectionUtils.isEmpty(businessOrgDTOS)) {
                throw new BusinessErrorException("当前登录连锁没有获取到组织信息");
            }
            orgId = businessOrgDTOS.get(0).getId();
            // 校验角色
            if (checkRole && CollectionUtils.isNotEmpty(roleCodes)) {
                List<UserRoleRelateDTO> userRoles = permissionExtService.getUserRoles(tokenUserDTO.getUserId());
                if (CollectionUtils.isEmpty(userRoles)) {// 没有角色
                    throw new BusinessErrorException("当前用户没有角色");
                }

                List<String> userRoleCodes = userRoles.stream().map(UserRoleRelateDTO::getRoleCode).collect(Collectors.toList());
                userRoleCodes.retainAll(roleCodes);
                if (CollectionUtils.isEmpty(userRoleCodes)) {
                    throw new BusinessErrorException("当前用户没有角色["+String.join(",", roleCodes)+"]");
                }
            }
        }

        List<ChildOrgsDTO> childOrgDTOS = permissionExtService.listChildOrgAssignedType(Lists.newArrayList(orgId), OrgTypeEnum.STORE.getCode());
        if (CollectionUtils.isEmpty(childOrgDTOS) || CollectionUtils.isEmpty(childOrgDTOS.get(0).getChildren())) {
        	return Lists.newArrayList();
            //throw new BusinessErrorException("当前机构下没有门店组织信息");
        }

        List<Long> childOrgIds = childOrgDTOS.get(0).getChildren().stream().map(OrgDTO::getId).collect(Collectors.toList());
        List<Long> userDataOutIdList = permissionExtService.retainUserDataScopeOutIdListByType(tokenUserDTO.getUserId(), OrgTypeEnum.STORE.getCode(), childOrgIds);
        if (CollectionUtils.isEmpty(userDataOutIdList)) {
            throw new BusinessErrorException("当前机构下的门店没有用户数据权限");
        }

        userDataStoreIdListCache.setAsync(userDataOutIdList, 6, TimeUnit.HOURS);

        return userDataOutIdList;
    }

    @Override
    @NewSpan
    public void initPageGoodsList(GoodsQueryParam param, TokenUserDTO userDTO) {
        param.setDownload(true);
        if (StringUtils.isEmpty(param.getDownloadCode())) {
            logger.info("PriceQueryServiceImpl|initPageGoodsList|downloadCode不能为空");
            return;
        }
        // 缓存30分钟，用完删除
        int cacheTime = 30;
        //强制从第一页开始
        param.setPage(1);
        int page = 1;
        param.setPageSize(GOODS_STORE_DETAIL_PAGE_SIZE);
        //第一页的数据
        PageResult<Map<String, Object>> pageResult = pageGoodsList(param, userDTO);
        long totalPage = (long) Math.ceil(pageResult.getTotal() * 1.0 / param.getPageSize());
        if (totalPage <= 1) {
            pageResult.setFinished(true);
        }
        String cacheKeyPrefix = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.PRICE_QUERY_DETAIL_RESULT + param.getDownloadCode() + "_";
        String keyCache = cacheKeyPrefix + page;
        RBucket<PageResult<Map<String, Object>>> pageGoodsDTOListCache = redissonClient.getBucket(keyCache);
        pageGoodsDTOListCache.set(pageResult, cacheTime, TimeUnit.MINUTES);
        logger.info("PriceQueryServiceImpl|initPageGoodsList|page: 1");

        for (page = 2; page <= totalPage; page++) {
            keyCache = cacheKeyPrefix + page;
            param.setPage(page);
            pageResult = pageGoodsList(param, userDTO);
            if (page == totalPage) {
                pageResult.setFinished(true);
            }
            pageGoodsDTOListCache = redissonClient.getBucket(keyCache);
            pageGoodsDTOListCache.set(pageResult, cacheTime, TimeUnit.MINUTES);
            logger.info("PriceQueryServiceImpl|initPageGoodsList|page: {}", page);
        }
    }

    @Override
    public PageResult<Map<String, Object>> pageGoodsListFromCache(String downloadCode, Integer page) {
        if (StringUtils.isEmpty(downloadCode)) {
            logger.info("PriceQueryServiceImpl|initPageGoodsList|searchCode不能为空");
            throw new AmisBadRequestException();
        }
        if (page == null || page < 1) {
            logger.info("PriceQueryServiceImpl|initPageGoodsList|page参数必须有值且大于等于1");
            throw new AmisBadRequestException();
        }

        String cacheKey = RedisKeysConstant.PROJECT_NAME + RedisKeysConstant.PRICE_QUERY_DETAIL_RESULT
            + downloadCode + "_" + page;
        RBucket<PageResult<Map<String, Object>>> pageResultCache = redissonClient.getBucket(cacheKey);
        PageResult<Map<String, Object>> pageResult = pageResultCache.get();
        if (pageResult == null) {
            pageResult = new PageResult<>();
            pageResult.setPagePrepared(false);
        } else {
            pageResult.setPagePrepared(true);
            pageResultCache.deleteAsync();
        }
        return pageResult;
    }

    @Override
    public void cacheGoodsStoreIds(List<String> goodsNoList, TokenUserDTO userDTO, List<Long> storeIdList) {
        Map<String, PriceType> userPriceTypeMap = priceTypeService.getUserPriceTypeMap(userDTO.getUserId());
        Map<Integer, PriceChannel> userPriceChannelMap = priceChannelService.getUserChannelMap(userDTO.getUserId());

        List<String> userPriceTypeCodeLsit = Lists.newArrayList(userPriceTypeMap.keySet());
        List<Integer> userPriceChannelIdList = Lists.newArrayList(userPriceChannelMap.keySet());
        if (CollectionUtils.isNotEmpty(goodsNoList)) {
            goodsNoList.forEach(goodsNo ->
                queryGoodsStoreIds(goodsNo, userPriceTypeCodeLsit, userPriceChannelIdList, userDTO, storeIdList));
        }
    }

    private List<Long> queryGoodsStoreIds(String goodsNo, List<String> userPriceTypeCodeList, List<Integer> userPriceChannelIdList, TokenUserDTO userDTO,List<Long> storeIdList) {
        // 构建缓存key，包含所有影响查询结果的参数
        String priceQueryGoodsnoKey = buildPriceQueryGoodsnoKey(goodsNo, userPriceTypeCodeList, userPriceChannelIdList, userDTO.getUserId(), storeIdList);
        logger.debug("PriceQueryServiceImpl|queryGoodsStoreIds|构建缓存key|key={}", priceQueryGoodsnoKey);
        RBucket<List<Long>> goodsStoreIdListCache = redissonClient.getBucket(priceQueryGoodsnoKey);
        List<Long> goodsStoreIdList = goodsStoreIdListCache.get();
        if (CollectionUtils.isEmpty(goodsStoreIdList)) {
            logger.info("PriceQueryServiceImpl|queryGoodsStoreIds|缓存未命中|开始查询数据库|goodsNo={}, userId={}, storeIdList.size={}",
                goodsNo, userDTO.getUserId(), storeIdList != null ? storeIdList.size() : 0);
        	if(null == goodsStoreIdList) {
        		goodsStoreIdList = Lists.newArrayList();
        	}
        	List<List<Long>> partition = Lists.partition(storeIdList, 200);
        	StorePriceParamVo paramVO = null;
        	List<Long> storeIds = null;
        	for (List<Long> subStoreIdList : partition) {
        		paramVO = new StorePriceParamVo();
                paramVO.setGoodsNoList(Lists.newArrayList(goodsNo));
                paramVO.setPriceTypeCodeList(userPriceTypeCodeList);
                paramVO.setChannelIdList(userPriceChannelIdList);
                paramVO.setStoreIdList(subStoreIdList);
                storeIds = nyuwaErpeFacadeService.getStoreIdByGoodsNoAndPriceTypeAndChannelId(paramVO);
                if(CollectionUtils.isNotEmpty(storeIds)) {
                	goodsStoreIdList.addAll(storeIds);
                }
			}
        	if(CollectionUtils.isNotEmpty(goodsStoreIdList)) {
            	goodsStoreIdListCache.set(goodsStoreIdList, 30, TimeUnit.MINUTES);
                logger.info("PriceQueryServiceImpl|queryGoodsStoreIds|查询完成并缓存|goodsNo={}, 查询结果数量={}, 缓存30分钟",
                    goodsNo, goodsStoreIdList.size());
            } else {
                logger.info("PriceQueryServiceImpl|queryGoodsStoreIds|查询完成无结果|goodsNo={}", goodsNo);
            }
        } else {
            logger.info("PriceQueryServiceImpl|queryGoodsStoreIds|缓存命中|goodsNo={}, 缓存结果数量={}",
                goodsNo, goodsStoreIdList.size());
        }
        return goodsStoreIdList;
    }

    /**
     * 构建价格查询商品门店缓存key
     *
     * 缓存key组成部分：
     * 1. 项目前缀：PRICECENTER_
     * 2. 功能前缀：PRICE_QUERY_GOODSNO_
     * 3. 商品编码：goodsNo
     * 4. 用户ID：userId
     * 5. 参数哈希：包含价格类型、渠道、门店列表的MD5值
     *
     * @param goodsNo 商品编码
     * @param userPriceTypeCodeList 用户可访问的价格类型代码列表
     * @param userPriceChannelIdList 用户可访问的渠道ID列表
     * @param userId 用户ID
     * @param storeIdList 门店ID列表
     * @return 完整的缓存key
     */
    private String buildPriceQueryGoodsnoKey(String goodsNo, List<String> userPriceTypeCodeList,
                                           List<Integer> userPriceChannelIdList, Long userId, List<Long> storeIdList) {
        // 构建参数字符串，包含所有影响查询结果的参数
        StringBuilder paramBuilder = new StringBuilder();

        // 添加价格类型列表（排序后拼接）
        if (CollectionUtils.isNotEmpty(userPriceTypeCodeList)) {
            paramBuilder.append("priceTypes:")
                       .append(StringUtils.join(userPriceTypeCodeList.stream().sorted().collect(Collectors.toList()), ","))
                       .append("|");
        }

        // 添加渠道ID列表（排序后拼接）
        if (CollectionUtils.isNotEmpty(userPriceChannelIdList)) {
            paramBuilder.append("channels:")
                       .append(StringUtils.join(userPriceChannelIdList.stream().sorted().collect(Collectors.toList()), ","))
                       .append("|");
        }

        // 添加门店ID列表（排序后拼接）
        if (CollectionUtils.isNotEmpty(storeIdList)) {
            paramBuilder.append("stores:")
                       .append(StringUtils.join(storeIdList.stream().sorted().collect(Collectors.toList()), ","));
        }

        // 对参数字符串进行MD5加密，避免key过长
        String paramsHash = Md5Utils.MD5Encode(paramBuilder.toString());

        // 构建最终的缓存key：项目前缀 + 功能前缀 + 商品编码 + 用户ID + 参数哈希
        return RedisKeysConstant.PROJECT_NAME +
               RedisKeysConstant.PRICE_QUERY_GOODSNO_KEY +
               goodsNo + "_" +
               userId + "_" +
               paramsHash;
    }

	@Override
	public List<Long> convertStoreId(GoodsQueryParam param,TokenUserDTO tokenUserDTO) {
		List<Long> storeIdList = Lists.newArrayList();
		List<Long> orgIdList = Lists.newArrayList();
		if(CollectionUtils.isNotEmpty(param.getStoreOrgIdList())){
			storeIdList = CacheVar.listStoreIdsByStoreOrgIds(param.getStoreOrgIdList());
		}else if(CollectionUtils.isNotEmpty(param.getAreaOrgIdList())) {
			orgIdList.addAll(param.getAreaOrgIdList());
		}else if(CollectionUtils.isNotEmpty(param.getBusinessOrgIdList())) {
			orgIdList.addAll(param.getBusinessOrgIdList());
		}else if(null != param.getPlatformId()) {
			orgIdList.addAll(Lists.newArrayList(param.getPlatformId()));
		}
		List<OrgDTO> orgDTOList = permissionExtService.findScopeStoreList(tokenUserDTO.getUserId(), orgIdList);
		if(CollectionUtils.isNotEmpty(orgDTOList)) {
			storeIdList = orgDTOList.stream().map(OrgDTO::getOutId).collect(Collectors.toList());
		}
		return storeIdList;
	}

}
