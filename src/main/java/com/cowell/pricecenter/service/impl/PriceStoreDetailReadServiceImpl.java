package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.config.AdjustPriceConfig;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.entity.PriceStoreDetailExample;
import com.cowell.pricecenter.enums.StatusEnum;
import com.cowell.pricecenter.mapper.PriceStoreDetailMapper;
import com.cowell.pricecenter.mapper.extension.PriceStoreDetailExMapper;
import com.cowell.pricecenter.service.PriceStoreDetailReadService;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/23 17:43
 */
@Service
public class PriceStoreDetailReadServiceImpl implements PriceStoreDetailReadService {

    private final Logger logger = LoggerFactory.getLogger(PriceStoreDetailReadServiceImpl.class);

    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;

    @Autowired
    private PriceStoreDetailExMapper priceStoreDetailExMapper;

    @Resource
    private AdjustPriceConfig adjustPriceConfig;

    @Override
    public List<PriceStoreDetail> getPriceStoreDetailByParam(Long storeId, List<Integer> channelIdList, List<String> goodsNoList,
                                                             List<String> priceTypeCodeList) {

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .channelIdList(channelIdList)
            .goodsNoList(goodsNoList)
            .priceTypeCodeList(priceTypeCodeList)
            .build();
        return query(query);
    }



    @Override
    public List<PriceStoreDetail> query(PriceStoreDetailQuery query) {
        PriceStoreDetailExample example = getPriceStoreDetailExample(query);

        return priceStoreDetailMapper.selectByExample(example);
    }

    /**
     * 根据查询参数获取查询Example
     * @param query PriceStoreDetailQuery
     * @return PriceStoreDetailExample
     */
    private PriceStoreDetailExample getPriceStoreDetailExample(PriceStoreDetailQuery query) {
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        buildCriteria(query, example, false);

        if (Objects.nonNull(query.getPage()) && Objects.nonNull(query.getPageSize())) {
            if (query.getPage() < 1) {
                query.setPage(1);
            }
            example.setOffset((query.getPage() - 1) * query.getPageSize());
            example.setLimit(query.getPageSize());
        }

        example.setOrderByClause(query.getOrderBy());
        return example;
    }

    /**
     * 构建标准查询语句
     * @param query PriceStoreDetailQuery
     * @param example PriceStoreDetailExample
     * @param or 是否使用or查询
     */
    private void buildCriteria(PriceStoreDetailQuery query, PriceStoreDetailExample example, boolean or) {

        if (Objects.isNull(query)) {
            return;
        }

        PriceStoreDetailExample.Criteria criteria;
        if (or) {
            criteria = example.or();
        } else {
            criteria = example.createCriteria();
        }

        if (Objects.nonNull(query.getStoreId())) {
            criteria.andStoreIdEqualTo(query.getStoreId());
        }

        if (Objects.nonNull(query.getBusinessId())) {
            criteria.andBusinessIdEqualTo(query.getBusinessId());
        }

        if (StringUtils.isNotBlank(query.getGoodsNo())) {
            criteria.andGoodsNoEqualTo(query.getGoodsNo());
        }

        if (CollectionUtils.isNotEmpty(query.getGoodsNoList())) {
            criteria.andGoodsNoIn(query.getGoodsNoList());
        }

        if (Objects.nonNull(query.getSkuId())) {
            criteria.andSkuIdEqualTo(query.getSkuId());
        }

        if (CollectionUtils.isNotEmpty(query.getSkuIdList())) {
            criteria.andSkuIdIn(query.getSkuIdList());
        }

        if (StringUtils.isNotBlank(query.getPriceTypeCode())) {
            criteria.andPriceTypeCodeEqualTo(query.getPriceTypeCode());
        }

        if (CollectionUtils.isNotEmpty(query.getPriceTypeCodeList())) {
            criteria.andPriceTypeCodeIn(query.getPriceTypeCodeList());
        }

        if (StringUtils.isNotBlank(query.getAdjustCode())) {
            criteria.andAdjustCodeEqualTo(query.getAdjustCode());
        }

        if (Objects.nonNull(query.getChannelId())) {
            criteria.andChannelIdEqualTo(query.getChannelId());
        }

        if (CollectionUtils.isNotEmpty(query.getChannelIdList())) {
            criteria.andChannelIdIn(query.getChannelIdList());
        }

        if (StringUtils.isNotBlank(query.getNextPriceDxsAdjustCode())) {
            criteria.andNextPriceDxsAdjustCodeEqualTo(query.getNextPriceDxsAdjustCode());
        }

        if (Objects.nonNull(query.getLevel())) {
            criteria.andLevelEqualTo(query.getLevel().byteValue());
        }

        if (StringUtils.isNotBlank(query.getExtendLike())) {
            criteria.andExtendLike(query.getExtendLike());
        }

        if (Objects.nonNull(query.getPriceIsNotNull())) {
            if (query.getPriceIsNotNull()) {
                criteria.andPriceIsNotNull();
            } else {
                criteria.andPriceIsNull();
            }
        }

        if (CollectionUtils.isNotEmpty(query.getIdList())) {
            criteria.andIdIn(query.getIdList());
        }

        if (Objects.nonNull(query.getStatus())) {
            criteria.andStatusEqualTo(query.getStatus().byteValue());
        }

        if (StringUtils.isNotBlank(query.getCurNameLike())) {
            criteria.andCurNameLike(query.getCurNameLike());
        }

        if (StringUtils.isNotBlank(query.getGoodsNoLike())) {
            criteria.andGoodsNoLike(query.getGoodsNoLike());
        }

        if (StringUtils.isNotBlank(query.getAdjustCodeLike())) {
            criteria.andAdjustCodeLike(query.getAdjustCodeLike());
        }

        if (Objects.nonNull(query.getGmtUpdateGreaterThan())) {
            criteria.andGmtUpdateGreaterThan(query.getGmtUpdateGreaterThan());
        }

        if (Objects.nonNull(query.getSpuId())) {
            criteria.andSpuIdEqualTo(query.getSpuId());
        }

        if (CollectionUtils.isNotEmpty(query.getSpuIdList())) {
            criteria.andSpuIdIn(query.getSpuIdList());
        }

        if (CollectionUtils.isNotEmpty(query.getFilterPriceTypeCodeList())) {
            criteria.andPriceTypeCodeNotIn(query.getFilterPriceTypeCodeList());
        }

        if (Objects.nonNull(query.getChannelStoreId())) {
            criteria.andChannelStoreIdEqualTo(query.getChannelStoreId());
        }

        if (StringUtils.isNotBlank(query.getPriceGroup())){
            criteria.andPriceGroupEqualTo(query.getPriceGroup());
        }
    }

    @Override
    public PriceStoreDetail query(Long storeId, Integer channelId, String priceTypeCode, String goodsNo) {
        return query(storeId, channelId, priceTypeCode, goodsNo, 0L, null,null);
    }

    @Override
    public PriceStoreDetail query(Long storeId, Integer channelId, String priceTypeCode, String goodsNo, Long skuId, Long channelStoreId,String priceGroup) {

        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .channelId(channelId)
            .priceTypeCode(priceTypeCode)
            .goodsNo(goodsNo)
            .skuId(skuId)
            .build();
        if (Objects.nonNull(channelStoreId)) {
            query.setChannelStoreId(channelStoreId);
        }
        if (StringUtils.isNotBlank(priceGroup)) {
            query.setPriceGroup(priceGroup);
        }
        List<PriceStoreDetail> list = query(query);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<PriceStoreDetail> query(Long storeId, String priceTypeCode, String goodsNo) {
        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .channelId(adjustPriceConfig.getDefaultChannelId())
            .priceTypeCode(priceTypeCode)
            .goodsNo(goodsNo)
            .build();

        return query(query);
    }

    @Override
    public long count(PriceStoreDetailQuery query) {
        return priceStoreDetailMapper.countByExample(getPriceStoreDetailExample(query));
    }

    @Override
    public List<PriceStoreDetail> queryWithOr(PriceStoreDetailQuery query, PriceStoreDetailQuery... or) {
        PriceStoreDetailExample example = getPriceStoreDetailExample(query);

        if (Objects.nonNull(or) && or.length > 0) {
            for (PriceStoreDetailQuery q : or) {
                buildCriteria(q, example, true);
            }
        }

        return priceStoreDetailMapper.selectByExample(example);
    }

    @Override
    public long countWithOr(PriceStoreDetailQuery query, PriceStoreDetailQuery... or) {
        PriceStoreDetailExample example = getPriceStoreDetailExample(query);

        if (Objects.nonNull(or) && or.length > 0) {
            for (PriceStoreDetailQuery q : or) {
                buildCriteria(q, example, true);
            }
        }
        return priceStoreDetailMapper.countByExample(example);
    }

    @Override
    public List<PriceStoreDetail> queryByGoodsnoesAndStoreIds(List<String> goodsNoList, List<Long> storeIds,
        List<String> priceTypeCodeList, List<Integer> channelIdList) {
        if (CollectionUtils.isEmpty(goodsNoList) || CollectionUtils.isEmpty(storeIds)) {
            logger.info("PriceStoreDetailReadServiceImpl|queryByStoreIdAndGoodsnoList|商品编列表或者门店列表不能为空");
            return Collections.emptyList();
        }
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andGoodsNoIn(goodsNoList);
        criteria.andStoreIdIn(storeIds);
        criteria.andStatusEqualTo((byte)StatusEnum.NORMAL.getCode());
        if (CollectionUtils.isNotEmpty(channelIdList)) {
            criteria.andChannelIdIn(channelIdList);
        }
        if (CollectionUtils.isNotEmpty(priceTypeCodeList)) {
            criteria.andPriceTypeCodeIn(priceTypeCodeList);
        }

        return priceStoreDetailMapper.selectByExample(example);
    }

    @Override
    public List<String> listGoodsNoListByStoreId(Long storeId) {
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo((byte)StatusEnum.NORMAL.getCode());
        criteria.andStoreIdEqualTo(storeId);
        List<String> result = priceStoreDetailExMapper.queryPriceStoreDetailGoodsNo(example);
        return result == null ? Collections.emptyList() : result;
    }

    @Override
    public List<PriceStoreDetail> queryByStoreIdAndGoodsnoList(Long storeId, List<String> goodsnoList,
            List<Integer> channelIdList, List<String> priceTypeCodeList) {
        if (storeId == null || CollectionUtils.isEmpty(goodsnoList)) {
            logger.info("PriceStoreDetailReadServiceImpl|queryByStoreIdAndGoodsnoList|storeId或者商品列表不能为空, storeId: {}",
                storeId);
            return Collections.emptyList();
        }
        PriceStoreDetailExample example = new PriceStoreDetailExample();
        PriceStoreDetailExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId);
        criteria.andGoodsNoIn(goodsnoList);
        criteria.andStatusEqualTo((byte)StatusEnum.NORMAL.getCode());

        if (CollectionUtils.isNotEmpty(channelIdList)) {
            criteria.andChannelIdIn(channelIdList);
        }
        if (CollectionUtils.isNotEmpty(priceTypeCodeList)) {
            criteria.andPriceTypeCodeIn(priceTypeCodeList);
        }

        return priceStoreDetailMapper.selectByExample(example);
    }
}
