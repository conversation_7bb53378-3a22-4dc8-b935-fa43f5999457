package com.cowell.pricecenter.service.impl;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.enums.OrgTypeEnum;
import com.cowell.pricecenter.enums.ReturnCodeEnum;
import com.cowell.pricecenter.mapper.PriceOrgGoodsIdGeneratorMapper;
import com.cowell.pricecenter.mapper.PriceOrgGoodsMapper;
import com.cowell.pricecenter.mapper.PriceStoreDetailMapper;
import com.cowell.pricecenter.security.SpringSecurityAuditorAware;
import com.cowell.pricecenter.service.IPressureCreatePriceService;
import com.cowell.pricecenter.service.dto.request.PressureCreatePriceParam;
import com.cowell.pricecenter.service.dto.response.PriceDTO;
import com.cowell.pricecenter.service.dto.response.StoreDTO;
import com.cowell.pricecenter.service.feign.PermissionService;
import com.cowell.pricecenter.web.rest.errors.BusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 压测价格生成
 * @Author: liubw
 * @Date: 2019/1/9 3:21 PM
 */
@Service
public class PressureCreatePriceServiceImpl extends BasePermissionService implements IPressureCreatePriceService {

    private final Logger log = LoggerFactory.getLogger(PressureCreatePriceServiceImpl.class);

    @Autowired
    private SpringSecurityAuditorAware springSecurity;
    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private PriceOrgGoodsIdGeneratorMapper idGeneratorMapper;
    @Autowired
    private PriceOrgGoodsMapper priceOrgGoodsMapper;


    @Override
    public void pressureCreatePrice(PressureCreatePriceParam param) {
        try {
            List<Long> orgGoodsIdList = param.getOrgGoodsIdList();
            List<Long> orgIdList = param.getOrgIdList();
            List<PriceDTO> priceDTOList = param.getDtoList();
            Map<Long, PriceOrgGoods> orgGoodsMap = getPriceOrgGoods(orgGoodsIdList);

            //根据orgIds换算出下面所有的storeIdList
            List<StoreDTO> storeIdList = getStoreId(orgIdList);
            log.info("getStoreIdList:{}", storeIdList);
            if (CollectionUtils.isEmpty(storeIdList)) {
                log.warn("根据机构ID未查询到门店！机构列表：【{}】", orgIdList);
                return;
            }
            Map<String, OrgDTO> orgDTOMap = getDirectParentOrgList(storeIdList.stream().map(storeDTO -> storeDTO.getOrgId()).collect(Collectors.toList()));
            log.info("获取所有机构map:{}", orgDTOMap);

            int insertUpdateResult = 0;
            Long userId = 123L;
            for (Long orgGoodsId : orgGoodsIdList) {

                //获取spuId
                PriceOrgGoods goods = orgGoodsMap.get(orgGoodsId);
                Long spuId = goods.getSpuId();
                String goodsNo = goods.getGoodsNo();

                for (PriceDTO dto : priceDTOList) {

                    for (StoreDTO store : storeIdList) {
                        //通过storeId获取组织机构目录，获取到 连锁名称和平台名称存储到map，避免重复调用接口
                        Long storeId = store.getId();
                        if (storeId == null) {
                            continue;
                        }
                        Long orgId = store.getOrgId();

                        PriceStoreDetail priceStoreDetail = new PriceStoreDetail();
                        priceStoreDetail.setPrice(dto.getPrice());
                        priceStoreDetail.setOrgId(123L);
                        priceStoreDetail.setOrgName("压测机构名称");
                        priceStoreDetail.setLevel((byte) 1);
                        priceStoreDetail.setAdjustCode("压测调价单号");
                        priceStoreDetail.setAdjustDetailId(1234L);
                        priceStoreDetail.setUpdatedBy(userId);

                        priceStoreDetail.setAuthOrgId(1L);
                        priceStoreDetail.setAuthOrgName("压测授权机构名称");
                        priceStoreDetail.setAuthOrgLevel((byte) 2);
                        priceStoreDetail.setStoreId(storeId);
                        priceStoreDetail.setStoreName(store.getName());
                        priceStoreDetail.setSpuId(spuId);
                        priceStoreDetail.setGoodsNo(goodsNo);
                        priceStoreDetail.setPriceTypeCode(dto.getCode());
                        priceStoreDetail.setPriceTypeId(1L);
                        priceStoreDetail.setPriceTypeName("压测价格类型名称");
                        OrgDTO businessOrgDTO = orgDTOMap.get("" + orgId + OrgTypeEnum.BUSINESS.getCode());
                        OrgDTO platformOrgDTO = orgDTOMap.get("" + orgId + OrgTypeEnum.REGIONAL_PLATFORM.getCode());
                        if (businessOrgDTO != null) {
                            priceStoreDetail.setBusinessId(businessOrgDTO.getOutId());
                            priceStoreDetail.setBusinessName(businessOrgDTO.getShortName());
                        }
                        if (platformOrgDTO != null) {
                            priceStoreDetail.setPlatformId(platformOrgDTO.getOutId());
                            priceStoreDetail.setPlatformName(platformOrgDTO.getShortName());
                        }
                        priceStoreDetail.setOrgGoodsId(orgGoodsId);
                        String curName = orgGoodsMap.get(orgGoodsId) == null ? "" : orgGoodsMap.get(orgGoodsId).getCurName();
                        String barCode = orgGoodsMap.get(orgGoodsId) == null ? "" : orgGoodsMap.get(orgGoodsId).getBarCode();
                        String opCode = orgGoodsMap.get(orgGoodsId) == null ? "" : orgGoodsMap.get(orgGoodsId).getOpCode();
                        priceStoreDetail.setCurName(curName);
                        priceStoreDetail.setBarCode(barCode);
                        priceStoreDetail.setOpCode(opCode);
                        priceStoreDetail.setExtend(curName + "_" + barCode + "_" + opCode);
                        priceStoreDetail.setCreatedBy(userId);
                        priceStoreDetail.setStatus((byte) 5);
                        priceStoreDetail.setVersion(5);

                        String priceTypeCode = dto.getCode();
                        PriceStoreDetailExample example = new PriceStoreDetailExample();
                        example.createCriteria().andStoreIdEqualTo(storeId).andSpuIdEqualTo(spuId).andPriceTypeCodeEqualTo(priceTypeCode);
                        long count  = priceStoreDetailMapper.countByExample(example);

                        if (count > 0) {
                            log.info("更新priceStoreDetail，参数:{}", priceStoreDetail);
                            insertUpdateResult = priceStoreDetailMapper.updateByExampleSelective(priceStoreDetail, example);
                        } else {
                            //获取自增ID
                            PriceOrgGoodsIdGenerator idGenerator = new PriceOrgGoodsIdGenerator();
                            idGeneratorMapper.replaceInto(idGenerator);
                            Long autoId = idGenerator.getId();
                            Long primaryKey = Long.parseLong(autoId.toString().concat(storeId.toString()));

                            priceStoreDetail.setId(primaryKey);

                            log.info("新增priceStoreDetail，参数:{}", priceStoreDetail);
                            insertUpdateResult = priceStoreDetailMapper.insertSelective(priceStoreDetail);
                        }
                    }
                }
            }
            log.info("insertUpdateResult结果:{}", insertUpdateResult);
        } catch (Exception e) {
            log.warn("批量压测异常", e);
            throw e;
        }

    }

    public static List removeDuplicate(List list) {
        HashSet h = new HashSet(list);
        list.clear();
        list.addAll(h);
        return list;

    }

    private List<StoreDTO> getStoreId(List orgIdList) {
        List<StoreDTO> storeDTOList = new ArrayList<>();
        //zc add去重
        log.info("根据id集合批量获取组织机构信息开始,过滤orgIdList前:{}", orgIdList);
        removeDuplicate(orgIdList);
        log.info("根据id集合批量获取组织机构信息开始,过滤orgIdList后:{}", orgIdList);


        //集团 平台 ,查询下面到门店
        Integer[] orgTypes = new Integer[1];
        orgTypes[0] = OrgTypeEnum.STORE.getCode();

        log.info("根据类型批量查询组织机构节点下指定类型组成的阉割版的树开始,参数:{}", orgIdList);
        ResponseEntity<Map<Long, List<OrgTreeDTO>>> responseEntity;
        try {
            responseEntity = permissionService.listOrgTreesByRootOrgIdAndTypesBatch(orgIdList, orgTypes);

        } catch (Exception e) {
            log.warn("根据类型批量查询组织机构节点下指定类型组成的阉割版的树异常:", e);
            throw new BusinessException(ReturnCodeEnum.GET_AUTH_ERROR);
        }
        log.info("根据类型批量查询组织机构节点下指定类型组成的阉割版的树结束,查询结果:{}", responseEntity.getBody());
        Map<Long, List<OrgTreeDTO>> treeMap = responseEntity.getBody();
        treeMap.forEach((key, val) -> addStoreDTO(val, storeDTOList));

        return storeDTOList;
    }

    private Map<Long, PriceOrgGoods> getPriceOrgGoods(List<Long> orgGoodIds) {
        Map<Long, PriceOrgGoods> map = new HashMap<>();
        PriceOrgGoodsExample goodsExample = new PriceOrgGoodsExample();
        goodsExample.createCriteria().andIdIn(orgGoodIds);
        List<PriceOrgGoods> priceOrgGoodsList = priceOrgGoodsMapper.selectByExample(goodsExample);
        for (PriceOrgGoods goods : priceOrgGoodsList) {
            map.put(goods.getId(), goods);
        }
        return map;
    }

}
