package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.PriceDictionaryQueryParam;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.PriceDictionaryDTO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;

import java.util.List;

/**
 * Created by schuangxigang on 2022/7/4 15:34.
 */
public interface PriceDictionaryService {
    PageResult<PriceDictionaryDTO> pageList(PriceDictionaryQueryParam param);

    void save(PriceDictionaryDTO dictionaryDTO, TokenUserDTO userDTO);

    PriceDictionaryDTO getById(Long id);

    PriceDictionaryDTO getByCode(String pCode, String code);

    PriceDictionaryDTO getOneByCode(String code);

    List<PriceDictionaryDTO> getByParentId(Long parentId);

    List<PriceDictionaryDTO> getChildrenByCode(String code);

    List<PriceDictionaryDTO> getAllChildrenByDictType(int dictType);

    List<PriceDictionaryDTO> getAllByParentId(Long parentId);

    List<PriceDictionaryDTO> getByCodes(String pCode, List<String> codes);

    List<PriceDictionaryDTO> getByIds(List<Long> ids);

}
