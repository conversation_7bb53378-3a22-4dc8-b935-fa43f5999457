package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.*;
import com.cowell.pricecenter.mq.vo.ControlOrderDetailSupplementDataVO;
import com.cowell.pricecenter.service.dto.AdjustPriceOrderDefaultDTO;
import com.cowell.pricecenter.service.dto.OaSource;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.AdjustImportResultDTO;
import com.cowell.pricecenter.service.dto.response.ExportFileCubeVO;
import com.cowell.pricecenter.service.dto.response.amis.CommonResult;

import java.util.List;
import java.util.Map;

public interface IPriceManageControlOrderService {

    int deleteByExample(PriceManageControlOrderExample example);

    int insert(PriceManageControlOrder record);

    int insertSelective(PriceManageControlOrder record);

    int updateByExampleSelective(PriceManageControlOrder record, PriceManageControlOrderExample example);

    int updateByExample(PriceManageControlOrder record, PriceManageControlOrderExample example);

    int deleteByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PriceManageControlOrder record);

    int updateByPrimaryKey(PriceManageControlOrder record);

    /**
     * 生成管控单门店商品明细
     * @param controlOrderCode
     */
    void generateControlStoreDetail(String controlOrderCode);

    /**
     * 批量更新管控单明细生效状态
     * @param ids 管控单明细主键id列表
     * @param effectStatus 生效状态
     * @see com.cowell.pricecenter.enums.EffectStatusEnum
     */
    void batchUpdateControlOrderDetail(List<Long> ids, Integer effectStatus);

    /**
     * 批量更新管控单状态
     * @param controlCodes 管控单编码
     * @param effectStatus 管控单状态
     */
    void batchUpdateControlOrder(List<String> controlCodes, Integer effectStatus);

    CommonResult addControlOrder(ControlOrderEditParam param, TokenUserDTO userDTO);

    Long editControlOrder(ControlOrderEditParam param);

    Long editRuleControlOrder(ControlOrderEditParam param);

    Integer deleteControlOrder(Long controlOrderId);

    void controlOrderGoodsAdd(ControlOrderGoodsParam param, TokenUserDTO userDTO);

    /**
     * 批量生成调价单
     * @param param
     * @param userDTO
     * @return
     */
    List<Long> batchOrSingleAddAdjustPriceOrder(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO);

    /**
     * 价格管控通知-批量或单个生成不调价申请
     * @param param
     * @param userDTO
     */
    void batchOrSingleAddNotControlApply(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO);

    /**
     * 通过管控单主键ID提交审核
     * @param controlOrderId
     * @param userDTO
     */
    void submitControlOrderToAuditById(Long controlOrderId, TokenUserDTO userDTO);

    /**
     * 批量删除商品明细
     * @param param
     */
    void deleteGoodsDetail(ControlGoodsDelParam param);

    /**
     * 保存管控单
     * @param id
     * @param userDTO
     */
    void saveControlOrder(Long id, TokenUserDTO userDTO);

    /**
     * 判断管控单是否补全数据完成
     * @param id
     * @param userDTO
     * @return
     */
    boolean isControlOrderDetailImportCompleted(Long id, TokenUserDTO userDTO);

    /**
     * 编辑管控单
     * @param param
     * @param userDTO
     */
    void editControlOrderDetail(AmisTableEditV2Param param, TokenUserDTO userDTO);

    /**
     * 补全数据
     * @param mqResult
     */
    void supplementControlOrderDetailData(ControlOrderDetailSupplementDataVO mqResult);

    /**
     * 审核状态修改
     */
    void auditBackUpdateStatusByCode(OaSource oaSource);

    /**
     * 编辑不执行管控单
     * @param param
     */
    void editNotControlOrder(ControlNotOrderEditParam param);

    /**
     * 立即保存管控单明细
     * @param param
     * @param userDTO
     */
    void immediatelySaveAndEditControlOrderDetail(Map<String, Object> param, TokenUserDTO userDTO);

    /**
     * 自动批量生成调价单并通知OA
     * @param param
     * @return
     */
    void autoBatchOrSingleAddAdjustPriceOrder(List<PriceControlNotice> param);

    /**
     * 管控单明细导入
     * @param userDTO
     */
    void importControlOrderDetails(ControlOrderImportV2Param param, TokenUserDTO userDTO);

    /**
     * 获取管控单导入结果
     * @param param
     * @return
     */
    AdjustImportResultDTO getAdjustImportResult(Long controlId);
    /**
     * 异步导出异常管控单明细文件到下载中心
     * @param adjustPriceOrderId
     */
    ExportFileCubeVO<Map<String, Object>> asyncExportErrorControlOrderDetailsFile(Long controlId, Integer page, Integer pageSize);

    List<Long> batchOrSingleAddAdjustPriceOrderByIdList(ControlNoticeAddAdjustParam param, TokenUserDTO userDTO);

    AdjustPriceOrder autoBuildInsertAdjustPriceOrder(AdjustPriceOrderDefaultDTO orderDefault, TokenUserDTO userDTO, Long userOrgId);
}
