package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.PriceChannel;
import com.cowell.pricecenter.entity.PriceManageControlOrder;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.service.dto.PriceOrderPriceTypeAndChannelChange;
import com.cowell.pricecenter.service.dto.request.AdjustPriceOrderListV2Param;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.feign.vo.PriceModeResult;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;

/**
 * @program: pricecenter
 * @description: 调价单、管控单的基础服务接口
 * @author: jmlu
 * @create: 2022-03-31 10:09
 **/

public interface IBasePriceOrderService {

    /**
     * 检查AdjustPriceOrder对象是否为NULL，为NULL则抛出异常
     * @param adjustPriceOrder
     */
    void checkAdjustPriceOrderIsNull(AdjustPriceOrder adjustPriceOrder);

    /**
     * 检查AdjustPriceOrder对象是否为NULL且调价单是否是提交用户所拥有，是NULL或者不是用户拥有则抛出异常
     * @param adjustPriceOrder
     * @param userDTO
     */
    void checkAdjustPriceOrderIsNullAndOwner(AdjustPriceOrder adjustPriceOrder, TokenUserDTO userDTO);

    /**
     * 检查AdjustPriceOrder对象是否为NULL和必有数据是否为NULL，为NULL则抛出异常
     * @param adjustPriceOrder
     */
    void checkAdjustPriceOrderMustHasProperties(AdjustPriceOrder adjustPriceOrder);

    /**
     * 把逗号分隔的渠道Ids转成渠道ID列表
     * @param channelIdsStr
     * @return
     */
    List<Integer> getChannelIdListFromStr(String channelIdsStr);

    /**
     * 把逗号分隔的价格类型ID字符串转成价格类型ID列表
     * @param priceTypeCodesStr
     * @return
     */
    List<String> getPriceTypeCodeListFromStr(String priceTypeCodesStr);

    /**
     * 校验用户是否拥有提供的价格类型，渠道类型，商品范围
     * @param userId
     * @param priceTypeCodelist
     * @param channelIdList
     * @param goodsScope
     */
    void checkUserPriceDimensiones(long userId,  long resourceId, List<String> priceTypeCodelist, List<Integer> channelIdList, Integer goodsScope);

    /**
     * 检查检查AdjustPriceOrder对象是否为NULL且调价单信息和明细是否可以更新或者删除，为NULL或者不能更新或者删除抛出异常
     * @param adjustPriceOrder
     */
    void checkAdjustPriceOrderIsNullAndCanChange(AdjustPriceOrder adjustPriceOrder);

    /**
     * 检查检查priceManageControlOrder对象是否为NULL且调价单信息和明细是否可以更新或者删除，为NULL或者不能更新或者删除抛出异常
     * @param priceManageControlOrder
     */
    void checkControlOrderIsNullAndCanChange(PriceManageControlOrder priceManageControlOrder);

    /**
     * 根据调价明细Amis表格的AmisDiffRow获取价格类型Code列表
     * @param columnNames
     * @return
     */
    List<String> getPriceTypeCodeListFromAmisDiffRow(Set<String> columnNames);

    /**
     * 根据价格类型编码（逗号分隔）获取Code为Key的Map，返回的个数不同则抛出异常
     * @param priceTypeCodesStr
     * @return
     */
    Map<String, PriceType> getPriceTypesByCodes(String priceTypeCodesStr);

    /**
     * 根据价格类型编码列表获取Code为Key的Map，返回的个数不同则抛出异常
     * @param priceTypeCodeList
     * @return
     */
    Map<String, PriceType> getPriceTypesByCodes(List<String> priceTypeCodeList);

    /**
     * 根据商品编码，价格类型，组织机构ID列表获取众数，假如单个门店，则返回商品在门店现价
     * @param goodsNo
     * @param priceTypeCode
     * @param orgLevelVOList
     * @param priceModelResultOptional
     * @param channelIdList
     */
    Optional<BigDecimal> getPriceMode(String goodsNo, String priceTypeCode, List<OrgLevelVO> orgLevelVOList,
        Optional<PriceModeResult> priceModelResultOptional, List<Integer> channelIdList) ;

    /**
     * 根据渠道ID（逗号分隔）获取Code为Key的Map，返回的个数不同则抛出异常
     * @param channelIds
     * @return
     */
    Map<Integer, PriceChannel> getPriceChannelByChannelIds(String channelIds);

    /**
     * 根据渠道ID列表获取Code为Key的Map，返回的个数不同则抛出异常
     * @param channelIdList
     * @return
     */
    Map<Integer, PriceChannel> getPriceChannelByChannelIds(List<Integer> channelIdList);

    /**
     * 获取调价单明细下载名称
     * @param adjustCode
     * @return
     */
    String getAdjustPriceOrderDetailDownloadFileName(String adjustCode);

    /**
     * 把调价单中生效时间和制单时间拆分存储
     * @param param
     * @return
     */
    AdjustPriceOrderListV2Param dealDateRanges(AdjustPriceOrderListV2Param param);

    /**
     * 检查PriceManageControlOrder对象是否为NULL和必有数据是否为NULL，为NULL则抛出异常
     * @param controlOrder
     */
    void checkControlOrderMustHasProperties(PriceManageControlOrder controlOrder);

    /**
     * 根据选择的组织机构数据找到用户拥有所有下级组织机构ID, 有组织树列表数据优先取，没有取所选平台组织机构
     * @param param
     * @param userDTO
     * @return
     */
    AdjustPriceOrderListV2Param dealOrgIdList(AdjustPriceOrderListV2Param param, TokenUserDTO userDTO);

    /**
     * 记录新建审核日志
     * @param orderCode 订单编码
     * @param priceOrderTypeEnum
     * @param userDTO 操作用户
     * @param operateTime 操作时间
     */
    void recordNewAuditLog(String orderCode, PriceOrderTypeEnum priceOrderTypeEnum, OrderItemType orderItemType, OrderStatus orderStatus, TokenUserDTO userDTO, Date operateTime);

    /**
     * 提交OA审核
     * @param orderCode 订单Code
     * @param orderName 订单名称
     * @param effectiveTime
     * @param orderReason 修改理由
     * @param priceOrderTypeEnum
     * @param userDTO
     * @param operateTime
     */
    void submitOAAduit(String orderCode, String orderName, Date effectiveTime, String orderReason,
                       PriceOrderTypeEnum priceOrderTypeEnum, OrderItemType orderItemType, OrderStatus orderStatus, OaJumpUrlKeyEnum oaJumpUrlKeyEnum,
                       TokenUserDTO userDTO, Date operateTime,Integer blankType, Integer orgType, String jmBusiness);

    /**
     * 校验调价单审核成功审核失败的状态校验
     * @param adjustPriceOrder
     * @param auditStatusEnum 审核成功或者审核失败
     */
    void checkAdjustPriceOrderIsNullAndLastAudit(AdjustPriceOrder adjustPriceOrder, AuditStatusEnum auditStatusEnum);

    /**
     * 根据调价单编码获取调价单信息
     * @param adjustCode
     * @return
     */
    AdjustPriceOrder getAdjustPriceOrderByAdjustCode(String adjustCode);


    /**
     * 根据调价单Id获取调价单信息
     * @param adjustPriceOrderId
     * @return
     */
    AdjustPriceOrder getAdjustPriceOrderById(Long adjustPriceOrderId);

    /**
     * 根据调价单ID获取调价单的组织机构对象
     * @param adjustPriceOrderId
     * @return
     */
    List<OrgLevelVO> listOrgListByAdjustPriceOrderId(Long adjustPriceOrderId);

    /**
     * 检查管控单对象是否为NULL且调价单是否是提交用户所拥有，是NULL或者不是用户拥有则抛出异常
     * @param priceManageControlOrder
     * @param userDTO
     */
    void checkControlOrderIsNullAndOwner(PriceManageControlOrder priceManageControlOrder, TokenUserDTO userDTO);

    /**
     * 管控单判空
     * @param priceManageControlOrder
     */
    void checkControlOrderIsNull(PriceManageControlOrder priceManageControlOrder);

    /**
     * 渠道和价格类型修改情况
     * @param channelIds
     * @param priceTypeCodes
     * @param newChannelIdList
     * @param newPriceTypeCodeList
     * @return
     */
    PriceOrderPriceTypeAndChannelChange getChangeChannelAndPriceType(String channelIds, String priceTypeCodes, List<Integer> newChannelIdList, List<String> newPriceTypeCodeList);

    /**
     * 通过编码查询管控单
     * @param controlOrderCode
     * @return
     */
    PriceManageControlOrder getPriceManageControlOrderByCode(String controlOrderCode);

    /**
     * 是否需要特殊审核
     * @param adjustPriceOrderId
     * @return
     */
    boolean isNeedSpecialAuditByAdjustPriceOrderId(Long adjustPriceOrderId);

    /**
     * 是否今天i以后
     * @param scheduledTimeSecondLong 日期转化的秒（2020-12-12 00:00:00）
     * @return
     */
    boolean isAfterAndEqualToday(long scheduledTimeSecondLong);

    /**
     * 文件大小是否超限 true-是  false-否
     * @param file
     * @return
     */
    boolean fileSizeExceed(MultipartFile file);

    /**
     * 是否包含已生效的管控单
     * @param controlOrderNoList
     * @return
     */
    boolean containsEffectControlOrder(List<String> controlOrderNoList);

    /**
     * 获取管控单的生效状态
     * @param controlOrderNoList
     * @return
     */
    Map<String, PriceManageControlOrder> getEffectStatusAndControlOrder(List<String> controlOrderNoList);

    /**
     * 渠道ID列表是否都存在
     * @return
     */
    boolean isAllPriceChannelIdExist(List<Integer> channelIdList);

    /**
     * 渠道ID列表是否都存在
     * @return
     */
    boolean isAllPriceTypeCodeExist(List<String> priceTypeCodeList);

    /**
     * 价格类型排序
     * @param priceTypeCodesStr
     * @return
     */
    List<PriceType> getSortedPriceTypeList(String priceTypeCodesStr);

    /**
     * 价格类型排序
     * @param priceTypeCodeList
     * @return
     */
    List<PriceType> getSortedPriceTypeList(List<String> priceTypeCodeList);

}


