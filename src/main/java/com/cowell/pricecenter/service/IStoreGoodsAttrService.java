package com.cowell.pricecenter.service;

import java.util.Date;
import java.util.List;

import com.cowell.pricecenter.entity.StoreGoodsAttr;

/**
 * @Description: 门店商品属性服务
 * @author: mayue
 * @date:   2025年4月22日 下午5:40:33
 */
public interface IStoreGoodsAttrService {
	/**
     * 批量保存
     * @param list
     * @return
     */
    int batchInsert(List<StoreGoodsAttr> list);

    /**
     * 根据连锁id、时间批量删除
     * @param businessId 连锁id
     * @param storeIdList 门店id集合
     * @param deleteBeforeNow 删除小于该时间的数据
     * @param limit 每次删除数据量
     */
    void deleteBatch(Long businessId,List<Long> storeIdList,Date deleteBeforeNow,Integer limit);
}
