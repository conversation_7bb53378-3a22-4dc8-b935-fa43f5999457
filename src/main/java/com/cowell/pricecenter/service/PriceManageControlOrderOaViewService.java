package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.controlOrder.*;

import java.util.Map;

/**
 * Created by schuangxigang on 2022/4/21 19:10.
 */
public interface PriceManageControlOrderOaViewService {
    /**
     * 获取管控单基本信息(OA查看)
     *
     * @param orderNo
     * @return
     */
    PriceManageControlOrderVO getControlOrderBaseInfo(String orderNo);

    /**
     * 获取管控单简单信息(OA查看)
     *
     * @param orderNo
     * @return
     */
    ControlOrderSimpleDTO getControlOrderSimpleInfo(String orderNo);


    /**
     * 管控单明细带分页(OA查看)
     *
     * @param orderNo
     * @param page
     * @param pageSize
     * @return
     */
    PageResult<Map<String, Object>> pageControlOrderDetailList(String orderNo, Integer page, Integer pageSize);

    /**
     * 不执行管控单明细列表(OA查看)
     *
     * @param orderNo
     * @param page
     * @param pageSize
     * @return
     */
    PageResult<ControlOrderDetailListVO> pageNonExecControlOrderDetailList(String orderNo, Integer page, Integer pageSize);

    /**
     * 商品列表
     *
     * @param orderNo
     * @param page
     * @param pageSize
     * @return
     */
    PageResult<ControlOrderDetailListVO> pageGoodsList(String orderNo, Integer page, Integer pageSize);

    /**
     * 商品列表
     *
     * @param orderNo
     * @param page
     * @param pageSize
     * @param isNonExecControl  是否不执行管控单
     * @return
     */
    PageResult<ControlOrderDetailListVO> pageGoodsList(String orderNo, Integer page, Integer pageSize, boolean isNonExecControl);
}
