package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.enums.SystemCodeEnum;
import com.cowell.pricecenter.service.dto.HdBDataDTO;
import com.cowell.pricecenter.service.dto.HdDataDetailDTO;
import com.cowell.pricecenter.service.dto.HdTableDTO;
import com.cowell.pricecenter.service.dto.SendRequestBodyDTO;
import com.cowell.pricecenter.utils.Md5Utils;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.Assert;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description: 海典service
 * @date 2020/03/06 23:15
 */
@Slf4j
@Service
public class HdService {

    private static final String BTYPE_PRICE = "2009";

    private static final String PRICE_REQUEST_URL = "/gaoji-web/v_1_0/storeprice/demand.do";

//    120(开发)---> host.addr = http://**********:8088/gaoji-web
//    300(测试)---> host.addrL = http://*********:8096/gaoji-web
//    600(预生产)---> host.addr = http://*********:5672/gaoji-web
//    800(生产)---> host.addr = http://hpos.gaojihealth.com:8090/gaoji-web
//    @Value("${noticeHDSendData:http://*********:5672}")
    @Value("${noticeHDSendData}")
    private String noticeHDSendData;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 调用海典补偿价格数据
     * @param compId
     * @param busNO
     * @param goodsNoList
     * @return
     */
    public String callBackHdCompensate(String compId,String busNO,List<String> goodsNoList){
        Assert.notNull(compId,"MDM企业编码不能为空");
        Assert.notNull(busNO,"MDM门店编码不能为空");
        Assert.notEmpty(goodsNoList,"商品编码不能为空");
        //明细列表
        List<HdDataDetailDTO> details = getHdDataDetails(goodsNoList);
        if(CollectionUtils.isEmpty(details)){
            log.warn("<==HdService||callBackHdCompensate||组装海典商品价格明细列表为空,请求数据:compId:{}||busNO:{}||{}", compId,busNO,JSONObject.toJSONString(goodsNoList));
            return null;
        }
        //组装table数据
        List<HdTableDTO> tables = getTables(compId,busNO, details);
        //组装请求数据
        JSONObject requestParam = new JSONObject();
        requestParam.put("Table", tables);
        SendRequestBodyDTO sendBody = new SendRequestBodyDTO();
        sendBody.setRequestUrl(noticeHDSendData + PRICE_REQUEST_URL);
        sendBody.setRequestBody(requestParam.toJSONString());
        //接口描述信息
        sendBody.setServiceDesc("价格对比服务");
         return sendRequest(sendBody);
    }

    /**
     * 发送请求数据
     * @param sendBody
     * @return
     */
    private String sendRequest(SendRequestBodyDTO sendBody) {
        log.info("==>HdService||sendRequest||请求数据:{}", JSON.toJSONString(sendBody));
        HttpHeaders requestHeaders = new HttpHeaders();
        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        requestHeaders.setContentType(type);
        requestHeaders.add("Accept", MediaType.APPLICATION_JSON.toString());
        //请求
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(sendBody.getRequestUrl(), new HttpEntity(sendBody.getRequestBody(), requestHeaders), String.class);
        Assert.notNull(responseEntity,  "ERP请求相应失败!");
        String objStr = responseEntity.getBody();
        if(StringUtils.isEmpty(objStr)){
            throw new BusinessErrorException("调用海典系统返回数据为空");
        }
        JSONObject jsonObject = JSONObject.parseObject(objStr);
        String result = jsonObject.getJSONArray("Table").getJSONObject(0).getJSONObject("bdata").getString("code");
        if (!"200".equals(result)) {
            log.error("<==HdService|sendRequest|调用海典返回错误:{}",objStr);
            throw new BusinessErrorException("调用海典系统获取商品价格失败");
        }
        return result;
    }

    /**
     * 组装table数据
     * @param comId
     * @param busNO
     * @param details
     * @return
     */
    private List<HdTableDTO> getTables(String comId,String busNO,List<HdDataDetailDTO> details){
        List<HdTableDTO> tables = Lists.newArrayList();
        HdTableDTO tableDTO = new HdTableDTO();
        tableDTO.setBguid(UUID.randomUUID().toString().replaceAll("-","").toUpperCase());
        tableDTO.setBtype(BTYPE_PRICE);
        tableDTO.setBsource(SystemCodeEnum.POS.getValue());
        tableDTO.setBdestination(SystemCodeEnum.HD_POS.getValue());
        tableDTO.setBdatetime(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        tableDTO.setBstatus("1");
        tableDTO.setBcallback("");
        tableDTO.setBversion("v_1_1");
        tableDTO.setBkeys("");
        HdBDataDTO dataDTO = new HdBDataDTO();
        dataDTO.setCompId(comId);
        dataDTO.setBusNO(busNO);
        dataDTO.setDetails(details);
        tableDTO.setBdata(dataDTO);
        tableDTO.setBdatahash(Md5Utils.MD5Encode(JSONObject.toJSONString(dataDTO)));
        tables.add(tableDTO);
        return tables;
    }

    /**
     * 明细列表
     * @param goodsNoList
     * @return
     */
    private static List<HdDataDetailDTO> getHdDataDetails(List<String> goodsNoList){
        Assert.notEmpty(goodsNoList,"商品编码不能为空");
        List<HdDataDetailDTO> details = Lists.newArrayList();
        for (int i = 0; i < goodsNoList.size(); i++) {
            HdDataDetailDTO detail = new HdDataDetailDTO();
            detail.setGoodsNo(goodsNoList.get(i));
            detail.setRowNo(i);
            details.add(detail);
        }
        return details;
    }

}
