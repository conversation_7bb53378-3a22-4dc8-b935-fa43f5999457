package com.cowell.pricecenter.service.apollo;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version ChannelConfigProperties.java, 2019/7/25 10:51
 */
@Component
@ConfigurationProperties
public class SyncPriceChannelConfigProperties {
    private static Logger log = LoggerFactory.getLogger(SyncPriceChannelConfigProperties.class);

    @Value("${sync.price.channel.store:}")
    private String configString;

    @Value("${no.sync.price.channel.store.id:}")
    private String noSyncPriceChannelStoreId;


    public boolean isSyncPrice(Long businessId, Long storeId) {

        if(StringUtil.isBlank(configString)){
           return false;
        }
        JSONObject config = JSONObject.parseObject(configString);
        log.info("连锁 :{} 同步价格门店配置列表 :{}" ,businessId, configString);
        String str = config.getString(businessId.toString());
        if(StringUtil.isBlank(str)){
            return false;
        }
        List<String> stores = Arrays.asList(str.split(","));
        if(stores.contains(storeId.toString()) || stores.contains("1234567890")){
            return true;
        }
        return false;
    }

    public boolean isNoSyncPriceByChannelStoreId(String channelStoreId){
        log.info("不同步价格门店配置列表 :{}" , noSyncPriceChannelStoreId);
        if(StringUtil.isBlank(noSyncPriceChannelStoreId) || StringUtils.isEmpty(channelStoreId)){
            return false;
        }
        List<String> storeIds = Arrays.asList(noSyncPriceChannelStoreId.split(","));
        return storeIds.stream().anyMatch(item->item.equals(channelStoreId));
    }


}
