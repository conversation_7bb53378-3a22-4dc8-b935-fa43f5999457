package com.cowell.pricecenter.service.apollo;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version ChannelConfigProperties.java, 2019/7/25 10:51
 */
@Component
@ConfigurationProperties
public class SyncPriceToCubeConfigProperties {
    private static Logger log = LoggerFactory.getLogger(SyncPriceToCubeConfigProperties.class);

    @Value("${sync.price.cube.businessId:}")
    private String syncCubeBusinessId;

    public boolean isSyncPriceToCube(Long businessId) {
        if(StringUtil.isBlank(syncCubeBusinessId)){
            return false;
        }
        List<String> businessIds = Arrays.asList(syncCubeBusinessId.split(","));
        if(businessIds.contains(businessId.toString()) || businessIds.contains("12345")){
            return true;
        }
        return false;
    }
}
