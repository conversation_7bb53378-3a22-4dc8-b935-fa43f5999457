package com.cowell.pricecenter.service.apollo;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 类说明
 *
 * <AUTHOR>
 * @version ChannelConfigProperties.java, 2019/7/25 10:51
 */
@Component
@ConfigurationProperties
public class SyncPriceAppointCompIdProperties {
    private static Logger log = LoggerFactory.getLogger(SyncPriceAppointCompIdProperties.class);

    @Value("${sync.price.appoint.compId:}")
    private String configString;

    /**
     * 指定连锁开关是否开启
     * @return
     */
    public boolean isSwitchOnAppointCompId() {

        if(StringUtil.isBlank(configString)){
            return false;
        }
        JSONObject config = JSONObject.parseObject(configString);

        String switchStatus = config.getString("switch");

        if (StringUtils.isBlank(switchStatus)){
            return false;
        }

        return switchStatus.equals("1");

    }


    /**
     * 指定连锁编码 是否包含穿过来的 连锁
     * @param compId
     * @return
     */
    public boolean isSwitchOnAndContainAppointCompId(String compId) {

        if(StringUtil.isBlank(configString)){
           return false;
        }
        JSONObject config = JSONObject.parseObject(configString);
        log.info("连锁 :{} 同步价格门店配置列表 :{}" ,compId, configString);
        String switchStatus = config.getString("switch");
        if (StringUtils.isBlank(switchStatus) || switchStatus.equals("0")){
            return false;
        }
        String compIdsConfig = config.getString("compIds");
        if (StringUtils.isBlank(compIdsConfig)){
            return false;
        }
        List<String> compIds = Arrays.asList(compIdsConfig.split(","));
        return compIds.contains(compId);
    }


}
