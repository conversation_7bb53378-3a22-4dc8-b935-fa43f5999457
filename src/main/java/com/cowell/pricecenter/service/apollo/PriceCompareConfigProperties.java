package com.cowell.pricecenter.service.apollo;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 类说明 价格同步 华南连锁编码
 *
 * <AUTHOR>
 * @version ChannelConfigProperties.java, 2019/7/25 10:51
 */
@Component
@ConfigurationProperties
public class PriceCompareConfigProperties {
    private static Logger log = LoggerFactory.getLogger(PriceCompareConfigProperties.class);

    @Value("${price.compare.huannan.comId:}")
    private String configString;


    public boolean isHuaNanComId(String comId) {
        log.info("连锁 :{} 华南连锁编码配置列表 :{}" ,comId, configString);
        if(StringUtil.isBlank(configString)){
            return false;
        }
        List<String> comIds = Arrays.asList(configString.split(","));
        if(CollectionUtils.isEmpty(comIds)){
            return false;
        }
        return comIds.stream().anyMatch(item->item.equals(comId));
    }

}
