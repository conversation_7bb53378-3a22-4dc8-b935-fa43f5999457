package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrderDetail;
import com.cowell.pricecenter.service.dto.response.AdjustOrderDetailGoodsExtend1;
import java.util.List;
import java.util.Map;

/**
 * @program: pricecenter
 * @description: 调价单明细2.0版本的读服务接口
 * @author: jmlu
 * @create: 2022-09-20 15:24
 **/

public interface IAdjustPriceOrderDetailV2ReadService {

    /**
     * 根据调价单号和明细ID列表获取明细数据列表
     * @param adjustCode
     * @param detailIds
     * @return
     */
    List<AdjustPriceOrderDetail> listByAdjustCodeAndDetailIds(String adjustCode, List<Long> detailIds);

    /**
     * 根据调价单号和明细ID列表获取明细数据Map,DetailId作为Key
     * @param adjustCode
     * @param detailIds
     * @return
     */
    Map<Long, AdjustPriceOrderDetail> getMapByAdjustCodeAndDetailIds(String adjustCode, List<Long> detailIds);

    /**
     * 根据调价单号获取商品编码和扩展字段extend1
     * @param adjustCode
     * @return
     */
    List<AdjustOrderDetailGoodsExtend1> listGoodsExtend1ByAdjustCode(String adjustCode);
}
