package com.cowell.pricecenter.service.thread;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 批处理容器
 */
@Component
public class BatchContainerExecutor {
    private static final Logger logger = LoggerFactory.getLogger("BatchContainerExecutor");

    private static int threadNumLocal  = Runtime.getRuntime().availableProcessors() * 2;

    private static ExecutorService pool = Executors.newFixedThreadPool(threadNumLocal);

    /**
     *提交事务
     * @param task
     * @return
     */
    public Object execute(Callable<Object> task) {
        Object results = null;
        ExecutorCompletionService<Object> concurrentExecutor = new ExecutorCompletionService<Object>(pool);

        concurrentExecutor.submit(task);
        return results;
    }

    /**
     * 并发处理完所有的任务
     * @param taskList
     */
    public List<Object> execute(List<Callable<Object>> taskList) {
        int taskSize = taskList.size();
        List<Object> results = new ArrayList<Object>(taskSize);
        ExecutorCompletionService<Object> concurrentExecutor = new ExecutorCompletionService<Object>(pool);
        for (Callable<Object> callable : taskList) {
            concurrentExecutor.submit(callable);
        }
        for (int i = 0; i < taskSize; i++) {
            Object result = getQuenueMsg(concurrentExecutor);
            if(result!=null && StringUtils.isNotBlank(result.toString())){
                results.add(result);
                return results;
            }
        }
        return results;
    }

    /**
     * 获取消息
     * @param concurrentExecutor
     * @return
     */
    private Object getQuenueMsg(ExecutorCompletionService<Object> concurrentExecutor){
        Object result = null;
        try {
            result = concurrentExecutor.poll(10, TimeUnit.SECONDS).get(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            logger.warn("getQuenueMsg_InterruptedException",e);
            return e.getMessage();
        } catch (Exception e) {
            logger.warn("getQuenueMsg_Exception",e);
            return e.getMessage();
        }
        return result;
    }

    public <V> Future<V> submit(Callable<V> callable){
        return pool.submit(callable);
    }
}
