package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrderDetail;
import com.cowell.pricecenter.entity.AdjustPriceOrderDetailExample;
import com.cowell.pricecenter.mapper.AdjustPriceOrderDetailMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderDetailExMapper;
import com.cowell.pricecenter.service.dto.response.AdjustOrderDetailGoodsExtend1;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: pricecenter
 * @description: 调价单明细2.0版本的读服务接口实现
 * @author: jmlu
 * @create: 2022-09-20 15:25
 **/

@Service
public class AdjustPriceOrderDetailV2ReadServiceImpl implements IAdjustPriceOrderDetailV2ReadService {

    private final Logger logger = LoggerFactory.getLogger(AdjustPriceOrderDetailV2ReadServiceImpl.class);

    @Autowired
    private AdjustPriceOrderDetailExMapper adjustPriceOrderDetailExMapper;

    @Autowired
    private AdjustPriceOrderDetailMapper adjustPriceOrderDetailMapper;

    @Override
    public List<AdjustPriceOrderDetail> listByAdjustCodeAndDetailIds(String adjustCode, List<Long> detailIds) {
        if (adjustCode == null || CollectionUtils.isEmpty(detailIds)) {
            return Collections.emptyList();
        }
        AdjustPriceOrderDetailExample example = new AdjustPriceOrderDetailExample();
        example.createCriteria()
            .andAdjustCodeEqualTo(adjustCode)
            .andIdIn(detailIds);
        List<AdjustPriceOrderDetail> orderDetailList = adjustPriceOrderDetailMapper.selectByExample(example);
        return orderDetailList == null ? Collections.emptyList() : orderDetailList;
    }

    @Override
    public Map<Long, AdjustPriceOrderDetail> getMapByAdjustCodeAndDetailIds(String adjustCode, List<Long> detailIds) {
        List<AdjustPriceOrderDetail> detailList = listByAdjustCodeAndDetailIds(adjustCode, detailIds);
        return detailList.stream().collect(Collectors.toMap(k1 -> k1.getId(), Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public List<AdjustOrderDetailGoodsExtend1> listGoodsExtend1ByAdjustCode(String adjustCode) {
        List<AdjustOrderDetailGoodsExtend1> detailGoodsExtend1List = adjustPriceOrderDetailExMapper.listGoodsExtend1ByAdjustCode(adjustCode);
        return detailGoodsExtend1List == null ? Collections.emptyList() : detailGoodsExtend1List;
    }

}
