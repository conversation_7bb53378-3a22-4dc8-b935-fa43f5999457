package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.builder.dto.StoreChannelMappingDTO;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.service.impl.AdjustPriceOrderServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-06-20 17:11
 */
@Service
public class FeignStoreService {

    private final Logger logger = LoggerFactory.getLogger(FeignStoreService.class);

    @Autowired
    private StoreService storeService;
    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IAlertService alertService;


    /**
     * 获取 所有mdm连锁id
     * @return
     */
    public List<MdmBusinessBaseToRedisDTO> findAllMdmBusinessBase(){
        logger.warn("查询全部MDM连锁编码");
        RBucket<String> rBucket = redissonClient.getBucket(RedisKeysConstant.PRICE_CENTER_GET_ALL_MDM_BUSINESS);
        if (StringUtils.isNotBlank(rBucket.get())){
            logger.info("从缓存中获取连锁所有信息");
            return JSONObject.parseArray(rBucket.get(), MdmBusinessBaseToRedisDTO.class);
        }

        List<MdmBusinessBaseToRedisDTO> mdmBusinessBaseToRedisDTOS = Lists.newArrayList();
        try{
            List<MdmBusinessBaseDTO> mdmBusinessBaseDTOList = storeService.findAllMdmBusinessBase();

            if (CollectionUtils.isNotEmpty(mdmBusinessBaseDTOList)){
                mdmBusinessBaseToRedisDTOS = mdmBusinessBaseDTOList.stream().map(this::toMdmBusinessBaseToRedisDTO).collect(Collectors.toList());

                rBucket.set(JSONObject.toJSONString(mdmBusinessBaseToRedisDTOS),24, TimeUnit.HOURS);
                return mdmBusinessBaseToRedisDTOS;
            }
        }catch (Exception e){
            logger.error("查询全部MDM连锁编码异常:",e);
        }
        return mdmBusinessBaseToRedisDTOS;
    }


    /**
     * 根据连锁获取门店信息
     * @param businessId
     * @return
     */
    public List<MdmStoreBaseToRedisDTO> findMdmStoreByBusinessId(Long businessId){
        logger.warn("查询指定连锁对应的MDM门店编码:{}",businessId);

        RBucket<String> rBucket = redissonClient.getBucket(RedisKeysConstant.PRICE_CENTER_GET_ALL_MDM_STORE_BY_BUSINESS+businessId);
        if (StringUtils.isNotBlank(rBucket.get())){
            logger.info("从缓存中获取指定连锁对应的MDM门店编码信息");
            return JSONObject.parseArray(rBucket.get(), MdmStoreBaseToRedisDTO.class);
        }

        List<MdmStoreBaseToRedisDTO> mdmStoreBaseToRedisDTOList = Lists.newArrayList();

        try {

            ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeService.findMdmStoreByBusinessId(businessId);

            if (CollectionUtils.isNotEmpty(responseEntity.getBody())){

                mdmStoreBaseToRedisDTOList = responseEntity.getBody().stream().map(this::toMdmStoreBaseToRedisDTO).collect(Collectors.toList());

                rBucket.set(JSONObject.toJSONString(mdmStoreBaseToRedisDTOList),24, TimeUnit.HOURS);
                return mdmStoreBaseToRedisDTOList;
            }
        }catch (Exception e){
            logger.error("查询指定连锁对应的MDM门店编码异常:",e);
        }

        return mdmStoreBaseToRedisDTOList;

    }


    private MdmBusinessBaseToRedisDTO toMdmBusinessBaseToRedisDTO(MdmBusinessBaseDTO item){
        MdmBusinessBaseToRedisDTO mdmBusinessBaseToRedisDTO = new MdmBusinessBaseToRedisDTO();
        BeanUtils.copyProperties(item,mdmBusinessBaseToRedisDTO);
        return mdmBusinessBaseToRedisDTO;
    }

    private MdmStoreBaseToRedisDTO toMdmStoreBaseToRedisDTO(MdmStoreBaseDTO item){
        MdmStoreBaseToRedisDTO mdmBusinessBaseToRedisDTO = new MdmStoreBaseToRedisDTO();
        BeanUtils.copyProperties(item,mdmBusinessBaseToRedisDTO);
        return mdmBusinessBaseToRedisDTO;
    }

    public List<MdmStoreBaseDTO> getStoreListByStoreIds(String code, List<Long> storeIds) {
        List<MdmStoreBaseDTO> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(storeIds)) {
            return list;
        }

        try {
            ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeService.findStoreByStoreIds(storeIds.stream().filter(Objects::nonNull)
                .collect(Collectors.toList()));
            if (Objects.isNull(responseEntity) || CollectionUtils.isEmpty(responseEntity.getBody())) {
                return list;
            }

            List<MdmStoreBaseDTO> body = responseEntity.getBody();
            if (body.size() < storeIds.size()) {
                List<Long> returnStoreIds = body.stream().map(MdmStoreBaseDTO::getStoreId).collect(Collectors.toList());
                List<Long> temp = new ArrayList<>(storeIds);
                temp.removeAll(returnStoreIds);
                logger.debug("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情部分门店未查询到数据,adjustCode:{},storeIds:{}", code, temp);
                alertService.alert("价格中台-FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情部分门店未查询到数据", "adjustCode:" + code + ",storeIds:：" + temp);
            }
            return body;
        } catch (Exception e) {
            logger.error("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情异常", e);

            for (Long storeId : storeIds) {
                try {
                    ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeService.findStoreByStoreIds(Collections.singletonList(storeId));
                    if (Objects.isNull(responseEntity) || CollectionUtils.isEmpty(responseEntity.getBody())) {
                        logger.debug("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情未查询到结果,adjustCode:{},storeId:{}", code, storeId);
                        alertService.alert("价格中台-FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情部分门店未查询到数据", "adjustCode" + code + ",storeId:：" + storeId);
                        continue;
                    }
                    list.addAll(responseEntity.getBody());
                } catch (Exception ex) {
                    logger.error("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情异常,adjustCode:{},storeId:{},Exception:{}", code, storeId, ex);
                    alertService.alert("价格中台-FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情异常", "adjustCode" + code + ",门店ID：" + storeId, ex);
                }
            }
        }

        return list;
    }

    /**
     * 通过门店id批量查询门店详情
     * @param storeIds 门店id列表
     * @return List<MdmStoreBaseDTO>
     */
    public List<MdmStoreBaseDTO> getStoreListByStoreIds(List<Long> storeIds) {
        List<MdmStoreBaseDTO> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(storeIds)) {
            return list;
        }

        try {
            ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeService.findStoreByStoreIds(storeIds.stream().filter(Objects::nonNull)
                .collect(Collectors.toList()));
            if (Objects.isNull(responseEntity) || CollectionUtils.isEmpty(responseEntity.getBody())) {
                return list;
            }

            List<MdmStoreBaseDTO> body = responseEntity.getBody();
            if (body.size() != storeIds.size()) {
                List<Long> returnStoreIds = body.stream().map(MdmStoreBaseDTO::getStoreId).collect(Collectors.toList());
                storeIds.removeAll(returnStoreIds);
                logger.debug("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情部分门店未查询到数据,storeIds:{}", storeIds);
            }
            return body;
        } catch (Exception e) {
            logger.error("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情异常", e);

            for (Long storeId : storeIds) {
                try {
                    ResponseEntity<List<MdmStoreBaseDTO>> responseEntity = storeService.findStoreByStoreIds(Collections.singletonList(storeId));
                    if (Objects.isNull(responseEntity) || CollectionUtils.isEmpty(responseEntity.getBody())) {
                        logger.debug("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情未查询到结果,storeId:{}", storeId);
                        continue;
                    }
                    list.addAll(responseEntity.getBody());
                } catch (Exception ex) {
                    logger.error("FeignStoreService|getStoreListByStoreIds|通过门店id查询门店详情异常,storeId:{},Exception:{}", storeId, ex);
                }
            }
        }

        return list;
    }

    public MdmStoreBaseDTO getMdmStoreBase(String storeNo) {
        String key = PriceConstant.MDM_STORE_PREFIX + storeNo;
        MdmStoreBaseDTO mdmStoreBaseDTO;
        RBucket<String> bucket = redissonClient.getBucket(key);
        if(bucket.isExists()) {
            String json = bucket.get();
            if(StringUtils.isNotBlank(json)){
                mdmStoreBaseDTO = JSON.parseObject(json,MdmStoreBaseDTO.class);
                logger.info("[getMdmStoreBase] 命中缓存|storeNo:{},MdmStoreBaseDTO:{}", storeNo, json);
                return mdmStoreBaseDTO;
            }
        }
        ResponseEntity<MdmStoreBaseDTO> mdmStoreBaseDTORes = storeService.findByStoreNoInternal(storeNo);
        mdmStoreBaseDTO = mdmStoreBaseDTORes.getBody();
        if(mdmStoreBaseDTO == null || mdmStoreBaseDTO.getBusinessId() == null || mdmStoreBaseDTO.getStoreId() == null) {
            logger.warn("[getMdmStoreBase] some info is empty in mdmStoreBaseDTO! storeNo:{}|mdmStoreBaseDTO:{}", storeNo, mdmStoreBaseDTO);
            return null;
        }
        redissonClient.getBucket(key).set(JSON.toJSONString(mdmStoreBaseDTO), 1, TimeUnit.HOURS);
        logger.info("[getMdmStoreBase] 查询到mdmStore信息|storeNo:{},MdmStoreBaseDTO:{}", storeNo, mdmStoreBaseDTO);
        return mdmStoreBaseDTO;
    }

    public StoreBriefDTO getStoreDTOByStoreId(Long storeId) {
        StoreBriefDTO crmStoreDTO = null;
        try {
            if (Objects.nonNull(storeId)) {
                RBucket<String> rBucket = redissonClient.getBucket(RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_STORE_INFO_KEY + storeId + "");
                if (Objects.isNull(rBucket) || org.apache.commons.lang.StringUtils.isBlank(rBucket.get())) {
                    crmStoreDTO = storeService.getStoreById(storeId).getBody();
                    logger.info("FeignStoreService|getStoreDTOByStoreId|获取店铺信息|crmStoreDTO:{}", crmStoreDTO);
                    rBucket.set(JSON.toJSONString(crmStoreDTO));
                    rBucket.expire(7, TimeUnit.DAYS);
                } else {
                    crmStoreDTO = JSON.parseObject(rBucket.get(), StoreBriefDTO.class);
                }

                logger.info("FeignStoreService|getStoreDTOByStoreId|查询店铺详情|crmStoreDTO={}", crmStoreDTO);
            }
        } catch (Exception e) {
            logger.error("FeignStoreService|getStoreDTOByStoreId|通过storeId查询店铺详情异常!|storeId={}", storeId, e);
        }
        return crmStoreDTO;
    }

    /**
     * 调用store店铺与第三方店铺绑定关联关系数据
     *
     * @param channel    渠道
     * @param storeId    crm storeId
     * @param bindStatus 绑定状态
     */
    public List<StoreChannelMappingDTO> getMappingStore(Integer channel, Long storeId, Integer bindStatus){

        String key = RedisKeysConstant.PRICE_CENTER_GET_MAPPING_STORE + storeId + "_" + channel + "_" + bindStatus;
        RBucket<String> rBucket = redissonClient.getBucket(key);
        if (StringUtils.isNotBlank(rBucket.get())){
            logger.info("查询门店对应中心店，storeId:{}", storeId);
            return JSONObject.parseArray(rBucket.get(), StoreChannelMappingDTO.class);
        }

        List<StoreChannelMappingDTO> storeChannelMappingDTOList = Lists.newArrayList();

        try {

            ResponseEntity<List<StoreChannelMappingDTO>> responseEntity = storeService.getMappingStore(channel, storeId, bindStatus);

            if (CollectionUtils.isNotEmpty(responseEntity.getBody())){
                storeChannelMappingDTOList = responseEntity.getBody();
                rBucket.set(JSONObject.toJSONString(storeChannelMappingDTOList),2, TimeUnit.HOURS);
                return storeChannelMappingDTOList;
            } else {
                rBucket.set(JSONObject.toJSONString(storeChannelMappingDTOList),1, TimeUnit.HOURS);
            }
        }catch (Exception e){
            logger.error("查询门店对应中心店，storeId:{}, e:{}", storeId, e);
        }

        return storeChannelMappingDTOList;

    }

}
