package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.config.AdjustPriceConfig;
import com.cowell.pricecenter.constant.PriceConstant;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.*;
import com.cowell.pricecenter.mapper.extension.PriceStoreDetailExMapper;
import com.cowell.pricecenter.mq.producer.PriceSyncCompensateForSelfProducer;
import com.cowell.pricecenter.mq.producer.PriceSyncTaskProducer;
import com.cowell.pricecenter.mq.vo.ItemPrice;
import com.cowell.pricecenter.mq.vo.PriceSyncSelfMqVO;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskVO;
import com.cowell.pricecenter.service.apollo.PriceCompareConfigProperties;
import com.cowell.pricecenter.service.dto.request.SapPriceReqDTO;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;
import com.cowell.pricecenter.service.vo.IncrementPriceSyncVo;
import com.cowell.pricecenter.utils.EmailUtils;
import com.cowell.pricecenter.web.rest.errors.BusinessErrorException;
import com.cowell.pricecenter.web.rest.util.BigDecimalUtils;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.cowell.pricecenter.web.rest.vo.MdmDataTransformDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.cowell.pricecenter.platformapi.HttpRequestUrl.EB_SYNC_PRICE_GOODS_LIMIT;

/**
 * 类说明 价格对比批量 插入
 *
 * @Author: liw
 * @Date: 2020-06-01 13:38
 */
@Slf4j
@Service
public class PriceCenterComparePriceService {

    private static final Logger logger = LoggerFactory.getLogger(PriceCenterComparePriceService.class);

    @Autowired
    private PriceSyncBaseService priceSyncBaseService;
    @Autowired
    private PriceSyncCompensateForSelfProducer priceSyncCompensateForSelfProducer;
    @Autowired
    private PriceCompareConfigProperties priceCompareConfigProperties;
    @Autowired
    private PriceSyncTaskProducer priceSyncTaskProducer;
    @Autowired
    private PriceStoreDetailExMapper priceStoreDetailExMapper;

    @Autowired
    @Qualifier("taskExecutorTrace")
    private AsyncTaskExecutor asyncTaskExecutor;

    @Autowired
    @Qualifier("taskExecutorTrace2")
    private AsyncTaskExecutor asyncTaskExecutor2;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    @Autowired
    private AdjustPriceConfig adjustPriceConfig;

    @Autowired
    private IPriceService priceService;

    @Autowired
    private ISapService sapService;

    @Value("${email.send.user:}")
    private String emailSendUser;

    private static Integer DEFAULT_PAGE_SIZE = 100;

    public void insertOrUpdatePriceStoreDetail(List<ItemPrice> itemPriceList, Integer specialSign) {
        logger.info("对比价格后补发的数据-自查视图补发,对比商品价格进行List分组");
        //先根据连锁 和 门店 分组
        Map<String, List<ItemPrice>> resultCompIdAndBusNoMap = Maps.newHashMap();

        for (ItemPrice bb : itemPriceList) {
            String compId = bb.getCompid();
            String busNo = bb.getBusno();

            //如果是海典的 需要做价格拦截
            if (!priceCompareConfigProperties.isHuaNanComId(compId)) {
                IncrementPriceSyncVo.TableBean.BdataBean bb2 = new IncrementPriceSyncVo.TableBean.BdataBean();
                BeanUtils.copyProperties(bb, bb2);
                bb2.setCompId(bb.getCompid());
                bb2.setBusNo(bb.getBusno());
                bb2.setWareCode(bb.getWarecode());
                bb2.setSyncDate(System.currentTimeMillis() + "");
                bb2.setIsSpecial(bb.getIsSpecial() == null ? 0 : bb.getIsSpecial());

                PriceSyncSelfMqVO priceSyncSelfMqVO = new PriceSyncSelfMqVO();
                //海典推送价格是否进行对比
                if (specialSign.equals(PriceComparisonEnum.NOT_COMPARE_PRICES.getCode())) {
                    priceSyncSelfMqVO.setIsPriceComparison(PriceComparisonEnum.NOT_COMPARE_PRICES.getCode());
                }
                priceSyncSelfMqVO.setIncrementSyncBean(bb2);
                priceSyncSelfMqVO.setWarning(true);
                priceSyncSelfMqVO.setDataPrimaryId(null);
                priceSyncSelfMqVO.setIncrementSyncBean(bb2);
                priceSyncCompensateForSelfProducer.sendMq(priceSyncSelfMqVO);
                logger.info("对比价格后补发的数据-自查视图补发 不是华南的连锁需要进行价格变更拦截:{},{}", compId, priceSyncSelfMqVO);
                continue;
            }

            //map 的key 连锁 + 门店 + 价格类型
            String mapKey = compId + "_" + busNo + "_" + bb.getType();
            logger.info("对比价格后补发的数据-自查视图补发 需要指定同步的连锁:{}", mapKey);
            if (resultCompIdAndBusNoMap.containsKey(mapKey)) {
                resultCompIdAndBusNoMap.get(mapKey).add(bb);
            } else {
                List<ItemPrice> bdataBeanList = Lists.newArrayList();
                bdataBeanList.add(bb);
                resultCompIdAndBusNoMap.put(mapKey, bdataBeanList);
            }
        }

        if (MapUtils.isEmpty(resultCompIdAndBusNoMap)) {
            return;
        }

        this.insertOrUpdatePriceStoreDetail(resultCompIdAndBusNoMap);

    }


    /**
     * 批量插入   最好 控制下 数量
     * <p>
     * 按照  compId + "_" + busNo + "_" + bb.getType() 分组的
     *
     * @param resultCompIdAndBusNoMap
     */
    public void insertOrUpdatePriceStoreDetail(Map<String, List<ItemPrice>> resultCompIdAndBusNoMap) {
        logger.info("对比价格后补发的数据-自查视图补发,对比商品价格进行Map分组");
        if (MapUtils.isEmpty(resultCompIdAndBusNoMap)) {
            return;
        }

        resultCompIdAndBusNoMap.forEach((k, v) -> {

            //根据key 查询连锁门店
            String[] compIdAndBusNo = k.split("_");

            if (CollectionUtils.isEmpty(v)) {
                return;
            }

            String compId = compIdAndBusNo[0];
            String busNo = compIdAndBusNo[1];
            String priceType = compIdAndBusNo[2];
            String priceTypeCode = priceType.equals("1") ? "LSJ" : "HYJ";


            //进行门店ID和连锁ID转换
            Long[] businessIdAndStoreId = this.getBusinessIdAndStoreId(compId, busNo);
            long businessId = businessIdAndStoreId[0];
            long storeId = businessIdAndStoreId[1];

            //1. 根据 连锁和商品编码  获取商品编码
            List<String> goodNoListStr = v.stream().map(ItemPrice::getWarecode).distinct().collect(Collectors.toList());

            //2. 根据门店批量查询是否已经存在价格 商品
            PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                .storeId(storeId)
                .priceTypeCode(priceTypeCode)
                .goodsNoList(goodNoListStr)
                .channelId(adjustPriceConfig.getPushPosChannelId())
                .build();
            List<PriceStoreDetail> priceStoreDetailDbList = priceStoreDetailReadService.query(query);

            //查询 数据库中商品的价格 map
            Map<String, PriceStoreDetail> map = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(priceStoreDetailDbList)) {
                map = priceStoreDetailDbList.stream().collect(Collectors.toMap(PriceStoreDetail::getGoodsNo, item -> item, (k1, k2) -> k1));
            }


            //3. 进行数据的更新和 修改
            List<ItemPrice> bdataBeanInsertList = Lists.newArrayList();
            for (ItemPrice bean : v) {
                String wareCode = bean.getWarecode();

                //说明是新增
                if (map.get(wareCode) == null) {
                    logger.info("对比价格后补发的数据-自查视图补发, 商品价格不存在:{}", wareCode);
                    bdataBeanInsertList.add(bean);
                } else {
                    logger.info("对比价格后补发的数据-自查视图补发, 商品价格存在:{}", wareCode);
                    PriceStoreDetail priceStoreDetail = map.get(wareCode);
                    BigDecimal hdPrice = new BigDecimal(bean.getPrice()).setScale(PriceConstant.PRICE_SCALE, BigDecimal.ROUND_FLOOR);
                    BigDecimal itemPrice = new BigDecimal(Objects.requireNonNull(BigDecimalUtils.convertYuanByFen(priceStoreDetail.getPrice().longValue()))).setScale(2, BigDecimal.ROUND_FLOOR);
                    //如果价格不一样，需要修改 发送mq
                    if (itemPrice.compareTo(hdPrice) == 0) {
                        logger.info("对比价格后补发的数据-自查视图补发,价格一致不需要更新 门店:{} 编码:{}", storeId, wareCode);
                        return;
                    }

                    PriceSyncTaskVO priceSyncTaskVO = new PriceSyncTaskVO();
                    priceSyncTaskVO.setBusinessId(businessId);
                    priceSyncTaskVO.setStoreId(storeId);
                    priceSyncTaskVO.setSpuId(0L);
                    priceSyncTaskVO.setPriceTypeCode(priceTypeCode);
                    priceSyncTaskVO.setNewPrice(hdPrice);
                    priceSyncTaskVO.setGoodsNo(wareCode);
                    priceSyncTaskVO.setType(PriceSyncTaskTypeEnum.SYNC_NEW_HD_PRICE.getType());
                    priceSyncTaskProducer.sendPriceSyncTask(priceSyncTaskVO);
                    logger.info("对比价格后补发的数据-自查视图补发,价格不一致 门店:{} 编码:{}", storeId, wareCode);
                }
            }

            if (CollectionUtils.isEmpty(bdataBeanInsertList)) {
                logger.info("对比价格后补发的数据-自查视图补发,新增的商品价格为空,不做批量插入 门店:{}", storeId);
                return;
            }

            //4. 进行数据批量  插入
            this.insertNewPrice(bdataBeanInsertList);
        });

    }

    @Transactional(rollbackFor = Exception.class)
    public void insertNewPrice(List<ItemPrice> itemPriceList) {
        logger.info("对比价格后补发的数据-自查视图补发,新增商品价格");
        try {
            //将商品列表进行分组
            List<List<ItemPrice>> bDataAllList = Lists.partition(itemPriceList, EB_SYNC_PRICE_GOODS_LIMIT);
            bDataAllList.forEach(item -> {
                List<PriceStoreDetail> priceStoreDetailList = item.stream().map(this::toPriceStoreDetail).collect(Collectors.toList());
                List<List<PriceStoreDetail>> priceStoreDetailLists = Lists.partition(priceStoreDetailList, 20);
                priceStoreDetailLists.forEach(item3 -> {
                    asyncTaskExecutor.execute(() -> {
                        try {
                            logger.info("对比价格后补发的数据-自查视图补发,新增商品价格-异步处理中:{}", JSONObject.toJSONString(item3));
                            int result = priceStoreDetailExMapper.batchInsert(item3);
                            logger.info("对比价格后补发的数据-自查视图补发,批量插入成功:{}", result);
                            //补充门店连锁信息
                            item3.forEach(this::supplementInfo);

                        } catch (Exception e) {
                            logger.warn("对比价格后补发的数据-自查视图补发,批量插入异常:", e);
                            item3.forEach(itemP -> asyncTaskExecutor2.execute(() -> {
                                try {
                                    int result = priceStoreDetailExMapper.batchInsert(Collections.singletonList(itemP));
                                    logger.info("对比价格后补发的数据-自查视图补发,批量插入异常后,单个保存重试 结果:{} :{} :{}", result, itemP.getStoreId(), itemP.getGoodsNo());
                                    //补充信息
                                    this.supplementInfo(itemP);
                                } catch (Exception e1) {
                                    logger.warn("对比价格后补发的数据-自查视图补发,批量保存异常后,单个保存重试异常 :{} :{} :", itemP.getStoreId(), itemP.getGoodsNo(), e1);
                                }
                            }));
                        }
                    });
                });
            });
        } catch (Exception e) {
            logger.warn("对比价格后补发的数据-自查视图补发 处理异常:", e);
        }

    }


    /**
     * 组装 PriceStoreDetail
     *
     * @param item
     * @return
     */
    public PriceStoreDetail toPriceStoreDetail(ItemPrice item) {
        String goodsNo = item.getWarecode();
        String priceTypeCode = "";
        String type = item.getType();

        //获取可用的价格列表
        Map<String, PriceType> priceTypeMap = priceSyncBaseService.getPriceTypeMapsCache();
        if (null == priceTypeMap) {
            logger.info("对比价格后补发的数据-自查视图补发 获取可用的价格列表异常{}", priceTypeMap);
            return null;
        }
        if ("1".equals(type)) {
            priceTypeCode = PTypeEnum.LSJ.getCode();
        }
        if ("2".equals(type)) {
            priceTypeCode = PTypeEnum.HYJ.getCode();
        }
        PriceType priceType = priceTypeMap.get(priceTypeCode);
        if (null == priceType) {
            logger.info("对比价格后补发的数据-自查视图补发 获取可用的价格列表后跟海典的值不匹配{}", type);
            return null;
        }


        //进行门店ID和连锁ID转换
        Long[] businessIdAndStoreId = this.getBusinessIdAndStoreId(item.getCompid(), item.getBusno());
        long businessId = businessIdAndStoreId[0];
        long storeId = businessIdAndStoreId[1];

        PriceStoreDetail priceStoreDetail = new PriceStoreDetail();


        priceStoreDetail.setBusinessId(businessId);
        priceStoreDetail.setStoreId(storeId);
        priceStoreDetail.setPrice(new BigDecimal(BigDecimalUtils.convertFenByYuan(item.getPrice())));
        priceStoreDetail.setGoodsNo(goodsNo);
        priceStoreDetail.setPriceTypeCode(priceType.getCode());
        priceStoreDetail.setPriceTypeId(priceType.getId());
        priceStoreDetail.setPriceTypeName(priceType.getName());

        priceStoreDetail.setItemId(0L);
        priceStoreDetail.setOrgId(0L);
        priceStoreDetail.setOrgName("");
        priceStoreDetail.setOrgGoodsId(0L);
        priceStoreDetail.setAdjustCode("0");
        priceStoreDetail.setAdjustDetailId(11L);
        priceStoreDetail.setAuthOrgId(0L);
        priceStoreDetail.setAuthOrgName("0");
        priceStoreDetail.setLevel((byte) OrgLevelEnum.BUSINESS.getCode());

        priceStoreDetail.setAuthOrgLevel((byte) OrgLevelEnum.BUSINESS.getCode());
        priceStoreDetail.setSpuId(0L);

        priceStoreDetail.setSyncDate(System.currentTimeMillis());
        priceStoreDetail.setUpdatedBy(0L);
        priceStoreDetail.setUpdatedByName("price_compare_" + item.getType());
        priceStoreDetail.setStatus(DeleteStatusEnum.NORMAL.getCode().byteValue());
        priceStoreDetail.setCreatedBy(0L);
        priceStoreDetail.setVersion(PriceStoreDetailVersionEnum.HD_COMPENSATE.getCode());

        priceStoreDetail.setPriceSign(item.getIsSpecial() != null ? Long.valueOf(item.getIsSpecial()) : 0L);
        priceStoreDetail.setChannelId(adjustPriceConfig.getPushPosChannelId());
        priceStoreDetail.setSkuId(0L);


        return priceStoreDetail;
    }

    /**
     * 补充信息
     *
     * @param item
     */
    public void supplementInfo(PriceStoreDetail item) {
        logger.info("对比价格后补发的数据-自查视图补发, 补充信息 :{} :{}", item.getStoreId(), item.getGoodsNo());
        PriceSyncTaskVO priceSyncTaskVO = new PriceSyncTaskVO();
        priceSyncTaskVO.setBusinessId(item.getBusinessId());
        priceSyncTaskVO.setStoreId(item.getStoreId());
        priceSyncTaskVO.setSpuId(item.getSpuId());
        priceSyncTaskVO.setGoodsNo(item.getGoodsNo());
        priceSyncTaskVO.setType(PriceSyncTaskTypeEnum.SYNC_TASK_SUPPLEMENT_INFO.getType());
        priceSyncTaskVO.setPriceType(PTypeEnum.getTypeByCode(item.getPriceTypeCode()));
        priceSyncTaskProducer.sendPriceSyncTask(priceSyncTaskVO);
        logger.info("对比价格后补发的数据-自查视图补发, 补充信息 发送mq成功 :{} :{}", item.getStoreId(), item.getGoodsNo());

        priceService.afterSavePrice(item);
    }

    /**
     * 获取门店ID、连锁ID
     *
     * @param compId
     * @param busNo
     * @return
     */
    private Long[] getBusinessIdAndStoreId(String compId, String busNo) {
        //读缓存
        MdmDataTransformDTO mdmDataTransformDTO;
        try {
            mdmDataTransformDTO = priceSyncBaseService.getMdmCache(busNo, compId);
            if (null == mdmDataTransformDTO) {
                logger.info("对比价格后补发的数据-自查视图补发, 连锁+门店转换为空 :{} :{}，跳过当前数据", compId, busNo);
                throw new BusinessErrorException("连锁+门店转换为空");
            }
        } catch (Exception e) {
            logger.error("对比价格后补发的数据-自查视图补发, 连锁+门店转换异常 :{} :{} ", compId, busNo, e);
            throw new BusinessErrorException("连锁+门店转换异常");
        }

        return new Long[]{mdmDataTransformDTO.getBusinessId(), mdmDataTransformDTO.getStoreIds().get(0)};
    }


    public void comparePrice(String comId, String storeNo,
                             Long storeId, Long channelStoreId) {
        Integer page = 1;
        List<JSONObject> diffPriceList = Lists.newArrayList();
        while (true) {
            try {
                PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
                    .storeId(storeId)
                    .priceTypeCode(PTypeEnum.MLJ.getCode())
                    .channelId(4)
                    .channelStoreId(channelStoreId)
                    .page(page)
                    .pageSize(DEFAULT_PAGE_SIZE)
                    .build();
                List<PriceStoreDetail> pricecenterDataList = priceStoreDetailReadService.query(query);
                if (CollectionUtils.isEmpty(pricecenterDataList)) {
                    break;
                }
                List<List<PriceStoreDetail>> partitionList = Lists.partition(pricecenterDataList, 10);
                List<PriceStoreDetail> allSapPrices = Lists.newArrayList();
                partitionList.forEach(priceDataList -> {
                    SapPriceReqDTO sapPriceReqDTO = new SapPriceReqDTO();
                    sapPriceReqDTO.setComId(comId);
                    sapPriceReqDTO.setGoodsList(priceDataList.stream().map(PriceStoreDetail::getGoodsNo).collect(Collectors.toList()));
                    List<PriceStoreDetail> sapPrices = sapService.queryPrice(sapPriceReqDTO);
                    allSapPrices.addAll(sapPrices);
                });

                Map<String, PriceStoreDetail> priceMap = allSapPrices.stream().collect(Collectors.toMap(PriceStoreDetail::getGoodsNo, item -> item, (k1, k2) -> k1));
                for (PriceStoreDetail priceCenterData : pricecenterDataList) {
                    JSONObject diffData = new JSONObject();
                    BigDecimal priceCenterPrice = priceCenterData.getPrice();
                    PriceStoreDetail sapPriceData = priceMap.get(priceCenterData.getGoodsNo());
                    if (Objects.isNull(priceCenterPrice) && Objects.isNull(sapPriceData.getPrice())) {
                        continue;
                    }

                    if (Objects.isNull(sapPriceData)) {
                        diffData.put("goodsNo", priceCenterData.getGoodsNo());
                        //价格中心价格
                        diffData.put("priceCenterPrice", priceCenterPrice.toString());
                        //价格中心数据状态
                        diffData.put("priceCenterPriceStatus", priceCenterData.getStatus());
                        //sap价格
                        diffData.put("sapPrice", "SAP未查询到价格或者是查询RFC报错了！");
                        //sap价格数据状态
                        diffData.put("sapPriceStatus", "SAP未查询到价格或者是查询RFC报错了！");
                        diffPriceList.add(diffData);
                        continue;
                    }
                    Byte sapStatus = sapPriceData.getStatus();
                    if (Objects.isNull(sapStatus)) {
                        sapStatus = new Byte("-3");
                    }
                    Byte centerStatus = priceCenterData.getStatus();
                    BigDecimal sapPrice = Objects.isNull(sapPriceData.getPrice()) ? new BigDecimal("0") : sapPriceData.getPrice();
                    if (priceCenterPrice.compareTo(sapPrice) != 0 || !centerStatus.equals(sapStatus)) {
                        diffData.put("goodsNo", priceCenterData.getGoodsNo());
                        //价格中心价格
                        diffData.put("priceCenterPrice", priceCenterPrice.toString());
                        //价格中心数据状态
                        diffData.put("priceCenterPriceStatus", priceCenterData.getStatus());

                        //sap价格
                        diffData.put("sapPrice", sapPrice.toString());
                        //sap价格数据状态
                        diffData.put("sapPriceStatus", sapStatus);
                        diffPriceList.add(diffData);
                    }

                }
                page++;
            } catch (Exception e) {
                log.error("PriceCenterComparePriceService|comparePrice|比对价格异常", e);
                break;
            }

        }

        if (CollectionUtils.isEmpty(diffPriceList)) {
            log.info("PriceCenterComparePriceService|comparePrice|未查询到差异的数据");
            return;
        }
        String emailContent = buildEmailContent(diffPriceList);
        if (StringUtils.isEmpty(emailContent)) {
            return;
        }
        String[] to = StringUtils.split(emailSendUser, ",");
        EmailUtils.sendMail(to, "目录价差异" + "_" + DateUtils.dateToString(new Date()), emailContent, null);
    }

    private String buildEmailContent(List<JSONObject> diffPriceList) {
        if (CollectionUtils.isEmpty(diffPriceList)) {
            return "";
        }
        String date = DateUtils.dateToString(new Date());
        StringBuilder content = new StringBuilder("<html><head></head><body><h3>" + date + "&nbsp;B2B目录价差异</h3>");
        content.append("<table border=\"5\" style=\"border:solid 1px #E8F2F9;font-size=14px;;font-size:18px;width:500px;text-align:center\">");
        content.append("<tr ><td>商品编码</td><td>价格中心价格</td><td>价格中心数据状态</td><td>sap价格</td><td>sap价格数据状态</td></tr>");
        diffPriceList.forEach(jsonObject -> {
            content.append("<tr>");
            content.append("<td>").append(jsonObject.getString("goodsNo")).append("</td>"); //第一列
            content.append("<td>").append(jsonObject.getString("priceCenterPrice")).append("</td>"); //第二列
            content.append("<td>").append(jsonObject.getString("priceCenterPriceStatus")).append("</td>"); //第三列

            content.append("<td>").append(jsonObject.getString("sapPrice")).append("</td>"); //第二列
            content.append("<td>").append(jsonObject.getString("sapPriceStatus")).append("</td>"); //第三列
            content.append("</tr>");
        });
        content.append("</table>");
        content.append("</body></html>");
        return content.toString();
    }
}
