package com.cowell.pricecenter.service;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

import com.cowell.pricecenter.service.dto.request.PermissionParam;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * @program: pricecenter
 * @description: excel导入调价单明细DTO
 * @author: jmlu
 * @create: 2022-04-20 17:21
 **/

@Data
public class ImportAdjustPriceOrderDetailDTO extends PermissionParam {

    private static final long serialVersionUID = 2489318876300258397L;

    @ApiModelProperty("表格行号")
    private Integer lineNo;

    @ApiModelProperty("商品编码")
    private String goodsNo;
    
    @ApiModelProperty("三方商品编码")
    private String outSkuCode;
    
    @ApiModelProperty("skuId")
    private String skuId="0";

    @ApiModelProperty("会员特价 0 - 非特价  1 - 特价")
    private Integer priceFlag;

    @ApiModelProperty("调整后商品类型")
    private String goodsType;

    @ApiModelProperty("调整后活动类型")
    private String activityType;

    @ApiModelProperty("调价原因")
    private String adjustReason;

    @ApiModelProperty("新价格（元）_零售价")
    private BigDecimal priceLSJ;

    @ApiModelProperty("新价格（元）_会员价")
    private BigDecimal priceHYJ;

    @ApiModelProperty("折扣计算_会员价")
    private BigDecimal rebateHYJ;

    @ApiModelProperty("新价格（元）_拆零价")
    private BigDecimal priceCLJ;
    
    @ApiModelProperty("新价格（元）_会员拆零价")
    private BigDecimal priceCHYJ;
    
    @ApiModelProperty("新价格（元）_拼多多-拼团价")
    private BigDecimal pricePDDPTJ;

    @ApiModelProperty("新价格（元）_拼多多-单买价")
    private BigDecimal pricePDDDMJ;

    @ApiModelProperty("新价格（元）_美团售价")
    private BigDecimal priceMTLSJ;
    
    @ApiModelProperty("新价格（元）_抖音售价")
    private BigDecimal priceDYLSJ;

    @ApiModelProperty("校验通过")
    private Boolean result;

    @ApiModelProperty("备注")
    private String message;
    
    
    
    @ApiModelProperty("机构类型 1:选择门店列表;2:选择门店组;3:选择门店标签")
    private Integer goodsOrgTabType;
    private String goodsOrgTabTypeParam;
    @ApiModelProperty("机构名称")
    private String orgNames;
    @ApiModelProperty("机构类型")
    private String orgIds;
    private String orgIdsParam;
    @ApiModelProperty("机构级别")
    private String orgLevels;
    @ApiModelProperty("机构类型等于2、3时 门店组id、门店标签id  有值")
    private String tabTypeVals;
    @ApiModelProperty("覆盖下级 0:不覆盖，1：覆盖")
    private Integer overrideLowerLevel;
    private String overrideLowerLevelParam;
    
    @ApiModelProperty("商品名称")
    private String goodsName;
    @ApiModelProperty("通用名称")
    private String curName;
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    @ApiModelProperty("规格")
    private String jhiSpecification;
    @ApiModelProperty("单位")
    private String goodsUnit;
    @ApiModelProperty("产地")
    private String prodarea;
    
    private Long businessId;
    
    private Long channelMappingStoreId;
    
    /**
     * 是否从主单复制机构
     */
    private Boolean isCopyAdjustOrderOrg = false;
    
}
