/**
* @mbg.generated
* generator on Fri Mar 18 13:53:27 CST 2022
*/
package com.cowell.pricecenter.service;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.PriceChangeNotice;
import com.cowell.pricecenter.entity.PriceChangeNoticeExample;
import com.cowell.pricecenter.enums.PriceChangeNoticeEnum;

import java.util.List;

public interface PriceChangeNoticeService {
    /**
    * deleteByPrimaryKey
    * @param id id
    * @return int int
    */
    int deleteByPrimaryKey(Long id);

    List<PriceChangeNotice> selectByExample(PriceChangeNoticeExample example);

    long countByExample(PriceChangeNoticeExample example);

    /**
    * insert
    * @param row row
    * @return int int
    */
    int insert(PriceChangeNotice row);

    /**
    * insertSelective
    * @param row row
    * @return int int
    */
    int insertSelective(PriceChangeNotice row);

    /**
    * selectByPrimaryKey
    * @param id id
    * @return PriceChangeNotice PriceChangeNotice
    */
    PriceChangeNotice selectByPrimaryKey(Long id);

    /**
    * updateByPrimaryKeySelective
    * @param row row
    * @return int int
    */
    int updateByPrimaryKeySelective(PriceChangeNotice row);

    /**
    * updateByPrimaryKey
    * @param row row
    * @return int int
    */
    int updateByPrimaryKey(PriceChangeNotice row);

    /**
     * 保存价格变动通知
     * @param adjustPriceOrder 调价单
     * @param storeId 门店ID
     * @param storeOrgDTO 门店
     * @param businessOrgDTO 连锁
     * @param platformOrgDTO 平台
     * @param noticeType 类型
     */
    void savePriceChangeNotice(AdjustPriceOrder adjustPriceOrder,
                               Long storeId,
                               OrgDTO storeOrgDTO,
                               OrgDTO businessOrgDTO,
                               OrgDTO platformOrgDTO,
                               PriceChangeNoticeEnum.NoticeType noticeType);
}
