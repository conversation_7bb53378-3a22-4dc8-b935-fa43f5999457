package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceManageControlOrderDetail;
import com.cowell.pricecenter.entity.PriceManageControlOrderDetailExample;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/3/17 11:31
 */

public interface PriceManageControlOrderDetailService {

    int deleteByExample(PriceManageControlOrderDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PriceManageControlOrderDetail record);

    int insertSelective(PriceManageControlOrderDetail record);

    int updateByExampleSelective(PriceManageControlOrderDetail record, PriceManageControlOrderDetailExample example);

    int updateByExample(PriceManageControlOrderDetail record, PriceManageControlOrderDetailExample example);

    int updateByPrimaryKeySelective(PriceManageControlOrderDetail record);

    int updateByPrimaryKey(PriceManageControlOrderDetail record);

    /**
     * 通过游标的方式扫描管控单门店商品明细
     * @param controlOrderCode 管控单编码
     */
    void cursorScanControlOrderDetailList(String controlOrderCode);

    /**
     * 更新管控单待生效时间
     * @param controlOrderCode 管控单编码
     * @param dxsDate 待生效时间
     */
    void updateControlOrderDetailDxsDate(String controlOrderCode, LocalDateTime dxsDate);
}



