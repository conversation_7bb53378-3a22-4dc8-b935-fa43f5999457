package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.BizGoodsWhiteListParam;
import com.cowell.pricecenter.service.dto.request.SearchWhiteListParam;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.AdjustImportResultDTO;
import com.cowell.pricecenter.service.dto.response.ExportFileCubeVO;
import com.cowell.pricecenter.service.dto.response.ImportExcelModel;
import com.cowell.pricecenter.service.dto.response.WhiteListDTO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;

import java.util.List;
import java.util.Map;

public interface BizGoodsWhiteListService {
    /**
     * 编辑商品白名单
     * @param param
     * @param userDTO
     */
    void editWhiteList(BizGoodsWhiteListParam param, TokenUserDTO userDTO);

    /**
     * 获取商品白名单列表
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<WhiteListDTO> getWhiteList(SearchWhiteListParam param, TokenUserDTO userDTO);

    /**
     * 删除商品白名单
     * @param param
     * @param userDTO
     */
    void whiteListRemoveGoods(BizGoodsWhiteListParam param, TokenUserDTO userDTO);

    /**
     * 获取白名单下所有商品编码
     * @param param
     * @param userDTO
     * @return
     */
    List<String> getWhiteListAllGoodsNoList(SearchWhiteListParam param, TokenUserDTO userDTO);
    
    /**
     * 添加缓存商品白名单
     * @param param
     * @param userDTO
     */
    void addCacheWhiteList(BizGoodsWhiteListParam param, TokenUserDTO userDTO);
    
    /**
     * 获取缓存中商品白名单
     * @param param
     * @param userDTO
     * @return
     */
    com.cowell.pricecenter.service.dto.response.amis.PageResult<WhiteListDTO> getCacheWhiteList(SearchWhiteListParam param, TokenUserDTO userDTO);
    
    /**
     * 根据商品编码删除缓存商品白名单
     * @param param
     * @param userDTO
     */
    void deleteCacheWhiteList(SearchWhiteListParam param, TokenUserDTO userDTO);
    
    /**
     * 导入缓存商品白名单
     * @param param
     * @param userDTO
     */
    void importCacheWhiteList(BizGoodsWhiteListParam param);
    
    /**
     * 轮询导入状态
     * @param param
     */
    AdjustImportResultDTO importCacheWhiteListStatus(BizGoodsWhiteListParam param);
    
    /**
     * 导出异常数据明细
     * @param param
     * @return
     */
    ExportFileCubeVO<ImportExcelModel> asyncExportErrorWhiteListDetailsFile(SearchWhiteListParam param);
    
    /**
     * 提交商品白名单
     * @param param
     */
    void submitWhiteList(BizGoodsWhiteListParam param, TokenUserDTO userDTO);
}
