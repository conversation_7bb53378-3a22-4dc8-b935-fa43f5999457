package com.cowell.pricecenter.service;

import com.cowell.pricecenter.redis.RedisKeysConstant;

/**
 * @program: pricecenter
 * @description: 通过Redis缓存进行下载接口
 * @author: jmlu
 * @create: 2022-09-22 09:28
 **/

public interface IDownloadFromRedisCacheService {

    /**
     * 缓存本次下载总次数
     * @param prefixKey 功能key
     * @param downloadCode 下载唯一码
     * @param pageCount 下载的总页数
     */
    void cacheDownloadPageCount(String prefixKey, String downloadCode, Integer pageCount);


    /**
     * 获取本次下载总次数
     * @param prefixKey
     * @param downloadCode
     * @return
     */
    Integer getDownloadPageCount(String prefixKey, String downloadCode);
}
