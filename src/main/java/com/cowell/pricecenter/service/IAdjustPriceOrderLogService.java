package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrder;

/**
 * <AUTHOR>
 * @date 2023/4/21 16:19
 */
public interface IAdjustPriceOrderLogService {

    /**
     * 根据调价单记录日志
     * @param adjustPriceOrder 调价单
     * @return int
     */
    int recordLogByAdjustPriceOrder(AdjustPriceOrder adjustPriceOrder);

    /**
     * 根据调价单号记录日志
     * @param adjustCode 调价单号
     * @return int
     */
    int recordLogByAdjustPriceOrder(String adjustCode);

    /**
     * 删除历史日志
     */
    void deleteHistoryLog(int beforeDays);
}
