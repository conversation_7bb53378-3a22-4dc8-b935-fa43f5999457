package com.cowell.pricecenter.service;

import com.cowell.pricecenter.builder.BuilderFactoryManager;
import com.cowell.pricecenter.builder.SyncPricePlatformBuilder;
import com.cowell.pricecenter.builder.dto.StoreChannelMappingDTO;
import com.cowell.pricecenter.builder.dto.SyncPriceDTO;
import com.cowell.pricecenter.builder.utils.ArrayListUtils;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.PlatformChannelEnum;
import com.cowell.pricecenter.enums.PriceSyncTaskTypeEnum;
import com.cowell.pricecenter.enums.SyncResultEnum;
import com.cowell.pricecenter.mq.producer.PriceSyncTaskProducer;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskGoodsVO;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskVO;
import com.cowell.pricecenter.service.dto.PriceSyncRecordDTO;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 价格同步第三方 方法
 * <AUTHOR>
 */
@Service
@Slf4j
public class PriceSyncPlatformService {

    private final Logger logger = LoggerFactory.getLogger(PriceSyncPlatformService.class);

    @Resource
    private BuilderFactoryManager builderFactoryManager;

    @Autowired
    private PriceSyncTaskProducer priceSyncTaskProducer;

    @Autowired
    private PriceSync2PlatformRecordService priceSync2PlatformRecordService;

    @Autowired
    private PriceStoreDetailReadService priceStoreDetailReadService;

    /**
     * 同步价格到第三方平台 JD、EB、MT
     * @param syncPrice 请求消息体
     * @param channel 渠道类型
     */
    public void doSyncPrice(SyncPriceDTO syncPrice, Integer channel){

        logger.info("开始|价格同步|至第三方平台, 渠道类型:{}",channel);
        PlatformChannelEnum platformChannelEnum = PlatformChannelEnum.getPlatformChannelEnum(channel);


        StoreChannelMappingDTO storeChannelMappingDTO = syncPrice.getChannelMapping();
        Long businessId = storeChannelMappingDTO.getBusinessId();
        Long storeId = storeChannelMappingDTO.getStoreId();

        if (platformChannelEnum == null){
            log.error("|价格同步|根据渠道同步至第三方平台, 渠道不存在 连锁:{} 门店:{} 渠道:{}",storeChannelMappingDTO.getBusinessId(),storeChannelMappingDTO.getStoreId(),channel);
            return;
        }


        List<PriceSyncTaskGoodsVO>  syncTaskGoodsVOList = syncPrice.getSyncPriceList();

        if (CollectionUtils.isEmpty(syncTaskGoodsVOList)){
            log.info("|价格同步|根据渠道同步至第三方平台, 商品列表为空 返回成功 连锁:{} 门店:{}",businessId,storeId);
            return;
        }

        //1. 商品去重
        syncTaskGoodsVOList = syncTaskGoodsVOList.stream().distinct().collect(Collectors.toList());

        //2. 数据验证
        List<PriceSyncTaskGoodsVO> errorGoodsVOList = syncTaskGoodsVOList.stream().filter(item-> StringUtils.isBlank(item.getGoodsNo())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errorGoodsVOList)){
            logger.error("|价格同步|根据渠道同步至第三方平台, 商品列表中存在商品编码为空的数据,渠道类型:{} 连锁:{} 门店:{} 商品列表:{}",channel,businessId,storeId,errorGoodsVOList.toString());
        }

        //3. 移除错误数据
        syncTaskGoodsVOList = ArrayListUtils.receiveDefectList(syncTaskGoodsVOList,errorGoodsVOList);


        //4. 过滤调商品价格为零的商品
        List<PriceSyncTaskGoodsVO> priceZeroGoodsVOList = syncTaskGoodsVOList.stream().filter(item-> (item.getPrice() == 0 || item.getPrice() == null) ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(priceZeroGoodsVOList)){
            logger.error("|价格同步|根据渠道同步至第三方平台, 商品列表中存在商品价格为空/为零的数据,渠道类型:{} 连锁:{} 门店:{} 商品列表:{}",channel,businessId,storeId,priceZeroGoodsVOList.toString());

            //记录日志
            this.insertSyncPriceRecordOneReason(businessId,storeId,"商品价格为空/为零",priceZeroGoodsVOList,channel);
        }
        //4.1  移除错误数据
        syncTaskGoodsVOList = ArrayListUtils.receiveDefectList(syncTaskGoodsVOList,priceZeroGoodsVOList);

        if (CollectionUtils.isNotEmpty(syncTaskGoodsVOList)){
            logger.warn("|价格同步|根据渠道同步至第三方平台, 商品列表经过筛选后的商品为空不需要做同步,渠道类型:{} 连锁:{} 门店:{} 商品列表:{}",channel,businessId,storeId,syncTaskGoodsVOList.toString());
            return;
        }



        //5. 获取对应的 builder
        SyncPricePlatformBuilder syncPricePlatformBuilder = null;
        try {
            syncPricePlatformBuilder = builderFactoryManager.getBuilder(channel);
        } catch (Exception e) {
            logger.warn("|价格同步|根据渠道同步至第三方平台, 根据渠道类型没有找到对应的实现类型:",e);
            return;
        }
        if(null == syncPricePlatformBuilder){
            log.warn("|价格同步|根据渠道同步至第三方平台, 接收消息渠道channel不存在{}",channel);
            return;
        }

        syncPrice.setSyncPriceList(syncTaskGoodsVOList);


        //6. 同步价格
        List<String> failGoodsNoList = syncPricePlatformBuilder.syncPrice(syncPrice);
        logger.info("|价格同步|根据渠道同步至第三方平台,请求参数:{} 渠道:{} 门店:{} 失败的数据:{}",syncPrice.toString(),channel,storeId,failGoodsNoList.toString());
        if (CollectionUtils.isEmpty(failGoodsNoList)){
            logger.info("|价格同步|根据渠道同步至第三方平台,渠道:{} 门店:{} 全部同步成功,不再做同步重试",channel,storeId);
            return;
        }

        //7. 发送 价格同步 重试
        this.doSyncPriceRetry(businessId,storeId,failGoodsNoList);
        logger.info("|价格同步|根据渠道同步至第三方平台,渠道:{} 门店:{} 部分同步成功,正在进行同步重试",channel,storeId);

    }

    /**
     * 同步 日志记录
     * @param businessId 连锁
     * @param storeId 门店
     * @param reason 错误的原因
     * @param goodsVOList 商品列表
     * @param channel 渠道
     */
//    @Transactional(rollbackFor = Exception.class)
    public void insertSyncPriceRecordOneReason(Long businessId, Long storeId, String reason, List<PriceSyncTaskGoodsVO> goodsVOList, Integer channel) {

        logger.info("|价格同步|-记录同步日志-单个原因, 请求参数 连锁:{} 门店:{} 失败的原因:{} 商品列表:{} 渠道:{}",businessId,storeId,reason,goodsVOList,channel);
        if (CollectionUtils.isEmpty(goodsVOList)){
            logger.info("|价格同步|-记录同步日志-单个原因, 商品列表为空, 不做记录");
            return;
        }

        Map<String,Object> resultMap = Maps.newHashMap();

        goodsVOList.forEach(item->{
            resultMap.putIfAbsent(item.getGoodsNo(),reason);
        });


        this.insertSyncPriceRecord(businessId,storeId,resultMap,goodsVOList,channel);
    }

    /**
     * 同步 日志记录
     * @param businessId 连锁
     * @param storeId 门店
     * @param resultMap 错误的商品对应的原因
     * @param goodsVOList 商品列表
     * @param channel 渠道
     */
//    @Transactional(rollbackFor = Exception.class)
    public void insertSyncPriceRecord(Long businessId, Long storeId, Map<String,Object> resultMap, List<PriceSyncTaskGoodsVO> goodsVOList, Integer channel){

        logger.info("|价格同步|-记录同步日志, 请求参数 连锁:{} 门店:{} 失败的商品:{} 商品列表:{} 渠道:{}",businessId,storeId,resultMap,goodsVOList,channel);

        try {
            //需要记录的商品价格同步日志 的 DTO
            List<PriceSyncRecordDTO> priceSyncRecordDTOS = Lists.newArrayList();
            //成功的商品列表
            List<String> successGoodsList = Lists.newArrayList();
            //失败的商品列表
            List<String> failGoodsList = Lists.newArrayList();

            if (CollectionUtils.isEmpty(goodsVOList)){
                logger.info("|价格同步|-记录同步日志 商品列表为空, 不做记录");
                return;
            }

            PlatformChannelEnum channelEnum = PlatformChannelEnum.getPlatformChannelEnum(channel);
            if (channelEnum == null){
                logger.info("|价格同步|-记录同步日志 不存在的渠道类型:{}",channel);
                this.resultOneFailReasonRecord(businessId,storeId,goodsVOList,"不存在的渠道类型",channel);
                return;
            }


            //同步商品的 商品编码列表
            List<String> goodsNoList = Lists.newArrayList();
            //同步商品的 商品编码 对应的价格
            Map<String,Long> goodsNoToPriceMap = Maps.newHashMap();
            goodsVOList.forEach(item->{
                if (StringUtils.isNotBlank(item.getGoodsNo())){
                    goodsNoList.add(item.getGoodsNo());
                    goodsNoToPriceMap.putIfAbsent(item.getGoodsNo(),item.getPrice());
                }
            });

            //根据商品编码编码查询商品相关信息
            List<PriceStoreDetail> priceStoreDetailList = getPriceStoreDetailList(businessId,storeId,goodsNoList);

            if (CollectionUtils.isEmpty(priceStoreDetailList)){
                logger.info("|价格同步|-记录同步日志 商品价格不存在 商品列表:{}",goodsNoList);

                this.resultOneFailReasonRecord(businessId,storeId,goodsVOList,"商品价格不存在",channel);
                return;
            }

            //商品编码对应的价格详情Map
            Map<String,PriceStoreDetail> goodsNoToPriceStoreDetail = priceStoreDetailList.stream().collect(Collectors.toMap(PriceStoreDetail::getGoodsNo, item->item,(k1,k2)->k1));

            if (MapUtils.isNotEmpty(resultMap)){
                logger.info("|价格同步|-记录同步日, 部分同步成功 同步失败的商品 :{}",resultMap.toString());

                for (PriceSyncTaskGoodsVO item : goodsVOList){

                    if (resultMap.get(item.getGoodsNo()) != null){

                        failGoodsList.add(item.getGoodsNo());
                    }else{
                        successGoodsList.add(item.getGoodsNo());
                    }
                }
            }else{
                logger.info("|价格同步|-记录同步日, 请求中错误商品编码对应的原因Map为空, 则表示全部同步成功");
                successGoodsList = goodsVOList.stream().map(PriceSyncTaskGoodsVO::getGoodsNo).collect(Collectors.toList());
            }


            //记录成功的日志
            if (CollectionUtils.isNotEmpty(successGoodsList)){
                logger.info("|价格同步|-记录同步日, 记录成功的商品价格同步记录 商品列表:{}",successGoodsList.toString());
                List<PriceSyncRecordDTO> list = this.getPriceSyncRecordDTOS(businessId,storeId,resultMap,goodsNoToPriceMap,
                    goodsNoToPriceStoreDetail,successGoodsList,SyncResultEnum.SUCCESS.getCode(),channel);
                priceSyncRecordDTOS.addAll(list);
            }

            //记录失败的日志
            if (CollectionUtils.isNotEmpty(failGoodsList)){
                logger.info("|价格同步|-记录同步日, 记录失败的商品价格同步记录 商品列表:{}",failGoodsList.toString());
                List<PriceSyncRecordDTO> list = this.getPriceSyncRecordDTOS(businessId,storeId,resultMap,goodsNoToPriceMap,
                    goodsNoToPriceStoreDetail,failGoodsList,SyncResultEnum.FAIL.getCode(),channel);
                priceSyncRecordDTOS.addAll(list);

            }
            //开始插入记录
            this.batchInsertAndUpdateStatus(priceSyncRecordDTOS);
        }catch (Exception e){
            logger.error("|价格同步|-记录同步日, 异常:",e);
            this.resultOneFailReasonRecord(businessId,storeId,goodsVOList,e.getMessage(),channel);
        }
    }


    /**
     * 重试 失败后的商品编码列表
     * @param businessId 连锁
     * @param storeId 门店
     * @param failGoodsNoList 失败的商品列表
     */
    private void doSyncPriceRetry(Long businessId,Long storeId,List<String> failGoodsNoList){
        logger.info("|价格同步-重试|进入价格同步重试 请求参数 连锁:{} 门店 :{} 需要重试的商品编码列表:{}",businessId,storeId,failGoodsNoList);

        if (CollectionUtils.isEmpty(failGoodsNoList)){
            logger.warn("|价格同步-重试|进入价格同步重试 需要重试的商品编码列表为空,不做同步操作");
            return;
        }

        PriceSyncTaskVO vo = new PriceSyncTaskVO();
        vo.setBusinessId(businessId);
        vo.setStoreId(storeId);
        vo.setChannel(PlatformChannelEnum.MT.getType());
        vo.setType(PriceSyncTaskTypeEnum.SYNC_RETRY.getType());

        List<PriceSyncTaskGoodsVO> list = Lists.newArrayList();
        failGoodsNoList.forEach(item->{
            PriceSyncTaskGoodsVO vo1 = new PriceSyncTaskGoodsVO();
            //价格每次需要查询
            vo1.setPrice(null);
            vo1.setGoodsNo(item);
            list.add(vo1);
        });
        vo.setGoodsList(list);

        //发送mq
        priceSyncTaskProducer.sendPriceSyncTask(vo);
        logger.info("|价格同步-重试|价格同步重试已发送MQ 连锁:{} 门店 :{}",businessId,storeId);
    }

    /**
     * 设置 记录日志的 list、
     * @param businessId 连锁
     * @param storeId 门店
     * @param resultMap 失败商品的原因
     * @param goodsNoToPriceMap 商品和价格的对应关系
     * @param goodsNoToPriceStoreDetail 商品价格信息
     * @param goodsList 价格列表
     * @param status 成功/失败 状态
     * @param channel 渠道
     * @return
     */
    private List<PriceSyncRecordDTO> getPriceSyncRecordDTOS(Long businessId,Long storeId,Map<String,Object> resultMap,Map<String,Long> goodsNoToPriceMap,Map<String, PriceStoreDetail> goodsNoToPriceStoreDetail,List<String> goodsList,Byte status,Integer channel){

        List<PriceSyncRecordDTO> priceSyncRecordDTOS = Lists.newArrayList();

        for (String item : goodsList){

            PriceStoreDetail detail = null;
            if (MapUtils.isNotEmpty(goodsNoToPriceStoreDetail)){
                detail = goodsNoToPriceStoreDetail.get(item);
            }

            Long price = null;
            if (MapUtils.isNotEmpty(goodsNoToPriceMap)){
                price = goodsNoToPriceMap.get(item);
            }

            String syncWarnDesc ="";
            if (MapUtils.isNotEmpty(resultMap)){
                syncWarnDesc = resultMap.get(item)+"";
            }

            PriceSyncRecordDTO successDto = toPriceSyncRecordDTO(businessId,storeId,item,status,
                syncWarnDesc,price, detail,channel);
            priceSyncRecordDTOS.add(successDto);
        }

        return priceSyncRecordDTOS;
    }

    /**
     * 记录单一错误的 日志记录
     * @param businessId 连锁
     * @param storeId 门店
     * @param goodsVOList 商品
     * @param reason 原因
     * @param channel 渠道
     */
    private void resultOneFailReasonRecord(Long businessId,Long storeId,List<PriceSyncTaskGoodsVO> goodsVOList,String reason,Integer channel){

        logger.info("|价格同步|-记录同步日志, 记录单一错误的日志记录 请求参数 连锁:{} 门店:{} 商品信息:{} 原因:{} 渠道:{}",businessId,storeId,goodsVOList,reason,channel);
        List<PriceSyncRecordDTO> priceSyncRecordDTOS = Lists.newArrayList();

        goodsVOList.forEach(item-> priceSyncRecordDTOS.add(this.toPriceSyncRecordDTO(businessId,storeId,item.getGoodsNo(), SyncResultEnum.FAIL.getCode(),reason,item.getPrice(),null,channel)));

        this.batchInsertAndUpdateStatus(priceSyncRecordDTOS);

    }

    /**
     * 开始记录 日志
     * @param priceSyncRecordDTOS 请求参数
     */
    private void batchInsertAndUpdateStatus(List<PriceSyncRecordDTO> priceSyncRecordDTOS ){
        logger.info("|价格同步|-记录同步日志");
        try {
            //批量插入 日志记录
            priceSync2PlatformRecordService.batchInsertAndUpdateStatus(priceSyncRecordDTOS);
        }catch (Exception e){
            logger.warn("|价格同步|-记录同步日志 异常:",e);

        }

    }


    /**
     *
     * 获取商品 名称已经 门店名称
     * @param businessId 连锁
     * @param storeId 门店
     * @param goodsNoList 商品编码
     * @return
     */
    private List<PriceStoreDetail> getPriceStoreDetailList(Long businessId, Long storeId, List<String> goodsNoList){
        logger.info("|价格同步|-记录同步日志, 查询门店商品价格列表返回为空 门店ID:{} ",storeId);
        //查询商品价格信息
        PriceStoreDetailQuery query = PriceStoreDetailQuery.builder()
            .storeId(storeId)
            .businessId(businessId)
            .goodsNoList(goodsNoList)
            .build();
        List<PriceStoreDetail> priceStoreDetailList = priceStoreDetailReadService.query(query);

        if (CollectionUtils.isEmpty(priceStoreDetailList)){
            logger.info("|价格同步|-记录同步日志, 查询门店商品价格列表返回为空 门店ID:{} ",storeId);
            return Lists.newArrayList();
        }

        return priceStoreDetailList;
    }

    private PriceSyncRecordDTO toPriceSyncRecordDTO(Long businessId,Long storeId, String goodsNo,
                                                    Byte syncResult,String syncWarnDesc,
                                                    Long price, PriceStoreDetail detail,Integer channel){

        PriceSyncRecordDTO dto = new PriceSyncRecordDTO();
        dto.setBusinessId(businessId);
        dto.setStoreId(storeId);
        //设置渠道类型
        dto.setChannel(channel);
        dto.setGoodsNo(goodsNo);
        dto.setPrice(price);
        dto.setPriceType(PTypeEnum.getTypeByType(channel));
        dto.setSyncResult(syncResult);
        dto.setSyncWarnDesc(syncWarnDesc);
        dto.setCreatedBy("-1");
        dto.setGmtCreate(new Date());
        dto.setGmtUpdate(new Date());
        dto.setStatus((byte)0);

        if (detail != null){
            dto.setStoreName(detail.getStoreName());
            dto.setSkuName(StringUtils.isBlank(detail.getCurName()) ? goodsNo : detail.getCurName());
        }else{
            dto.setStoreName(storeId+"");
            dto.setSkuName(goodsNo);
        }
        return dto;
    }





}
