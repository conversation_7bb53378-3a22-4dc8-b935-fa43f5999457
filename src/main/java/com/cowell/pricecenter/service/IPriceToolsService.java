package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.service.dto.ImportPriceUpdateDTO;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;

import java.util.List;


public interface IPriceToolsService{
    public boolean correctPriceStoreDetailAndAdjustPriceDiff(Long storeId,String priceTypeCode);

    public boolean priceStoreDetailAndAdjustPriceDiff(Long storeId);

    CommonResponse comparisonPriceTypeDifference(Long businessId, String storeIds, String sourcePriceTypeCode, String targetPriceTypeCode);

    CommonResponse comparisonPriceTypeTaskProgress(String taskId);

    void doComparisonPriceTypeDifference(String taskId,Long storeId,String sourcePriceTypeCode, String targetPriceTypeCode);

    public boolean priceStoreDetailAndpriceDetailHistoryDiff(Long storeId);

    CommonResponse initializePrice(Long businessId, String sourcePriceTypeCode, String targetPriceTypeCode);

    CommonResponse initializePriceByStoreId(Long businessId, Long storeId, String sourcePriceTypeCode, String targetPriceTypeCode);

    boolean initializeCenterStorePrice(Long businessId,Long storeId,Long channelStoreId,String priceTypeCode, Integer channelId);

    boolean comparisonPriceByTargetStoreIdAndStoreId(Long targetStoreId,Long storeId,String goodsNo, String priceTypeCode, Integer channelId);

    boolean comparisonPriceByTargetStoreIdPriceCodeAndStoreIdPriceCode(Long targetStoreId, String targetPriceTypeCode, Long storeId, String storeName,String priceTypeCode, String goodsNo, Integer channelId);

    void executeComparisonPriceByTargetStoreIdAndStoreId(Long storeId,PriceStoreDetail priceStoreDetail);

    void executeComparisonPriceByTargetStoreIdPriceCodeAndStoreIdPriceCode(Long storeId,String storeName,String priceTypeCode, PriceStoreDetail priceStoreDetail);

    boolean delPriceStoreDetailSpu0();

    boolean changeAdjustPriceOrderStatus(String adjust_code, int auditStatus);

    /**
     * 批量初始化指定价格类型的数据
     *
     * @param priceStoreDetails
     * @param targetPriceTypeCode
     */
    void initializePriceByStoreId(List<PriceStoreDetail> priceStoreDetails, String targetPriceTypeCode);

    /**
     * 对比商品中心上架的商品 推送差异的价格 给商品中心
     *
     * @param businessId
     * @param storeIds
     * @param priceTypeCode
     */
    void compareItemCenterPriceToUpdate(Long businessId, String storeIds, String priceTypeCode, Integer channelId);

    /**
     * 根据门店同步商品最新价格给 商品中心
     *
     * @param storeId
     * @param priceTypeCode
     * @param channelId
     */
    void syncPriceToItemCenterByStore(Long storeId, String priceTypeCode, Integer channelId);

    /**
     * 根据门店同步商品最新价格给 商品中心
     *
     * @param businessId 连锁
     * @param storeId 门店
     * @param priceTypeCode 价格类型
     * @param goodsNos 商品编码
     * @param channelId 渠道
     */
    void syncPriceToItemCenterByStore(Long businessId, Long storeId, String priceTypeCode, List<String> goodsNos, Integer channelId);

    /**
     * 修改价格 从原价格类型的价格 修改到 目标价格类型的价格
     *
     * @param businessId
     * @param storeIds
     * @param sourcePriceTypeCode
     * @param targetPriceTypeCode
     */
    void updatePriceByPriceType(Long businessId, String storeIds, String sourcePriceTypeCode, String targetPriceTypeCode, Integer channelId);

    /**
     * 查询商品价格为空 或者0 补充价格
     *
     * @param businessId
     * @param storeId
     * @param priceTypeCode
     * @param goodsNo
     */
    void selectPriceNullOrZero(Long businessId, Long storeId, String priceTypeCode, String goodsNo);

    /**
     * 高济药急送渠道价 对比零售价
     *
     * @param storeId
     */
    void channelPriceCompareLSJ(Long businessId, Long storeId, String priceTypeCode);

    void importPriceUpdate(List<ImportPriceUpdateDTO> dtos);
    void importPriceUpdateNew(List<ImportPriceUpdateDTO> dtos);

    CommonResponse queryGoodsPriceCount(String queryTaskId,Long businessId,String storeIds,String priceTypeCode);
    Long doSelectGoodsPriceCount(String taskId,Long storeId,String targetPriceTypeCode);
    Long doSelectGoodsPriceCount(String taskId,Long storeId,String targetPriceTypeCode, Integer channelId);
    /**
     * 修改门店 连锁ID
     * @param storeId 门店ID
     * @param newBusinessId 新连锁ID
     */
    void updateBusinessId(Long storeId,Long newBusinessId);

    /**
     * 修改价格 从原价格类型的价格 修改到 目标价格类型的价格
     *
     * @param businessId
     * @param storeIds
     * @param goodsNo
     * @param sourcePriceTypeCode
     * @param targetPriceTypeCode
     */
    void copyPrice(Long businessId, String storeIds,String goodsNo, String sourcePriceTypeCode, String targetPriceTypeCode, Integer channelId);

    /**
     * 同步药急送价格
     * @param businessId 连锁
     * @param storeIds 门店ID
     * @param goodsNos 商品编码
     */
    void syncGJYJSPrice(Long businessId, String storeIds, String goodsNos);

    /**
     * 同步价格至商品中台
     * @param businessId 连锁
     * @param storeIds 门店ID
     * @param priceTypeCode 价格类型
     * @param channel 渠道
     */
    void syncItemCenter(Long businessId, String storeIds, String priceTypeCode, int channel);
}
