package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.service.dto.PushMsgDTO;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.request.WarnRecordQueryParam;
import com.cowell.pricecenter.service.dto.request.WarnRecordReceiveParam;
import com.cowell.pricecenter.service.dto.request.WarnRuleQueryParam;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.dto.response.amis.CommonResult;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;

import java.util.List;

import org.apache.ibatis.annotations.Param;

/**
 * Created by schuangxigang on 2022/7/5 18:51.
 */
public interface PriceWarnService {

    PageResult<WarnRuleListDTO> searchWarnRulePageList(WarnRuleQueryParam param, TokenUserDTO userDTO);

    WarnRuleDTO getWarnRuleById(Long id);

    CommonResult saveWarnRule(WarnRuleDTO warnRuleDTO, TokenUserDTO userDTO) ;

    void deleteWarnRuleById(Long id, TokenUserDTO userDTO);

    PageResult<WarnRecordDTO> pageWarnRecordList(WarnRecordQueryParam param);

    void receiveBdpWarnRecordList(List<WarnRecordReceiveParam> params);

    NoticeReminder getReminder();

    /**
     *
     * @Title: selectPriceWarnPushData
     * @Description: 查询符合调价数据推送智店通
     * @param: @param businessId
     * @param: @param startTime
     * @param: @param endTime
     * @param: @return
     * @return: List<PushMsgDTO>
     * @throws
     */
    List<PushMsgDTO> selectPriceWarnPushData(Long businessId,String startTime,String endTime);

    void pushPriceWarnResultNoticeMsg();

    void sendQw(AdjustPriceOrder adjustPriceOrder, String message);

    void sendEmail(AdjustPriceOrder adjustPriceOrder, String message);
}
