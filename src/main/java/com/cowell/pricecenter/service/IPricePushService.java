package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PricePushTask;
import com.cowell.pricecenter.enums.PriceUpdateResultEnum;
import com.cowell.pricecenter.enums.PushResultEnum;
import com.cowell.pricecenter.mq.vo.PricePushVO;
import com.cowell.pricecenter.service.vo.PricePushProduceVo;
import com.cowell.pricecenter.web.rest.vo.PricePushResultVO;

import java.util.List;

/**
 * @Description: 价格推送服务
 * @Author: liubw
 * @Date: 2019/2/26 11:23 AM
 */
public interface IPricePushService {
    /**
     * 更新POS回调的处理结果
     * @param pushResultVO POS返回的处理结果
     */
    int updateTaskResult(PricePushResultVO pushResultVO);


    /**
     * 批量插入海典推送改价格任务数据
     * @param taskList 任务列表
     * @return int
     */
    int batInsertPricePushTask(List<PricePushTask> taskList);


    /**
     * 处理推送任务
     * @param vo 推送参数
     * @return true:推送成功
     */
    boolean pushTask(PricePushProduceVo vo);


    boolean executePushTask(PricePushTask task);

    int updateTaskResult(Long taskId, PushResultEnum pushResultEnum, PushResultEnum oldPushResultEnum);

    /**
     * 更新价格推送任务的状态
     * @param taskId 任务ID
     * @param pushResultEnum result的状态值
     * @param priceUpdateResultEnum 跟随此状态值的其他状态的默认值
     * @return int
     */
    int updateTaskResultWithDefaultValue(Long taskId, PushResultEnum pushResultEnum, PriceUpdateResultEnum priceUpdateResultEnum);

    /**
     * 重推任务给POS方
     * @param task PricePushTask
     */
    void rePush(PricePushTask task);

}
