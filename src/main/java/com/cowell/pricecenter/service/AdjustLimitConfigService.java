package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrderDetail;
import com.cowell.pricecenter.enums.ControlGoodsPropertyEnum;
import com.cowell.pricecenter.enums.GoodsLimitStatusEnum;
import com.cowell.pricecenter.service.dto.GoodsClassification;
import com.cowell.pricecenter.service.dto.response.PriceStoreDetailVo;
import com.cowell.pricecenter.service.feign.vo.AdjustLimitConfigRow;
import com.cowell.pricecenter.service.vo.OrgLevelVO;

import java.util.List;
import java.util.Map;

/**
 * @program: pricecenter
 * @description: 调价单管控服务
 * @author: jmlu
 * @create: 2022-12-10 19:58
 **/

public interface AdjustLimitConfigService {

    /**
     *  检查商品所在公司是否在限购中 分类
     * @param goodsClassification
     * @param businessIdList
     * @return
     */
	GoodsLimitStatusEnum checkGoodsClassificationLimit(GoodsClassification goodsClassification, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,
			AdjustPriceOrderDetail orderDetail,Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap);
    
    /**
     *  检查商品所在公司是否在限购中 商品编码
     * @param goodsNo
     * @param businessIdList
     * @return
     */
	GoodsLimitStatusEnum checkGoodsNoLimit(String goodsNo, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
			Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap);

    /**
     *  检查商品名称所在公司是否在限制的商品名称中，模糊匹配
     * @param goodsName
     * @param goodsClassification
     * @param businessIdList
     * @return
     */
	GoodsLimitStatusEnum checkGoodsNameLimit(String goodsName, GoodsClassification goodsClassification, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,
			AdjustPriceOrderDetail orderDetail,Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap);

    /**
     * 校验商品是否在限制中
     * @param goodsNo
     * @param goodsName
     * @param categoryId
     * @param businessIdList
     * @return
     */
    GoodsLimitStatusEnum checkGoodsLimit(String goodsNo, String goodsName, String categoryId, String covidtype,Map<Long, String> pushLevelsMap, List<OrgLevelVO> orgLevelVOList, AdjustPriceOrderDetail orderDetail, Long userId);
    
    /**
     *  检查商品所在公司是否在限购中 商品属性
     * @param goodsNo
     * @param businessIdList
     * @return
     */
	GoodsLimitStatusEnum checkCovidTypeLimit(String covidtype, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
			Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap, ControlGoodsPropertyEnum propertyEnum);
	
    /**
     *  检查商品所在公司是否在限购中 商品属性
     * @param goodsNo
     * @param businessIdList
     * @return
     */
	GoodsLimitStatusEnum checkGoodsPropertyLimit(Map<Long, String> pushLevelsMap, List<Long> businessIdList,List<PriceStoreDetailVo> storeDetailList,AdjustPriceOrderDetail orderDetail,
			Integer limitLevel,Map<String, List<AdjustLimitConfigRow>> limitConfigRowMap, ControlGoodsPropertyEnum propertyEnum);
}
