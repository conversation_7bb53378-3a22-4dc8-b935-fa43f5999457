package com.cowell.pricecenter.service;

import com.cowell.pricecenter.enums.AdjustLimitConfigEnum;
import com.cowell.pricecenter.enums.PriceManageWhiteListEnum;
import com.cowell.pricecenter.service.feign.vo.AdjustLimitConfigRow;
import com.cowell.pricecenter.service.feign.vo.PriceManageWhiteListVO;

import java.util.List;

/**
 * @program: pricecenter
 * @description: ErpBizSupport的feign的扩展类
 * @author: jmlu
 * @create: 2022-12-11 12:10
 **/

public interface ErpBizSupportExtService {


    /**
     * 获取调价限制配置
     * @param limitConfigEnum
     * @param businessId
     * @return
     */
    List<AdjustLimitConfigRow> queryAdjustLimitConfig(AdjustLimitConfigEnum limitConfigEnum, Long businessId, String goodsNo);
    
    /**
     * 
     * @Title: queryPriceManageWhiteList   
     * @Description: 查询调价管控用户机构白名单   
     * @param: @param whiteListEnum
     * @param: @param businessIdList
     * @param: @param userId
     * @param: @return      
     * @return: List<PriceManageWhiteListVO>      
     * @throws
     */
    List<PriceManageWhiteListVO> queryPriceManageWhiteList(PriceManageWhiteListEnum whiteListEnum,List<Long> businessIdList,Long userId);
}
