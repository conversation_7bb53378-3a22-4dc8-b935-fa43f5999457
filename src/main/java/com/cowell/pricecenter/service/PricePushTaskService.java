package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PricePushTask;
import com.cowell.pricecenter.enums.PriceUpdateResultEnum;
import com.cowell.pricecenter.enums.PushResultEnum;

/**
 * <AUTHOR>
 * @date 2022/7/21 10:29
 */
public interface PricePushTaskService {

    int updateTaskResult(Long taskId, PushResultEnum pushResultEnum, PushResultEnum oldPushResultEnum);

    int updateTaskResult(Long taskId, PushResultEnum pushResultEnum, PriceUpdateResultEnum priceUpdateResultEnum);

    /**
     * 根据历史明细更新task状态
     * @param pushCode pushCode
     * @param batch batch
     * @param taskId taskId
     * @param updateTask task
     * @param originMsg 原始信息
     * @return int
     */
    int updateTaskByHistory(String pushCode, Integer batch, Long taskId, PricePushTask updateTask, String originMsg);
}
