package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSONObject;
import com.cowell.common.thread.ThreadUtils;
import com.cowell.pricecenter.entity.PriceSyncRecord;
import com.cowell.pricecenter.entity.PriceSyncRecordExample;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.SyncResultEnum;
import com.cowell.pricecenter.mapper.PriceSyncRecordMapper;
import com.cowell.pricecenter.mapper.extension.PriceSyncRecordExtMapper;
import com.cowell.pricecenter.security.SecurityUtils;
import com.cowell.pricecenter.service.dto.FileDTO;
import com.cowell.pricecenter.service.dto.PriceSyncRecordDTO;
import com.cowell.pricecenter.service.dto.request.FileDownloadTaskDTO;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.utils.ExportUtil;
import com.cowell.pricecenter.web.rest.util.*;
import com.cowell.pricecenter.web.rest.vo.PriceSyncRecordVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description: 价格同步日志service
 * @date 2020/03/05 18:10
 */
@Slf4j
@Service
public class PriceSync2PlatformRecordService {

    @Resource
    private PriceSyncRecordMapper priceSyncRecordMapper;

    @Resource
    private PriceSyncRecordExtMapper priceSyncRecordExtMapper;

    @Resource
    private FileService fileService;

    /**
     * pil保存
     * @param dtoList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsert(List<PriceSyncRecordDTO> dtoList){
        //验证
        validateDTO(dtoList);
        TokenUserDTO tokenUserDTO = SecurityUtils.getCurrentUserToken();
        String userId = null == tokenUserDTO ? "-1" : String.valueOf(tokenUserDTO.getUserId());
        //转换
        List<PriceSyncRecord> priceSyncRecordList = dtoList.stream().map(item -> {
            PriceSyncRecord priceSyncRecord = new PriceSyncRecord();
            BeanUtils.copyProperties(item, priceSyncRecord);
            priceSyncRecord.setCreatedBy(userId);
            priceSyncRecord.setUpdatedBy(userId);
            return priceSyncRecord;
        }).collect(Collectors.toList());
        //保存
        priceSyncRecordExtMapper.batchInsert(priceSyncRecordList);
    }

    /**
     * 验证
     * @param recordDTOList
     */
    private void validateDTO(List<PriceSyncRecordDTO> recordDTOList) {
        Assert.notEmpty(recordDTOList,"数据不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getBusinessId()),"连锁id不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getStoreId()),"门店id不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getStoreName()),"门店名称不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getGoodsNo()),"商品编码不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getSkuName()),"商品名称不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getPrice()),"同步价格不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getPriceType()),"价格类型不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getSyncResult()),"价格同步结果不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getCreatedBy()),"价格同步的操作人不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getChannel()),"渠道不能为空");
    }

    /**
     * 更新状态 status = -1
     * @param storeId
     * @param goodsNoList
     * @param newStatus
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Long storeId, List<String> goodsNoList, Byte srcStatus, Byte newStatus,String userId,Integer channel){
        PriceSyncRecordExample example = new PriceSyncRecordExample();
        PriceSyncRecordExample.Criteria criteria = example.createCriteria();
        criteria.andStoreIdEqualTo(storeId);
        criteria.andGoodsNoIn(goodsNoList);
        criteria.andChannelEqualTo(channel);
        criteria.andStatusEqualTo(srcStatus);
        PriceSyncRecord record = new PriceSyncRecord();
        record.setStatus(newStatus);
        record.setUpdatedBy(userId);
        priceSyncRecordMapper.updateByExampleSelective(record,example);
    }

    private ReentrantLock reentrantLock = new ReentrantLock();

    /**
     * 批量更新保存
     * @param recordDTOList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertAndUpdateStatus(List<PriceSyncRecordDTO> recordDTOList){
        log.info("==>PriceSync2PlatformRecordService||batchInsertAndUpdateStatus||批量更新保存同步价格信息,请求数据:{}", JSONObject.toJSONString(recordDTOList));
        //验证
        validateDTO(recordDTOList);
        Map<Long, List<PriceSyncRecordDTO>> priceSyncRecordMap = recordDTOList.stream().collect(Collectors.groupingBy(PriceSyncRecordDTO::getStoreId));
        if(CollectionUtils.isEmpty(priceSyncRecordMap)){
            log.info("<==PriceSync2PlatformRecordService||batchInsertAndUpdateStatus||根据门店分组后数据为空");
            return;
        }
        priceSyncRecordMap.forEach((k,v) -> {
            Map<Integer, List<PriceSyncRecordDTO>> byPriceTypeMap = v.stream().collect(Collectors.groupingBy(PriceSyncRecordDTO::getChannel));
            doBatchInsertAndUpdateStatus(k,byPriceTypeMap);
        });
        log.info("<==PriceSync2PlatformRecordService||batchInsertAndUpdateStatus||批量更新保存同步价格信息完成");
    }

    private synchronized void doBatchInsertAndUpdateStatus(Long storeId,Map<Integer, List<PriceSyncRecordDTO>> byPriceTypeMap){
        if(CollectionUtils.isEmpty(byPriceTypeMap)){
            return;
        }
        TokenUserDTO tokenUserDTO = SecurityUtils.getCurrentUserToken();
        String userId = null == tokenUserDTO ? "-1" : String.valueOf(tokenUserDTO.getUserId());
        PriceSync2PlatformRecordService priceSync2PlatformRecordService = GetApplicationContextUtils.getBean(PriceSync2PlatformRecordService.class);
        byPriceTypeMap.forEach((channel,dataList) -> {
            List<String> goodsNoList = dataList.stream().map(item -> item.getGoodsNo()).distinct().collect(Collectors.toList());
            //更新
            priceSync2PlatformRecordService.updateStatus(storeId,goodsNoList,(byte)0,(byte)-1,userId,channel);
            //保存
            priceSync2PlatformRecordService.batchInsert(dataList);
        });
    }


    /**
     * 列表
     * @param vo
     * @return
     */
    public Pagination<PriceSyncRecordVO> getPriceSyncRecordList(PriceSyncRecordVO vo) {
        log.info("==>PriceSync2PlatformRecordService||getPriceSyncRecordList||列表查询，请求数据:{}||page:{}", JSONObject.toJSONString(vo));
        Assert.notNull(vo.getBusinessId(),"连锁id不能为空");
        Assert.isTrue(null != vo.getStartTime() && null != vo.getEndTime(),"日期不能为空");
        int days = DateUtils.differentDaysByMillisecond(vo.getStartTime(),vo.getEndTime());
        Assert.isTrue(days <= 7,"所选日期范围不能超过7天");
        PriceSyncRecordExample example = getPriceSyncRecordExample(vo, 0);
        List<PriceSyncRecord> priceSyncRecords = priceSyncRecordMapper.selectByExample(example);
        Pagination<PriceSyncRecordVO> pageDTO = new Pagination();
        if(CollectionUtils.isEmpty(priceSyncRecords)){
            pageDTO.setSource(new ArrayList<>());
            return pageDTO;
        }
        List<PriceSyncRecordVO> respList = getPriceSyncRecordVOList(priceSyncRecords);
        int count = priceSyncRecordMapper.countByExample(example);
        pageDTO.setPage(vo.getPage());
        pageDTO.setSize(vo.getPageSize());
        pageDTO.setHitCount(count);
        pageDTO.setTotalPages(count / vo.getPageSize() + 1);
        pageDTO.setSource(respList);
        log.info("<==PriceSync2PlatformRecordService||getPriceSyncRecordList||列表查询，返回:{}", JSONObject.toJSONString(respList));
        return pageDTO;
    }

    private PriceSyncRecordExample getPriceSyncRecordExample(PriceSyncRecordVO vo,int type){
        PriceSyncRecordExample example = new PriceSyncRecordExample();
        PriceSyncRecordExample.Criteria criteria = example.createCriteria();
        if(null != vo.getStoreId() && vo.getStoreId() > 0){
            criteria.andStoreIdEqualTo(vo.getStoreId());
        }
        criteria.andBusinessIdEqualTo(vo.getBusinessId());
        criteria.andStatusEqualTo((byte)0);
        if(SyncResultEnum.SUCCESS.getCode() == vo.getSyncResult()){
            criteria.andSyncResultEqualTo(SyncResultEnum.SUCCESS.getCode());
        }else if(SyncResultEnum.EXCEPTION.getCode() == vo.getSyncResult()){
            //查询异常 失败
            criteria.andSyncResultNotEqualTo(SyncResultEnum.SUCCESS.getCode());
        }
        if(null != vo.getStartTime() && null != vo.getEndTime()){
            criteria.andGmtCreateBetween(vo.getStartTime(),vo.getEndTime());
        }
        if(StringUtils.isNotEmpty(vo.getKeyWord())){
            criteria.andGoodsNoEqualToOrSkuNameLike(vo.getKeyWord());
        }
        //0列表  1导出
        if(0 == type){
            StringBuffer buffer = new StringBuffer("store_id,gmt_create DESC limit ");
            buffer.append(String.valueOf(vo.getPage() * vo.getPageSize())).append(",").append(String.valueOf(vo.getPageSize()));
            example.setOrderByClause(buffer.toString());
        }
        return example;
    }

    private List<PriceSyncRecordVO> getPriceSyncRecordVOList(List<PriceSyncRecord> priceSyncRecords){
        List<PriceSyncRecordVO> respList = Optional.of(priceSyncRecords).orElse(Lists.newArrayList()).stream().map(item -> {
            PriceSyncRecordVO respVo = new PriceSyncRecordVO();
            BeanUtils.copyProperties(item,respVo);
            respVo.setPriceTypeDesc(PTypeEnum.getDesc(item.getPriceType()));
            respVo.setPrice(BigDecimalUtils.convertYuanByFen(item.getPrice()));
            respVo.setSyncResultDesc(SyncResultEnum.getDesc(item.getSyncResult()));
            return respVo;
        }).collect(Collectors.toList());
        return respList;
    }

    /**
     * 导出
     * @param vo
     * @return
     */
    public String export(PriceSyncRecordVO vo) {
        log.info("==>PriceSync2PlatformRecordService||export||导出筛选结果，请求数据:{}||param:{}", JSONObject.toJSONString(vo));
        Assert.notNull(vo.getBusinessId(),"连锁id不能为空");
        Assert.isTrue(null != vo.getStartTime() && null != vo.getEndTime(),"日期不能为空");
        int days = DateUtils.differentDaysByMillisecond(vo.getStartTime(),vo.getEndTime());
        Assert.isTrue(days <= 7,"所选日期范围不能超过7天");
        Long loginUserId = SecurityUtils.getCurrentUserToken().getUserId();
        ThreadUtils.submit(() -> {
            String taskName = loginUserId + "_" + vo.getBusinessId() + "_" +"价格同步日志";
            PriceSyncRecordExample example = getPriceSyncRecordExample(vo, 1);
            List<PriceSyncRecord> priceSyncRecords = priceSyncRecordMapper.selectByExample(example);
            List<PriceSyncRecordVO> respList = getPriceSyncRecordVOList(priceSyncRecords);
            LinkedHashMap<String, String> map = ExportUtil.getPriceSyncPropertyMap();
            byte[] fileByte = new byte[0];
            try {
                fileByte = ExcelUtils.listToExcel(taskName, respList, map, "sheet0", 65535, new ArrayList<>());
            } catch (Exception e) {
                log.warn("<==PriceSync2PlatformRecordService||export||导出筛选结果,异常||{}",JSONObject.toJSONString(vo));
            }
            FileDTO fileDTO = new FileDTO(loginUserId,JSONObject.toJSONString(vo), taskName, "/api/price/sync/record/export",vo.getStartTime(),vo.getEndTime());
            FileDownloadTaskDTO fileDownloadTaskDTO = fileService.createDownloadCenterTaskStart(fileDTO);
            fileService.createDownloadCenterTaskEnd(fileDownloadTaskDTO, taskName, fileByte,"fileDownloader/price/sync/export");
            log.info("<==PriceSync2PlatformRecordService||export||导出筛选结果结束", JSONObject.toJSONString(vo));
        });
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteSyncRecordRecord(PriceSyncRecordDTO dto) {
        log.info("==>PriceSync2PlatformRecordService||deleteSyncRecordRecord||删除价格同步日志,{}",JSONObject.toJSONString(dto));
        Assert.notNull(dto.getBusinessId(),"连锁id不能为空");
        Assert.notNull(dto.getGmtCreate(),"日期不能为空");
        PriceSyncRecordExample example = new PriceSyncRecordExample();
        PriceSyncRecordExample.Criteria criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(dto.getBusinessId());
        criteria.andStatusEqualTo((byte)-1);
        criteria.andGmtCreateLessThanOrEqualTo(dto.getGmtCreate());
        int count = priceSyncRecordMapper.deleteByExample(example);
        log.info("<==PriceSync2PlatformRecordService||deleteSyncRecordRecord||删除价格同步日志完成数量:{}={}",count,JSONObject.toJSONString(dto));
    }
}
