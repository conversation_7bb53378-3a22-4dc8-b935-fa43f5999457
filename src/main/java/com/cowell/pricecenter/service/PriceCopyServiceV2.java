package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceCopyInfo;
import com.cowell.pricecenter.enums.PriceCopyEffectStatusEnum;
import com.cowell.pricecenter.service.dto.OaSource;
import com.cowell.pricecenter.service.dto.PriceStoreCopyDTO;
import com.cowell.pricecenter.service.dto.request.PriceCopyGoodsQueryParam;
import com.cowell.pricecenter.service.dto.request.PriceCopyOrderParam;
import com.cowell.pricecenter.service.dto.request.PriceCopyOrderQueryParam;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.priceCopy.PriceCopyGoodsDTO;
import com.cowell.pricecenter.service.dto.response.priceCopy.PriceCopyOrderBaseInfo;
import com.cowell.pricecenter.service.dto.response.priceCopy.PriceCopyOrderDTO;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;

import java.util.List;

/**
 * 价格复制接口
 *
 * Created by schuangxigang on 2022/4/6 10:33.
 */
public interface PriceCopyServiceV2 {
    void submitOrder(PriceCopyOrderParam param);

    PageResult<PriceCopyOrderDTO> pageList(PriceCopyOrderQueryParam param);

    PageResult<PriceCopyGoodsDTO> pageGoodsList(PriceCopyGoodsQueryParam param);

    List<PriceCopyOrderBaseInfo> getListByCopyNo(String copyNo);

    void afterAudit(OaSource oaSource);

    void repeatCopyPriceStoreDetails(PriceCopyInfo priceCopyInfo) ;

    void saveEffectStatus(PriceCopyInfo priceCopyInfo, PriceCopyEffectStatusEnum effectStatus);

    PriceCopyInfo getByCopyNo(String copyNo);

    /**
     * 使用新事务执行SQL
     * @param priceCopyInfo
     * @param pushCode
     * @param effectStatus
     */
    void saveEffectStatusAndPushCodeNewTransaction(PriceCopyInfo priceCopyInfo, String pushCode,
        PriceCopyEffectStatusEnum effectStatus);

    void resendPricePushTaskMQ(String copyNo);

    PriceStoreCopyDTO copyPriceStoreDetails(PriceCopyInfo priceCopyInfo,String pushCode);

    /**
     *
     * @Title: getPriceCopyStore
     * @Description: 根据门店id查询存在价格复制的门店信息
     * @param: @param storeIdList
     * @param: @return
     * @return: List<Long>
     * @throws
     */
    List<Long> getPriceCopyStore(List<Long> storeIdList);
}
