package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.mq.vo.HisPriceForSelfMqVO;
import com.cowell.pricecenter.service.dto.request.EditPriceParam;
import com.cowell.pricecenter.service.dto.request.PriceManagerParam;
import com.cowell.pricecenter.service.dto.request.PriceQueryV2Param;
import com.cowell.pricecenter.service.dto.response.PriceQueryGoodsNoInfoDTO;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;

/**
 * @Auther: zhc
 * @Description:门店价格信息管理业务层接口
 */
public interface PriceStoreDetailManageInfoService {
    /**
     * 运营后台-编辑价格
     * @param param
     * @return
     */
    CommonResponse editItemPrice(EditPriceParam param);

    /**
     * 根据门店商品渠道批量获取商品价格
     * @param param
     * @return
     */
    List<PriceQueryGoodsNoInfoDTO> getPriceByParam(List<PriceQueryV2Param> param);

    /**
     * 运营后台-获取指定药店定价列表
     * @param param
     * @return
     */
    List<PriceQueryGoodsNoInfoDTO> getPriceStoreDetailsByParam(PriceQueryV2Param param);

    /**
     * 根据门店ID和goodsNoList列表获取指定价格
     * @param param
     * @return
     */
    List<PriceStoreDetail> getPriceByStoreIdAndGoodsNoListV2(PriceQueryV2Param param);

    /**
     * 运营后台-根据门店ID、商品编码获取各渠道价
     *
     * @param storeId
     * @param goodsNo
     * @return
     */
    List<PriceStoreDetail> getPriceStoreDetailSByStoreIdAndGoodsNo(Long storeId, String goodsNo);

    /**
     * 运营后台MQ自销同步价格
     * @param param
     */
    void editItemPriceSelf(HisPriceForSelfMqVO param);

    /**
     * 导入互医价格
     * @param file
     */
    ResponseEntity<HashMap<String, Object>> importB2CPrice(MultipartFile file);

    ResponseEntity<HashMap<String, Object>> importReplenishBusinessNameAndStoreName(MultipartFile file);

    /**
     * 新增或修改门店商品价格
     * @param param
     * @return
     */
    CommonResponse saveOrUpdateItemPrice(List<PriceManagerParam> param);

    /**
     * 删除门店商品价格
     * @param param
     * @return
     */
    CommonResponse deleteItemPrice(List<PriceManagerParam> param);
}
