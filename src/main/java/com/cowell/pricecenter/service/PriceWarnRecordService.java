package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSONObject;
import com.beust.jcommander.internal.Maps;
import com.cowell.common.thread.ThreadUtils;
import com.cowell.pricecenter.entity.PriceWarningRecord;
import com.cowell.pricecenter.entity.PriceWarningRecordExample;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.PriceWarnTypeEnun;
import com.cowell.pricecenter.mapper.PriceWarningRecordMapper;
import com.cowell.pricecenter.mapper.extension.PriceWarningRecordExtMapper;
import com.cowell.pricecenter.mq.producer.PriceWarningProducer;
import com.cowell.pricecenter.redis.RedisKeysConstant;
import com.cowell.pricecenter.security.SecurityUtils;
import com.cowell.pricecenter.service.dto.*;
import com.cowell.pricecenter.service.dto.request.FileDownloadTaskDTO;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.utils.ExportUtil;
import com.cowell.pricecenter.web.rest.util.*;
import com.cowell.pricecenter.web.rest.vo.BatchSyncOperatorVO;
import com.cowell.pricecenter.web.rest.vo.PriceWarningRecordVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description: 价格预警service
 * @date 2020/03/03 10:59
 */
@Slf4j
@Service
public class PriceWarnRecordService {

    @Resource
    private OmsService omsService;

    @Resource
    private PriceWarningRecordExtMapper priceWarningRecordExtMapper;

    @Resource
    private PriceWarningRecordMapper priceWarningRecordMapper;

    @Resource
    private PriceWarningProducer priceWarningProducer;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private FileService fileService;

    @Resource
    private PushZDTMsgService pushZDTMsgService;

    /**
     * 批量保存
     * @param recordDTOList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<PriceWarningRecordDTO> recordDTOList){
        validateDTO(recordDTOList);
        TokenUserDTO tokenUserDTO = SecurityUtils.getCurrentUserToken();
        String userId = null == tokenUserDTO ? "-1" : String.valueOf(tokenUserDTO.getUserId());
        List<PriceWarningRecord> recordList = recordDTOList.stream().map(item -> {
            PriceWarningRecord record = new PriceWarningRecord();
            BeanUtils.copyProperties(item, record);
            PriceWarningRecordExtend extend = new PriceWarningRecordExtend();
            extend.setBusNO(item.getBusNO());
            extend.setCompId(item.getCompId());
            extend.setBusinessName(item.getBusinessName());
            record.setExtend(JSONObject.toJSONString(extend));
            record.setUpdatedBy(userId);
            record.setCreatedBy(userId);
            return record;
        }).collect(Collectors.toList());
        List<List<PriceWarningRecord>> partition = Lists.partition(recordList, 150);
        partition.stream().forEach(records -> {
            priceWarningRecordExtMapper.batchInsert(recordList);
        });
    }

    /**
     * 更新状态 status = -1
     * @param record
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(PriceWarningRecord record,List<String> goodsNoList,Byte newStatus,String userId){
        priceWarningRecordExtMapper.updateStatus(record,goodsNoList,newStatus,userId);
    }

    /**
     * 批量更新保存
     * @param recordDTOList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchInsertAndUpdateStatus(List<PriceWarningRecordDTO> recordDTOList){
        log.info("==>PriceWarnRecordService||batchInsertAndUpdateStatus||批量更新保存价格预警信息,请求数据:{}", JSONObject.toJSONString(recordDTOList));
        //验证
        validateDTO(recordDTOList);
        Map<Long, List<PriceWarningRecordDTO>> PriceWarningRecordMap = recordDTOList.stream().collect(Collectors.groupingBy(PriceWarningRecordDTO::getStoreId));
        if(CollectionUtils.isEmpty(PriceWarningRecordMap)){
            log.info("<==PriceWarnRecordService||batchInsertAndUpdateStatus||根据门店分组后数据为空");
            return;
        }
        PriceWarningRecordMap.forEach((k,v) -> {
            Map<Integer, List<PriceWarningRecordDTO>> byPriceTypeMap = v.stream().collect(Collectors.groupingBy(PriceWarningRecordDTO::getPriceType));
            doBatchInsertAndUpdateStatus(byPriceTypeMap);
        });
        log.info("<==PriceWarnRecordService||batchInsertAndUpdateStatus||批量更新保存价格预警信息完成");
    }

    private synchronized void doBatchInsertAndUpdateStatus(Map<Integer, List<PriceWarningRecordDTO>> byPriceTypeMap){
        if(CollectionUtils.isEmpty(byPriceTypeMap)){
            return;
        }
        TokenUserDTO tokenUserDTO = SecurityUtils.getCurrentUserToken();
        String userId = null == tokenUserDTO ? "-1" : String.valueOf(tokenUserDTO.getUserId());
        PriceWarnRecordService priceWarnRecordService = GetApplicationContextUtils.getBean(PriceWarnRecordService.class);
        byPriceTypeMap.forEach((priceType,dataList) -> {
            List<String> goodsNoList = dataList.stream().map(item -> item.getGoodsNo()).distinct().collect(Collectors.toList());
            //更新
            priceWarnRecordService.updateStatus(dataList.get(0),goodsNoList,(byte)-1,userId);
            //保存
            priceWarnRecordService.batchSave(dataList);
        });
    }

    private void validateDTO(List<PriceWarningRecordDTO> recordDTOList){
        Assert.notEmpty(recordDTOList,"请求数据为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getBusinessId()),"连锁id不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getStoreId()),"门店id不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getStoreName()),"门店名称不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getGoodsNo()),"商品编码不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getSkuName()),"商品名称不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getPriceType()),"价格类型不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getSrcPrice()),"当前价格不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getNewPrice()),"待生效价格不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getLastUpdateTime()),"最后修改时间不能为空");
        Assert.isTrue(recordDTOList.stream().anyMatch(item -> null != item.getNewPrice()),"待生效价格不能为空");
//        Assert.isTrue(recordDTOList.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getBusNO())),"MDM门店编码不能为空");
//        Assert.isTrue(recordDTOList.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getCompId())),"MDM连锁编码不能为空");
    }

    /**
     * 列表
     */
    public Pagination<PriceWarningRecordVO> getPriceWarningRecordList(PriceWarningRecordVO vo){
        log.info("==>PriceWarnRecordService||getPriceWarningRecordList||列表查询，请求数据:{}||page:{}", JSONObject.toJSONString(vo));
        Assert.notNull(vo.getBusinessId(),"连锁id不能为空");
        Assert.isTrue(null != vo.getStartTime() && null != vo.getEndTime(),"日期不能为空");
        int days = DateUtils.differentDaysByMillisecond(vo.getStartTime(),vo.getEndTime());
        Assert.isTrue(days <= 30,"所选日期范围不能超过30天");
        //查询时间 默认为近3天，精确到年月日时分秒
        Map<String, Object> queryParamMap = getQueryParam(vo);
        List<PriceWarningRecord> list = priceWarningRecordExtMapper.selectByPage(queryParamMap);
        Pagination<PriceWarningRecordVO> pageDTO = new Pagination();
        if(CollectionUtils.isEmpty(list)){
            pageDTO.setSource(new ArrayList<>());
            return pageDTO;
        }
        int count = priceWarningRecordExtMapper.count(queryParamMap);
        List<PriceWarningRecordVO> respList = list.stream().map(item -> {
            PriceWarningRecordVO respVo = new PriceWarningRecordVO();
            BeanUtils.copyProperties(item,respVo);
            respVo.setPriceTypeDesc(PTypeEnum.getDesc(item.getPriceType()));
            respVo.setSrcPrice(BigDecimalUtils.convertYuanByFen(item.getSrcPrice()));
            respVo.setNewPrice(BigDecimalUtils.convertYuanByFen(item.getNewPrice()));
            respVo.setPriceWarnTypeDesc(PriceWarnTypeEnun.getMsg(item.getPriceWarnType()));
            String extendStr = item.getExtend();
            if(StringUtils.isNotEmpty(extendStr)){
                PriceWarningRecordExtend priceWarningRecordExtend = JSONObject.parseObject(extendStr, PriceWarningRecordExtend.class);
                respVo.setBusNO(priceWarningRecordExtend.getBusNO());
                respVo.setCompId(priceWarningRecordExtend.getCompId());
            }
            return respVo;
        }).collect(Collectors.toList());
        pageDTO.setPage(vo.getPage());
        pageDTO.setSize(vo.getPageSize());
        pageDTO.setHitCount(count);
        pageDTO.setTotalPages(count / vo.getPageSize() + 1);
        pageDTO.setSource(respList);
        log.info("<==PriceWarnRecordService||getPriceWarningRecordList||列表查询，返回:{}", JSONObject.toJSONString(respList));
        return pageDTO;
    }

    private Map<String,Object> getQueryParam(PriceWarningRecordVO vo){
        Map<String,Object> map = Maps.newHashMap();
        map.put("businessId",vo.getBusinessId());
        map.put("storeId",vo.getStoreId());
        map.put("startTime",vo.getStartTime());
        map.put("endTime",vo.getEndTime());
        map.put("keyWord",vo.getKeyWord());
        map.put("priceWarnType",vo.getPriceWarnType());
        map.put("current",vo.getPage() * vo.getPageSize());
        map.put("pageSize",vo.getPageSize());
        return map;
    }

    /**
     *  批量放弃同步||确认同步
     * @param vo
     */
    public void batchCancelAndConfimSync(BatchSyncOperatorVO vo) {
        log.info("==>PriceWarnRecordService||batchCancelAndConfimSync||批量放弃同步||确认同步，请求:{}", JSONObject.toJSONString(vo));
        Assert.notNull(vo,"请求数据不能为空");
        List<PriceWarningRecordVO> vos = vo.getVos();
        Assert.notEmpty(vos,"请求数据不能为空");
        Assert.isTrue(vos.stream().anyMatch(item -> null != item.getBusinessId()),"连锁id不能为空");
        Assert.isTrue(vos.stream().anyMatch(item -> null != item.getStoreId()),"门店id不能为空");
        Assert.isTrue(vos.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getGoodsNo())),"商品编码不能为空");
        Assert.isTrue(vos.stream().anyMatch(item -> null != item.getPriceType()),"价格类型不能为空");
//        Assert.isTrue(vos.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getBusNO())),"MDM门店编码不能为空");
//        Assert.isTrue(vos.stream().anyMatch(item -> StringUtils.isNotEmpty(item.getCompId())),"MDM连锁编码不能为空");
        Assert.isTrue(vos.stream().anyMatch(item -> null != item.getPriceWarnType()),"异常类型不能为空");
        Map<Long, List<PriceWarningRecordVO>> map = vos.stream().collect(Collectors.groupingBy(PriceWarningRecordVO::getStoreId));
        if(CollectionUtils.isEmpty(map)){
            log.warn("<==PriceWarnRecordService||batchCancelAndConfimSync||根据门店id分组后数据为空");
            return;
        }
        map.forEach((k,v) -> {
            Map<Integer, List<PriceWarningRecordVO>> priceTypeMap = v.stream().collect(Collectors.groupingBy(PriceWarningRecordVO::getPriceType));
            priceTypeMap.forEach((priceType,dataList) -> {
                BatchSyncOperatorVO batchSyncOperatorVO = new BatchSyncOperatorVO();
                batchSyncOperatorVO.setStatus(vo.getStatus());
                batchSyncOperatorVO.setVos(dataList);
                batchSyncOperatorVO.setCurrentUserToken(SecurityUtils.getCurrentUserToken());
                priceWarningProducer.sendMq(batchSyncOperatorVO);
            });
        });
        log.info("<==PriceWarnRecordService||getPriceWarningRecordList||批量放弃同步=确认同步完成");
    }

    /**
     * 导出筛选结果
     * @param vo
     */
    public String export(PriceWarningRecordVO vo) {
        log.info("==>PriceWarnRecordService||export||导出筛选结果，请求数据:{}", JSONObject.toJSONString(vo));
        Assert.notNull(vo.getBusinessId(),"连锁id不能为空");
        Assert.isTrue(null != vo.getStartTime() && null != vo.getEndTime(),"日期不能为空");
        int days = DateUtils.differentDaysByMillisecond(vo.getStartTime(),vo.getEndTime());
        Assert.isTrue(days <= 30,"所选日期范围不能超过30天");
//        String uuid = UUID.randomUUID().toString().replaceAll("-","");
//        String key = RedisKeysConstant.PRICE_EXPORT_KEY.concat(uuid);
//        RBucket<Integer> bucket = redissonClient.getBucket(key);
//        bucket.set(ExportEnum.PROCESSING.getCode(),TIMES, TimeUnit.SECONDS);

        Long loginUserId = SecurityUtils.getCurrentUserToken().getUserId();
        ThreadUtils.submit(() -> {
            String taskName = loginUserId + "_" + vo.getBusinessId() + "_" +"价格预警清单";
            Map<String, Object> queryParamMap = getQueryParam(vo);
            List<PriceWarningRecord> list = priceWarningRecordExtMapper.queryList(queryParamMap);
//            if(CollectionUtils.isEmpty(list)){
//                log.info("<==PriceWarnRecordService||export||导出筛选结果,查询结果为空param:{}", JSONObject.toJSONString(vo));
//                bucket.set(ExportEnum.NODATA.getCode(),TIMES, TimeUnit.SECONDS);
//                return;
//            }
            List<PriceWarningRecordVO> priceWarningRecordVOList = Optional.ofNullable(list).orElse(Lists.newArrayList()).stream().map(item -> {
                PriceWarningRecordVO priceWarningRecordVO = new PriceWarningRecordVO();
                BeanUtils.copyProperties(item, priceWarningRecordVO);
                priceWarningRecordVO.setSrcPrice(BigDecimalUtils.convertYuanByFen(item.getSrcPrice()));
                priceWarningRecordVO.setNewPrice(BigDecimalUtils.convertYuanByFen(item.getNewPrice()));
                priceWarningRecordVO.setPriceTypeDesc(PTypeEnum.getDesc(item.getPriceType()));
                priceWarningRecordVO.setPriceWarnTypeDesc(PriceWarnTypeEnun.getMsg(item.getPriceWarnType()));
                return priceWarningRecordVO;
            }).collect(Collectors.toList());
            LinkedHashMap<String, String> map = ExportUtil.getPriceWarnPropertyMap();
            byte[] fileByte = new byte[0];
            try {
                fileByte = ExcelUtils.listToExcel(taskName, priceWarningRecordVOList, map, "sheet0", 65535, new ArrayList<>());
            } catch (Exception e) {
                log.warn("<==PriceWarnRecordService||export||导出筛选结果,异常||{}",JSONObject.toJSONString(vo));
//                bucket.set(ExportEnum.FAIL.getCode(),TIMES, TimeUnit.SECONDS);
//                return;
            }
            FileDTO fileDTO = new FileDTO(loginUserId,JSONObject.toJSONString(vo), taskName, "/api/price/warn/record/export",vo.getStartTime(),vo.getEndTime());
            FileDownloadTaskDTO fileDownloadTaskDTO = fileService.createDownloadCenterTaskStart(fileDTO);
//            if(null == fileDownloadTaskDTO){
//                return;
//            }
            fileService.createDownloadCenterTaskEnd(fileDownloadTaskDTO, taskName, fileByte,"fileDownloader/price/waring/export");
            log.info("<==PriceWarnRecordService||export||导出筛选结果结束", JSONObject.toJSONString(vo));
        });
        return "ok";
    }


    /**
     * 获取导出筛选结果
     * @param uuid
     * @return
     */
    public Integer exportResult(String uuid) {
        log.info("==>PriceWarnRecordService||export||获取导出筛选结果{}",uuid);
        Assert.notNull(uuid,"参数不能为空");
        String key = RedisKeysConstant.PRICE_EXPORT_KEY.concat(uuid);
        RBucket<Integer> bucket = redissonClient.getBucket(key);
        Integer code = bucket.get();
        log.info("<==PriceWarnRecordService||export||导出筛选结果,返回:{}",code);
        return code;
    }

    /**
     * 推送价格预警信息到智店通
     */
    public void pushPriceWarn2WisdomStore(String businessIdStr) {
        log.info("==>PriceWarnRecordService||pushPriceWarn2WisdomStore||推送价格预警信息到智店通");
        List<Long> businessIds = null;
        if(StringUtils.isNotEmpty(businessIdStr)){
            String[] ids = businessIdStr.split(",");
            businessIds = Arrays.stream(ids).filter(id -> StringUtils.isNotEmpty(id)).map(id -> Long.valueOf(id)).distinct().collect(Collectors.toList());
        }else{
            businessIds = omsService.getBusinessIds();
        }
        if(CollectionUtils.isEmpty(businessIds)){
            log.warn("<==PriceWarnRecordService||pushPriceWarn2WisdomStore||连锁id为空");
            return;
        }
        businessIds.forEach(businessId-> {
            List<PushMsgDTO> pushMsgDTOList = priceWarningRecordExtMapper.queryPushMsg(businessId);
            log.info("<==PriceWarnRecordService||pushPriceWarn2WisdomStore||查询价格预警信息如下:{}||businessId:{}", JSONObject.toJSONString(pushMsgDTOList), businessId);
            if(CollectionUtils.isEmpty(pushMsgDTOList)){
                return;
            }
            int tjycNum = pushMsgDTOList.stream().filter(item -> 0 == item.getPriceWarnType()).map(item -> item.getNum()).findFirst().orElse(0);
            int hdycNum = pushMsgDTOList.stream().filter(item -> 1 == item.getPriceWarnType()).map(item -> item.getNum()).findFirst().orElse(0);
            pushZDTMsgService.pushZDTMsgPriceWarn(businessId,hdycNum,tjycNum);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletePriceWarnARecord(PriceWarningRecordDTO dto) {
        log.info("==>PriceWarnRecordService||deletePriceWarnRecordRecord||删除价格告警日志,{}",JSONObject.toJSONString(dto));
        Assert.notNull(dto.getBusinessId(),"连锁id不能为空");
        Assert.notNull(dto.getGmtCreate(),"日期不能为空");
        //删除价格告警信息
        PriceWarningRecordExample priceWarningRecordExample = new PriceWarningRecordExample();
        PriceWarningRecordExample.Criteria criteria = priceWarningRecordExample.createCriteria();
        criteria.andBusinessIdEqualTo(dto.getBusinessId());
        criteria.andStatusEqualTo((byte)-1);
        criteria.andGmtCreateLessThanOrEqualTo(dto.getGmtCreate());
        int count = priceWarningRecordMapper.deleteByExample(priceWarningRecordExample);
        log.info("<==PriceWarnRecordService||deletePriceWarnRecordRecord||删除价格告警日志完成,数量:{}={}",count,JSONObject.toJSONString(dto));
    }

}
