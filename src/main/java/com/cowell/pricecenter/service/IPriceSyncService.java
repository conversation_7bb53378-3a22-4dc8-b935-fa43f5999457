package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceChange;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.enums.PTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/19 11:12
 */
public interface IPriceSyncService {

    /**
     * 根据价格类型处理价格同步逻辑
     * @param priceChange 价格变动通知
     * @param pTypeEnum 价格类型
     * @return true:处理成功
     */
    boolean doSyncByPriceType(PriceChange priceChange, PTypeEnum pTypeEnum);

    /**
     * 同步扩展中配置的三方渠道价格
     * @param priceStoreDetail 价格
     * @return 保存成功的价格
     */
    List<PriceStoreDetail> syncExtendPriceType(PriceStoreDetail priceStoreDetail);

    /**
     * 同步价格到线上三方渠道
     * @param storeId 门店ID
     * @param businessId 连锁ID
     * @param allSavePrice 需要同步的价格
     */
    void syncToPlatform(Long storeId, Long businessId, List<PriceStoreDetail> allSavePrice);

    /**
     * 同步中心店
     * @param priceStoreDetail 价格
     * @return true：同步成功
     */
    boolean syncCenterStore(PriceStoreDetail priceStoreDetail);

    /**
     * 同步到商品中台
     * @param priceStoreDetail 价格
     */
    void syncItemCenter(PriceStoreDetail priceStoreDetail);

    /**
     * 同步到商品中台
     * @param storeId 门店
     * @param priceTypeCode 价格类型
     * @param channel 价格渠道
     */
    void syncItemCenter(Long storeId, String priceTypeCode, int channel);

    /**
     * 根据价格类型处理价格特价标签同步逻辑
     * @param priceChange 价格变动通知
     * @param pTypeEnum 价格类型
     * @return int
     */
    int doChangePriceSpecial(PriceChange priceChange, PTypeEnum pTypeEnum);
}
