/**
* @mbg.generated
* generator on Fri Mar 18 15:58:59 CST 2022
*/
package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.AdjustPriceOrderMqResult;
import com.cowell.pricecenter.entity.AdjustPriceOrderMqResultExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AdjustPriceOrderMqResultService {
    /**
    * insert
    * @param row row
    * @return int int
    */
    int insert(AdjustPriceOrderMqResult row);

    /**
    * insertSelective
    * @param row row
    * @return int int
    */
    int insertSelective(AdjustPriceOrderMqResult row);

    int updateByExampleSelective(AdjustPriceOrderMqResult row, AdjustPriceOrderMqResultExample example);

    /**
     * countByExample
     * @param example example
     * @return long long
     */
    long countByExample(AdjustPriceOrderMqResultExample example);

    /**
     * 查询连锁ID
     * @param adjustCode 调价单号
     * @return 连锁ID
     */
    List<Long> selectDistinctBusinessIdByAdjustCode(String adjustCode);

    /**
     * deleteByExample
     * @param example example
     * @return int int
     */
    int deleteByExample(AdjustPriceOrderMqResultExample example);

    int deleteByAdjustCode(String adjustCode);

    /**
     * 根据调价单号和门店ID查询
     * @param adjustCode 调价单号
     * @param storeId 门店ID
     * @return AdjustPriceOrderMqResult
     */
    AdjustPriceOrderMqResult selectByAdjustAndStoreId(String adjustCode, Long storeId);

    List<String> selectDistinctAdjustCodeByGmtCreate(Date gmtCreate);
}
