package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.feign.vo.PriceModeResult;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import java.util.List;
import java.util.Optional;

/**
 * @program: pricecenter
 * @description: 促销系统服务的封装接口
 * @author: jmlu
 * @create: 2022-04-07 10:59
 **/

public interface IMarketingExtService {


    /**
     * 根据商品编码和组织结构ID获取众数
     *
     * @param goodsNo
     * @param orgIdList
     * @return
     */
    Optional<PriceModeResult> getPriceModel(String goodsNo,  List<Long> orgIdList);

    /**
     * 根据商品编码和组织结构ID获取众数
     *
     * @param goodsNo
     * @param orgLevelVOList
     * @return
     */
    Optional<PriceModeResult> getPriceModeByOrgLevelVO(String goodsNo, List<OrgLevelVO> orgLevelVOList);
}
