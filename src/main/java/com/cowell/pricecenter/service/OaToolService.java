package com.cowell.pricecenter.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import java.util.Objects;

/**
 * Created by schuangxigang on 2022/5/5 23:06.
 */
@Slf4j
@Service
public class OaToolService {
    @Value("${price.3rd.oa.verify_code_salt:}")
    private String oaVerifyCodeSalt;
    @Value("${price.3rd.oa.verify:true}")
    private boolean oaVerify;

    public Boolean verifyCode(String orderNo, String verifyCode) {
        if (!oaVerify) {
            return true;
        }
        String verifyStr = orderNo + oaVerifyCodeSalt;
        String md5Code = DigestUtils.md5DigestAsHex(verifyStr.getBytes()).toUpperCase();
        return Objects.equals(verifyCode, md5Code);
    }
}
