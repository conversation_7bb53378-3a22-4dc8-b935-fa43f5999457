//package com.cowell.pricecenter.service;
//
//import com.cowell.pricecenter.service.dto.request.PriceManageStatusParam;
//import com.cowell.pricecenter.service.dto.response.amis.SelectorResult;
//
///**
// * 价格状态或类型服务
// */
//public interface PriceManageStatusService {
//
//    /**
//     * 获取信息
//     * @param statusParam
//     */
//    SelectorResult getInfo(PriceManageStatusParam statusParam);
//
//    /**
//     * 获取接口标识
//     * @return
//     */
//    Integer getCode();
//}
