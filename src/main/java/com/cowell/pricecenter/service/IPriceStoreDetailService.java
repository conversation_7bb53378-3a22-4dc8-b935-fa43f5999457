package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.entity.UnPriceStoreDetail;
import com.cowell.pricecenter.service.dto.StoreCostDTO;
import com.cowell.pricecenter.service.dto.request.PriceQueryParam;
import com.cowell.pricecenter.service.dto.request.PriceStoreDetailParam;
import com.cowell.pricecenter.service.dto.request.PriceSyncInsertlParam;
import com.cowell.pricecenter.service.dto.response.*;
import com.cowell.pricecenter.service.vo.GoodsOfJointVo;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import com.cowell.pricecenter.web.rest.vo.PriceSaveOrUpdateVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;


public interface IPriceStoreDetailService {

    String priceListImport(MultipartFile file) throws Exception;

    ImportPriceStoreDetailResponse getPriceListImportStatus(String key) throws Exception;

    int delPriceStoreDetailBuessIdIsNull();

    PriceQueryNewDTO getPricedCount(PriceStoreDetailParam param);

    Map<String, Object> getPricedStoreDetailList(PriceStoreDetailParam param);

    Map<String, Object> getUnPricedStoreDetailList(PriceStoreDetailParam param);

    List<UnPriceStoreDetail> downloadUnPricedDetailList(HttpServletRequest request);

    Boolean insertSync(PriceSyncInsertlParam priceSyncInsertlParam, SpuNewVo spuNewVo, boolean isWarning);

    boolean confirmSync(PriceSyncInsertlParam priceSyncInsertlParam);

    void pushWarnRecord(PriceStoreDetail priceStoreDetaildb, BigDecimal newPrice, String busNo, String compId, Byte priceSource, Byte priceWarnType);

    CommonResponse saveOrUpdateForHL(PriceSaveOrUpdateVO param);

    CommonResponse saveOrUpdateForHLByType(PriceSaveOrUpdateVO param);


    List<PriceQueryGoodsNoDTO> getPriceByType(PriceQueryParam param);

    /**
     * 获取零售价 和会员价
     * @param param
     * @return
     */
    List<PriceCompareDTO> getPriceAndMemberPrice(PriceQueryParam param);

    /**
     * 获取所有的POS端的价格
     * @param param
     * @return
     */
    List<PriceQueryGoodsNoDTO> getAllPOSPricePage(PriceQueryParam param);

     boolean syncHdLSJtoGJYJSStep1(Map<String, PriceType> priceTypeMap, PriceSyncConfigDTO priceSyncConfigDTO,
                                         boolean isWarning, String priceTypeCode, PriceStoreDetail priceStoreDetail, PriceSyncInsertlParam priceSyncInsertlParam,
                                         long storeId);

     void saveGoodsOfJointInfo(GoodsOfJointVo vo);

     void addDetailForStoreCost(StoreCostDTO vo, SpuNewVo spuNewVo);

    /**
     * 同步成本价消息
     * @param vo
     */
     void addItemCostPrice(StoreCostDTO vo);


    /**
     * 更新价格信息
     * @param priceStoreDetail
     */
    void updatePriceStoreDetail(PriceStoreDetail priceStoreDetail);

    /**
     * 根据商品编码、门店ID、价格类型Code获取价格
     * @param goodsNo
     * @param storeId
     * @param priceTypeCode
     * @param channelIdList
     * @return
     */
    Optional<BigDecimal> getPriceByGoodsAndStoreAndPriceType(String goodsNo, Long storeId, String priceTypeCode,
                                                             List<Integer> channelIdList);


    /**
     * 根据门店ID和相关ID获取价格
     * @param param PriceQueryParam
     * @return List<PriceQueryDTO>
     */
    List<PriceQueryDTO> getPriceByStoreIdAndRelateNo(PriceQueryParam param);

}
