/**
* @mbg.generated
* generator on Fri Mar 18 13:56:06 CST 2022
*/
package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceControlNotice;
import com.cowell.pricecenter.entity.PriceControlNoticeExample;
import com.cowell.pricecenter.service.dto.PriceControlNoticeQueryDTO;

import java.util.List;

public interface PriceControlNoticeService {
    /**
     * deleteByPrimaryKey
     * @param id id
     * @return int int
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert
     * @param row row
     * @return int int
     */
    int insert(PriceControlNotice row);

    /**
     * insertSelective
     * @param row row
     * @return int int
     */
    int insertSelective(PriceControlNotice row);

    /**
     * selectByPrimaryKey
     * @param id id
     * @return PriceControlNotice PriceControlNotice
     */
    PriceControlNotice selectByPrimaryKey(Long id);

    /**
     * updateByPrimaryKeySelective
     * @param row row
     * @return int int
     */
    int updateByPrimaryKeySelective(PriceControlNotice row);

    /**
     * updateByPrimaryKey
     * @param row row
     * @return int int
     */
    int updateByPrimaryKey(PriceControlNotice row);

    long countByExample(PriceControlNoticeExample example);

    int deleteByExample(PriceControlNoticeExample example);

    int insertOrUpdate(PriceControlNotice record);

    int insertOrUpdateSelective(PriceControlNotice record);

    List<PriceControlNotice> selectByExample(PriceControlNoticeExample example);

    int updateByExampleSelective(PriceControlNotice record, PriceControlNoticeExample example);

    int updateByExample(PriceControlNotice record, PriceControlNoticeExample example);

    int updateBatch(List<PriceControlNotice> list);

    int updateBatchSelective(List<PriceControlNotice> list);

    int batchInsert(List<PriceControlNotice> list);

    /**
     * 查询管控通知总数
     * @param queryDTO
     * @return long
     */
    long countPriceControlNotice(PriceControlNoticeQueryDTO queryDTO);

    /**
     * 查询管控通知列表
     * @param queryDTO
     * @return List<PriceControlNotice>
     */
    List<PriceControlNotice> getPriceControlNoticeListByParam(PriceControlNoticeQueryDTO queryDTO);

    /**
     * 保存管控通知
     * @param list 管控通知列表
     * @return int
     */
    int saveControlNotice(List<PriceControlNotice> list);
}




