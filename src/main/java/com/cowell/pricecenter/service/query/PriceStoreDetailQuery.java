package com.cowell.pricecenter.service.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 16:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PriceStoreDetailQuery {

    @ApiModelProperty(value="门店id")
    private Long storeId;

    @ApiModelProperty(value="连锁机构ID")
    private Long businessId;

    @ApiModelProperty(value="商品编码")
    private String goodsNo;
    @ApiModelProperty(value="商品编码集合")
    private List<String> goodsNoList;

    @ApiModelProperty(value="价格类型")
    private String priceTypeCode;
    @ApiModelProperty(value="价格类型集合")
    private List<String> priceTypeCodeList;

    @ApiModelProperty(value="过滤价格类型集合")
    private List<String> filterPriceTypeCodeList;

    @ApiModelProperty(value="调价单号")
    private String adjustCode;

    @ApiModelProperty(value="对应的价格渠道id")
    private Integer channelId;
    @ApiModelProperty(value="对应的价格渠道id集合")
    private List<Integer> channelIdList;

    @ApiModelProperty(value="待生效调价单号")
    private String nextPriceDxsAdjustCode;

    @ApiModelProperty(value="商品skuId")
    private Long skuId;
    @ApiModelProperty(value="商品skuId集合")
    private List<Long> skuIdList;

    @ApiModelProperty(value="当前页 从1开始")
    private Integer page;

    @ApiModelProperty(value="每页数量")
    private Integer pageSize;

    @ApiModelProperty(value="调价权限(1集团,2平台,3连锁,4门店)")
    private Integer level;

    @ApiModelProperty(value="多条件匹配参数(通用名curName(price_org_goods表))")
    private String extendLike;

    @ApiModelProperty(value="价格是否为空")
    private Boolean priceIsNotNull;

    @ApiModelProperty(value="状态(-1删除，0正常)")
    private Integer status;

    @ApiModelProperty(value="ID集合")
    private List<Long> idList;

    @ApiModelProperty(value="商品通用名模糊查询")
    private String curNameLike;

    @ApiModelProperty(value="商品编码模糊查询")
    private String goodsNoLike;

    @ApiModelProperty(value="调价单号模糊查询")
    private String adjustCodeLike;

    @ApiModelProperty(value="修改时间大于")
    private Date gmtUpdateGreaterThan;

    @ApiModelProperty(value="排序")
    private String orderBy;

    @ApiModelProperty(value="商品spuId")
    private Long spuId;
    @ApiModelProperty(value="商品spuId集合")
    private List<Long> spuIdList;

    /**
     * 渠道门店id
     */
    private Long channelStoreId;

    /**
     * 加盟店价格组
     */
    private String priceGroup;
}
