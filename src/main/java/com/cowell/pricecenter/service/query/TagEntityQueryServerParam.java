package com.cowell.pricecenter.service.query;

import lombok.Data;

import java.util.List;

/**
 * 
 * @ClassName:  TagEntityQueryServerParam   
 * @author: my
 * @date:   2022年7月19日 下午5:01:32      
 * @Copyright:
 */
@Data
public class TagEntityQueryServerParam {

    /**
     * 标签位
     */
    private Integer tagIndex ;

    /**
     * 标签位
     */
    private List<Integer> tagIndexList ;

    /**
     * 标签位
     * 必传
     */
    private List<String> tagCodeList ;


    /**
     * 标签id
     * 必传
     */
    private Integer tagType;
    /**
     * 标签id
     * 必传
     */
    private Long tagId;
    /**
     * 标签业务渠道
     * 必传
     */
    private Integer tagBizType ;

    /**
     * 实体类型
     * 必传
     */
    private Integer entityType ;

    /**
     * 服务端需要传递具体类型不要传递全部
     */
    private Integer entitySelectType ;


    /**
     * 关键字，标题或者编码
     *
     */
    private String key ;


    private Integer page= 1;

    private Integer prePage = 200;

    /**
     * 标签id List
     *
     */
    private List<Long> tagIdList ;

    /**
     * 返回类型
     * 0：返回实体详情
     * 1：返回实体code
     * 2: 返回管理端详情
     *  com.cowell.tag.entity.enums.ResultTypeEnums
     */
    private Integer resultType=0;

    /**
     * 是否是管理端请求
     * 0：否
     * 1：是
     *com.cowell.tag.entity.enums.YNEnums
     */
    private Integer managerFlag=0;

    /**
     * 搜索标签关键字
     *
     */
    private String tagKey ;

}
