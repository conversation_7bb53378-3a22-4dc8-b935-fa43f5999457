package com.cowell.pricecenter.service.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * adjust_price_order_org_store_detail
 * <AUTHOR>
@ApiModel(value="com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail调价单组织门店明细表")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AdjustPriceOrderOrgStoreDetailQuery implements Serializable {
    /**
     * 主键id
     */
    @ApiModelProperty(value="主键id")
    private Long id;

    /**
     * 分布式ID
     */
    @ApiModelProperty(value="分布式ID")
    private String orgStoreDetailId;

    /**
     * 调价单编码
     */
    @ApiModelProperty(value="调价单编码")
    private String adjustCode;

    /**
     * 机构id
     */
    @ApiModelProperty(value="机构id")
    private Long orgId;

    /**
     * 机构名称
     */
    @ApiModelProperty(value="机构名称")
    private String orgName;

    /**
     * 调价级别
     */
    @ApiModelProperty(value="调价级别")
    private Integer adjustPriceLevel;

    /**
     * 价格类型
     */
    @ApiModelProperty(value = "价格类型")
    private String priceTypeCode;
    /**
     * 门店ID
     */
    @ApiModelProperty(value="门店ID")
    private Long storeId;

    /**
     * 门店名称
     */
    @ApiModelProperty(value="门店名称")
    private String storeName;

    /**
     * 连锁机构ID
     */
    @ApiModelProperty(value="连锁机构ID")
    private Long businessId;

    /**
     * 连锁名称
     */
    @ApiModelProperty(value="连锁名称")
    private String businessName;

    /**
     * 平台机构ID
     */
    @ApiModelProperty(value="平台机构ID")
    private Long platformId;

    /**
     * 平台名称
     */
    @ApiModelProperty(value="平台名称")
    private String platformName;

    /**
     * 状态(-1删除，0未处理，1处理中，2处理成功，3处理失败)
     */
    @ApiModelProperty(value="状态(-1删除，0未处理，1处理中，2处理成功，3处理失败)")
    private Byte status;

    /**
     * 调价单执行时间
     */
    @ApiModelProperty(value="调价单执行时间")
    private Date adjustExecuteTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private Date gmtCreate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="更新时间")
    private Date gmtUpdate;
    
    /**
     * 页码
     */
    @ApiModelProperty(value="页码")
    private int page;
    
    /**
     * 每页显示数量
     */
    @ApiModelProperty(value="每页显示数量")
    private int pageSize;

    private static final long serialVersionUID = 1L;
}
