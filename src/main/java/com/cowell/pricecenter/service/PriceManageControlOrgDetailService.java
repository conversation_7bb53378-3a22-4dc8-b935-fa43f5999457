package com.cowell.pricecenter.service;

import java.util.List;

import com.cowell.pricecenter.entity.PriceManageControlOrgDetail;
import com.cowell.pricecenter.entity.PriceManageControlOrgDetailExample;
import com.cowell.pricecenter.service.dto.ControlOrderOrgDetailQueryDTO;

/**
 * <AUTHOR>
 * @date 2022/3/18 11:02
 */

public interface PriceManageControlOrgDetailService {


    long countByExample(PriceManageControlOrgDetailExample example);

    int deleteByExample(PriceManageControlOrgDetailExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PriceManageControlOrgDetail record);

    int insertSelective(PriceManageControlOrgDetail record);

    List<PriceManageControlOrgDetail> selectByExample(PriceManageControlOrgDetailExample example);

    PriceManageControlOrgDetail selectByPrimaryKey(Long id);

    int updateByExampleSelective(PriceManageControlOrgDetail record, PriceManageControlOrgDetailExample example);

    int updateByExample(PriceManageControlOrgDetail record, PriceManageControlOrgDetailExample example);

    int updateByPrimaryKeySelective(PriceManageControlOrgDetail record);

    int updateByPrimaryKey(PriceManageControlOrgDetail record);

    List<PriceManageControlOrgDetail> getControlOrgDetailList(ControlOrderOrgDetailQueryDTO detailQueryDTO);

    List<PriceManageControlOrgDetail> getControlOrgDetailListByCode(String controlOrderCode);

}
