package com.cowell.pricecenter.service;

import com.cowell.permission.dto.*;
import com.cowell.permission.vo.CrmStoreVO;
import com.cowell.pricecenter.enums.OrgLevelTypeEnum;
import com.cowell.pricecenter.enums.OrgTypeEnum;
import com.cowell.pricecenter.service.dto.OrgSummaryDTO;
import com.cowell.pricecenter.service.dto.StaffNumDTO;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.OrgStoreBusinessDTO;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.controlOrder.TypeInfo;
import com.cowell.pricecenter.service.vo.OrgLevelVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: yadi
 * @date: 2018-11-05 17:55
 **/
public interface IPermissionExtService {

    TokenUserDTO getCurrentUserToken();

    List<OrgStoreBusinessDTO> getOrgStoreBusinessBatch(String orgIds);

    List<OrgLevelVO> listByOrgIdAndLevel(Long orgId, Integer level);

    List<OrgLevelVO> getByUserId(Long userId);

    OrgLevelVO getPermission(Long storeId);

    OrgLevelVO getPermissionByOrgId(Long orgId);

    List<OrgLevelVO> listByOrgIdAndType(Long orgId, Integer type);

    List<Long> getTreeOrgIds(Long orgId);

    Map<Long, OrgLevelVO> listPermissionByOrgIds(List<Long> orgIds);

    Map<Long, List<Long>> getTreeOrgIdsMap(List<Long> orgIds);

    /**
     * 根据SapCode批量查询组织信息，默认查连锁级别
     * @param sapcodes
     * @return
     */
    Map<String, OrgLevelVO> queryOrgInfoBySapCodes(List<String> sapcodes);

    /**
     * 获取每个组织机构ID对应的包含自己及其父祖辈组织列表(列表按照组织顺序由高到低)
     *
     * @param orgIdList 机构列表
     * @return 所有上级组织
     */
     Map<Long, List<OrgDTO>> getAncestorsOrgList(List<Long> orgIdList);

    /**
     * 获取用户数据权限组织id集合(只返回500)
     * @param userId
     * @param resourceId
     * @return
     */
    List<Long> getUserDataOrgList(long userId, long resourceId);

    /**
     * 检查用户的数据权限,全部符合才为true
     * @param userId
     * @param resourceId
     * @param orgIdList
     * @return
     */
    boolean checkUserOrgPermissions(long userId, long resourceId, List<Long> orgIdList);

    /**
     * 获取用户有权限的直接组织下级
     * @param userId
     * @param resourceId
     * @param orgId
     * @return
     */
    List<OrgDTO> getUserOrgChildByOrgId(long userId, long resourceId, long orgId);

    /**
     * 获取所有下级组织机构的组织id集合
     * @param parentId
     * @return
     */
    List<Long> listAllChildOrgIdByParentId(long parentId);

    /**
     * 当前用户权限范围内的指定类型组织
     *
     * @param userId
     * @param resourceId
     * @param orgType
     * @param treeType
     * @return
     */
    List<OrgDTO> getUserDataScopeOrgByType(Long userId, Long resourceId, Integer orgType, String treeType);

    /**
     * 当前用户权限范围内的指定类型orgId集合
     *
     * @param userId
     * @param resourceId
     * @param orgType
     * @param treeType
     * @return
     */
    List<Long> getUserDataScopeOrgIdListByType(Long userId, Long resourceId, Integer orgType, String treeType);

    /**
     * 获取当前用户指定平台下的门店信息
     * @param userId
     * @param platOrgId
     * @return
     */
    List<Long> getUserStoreOrgIdByPlatform(Long userId, Long platOrgId);

    List<Long> getUserStoreIdByPlatform(Long userId, Long platOrgId);

    /**
     * 当前用户权限范围内的指定类型outId集合
     *
     * @param userId
     * @param resourceId
     * @param orgType
     * @param treeType
     * @return
     */
    List<Long> getUserDataScopeOutIdListByType(Long userId, Long resourceId, Integer orgType, String treeType);

    /**
     * 批量获取用户权限范围内的指定类型组织
     *
     * @param userIds
     * @param orgType
     * @return
     */
    Map<Long, List<OrgDTO>> getBatchUserScopeOrgByType(List<Long> userIds, Integer orgType);

    /**
     * 批量获取用户下的门店
     * @param userId
     * @param orgType
     * @return
     */
    List<Long> getCurrUserBelowStoreIdList(Long userId, Integer orgType);

    /**
     * 批量获取用户下的门店信息
     * @param userId
     * @param orgType
     * @return
     */
    List<OrgDTO> getCurrUserBelowStoreInfoList(Long userId, Integer orgType);

    /**
     * 通过userid和resourceid查询权限配置信息（如渠道、价格类型等）
     * @param userId
     * @param resourceId
     * @return
     */
    List<TypeInfo<String,String>> getPermInfoById(Long userId, Long resourceId);

    /**
     * 判断某个功能类型是否在本用户的权限范围内
     * @param userId
     * @param resourceId
     * @param codeList
     * @return
     */
    Boolean judgeInPermission(Long userId, Long resourceId, List<String> codeList);

    /**
     * 通过outId获取组织ID
     * @param outIdList
     * @param type
     * @return
     */
    List<Long> listOrgIdByOutId(List<Long> outIdList, Integer type);

    /**
     * 根据用户userId获取用户详情信息
     * @param userId
     * @param resourceId
     * @return
     */
    EmployeeDetailDTO getEmployeeDetailByUserIdAndResourceId(Long userId, Long resourceId);

    /**
     * 判断该用户是否有roleCode角色
     * @return
     */
    Boolean judgeRoleInPerm(String roleCode);

    /**
     * 渠道列表
     * @return
     */
    List<TypeInfo<String,String>> getPriceChannelList();

    /**
     * 价格类型列表
     * @return
     */
    List<TypeInfo<String,String>> getPriceTypeList();

    /**
     * 权限商品范围
     * @return
     */
    List<TypeInfo<String,String>> getPermGoodsList();

    /**
     * 已有orgIds和用户数据权限取交集(输出orgIdList)
     *
     * @param userId
     * @param resourceId
     * @param orgType
     * @param treeType
     * @param orgIds
     * @return
     */
    List<Long> retainUserDataScopeOrgIdListByType(Long userId, Long resourceId, Integer orgType, String treeType, List<Long> orgIds);

    /**
     * 已有orgIds和用户数据权限取交集(输出orgIdList)
     *
     * @param userId
     * @param orgType
     * @param orgIds
     * @return
     */
    List<Long> retainUserDataScopeOrgIdListByType(Long userId, Integer orgType, List<Long> orgIds);

    /**
     * 已有orgIds和用户数据权限取交集(输出outIdList)
     *
     * @param userId
     * @param resourceId
     * @param orgType
     * @param treeType
     * @param orgIds
     * @return
     */
    List<Long> retainUserDataScopeOutIdListByType(Long userId, Long resourceId, Integer orgType, String treeType, List<Long> orgIds);

    /**
     * 已有orgIds和用户数据权限取交集(输出outIdList)
     *
     * @param userId
     * @param orgType
     * @param orgIds
     * @return
     */
    List<Long> retainUserDataScopeOutIdListByType(Long userId, Integer orgType, List<Long> orgIds);

    /**
     * 获取商品搜索页的连锁信息
     * @param levelVOList
     * @return
     */
    List<OptionDto> getGoodsBusinessList(List<OrgLevelVO> levelVOList);

    /**
     * 获取商品搜索页的门店信息
     * @param levelVOList
     * @return
     */
    List<OptionDto> getGoodsStoreList(List<OrgLevelVO> levelVOList);

    /**
     * 根据类型批量查询组织机构节点下指定类型组成的阉割版的树
     *
     * @param rootOrgId
     * @param types
     * @return
     */
    List<OrgTreeDTO> listOrgTreesByRootOrgIdAndTypes(Long rootOrgId, List<Integer> types);

    /**
     * 获取组织信息
     * @param orgId
     * @return
     */
    OrgLevelVO getOrgLevelVOByOrgId(Long orgId);

    /**
     * 批量获取组织信息
     * @param userId
     * @param resourceId
     * @param orgIdList
     * @param isRecursion
     * @return
     */
    List<OrgDTO> getUserDataScopeChildOrgByOrgId(Long userId, Long resourceId, List<Long> orgIdList, Integer isRecursion);

    /**
     * 获取下级组织
     * @param orgIdList
     * @param targetType
     * @return
     */
    List<OptionDto> permisssionSonOrgInfo(List<Long> orgIdList, Integer targetType);

    /**
     * 查询当前组织机构信息，返回带isFullScope
     * @param userId
     * @param orgIdList
     * @return
     */
    List<OrgDTO> findIsFullScopeOrgIdList(Long userId, List<Long> orgIdList);

    /**
     * 查询有全部权限的组织ID集合
     * @param userId
     * @param orgIdList
     * @return
     */
    List<Long> findIsFullScopeSonOrgIdList(Long userId, List<Long> orgIdList);

    /**
     * 获取下级组织
     * @param orgIdList
     * @param orgLevelTypeEnum
     * @return
     */
    List<OrgLevelVO> listChildOrgs(List<Long> orgIdList, OrgLevelTypeEnum orgLevelTypeEnum);

    /**
     * 获取组织的所属门店
     * @param orgIdList
     * @return
     */
    List<OrgLevelVO> listStoreInfosByOrgIds(List<Long> orgIdList);

    /**
     * 根据祖先节点返回组织汇总DTO, ancestorsOrgList层级由高到低排列
     * @param ancestorsOrgList
     * @return
     */
    OrgSummaryDTO getOrgSummaryDTO(List<OrgDTO> ancestorsOrgList);

    /**
     * 查询拥有所有权限的子树
     * @param userId
     * @param orgIdList
     * @return
     */
    List<OrgDTO> findIsFullScopeSonOrgList(Long userId, List<Long> orgIdList);

    /**
     * 把组织机构列表中门店找到上级（片区700或者连锁500）然后和其他组织机构去重
     * @param orgLevelVOList
     * @return
     */
    List<OrgLevelVO> listOrgLevelVOListAndResetStoreIntoParent(List<OrgLevelVO> orgLevelVOList);

    /**
     * 根据角色编码和组织Id批量获取用户信息
     *
     * @param roleCodes
     * @param orgIds
     * @return
     */
    List<EmployeeDetailDTO> getBatchUsersByRolesAndOrgIds(List<String> roleCodes, List<Long> orgIds);

    /**
     * 通过outId获取组织
     *
     * @param outIdList
     * @param type
     * @return
     */
    List<OrgDTO> listOrgByOutId(List<Long> outIdList, Integer type);

    /**
     * 获取用户角色
     *
     * @param userId
     * @return
     */
    List<UserRoleRelateDTO> getUserRoles(Long userId);

    /**
     * 根据上级组织机构ID和类型获取组织机构下所有该类型的组织机构列表
     *
     * @param orgIds
     * @param type
     * @return
     */
    List<ChildOrgsDTO> listChildOrgAssignedType(List<Long> orgIds, Integer type);

    /**
     * 获取连锁信息通过组织信息
     * @param orgLevelVOList
     * @return
     */
    List<OrgLevelVO> listBusinessInfoByOrg(List<OrgLevelVO> orgLevelVOList, Long userId);

    List<OrgDTO> listChildOrgByOrgTypeAndOrgPath(Integer orgType, String orgPath);

    OrgDTO getOrgInfoById(Long orgId);
    /**
     *
     * @Title: listStoreByOrgIds
     * @Description: 根据组织id 获取门店信息
     * @param: @param ids
     * @param: @return
     * @return: List<CrmStoreVO>
     * @throws
     */
    List<CrmStoreVO> listStoreByOrgIds(Long[] ids);

    /**
     * 获取用户层级的组织树
     * @return
     */
    List<OrgLevelVO> listUserDataScopeTreesByTypes(Long userId, List<Integer> types, Integer rootOrgType);

    /**
     * 获取用户拥有的公司
     * @param userId
     * @return
     */
    List<OrgLevelVO> listUserBusinessList(Long userId);

    List<TypeInfo<String, String>> getPriceTypeList(Long userId);

    List<TypeInfo<String, String>> getPriceChannelList(Long userId);

    /**
     * 根据门店组织Id列表获取连锁数据列表
     * @param storeOrgIdList
     * @return
     */
    List<OrgDTO> listBusinessOrgDTOByStoreOrgIds(List<Long> storeOrgIdList);

    /**
     * 根据门店组织Id列表获取连锁数据Map
     * @param storeOrgIdList
     * @return
     */
    Map<Long, OrgDTO> getBusinessOrgDTOMapByStoreOrgIds(List<Long> storeOrgIdList);

    /**
     * 根据门店组织Id列表获取平台数据列表
     * @param storeOrgIdList
     * @return
     */
    List<OrgDTO> listPlatformOrgDTOByStoreOrgIds(List<Long> storeOrgIdList);

    /**
     * 根据门店组织Id列表获取平台数据Map
     * @param storeOrgIdList
     * @return
     */
    Map<Long, OrgDTO> getPlatformOrgDTOMapByStoreOrgIds(List<Long> storeOrgIdList);

    /**
     * 获取组织机构列表中用户拥有的组织对象列表
     * @param userId
     * @param orgTypeEnum
     * @param orgIds
     * @return
     */
    List<OrgLevelVO> getUserOrgListByType(Long userId,  OrgTypeEnum orgTypeEnum, List<Long> orgIds);

    /**
     * 调价单类型列表
     * @return
     */
    List<TypeInfo<String,String>> getAdjustTypeList();

    /**
     * 获取用户是否有权限访问指定orgId下的所有子级组织，返回门店信息
     * @param userId
     * @param orgIdList
     * @return
     */
    List<OrgDTO> findScopeStoreList(Long userId, List<Long> orgIdList);

    /**
     * 获取用户权限的阉割版树
     * @param userId
     * @param resourceId
     * @param types
     * @return
     */
    List<OrgTreeDTO> getOrgTreeByUser(Long userId, Long resourceId, Set<Integer> types);

    /**
     * UserId 获取用户staffNum
     * @param userId
     * @return
     */
    StaffNumDTO getStaffNumByUserId(Long userId);

    /**
     * 校验用户的数据权限
     * @param userId
     * @param resourceId
     * @param orgIds
     * @return
     */
    Boolean checkDataScopes(long userId, long resourceId, String orgIds);

    /**
     * 连锁包含有权限的门店
     * @param userId
     * @param resourceId
     * @param businessIdList
     * @param idType
     * @return
     */
    List<OrgDTO> getUserDataScopeStoreBatch(Long userId,Long resourceId,List<Long> businessIdList,Integer idType);
}
