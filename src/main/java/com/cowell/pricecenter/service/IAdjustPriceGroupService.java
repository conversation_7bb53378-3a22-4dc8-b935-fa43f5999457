package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.AdjustPriceGroupParam;
import com.cowell.pricecenter.service.dto.request.PriceGroupParam;
import com.cowell.pricecenter.service.dto.request.PriceGroupSimpleParam;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;

/**
 * @Auther: 张晨
 * @Date: 2018/10/29 17:17
 * @Description:
 */
public interface IAdjustPriceGroupService{
    /**
     * 新增价格组
     * @param priceGroupParam
     * @return
     */
    CommonResponse addPriceGroup(AdjustPriceGroupParam priceGroupParam);

    /**
     * 查询
     * @param priceGroupParam
     * @return
     */
    CommonResponse queryPriceGroup(AdjustPriceGroupParam priceGroupParam);

    /**
     * 查询价格组明细
     * @param priceGroupParam
     * @return
     */
    CommonResponse queryPriceGroupDetail(AdjustPriceGroupParam priceGroupParam);


    /**
     * 批量审核
     * @param priceGroupParam
     * @return
     */
    CommonResponse auditPriceGroupBatch(AdjustPriceGroupParam priceGroupParam);



    /**
     * 编辑修改价格组
     * @param priceGroupParam
     * @return
     */
    CommonResponse editPriceGroup(AdjustPriceGroupParam priceGroupParam);

    /**
     * 删除价格组
     * @param priceGroupParam
     * @return
     */
    CommonResponse deletePriceGroup(AdjustPriceGroupParam priceGroupParam);




}
