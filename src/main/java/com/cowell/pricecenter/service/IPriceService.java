package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceChange;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.entity.PriceType;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.PriceChangeEventEnum;

import java.math.BigDecimal;
import java.util.List;

/**
 * 价格服务
 * <AUTHOR>
 * @date 2022/5/19 09:27
 */
public interface IPriceService {

    /**
     * 保存价格前的操作
     * @param detail 价格
     */
    void beforeSavePrice(PriceStoreDetail detail);

    /**
     * 保存价格
     * <p>判断是否需要价格预警并保存价格。如果价格保存成功，则会新增价格变动历史、同步CUBE、发送价格变动通知。</p>
     * @param priceStoreDetail 价格
     * @return int
     */
    int savePrice(PriceStoreDetail priceStoreDetail);

    /**
     * 保存价格后的操作
     * @param detail 价格
     */
    void afterSavePrice(PriceStoreDetail detail);


    /**
     * 保存价格
     * @param priceStoreDetail 价格
     * @param priceType 价格类型
     * @return int
     */
    int savePrice(PriceStoreDetail priceStoreDetail, PTypeEnum priceType);

    /**
     * 保存价格
     * @param storeId 门店ID
     * @param goodsNo 商品编码
     * @param skuId skuID
     * @param priceTypeCode 价格类型
     * @param price 价格
     * @param channelId 渠道
     * @return int
     */
    int savePrice(Long storeId, String goodsNo, Long skuId, String priceTypeCode, BigDecimal price, Integer channelId);


    /**
     * 保存价格历史
     * @param detail PriceStoreDetail
     */
    void insertPriceDetailHistorySync(PriceStoreDetail detail);

    /**
     * 构建价格变动对象
     * @param detail PriceStoreDetail
     * @param priceChangeEventEnum priceChangeEventEnum
     * @return PriceChange
     */
    PriceChange buildPriceChange(PriceStoreDetail detail, PriceChangeEventEnum priceChangeEventEnum);

    /**
     * fas价格变动事件
     * @param detail PriceStoreDetail
     * @param priceChangeEventEnum priceChangeEventEnum
     */
    void sendPriceChange(PriceStoreDetail detail, PriceChangeEventEnum priceChangeEventEnum);

    /**
     * 判断是否需要价格预警
     * @param oldDetail 原价格
     * @param detail 价格
     * @return 是否预警
     */
    boolean isNeedPriceWarn(PriceStoreDetail oldDetail, PriceStoreDetail detail);

    /**
     * 构建PriceStoreDetail
     * @param storeId 门店ID
     * @param goodsNo 商品编码
     * @param skuId skuID
     * @param priceTypeCode 价格类型
     * @param price 价格
     * @param channelId 渠道
     * @return PriceStoreDetail
     */
    PriceStoreDetail buildPriceStoreDetail(Long storeId, String goodsNo, Long skuId, String priceTypeCode, BigDecimal price, Integer channelId);

    /**
     * 修改价格特价标签
     * @param storeId 门店ID
     * @param goodsNo 商品编码
     * @param priceTypeCodes 价格类型集合
     * @param channelId 渠道
     * @param isSpecial 是否特价 0-否 1-是
     * @return int
     */
    int updatePriceSign(Long storeId, String goodsNo, List<String> priceTypeCodes, Integer channelId, Integer isSpecial);

    /**
     * 修改价格特价标签
     * @param storeId 门店ID
     * @param goodsNo 商品编码
     * @param priceTypeCodes 价格类型集合
     * @param channelId 渠道
     * @param isSpecial 是否特价
     * @return int
     */
    int updatePriceSign(Long storeId, String goodsNo, List<String> priceTypeCodes, Integer channelId, boolean isSpecial);

    /**
     * 是否是特价
     * @param priceSign 价格标签
     * @return boolean
     */
    boolean isSpecial(Long priceSign);

}
