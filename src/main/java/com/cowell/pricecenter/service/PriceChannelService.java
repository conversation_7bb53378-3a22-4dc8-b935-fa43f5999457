package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceChannel;
import com.cowell.pricecenter.entity.PriceChannelExample;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date  2022/3/18 10:57
 */

public interface PriceChannelService{


    long countByExample(PriceChannelExample example);

    int deleteByExample(PriceChannelExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(PriceChannel record);

    int insertSelective(PriceChannel record);

    List<PriceChannel> selectByExample(PriceChannelExample example);

    PriceChannel selectByPrimaryKey(Integer id);

    int updateByExampleSelective(PriceChannel record,PriceChannelExample example);

    int updateByExample(PriceChannel record,PriceChannelExample example);

    int updateByPrimaryKeySelective(PriceChannel record);

    int updateByPrimaryKey(PriceChannel record);

    /**
     * 根据ChannelId获渠道信息
     * @param channelId
     * @return
     */
    Optional<PriceChannel> getPriceChannelByChannelId(Integer channelId);

    /**
     * 根据channelId列表获取渠道列表
     * @param channelIds
     * @return
     */
    List<PriceChannel> getPriceChannelListByChannelIds(List<Integer> channelIds);

    /**
     * 获取所有渠道列表
     * @return
     */
    List<PriceChannel> getAllPriceChannelList();

    /**
     * 获取所有渠道Map
     * @return
     */
    Map<Integer, PriceChannel> getAllChannelMap();

    /**
     * 获取指定渠道Map
     * @return
     */
    Map<Integer, PriceChannel> getChannelMap(List<Integer> channelIdList);


    /**
     * 获取用户渠道Map
     *
     * @return
     */
     Map<Integer, PriceChannel> getUserChannelMap(Long userId);
}
