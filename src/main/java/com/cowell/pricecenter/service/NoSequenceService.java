package com.cowell.pricecenter.service;

import com.cowell.pricecenter.enums.NoSequenceTypeEnum;

public interface NoSequenceService {

    /**
     * 生成调价单单号/价格管控单号
     * @param priceSource 价格类型（例如会员价，零售价）
     * @param noSequenceType 单号序列类型（例如调价单、管控单）
     * @return
     */
    String priceNoSequence(String priceSource, NoSequenceTypeEnum noSequenceType);

    /**
     * 生成调价单单号/价格管控单号
     * @param noSequenceType 单号序列类型（例如调价单、管控单）
     * @return
     */
    String priceNoSequence(NoSequenceTypeEnum noSequenceType);

}
