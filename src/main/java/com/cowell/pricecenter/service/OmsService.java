package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.feign.OmsFeignService;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description:
 * @date 2020/03/05 16:41
 */
@Slf4j
@Service
public class OmsService {

    @Autowired
    private OmsFeignService omsFeignService;

    public List<Long> getBusinessIds() {
        ResponseEntity<Set<String>> responseEntity = null;
        try {
            responseEntity = omsFeignService.getBusinessIds();
        } catch (Exception e) {
            log.error("<==OmsService||getBusinessIds||调用oms服务异常",e);
        }
        List<Long> businessIds = null;
        if(null != responseEntity && HttpStatus.OK.equals(responseEntity.getStatusCode())){
            Set<String> businessIdSet = responseEntity.getBody();
            businessIds = Optional.ofNullable(businessIdSet).orElse(Sets.newHashSet()).stream().map(item -> Long.valueOf(item)).collect(Collectors.toList());
        }
        log.info("<==OmsService||getBusinessIds||获取oms连锁id list,返回:{}",businessIds);
        return businessIds;
    }
}
