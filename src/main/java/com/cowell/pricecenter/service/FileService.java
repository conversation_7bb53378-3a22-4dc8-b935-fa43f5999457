package com.cowell.pricecenter.service;

import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.service.dto.FileDTO;
import com.cowell.pricecenter.service.dto.request.FileDownloadTaskDTO;
import com.cowell.pricecenter.service.feign.IScrmFeignService;
import com.cowell.pricecenter.service.feign.StoreService;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description:
 * @date 2020/03/05 22:32
 */
@Slf4j
@Service
public class FileService {

    @Autowired
    private IScrmFeignService scrmFeignService;

    @Autowired
    private StoreService storeService;


    public FileDownloadTaskDTO createDownloadCenterTaskStart(FileDTO fileDTO) {
        FileDownloadTaskDTO fileDownloadTaskDTO = new FileDownloadTaskDTO();
        fileDownloadTaskDTO.setName(fileDTO.getTaskName());
        fileDownloadTaskDTO.setPlatformType(1);
        fileDownloadTaskDTO.setStatus(1);
        fileDownloadTaskDTO.setRequestType(1);
        fileDownloadTaskDTO.setRequestUrl(fileDTO.getUrl());
        fileDownloadTaskDTO.setRequestParam(fileDTO.getRequestParam());
        fileDownloadTaskDTO.setCreatedBy(fileDTO.getLoginUserId());
        fileDownloadTaskDTO.setStartTime(DateUtils.dateToString(fileDTO.getStartTime()));
        fileDownloadTaskDTO.setEndTime(DateUtils.dateToString(fileDTO.getEndTime()));
        FileDownloadTaskDTO fileDownloadTask = new FileDownloadTaskDTO();
        try {
            fileDownloadTask = scrmFeignService.createFileDownloadTask(fileDownloadTaskDTO);
        } catch (Exception e) {
            log.error("<==FileService||创建文件上传任务失败,{}", JSONObject.toJSONString(fileDTO),e);
        }
        return fileDownloadTask;
    }

    public String createDownloadCenterTaskEnd(FileDownloadTaskDTO saveDto, String taskName, byte[] fileByte,String fileUrl) {
        try {
            String fileName = taskName + ".xls";
            ResponseEntity<Map<String, Object>> mapResponseEntity = storeService.uploadfileByByte(fileUrl + fileName, fileByte);
            log.info(taskName + " 下载上传 结果：{}", mapResponseEntity);
            Map<String, Object> body = mapResponseEntity.getBody();
            FileDownloadTaskDTO fileDownloadTaskDTO2 = new FileDownloadTaskDTO();
            fileDownloadTaskDTO2.setId(saveDto.getId());
            fileDownloadTaskDTO2.setFileName(fileName);
            fileDownloadTaskDTO2.setFilePlace(1);
            fileDownloadTaskDTO2.setStatus(2);
            fileDownloadTaskDTO2.setReason("成功");
            fileDownloadTaskDTO2.setFileUrl(String.valueOf(body.get("fileUrl")));
            if (!body.get("success").equals(true)) {
                fileDownloadTaskDTO2.setStatus(3);
                fileDownloadTaskDTO2.setReason("上传文件失败");
                log.warn(taskName + " 上传文件失败！ body:{}", body);
            }
            scrmFeignService.updateFileDownloadTask(fileDownloadTaskDTO2);
            return body.get("fileUrl")+"";
        } catch (Exception e) {
            log.error(taskName + " 文件生成失败", e);
        }
        return "";
    }
}
