package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.PricePrintParam;
import com.cowell.pricecenter.service.dto.request.PriceStoreDetailParam;

import java.util.Map;

/**
 * 价签打印服务接口
 * @Author: Susu
 * @Description:
 * @Date: 2021/6/7 13:24
 * com.cowell.pricecenter.service
 */
public interface IPriceLabelPrintService {
    /**
     * 百分号
     */
    public static final String PERCENT_SIGN = "%";
    /**
     * 分页标记
     */
    public static final String PAGE_FLAG = "pageFlag";
    /**
     * 总计
     */
    public static final String TOTAL_SIZE = "totalSize";
    /**
     * 价签集合key
     */
    public static final String PRICE_STORE_LIST = "priceStoreList";

    /**
     * 通过价签打印查询参数分页获取商品集合
     * @param param
     * @return
     */
    Map<String, Object> queryPriceStoreDetailListByEs(PricePrintParam param);

    /**
     * 根据价签单号获取商品集合
     * @param param
     * @return
     */
    Map<String, Object> queryPriceStoreDetailListByAdjustCode(PricePrintParam param);
}
