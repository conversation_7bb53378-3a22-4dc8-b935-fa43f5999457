package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.OrderViewParam;
import com.cowell.pricecenter.service.dto.request.PriceAdjustDetailQueryParam;
import com.cowell.pricecenter.service.dto.request.PriceAdjustNoticeQueryParam;
import com.cowell.pricecenter.service.dto.response.NoticeReminder;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.priceAdjustNotice.PriceAdjustDetailDTO;
import com.cowell.pricecenter.service.dto.response.priceAdjustNotice.PriceAdjustNoticeDTO;

/**
 * 价格调整通知接口
 *
 * Created by schuangxigang on 2022/3/28 19:28.
 */
public interface PriceAdjustNoticeService {
    PageResult<PriceAdjustNoticeDTO> pageNoticeList(PriceAdjustNoticeQueryParam param);

    PageResult<PriceAdjustDetailDTO> pageDetailList(PriceAdjustDetailQueryParam param);

    PageResult<PriceAdjustDetailDTO> pageDetailList(OrderViewParam orderViewParam);

    NoticeReminder getNoticeReminder();
}
