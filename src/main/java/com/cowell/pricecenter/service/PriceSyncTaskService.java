package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceSyncTask;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskGoodsVO;
import com.cowell.pricecenter.mq.vo.PriceSyncTaskVO;

import java.math.BigDecimal;
import java.util.List;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-03-03 19:22
 */
public interface PriceSyncTaskService {


    /**
     * 保存配置信息
     * @param dto
     */
    void savePriceSyncTask(PriceSyncTask dto);

    /**
     * 任务拆解
     * @param vo
     */
    void priceSyncTaskDismantling(PriceSyncTaskVO vo);

    /**
     * 价格同步补充信息
     * @param vo
     */
    void priceSyncSupplementInfo(PriceSyncTaskVO vo);

    /**
     * 修改价格
     * @param vo
     */
    void updateNewPrice(PriceSyncTaskVO vo);


    /**
     * 同步指定连锁 门店 价格到 第三方平台
     * @param businessId
     * @param storeId
     * @param goodsVOS
     * @param pTypeEnumCode
     */
    void syncPriceToPlatform(Long businessId, Long storeId, List<PriceSyncTaskGoodsVO> goodsVOS,String pTypeEnumCode);

    /**
     * 同步指定连锁 门店 价格到 第三方平台
     * @param businessId
     * @param storeId
     * @param goodsVOS
     * @param pTypeEnumCode
     */
    void syncPriceToPlatformByManual(Long businessId, Long storeId, List<PriceSyncTaskGoodsVO> goodsVOS,String pTypeEnumCode);


    /**
     * 通过连锁、门店、商品编码  根据价格同步配置 获取指定 渠道价格
     * @param businessId
     * @param storeId
     * @param pTypeEnumPlatform
     * @param goodsNoList
     * @return
     */
    List<PriceSyncTaskGoodsVO> getSyncPriceByConfigToDesignatedPlatform(Long businessId, Long storeId, String pTypeEnumPlatform,List<String> goodsNoList);

    /**
     * 同步价格到商品中心
     * @param price
     * @param goodsNo
     * @param businessId
     * @param storeId
     */
    void syncGJYJSPriceToItemCenter(BigDecimal price,String goodsNo,Long businessId,Long storeId);



    void updateOrInsertNewPriceType(String priceTypeCode,BigDecimal price,String goodsNo,Long businessId,Long storeId);
}
