package com.cowell.pricecenter.service;

import com.cowell.permission.dto.OrgDTO;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.service.dto.MdmStoreBaseDTO;
import com.cowell.pricecenter.service.dto.OrgToRedisDTO;
import com.cowell.pricecenter.service.dto.request.*;
import com.cowell.pricecenter.service.dto.response.StoreGoodsnoPriceDTO;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.dto.response.priceQuery.GoodsDTO;
import com.cowell.pricecenter.service.dto.response.priceQuery.PriceHistoryDTO;
import com.cowell.pricecenter.service.dto.response.priceQuery.PriceHistoryLineChart;

import java.util.List;
import java.util.Map;

/**
 * 价格查询接口
 *
 * Created by schuangxigang on 2022/4/11 15:55.
 */
public interface PriceQueryService {
    PageResult<Map<String, Object>> pageGoodsList(GoodsQueryParam param, TokenUserDTO userDTO);

    GoodsDTO getGoodsPrice(TokenUserDTO userDTO, UniqueGoodsPriceQuery query);

    PageResult<PriceHistoryDTO> getHistoryList(PriceHistoryQueryParam param);

    PriceHistoryLineChart getHistoryLineChart(PriceHistoryQueryParam param);

    Map<String, AdjustPriceOrder> getAdjustOrderMap(List<String> adjustCodes);

    Map<Long, List<OrgDTO>> getAncestorsOrgList(List<Long> storeIds);

    Map<Long, List<OrgToRedisDTO>> getCachedAncestorsOrgList(String redisKey, List<Long> storeIds);

    void clearCache(String redisKey);

    List<Long> getUserDataStoreIdList(TokenUserDTO tokenUserDTO, Long orgId, boolean storeCenter, boolean checkRole, List<String> roleCodes);

    /**
     * 分页初始化商品价格数据到redis中
     * @param param
     * @param tokenUserDTO
     */
    void initPageGoodsList(GoodsQueryParam param, TokenUserDTO tokenUserDTO);

    /**
     * 从redis缓存中取数据
     * @param searchCode NOT NULL
     * @param page >= 1
     * @return
     */
    PageResult<Map<String, Object>> pageGoodsListFromCache(String searchCode, Integer page);

    void cacheGoodsStoreIds(List<String> goodsNoList, TokenUserDTO userDTO, List<Long> storeIdList);
    
    Map<Long,MdmStoreBaseDTO> getStoreInfo(List<Long> storeIdList);
    
    List<Long> convertStoreId(GoodsQueryParam goodsQueryParam,TokenUserDTO tokenUserDTO);
}
