package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.service.query.PriceStoreDetailQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/23 17:42
 */
public interface PriceStoreDetailReadService {

    /**
     * 通过几个通用的查询条件进行查询门店价格表
     * @param storeId 门店id
     * @param channelIdList 渠道id列表
     * @param goodsNoList 商品编码列表
     * @param priceTypeCodeList 商品类型编码
     * @return List<PriceStoreDetail>
     */
    List<PriceStoreDetail> getPriceStoreDetailByParam(Long storeId, List<Integer> channelIdList, List<String> goodsNoList,
                                                      List<String> priceTypeCodeList);

    /**
     * 列表查询
     * @param query 查询参数
     * @return List<PriceStoreDetail>
     */
    List<PriceStoreDetail> query(PriceStoreDetailQuery query);

    /**
     * 价格查询
     * @param storeId 门店ID
     * @param channelId 渠道
     * @param priceTypeCode 价格类型
     * @param goodsNo 商品编码
     * @return PriceStoreDetail
     */
    PriceStoreDetail query(Long storeId, Integer channelId, String priceTypeCode, String goodsNo);

    /**
     * 价格查询
     * @param storeId 门店ID
     * @param channelId 渠道
     * @param priceTypeCode 价格类型
     * @param goodsNo 商品编码
     * @param skuId skuId
     * @return PriceStoreDetail
     */
    PriceStoreDetail query(Long storeId, Integer channelId, String priceTypeCode, String goodsNo, Long skuId, Long channelStoreId,String priceGroup);

    /**
     * 价格查询
     * @param storeId 门店ID
     * @param priceTypeCode 价格类型
     * @param goodsNo 商品编码
     * @return List<PriceStoreDetail>
     */
    List<PriceStoreDetail> query(Long storeId, String priceTypeCode, String goodsNo);


    /**
     * 数量查询
     * @param query 查询参数
     * @return 数量
     */
    long count(PriceStoreDetailQuery query);

    /**
     * 进行OR列表查询
     * @param query 查询参数
     * @param or 查询参数
     * @return List<PriceStoreDetail>
     */
    List<PriceStoreDetail> queryWithOr(PriceStoreDetailQuery query, PriceStoreDetailQuery... or);

    /**
     * 进行OR数量查询
     * @param query 查询参数
     * @param or 查询参数
     * @return 数量
     */
    long countWithOr(PriceStoreDetailQuery query, PriceStoreDetailQuery... or);

    /**
     * 查询单商品多门店
     * @param goodsNoList NOT NULL
     * @param storeIds NOT NULL
     * @param priceTypeCodeList
     * @param channelIdList
     * @return
     */
    List<PriceStoreDetail> queryByGoodsnoesAndStoreIds(List<String> goodsNoList, List<Long> storeIds,
        List<String> priceTypeCodeList, List<Integer> channelIdList);

    /**
     * 根据门店查询门店拥有的去重后的商品编码
     * @param storeId
     * @return
     */
    List<String> listGoodsNoListByStoreId(Long storeId);

    /**
     * 查询单门店多商品
     * @param storeId NOT NULL
     * @param goodsnoList NOT NULL
     * @param channelIdList
     * @return priceTypeCodeList
     * @return
     */
    List<PriceStoreDetail> queryByStoreIdAndGoodsnoList(Long storeId, List<String> goodsnoList,
        List<Integer> channelIdList, List<String> priceTypeCodeList);

}
