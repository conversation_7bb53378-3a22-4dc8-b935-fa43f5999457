package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.response.PriceSyncConfigDTO;

/**
 * 类说明
 *
 * @Author: liw
 * @Date: 2020-03-03 19:22
 */
public interface PriceSyncConfigService {


    /**
     * 保存配置信息
     * @param dto
     */
    void savePriceConfig(PriceSyncConfigDTO dto);

    /**
     * 获取 价格配置 通过连锁和类型
     * @param businessId
     * @param configType
     * @return
     */
    PriceSyncConfigDTO getPriceConfig(Long businessId, String configType);

    /**
     * 获取 价格配置 通过连锁
     * @param businessId
     * @return
     */
    PriceSyncConfigDTO getPriceConfigByBusinessId(Long businessId);

    /**
     * 根据连锁获取渠道类型
     * @param businessId
     * @return
     */
    String channelTypeStr(Long businessId);

    /**
     * 获取连锁同步类型
     * @param businessId 连锁ID
     * @return 同步类型
     */
    Integer getBusinessSyncType(Long businessId);
}
