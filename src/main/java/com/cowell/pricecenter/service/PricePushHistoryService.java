package com.cowell.pricecenter.service;

import com.cowell.pricecenter.entity.PricePushHistoryExample;
import com.cowell.pricecenter.enums.PriceUpdateResultEnum;
import com.cowell.pricecenter.enums.PushResultEnum;
import com.cowell.pricecenter.param.PricePushHistoryParam;
import com.cowell.pricecenter.service.dto.PricePushHistoryDTO;
import com.cowell.pricecenter.service.dto.PricePushHistoryStatisticsDTO;
import com.cowell.pricecenter.service.dto.request.PageResponse;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;

import java.util.List;
import java.util.Map;

import com.cowell.pricecenter.entity.PricePushHistory;
import com.cowell.pricecenter.web.rest.vo.PricePushResultVO;
import com.github.pagehelper.PageInfo;

/**
 * <AUTHOR>
 * @date 2022/4/7 17:51
 */

public interface PricePushHistoryService {


    long countByExample(PricePushHistoryExample example);

    int deleteByExample(PricePushHistoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(PricePushHistory record);

    int insertOrUpdate(PricePushHistory record);

    int insertOrUpdateSelective(PricePushHistory record);

    int insertSelective(PricePushHistory record);

    List<PricePushHistory> selectByExample(PricePushHistoryExample example);

    PricePushHistory selectByPrimaryKey(Long id);

    int updateByExampleSelective(PricePushHistory record, PricePushHistoryExample example);

    int updateByExample(PricePushHistory record, PricePushHistoryExample example);

    int updateByPrimaryKeySelective(PricePushHistory record);

    int updateByPrimaryKey(PricePushHistory record);

    int updateBatch(List<PricePushHistory> list);

    int updateBatchSelective(List<PricePushHistory> list);

    int batchInsert(List<PricePushHistory> list);

    /**
     * 更新线下生效失败的商品价格明细
     * @param pushCode 推送号
     * @param batch 推送批次
     * @param historyUpdate 要更新的数据
     * @param priceList 明细
     */
    void updateFailedDetail(String pushCode, Integer batch, PricePushHistory historyUpdate, List<PricePushResultVO.Detail> priceList);

    /**
     * 更新线下生效失败的商品价格明细
     * @param pushCode 推送号
     * @param batch 推送批次
     * @param historyUpdate 要更新的数据
     * @return int
     */
    int updateDetailByPushCodeAndBatch(String pushCode, Integer batch, PricePushHistory historyUpdate);

    /**
     *
     * @Title: selectPricePushHistoryStatistics
     * @Description: 执价结果统计
     * @param: @param param
     * @param: @return
     * @return: PageResult<PricePushHistoryStatisticsDTO>
     * @throws
     */
    PricePushHistoryStatisticsDTO selectPricePushHistoryStatistics(PricePushHistoryParam param);
    /**
     *
     * @Title: selectPricePushHistoryPage
     * @Description: 执价结果列表
     * @param: @param param
     * @param: @return
     * @return: PageResult<PricePushHistoryStatisticsDTO>
     * @throws
     */
    PageResult<PricePushHistoryDTO> selectPricePushHistoryPage(PricePushHistoryParam param);

    /**
     * 更新明细的发送状态
     * @param pushCode pushCode
     * @param batch 推送批次
     * @param resultEnum 状态枚举
     * @return int
     */
    int updateSendStatus(String pushCode, Integer batch, PushResultEnum resultEnum);
    
    /**
     * 重新执价
     * @param adjustCode
     */
    void afreshEffectAdjustPrice(String adjustCode);

}


