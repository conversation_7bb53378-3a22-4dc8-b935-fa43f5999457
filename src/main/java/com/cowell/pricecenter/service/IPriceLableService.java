package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.PriceLabelParam;
import com.cowell.pricecenter.service.dto.response.ImportPriceLabelResponse;
import com.cowell.pricecenter.service.dto.response.SaleCategoryDTO;
import com.cowell.pricecenter.web.rest.vo.CommonResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Auther: 张晨
 * @Date: 2018/10/29 17:17
 * @Description:
 */
public interface IPriceLableService {
    /**
     * 价格导入
     *
     * @param file
     * @param orgId
     * @return
     */
    String priceImport(MultipartFile file, long businessId, long orgId);

    ImportPriceLabelResponse getPriceLabelImportStatus(String key);

    /**
     * 新增
     *
     * @param priceLabelParam
     * @return
     */
    CommonResponse saveLabelTemplate(PriceLabelParam priceLabelParam);

    /**
     * 修改
     *
     * @param priceLabelParam
     * @return
     */
    CommonResponse editLabelTemplate(PriceLabelParam priceLabelParam);

    /**
     * 查询
     *
     * @param priceLabelParam
     * @return
     */
    CommonResponse queryLabelTemplateList(PriceLabelParam priceLabelParam);

    CommonResponse queryLabelTemplatePageList(PriceLabelParam priceLabelParam);

    CommonResponse delLabelTemplate(PriceLabelParam priceLabelParam);


    /**
     * 查询
     *
     * @param priceLabelParam
     * @return
     */
    CommonResponse queryLabelTemplateDetail(PriceLabelParam priceLabelParam);

    CommonResponse queryPriceLabeElement(long businessId, long parentId);

    List<SaleCategoryDTO> categoryList(long businessId, long parentId);
}
