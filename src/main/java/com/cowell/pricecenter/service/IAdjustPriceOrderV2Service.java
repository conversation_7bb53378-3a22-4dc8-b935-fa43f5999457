package com.cowell.pricecenter.service;

import com.cowell.permission.dto.OrgTreeDTO;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.AdjustPriceOrderExample;
import com.cowell.pricecenter.entity.AdjustPriceOrderOrgStoreDetail;
import com.cowell.pricecenter.enums.AuditStatusEnum;
import com.cowell.pricecenter.service.dto.DownloadParamDTO;
import com.cowell.pricecenter.service.dto.ImportInitStorePriceTaskStatusDTO;
import com.cowell.pricecenter.service.dto.InitNewStorePriceDTO;
import com.cowell.pricecenter.service.dto.InitNewStorePriceHeadDTO;
import com.cowell.pricecenter.service.dto.ItemSkuQueryApiDTO;
import com.cowell.pricecenter.service.dto.request.AdjustPriceOrderListV2Param;
import com.cowell.pricecenter.service.dto.request.AdjustPriceOrderV2Param;
import com.cowell.pricecenter.service.dto.request.NewSpuQueryParam;
import com.cowell.pricecenter.service.dto.request.SaveAdjustPriceOrderDTO;
import com.cowell.pricecenter.service.dto.request.TokenUserDTO;
import com.cowell.pricecenter.service.dto.response.AdjustImportResultDTO;
import com.cowell.pricecenter.service.dto.response.AdjustPriceOrderOrgDetailVO;
import com.cowell.pricecenter.service.dto.response.AdjustPriceOrderV2VO;
import com.cowell.pricecenter.service.dto.response.CommonNewSpuVo;
import com.cowell.pricecenter.service.dto.response.ItemSkuVo;
import com.cowell.pricecenter.service.dto.response.amis.OptionDto;
import com.cowell.pricecenter.service.dto.response.amis.PageResult;
import com.cowell.pricecenter.service.vo.B2cOrgVO;
import com.cowell.pricecenter.service.vo.OrgLevelVO;
import com.cowell.pricecenter.service.vo.OrgParam;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @program: pricecenter
 * @description: 调价单管理2.0版本的服务接口
 * @author: jmlu
 * @create: 2022-03-15 21:32
 **/

public interface IAdjustPriceOrderV2Service {

    /**
     * 查询调价单列表带分页
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<AdjustPriceOrderV2VO> searchAdjustPriceOrderList(AdjustPriceOrderListV2Param param, TokenUserDTO userDTO);

    /**
     * 管控通知页面的调价单列表列表
     * @param param
     * @param userDTO
     * @return
     */
    PageResult<AdjustPriceOrderV2VO> searchControlAdjustPriceOrderList(AdjustPriceOrderListV2Param param, TokenUserDTO userDTO);

    /**
     * 新增调价单
     * @param param
     * @param userDTO
     * @return
     */
    long addAdjustPriceOrder(AdjustPriceOrderV2Param param, TokenUserDTO userDTO);

    /**
     * 编辑调价单
     * @param param
     * @param userDTO
     * @return
     */
    void editAdjustPriceOrder(AdjustPriceOrderV2Param param, TokenUserDTO userDTO);

    /**
     * 根据ID删除调价单
     * @param adjustPriceOrderId
     * @param userDTO
     */
    void deleteAdjustPriceOrderById(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 根据调价单单位获取调价单信息
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    AdjustPriceOrderV2VO getAdjustPriceOrderById(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 根据调价单单位获取调价单信息
     * @param adjustCode
     * @param userDTO
     * @return
     */
    AdjustPriceOrderV2VO getAdjustPriceOrderByCode(String adjustCode, TokenUserDTO userDTO);

    /**
     * 保存调价单（调价单的状态由制单中变成待提交）
     * @param adjustPriceOrderId
     * @param userDTO
     */
    void saveAdjustPriceOrder(Long adjustPriceOrderId,Boolean isPortal, Boolean cancel, TokenUserDTO userDTO);

    /**
     * 根据调价单ID设置调价单状态为制单中
     * @param adjustPriceOrderId
     * @param userDTO
     */
    void setAdjustPriceOrderInPreparationById(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 根据调价单ID去提交审核，只有待提交的状态才能提交审核
     * @param adjustPriceOrderId
     * @param userDTO
     */
    void submitAdjustPriceOrderToAuditById(Long adjustPriceOrderId,Boolean isPortal, Integer effectMode,String scheduledTimeSecondLong, TokenUserDTO userDTO);

    /**
     * 查询调价单的组织机构相关信息（包含所选组织类型和组织列表）
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    AdjustPriceOrderOrgDetailVO listAdjustPriceOrderOrgDetailList(Long adjustPriceOrderId, TokenUserDTO userDTO);


    /**
     * OA审核成功或者审核失败调用
     * @param adjustCode
     * @param auditStatusEnum
     */
    void auditSuccessOrFailure(String adjustCode, AuditStatusEnum auditStatusEnum);

    /**
     * 通过调价单Id获取调价单关联连锁企业信息选择商品
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    List<OptionDto> listBusinessInfosByOrderId(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 通过调价单Id获取调价单关联的门店用于选择商品
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    List<OptionDto> listStoreInfosByByOrderId(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 调价单是否可以提交审核
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    boolean isAdjustPriceOrderToSubmit(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 调价单是否可以保存
     * @param adjustPriceOrderId
     * @param userDTO
     * @return
     */
    boolean isAdjustPriceOrderToSave(Long adjustPriceOrderId, TokenUserDTO userDTO);

    /**
     * 根据调价单ID去立即值价
     * @param adjustPriceOrderId
     * @param userDTO
     */
    void immediatelyExecutePriceById(Long adjustPriceOrderId, TokenUserDTO userDTO);
    /**
     * 根据用户选择的组织机构及其权限判断用户实际所选组织
     * ex：假如用户拥有价格中台的西南平台权限，则直接返回西南平台组织ID；假如没有西南平台权限，且选择西南平台，则把西南平台转化为西南平台下所拥有的子节点ID列表
     * @param userId
     * @param resourceId
     * @param orgIdList
     * @return
     */
    List<OrgLevelVO> getUserActualSelectOrgDTOList(long userId, long resourceId, List<Long> orgIdList);
    /**
     *
     * @Title: getAdjustTypeList
     * @Description: 获取调价单类型
     * @param: @param adjustPriceOrderId
     * @param: @return
     * @return: List<OptionDto>
     * @throws
     */
    List<OptionDto> getAdjustTypeList(Long adjustPriceOrderId);
    /**
     *
     * @Title: getAdjustStoreDetailsFromOrgs
     * @Description: 对组织列表按照组织层级进行分组并取出所有门店
     * @param: @param adjustCode
     * @param: @param orgLevelVOList
     * @param: @param operateTime
     * @param: @return
     * @return: List<AdjustPriceOrderOrgStoreDetail>
     * @throws
     */
    List<AdjustPriceOrderOrgStoreDetail> getAdjustStoreDetailsFromOrgs(String adjustCode, List<OrgLevelVO> orgLevelVOList, Date operateTime);
    /**
     *
     * @Title: saveOrgStoreDetail
     * @Description: 保存预执价数据
     * @param: @param adjustCode
     * @return: void
     * @throws
     */
    void saveOrgStoreDetail(String adjustCode);

    /**
     *
     * @Title: selectByAdjustCode
     * @Description: 根据调价单号查询
     * @param: @param adjustCode
     * @param: @return
     * @return: AdjustPriceOrder
     * @throws
     */
    AdjustPriceOrder selectByAdjustCode(String adjustCode);

    /**
     *
     * @Title: updateByExampleSelective
     * @Description: 修改AdjustPriceOrder
     * @param: @param record
     * @param: @param example
     * @param: @return
     * @return: int
     * @throws
     */
    int updateByExampleSelective(AdjustPriceOrder record, AdjustPriceOrderExample example);

    /**
     * 查询生效时间在startTime和endTime的所有调价单
     * @param startTime
     * @param endTime
     * @param page
     * @param pageSize
     * @return
     */
    List<AdjustPriceOrder> listEffectAdjustOrdersByEffectTime(Integer adjustType,LocalDateTime startTime, LocalDateTime endTime, Integer page, Integer pageSize);

    /**
     * 重新执价
     * @param adjustCode
     */
    void afreshEffectAdjustPrice(String adjustCode);

    /**
     * 调价单主表机构
     * @param adjustCode
     * @param orgLevelVOList
     * @param userDTO
     * @param operateTime
     */
    void insertAdjustOrgDetailList(String adjustCode, List<OrgLevelVO> orgLevelVOList, TokenUserDTO userDTO, LocalDateTime operateTime);

    /**
     * 分批次缓存初始化新店价格到redis
     * @param cacheInitList
     */
    void batchCacheInitNewStorePrice(InitNewStorePriceHeadDTO initPriceList);

    /**
     * 初始化新店价格
     * @param downloadCodePageList
     */
    void initNewStorePriceForCache(List<DownloadParamDTO> downloadCodePageList);

    /**
     * 初始化价格 生成调价单
     * @param param
     */
    void initNewStorePriceToDb(SaveAdjustPriceOrderDTO param,TokenUserDTO tokenUserDTO);

    /**
     * 根据调价单号查询 调价单价格低于集团标牌价商品
     * @param adjustCode
     * @return
     */
    AdjustImportResultDTO getAdjustGoodsWarnList(Long adjustId);

    /**
     * 查询机构
     * @param param
     * @return
     */
    List<OrgTreeDTO> getAbsScopeOrgTrees(OrgParam param,TokenUserDTO userDTO);

    /**
     * 查询商品信息
     * @param dto
     * @return
     */
    PageResult<CommonNewSpuVo> getItemSkuList(NewSpuQueryParam dto);

    /**
     * 更新导入状态
     * @param param
     */
    void updateImportResult(AdjustImportResultDTO param);

    /**
     * 保存导入异常数据
     * @param param
     */
    void saveImportErrorData(List<InitNewStorePriceDTO> param);

    /**
     * 初始化导入任务状态
     * @param status 导入任务ID
     */
    void initializeImportTask(ImportInitStorePriceTaskStatusDTO status);

    Long getAdjustIdForHdFromCache(String sapCode, TokenUserDTO userDTO);

}
