package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.request.PriceLimitControlGoodsExcel;
import com.cowell.pricecenter.service.dto.request.PriceLimitControlParam;
import com.cowell.pricecenter.service.dto.request.PriceLimitControlQueryParam;
import com.cowell.pricecenter.service.dto.response.CommonRes;
import com.cowell.pricecenter.service.dto.response.amis.CommonResult;

import java.util.List;

public interface IPriceLimitControlService {

    /**
     * 查询医保价格限控规则列表
     * @param param 查询参数
     * @return 分页结果
     */
    CommonResult queryPriceLimitControlList(PriceLimitControlQueryParam param);

    /**
     * 保存或更新医保价格限控规则
     * @param param 规则参数
     * @return 操作结果
     */
    CommonResult saveOrUpdatePriceLimitControl(PriceLimitControlParam param);

    /**
     * 查询医保价格限控规则详情
     * @param param 查询参数
     * @return 规则详情
     */
    CommonResult queryPriceLimitControlDetail(PriceLimitControlQueryParam param);

    /**
     * 删除医保价格限控规则 (软删除)
     * @param param 删除参数
     * @return 操作结果
     */
    CommonResult deletePriceLimitControl(PriceLimitControlQueryParam param);

    /**
     * 启用/禁用医保价格限控规则
     * @param param 状态参数
     * @return 操作结果
     */
    CommonResult updatePriceLimitControlStatus(PriceLimitControlQueryParam param);

    /**
     * 批量导入商品到指定规则
     * @param ruleId 规则ID
     * @param goodsList 商品列表
     * @return 操作结果，包含每个商品的导入状态和错误信息
     */
    CommonRes batchImportGoods(Long ruleId, List<PriceLimitControlGoodsExcel> goodsList);

    /**
     * 从指定规则中批量删除商品
     * @param ruleId 规则ID
     * @return 操作结果
     */
    CommonResult batchDeleteGoods(Long ruleId, List<Long> goodsIds);

    /**
     * 获取下一个可用的规则ID
     * @return 规则ID
     */
    Long getRuleId();

    /**
     * 查询商品列表
     * @param param
     * @return
     */
    CommonResult queryGoodsList(PriceLimitControlQueryParam param);
}
