package com.cowell.pricecenter.service;

import com.cowell.pricecenter.service.dto.GoodsDatailReqDTO;
import com.cowell.pricecenter.service.dto.GoodsMarketCateQueryVO;
import com.cowell.pricecenter.service.dto.MarketCateVo;
import com.cowell.pricecenter.service.dto.response.ItemSkuVo;
import java.util.List;
import java.util.Map;

public interface IItemCenterService {

    List<ItemSkuVo> queryItem(GoodsDatailReqDTO goodsDatailReqDTO);

    Map<String, List<MarketCateVo>> getMarketingCateItemByItemUnionId(GoodsMarketCateQueryVO goodsMarketCateQueryVO);
}
