package com.cowell.pricecenter.constant;

import com.cowell.pricecenter.redis.RedisKeysConstant;

/**
 *
 * <AUTHOR>
 */
public class PriceSyncConstant {

    public static final String PRICE_SYNC_CONFIG_TYPE = "PRICE_SYNC";

    /**
     * 时间format 格式
     */
    public static final String DATE_FORMAT_ = "yyyy-MM-dd HH:mm:ss";


    /**
     * token 采用OAuth授权方式为必填参数
     */
    public static final String JD_TOKEN_FIELD = "token";

    /**
     * 应用的app_key
     */
    public static final String JD_APP_KEY_FIELD = "app_key";

    /**
     * 签名
     */
    public static final String JD_SIGN_FIELD = "sign";

    /**
     * 时间戳，格式为yyyy-MM-dd HH:mm:ss，例如：2011-06-16 13:23:30。京东API服务端允许客户端请求时间误差为6分钟
     */
    public static final String JD_TIME_STAMP_FIELD = "timestamp";

    /**
     * 暂时只支持json
     */
    public static final String JD_FORMAT_FIELD = "format";

    /**
     *暂时只支持json
     */
    public static final String JD_FORMAT_FIELD_VALUE = "json";

    /**
     * API协议版本，可选值:1.0
     */
    public static final String JD_VERSION_FIELD ="v";

    /**
     * 默认1.0版本号
     */
    public static final String JD_VERSION_FIELD_VALUE = "1.0";

    public static final String JD_PARAM_JSON_FIELD = "jd_param_json";



    /**
     * 美团 请求返回的 参数常量
     */
    public static final String MT_RESULT_MAP_DATA = "data";
    public static final String MT_RESULT_MAP_CODE = "code";
    public static final String MT_RESULT_MAP_MSG = "msg";
    public static final String MT_RESULT_DATA_OK= "ok";
    public static final String MT_RESULT_DATA_PART_SUCCESS = "partSuccess";
    public static final String MT_RESULT_DATA_FAIL = "fail";



    public static final String JD_ONE_SYNC_PRICE_TYPE_UPDATE_STATION_PRICE = "updateStationPrice";

    public static final String JD_ONE_SYNC_PRICE_TYPE_CHANGE_PRICE = "changePrice";


    public static final String DEFAULT_USER_NAME = "SYSTEM";


    public static final String WAIT_DSX_ADJUST_CODE = "wait_dsx_adjust_code";

    public static final String PRICE_SYNC_CONFIG_B = "PriceSyncConfig_B";

    public static final String PRICE_BUSINESS_CHANNEL_LIST_INFO = RedisKeysConstant.PROJECT_NAMES + RedisKeysConstant.PRICE_BUSINESSCHANNELLIST_INFO;

    /**
     * 加盟所属连锁
     */
    public static final String GJ_CODE = "gjCode";
}
