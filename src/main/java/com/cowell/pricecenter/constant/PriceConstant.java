package com.cowell.pricecenter.constant;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.cowell.pricecenter.enums.StoreAttrTypeEnum;

/**
 * @program: pricecenter
 * @description: 价格常量
 * @author: jmlu
 * @create: 2022-04-22 05:26
 **/

public class PriceConstant {
	
	/**
	 * 入参与当前数据对比只要包含以下数据 需要按照直营类型返回
	 */
	public static final List<String> DIRECTSALE_LIST = new ArrayList<>(Arrays.asList(
			StoreAttrTypeEnum.DIRECT_SELFBUILD.getMessage(),
			StoreAttrTypeEnum.DIRECT_ACQUISITION.getMessage(),
			StoreAttrTypeEnum.JOIN.getMessage(),
			StoreAttrTypeEnum.JOIN_DIVISION_CONCESSION.getMessage(),
			StoreAttrTypeEnum.JOIN_DIVISION_SOLE_PROPRIETORSHIP.getMessage()
    ));
	
	/**
	 * 入参与当前数据对比只要包含以下数据 需要按照加盟单体类型返回
	 */
	public static final List<String> JOIN_SINGLE_LIST = new ArrayList<>(Arrays.asList(
			StoreAttrTypeEnum.JOIN_SINGLE_CONTAIN.getMessage(),
			StoreAttrTypeEnum.JOIN_SINGLE_NO_CONTAIN.getMessage()
    ));
	/**
	 * 入参与当前数据对比只要包含以下数据 需要按照加盟连锁类型返回
	 */
	public static final List<String> JOIN_CHAIN_LIST = new ArrayList<>(Arrays.asList(
			StoreAttrTypeEnum.JOIN_CHAIN_NO_CONTAIN.getMessage(),
			StoreAttrTypeEnum.JOIN_CHAIN_CONTAIN.getMessage()
    ));
    
	
    /**
     * 价格类型、组织机构名称展示的分隔符
     */
    public static final String NAME_SEPARATOR = "、";

    /**
     * 价格类型、组织机构值的分隔符
     */
    public static final String VALUE_SEPARATOR = ",";
    /**
     * 分页查询 最小记录数
     */
    public static final Long MIN_TOTAL_COUNT = 0L;

    /**
     * 高济医疗的组织机构ID
     */
    public static final Long GAOJI_TOP_ID = 3L;

    public static final Long FILE_SIZE_LIMIT = 1048576L; // 1*1024*1024L;

    /** BigDecimal -1  */
    public static final BigDecimal DEFAULT_LESS_ZERO = new BigDecimal(-1L);

    /**
     * 斜杠
     */
    public static final String SLANTING_BAR = "/";

    public static final String MDM_STORE_PREFIX = "PRICE_CENTER_MDM_STORE_PREFIX_";

    /**
     * 默认资源id
     */
    public static final long DEFAULT_RESOURCEID = 6;

    //维度默认DictType是0
    public static final int DIMENSION_DICT_TYPE = 0;

    //维度默认parentId是0
    public static final long DIMENSION_PARENT_ID = 0;

    // 价格精度（元）
    public static final int PRICE_SCALE = 3;

    /**
     * 权限的组织结构orgPath的分隔符
     */
    public static final String ORG_PATH_SEPARATOR = "/";

    /**
     * 批量提交SIZE
     */
    public static final Integer BATCH_COMMIT_SIZE = 100;

    public static final String UPDATE_PRICE_STORE_DETAIL_FAIL_TIPS = "更新PriceStoreDetail表未成功";
}
