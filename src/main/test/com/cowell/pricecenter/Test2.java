package com.cowell.pricecenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cowell.pricecenter.service.dto.request.AdjustPriceGroupStoreInfo;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.cowell.pricecenter.service.dto.response.PriceSyncConfigDTO;
import com.cowell.pricecenter.service.dto.response.PriceSyncConfigDataDTO;
import org.junit.Test;
import org.springframework.beans.BeanUtils;

public class Test2{
    @Test
    public static void test() {
        List<AdjustPriceGroupStoreInfo> storeIds =  JSON.parseObject("[{\"label\":\"安贞店测试01\",\"key\":2108},{\"label\":\"安贞店测试02\",\"key\":2109},{\"label\":\"安贞店测试03\",\"key\":2110}]",new ArrayList<AdjustPriceGroupStoreInfo>().getClass());
        System.out.println(storeIds);
        Set<Long> ll = new HashSet<>();
        ll.add(11l);
        ll.add(22l);
        ll.add(11l);
        System.out.println(ll);
    }



}
