package com.cowell.pricecenter;

/**
 * <AUTHOR>
 * @date 2022/4/1 15:07
 */
public class GenerateSqlTest {

    public static void main(String[] args) {
        for (int i=0; i< 128l; i++) {
            String tableNum = String.format("%03d", i);
            String sql = "CREATE TABLE `item_base_extend_"+tableNum+"` (\n" +
                "  `item_id` bigint(20) NOT NULL COMMENT '商品id',\n" +
                "  `out_data_id` varchar(20) DEFAULT NULL COMMENT 'mdm同步的一店一目数据id',\n" +
                "  `goods_no` varchar(200) NOT NULL DEFAULT '' COMMENT 'mdm商品编码',\n" +
                "  `business_id` bigint(20) NOT NULL COMMENT '连锁id',\n" +
                "  `store_id` bigint(20) NOT NULL COMMENT '门店id',\n" +
                "  `bus_no` varchar(20) NOT NULL COMMENT 'mdm门店编码',\n" +
                "  `com_id` varchar(100) NOT NULL DEFAULT '' COMMENT 'mdm连锁编码',\n" +
                "  `minimum_display` bigint(20) NOT NULL DEFAULT '0' COMMENT '最小陈列量',\n" +
                "  `push_level` varchar(40) NOT NULL DEFAULT '' COMMENT '销售属性',\n" +
                "  `goods_line` varchar(10) NOT NULL DEFAULT '' COMMENT '经营属性',\n" +
                "  `config_type` varchar(10) NOT NULL DEFAULT '' COMMENT '配置类型',\n" +
                "  `no_purchase` varchar(2) NOT NULL DEFAULT '' COMMENT '禁止采购',\n" +
                "  `no_delivery` varchar(2) NOT NULL DEFAULT '' COMMENT '禁止配送',\n" +
                "  `no_rtwind` varchar(2) NOT NULL DEFAULT '' COMMENT '禁止返仓',\n" +
                "  `no_rtvind` varchar(2) NOT NULL DEFAULT '' COMMENT '禁止返厂',\n" +
                "  `no_retailind` varchar(2) NOT NULL DEFAULT '' COMMENT '禁止销售',\n" +
                "  `no_applyfor` varchar(2) NOT NULL DEFAULT '' COMMENT '禁止请货',\n" +
                "  `no_transfer` varchar(2) NOT NULL DEFAULT '' COMMENT '禁止调拨',\n" +
                "  `max_reqqty` bigint(100) NOT NULL DEFAULT '0' COMMENT '请货最大上限',\n" +
                "  `preregister_product` varchar(2) NOT NULL DEFAULT '' COMMENT '处方登记品',\n" +
                "  `follow_product` varchar(2) NOT NULL DEFAULT '' COMMENT '随访品',\n" +
                "  `cold_product` varchar(2) NOT NULL DEFAULT '' COMMENT '冷链品',\n" +
                "  `upturned_product` varchar(2) NOT NULL DEFAULT '' COMMENT '上翻品',\n" +
                "  `version` int(32) NOT NULL DEFAULT '0' COMMENT '版本',\n" +
                "  `gmt_create` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                "  `gmt_update` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',\n" +
                "  `sync_time` datetime DEFAULT NULL COMMENT '同步时间',\n" +
                "  `created_by` varchar(32) NOT NULL DEFAULT '' COMMENT '创建人',\n" +
                "  `updated_by` varchar(32) NOT NULL DEFAULT '' COMMENT '更新人',\n" +
                "  `extend` text COMMENT '扩展字段',\n" +
                "  KEY `idx_storeId_businessId_goodsno` (`store_id`,`business_id`,`item_id`) USING BTREE\n" +
                ") ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='一店一目属性';";
            System.out.println(sql);
        }
    }
}
