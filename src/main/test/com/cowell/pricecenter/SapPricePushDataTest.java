package com.cowell.pricecenter;

import com.alibaba.fastjson.JSONObject;
import com.cowell.framework.utils.IdUtils;
import lombok.val;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.remoting.exception.RemotingException;

/**
 * <AUTHOR>
 * @date 2024/6/17 10:33 AM
 */
public class SapPricePushDataTest {

    public static void main(String[] args) throws MQClientException, MQBrokerException, RemotingException, InterruptedException {

        // 1. 创建消息生产者 Producer，并制定生产者组名
        DefaultMQProducer producer = new DefaultMQProducer("please_rename_unique_group_name_sap_price");
        // 2. 指定 Nameserver 地址
        producer.setNamesrvAddr("10.8.131.14:9876;10.8.131.15:9876");
        // 3. 启动 Producer 实例
        producer.start();

        for (int i = 0; i <= 1; i++) {
            String message = "{\"action\":\"callSapToStockcenterInfo\",\"appName\":\"stock\",\"destination\":\"stock\",\"erpType\":\"\",\"limit\":200,\"passWord\":\"\",\"requestBody\":\"{\\\"Table\\\":{\\\"bdatetime\\\":\\\"2024-06-22 11:37:50\\\",\\\"bstatus\\\":1,\\\"bdatahash\\\":\\\"F609C911C29DEAEB003E7371DC337603\\\",\\\"btype\\\":8018,\\\"bdestination\\\":603,\\\"bguid\\\":\\\"SAPERP600FA163EE963E71EEF8C891992908A02F1\\\",\\\"bsource\\\":101,\\\"bversion\\\":\\\"V1.0\\\",\\\"bdata\\\":\\\"[{\\\\\\\"BUKRS\\\\\\\":\\\\\\\"3680\\\\\\\",\\\\\\\"WERKS\\\\\\\":\\\\\\\"Y3G9\\\\\\\",\\\\\\\"ZTYPE\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"MATNR\\\\\\\":\\\\\\\"1000016\\\\\\\",\\\\\\\"VERPR\\\\\\\":21,\\\\\\\"ZLOEVM\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"MXWRT\\\\\\\":21,\\\\\\\"GKWRT\\\\\\\":21}]\\\"}}\",\"requestMode\":\"MQ\",\"requestUrl\":\"\",\"serviceDesc\":\"sap推送目录价\",\"source\":\"hd or yk\",\"userName\":\"\"}";
            // 4. 创建消息对象，指定主题 Topic、Tag 和消息体
            Message msg = new Message("ERP_CALLBACK_SAP_PUSH_PRICE_SERVICE_TOPIC_TEST", /* Tag* */ "ERP_CALLBACK_SAP_PUSH_PRICE_SERVICE_TAG_TEST", message.getBytes());
            // 5. 发送消息到一个 Broker
            SendResult sendResult = producer.send(msg);
            // 6. 打印发送结果
            System.out.printf("%s%n", sendResult);
        }

        // 7. 关闭 Producer 实例
        producer.shutdown();


//        Long lastFiveNum = Long.parseLong(IdUtils.getFirstNumber(IdUtils.getLastNumber("3686663250760",10),5));
//        Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(lastFiveNum.toString(),3));
//        System.out.println(lastThreeNum);
//        System.out.println("table====" + lastThreeNum%128);
//
//        System.out.println((lastFiveNum - lastThreeNum) / 1000);
//        System.out.println("db====" + 23%4);
    }
}
