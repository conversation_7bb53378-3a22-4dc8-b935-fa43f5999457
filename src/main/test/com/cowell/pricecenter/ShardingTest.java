package com.cowell.pricecenter;

import com.alibaba.fastjson.JSONObject;
import com.cowell.framework.utils.IdUtils;
import com.cowell.pricecenter.service.dto.response.PriceSyncConfigDTO;
import com.cowell.pricecenter.service.dto.response.PriceSyncConfigDataDTO;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;

import java.util.Date;

public class ShardingTest {
    private static final int DB_COUNT = 4;
    private static final int TABLE_COUNT = 128;

    @Test
    public void test1() {
        String val = "904620431763";
        Long lastFiveNum = Long.parseLong(IdUtils.getFirstNumber(IdUtils.getLastNumber(val.toString(), 10), 5));
        Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(lastFiveNum.toString(), 3));
        String suffix = "_" + lastThreeNum % TABLE_COUNT + "";
        System.out.println(suffix);
        //                Long lastFiveNum = Long.parseLong(IdUtils.getLastNumber(val, 5));
//                Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(val, 3));
        Long dbNum = (lastFiveNum - lastThreeNum) / 1000;
        // 后五位前两位分库
        suffix = "_" + dbNum % DB_COUNT + "";
        System.out.println(dbNum);
        System.out.println(suffix);
    }


    @Test
    public  void test3() {
        PriceSyncConfigDTO dto = new PriceSyncConfigDTO();
        PriceSyncConfigDataDTO dataDTO = JSONObject.parseObject("{\"syncType\":1}", PriceSyncConfigDataDTO.class);
        BeanUtils.copyProperties(dataDTO, dto);
        System.out.println(dto);
    }


    @Test
    public void test2() {
        String s = "1850287224147#1848078424147#1847282524147#1849629124147#1848890624147#1850459224147#1849467124147#1847873424147#1849837924147#1850013824147#1850831024147#1847474324147#1847625324147#1848457224147#1850679024147#1851227724147#1849065024147#1848668124147#1851044824147#1849226324147#1947621124147#2228858124147#2229022324147#2020636898747#2020273998747#2229835624147#2217227624147#2217486624147#2265472924147#2294244724147#1649488337979#1645833037979#1643673537979#1642490137979#1647887937979#1647232737979#1645481537979#1650463737979#1644217937979#1917011537979#1916815637979#1648240237979#1650622437979#1646626037979#1646026337979#1645234237979#1650823237979#1643881637979#1649049137979#1650242737979#1648843037979#1647688237979#1645696137979#1646479337979#1642847337979#1642286437979#1646867337979#1642677337979#1648654937979#1649862937979#1647498237979#1644036637979#1645037937979#1643082637979#1644637537979#1643468437979#1649267037979#1644458237979#1648067137979#1650048537979#1644876637979#1646213337979#1649693637979#1648442637979#1643217637979#2035674137979#1815451598747#1818034398747#1815048798747#1821217698747#1822462598747#1815238598747#1814685898747#1815611398747#1821062398747#1822080598747#1820676898747#1819623998747#1818872498747#1816493498747#1821893898747#1817273898747#1816094598747#1816267198747#1819250198747#1814886998747#1816677398747#1814295198747#1818680998747#1818485898747#1819841798747#1819497998747#1821612698747#1814435798747#1815846798747#1816886698747#1817697798747#1820093498747#1819085698747#1817099498747#1822275598747#1820235798747#1820857098747#1818280898747#1817885498747#1820428198747#1821497198747#1817437298747#1917644498747#1647046037979#1848231924147";

        String str[] = s.split("#");
        for (String val : str) {
            Long lastFiveNum = Long.parseLong(IdUtils.getFirstNumber(IdUtils.getLastNumber(val.toString(), 10), 5));
            Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(lastFiveNum.toString(), 3));
            String suffix = "_" + lastThreeNum % TABLE_COUNT + "";

            //                Long lastFiveNum = Long.parseLong(IdUtils.getLastNumber(val, 5));
//                Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(val, 3));
            Long dbNum = (lastFiveNum - lastThreeNum) / 1000;
            // 后五位前两位分库
            String suffixa = "_" + dbNum % DB_COUNT + "";

            if("_2".equals(suffixa)){
                System.out.println("select * from price_store_detail"+suffix+"  WHERE ( store_id = "+val+" )   LIMIT 10 ;");
            }
            //System.out.println(suffix);
        }

    }

    public static void main(String[] args) {
//        Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber("1AJUST20240711095515007231", 3));
//        long ext = lastThreeNum % TABLE_COUNT;
//        System.out.println(ext);


        Long lastFiveNum = Long.parseLong(IdUtils.getFirstNumber(IdUtils.getLastNumber("312322203231",10),5));
        Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(lastFiveNum.toString(),3));
        Long dbNum = (lastFiveNum - lastThreeNum) / 1000;
        // 后五位前两位分库
        String suffix = "_" + dbNum % 4 + "";
        System.out.println("database===" + suffix);
        // 后五位后三位两位分表
        String suffixT = "_" + lastThreeNum % 128 + "";
        System.out.println("table=====" +  suffixT);

//        String dateStr = DateUtils.dateToString(new Date(), DateUtils.DATETIME_FORMAT);
//        System.out.println(dateStr);
    }


}
