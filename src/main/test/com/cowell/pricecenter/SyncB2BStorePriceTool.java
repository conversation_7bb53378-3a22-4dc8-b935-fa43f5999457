package com.cowell.pricecenter;

import cn.hutool.poi.excel.ExcelReader;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

import java.util.List;
import java.util.Map;

public class SyncB2BStorePriceTool {

    public static void main(String[] args) throws Exception {
        {
            DefaultMQProducer producer = new DefaultMQProducer("please_rename_unique_group_name_sap_price");
            // 2. 指定 Nameserver 地址
            //预发地址
            producer.setNamesrvAddr("biz-core_microservice_rocketmq-01_stage.cowelltech.com:9876");
            // 2. 测试地址指定 Nameserver 地址
//            producer.setNamesrvAddr("10.8.131.14:9876;10.8.131.15:9876");
            //生产地址
//            producer.setNamesrvAddr("social-crm-microservice-rocketmq-01_prod.cowelltech.com:9876");
            // 3. 启动 Producer 实例
            producer.start();
//            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader("/Users/<USER>/文档/excel模板/最新批发价格-20240704.XLSX");
//            List<Map<String, Object>> dataList = reader.readAll();
//            for (Map<String, Object> dataMap : dataList) {
            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader("/Users/<USER>/文档/excel模板/龙一目录价-20240717最新_副本.XLSX");
            List<Map<String, Object>> dataList = reader.readAll();
            List<String> storeNoList = Lists.newArrayList("Y2J4","Y3I1","Y1U4","Y3H3","Y1A1","Y1A0","Y0R0","Y1U9","Y0ID","Y1V2","Y0P0","Y1V9","Y1U7","Y1U2","Y1C5","Y2S7","Y1B5","Y0P1","Y0R9","Y0T5","Y0IA","Y1Q2","Y2H2","Y0IB","Y0Z5","Y1G1","Y1P9","Y0N8","Y1B3","Y1N8","Y1C0","Y0P2","Y1H0","Y1E4","Y1B4","Y0IG","Y1R2","Y1D5","Y2E8","Y0V9","Y2F0","Y0J9","Y3K2","Y0W0","Y1T6","Y0J5","Y0Z9","Y1A6","Y0P6","Y0G3","Y2V7","Y1V7","Y0R5","Y3H2","Y1M1","Y3I9","Y0Z4","Y2L8","Y0S5","Y0R10","Y4CM","Y0T1","Y0H6","Y0T7","Y0I1",
                "Y2M4","Y0U3","Y0H0","Y0S4","Y0H2","Y0T9","Y2M7","Y3G0","Y1G2","Y0W3","Y1S5","Y0T3","Y1U0","Y1G3",
                "Y0T8","Y2K2","Y0G1","Y1X5","Y3C6","Y3I4","Y2L5","Y0P9","Y0W8","Y1M10","Y4CK","Y0IM","Y2J5","Y0F7",
                "Y3H1","Y2D6","Y0W9","Y2V0","Y2W9","Y3P0","Y2X0","Y0V3","Y2Y4","Y1H4","Y2X1","Y0H7","Y2X5","Y1H5",
                "Y2R7","Y2N0","Y2B9","Y2Y3","Y2R5","Y2R8","Y2D0","Y1L5","Y2H5","Y3C2","Y1S4","Y0V7","Y3A3","Y2O5",
                "Y2A7","Y3B6","Y3S5","Y2Z6","Y1T2","Y2R9","Y2Z3","Y2O1","Y2S2","Y2I2","Y3C9","Y3U0","Y3D7","Y2S0","Y3S6","Y3E1","Y3C3","Y3D9","Y1R6","Y2P2","Y1Q3","Y2W4","Y3D8","Y3I8","Y3E7","Y2R4","Y2M2","Y2K1","Y0G6","Y1Q9","Y0R7","Y0G4","Y2N3","Y0Y1","Y0G8","Y2F4","Y0Q6","Y1F6","Y0U8","Y1E7","Y3J1","Y3J4","Y1G6","Y0P8","Y1P0","Y1F5","Y0U0","Y1G7","Y1Z2","Y1Y8","Y2F3","Y2M3","Y1E0","Y1H6","Y2N4","Y1A7","Y2B4","Y1R3","Y1D4","Y1Q7","Y0H1","Y1B7","Y0H8","Y0G5","Y0Q1","Y1L3","Y0T6","Y1D9","Y2A4","Y1T5","Y2C7","Y0H5","Y0R8","Y2F8","Y0H9","Y2C3","Y3Q4","Y2O0","Y2C8","Y2Y7","Y3S2","Y0V5","Y3Z0","Y0W2","Y3Y9","Y0X2","Y3A4","Y2K7","Y1W6","Y1W8","Y2A9","Y2N8","Y2N7","Y1P4","Y1K5","Y3Y7","Y1X1","Y2K9","Y1W0","Y3R6","Y0V8","Y3K1","Y2B7","Y2B1","Y0X0","Y0U1","Y0W5","Y3Y6","Y0X6","Y1X3","Y3R0","Y3K5","Y2B6","Y2C0","Y3X8","Y3Y8","Y2L2","Y2Z4","Y0I5","Y2S6","Y2X2","Y2B5","Y1Y0","Y2T0","Y0H4","Y1Y6","Y1A3","Y0M3","Y2P4","Y0U7","Y1W1","Y2Q1","Y0F9","Y0U5","Y1V5","Y1K7","Y1M6","Y2P3","Y2P6","Y0I6","Y0N6","Y0Y0","Y1S6","Y2B3","Y0L9","Y2A5","Y3F8","Y2L6","Y0U6","Y3T8","Y0I0","Y2R1","Y1R4","Y0I3","Y2P7","Y3X6","Y1K0","Y0Y6","Y2O9","Y3J2","Y3S7","Y3L8","Y0IJ","Y0X8","Y0N7","Y0F6","Y2S4","Y1U6","Y2A2","Y1S7","Y1P7","Y1Z3","Y0X7","Y3N8","Y0W7","Y0Y7","Y0L2","Y0L1","Y2A8","Y0L3","Y0L4","Y2Y5","Y3T3","Y3U7","Y0W6","Y3S8","Y3T0","Y3B4","Y3Z5","Y1S8","Y1J3","Y1J8","Y0X9","Y2D3","Y0Y2","Y0Y3","Y2D1","Y3U5","Y3C5","Y3T4","Y0Y4","Y3D2","Y3T2","Y3D1","Y3V1","Y2P9","Y3V7","Y3D3","Y3Z6","Y2P8","Y0Y8","Y1N1","Y2E3","Y4A5","Y2D7","Y3E5","Y3F5","Y4A0","Y0Z1","Y4A1","Y0S2","Y0Z7","Y0K0","Y1G0","Y0K9","Y0IK","Y2R2","Y0S8","Y3D5","Y2S1","Y1W2","Y3K9","Y2R6","Y1Q0","Y2W8","Y3D6","Y1Y2","Y3V4","Y2V1","Y2G7","Y1B2","Y2L7","Y2L4","Y3E0","Y2P1","Y1Y3","Y2M5","Y2T2","Y1B9","Y3J0","Y0R1","Y2D9","Y2J8","Y2S3","Y0Q5","Y3E6","Y1D8","Y0G9","Y0Y9","Y3E2","Y2K8","Y1B0","Y3M7","Y3E3","Y1B1","Y3E9","Y3E4","Y2E6","Y3J7","Y2R3","Y1T8","Y0I2","Y2D8","Y1A9","Y1F2","Y2L1","Y2U2","Y0L7","Y0X3","Y1L1","Y1L6","Y3U9","Y2C6","Y0M0","Y0M2","Y2J1","Y3V0","Y3V5","Y0Q9","Y0R6","Y4A7","Y1L8","Y2B8","Y2U5","Y3U8","Y2D2","Y0F8","Y0M4","Y3W3","Y2J0","Y2G2","Y4A9","Y2F7","Y2I9","Y0T2","Y2G1","Y3G2","Y2U4","Y4A8","Y2L3","Y2J2","Y3V9","Y1N6","Y0IE","Y0Q2","Y1V4","Y2F9","Y1M8","Y2H4","Y0L5","Y2M6","Y2C4","Y1D6","Y3F4","Y1R7","Y2G4","Y3Z1","Y2Q0","Y0Q3","Y3X5","Y2A3","Y1X2","Y1S2","Y0V6","Y2X9","Y2V3","Y3F9","Y3Q3","Y3Q1","Y0U2","Y3Q2","Y3N6","Y2Q4","Y1W5","Y0V4","Y3Q0","Y2Q3","Y1X8","Y2S9","Y0G7","Y1B6","Y2J9","Y0S7","Y3P6","Y3N4","Y0I7","Y0X5","Y1Y7","Y0V1","Y3L2","Y2U6","Y2X8","Y0T0","Y0IL","Y1U3","Y0R3","Y2M0","Y3L4","Y2Y0","Y0IF","Y2J7","Y0X4","Y3D0","Y2I5","Y3W4","Y3W6","Y2I7","Y3G3","Y1D0","Y1A8","Y2T1","Y3W0","Y3G4","Y3W7","Y2E1","Y0IC","Y3H5","Y1W9","Y1T9","Y0IH","Y3J9","Y0K1","Y1D3","Y1C9","Y2U7","Y1W7","Y3X4","Y1X0","Y1U8","Y1Z4","Y0K5","Y1J0","Y1X9","Y2Y6","Y0Y5","Y3A6","Y0J4","Y3A5","Y1X4","Y1V1","Y2Z7","Y3X0","Y3R4","Y3B9","Y3X3","Y3U3","Y3A1","Y1V3","Y3Z3","Y3X1","Y2Z9","Y3Y0","Y3U4","Y2O6","Y3Y4","Y2P0","Y2E0","Y0Z0","Y0Z8","Y0Q0","Y1N5","Y0J1","Y0G2","Y4A4","Y2E7","Y0J6","Y4A3","Y3Z8","Y1C1","Y1C6","Y4A2","Y1Q4","Y1Z0","Y1Z5","Y0K4","Y0K7","Y3Q5","Y3I7","Y1G5","Y0J8","Y1C2","Y1R1","Y1P5","Y0S3","Y0J7","Y0K2","Y1J1","Y1E9","Y0K3","Y0J3","Y3Z7","Y1H1","Y1N7","Y1X6","Y0H3","Y3N5","Y2T6","Y1B8","Y1H9","Y3N0","Y2K6","Y0V2","Y0J2","Y0S1","Y3I2","Y3I3","Y1C8","Y1E2","Y3L0","Y1E1","Y1D2","Y1D1","Y1C3","Y3K4","Y1C7","Y1F1","Y3J5","Y3J6","Y1E3","Y3K6","Y1Z9","Y3L7","Y3J8","Y1E5","Y2A1","Y3L5","Y1Z8","Y1W4","Y3M9","Y2A0","Y3M3","Y3M5","Y2V8","Y2K4","Y2W6","Y1F8","Y1Z7","Y3N3","Y2T7","Y1F9","Y1Z6","Y3I5","Y2O8","Y1G4","Y3K0","Y2I4","Y0M6","Y1W3","Y3U6","Y0M5","Y1F7","Y0N3","Y2Q7","Y4B0","Y2K3","Y2H3","Y1M4","Y1V0","Y0R2","Y2L9","Y2J6","Y0V0","Y2I0","Y2H1","Y3N1","Y3M1","Y3P8","Y2W7","Y3Q8","Y3M2","Y3V6","Y3I0","Y2U9","Y2G8","Y0L8","Y0I4","Y2K0","Y2W1","Y2E5","Y4A6","Y3D4","Y3L3","Y3M8","Y3R9","Y2G9","Y1M7","Y2G0","Y0P5","Y2U3","Y3Q9","Y2H0","Y3S0","Y3P5","Y1M3","Y2F5","Y3L1","Y0X1","Y1A2","Y2N1","Y2G6","Y3L9","Y2H9","Y0W4","Y1V8","Y1F0","Y1P2","Y1R5","Y1P6","Y2U1","Y1J4","Y1E6","Y1J2","Y2U0","Y2L0","Y2T8","Y2X6","Y1K1","Y0S9","Y2M8","Y3K3","Y1K9","Y1P8","Y2T9","Y1H7","Y3S9","Y1D7","Y1P1","Y1X7","Y3R1","Y1K2","Y3S3","Y3P7","Y3T1","Y2Y2","Y1G9","Y2I8","Y1P3","Y1L0","Y3R2","Y3F6","Y3V2","Y1Q1","Y2V2","Y2H6","Y3T9","Y0Z6","Y2B2","Y1F3","Y2F6","Y3R5","Y1L4","Y2K5","Y3U1","Y3M4","Y0L6","Y3T5","Y2Z8","Y3A0","Y1Y1","Y2G5","Y2W2","Y0W1","Y3Y1","Y1Q5","Y0K8","Y3M6","Y1L2","Y1J6","Y2Y1","Y3Z2","Y2H7","Y3Y3","Y3S4","Y3S1","Y3A7","Y1Y9","Y1K8","Y3X7","Y2Z2","Y2H8","Y2C1","Y3A8","Y0M1","Y3X9","Y1S3","Y3C0","Y2O4","Y2M9","Y3A2","Y2N9","Y3Y2","Y2I3","Y2O7","Y2C9","Y0K6","Y0N9","Y2C5","Y3Y5","Y2I1","Y3U2","Y1T1","Y3B3","Y3T6","Y1S9","Y3B8","Y2D4","Y3C4","Y1T0","Y1L9","Y2E2","Y3C1","Y3B5","Y3W2","Y2D5","Y2O2","Y2I6","Y2O3","Y0T4","Y1F4","Y3F0","Y2V4","Y3F3","Y2V9","Y1Z1","Y2V6","Y2V5","Y1M0","Y2W0","Y3F1","Y2Y9","Y1Y4","Y1M9","Y2W3","Y2Z1","Y1Q6","Y1Y5","Y2N6","Y1R0","Y1Q8","Y3N7","Y3C7","Y2A6","Y2X3","Y1H2","Y3Q6","Y3F2","Y2E9","Y2X7","Y1R9","Y1J9","Y0M7","Y0N1","Y3G6","Y2Q5","Y3P1","Y2W5","Y2E4","Y0N5","Y2Q9","Y3H6","Y0N4","Y0N0","Y3T7","Y3W8","Y3W9","Y3N9","Y0I8","Y3G1","Y0I9","Y3K8","Y2J3","Y3P3","Y3G5","Y2Q2","Y3H8","Y2Q8","Y2Q6","Y2R0","Y3L6","Y1N3","Y1U1","Y3J3","Y3Z4","Y0M8","Y2S5","Y3H7","Y1C4","Y3I6","Y3P2","Y3Z9","Y3G8","Y1N2","Y3V3","Y0N2","Y0M9","Y3H4","Y3K7","Y3H9","Y0Q7","Y3R8","Y2Y8","Y3A9","Y2T4","Y3P4","Y3H0","Y3R7","Y1J7","Y1H8","Y0P3","Y3W1","Y1M5","Y0R4","Y1L7","Y0P7","Y3B7","Y2U8","Y3V8","Y0Z2","Y1T4","Y3F7","Y1K3","Y1A4","Y0G0","Y2Z0","Y2Z5","Y1T3","Y0Q8","Y1G8","Y1E8","Y3G9","Y2G3","Y3W5","Y0S6","Y1A5","Y0P4","Y1K4","Y2N2","Y1N9","Y1U5","Y3R3","Y1R8","Y2S8","Y1V6","Y2T3","Y1M2","Y2P5","Y2T5","Y0U9","Y3P9","Y1J5","Y1H3","Y3N2","Y2N5","Y2B0","Y2F2","Y2F1","Y3C8","Y0IN","Y3B0","Y2X4","Y1S0","Y3Q7","Y3E8","Y3B2","Y1S1","Y1T7","Y4B1","Y1K6","Y3B1","Y0U4","Y3X2","Y2M1","Y0Q4","Y0L0","Y3M0","Y3G7","Y4B2","Y2C2","Y0Z3","Y1N4","Y4BJ","Y4BK","Y4BL","Y4BM","Y4BN","Y4BN",
                "V701","Y4CL","Y4F5","Y4F7","Y4GT","Y4E7","Y4GU","Y4E1","Y4E2","Y4F4","Y4E6","Y4E3","Y4GV","Y4GW","Y4F8","Y4GS","Y4E8","Y4J2","Y4HC","Y4JB","Y4JG","Y4JH","Y4JK","Y4J8","Y4JJ","Y4J6","Y4HB","Y4JQ"

                );
//            List<String> storeNoList = Lists.newArrayList("Y4E6");
            for (Map<String, Object> dataMap : dataList) {
                // 获取商品编号
                String goodsNo = dataMap.get("商品").toString();
                String price = dataMap.get("单价").toString();
                String lowerLimit = dataMap.get("金额下限").toString();
                String upperLimit = dataMap.get("金额上限").toString();
                for (String storeNo : storeNoList) {
                    String message = "{\"action\":\"callSapToStockcenterInfo\",\"appName\":\"stock\",\"destination\":\"stock\",\"erpType\":\"\",\"limit\":200,\"passWord\":\"\",\"requestBody\":\"{\\\"Table\\\":{\\\"bdatetime\\\":\\\"2024-07-04 18:06:50\\\",\\\"bstatus\\\":1,\\\"bdatahash\\\":\\\"F609C911C29DEAEB003E7371DC337603\\\",\\\"btype\\\":8018,\\\"bdestination\\\":603,\\\"bguid\\\":\\\"SAPERP600FA163EE963E71EEF8C891992908A02F1\\\",\\\"bsource\\\":101,\\\"bversion\\\":\\\"V1.0\\\",\\\"bdata\\\":\\\"[{\\\\\\\"BUKRS\\\\\\\":\\\\\\\"3680\\\\\\\",\\\\\\\"WERKS\\\\\\\":\\\\\\\"" + storeNo + "\\\\\\\",\\\\\\\"ZTYPE\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"MATNR\\\\\\\":\\\\\\\"" + goodsNo + "\\\\\\\",\\\\\\\"VERPR\\\\\\\":" + price + ",\\\\\\\"ZLOEVM\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"MXWRT\\\\\\\":" + lowerLimit + ",\\\\\\\"GKWRT\\\\\\\":" + upperLimit + "}]\\\"}}\",\"requestMode\":\"MQ\",\"requestUrl\":\"\",\"serviceDesc\":\"sap推送目录价\",\"source\":\"hd or yk\",\"userName\":\"\"}";
                    // 4. 创建消息对象，指定主题 Topic、Tag 和消息体
                    Message msg = new Message("ERP_CALLBACK_SAP_PUSH_PRICE_SERVICE_TOPIC_STAGE", /* Tag* */ "ERP_CALLBACK_SAP_PUSH_PRICE_SERVICE_TAG_STAGE", message.getBytes());
                    // 5. 发送消息到一个 Broker
                    SendResult sendResult = producer.send(msg);
                    System.out.println("goodsNo====" + goodsNo);
                    System.out.println("storeNo=====" + storeNo);
                }
                // 6. 打印发送结果
                System.out.println(goodsNo);
            }
//        }
        }
    }
}

