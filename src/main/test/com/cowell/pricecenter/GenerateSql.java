package com.cowell.pricecenter;

/**
 * <AUTHOR>
 * @date 2024/6/15 10:46 AM
 */
public class GenerateSql {
    public static void main(String[] args) {
//        generateSql();
//        generateDropIndexSql("IDX_BUSINESS_ITEM_UNION_STORE_ID", "item_base_%s", 128);
//        generateDropIndexSql("IDX_BUSINESS_STORE_SKU", "item_sku_%s", 128);
//        generatePriceSql();
        generatePriceIndex();
    }

    private static void generatePriceSql() {
        String priceSql = "ALTER TABLE price_store_detail_%s ADD COLUMN channel_store_id BIGINT DEFAULT 0 COMMENT '渠道门店id';\n";
        for (int i = 0; i < 128; i++){
            System.out.println(String.format(priceSql, i));
        }
    }
    private static void generateSql() {
        int length = 3;
        for (int i = 0; i < 128; i++) {
            String paddingNo = String.format("%0" + length + "d", i);
            String sql1 = "ALTER TABLE item_base_%s\n" +
                "ADD COLUMN channel_store_id BIGINT DEFAULT 0 COMMENT '渠道门店id',\n" +
                "ADD COLUMN channel_type int DEFAULT 0 COMMENT '门店商品渠道类型(0:默认; 1:加盟-B2B;)';";
            System.out.println(String.format(sql1, paddingNo));
            String itemBaseIndexSql = "ALTER TABLE item_base_%s\n" +
                "ADD INDEX idx_bid_iuid_sid_csid_ct (business_id, item_union_id, store_id, channel_store_id, channel_type);";
            System.out.println(String.format(itemBaseIndexSql, paddingNo));

            String skuSql = "ALTER TABLE item_sku_%s\n" +
                "ADD COLUMN channel_store_id BIGINT DEFAULT 0 COMMENT '渠道门店id',\n" +
                "ADD COLUMN channel_type int DEFAULT 0 COMMENT '门店商品渠道类型(0:默认; 1:加盟-B2B;)';";
            System.out.println(String.format(skuSql, paddingNo));
            String itemSkuIndexSql = "ALTER TABLE item_sku_%s\n" +
                "ADD INDEX idx_bid_iuid_sid_smc_csid_ct (business_id, store_id,sku_merchant_code, channel_store_id, channel_type);";
            System.out.println(String.format(itemSkuIndexSql, paddingNo));

            String itemInfoSql = "ALTER TABLE item_info_%s\n" +
                "ADD COLUMN channel_store_id BIGINT DEFAULT 0 COMMENT '渠道门店id',\n" +
                "ADD COLUMN channel_type int DEFAULT 0 COMMENT '门店商品渠道类型(0:默认; 1:加盟-B2B;)';";
            System.out.println(String.format(itemInfoSql, paddingNo));

//            String channelQrCode = "ALTER TABLE item_channel_qr_code_%s\n" +
//                "ADD COLUMN channel_store_id BIGINT DEFAULT 0 COMMENT '渠道门店id',\n" +
//                "ADD COLUMN channel_type int DEFAULT 0 COMMENT '门店商品渠道类型(0:默认; 1:加盟-B2B;)';";
//            System.out.println(String.format(channelQrCode, paddingNo));

            String skuSnapSql = "ALTER TABLE item_sku_snap_%s\n" +
                "ADD COLUMN channel_store_id BIGINT DEFAULT 0 COMMENT '渠道门店id',\n" +
                "ADD COLUMN channel_type int DEFAULT 0 COMMENT '门店商品渠道类型(0:默认; 1:加盟-B2B;)';";
            System.out.println(String.format(skuSnapSql, paddingNo));


        }
    }

    private static void generateDropIndexSql(String indexName, String tableName, Integer tableNum) {
        int length = 3;
        for (int i = 0; i < tableNum; i++) {
            String paddingNo = String.format("%0" + length + "d", i);
            String dropIndexSql = "DROP INDEX " + indexName + " ON " + String.format(tableName, paddingNo)  + ";";
            System.out.println(dropIndexSql);
        }

    }

    private static void generatePriceIndex() {
        String priceSql="ALTER TABLE price_store_detail_%s ADD INDEX idx_store_id_ptc_channel_id_csId (store_id, price_type_code, channel_id, channel_store_id);";
        for (int i = 0; i < 128; i++){
            System.out.println(String.format(priceSql, i));
        }
    }

}
