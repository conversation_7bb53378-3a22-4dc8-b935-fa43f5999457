package com.cowell.pricecenter;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cowell.pricecenter.service.dto.request.ImportPriceStoreDetailDTO;

import java.io.File;
import java.io.FileInputStream;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
public class UploadFileTest{
    @Test
    public void test() throws Exception {
        FileInputStream fis = null;
        File file = new File("d://调价单模版.xls");//文件路径(包括文件名称)
//        if (!file.exists()) {
//            file.createNewFile();
//        }
//        //将文件读入输入流
        fis = new FileInputStream(file);

        ExcelReader reader = ExcelUtil.getReader(fis);
//        List<List<Object>> readAllw = reader.read();

        List<Map<String, Object>> readAll = reader.readAll();
        Map<String, Object> checkRowMap = readAll.get(0);
        Set<String> set = checkRowMap.keySet();
        Object[] ss = set.toArray();
        System.out.println(String.valueOf(ss[0]));
        System.out.println(String.valueOf(ss[1]));

    }
}
