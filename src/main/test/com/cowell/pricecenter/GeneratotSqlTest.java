package com.cowell.pricecenter;

import org.junit.Test;

import java.io.IOException;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description:
 * @date 2020/03/03 10:27
 */
public class GeneratotSqlTest {

    @Test
    public void priceWarningTest(){
        StringBuffer newSql = new StringBuffer();
        for (int i = 0; i < 32; i++) {
            String preSql = "CREATE TABLE `price_warning_record_"+ String.format("%03d",i) +"` (                                                                             \n"+
                "   `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键',  \n"+
                "   `business_id` BIGINT(20) NOT NULL COMMENT '连锁id',      \n"+
                "   `store_id` BIGINT(20) NOT NULL COMMENT '门店id',         \n"+
                "   `store_name` VARCHAR(64) NOT NULL COMMENT '门店名称',    \n"+
                "   `goods_no` VARCHAR(64) NOT NULL COMMENT '商品编码',      \n"+
                "   `sku_name` VARCHAR(64) NOT NULL COMMENT '商品名称',      \n"+
                "   `price_type` INT(3) NOT NULL COMMENT '价格类型',       \n"+
                "   `src_price` BIGINT(20) NOT NULL COMMENT '当前价格',      \n"+
                "   `new_price` BIGINT(20) NOT NULL COMMENT '生效价格',      \n"+
                "   `price_source` TINYINT(3) NOT NULL COMMENT '价格来源 0：价格中台 1：海典pos',  \n"+
                "   `price_warn_type` TINYINT(3) NOT NULL COMMENT '价格异常原因 0：调价预警异常 1：海典比价异常', \n"+
                "   `last_update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间', \n"+
                "   `created_by` VARCHAR(100) DEFAULT '' COMMENT '创建人', \n"+
                "   `gmt_create` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', \n"+
                "   `updated_by` VARCHAR(100) DEFAULT NULL COMMENT '更新人', \n"+
                "   `gmt_update` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间', \n"+
                "   `status` TINYINT(3) DEFAULT '0' COMMENT '状态(-1删除，0正常)', \n"+
                "   `version` INT(11) DEFAULT '0' COMMENT '版本号', \n"+
                "   `extend` VARCHAR(2000) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '扩展字段', \n"+
                "   PRIMARY KEY (`id`), \n"+
                "   KEY `IDX_STORE_BUSINESS_GOODSNO_NAME_TYPE_GMTCREATE` (`store_id`,`business_id`,`status`,`goods_no`,`gmt_create`,`sku_name`,`price_warn_type`), \n"+
                "   KEY `IDX_BUSINESS_GOODSNO_NAME_TYPE_GMTCREATE` (`business_id`,`status`,`goods_no`,`gmt_create`,`sku_name`,`price_warn_type`) \n"+
                " ) ENGINE=INNODB DEFAULT CHARSET=utf8mb4 COMMENT='价格预警表';\n"  ;

            newSql.append(preSql).append("\r\n");
            if(i != 0 && i % 100 == 0){
                newSql.append("COMMIT;").append("\r\n");
            }
        }
        try {
            FileUtil.writeFileContent(newSql.toString(),"D:\\price_warning_record.sql");
        } catch (IOException e) {

        }
    }

    @Test
    public void priceSyncRecordTest(){
        StringBuffer newSql = new StringBuffer();
        for (int i = 0; i < 32; i++) {
            String preSql = " CREATE TABLE `price_sync_record_"+ String.format("%03d",i) +"` (                                                 \n"+
                "    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',                                                      \n"+
                "    `business_id` bigint(20) NOT NULL COMMENT '连锁id',                                                          \n"+
                "    `store_id` bigint(20) NOT NULL COMMENT '门店id',                                                             \n"+
                "    `store_name` varchar(64) NOT NULL COMMENT '门店名称',                             \n"+
                "    `channel` int(3) NOT NULL DEFAULT 0 COMMENT '渠道',                             \n" +
                "    `goods_no` varchar(32) NOT NULL COMMENT '商品编码',                               \n"+
                "    `sku_name` varchar(64) NOT NULL COMMENT '商品名称',                                   \n"+
                "    `price` bigint(20) NOT NULL COMMENT '同步价格',                                                              \n"+
                "    `price_type` INT(3) NOT NULL COMMENT '价格类型',                                                         \n"+
                "    `sync_result` tinyint(3) NOT NULL COMMENT '同步结果',                                                        \n"+
                "    `sync_warn_desc` varchar(512) DEFAULT '' COMMENT '异常原因',                       \n"+
                "    `created_by` varchar(100) DEFAULT '' COMMENT '创建人',                            \n"+
                "    `gmt_create` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '创建时间',                              \n"+
                "    `updated_by` varchar(100) DEFAULT NULL COMMENT '更新人',                          \n"+
                "    `gmt_update` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',    \n"+
                "    `status` tinyint(3) DEFAULT '0' COMMENT '状态(-1删除，0正常)',                                               \n"+
                "    `extend` varchar(2000) DEFAULT NULL COMMENT '扩展字段',                           \n"+
                "    `version` int(11) DEFAULT '0' COMMENT '版本号',                                                              \n"+
                "    PRIMARY KEY (`id`),                                                                                          \n"+
                "    KEY `IDX_ONE` (`store_id`,`business_id`,`status`,`sync_result`,`gmt_create`,`goods_no`,`sku_name`),              \n"+
                "    KEY `IDX_TWO` (`business_id`,`status`,`sync_result`,`gmt_create`,`goods_no`,`sku_name`)                          \n"+
                "  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='价格同步日志表';                                              \n";

            newSql.append(preSql).append("\r\n");
            if(i != 0 && i % 10 == 0){
                newSql.append("COMMIT;").append("\r\n");
            }
        }
        try {
            FileUtil.writeFileContent(newSql.toString(),"D:\\price_sync_record.sql");
        } catch (IOException e) {

        }
    }
}
