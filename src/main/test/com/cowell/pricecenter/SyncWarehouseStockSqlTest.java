package com.cowell.pricecenter;

import cn.hutool.poi.excel.ExcelReader;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class SyncWarehouseStockSqlTest {

    public static void main(String[] args) throws Exception {
            DefaultMQProducer producer = new DefaultMQProducer("please_rename_unique_group_name_sap_price");
            // 2. 测试地址指定 Nameserver 地址
//            producer.setNamesrvAddr("10.8.131.14:9876;10.8.131.15:9876");
            //预发地址
        producer.setNamesrvAddr("biz-core_microservice_rocketmq-01_stage.cowelltech.com:9876");
        String sql = "insert into stock_goods_batch_code_120 ('id','business_id','store_id','sku_merchant_code','batch_code','batch_no','piece_unit','stock'," +
            " ''buy_stock','piece_buy_stock','waitting_area_stock'," +
            "'piece_waitting_area_stock','unqualified_area_stock','piece_unqualified_are', 'wait_stock','transit_stock','unit','locked','version'," +
            "'medical_stock','medical_lock_stock','retail_stock',''retail_lock_stock','location','expdate_type') values";
            // 3. 启动 Producer 实例
            producer.start();
            ExcelReader reader = cn.hutool.poi.excel.ExcelUtil.getReader("/Users/<USER>/文档/excel模板/龙一仓库存.XLSX");
//        id	business_id	store_id	sku_merchant_code	batch_code	batch_no	piece_unit	stock	buy_stock	piece_buy_stock	waitting_area_stock	piece_waitting_area_stock	unqualified_area_stock	piece_unqualified_area_stock
        //wait_stock	transit_stock	unit	expire_date	produce_date	produce_company	supply	locked	sync_date	apply_type	version	extend	created_by	gmt_create	last_modified_by	gmt_update	medical_stock	medical_lock_stock	retail_stock	retail_lock_stock	location	update_timestamp	expdate_type
        List<Map<String, Object>> dataList = reader.readAll();
        for (Map<String, Object> dataMap : dataList) {
                Long id = (Long)dataMap.get("id");
                // 获取商品编号
                Long business_id = (Long)dataMap.get("business_id");
            Long store_id = (Long)dataMap.get("store_id");
            String batch_code = dataMap.get("batch_code").toString();
            String batch_no = dataMap.get("batch_no").toString();
            BigDecimal piece_unit =  (BigDecimal)dataMap.get("piece_unit");
            BigDecimal stock = (BigDecimal)dataMap.get("stock");
            BigDecimal buy_stock = (BigDecimal)dataMap.get("buy_stock");
            BigDecimal piece_buy_stock = (BigDecimal)dataMap.get("piece_buy_stock");
            BigDecimal waitting_area_stock = (BigDecimal)dataMap.get("waitting_area_stock");
            BigDecimal piece_waitting_area_stock = (BigDecimal)dataMap.get("piece_waitting_area_stock");
            BigDecimal unqualified_area_stock = (BigDecimal)dataMap.get("unqualified_area_stock");
            BigDecimal piece_unqualified_area_stock = (BigDecimal)dataMap.get("piece_unqualified_area_stock");
            BigDecimal wait_stock = (BigDecimal)dataMap.get("wait_stock");
            BigDecimal transit_stock = (BigDecimal)dataMap.get("transit_stock");
            String unit = dataMap.get("unit").toString();



            // 价格
                String price = dataMap.get("单价").toString();
                String lowerLimit = dataMap.get("金额下限").toString();
                String upperLimit = dataMap.get("金额上限").toString();
//                for (String storeNo : storeNoList) {
//                    String message = "{\"action\":\"callSapToStockcenterInfo\",\"appName\":\"stock\",\"destination\":\"stock\",\"erpType\":\"\",\"limit\":200,\"passWord\":\"\",\"requestBody\":\"{\\\"Table\\\":{\\\"bdatetime\\\":\\\"2024-07-04 18:06:50\\\",\\\"bstatus\\\":1,\\\"bdatahash\\\":\\\"F609C911C29DEAEB003E7371DC337603\\\",\\\"btype\\\":8018,\\\"bdestination\\\":603,\\\"bguid\\\":\\\"SAPERP600FA163EE963E71EEF8C891992908A02F1\\\",\\\"bsource\\\":101,\\\"bversion\\\":\\\"V1.0\\\",\\\"bdata\\\":\\\"[{\\\\\\\"BUKRS\\\\\\\":\\\\\\\"3680\\\\\\\",\\\\\\\"WERKS\\\\\\\":\\\\\\\"" + storeNo + "\\\\\\\",\\\\\\\"ZTYPE\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"MATNR\\\\\\\":\\\\\\\"" + goodsNo + "\\\\\\\",\\\\\\\"VERPR\\\\\\\":" + price + ",\\\\\\\"ZLOEVM\\\\\\\":\\\\\\\"1\\\\\\\",\\\\\\\"MXWRT\\\\\\\":" + lowerLimit + ",\\\\\\\"GKWRT\\\\\\\":" + upperLimit + "}]\\\"}}\",\"requestMode\":\"MQ\",\"requestUrl\":\"\",\"serviceDesc\":\"sap推送目录价\",\"source\":\"hd or yk\",\"userName\":\"\"}";
//                    // 4. 创建消息对象，指定主题 Topic、Tag 和消息体
//                    Message msg = new Message("ERP_CALLBACK_SAP_PUSH_PRICE_SERVICE_TOPIC_STAGE", /* Tag* */ "ERP_CALLBACK_SAP_PUSH_PRICE_SERVICE_TAG_STAGE", message.getBytes());
//                    // 5. 发送消息到一个 Broker
//                    SendResult sendResult = producer.send(msg);
//                }
                // 6. 打印发送结果
            }
//        }
    }
}
