package com.cowell.pricecenter.service;

import com.cowell.pricecenter.PricecenterApp;
import com.cowell.pricecenter.enums.AdjustPriceVersionEnum;
import com.cowell.pricecenter.enums.AdjustTypeEnum;
import com.cowell.pricecenter.enums.AuditStatusEnum;
import com.cowell.pricecenter.enums.ExecEnum;
import com.cowell.pricecenter.service.dto.request.AuditAdjustPriceOrderDTO;
import com.cowell.pricecenter.service.dto.request.SaveAdjustPriceOrderDTO;
import com.cowell.pricecenter.service.dto.request.SaveAdjustPriceOrderDetailDTO;
import com.cowell.pricecenter.web.rest.util.CheckAdjustPriceOrderUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.StopWatch;

import java.util.Collections;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/11 15:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PricecenterApp.class})
public class IAdjustPriceOrderServiceTest {

    @Autowired
    private IAdjustPriceOrderService adjustPriceOrderService;

    @Test
    public void effectAdjustPriceOrderAgain() {
        adjustPriceOrderService.effectAdjustPriceOrderAgain(AdjustPriceVersionEnum.VERSION_2_0.getVersion(), new Date());
    }

    @Test
    public void saveAdjustOrder() {
        SaveAdjustPriceOrderDTO dto = new SaveAdjustPriceOrderDTO();
        dto.setAdjustType((byte) AdjustTypeEnum.HIS.getCode());
        dto.setOrgId(603L);

        // 可为空
        dto.setOrgName("积分商城测试连锁");
        // 可为空
        dto.setOrderLevel(500);

        dto.setReason("调价原因");
        dto.setExecStatus((byte) ExecEnum.EXEC_NOW.getCode());
        dto.setUserId(111L);
        dto.setUserName("testUser");
        dto.setChannel("2");

        // 可为空
        // 商品范围(0:全部商品;1:白名单商品)
        dto.setGoodsScope(0);

        dto.setAdjustPriceType("LSJ");
        dto.setAdjustName("adjustName");
        dto.setAdjustGoodsCount(1);

        // 可为空
        dto.setAdjustOrgTabType(1);


        //
        //    /**
        //     * 预计生效时间
        //     */
        //    @ApiModelProperty(value = "预计生效时间，格式yyyy-MM-dd")
        //    private String scheduledTime;

        SaveAdjustPriceOrderDetailDTO detailDTO = new SaveAdjustPriceOrderDetailDTO();
        detailDTO.setGoodsNo("8100339");
        detailDTO.setSkuId(8100339999L);
        detailDTO.setSpuId(18100339999L);
        detailDTO.setPrice("12.2");
        detailDTO.setOrgIds("603");

        dto.setDetailList(Collections.singletonList(detailDTO));

        StopWatch stopWatch = new StopWatch("Test");
        stopWatch.start("saveAdjustPriceOrder");
        CheckAdjustPriceOrderUtil.checkSaveAdjustPriceOrder(dto);
        String adjustCode = adjustPriceOrderService.saveAdjustPriceOrder(dto);
        stopWatch.stop();

        AuditAdjustPriceOrderDTO auditAdjustPriceOrderDTO = new AuditAdjustPriceOrderDTO();
        auditAdjustPriceOrderDTO.setAdjustCode(adjustCode);
        auditAdjustPriceOrderDTO.setAuditStatus((byte) AuditStatusEnum.AUDIT_PASS.getCode());
        auditAdjustPriceOrderDTO.setUserId(111L);
        auditAdjustPriceOrderDTO.setUserName("testUser");
        stopWatch.start("audit");
        adjustPriceOrderService.audit(auditAdjustPriceOrderDTO);
        stopWatch.stop();
        System.out.println(stopWatch.prettyPrint());
    }
}
