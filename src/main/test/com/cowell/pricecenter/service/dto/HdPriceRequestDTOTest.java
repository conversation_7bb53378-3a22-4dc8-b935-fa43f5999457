package com.cowell.pricecenter.service.dto;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description:
 * @date 2020/03/07 16:41
 */
public class HdPriceRequestDTOTest {

    @Test
    public void hdPriceRequestDTOTets(){
        List<HdTableDTO> tables = Lists.newArrayList();
        HdTableDTO tableDTO = new HdTableDTO();
        tableDTO.setBguid("ERPSAAS34dccb7405024a249a0e88e250ece29e");
        tableDTO.setBtype("2009");
        tableDTO.setBsource("602");
        tableDTO.setBdestination("300");
        tableDTO.setBdatetime("2018-08-28 17:38:31");
        tableDTO.setBstatus("2");
        tableDTO.setBcallback("");
        tableDTO.setBversion("1.0");
        tableDTO.setBdatahash("3FE1F620C4A7EEFE0569DF1E0CB3C873");
        tableDTO.setBkeys("1");
        HdBDataDTO dataDTO = new HdBDataDTO();
        dataDTO.setCompId("3050");
        dataDTO.setBusNO("A07E");
        List<HdDataDetailDTO> detailDTOS = Lists.newArrayList();
        HdDataDetailDTO detailDTO = new HdDataDetailDTO();
        detailDTO.setRowNo(1);
        detailDTO.setGoodsNo("0001");
        detailDTOS.add(detailDTO);
        detailDTO = new HdDataDetailDTO();
        detailDTO.setRowNo(2);
        detailDTO.setGoodsNo("10011");
        detailDTOS.add(detailDTO);
        detailDTO = new HdDataDetailDTO();
        detailDTO.setRowNo(3);
        detailDTO.setGoodsNo("12001");
        detailDTOS.add(detailDTO);
        dataDTO.setDetails(detailDTOS);
        tableDTO.setBdata(dataDTO);
        tables.add(tableDTO);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Table", tables);
        System.out.println(jsonObject.toJSONString());
    }
}
