package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.PricecenterApp;
import com.cowell.pricecenter.entity.PriceChange;
import com.cowell.pricecenter.entity.PricePushTaskResultCount;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.mapper.extension.PricePushTaskExtMapper;
import com.cowell.pricecenter.service.IPriceSyncService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.List;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2022/5/19 14:53
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PricecenterApp.class)
public class PriceSyncServiceImplTest {

    @Resource
    private IPriceSyncService priceSyncServiceImpl;

    @Autowired
    private PricePushTaskExtMapper pricePushTaskExtMapper;

    @Test
    public void doSyncByPriceType() {

//        PriceChange priceChange = PriceChange.builder()
//            .businessId(99999L)
//            .storeId(87185399999L)
//            .priceTypeCode("LSJ")
//            .goodsNo("1020075")
//            .channelId(0)
//            .build();
//        priceSyncServiceImpl.doSyncByPriceType(priceChange, PTypeEnum.LSJ);

        List<PricePushTaskResultCount> updateResultCountList = pricePushTaskExtMapper.countOfEffectResult("1AJUST202206241804120002");
        System.out.println(updateResultCountList);
    }

    @Test
    public void syncExtendPriceType() {
    }

    @Test
    public void syncToPlatform() {
    }

    @Test
    public void syncCenterStore() {
    }

    @Test
    public void syncItemCenter() {
    }
}
