package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.PricecenterApp;
import com.cowell.pricecenter.enums.AdjustPriceVersionEnum;
import com.cowell.pricecenter.service.IAdjustPriceOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2022/3/24 17:59
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PricecenterApp.class)
public class AdjustPriceOrderServiceImplTest{

    @Autowired
    private IAdjustPriceOrderService adjustPriceOrderService;

    @Test
    public void distributeChangeDsxPriceToPrice() {
        adjustPriceOrderService.distributeChangeDsxPriceToPrice(AdjustPriceVersionEnum.VERSION_2_0.getVersion());
    }

    @Test
    public void planEffectAdjustPriceOrder() {
        adjustPriceOrderService.planEffectAdjustPriceOrder(AdjustPriceVersionEnum.VERSION_2_0.getVersion());
    }
}
