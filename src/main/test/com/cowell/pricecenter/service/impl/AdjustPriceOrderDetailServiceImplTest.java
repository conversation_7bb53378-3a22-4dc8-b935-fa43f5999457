package com.cowell.pricecenter.service.impl;

import com.cowell.pricecenter.PricecenterApp;
import com.cowell.pricecenter.entity.AdjustPriceOrder;
import com.cowell.pricecenter.entity.AdjustPriceOrderMqResult;
import com.cowell.pricecenter.enums.AdjustPriceOrderMqResultStatusEnum;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMapper;
import com.cowell.pricecenter.mapper.AdjustPriceOrderMqResultMapper;
import com.cowell.pricecenter.mapper.extension.AdjustPriceOrderMqResultExtMapper;
import com.cowell.pricecenter.service.IAdjustPriceOrderDetailService;
import com.cowell.pricecenter.service.IAdjustPriceOrderService;
import com.cowell.pricecenter.service.dto.response.AdjustPriceOrderDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @date 2022/3/29 14:22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = PricecenterApp.class)
public class AdjustPriceOrderDetailServiceImplTest {

    @Autowired
    private IAdjustPriceOrderDetailService adjustPriceOrderDetailService;

    @Autowired
    private IAdjustPriceOrderService adjustPriceOrderService;

    @Autowired
    private AdjustPriceOrderMapper adjustPriceOrderMapper;

    @Autowired
    private AdjustPriceOrderMqResultExtMapper adjustPriceOrderMqResultMapper;

    @Test
    public void distributeEffectAdjustPriceOrder() {

        System.out.println(adjustPriceOrderMqResultMapper.selectDistinctBusinessIdByAdjustCode("1AJUST202205061502520024"));;

//        AdjustPriceOrder adjustPriceOrder = adjustPriceOrderMapper.selectByAdjustCode("1AJUST202201051436298664");
//        adjustPriceOrderDetailService.distributePreEffectAdjustPriceOrder(adjustPriceOrder);
    }

    @Test
    public void preEffectPriceStoreDetail() {
        AdjustPriceOrderMqResult result = AdjustPriceOrderMqResult.builder()
            .adjustCode("1AJUST202204281351020003")
            .orgId(10001490L)
            .storeId(264257063298L)
            .businessId(363298L)
            .adjustPriceLevel(500)
//            .changeDsxPriceToPrice(true)
            .build();
        adjustPriceOrderDetailService.preEffectPriceStoreDetail(result);
    }

    @Test
    public void distributeChangeDsxPriceToPrice() {
    }

    @Test
    public void changeDsxPriceToPrice() {
        AdjustPriceOrderMqResult result = AdjustPriceOrderMqResult.builder()
            .adjustCode("1AJUST202201051436298664")
            .orgId(877L)
            .storeId(87185399999L)
            .businessId(99999L)
            .changeDsxPriceToPrice(true)
            .build();
        adjustPriceOrderDetailService.changeDsxPriceToPrice(result);
    }

}
