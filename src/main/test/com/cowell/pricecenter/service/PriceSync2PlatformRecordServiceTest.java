package com.cowell.pricecenter.service;

import com.cowell.pricecenter.PricecenterApp;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.service.dto.PriceSyncRecordDTO;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description:
 * @date 2020/03/05 18:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PricecenterApp.class})
public class PriceSync2PlatformRecordServiceTest {

    @Autowired
    private PriceSync2PlatformRecordService priceSync2PlatformRecordService;

    @Test
    public void batchInsert() throws Exception {
        List<PriceSyncRecordDTO> dtoList = Lists.newArrayList();
        for (int i = 0; i < 30; i++) {
            PriceSyncRecordDTO dto = new PriceSyncRecordDTO();
            dto.setBusinessId(99999L);
            dto.setStoreId(1234599999L);
            dto.setStoreName("AAAA" + i);
            dto.setGoodsNo(i + "-000");
            dto.setSkuName(i + "BBBB");
            dto.setPrice(100L);
            dto.setPriceType(PTypeEnum.LSJ.getType());
            dto.setSyncResult((byte)0);
            if(i % 2 == 1){
                dto.setSyncResult((byte)1);
                dto.setSyncWarnDesc("CCCCCC");
            }
            dto.setCreatedBy("-1");
            dto.setGmtCreate(new Date());
            dto.setGmtUpdate(new Date());
            dto.setStatus((byte)0);
            dto.setChannel(3);
            dtoList.add(dto);

        }
        priceSync2PlatformRecordService.batchInsertAndUpdateStatus(dtoList);
    }


    @Test
    public void  updateStatus(){
        List<String> goodsNoList = Lists.newArrayList();
        for (int i = 0; i < 30; i++) {
            goodsNoList.add(i + "-000");
        }
        priceSync2PlatformRecordService.updateStatus(1234599999L,goodsNoList,(byte)0,(byte)-1,"-1",3);
    }

    @Test
    public void deletePriceWarnARecord() throws Exception {
        Date beforeDate = DateUtils.getDateByFormat(-15);
        PriceSyncRecordDTO syncDto = new PriceSyncRecordDTO();
        syncDto.setBusinessId(99999L);
        syncDto.setGmtCreate(beforeDate);
        priceSync2PlatformRecordService.deleteSyncRecordRecord(syncDto);
    }

}
