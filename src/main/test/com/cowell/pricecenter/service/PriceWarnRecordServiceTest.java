package com.cowell.pricecenter.service;

import com.cowell.pricecenter.PricecenterApp;
import com.cowell.pricecenter.entity.PriceManageControlOrder;
import com.cowell.pricecenter.entity.PriceManageControlOrderDetail;
import com.cowell.pricecenter.entity.PriceManageControlStoreDetail;
import com.cowell.pricecenter.entity.PriceStoreDetail;
import com.cowell.pricecenter.enums.AuditStatusEnum;
import com.cowell.pricecenter.enums.PSourceEnum;
import com.cowell.pricecenter.enums.PTypeEnum;
import com.cowell.pricecenter.enums.PriceWarnTypeEnun;
import com.cowell.pricecenter.mapper.PriceStoreDetailMapper;
import com.cowell.pricecenter.service.dto.ControlOrderStoreDetailQueryDTO;
import com.cowell.pricecenter.service.dto.ControlStoreGoodsDetailResultDTO;
import com.cowell.pricecenter.service.dto.PriceManageControlStoreDetailDTO;
import com.cowell.pricecenter.service.dto.PriceWarningRecordDTO;
import com.cowell.pricecenter.service.dto.response.SpuNewVo;
import com.cowell.pricecenter.service.feign.SearchService;
import com.cowell.pricecenter.web.rest.util.DateUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ProjectName pricecenter
 * @Description:
 * @date 2020/03/03 14:13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {PricecenterApp.class})
public class PriceWarnRecordServiceTest {


    @Resource
    private PriceWarnRecordService priceWarnRecordService;

    @Autowired
    private PriceManageControlStoreDetailService priceManageControlStoreDetailService;

    @Autowired
    private IPriceManageControlOrderService priceManageControlOrderService;

    @Autowired
    private PriceManageControlOrderDetailService orderDetailService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private PriceStoreDetailReadService readService;

    @Autowired
    private PriceStoreDetailMapper priceStoreDetailMapper;


    @Test
    public void batchSave() throws Exception {
    }

    @Test
    public void batchInsertAndUpdateStatus() throws Exception {
        List<PriceWarningRecordDTO> recordList = Lists.newArrayList();
        for (int i = 0; i < 30; i++) {
            PriceWarningRecordDTO record = new PriceWarningRecordDTO();
            record.setBusinessId(99999L);
            record.setStoreId(1234599999L);
            record.setStoreName("AAAA-" + i);
            record.setGoodsNo("1234-" + i);
            record.setSkuName("QQQ-" + i);
            record.setPriceType(PTypeEnum.JDDJ.getType());
            record.setSrcPrice(10L);
            record.setNewPrice(20L);
            record.setPriceSource(PSourceEnum.PRICECENTER.getCode());
            record.setPriceWarnType(PriceWarnTypeEnun.BJ.getCode());
            if(i % 2 == 0){
                record.setPriceWarnType(PriceWarnTypeEnun.TJ.getCode());
            }
            record.setLastUpdateTime(new Date());
            record.setCreatedBy("-1");
            record.setGmtCreate(new Date());
            record.setUpdatedBy("-1");
            record.setGmtUpdate(new Date());
            record.setStatus((byte)0);
            record.setVersion(0);
            record.setBusNO("A295" + i);
            record.setCompId("30W0" + i);
            recordList.add(record);
        }
        priceWarnRecordService.batchInsertAndUpdateStatus(recordList);
    }

    @Test
    public void pushPriceWarn2WisdomStore() throws Exception {
        priceWarnRecordService.pushPriceWarn2WisdomStore("99999");
    }

    @Test
    public void deletePriceWarnARecord() throws Exception {
        Date beforeDate = DateUtils.getDateByFormat(-15);
        PriceWarningRecordDTO dto = new PriceWarningRecordDTO();
        dto.setBusinessId(99999L);
        dto.setGmtCreate(beforeDate);
        priceWarnRecordService.deletePriceWarnARecord(dto);
    }

    /**
     * 执行管控-清空门店商品价格
     */
    @Test
    public void testExecutePriceControl() {
        priceManageControlStoreDetailService.executePriceControl();
    }

    /**
     * 生成管控门店-商品明细
     */
    @Test
    public void testGenerateControlStoreDetail() {
        priceManageControlOrderService.generateControlStoreDetail("2CONTROL202204272024320034");
    }

    @Test
    public void testSavePriceControlStoreDetail() {
        ControlStoreGoodsDetailResultDTO resultDTO = new ControlStoreGoodsDetailResultDTO();
        resultDTO.setInsertList(Lists.newArrayList());

        ControlOrderStoreDetailQueryDTO storeDetailQueryDTO = new ControlOrderStoreDetailQueryDTO();
        storeDetailQueryDTO.setGoodsNoList(Lists.newArrayList("1004847"));
        storeDetailQueryDTO.setPriceTypeCodeList(Lists.newArrayList("HYJ"));
        storeDetailQueryDTO.setChannelIdList(Lists.newArrayList(3));
        storeDetailQueryDTO.setControlOrderCodeList(Lists.newArrayList("2CONTROL202204262101550025"));
        storeDetailQueryDTO.setStoreIdList(Lists.newArrayList(158121499999L));
        List<PriceManageControlStoreDetail> list = priceManageControlStoreDetailService.getControlStoreDetailByParam(storeDetailQueryDTO);

        PriceManageControlStoreDetailDTO storeDetailDTO = new PriceManageControlStoreDetailDTO();
        BeanUtils.copyProperties(list.get(0), storeDetailDTO);
        storeDetailDTO.setControlOrderCode("2CONTROL202204262101550025");
        storeDetailDTO.setUpperLimit(new BigDecimal(2300));
        storeDetailDTO.setLowerLimit(new BigDecimal(900));
        storeDetailDTO.setDxsDate(LocalDateTime.parse("2022-05-04 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        PriceManageControlOrderDetail orderDetail = new PriceManageControlOrderDetail();
        orderDetail.setUpperLimit(new BigDecimal(2300));
        orderDetail.setLowerLimit(new BigDecimal(900));
        orderDetail.setControlOrderAuditTime(LocalDateTime.parse("2022-04-26 21:06:52", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderDetail.setDxsDate(LocalDateTime.parse("2022-05-04 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        storeDetailDTO.setOrderDetail(orderDetail);
        resultDTO.getInsertList().add(storeDetailDTO);

        priceManageControlStoreDetailService.savePriceControlStoreDetail(resultDTO);
    }

    @Test
    public void createControlOrder() {
//        PriceManageControlOrder priceManageControlOrder = new PriceManageControlOrder();
//        priceManageControlOrder.setEffectTime(LocalDateTime.now());
//        priceManageControlOrder.setControlOrderId("Control20220408171033");
//        priceManageControlOrder.setControlType(1);
//        priceManageControlOrder.setEffectStatus("1");
//        priceManageControlOrder.setOrderLevel(3);
//        priceManageControlOrder.setAuditStatus(AuditStatusEnum.AUDIT_PASS.getCode());
//        priceManageControlOrder.setChannel("1,2");
//        priceManageControlOrder.setPriceType("LSJ,HYJ,GJYJS");
//        priceManageControlOrder.setControlOrderType(1);
//        priceManageControlOrder.setOrgId(602L);
//        priceManageControlOrder.setStatus(0);
//        priceManageControlOrder.setVersion(0);
//        priceManageControlOrderService.insert(priceManageControlOrder);

        List<String> goodsList = Lists.newArrayList("1118290", "1236031", "1244480", "1003048", "1003047",
            "1003048", "1010508", "1005283", "1176430", "8111142", "1062224", "1015199", "1062371", "1007246", "1053335",
            "1007848", "1007639", "1007350", "1007197", "1006975", "1006975", "1007639", "1010818", "401007045", "1020075",
            "1020118", "1020138", "1000045");
        PriceManageControlOrderDetail orderDetail = new PriceManageControlOrderDetail();
        ResponseEntity<List<SpuNewVo> > responseEntity = searchService.getNewSpuList(99999L, goodsList.stream().map(str -> Long.valueOf(str)).collect(Collectors.toList()));
        List<SpuNewVo> spuNewVos = responseEntity.getBody();
        spuNewVos.forEach(spuNewVo -> {
            String priceType = "LSJ,HYJ,GJYJS";
            Arrays.stream(priceType.split(",")).forEach(str -> {
                PriceManageControlOrderDetail orderDetail1 = new PriceManageControlOrderDetail();
                BeanUtils.copyProperties(spuNewVo, orderDetail1);
                orderDetail1.setUpperLimit(new BigDecimal(50));
                orderDetail1.setLowerLimit(new BigDecimal(51));
                orderDetail1.setGuidePrice(new BigDecimal(45));
                orderDetail1.setSpuId(spuNewVo.getId());
                orderDetail1.setDxsDate(LocalDateTime.now());
                orderDetail1.setEffectStatus(0);
                orderDetail1.setChannelId(1);
                orderDetail1.setPriceTypeId(Long.valueOf(PTypeEnum.getPTypeEnum(str).getType() + ""));
                orderDetail1.setDosage(spuNewVo.getDosageformsid());
                orderDetail1.setControlOrderCode("Control20220408171033");
                orderDetail1.setPriceTypeCode(PTypeEnum.getPTypeEnum(str).getCode());
                orderDetail1.setPriceTypeName(PTypeEnum.getPTypeEnum(str).getDesc());
                orderDetail1.setGoodsName(spuNewVo.getName());
                orderDetail1.setCurName(spuNewVo.getCurName());
                orderDetail1.setManufacturer(spuNewVo.getFactoryid());
                orderDetailService.insert(orderDetail1);
            });

        });
    }

    @Test
    public void updatePriceStoreDetail() {
        ControlOrderStoreDetailQueryDTO queryDTO = new ControlOrderStoreDetailQueryDTO();
        queryDTO.setControlOrderCodeList(Lists.newArrayList("2CONTROL202204201353520002"));
        List<PriceManageControlStoreDetail> storeDetails = priceManageControlStoreDetailService.getControlStoreDetailByParam(queryDTO);
        if (CollectionUtils.isNotEmpty(storeDetails)) {
            storeDetails.forEach(storeDetail -> {
                List<PriceStoreDetail> existDetail = readService.getPriceStoreDetailByParam(storeDetail.getStoreId(), null, Lists.newArrayList(storeDetail.getGoodsNo()),
                    Lists.newArrayList(storeDetail.getPriceTypeCode()));

                if (CollectionUtils.isNotEmpty(existDetail)) {
                    for (PriceStoreDetail priceStoreDetail : existDetail) {
                        String stirng = Objects.nonNull(priceStoreDetail.getPrice()) ? priceStoreDetail.getPrice().toString() : "";
                        System.out.println("PriceWarnRecordServiceTest|updatePriceStoreDetail|原来的价格" + stirng);
                        priceStoreDetail.setChannelId(1);
                        priceStoreDetail.setPrice(new BigDecimal(1500));
                        int effectNum = priceStoreDetailMapper.updateByPrimaryKey(priceStoreDetail);
                        System.out.println("PriceWarnRecordServiceTest|updatePriceStoreDetail|影响行数" +  effectNum);
                    }

                }
            });
        }


    }
}
