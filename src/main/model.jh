/**
*分库分表：businessId
*/
entity ItemBase  {
   itemId       		 Long  /**主键，商品id，生成策略：自增0+businessId后五位**/,
   businessId   		 Long   /**连锁id**/,
   storeId      		 Long /**门店id**/,
   spuId        		 Long   /**spu id**/,
   bgCateId    		 Long   /**后台类目id**/,
   itemName    		String maxlength(500)   /**商品标题**/,
   price         		 Long   /**商品价格(备注：sku价格)**/,
   imgHead      		String maxlength(200)  /**商品展示主图**/,
   status        		Integer   /**商品状态：1正常，2下架，3删除**/,
   stock         		Long    /**商品库存**/,
   buyStock     		Long    /**记录当前可购买的商品库存数**/,
   sold          		Long    /**商品销量**/,
   isSoldout    		Integer   /**商品售罄状态：0,未售罄 1售罄**/,
   offShelfTime  		ZonedDateTime   /**下架**/,
   flag  				Integer  /**0:不推荐;1:推荐**/,
   extend1  			String maxlength(2048)    /**扩展字段1**/,
   extend2  			String maxlength(8192)    /**扩展字段2**/,
   extend3  			String maxlength(2048)    /**扩展字段3**/,
   flagBin  			 Integer   /**扩展用，二制位标记(商品状态记录0-1-1-1-1-0)**/,
   merchantCode  		String maxlength(64)     /**商家编号**/,
   turnover  			 Long   /**营业额**/,
   version  			Integer  /**版本号**/
}



/**
*商品拓展信息表(商品单记录扩展) ;
*/
entity ItemInfo  {
   itemId  			 Long  /**商品id，生成策略：自增1+businessId后五位**/,
   businessId  		 Long   /**连锁id**/,
   storeId  			 Long /**门店id**/,
   imgs  				String /**商品图片列表地址集合**/,
   titles  				String /**图片描述(数组结构)**/,
   itemComment  		String  /**商品描述**/,
   extend1  			String maxlength(2048)    /**扩展字段1**/,
   extend2  			String maxlength(2048)    /**扩展字段2**/,
   extend3  			String maxlength(2048)    /**扩展字段3**/,
   extend4  			String maxlength(2048)    /**扩展字段4**/,
   extend5  			String maxlength(2048)    /**扩展字段5**/,
   itemDetail  		        String /**对商品详情的描述， 格式为[{type:1,data}]**/,
   version  			Integer  /**版本号**/
}




/**
* 商品SKU表
*/
entity ItemSku  {
   item_skuId  		 Long  /**商品sku id，生成策略：自增+businessId后五位**/,
   businessId  		 Long   /**连锁id**/,
   storeId  			 Long /**门店id**/,
   itemId  			 Long  /**商品id，生成策略：自增+businessId后五位**/,
   cspuId  			 Long   /**cspu id**/,
   title  				String maxlength(64)    /**型号描述**/,
   attrIds  			String maxlength(200) required   /**商品属性**/,
   img  				String maxlength(200) required    /**图片地址**/,
   stock  				Long   /**商品库存**/,
   buyStock  			Long    /**记录当前可购买的商品库存数**/,
   price  				 Long   /**商品价格**/,
   status  				Integer  /**1 show; 2 hide**/,
   sold  				Long   /**商品销量**/,
   skuMerchantCode  	String maxlength(64)     /**外部商家商品编码**/,
   extend  				String maxlength(2048)     /**扩展字段**/,
   version  			Integer /**版本号**/
}





/**
*  tcc log表
*/
entity SeqLog  {
   keyId   Long  /**业务类型为0，表示item_skuId； 业务类型为1，待扩展**/,
   linkId   Long  /**业务类型为0，表示itemId； 业务类型为1，待扩展**/,
   seq  String maxlength(128) required /**单次分布式事务请求唯一seq号**/,
   type  Integer   /**业务类型：0表示订单减库存；1 待扩展**/,
   value   Long   /**业务类型为0：表示加减库存量；业务类型为1：待扩展**/,
   flag  Integer   /**记录状态：0表示正常执行; 1表示已回滚**/,
   version  Integer /**版本号**/
}


/**
*  标签表
*/
entity TagDetails  {
   name  String maxlength(100) required /**标签名称**/,
   level  Integer /**标签当前层级.0为顶层，最多两层**/,
   parentId   Long  /**父标签ID**/,
   childrenMutiSelect  Integer /**如作为父标签，子标签是否多选.1为支持多选，0为非多选**/,
   isLeaf  Integer  /**1:叶子;0:非叶子**/,
   type  Integer  /**标签类型:1普通业务标签 ;2资质标签;3用户输入标签**/,
   target  Integer  /**作用域:1商品;2卖家;3买家**/,
   vertical   Long   /**垂直行业，对应后台1级类目**/,
   scene  String maxlength(100) required /**标签使用场景**/,
   ownerId  Long /**标签归属**/,
   status Integer /**标签状态.1:有效;0无效**/,
   description  String maxlength(100)   /**描述**/,
   start_time  ZonedDateTime /**标签有效的起始时间**/,
   end_time  ZonedDateTime /**标签结束时间**/,
   version  Integer /**版本号**/
}

